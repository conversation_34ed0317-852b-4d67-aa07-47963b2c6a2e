const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ThreadUpdate,
    async execute(oldThread, newThread, client) {
        try {
            // Only handle unarchiving (was archived, now is not)
            if (!oldThread.archived || newThread.archived) return;
            
            console.log(`📂 Thread unarchived: ${newThread.name} in ${newThread.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newThread.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'THREAD_UNARCHIVE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'THREAD_UNARCHIVE',
                user: 'System',
                userId: null,
                action: 'Thread được unarchive',
                details: `Thread **${newThread.name}** đã được unarchive`,
                target: newThread.name,
                channel: newThread.parent?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who unarchived the thread from audit logs
            try {
                const auditLogs = await newThread.guild.fetchAuditLogs({
                    type: 111, // THREAD_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newThread.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Thread được unarchive bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for thread unarchive');
            }
            
            // Add to database
            await client.db.addAuditLog(newThread.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📂 Thread được unarchive',
                `Vừa có một thread được unarchive trong server`
            );
            
            embed.setColor(0x2ecc71); // Green for unarchive/restore
            
            // Get thread type names
            const threadTypes = {
                10: 'Announcement Thread',
                11: 'Public Thread',
                12: 'Private Thread'
            };
            
            // Calculate how long it was archived
            let archivedDuration = '';
            if (oldThread.archiveTimestamp) {
                const archivedTime = Date.now() - oldThread.archiveTimestamp;
                const archivedDays = Math.floor(archivedTime / (1000 * 60 * 60 * 24));
                const archivedHours = Math.floor((archivedTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                archivedDuration = `${archivedDays} ngày, ${archivedHours} giờ`;
            }
            
            embed.addFields([
                {
                    name: '> Tên thread',
                    value: `- ${newThread.name}`,
                    inline: true
                },
                {
                    name: '> ID thread',
                    value: `- ${newThread.id}`,
                    inline: true
                },
                {
                    name: '> Loại thread',
                    value: `- ${threadTypes[newThread.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh cha',
                    value: `- ${newThread.parent || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người unarchive',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian unarchive',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            if (archivedDuration) {
                embed.addFields({
                    name: '> Thời gian đã archive',
                    value: `- ${archivedDuration}`,
                    inline: true
                });
            }
            
            if (oldThread.archiveTimestamp) {
                embed.addFields({
                    name: '> Được archive lúc',
                    value: `- <t:${Math.floor(oldThread.archiveTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            embed.addFields([
                {
                    name: '> Số tin nhắn',
                    value: `- ${newThread.messageCount || 0} tin nhắn`,
                    inline: true
                },
                {
                    name: '> Số thành viên',
                    value: `- ${newThread.memberCount || 0} thành viên`,
                    inline: true
                }
            ]);
            
            // Add thread owner info if available
            if (newThread.ownerId) {
                try {
                    const owner = await newThread.guild.members.fetch(newThread.ownerId);
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- ${owner.user.tag}`,
                        inline: true
                    });
                } catch (error) {
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- Unknown (ID: ${newThread.ownerId})`,
                        inline: true
                    });
                }
            }
            
            // Add auto-archive info
            embed.addFields({
                name: '> Auto-archive',
                value: `- ${newThread.autoArchiveDuration} phút không hoạt động`,
                inline: true
            });
            
            // Add thread link
            embed.addFields({
                name: '> Link thread',
                value: `- [Nhấn để xem](https://discord.com/channels/${newThread.guild.id}/${newThread.id})`,
                inline: true
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do unarchive',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Add note about thread activity
            embed.addFields({
                name: '> Lưu ý',
                value: '- Thread giờ đây có thể nhận tin nhắn mới và hoạt động bình thường',
                inline: false
            });
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newThread.guild.name} • Thread Unarchive`,
                iconURL: newThread.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Thread unarchive logged: ${newThread.name}`);
            
        } catch (error) {
            console.error('Error in threadUnarchive audit log:', error);
        }
    }
};
