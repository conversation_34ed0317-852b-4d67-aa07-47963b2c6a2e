const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'soundboardSoundUpdate', // Custom event name
    async execute(oldSound, newSound, client) {
        try {
            // Only handle emoji changes
            if (oldSound.emoji === newSound.emoji) return;
            
            console.log(`😀 Soundboard sound emoji updated: ${newSound.name} in ${newSound.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newSound.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'SOUNDBOARD_SOUND_EMOJI_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'SOUNDBOARD_SOUND_EMOJI_UPDATE',
                user: 'System',
                userId: null,
                action: 'Emoji soundboard được cập nhật',
                details: `Emoji của âm thanh soundboard **${newSound.name}** đã được cập nhật`,
                target: newSound.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the sound emoji from audit logs
            try {
                const auditLogs = await newSound.guild.fetchAuditLogs({
                    type: 131, // SOUNDBOARD_SOUND_UPDATE (hypothetical)
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newSound.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Emoji soundboard được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for soundboard emoji update');
            }
            
            // Add to database
            await client.db.addAuditLog(newSound.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '😀 Emoji soundboard được cập nhật',
                `Vừa có emoji soundboard được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Âm thanh',
                    value: `- ${newSound.name}`,
                    inline: true
                },
                {
                    name: '> ID âm thanh',
                    value: `- ${newSound.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Emoji cũ',
                    value: `- ${oldSound.emoji || 'Không có emoji'}`,
                    inline: true
                },
                {
                    name: '> Emoji mới',
                    value: `- ${newSound.emoji || 'Không có emoji'}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Determine change type and set appropriate color
            if (newSound.emoji && oldSound.emoji) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Thay đổi emoji`,
                    inline: true
                });
                embed.setColor(0x3498db); // Blue for change
            } else if (newSound.emoji && !oldSound.emoji) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Thiết lập emoji mới`,
                    inline: true
                });
                embed.setColor(0x2ecc71); // Green for new emoji
            } else if (!newSound.emoji && oldSound.emoji) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Xóa emoji`,
                    inline: true
                });
                embed.setColor(0xe74c3c); // Red for removed emoji
            }
            
            // Add sound properties
            if (newSound.volume !== undefined) {
                embed.addFields({
                    name: '> Âm lượng',
                    value: `- ${Math.round(newSound.volume * 100)}%`,
                    inline: true
                });
            }
            
            // Add creator info if available
            if (newSound.user) {
                embed.addFields({
                    name: '> Người tạo',
                    value: `- ${newSound.user.tag}`,
                    inline: true
                });
            }
            
            // Add creation info if available
            if (newSound.createdTimestamp) {
                embed.addFields({
                    name: '> Được tạo lúc',
                    value: `- <t:${Math.floor(newSound.createdTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add impact information
            if (newSound.emoji && !oldSound.emoji) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Âm thanh giờ đây có emoji đại diện',
                        '• Dễ nhận biết trong soundboard',
                        '• Tăng tính thẩm mỹ cho giao diện',
                        '• Giúp người dùng tìm kiếm nhanh hơn'
                    ].join('\n'),
                    inline: false
                });
            } else if (!newSound.emoji && oldSound.emoji) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Âm thanh không còn emoji đại diện',
                        '• Chỉ hiển thị tên trong soundboard',
                        '• Có thể khó nhận biết hơn',
                        '• Giao diện đơn giản hơn'
                    ].join('\n'),
                    inline: false
                });
            } else if (newSound.emoji && oldSound.emoji) {
                embed.addFields({
                    name: '> 🔄 Tác động',
                    value: [
                        '• Emoji đại diện đã được thay đổi',
                        '• Giao diện mới cho âm thanh',
                        '• Có thể cần thời gian để người dùng quen',
                        '• Cập nhật hình ảnh thương hiệu'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add emoji guidelines
            embed.addFields({
                name: '> 💡 Lưu ý về emoji',
                value: [
                    '• Emoji giúp nhận biết nhanh âm thanh',
                    '• Nên chọn emoji phù hợp với nội dung âm thanh',
                    '• Emoji hiển thị trong soundboard picker',
                    '• Có thể sử dụng emoji Unicode hoặc custom emoji'
                ].join('\n'),
                inline: false
            });
            
            // Add usage info
            embed.addFields({
                name: '> 🎵 Cách sử dụng',
                value: [
                    '• Vào voice channel',
                    '• Nhấn biểu tượng soundboard',
                    `• Tìm ${newSound.emoji ? `emoji ${newSound.emoji}` : `âm thanh "${newSound.name}"`}`,
                    '• Nhấn để phát âm thanh'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newSound.guild.name} • Soundboard Emoji Update`,
                iconURL: newSound.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Soundboard emoji update logged: ${newSound.name}`);
            
        } catch (error) {
            console.error('Error in soundboardSoundEmojiUpdate audit log:', error);
        }
    }
};
