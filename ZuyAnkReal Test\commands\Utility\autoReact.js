function _0x1bbbb3(_0xc01c38,_0x1cc2a2){return _0x4ca1(_0xc01c38- -0x335,_0x1cc2a2);}function _0x4ca1(_0x57c4c2,_0x1d413a){const _0x49625b=_0x4962();return _0x4ca1=function(_0x4ca17e,_0x220eeb){_0x4ca17e=_0x4ca17e-0xdd;let _0xf96814=_0x49625b[_0x4ca17e];return _0xf96814;},_0x4ca1(_0x57c4c2,_0x1d413a);}(function(_0x1e40bf,_0x3f125){const _0x44d77b=_0x1e40bf();function _0x661a31(_0x578309,_0x1bfcec){return _0x4ca1(_0x1bfcec-0x2ec,_0x578309);}while(!![]){try{const _0x2dbf7d=parseInt(_0x661a31(0x41b,0x3f4))/0x1+parseInt(_0x661a31(0x3c9,0x3e7))/0x2*(-parseInt(_0x661a31(0x3fa,0x3fb))/0x3)+parseInt(_0x661a31(0x3ed,0x3dc))/0x4*(-parseInt(_0x661a31(0x3e2,0x403))/0x5)+parseInt(_0x661a31(0x3f9,0x3ef))/0x6*(parseInt(_0x661a31(0x413,0x3ee))/0x7)+parseInt(_0x661a31(0x413,0x40c))/0x8*(parseInt(_0x661a31(0x426,0x40e))/0x9)+parseInt(_0x661a31(0x3e9,0x3e4))/0xa+parseInt(_0x661a31(0x3c1,0x3d8))/0xb;if(_0x2dbf7d===_0x3f125)break;else _0x44d77b['push'](_0x44d77b['shift']());}catch(_0x8a0384){_0x44d77b['push'](_0x44d77b['shift']());}}}(_0x4962,0x37de9));const AutoReact=require(_0x1bbbb3(-0x210,-0x221)),{SlashCommandBuilder,PermissionsBitField,EmbedBuilder}=require(_0x1bbbb3(-0x227,-0x21b));function _0x4962(){const _0x125031=['#5865F2','member','setRequired','38972tjhKIK','Flags','❌\x20The\x20emoji\x20\x22','client','setTitle','list','save','test','1368250NNRAoR','keyword','available','8ruaHBS','reactions','ID:\x20','findOne','autoreact','\x22\x20is\x20not\x20valid\x20or\x20not\x20accessible\x20by\x20the\x20bot.\x20Please\x20try\x20another\x20emoji.','getString','7CQXZro','385614VaTpYT','Auto\x20Reacts\x20List','permissions','addFields','Remove\x20an\x20existing\x20AutoReact','174282RfoAuG','🟢\x20Valid','\x22\x20has\x20been\x20successfully\x20removed.','setColor','toLowerCase','emoji','discord.js','150339hFaLRu','Emoji:\x20\x22','A\x20new\x20AutoReact\x20has\x20been\x20successfully\x20added.','add','push','Utility','Use\x20/autoreact\x20add\x20to\x20add\x20more\x20reactions.','Here\x20are\x20the\x20currently\x20configured\x20AutoReacts\x20for\x20this\x20server:','10UvOPVf','Status','options','Add\x20a\x20new\x20AutoReact','remove','addSubcommand','ManageGuild','Manage\x20AutoReact\x20settings','getSubcommand','135688NzrbAT','List\x20all\x20current\x20AutoReacts','27fVXLdd','Emoji','setDescription','../../models/autoReact','has','The\x20emoji\x20to\x20react\x20with','identifier','all','setFooter','You\x20do\x20not\x20have\x20permission\x20to\x20use\x20this\x20command.','reply','AutoReact\x20Added','The\x20keyword\x20to\x20react\x20to','get','❌\x20No\x20AutoReact\x20found\x20with\x20the\x20identifier\x20\x22','map','Keyword:\x20\x22','addStringOption','flat','Keyword','The\x20AutoReact\x20with\x20identifier\x20\x22','length','\x22.\x20Please\x20check\x20and\x20try\x20again.','setName','247720CssgwS'];_0x4962=function(){return _0x125031;};return _0x4962();}module['exports']={'data':new SlashCommandBuilder()[_0x1bbbb3(-0x24a,-0x234)](_0x1bbbb3(-0x236,-0x225))[_0x1bbbb3(-0x211,-0x222)](_0x1bbbb3(-0x217,-0x1fa))[_0x1bbbb3(-0x219,-0x22c)](_0x34abc2=>_0x34abc2[_0x1bbbb3(-0x24a,-0x26b)](_0x1bbbb3(-0x223,-0x23b))[_0x1bbbb3(-0x211,-0x238)](_0x1bbbb3(-0x21b,-0x223))[_0x1bbbb3(-0x250,-0x244)](_0x458c71=>_0x458c71[_0x1bbbb3(-0x24a,-0x24d)]('keyword')[_0x1bbbb3(-0x211,-0x1ec)](_0x1bbbb3(-0x255,-0x261))[_0x1bbbb3(-0x246,-0x265)](!![]))[_0x1bbbb3(-0x250,-0x268)](_0x10ddca=>_0x10ddca[_0x1bbbb3(-0x24a,-0x257)](_0x1bbbb3(-0x228,-0x245))[_0x1bbbb3(-0x211,-0x22c)](_0x1bbbb3(-0x20e,-0x232))[_0x1bbbb3(-0x246,-0x24e)](!![])))[_0x1bbbb3(-0x219,-0x232)](_0x4863f8=>_0x4863f8[_0x1bbbb3(-0x24a,-0x23f)](_0x1bbbb3(-0x21a,-0x20f))[_0x1bbbb3(-0x211,-0x1f7)](_0x1bbbb3(-0x22e,-0x22f))['addStringOption'](_0x2f3bb2=>_0x2f3bb2[_0x1bbbb3(-0x24a,-0x22e)]('identifier')[_0x1bbbb3(-0x211,-0x1ee)]('The\x20keyword\x20or\x20ID\x20of\x20the\x20AutoReact\x20to\x20remove')['setRequired'](!![])))['addSubcommand'](_0x146c10=>_0x146c10[_0x1bbbb3(-0x24a,-0x24c)]('list')[_0x1bbbb3(-0x211,-0x22a)](_0x1bbbb3(-0x214,-0x236))),'category':_0x1bbbb3(-0x221,-0x229),async 'execute'(_0x575ff8){function _0x4f2f1b(_0x52d5c3,_0x578d0f){return _0x1bbbb3(_0x578d0f-0x5c3,_0x52d5c3);}if(!_0x575ff8[_0x4f2f1b(0x37b,0x37c)][_0x4f2f1b(0x385,0x393)][_0x4f2f1b(0x3d8,0x3b4)](PermissionsBitField[_0x4f2f1b(0x3a1,0x37f)][_0x4f2f1b(0x3a2,0x3ab)]))return _0x575ff8[_0x4f2f1b(0x367,0x36c)]({'content':_0x4f2f1b(0x366,0x36b),'ephemeral':!![]});const _0x568232=_0x575ff8['guild']['id'];let _0x2ee741=await AutoReact[_0x4f2f1b(0x3a3,0x38c)]({'guildId':_0x568232});!_0x2ee741&&(_0x2ee741=new AutoReact({'guildId':_0x568232,'reactions':[]}));const _0x1fdab5=_0x575ff8[_0x4f2f1b(0x3bd,0x3a7)][_0x4f2f1b(0x3b4,0x3ad)]();if(_0x1fdab5===_0x4f2f1b(0x3a1,0x3a0)){const _0x3b6b4a=_0x575ff8[_0x4f2f1b(0x3aa,0x3a7)][_0x4f2f1b(0x372,0x38f)](_0x4f2f1b(0x367,0x387)),_0x115e39=_0x575ff8[_0x4f2f1b(0x3b4,0x3a7)][_0x4f2f1b(0x369,0x38f)]('emoji'),_0x4e561b=validateEmoji(_0x575ff8,_0x115e39);if(!_0x4e561b)return _0x575ff8['reply']({'content':_0x4f2f1b(0x385,0x380)+_0x115e39+_0x4f2f1b(0x3b5,0x38e),'ephemeral':!![]});const _0x29fe7d=_0x2ee741['reactions']['length'],_0x1c2148=_0x29fe7d+0x1;_0x2ee741[_0x4f2f1b(0x39d,0x38a)][_0x4f2f1b(0x3c7,0x3a1)]({'id':_0x1c2148,'keyword':_0x3b6b4a,'emoji':_0x115e39,'whitelistRoles':[],'whitelistChannels':[]}),await _0x2ee741[_0x4f2f1b(0x38c,0x384)]();const _0x2546bb=new EmbedBuilder()['setTitle'](_0x4f2f1b(0x387,0x36d))[_0x4f2f1b(0x39d,0x3b2)](_0x4f2f1b(0x3b2,0x39f))[_0x4f2f1b(0x376,0x394)]({'name':'ID','value':''+_0x1c2148,'inline':!![]},{'name':_0x4f2f1b(0x350,0x375),'value':_0x3b6b4a,'inline':!![]},{'name':_0x4f2f1b(0x392,0x3b1),'value':_0x115e39,'inline':!![]})[_0x4f2f1b(0x3af,0x399)]('#57F287');return _0x575ff8[_0x4f2f1b(0x35a,0x36c)]({'embeds':[_0x2546bb],'ephemeral':!![]});}else{if(_0x1fdab5===_0x4f2f1b(0x3a3,0x3a9)){const _0x4d2f6a=_0x575ff8[_0x4f2f1b(0x3b2,0x3a7)][_0x4f2f1b(0x3ac,0x38f)](_0x4f2f1b(0x3da,0x3b6));let _0xe3e73e=![];if(isNaN(_0x4d2f6a))_0x2ee741['reactions']=_0x2ee741[_0x4f2f1b(0x385,0x38a)]['filter'](_0x3a61ba=>_0x3a61ba[_0x4f2f1b(0x381,0x387)][_0x4f2f1b(0x37b,0x39a)]()!==_0x4d2f6a['toLowerCase']()),_0xe3e73e=!![];else{const _0x2b263d=parseInt(_0x4d2f6a),_0x31b99d=_0x2ee741[_0x4f2f1b(0x36e,0x38a)][_0x4f2f1b(0x367,0x377)];_0x2ee741[_0x4f2f1b(0x389,0x38a)]=_0x2ee741[_0x4f2f1b(0x365,0x38a)]['filter'](_0x2891bd=>_0x2891bd['id']!==_0x2b263d),_0xe3e73e=_0x31b99d!==_0x2ee741[_0x4f2f1b(0x38b,0x38a)][_0x4f2f1b(0x374,0x377)];}if(_0xe3e73e){await _0x2ee741[_0x4f2f1b(0x3a7,0x384)]();const _0x1ae87f=new EmbedBuilder()[_0x4f2f1b(0x39e,0x382)]('AutoReact\x20Removed')[_0x4f2f1b(0x38c,0x3b2)](_0x4f2f1b(0x364,0x376)+_0x4d2f6a+_0x4f2f1b(0x379,0x398))[_0x4f2f1b(0x3a2,0x399)]('#ED4245');return _0x575ff8[_0x4f2f1b(0x361,0x36c)]({'embeds':[_0x1ae87f],'ephemeral':!![]});}else return _0x575ff8[_0x4f2f1b(0x38e,0x36c)]({'content':_0x4f2f1b(0x38e,0x370)+_0x4d2f6a+_0x4f2f1b(0x394,0x378),'ephemeral':!![]});}else{if(_0x1fdab5===_0x4f2f1b(0x38e,0x383)){if(_0x2ee741['reactions'][_0x4f2f1b(0x398,0x377)]===0x0)return _0x575ff8[_0x4f2f1b(0x391,0x36c)]({'content':'No\x20AutoReacts\x20are\x20set.','ephemeral':!![]});const _0x5b99c1=await Promise[_0x4f2f1b(0x3c6,0x3b7)](_0x2ee741[_0x4f2f1b(0x380,0x38a)][_0x4f2f1b(0x35c,0x371)](async _0x84ac1d=>{const _0x13acc7=validateEmoji(_0x575ff8,_0x84ac1d[_0x162083(0x265,0x28b)]);function _0x162083(_0x48265c,_0x558e95){return _0x4f2f1b(_0x48265c,_0x558e95- -0x110);}return[{'name':_0x162083(0x29c,0x27b)+_0x84ac1d['id'],'value':'​','inline':!![]},{'name':_0x162083(0x264,0x262)+_0x84ac1d[_0x162083(0x26b,0x277)]+'\x22','value':_0x162083(0x2a5,0x28e)+_0x84ac1d[_0x162083(0x279,0x28b)]+'\x22','inline':!![]},{'name':_0x162083(0x2a6,0x296),'value':_0x13acc7?_0x162083(0x28c,0x287):'🔴\x20Invalid/Inaccessible','inline':!![]}];})),_0xe53650=_0x5b99c1[_0x4f2f1b(0x364,0x374)](),_0x58728b=new EmbedBuilder()['setTitle'](_0x4f2f1b(0x381,0x392))[_0x4f2f1b(0x3d3,0x3b2)](_0x4f2f1b(0x385,0x3a4))[_0x4f2f1b(0x372,0x394)](_0xe53650)[_0x4f2f1b(0x3b4,0x399)](_0x4f2f1b(0x3a0,0x37b))[_0x4f2f1b(0x394,0x3b8)]({'text':_0x4f2f1b(0x3c7,0x3a3)});return _0x575ff8[_0x4f2f1b(0x37b,0x36c)]({'embeds':[_0x58728b],'ephemeral':!![]});}}}}};function validateEmoji(_0xcbf86c,_0x5d5e95){const _0x359863=_0x5d5e95['match'](/<:\w+:(\d+)>/);if(_0x359863){const _0x240d92=_0x359863[0x1],_0x141933=_0xcbf86c[_0x1603e0(0x3cf,0x3dd)]['emojis']['cache'][_0x1603e0(0x3bd,0x3ba)](_0x240d92);return _0x141933&&_0x141933[_0x1603e0(0x3d6,0x3ea)]?!![]:![];}const _0x18e510=/\p{Emoji}/u;if(_0x18e510[_0x1603e0(0x3d3,0x3af)](_0x5d5e95))return!![];function _0x1603e0(_0x210f37,_0xe6c622){return _0x1bbbb3(_0x210f37-0x611,_0xe6c622);}return![];}