const fs = require('fs');
const path = require('path');

console.log('🎉 Server Setup Bot - Complete Feature Test');
console.log('==========================================\n');

// Test categories and their expected commands
const expectedCommands = {
    'welcome': ['welcome-setup.js', 'goodbye-setup.js'],
    'autoroles': ['autorole-setup.js'],
    'channels': ['channel-create.js', 'channel-bulk.js'],
    'permissions': ['permission-setup.js'],
    'verification': ['verification-setup.js'],
    'moderation': ['moderation-setup.js'],
    'templates': ['template-create.js'],
    'backup': ['backup-create.js', 'backup-list.js'],
    'content': ['emoji-manage.js'],
    'utility': ['help.js']
};

const expectedEvents = [
    'ready.js',
    'interactionCreate.js',
    'guildCreate.js',
    'guildMemberAdd.js',
    'guildMemberRemove.js'
];

const expectedUtils = [
    'embedBuilder.js',
    'permissions.js',
    'validators.js',
    'deployCommands.js'
];

const expectedHandlers = [
    'buttons/verifyHandler.js'
];

let totalTests = 0;
let passedTests = 0;

function runTest(testName, testFunction) {
    totalTests++;
    try {
        const result = testFunction();
        if (result) {
            console.log(`✅ ${testName}`);
            passedTests++;
        } else {
            console.log(`❌ ${testName}`);
        }
    } catch (error) {
        console.log(`❌ ${testName}: ${error.message}`);
    }
}

// Test 1: Project Structure
console.log('📁 Test 1: Kiểm tra cấu trúc project...');
runTest('Package.json exists', () => fs.existsSync('package.json'));
runTest('Source directory exists', () => fs.existsSync('src'));
runTest('Commands directory exists', () => fs.existsSync('src/commands'));
runTest('Events directory exists', () => fs.existsSync('src/events'));
runTest('Utils directory exists', () => fs.existsSync('src/utils'));
runTest('Database directory exists', () => fs.existsSync('src/database'));
runTest('Config directory exists', () => fs.existsSync('src/config'));
runTest('Handlers directory exists', () => fs.existsSync('src/handlers'));

// Test 2: Command Categories
console.log('\n📋 Test 2: Kiểm tra categories và commands...');
for (const [category, commands] of Object.entries(expectedCommands)) {
    const categoryPath = path.join('src', 'commands', category);
    runTest(`Category ${category} exists`, () => fs.existsSync(categoryPath));
    
    for (const command of commands) {
        const commandPath = path.join(categoryPath, command);
        runTest(`Command ${category}/${command}`, () => fs.existsSync(commandPath));
    }
}

// Test 3: Events
console.log('\n⚡ Test 3: Kiểm tra events...');
for (const event of expectedEvents) {
    const eventPath = path.join('src', 'events', event);
    runTest(`Event ${event}`, () => fs.existsSync(eventPath));
}

// Test 4: Utilities
console.log('\n🛠️ Test 4: Kiểm tra utilities...');
for (const util of expectedUtils) {
    const utilPath = path.join('src', 'utils', util);
    runTest(`Utility ${util}`, () => fs.existsSync(utilPath));
}

// Test 5: Handlers
console.log('\n🎛️ Test 5: Kiểm tra handlers...');
for (const handler of expectedHandlers) {
    const handlerPath = path.join('src', 'handlers', handler);
    runTest(`Handler ${handler}`, () => fs.existsSync(handlerPath));
}

// Test 6: Core Files
console.log('\n🏗️ Test 6: Kiểm tra core files...');
runTest('Main index.js', () => fs.existsSync('src/index.js'));
runTest('Database.js', () => fs.existsSync('src/database/database.js'));
runTest('Config.js', () => fs.existsSync('src/config/config.js'));

// Test 7: Documentation
console.log('\n📚 Test 7: Kiểm tra documentation...');
runTest('README.md', () => fs.existsSync('README.md'));
runTest('SETUP.md', () => fs.existsSync('SETUP.md'));
runTest('FEATURES_COMPLETE.md', () => fs.existsSync('FEATURES_COMPLETE.md'));

// Test 8: Configuration Files
console.log('\n⚙️ Test 8: Kiểm tra configuration...');
runTest('.env.example', () => fs.existsSync('.env.example'));
runTest('.gitignore', () => fs.existsSync('.gitignore'));
runTest('test.js', () => fs.existsSync('test.js'));

// Test 9: Package Dependencies
console.log('\n📦 Test 9: Kiểm tra dependencies...');
try {
    const packageJson = require('./package.json');
    const requiredDeps = ['discord.js', 'sqlite3', 'dotenv', 'moment', 'moment-timezone'];
    
    for (const dep of requiredDeps) {
        runTest(`Dependency ${dep}`, () => packageJson.dependencies && packageJson.dependencies[dep]);
    }
} catch (error) {
    console.log('❌ Cannot read package.json');
}

// Test 10: Command Syntax Check
console.log('\n🔍 Test 10: Kiểm tra syntax commands...');
const commandDirs = fs.readdirSync('src/commands');
for (const dir of commandDirs) {
    const dirPath = path.join('src', 'commands', dir);
    if (fs.statSync(dirPath).isDirectory()) {
        const files = fs.readdirSync(dirPath).filter(f => f.endsWith('.js'));
        for (const file of files) {
            runTest(`Syntax ${dir}/${file}`, () => {
                try {
                    require(path.join('.', dirPath, file));
                    return true;
                } catch (error) {
                    return false;
                }
            });
        }
    }
}

// Test 11: Feature Completeness
console.log('\n🎯 Test 11: Kiểm tra tính năng hoàn chỉnh...');
const featureChecks = {
    'Welcome System': () => fs.existsSync('src/commands/welcome/welcome-setup.js') && 
                            fs.existsSync('src/commands/welcome/goodbye-setup.js'),
    'Auto-Role System': () => fs.existsSync('src/commands/autoroles/autorole-setup.js'),
    'Channel Management': () => fs.existsSync('src/commands/channels/channel-create.js') && 
                               fs.existsSync('src/commands/channels/channel-bulk.js'),
    'Permission Management': () => fs.existsSync('src/commands/permissions/permission-setup.js'),
    'Verification System': () => fs.existsSync('src/commands/verification/verification-setup.js') &&
                                fs.existsSync('src/handlers/buttons/verifyHandler.js'),
    'Moderation Tools': () => fs.existsSync('src/commands/moderation/moderation-setup.js'),
    'Template System': () => fs.existsSync('src/commands/templates/template-create.js'),
    'Backup System': () => fs.existsSync('src/commands/backup/backup-create.js') &&
                           fs.existsSync('src/commands/backup/backup-list.js'),
    'Content Management': () => fs.existsSync('src/commands/content/emoji-manage.js'),
    'Utility System': () => fs.existsSync('src/commands/utility/help.js')
};

for (const [feature, check] of Object.entries(featureChecks)) {
    runTest(feature, check);
}

// Test Results
console.log('\n🎯 Kết quả test:');
console.log('================');
console.log(`✅ Passed: ${passedTests}/${totalTests}`);
console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
console.log(`📊 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

if (passedTests === totalTests) {
    console.log('\n🎉 TẤT CẢ TEST ĐÃ PASS!');
    console.log('Bot đã sẵn sàng cho production!');
} else {
    console.log('\n⚠️ Một số test chưa pass.');
    console.log('Vui lòng kiểm tra và khắc phục các vấn đề.');
}

console.log('\n📋 Bước tiếp theo:');
console.log('1. Khắc phục các test failed (nếu có)');
console.log('2. Thiết lập .env với token thật');
console.log('3. Chạy: npm run deploy');
console.log('4. Chạy: npm start');
console.log('5. Test bot trong Discord với /help');

console.log('\n🚀 Bot Features Summary:');
console.log('• 👋 Welcome/Goodbye System');
console.log('• 🎭 Auto-Role Management');
console.log('• 📝 Channel Creation & Templates');
console.log('• 🔒 Permission Management');
console.log('• ✅ Verification System');
console.log('• 🛡️ Moderation Tools');
console.log('• 📋 Server Templates');
console.log('• 💾 Backup & Restore');
console.log('• 🎨 Custom Content Management');
console.log('• ⚙️ Comprehensive Utilities');

console.log('\n✨ Test hoàn tất!');
