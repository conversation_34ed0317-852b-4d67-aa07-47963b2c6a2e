const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.StageInstanceUpdate,
    async execute(oldStageInstance, newStageInstance, client) {
        try {
            // Only handle topic changes
            if (oldStageInstance.topic === newStageInstance.topic) return;
            
            console.log(`✏️ Stage topic updated: ${oldStageInstance.topic} -> ${newStageInstance.topic} in ${newStageInstance.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newStageInstance.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'STAGE_TOPIC_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'STAGE_TOPIC_UPDATE',
                user: 'System',
                userId: null,
                action: 'Chủ đề stage được cập nhật',
                details: `Chủ đề stage đã được thay đổi từ **${oldStageInstance.topic || 'No topic'}** thành **${newStageInstance.topic || 'No topic'}**`,
                target: newStageInstance.topic || 'No topic',
                channel: newStageInstance.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the stage topic from audit logs
            try {
                const auditLogs = await newStageInstance.guild.fetchAuditLogs({
                    type: 84, // STAGE_INSTANCE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newStageInstance.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Chủ đề stage được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for stage topic update');
            }
            
            // Add to database
            await client.db.addAuditLog(newStageInstance.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Chủ đề stage được cập nhật',
                `Vừa có chủ đề stage được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> ID stage',
                    value: `- ${newStageInstance.id}`,
                    inline: true
                },
                {
                    name: '> Kênh stage',
                    value: `- ${newStageInstance.channel || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Chủ đề cũ',
                    value: `- ${oldStageInstance.topic || 'Không có chủ đề'}`,
                    inline: true
                },
                {
                    name: '> Chủ đề mới',
                    value: `- ${newStageInstance.topic || 'Không có chủ đề'}`,
                    inline: true
                },
                {
                    name: '> Thời gian thay đổi',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add privacy level
            const privacyLevels = {
                1: '🌍 Public (Mọi người có thể tham gia)',
                2: '🏠 Guild Only (Chỉ thành viên server)'
            };
            
            embed.addFields({
                name: '> Mức độ riêng tư',
                value: `- ${privacyLevels[newStageInstance.privacyLevel] || 'Unknown'}`,
                inline: true
            });
            
            // Add stage duration so far
            if (newStageInstance.createdTimestamp) {
                const duration = Date.now() - newStageInstance.createdTimestamp;
                const hours = Math.floor(duration / (1000 * 60 * 60));
                const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
                
                embed.addFields([
                    {
                        name: '> Stage bắt đầu lúc',
                        value: `- <t:${Math.floor(newStageInstance.createdTimestamp / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Đã diễn ra được',
                        value: `- ${hours} giờ, ${minutes} phút`,
                        inline: true
                    }
                ]);
            }
            
            // Add channel info
            if (newStageInstance.channel) {
                embed.addFields([
                    {
                        name: '> ID kênh',
                        value: `- ${newStageInstance.channel.id}`,
                        inline: true
                    },
                    {
                        name: '> Số người trong stage',
                        value: `- ${newStageInstance.channel.members?.size || 0} thành viên`,
                        inline: true
                    }
                ]);
            }
            
            // Add impact of topic change
            embed.addFields({
                name: '> 📝 Tác động',
                value: [
                    '• Chủ đề mới sẽ hiển thị cho tất cả người tham gia',
                    '• Giúp định hướng cuộc thảo luận',
                    '• Người mới tham gia sẽ biết nội dung stage',
                    '• Không ảnh hưởng đến người đang tham gia'
                ].join('\n'),
                inline: false
            });
            
            // Add topic guidelines
            embed.addFields({
                name: '> 💡 Lưu ý về chủ đề stage',
                value: [
                    '• Nên rõ ràng và mô tả đúng nội dung',
                    '• Tránh chủ đề gây tranh cãi không cần thiết',
                    '• Có thể thay đổi theo hướng thảo luận',
                    '• Giúp thu hút đúng đối tượng tham gia'
                ].join('\n'),
                inline: false
            });
            
            // Add stage link
            if (newStageInstance.channel) {
                embed.addFields({
                    name: '> Link tham gia stage',
                    value: `- [Nhấn để tham gia](https://discord.com/channels/${newStageInstance.guild.id}/${newStageInstance.channel.id})`,
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do thay đổi',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newStageInstance.guild.name} • Stage Topic Update`,
                iconURL: newStageInstance.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Stage topic update logged: ${oldStageInstance.topic} -> ${newStageInstance.topic}`);
            
        } catch (error) {
            console.error('Error in stageTopicUpdate audit log:', error);
        }
    }
};
