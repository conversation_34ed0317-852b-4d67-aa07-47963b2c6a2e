const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelPinsUpdate,
    async execute(channel, time, client) {
        try {
            console.log(`📌 Channel pins updated: ${channel.name} in ${channel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(channel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_PINS_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_PINS_UPDATE',
                user: 'System',
                userId: null,
                action: 'Tin nhắn ghim được cập nhật',
                details: `Tin nhắn ghim trong kênh **${channel.name}** đã được cập nhật`,
                target: channel.name,
                channel: channel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated pins from audit logs
            try {
                const auditLogs = await channel.guild.fetchAuditLogs({
                    type: 74, // MESSAGE_PIN or MESSAGE_UNPIN
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.extra?.channel?.id === channel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Tin nhắn ghim được cập nhật bởi ${auditEntry.executor.tag}`;
                }
            } catch (error) {
                console.log('Could not fetch audit logs for pins update');
            }
            
            // Add to database
            await client.db.addAuditLog(channel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📌 Tin nhắn ghim cập nhật',
                `Vừa có tin nhắn ghim được cập nhật trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${channel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${channel.id}`,
                    inline: true
                },
                {
                    name: '> Người thực hiện',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian cập nhật',
                    value: `- <t:${Math.floor((time || Date.now()) / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Loại kênh',
                    value: `- ${channel.type === 0 ? 'Text Channel' : 'Other'}`,
                    inline: true
                }
            ]);
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${channel.guild.name} • Channel Pins Update`,
                iconURL: channel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel pins update logged: ${channel.name}`);
            
        } catch (error) {
            console.error('Error in channelPinsUpdate audit log:', error);
        }
    }
};
