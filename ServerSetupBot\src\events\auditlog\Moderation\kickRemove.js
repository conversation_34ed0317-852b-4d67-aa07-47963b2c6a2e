const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildMemberAdd,
    async execute(member, client) {
        try {
            // Check if this user was recently kicked and is now rejoining
            const recentKick = await client.db.getRecentKick(member.guild.id, member.user.id);
            if (!recentKick) return;
            
            console.log(`🔄 Previously kicked user rejoined: ${member.user.tag} in ${member.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(member.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'KICK_REMOVE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'KICK_REMOVE',
                user: member.user.tag,
                userId: member.user.id,
                action: '<PERSON>hành viên bị kick đã quay lại',
                details: `**${member.user.tag}** đã quay lại server sau khi bị kick`,
                target: member.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(member.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔄 Thành viên bị kick đã quay lại',
                `Thành viên đã quay lại server sau khi bị kick`
            );
            
            embed.setColor(0x3498db); // Blue for return
            
            const timeSinceKick = Date.now() - new Date(recentKick.kickedAt).getTime();
            const daysSinceKick = Math.floor(timeSinceKick / (1000 * 60 * 60 * 24));
            const hoursSinceKick = Math.floor((timeSinceKick % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${member.user.tag} (${member.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${member.user.id}`,
                    inline: true
                },
                {
                    name: '> Thời gian quay lại',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Thời gian sau khi kick',
                    value: `- ${daysSinceKick} ngày ${hoursSinceKick} giờ`,
                    inline: true
                }
            ]);
            
            // Add kick info
            if (recentKick.moderator) {
                embed.addFields({
                    name: '> Moderator đã kick',
                    value: `- ${recentKick.moderator}`,
                    inline: true
                });
            }
            
            if (recentKick.reason) {
                embed.addFields({
                    name: '> Lý do kick trước đó',
                    value: `- ${recentKick.reason}`,
                    inline: false
                });
            }
            
            embed.addFields({
                name: '> Thời gian bị kick',
                value: `- <t:${Math.floor(new Date(recentKick.kickedAt).getTime() / 1000)}:F>`,
                inline: true
            });
            
            // Add monitoring recommendations
            embed.addFields({
                name: '> 👀 Khuyến nghị theo dõi',
                value: [
                    '• Theo dõi hành vi của thành viên',
                    '• Kiểm tra xem có vi phạm lại không',
                    '• Có thể cần thời gian thử thách',
                    '• Thông báo cho team moderation'
                ].join('\n'),
                inline: false
            });
            
            // Add quick return warning if applicable
            if (timeSinceKick < 24 * 60 * 60 * 1000) { // Less than 24 hours
                embed.addFields({
                    name: '> ⚠️ Quay lại nhanh',
                    value: [
                        '• Thành viên quay lại trong vòng 24h',
                        '• Cần theo dõi đặc biệt',
                        '• Có thể chưa hiểu rõ lý do kick',
                        '• Cân nhắc giải thích quy tắc'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0xe67e22); // Orange for quick return
            }
            
            // Set user avatar as thumbnail
            if (member.user.displayAvatarURL()) {
                embed.setThumbnail(member.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${member.guild.name} • Kicked Member Return`,
                iconURL: member.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Kick remove (return) logged: ${member.user.tag}`);
            
        } catch (error) {
            console.error('Error in kickRemove audit log:', error);
        }
    }
};
