const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle bitrate changes for voice channels
            if (oldChannel.bitrate === newChannel.bitrate || newChannel.type !== 2) return;
            
            console.log(`🎵 Channel bitrate updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_BITRATE_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_BITRATE_UPDATE',
                user: 'System',
                userId: null,
                action: 'Bitrate kênh thoại được thay đổi',
                details: `Bitrate của kênh thoại **${newChannel.name}** đã được thay đổi từ ${oldChannel.bitrate}kbps thành ${newChannel.bitrate}kbps`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the bitrate from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Bitrate kênh thoại được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for bitrate update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🎵 Bitrate kênh thoại thay đổi',
                `Vừa có bitrate kênh thoại được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Determine quality level
            const getQualityLevel = (bitrate) => {
                if (bitrate >= 320) return '🔥 Cao nhất (320kbps)';
                if (bitrate >= 256) return '⭐ Rất cao (256kbps)';
                if (bitrate >= 128) return '✅ Cao (128kbps)';
                if (bitrate >= 96) return '📊 Trung bình (96kbps)';
                if (bitrate >= 64) return '📉 Thấp (64kbps)';
                return '⚠️ Rất thấp';
            };
            
            embed.addFields([
                {
                    name: '> Kênh thoại',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Bitrate cũ',
                    value: `- ${oldChannel.bitrate}kbps`,
                    inline: true
                },
                {
                    name: '> Bitrate mới',
                    value: `- ${newChannel.bitrate}kbps`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Chất lượng cũ',
                    value: `- ${getQualityLevel(oldChannel.bitrate)}`,
                    inline: true
                },
                {
                    name: '> Chất lượng mới',
                    value: `- ${getQualityLevel(newChannel.bitrate)}`,
                    inline: true
                },
                {
                    name: '> Số người trong kênh',
                    value: `- ${newChannel.members.size} thành viên`,
                    inline: true
                }
            ]);
            
            // Add impact note
            const impact = newChannel.bitrate > oldChannel.bitrate ? 'tăng' : 'giảm';
            embed.addFields({
                name: '> Tác động',
                value: `- Chất lượng âm thanh sẽ ${impact}`,
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Bitrate Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel bitrate update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelBitrateUpdate audit log:', error);
        }
    }
};
