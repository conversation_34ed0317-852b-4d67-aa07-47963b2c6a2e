const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ApplicationCommandPermissionsUpdate,
    async execute(data, client) {
        try {
            console.log(`⚙️ App command permissions updated in ${data.guild?.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(data.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'APP_COMMAND_PERMISSION_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'APP_COMMAND_PERMISSION_UPDATE',
                user: 'System',
                userId: null,
                action: 'Quyền lệnh ứng dụng được cập nhật',
                details: `Quyền lệnh cho ứng dụng **${data.applicationId}** đã được cập nhật`,
                target: data.applicationId,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(data.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '⚙️ Quyền lệnh ứng dụng cập nhật',
                `Vừa có quyền lệnh ứng dụng được cập nhật`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> ID ứng dụng',
                    value: `- ${data.applicationId}`,
                    inline: true
                },
                {
                    name: '> Loại cập nhật',
                    value: `- Quyền lệnh`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add permissions info if available
            if (data.permissions && data.permissions.length > 0) {
                const permissionsList = data.permissions.map(perm => {
                    const type = perm.type === 1 ? 'Role' : perm.type === 2 ? 'User' : 'Channel';
                    const permission = perm.permission ? 'Allowed' : 'Denied';
                    return `• ${type}: <@${perm.id}> - ${permission}`;
                }).join('\n');
                
                embed.addFields({
                    name: '> Chi tiết quyền',
                    value: permissionsList.length > 1000 ? permissionsList.substring(0, 1000) + '...' : permissionsList,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${data.guild.name} • App Command Permission Update`,
                iconURL: data.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ App command permission update logged: ${data.applicationId}`);
            
        } catch (error) {
            console.error('Error in appCommandPermissionUpdate audit log:', error);
        }
    }
};
