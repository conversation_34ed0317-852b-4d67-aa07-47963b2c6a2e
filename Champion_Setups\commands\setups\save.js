const Command = require("../../structures/Command");
const Discord = require("discord.js");
const yml = require("js-yaml");
const fs = require("fs");

module.exports = class Save extends Command {
  constructor(client) {
    super(client, {
      name: "save",
      description: client.cmdConfig.save.description,
      usage: client.cmdConfig.save.usage,
      permissions: client.cmdConfig.save.permissions,
      aliases: client.cmdConfig.save.aliases,
      category: "setups",
      listed: client.cmdConfig.save.enabled,
      slash: true,
    });
  }

  async run(message, args) {
    message.channel.send({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.setup.server_saving, this.client.embeds.general_color)] });
    
    let channelList = message.guild.channels.cache.map((c, i) => {
      let parent = message.guild.channels.cache.get(c.parentId) || null;
      
      let channelType = "TEXT";
      if(c.type == 0) channelType = "TEXT";
      else if(c.type == 2) channelType = "VOICE";
      else if(c.type == 14) channelType = "CATEGORY";
      
      return `${parent == null ? null : parent.name} | ${c.name} | ${channelType} | ${i} | ${c.topic || null}`;
    });
    
    let roleList = [];
    
    message.guild.roles.cache.map((r) => {
      let roleObj = {
        name: r.name,
        color: r.hexColor,
        hoisted: r.hoisted, 
        permissions: new Discord.PermissionsBitField(r.permissions).toArray()
      };
      
      return roleList.push(roleObj);
    });
    
    let permList = {};
    message.guild.channels.cache.forEach(async(c, i) => {
      c.permissionOverwrites.cache.map((p) => {
        if(p.type == "role") {
          let role = this.client.utils.findRole(message.guild, p.id).name;
          let allow = new Discord.PermissionsBitField(p.allow).toArray();
          let deny = new Discord.PermissionsBitField(p.deny).toArray();
          
          permList[i] = [{
            role, 
            allow, 
            deny
          }];
        }
      });
    });

    let emojiList = [];
    message.guild.emojis.cache.map(async(e) => {
      let emojiObj = {
        name: e.name,
        url: e.url
      };
      
      return emojiList.push(emojiObj);
    });
    
    let serverData = {
      channels: channelList, 
      channelPermissions: permList, 
      roles: roleList, 
      emojis: emojiList,
      extra: {
        verificationLevel: this.client.config.save.verifyLevel == true ? message.guild.verificationLevel : null,
        banner: this.client.config.save.banner == true ? message.guild.bannerURL({ format: 'png' }) : null,
        icon: this.client.config.save.icon == true ? message.guild.iconURL({ format: 'png' }) : null
      }
    };
    
    let template = yml.dump(serverData);
    let regex = / /g;
    
    let path = `./configs/templates/${message.guild.name.toLowerCase().replace(regex, "")}.yml`;
    
    fs.writeFileSync(path, template);
    
    message.channel.send({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.setup.server_saved.replace("<name>", `${message.guild.name.toLowerCase().replace(regex, "")}`), this.client.embeds.success_color)] });
  }
  async slashRun(interaction, args) {
    interaction.reply({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.setup.server_saving, this.client.embeds.general_color)], ephemeral: this.client.cmdConfig.save.ephemeral });

    let channelList = interaction.guild.channels.cache.map((c, i) => {
      let parent = interaction.guild.channels.cache.get(c.parentId) || null;

      let channelType = "TEXT";
      if(c.type == 0) channelType = "TEXT";
      else if(c.type == 2) channelType = "VOICE";
      else if(c.type == 14) channelType = "CATEGORY";
      
      return `${parent == null ? null : parent.name} | ${c.name} | ${channelType} | ${i} | ${c.topic || null}`;
    });

    let roleList = [];

    interaction.guild.roles.cache.map((r) => {
      let roleObj = {
        name: r.name,
        color: r.hexColor,
        hoisted: r.hoisted,
        permissions: new Discord.PermissionsBitField(r.permissions).toArray()
      };

      return roleList.push(roleObj);
    });

    let permList = {};
    interaction.guild.channels.cache.forEach(async (c, i) => {
      c.permissionOverwrites.cache.map((p) => {
        if (p.type == "role") {
          let role = this.client.utils.findRole(interaction.guild, p.id).name;
          let allow = new Discord.PermissionsBitField(p.allow).toArray();
          let deny = new Discord.PermissionsBitField(p.deny).toArray();

          permList[i] = [{
            role,
            allow,
            deny
          }];
        }
      });
    });

    let serverData = {
      channels: channelList,
      channelPermissions: permList,
      roles: roleList,
      emojis: [],
      extra: {
        verificationLevel: this.client.config.save.verifyLevel == true ? interaction.guild.verificationLevel : null,
        banner: this.client.config.save.banner == true ? interaction.guild.bannerURL({ format: 'png' }) : null,
        icon: this.client.config.save.icon == true ? interaction.guild.iconURL({ format: 'png' }) : null
      }
    };

    let template = yml.dump(serverData);
    let regex = / /g;

    let path = `./configs/templates/${interaction.guild.name.toLowerCase().replace(regex, "")}.yml`;

    fs.writeFileSync(path, template);

    interaction.reply({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.setup.server_saved.replace("<name>", `${interaction.guild.name.toLowerCase().replace(regex, "")}`), this.client.embeds.success_color)], ephemeral: this.client.cmdConfig.save.ephemeral });
  }
};
