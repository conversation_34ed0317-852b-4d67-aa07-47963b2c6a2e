(function(_0xa0206,_0xd62871){const _0x145172=_0xa0206();function _0x1d39ed(_0x39b37e,_0x15ea3c){return _0x2d09(_0x15ea3c-0x103,_0x39b37e);}while(!![]){try{const _0x1c206b=-parseInt(_0x1d39ed(0x2b6,0x284))/0x1*(-parseInt(_0x1d39ed(0x292,0x29e))/0x2)+parseInt(_0x1d39ed(0x29d,0x279))/0x3*(parseInt(_0x1d39ed(0x29d,0x276))/0x4)+-parseInt(_0x1d39ed(0x26d,0x27c))/0x5+-parseInt(_0x1d39ed(0x26c,0x27d))/0x6*(parseInt(_0x1d39ed(0x245,0x271))/0x7)+-parseInt(_0x1d39ed(0x2a1,0x2b5))/0x8*(-parseInt(_0x1d39ed(0x27a,0x28e))/0x9)+parseInt(_0x1d39ed(0x2c0,0x2b8))/0xa*(-parseInt(_0x1d39ed(0x2ce,0x2a8))/0xb)+parseInt(_0x1d39ed(0x2b4,0x2c5))/0xc*(-parseInt(_0x1d39ed(0x2a9,0x28c))/0xd);if(_0x1c206b===_0xd62871)break;else _0x145172['push'](_0x145172['shift']());}catch(_0x3a75a7){_0x145172['push'](_0x145172['shift']());}}}(_0x7299,0x44588));const {EmbedBuilder,SlashCommandBuilder,ActionRowBuilder,StringSelectMenuBuilder,ButtonBuilder,ButtonStyle}=require(_0x4d31bc(-0x1e4,-0x1c6)),User=require('../../../models/UserData'),fs=require('fs'),yaml=require(_0x4d31bc(-0x224,-0x1fd)),config=yaml[_0x4d31bc(-0x1a5,-0x1d9)](fs[_0x4d31bc(-0x207,-0x211)](_0x4d31bc(-0x223,-0x1f0),_0x4d31bc(-0x1dd,-0x1d1)));function _0x4d31bc(_0x5f019a,_0x490c0e){return _0x2d09(_0x490c0e- -0x382,_0x5f019a);}const lang=yaml['load'](fs[_0x4d31bc(-0x236,-0x211)]('./lang.yml','utf8')),{replacePlaceholders}=require(_0x4d31bc(-0x1a7,-0x1ba)),userCache={};function _0x2d09(_0x31d192,_0x29f38a){const _0x729936=_0x7299();return _0x2d09=function(_0x2d09de,_0x4511f0){_0x2d09de=_0x2d09de-0x164;let _0x73629d=_0x729936[_0x2d09de];return _0x73629d;},_0x2d09(_0x31d192,_0x29f38a);}module[_0x4d31bc(-0x194,-0x1c3)]={'data':new SlashCommandBuilder()['setName'](_0x4d31bc(-0x1c5,-0x1d7))['setDescription']('Check\x20your\x20balance\x20or\x20another\x20user\x27s\x20balance')['addUserOption'](_0x268caa=>_0x268caa[_0x4d31bc(-0x1b9,-0x1e8)](_0x4d31bc(-0x1c0,-0x1d3))[_0x4d31bc(-0x209,-0x1ff)]('The\x20user\x20to\x20check\x20the\x20balance\x20of'))['addStringOption'](_0x176cf8=>_0x176cf8['setName'](_0x4d31bc(-0x218,-0x212))[_0x4d31bc(-0x1e4,-0x1ff)]('Type\x20of\x20balance\x20check')[_0x4d31bc(-0x1df,-0x1f4)]({'name':_0x4d31bc(-0x1cf,-0x1db),'value':_0x4d31bc(-0x233,-0x20d)})),'category':_0x4d31bc(-0x228,-0x204),async 'execute'(_0x2d996e){const _0x41ed77=_0x2d996e[_0x2a20a8(0x2da,0x2dc)][_0x2a20a8(0x289,0x28a)](_0x2a20a8(0x2e6,0x2d2)),_0x2c0485=_0x2d996e['options'][_0x2a20a8(0x2b8,0x29b)](_0x2a20a8(0x27b,0x293)),_0x193257=_0x41ed77||_0x2d996e[_0x2a20a8(0x2f1,0x2d2)];function _0x2a20a8(_0xface17,_0xcbc7c){return _0x4d31bc(_0xface17,_0xcbc7c-0x4a5);}const _0x1ed019=_0x193257['id']+'-'+_0x2d996e[_0x2a20a8(0x304,0x2ec)]['id'];let _0x49270c,_0x305bfc;_0x2c0485===_0x2a20a8(0x296,0x298)?(_0x305bfc={'balance':0x1,'bank':0x1,'transactionLogs':0x1},userCache[_0x1ed019]?_0x49270c=userCache[_0x1ed019]:(_0x49270c=await User['findOne']({'userId':_0x193257['id'],'guildId':_0x2d996e[_0x2a20a8(0x2c8,0x2ec)]['id']},_0x305bfc),!_0x49270c&&(_0x49270c={'balance':0x0,'bank':0x0,'transactionLogs':[]}),userCache[_0x1ed019]=_0x49270c)):(_0x305bfc={'balance':0x1,'bank':0x1},_0x49270c=await User[_0x2a20a8(0x26c,0x29e)]({'userId':_0x193257['id'],'guildId':_0x2d996e[_0x2a20a8(0x2c2,0x2ec)]['id']},_0x305bfc),!_0x49270c&&(_0x49270c={'balance':0x0,'bank':0x0}));const _0x23564a=_0x1259eb=>{function _0x19b422(_0x56daf2,_0x1b0cb5){return _0x2a20a8(_0x1b0cb5,_0x56daf2- -0x43e);}return new Intl[(_0x19b422(-0x16b,-0x17e))](_0x19b422(-0x1b3,-0x192),{'minimumFractionDigits':0x2,'maximumFractionDigits':0x2})[_0x19b422(-0x1ae,-0x1b2)](_0x1259eb);},_0x4e067=_0x2d8556=>{function _0x4f9523(_0x3ad072,_0x2eff17){return _0x2a20a8(_0x3ad072,_0x2eff17- -0xcd);}return _0x2d8556['replace'](/[_-]/g,'\x20')[_0x4f9523(0x1c9,0x1d8)](/\b\w/g,_0x590aa8=>_0x590aa8[_0x4f9523(0x1f3,0x21a)]());};if(_0x2c0485==='log'){let _0x3f8132=0x0,_0x279b6b=0x0;_0x49270c['transactionLogs'][_0x2a20a8(0x2e0,0x2d6)](_0x214c3f=>{function _0x1886cf(_0x30d30b,_0x506e93){return _0x2a20a8(_0x30d30b,_0x506e93-0x135);}_0x214c3f[_0x1886cf(0x412,0x419)]>0x0?_0x3f8132+=_0x214c3f[_0x1886cf(0x43b,0x419)]:_0x279b6b+=Math[_0x1886cf(0x3d0,0x3e2)](_0x214c3f[_0x1886cf(0x420,0x419)]);});const _0x329fff={'games':[_0x2a20a8(0x30b,0x2e1),_0x2a20a8(0x297,0x2c9),'blackjack_lose','coinflip','roll','roulette',_0x2a20a8(0x2a9,0x2d0)],'purchases':[_0x2a20a8(0x277,0x2aa)],'interest':[_0x2a20a8(0x29e,0x2a9)],'other':[_0x2a20a8(0x2a6,0x2c0),_0x2a20a8(0x2e9,0x2cf),_0x2a20a8(0x2a0,0x2b4),'deposit','admin-give-balance',_0x2a20a8(0x2ad,0x2c7),'admin-take-balance',_0x2a20a8(0x287,0x2b2),_0x2a20a8(0x2c3,0x2e3),_0x2a20a8(0x2ef,0x2e8),_0x2a20a8(0x290,0x2c2),'robbed',_0x2a20a8(0x2df,0x2c5),_0x2a20a8(0x291,0x297),_0x2a20a8(0x297,0x288)]};let _0x2d6075=_0x2a20a8(0x28f,0x2a2),_0x846fb9=0x1;const _0x40f109=0xa,_0x5244f0=_0x8e5173=>{function _0x32e746(_0xde3566,_0xa2b811){return _0x2a20a8(_0xde3566,_0xa2b811-0x54);}if(_0x8e5173===_0x32e746(0x2f0,0x2f6))return _0x49270c[_0x32e746(0x34f,0x32b)];return _0x49270c[_0x32e746(0x34e,0x32b)]['filter'](_0x592816=>_0x329fff[_0x8e5173][_0x32e746(0x304,0x32f)](_0x592816[_0x32e746(0x30c,0x2e7)]));},_0x58f094=_0x1ef94d=>{function _0x4a249b(_0x256808,_0x344bca){return _0x2a20a8(_0x344bca,_0x256808- -0x17e);}const _0x10d05b=_0x1ef94d[_0x4a249b(0x16f,0x162)]((_0x846fb9-0x1)*_0x40f109,_0x846fb9*_0x40f109),_0x24e36e=_0x10d05b['map'](_0x2e1f71=>{let _0x4120f6,_0x52d95a=_0x23564a(Math[_0x2eb552(-0x225,-0x244)](_0x2e1f71['amount']));function _0x2eb552(_0x5f0a06,_0x219055){return _0x4a249b(_0x5f0a06- -0x354,_0x219055);}const _0x24a689=_0x4e067(_0x2e1f71[_0x2eb552(-0x23f,-0x246)]),_0x23d36b='🪙';_0x2e1f71[_0x2eb552(-0x23f,-0x272)]===_0x2eb552(-0x218,-0x1fd)?_0x4120f6='-':_0x4120f6=_0x2e1f71[_0x2eb552(-0x1ee,-0x1d9)]>0x0?'+':'-';const _0x40cf91=''+_0x4120f6+_0x52d95a;if(_0x40cf91===_0x2eb552(-0x213,-0x211)&&_0x2e1f71[_0x2eb552(-0x23f,-0x254)]===_0x2eb552(-0x229,-0x1fb))return null;return _0x40cf91+'\x20'+_0x23d36b+'\x20('+_0x24a689+')';})[_0x4a249b(0x13e,0x141)](_0x24db69=>_0x24db69!==null)[_0x4a249b(0x15f,0x177)]('\x0a'),_0x25f533=_0x23564a(_0x3f8132),_0x3d2b11=_0x23564a(_0x279b6b),_0x566ead=_0x24e36e?'```diff\x0a'+_0x24e36e+_0x4a249b(0x122,0xf6):'```prolog\x0aNo\x20Transactions\x20Found```';return new EmbedBuilder()[_0x4a249b(0x131,0x139)](replacePlaceholders(lang[_0x4a249b(0x123,0xf7)][_0x4a249b(0x121,0x116)][_0x4a249b(0x14f,0x144)],{'user':_0x193257['username']}))[_0x4a249b(0x128,0x14b)](_0x566ead)['addFields']({'name':lang[_0x4a249b(0x123,0x135)]['Messages'][_0x4a249b(0x139,0x15c)],'value':'+'+_0x25f533+_0x4a249b(0x138,0x132),'inline':!![]},{'name':lang['Economy']['Messages']['lost'],'value':'-'+_0x3d2b11+_0x4a249b(0x138,0x13b),'inline':!![]})[_0x4a249b(0x13a,0x13e)]('#00FF00');},_0x4dcd40=async()=>{const _0x36fb16=_0x5244f0(_0x2d6075),_0x1b1ec3=Math['ceil'](_0x36fb16['length']/_0x40f109),_0x442249=_0x58f094(_0x36fb16);function _0x3441af(_0x14ac14,_0x551171){return _0x2a20a8(_0x14ac14,_0x551171- -0x67);}const _0x2b0b60=new StringSelectMenuBuilder()[_0x3441af(0x254,0x283)](_0x3441af(0x28e,0x279))['setPlaceholder']('Select\x20category')[_0x3441af(0x218,0x233)]({'label':'All','value':_0x3441af(0x266,0x23b),'description':_0x3441af(0x22b,0x220),'emoji':'📜'},{'label':_0x3441af(0x20d,0x225),'value':_0x3441af(0x27b,0x254),'description':'View\x20logs\x20from\x20games\x20like\x20blackjack,\x20roulette,\x20etc.','emoji':'🎮'},{'label':'Purchases','value':_0x3441af(0x247,0x228),'description':'View\x20logs\x20of\x20your\x20purchases','emoji':'🛒'},{'label':_0x3441af(0x248,0x226),'value':'interest','description':_0x3441af(0x276,0x24c),'emoji':'💰'},{'label':_0x3441af(0x23d,0x22b),'value':_0x3441af(0x223,0x23c),'description':_0x3441af(0x2a0,0x273),'emoji':'✨'}),_0x504450=new ActionRowBuilder()[_0x3441af(0x273,0x287)](_0x2b0b60),_0x3d0dd8=new ActionRowBuilder()[_0x3441af(0x299,0x287)](new ButtonBuilder()[_0x3441af(0x25d,0x283)]('previous')[_0x3441af(0x214,0x240)](_0x3441af(0x24c,0x25f))[_0x3441af(0x282,0x282)](ButtonStyle[_0x3441af(0x1f6,0x227)])['setDisabled'](_0x846fb9<=0x1),new ButtonBuilder()['setCustomId'](_0x3441af(0x235,0x249))[_0x3441af(0x26b,0x240)]('Next')[_0x3441af(0x26a,0x282)](ButtonStyle[_0x3441af(0x224,0x227)])[_0x3441af(0x28d,0x25d)](_0x846fb9>=_0x1b1ec3));await _0x2d996e[_0x3441af(0x26a,0x25c)]({'content':'','embeds':[_0x442249],'components':[_0x504450,_0x3d0dd8]});};await _0x2d996e[_0x2a20a8(0x2fd,0x2cb)]({'content':_0x2a20a8(0x2c6,0x2de),'fetchReply':!![]}),_0x4dcd40();const _0x418ca5=_0x2d996e['channel'][_0x2a20a8(0x2c7,0x2b9)]({'time':0xea60});_0x418ca5['on'](_0x2a20a8(0x2b2,0x2c1),async _0x150201=>{_0x150201['customId']===_0x8d1ac8(-0xf2,-0xc8)&&(_0x2d6075=_0x150201[_0x8d1ac8(-0xec,-0x114)][0x0],_0x846fb9=0x1,await _0x4dcd40(),await _0x150201[_0x8d1ac8(-0x127,-0x126)]());function _0x8d1ac8(_0x1b9ce3,_0x2ea376){return _0x2a20a8(_0x2ea376,_0x1b9ce3- -0x3d2);}_0x150201[_0x8d1ac8(-0x13d,-0x128)]===_0x8d1ac8(-0xf9,-0xd8)&&(_0x846fb9--,await _0x4dcd40(),await _0x150201[_0x8d1ac8(-0x127,-0x131)]()),_0x150201[_0x8d1ac8(-0x13d,-0x12b)]===_0x8d1ac8(-0x122,-0x12b)&&(_0x846fb9++,await _0x4dcd40(),await _0x150201['deferUpdate']());}),_0x418ca5['on']('end',async()=>{delete userCache[_0x1ed019];function _0x2d44e4(_0x2d75d8,_0x248fa3){return _0x2a20a8(_0x2d75d8,_0x248fa3- -0x172);}await _0x2d996e[_0x2d44e4(0x184,0x151)]({'components':[]});});}else{const _0x4578d5={'user':'<@'+_0x193257['id']+'>','balance':_0x23564a(_0x49270c['balance']),'bank':_0x23564a(_0x49270c[_0x2a20a8(0x267,0x289)])},_0x300de2=_0x193257['id']===_0x2d996e['user']['id']?replacePlaceholders(lang[_0x2a20a8(0x28b,0x2a1)][_0x2a20a8(0x29c,0x29f)][_0x2a20a8(0x2c5,0x2ce)],_0x4578d5):replacePlaceholders(lang[_0x2a20a8(0x2b6,0x2a1)]['Messages']['otherBalance'],_0x4578d5),_0x2cc385=new EmbedBuilder()[_0x2a20a8(0x2d9,0x2a6)](_0x300de2)[_0x2a20a8(0x2e4,0x2b8)](_0x2a20a8(0x2d6,0x2d1));return _0x2d996e[_0x2a20a8(0x2d5,0x2cb)]({'embeds':[_0x2cc385]});}}};function _0x7299(){const _0x5dd9c7=['values','toUpperCase','admin-set-bank','setStyle','setCustomId','./Utility/helpers','guild','slice','addComponents','View\x20all\x20transaction\x20logs','work','bank','getUser','en-US','Games','Interest','Primary','purchases','format','80437lztOue','Other','type','readFileSync','customId','10636OvTMqB','transfer_in','log','387mhsfNs','addOptions','getString','339900azNmMu','60DOHlEJ','findOne','Messages','\x0a```','Economy','all','other','139gCgdMI','replace','setDescription','setLabel','js-yaml','interest','purchase','deferUpdate','26Esiwox','abs','9TYTrhA','setTitle','next','addChoices','admin-take-bank','View\x20logs\x20of\x20bank\x20interest','daily','./config.yml','\x20🪙','gained','setColor','createMessageComponentCollector','blackjack_lose','games','filter','setName','6652Gpwhie','-0.00','beg','collect','rob','editReply','setDisabled','transfer_out','Previous','admin-give-bank','2785079XIaHnv','blackjack_draw','Log','reply','load','transactionLog','balance','crime','slot','#00FF00','user','NumberFormat','utf8','1996504FTwjwL','forEach','transactionLogs','20qPdvcw','previous','View\x20other\x20transaction\x20logs','includes','options','join','Loading...','discord.js','category_select','blackjack_win','exports','admin-set-balance','amount','514056XgzZeK'];_0x7299=function(){return _0x5dd9c7;};return _0x7299();}