const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle user limit changes for voice channels
            if (oldChannel.userLimit === newChannel.userLimit || newChannel.type !== 2) return;
            
            console.log(`👥 Channel user limit updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_USER_LIMIT_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_USER_LIMIT_UPDATE',
                user: 'System',
                userId: null,
                action: 'Giới hạn người dùng kênh thoại được thay đổi',
                details: `Giới hạn người dùng của kênh thoại **${newChannel.name}** đã được thay đổi từ ${oldChannel.userLimit || 'Không giới hạn'} thành ${newChannel.userLimit || 'Không giới hạn'}`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the user limit from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Giới hạn người dùng được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for user limit update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '👥 Giới hạn người dùng kênh thoại thay đổi',
                `Vừa có giới hạn người dùng kênh thoại được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Format limit display
            const formatLimit = (limit) => {
                if (limit === 0 || limit === null) return 'Không giới hạn';
                return `${limit} người`;
            };
            
            embed.addFields([
                {
                    name: '> Kênh thoại',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Giới hạn cũ',
                    value: `- ${formatLimit(oldChannel.userLimit)}`,
                    inline: true
                },
                {
                    name: '> Giới hạn mới',
                    value: `- ${formatLimit(newChannel.userLimit)}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Số người hiện tại',
                    value: `- ${newChannel.members.size} thành viên`,
                    inline: true
                }
            ]);
            
            // Add impact message
            if (newChannel.userLimit > 0 && oldChannel.userLimit === 0) {
                embed.addFields({
                    name: '> Tác động',
                    value: `- Kênh giờ đây có giới hạn ${newChannel.userLimit} người`,
                    inline: false
                });
                embed.setColor(0xe74c3c); // Red for restriction
            } else if (newChannel.userLimit === 0 && oldChannel.userLimit > 0) {
                embed.addFields({
                    name: '> Tác động',
                    value: `- Kênh giờ đây không có giới hạn người dùng`,
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for no restriction
            } else if (newChannel.userLimit > oldChannel.userLimit) {
                embed.addFields({
                    name: '> Tác động',
                    value: `- Có thể chứa thêm ${newChannel.userLimit - oldChannel.userLimit} người`,
                    inline: false
                });
            } else if (newChannel.userLimit < oldChannel.userLimit) {
                embed.addFields({
                    name: '> Tác động',
                    value: `- Giảm ${oldChannel.userLimit - newChannel.userLimit} chỗ`,
                    inline: false
                });
            }
            
            // Warning if current users exceed new limit
            if (newChannel.userLimit > 0 && newChannel.members.size > newChannel.userLimit) {
                embed.addFields({
                    name: '⚠️ Cảnh báo',
                    value: `- Hiện có ${newChannel.members.size} người nhưng giới hạn chỉ ${newChannel.userLimit}`,
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel User Limit Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel user limit update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelUserLimitUpdate audit log:', error);
        }
    }
};
