const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder } = require("discord.js");
const fs = require("fs");
const path = require("path");
const config = require("../../config.json");

const premiumFile = path.join(__dirname, "../../premium.json");

// Read premium data
function readPremiumData() {
    if (!fs.existsSync(premiumFile)) {
        fs.writeFileSync(premiumFile, JSON.stringify({ users: {} }, null, 2));
    }
    return JSON.parse(fs.readFileSync(premiumFile));
}

// Write premium data
function writePremiumData(data) {
    fs.writeFileSync(premiumFile, JSON.stringify(data, null, 2));
}

// Convert time to expiry timestamp
function getExpiryDate(time) {
    const now = Math.floor(Date.now() / 1000); // Get current time in seconds

    if (time === "10min") {
        return now + 10 * 60; // 10 minutes
    } else if (time.includes("month")) {
        const months = parseInt(time);
        return now + months * 30 * 24 * 60 * 60; // Approximate month calculation
    } else {
        return now + parseInt(time) * 24 * 60 * 60; // Custom days
    }
}

module.exports = {
    name: "premiumadd",
    description: "Add premium to a user",
    async execute(message, args) {
        const ownerId = config.ownerID.toString();

        if (message.author.id !== ownerId) {
            return message.reply("❌ Only the bot owner can use this command!");
        }

        let user = message.mentions.users.first() || message.guild.members.cache.get(args[0]);

        if (!user) {
            return message.reply("Usage: `!premiumadd <user>`");
        }

        let data = readPremiumData();

        if (data.users[user.id]) {
            return message.reply("❌ This user already has a premium plan!");
        }

        const embed = new EmbedBuilder()
            .setColor("#FFD700")
            .setTitle("📅 Select Premium Duration")
            .setDescription(`Please select the duration for **${user.username}**'s premium membership.`);

        const row = new ActionRowBuilder().addComponents(
            new StringSelectMenuBuilder()
                .setCustomId("premium_duration")
                .setPlaceholder("Choose duration")
                .addOptions([
                    { label: "10 Minutes", value: "10min", emoji: "⏳" },
                    { label: "1 Month", value: "1month", emoji: "📅" },
                    { label: "2 Months", value: "2month", emoji: "📆" },
                    { label: "3 Months", value: "3month", emoji: "📆" },
                    { label: "4 Months", value: "4month", emoji: "📆" },
                    { label: "5 Months", value: "5month", emoji: "📆" },
                    { label: "6 Months", value: "6month", emoji: "📆" },
                    { label: "Custom Time", value: "custom", emoji: "⌛" },
                ])
        );

        const msg = await message.channel.send({ embeds: [embed], components: [row] });

        // Collector for dropdown menu
        const collector = msg.createMessageComponentCollector({ time: 60000 });

        collector.on("collect", async (interaction) => {
            if (interaction.user.id !== message.author.id) {
                return interaction.reply({ content: "❌ You can't use this menu!", ephemeral: true });
            }

            let selectedTime = interaction.values[0];

            if (selectedTime === "custom") {
                await interaction.reply("⏳ Please enter custom duration in days (e.g., `30` for 30 days):");
                const filter = (m) => m.author.id === message.author.id;
                const collected = await message.channel.awaitMessages({ filter, max: 1, time: 30000 });

                let customTime = collected.first()?.content;
                if (!customTime || isNaN(customTime)) {
                    return message.channel.send("❌ Invalid time! Please try again.");
                }

                selectedTime = customTime;
            }

            let expiry = getExpiryDate(selectedTime);
            data.users[user.id] = { expiry };
            writePremiumData(data);

            const successEmbed = new EmbedBuilder()
                .setColor("#00FF00")
                .setTitle("✅ Premium Granted!")
                .setDescription(`**${user.username}** now has premium until <t:${expiry}:R>.`);

            await interaction.update({ embeds: [successEmbed], components: [] });
        });

        collector.on("end", () => {
            msg.edit({ components: [] }).catch(() => {});
        });
    }
};