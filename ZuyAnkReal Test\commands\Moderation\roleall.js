(function(_0xab685d,_0x53cee2){function _0x48d915(_0x555c94,_0x14abc8){return _0x14bb(_0x555c94- -0xd3,_0x14abc8);}const _0x165570=_0xab685d();while(!![]){try{const _0x22d6c4=parseInt(_0x48d915(-0x4e,-0x30))/0x1*(parseInt(_0x48d915(-0x5c,-0x65))/0x2)+parseInt(_0x48d915(-0x27,-0x49))/0x3+parseInt(_0x48d915(-0x3c,-0x39))/0x4+parseInt(_0x48d915(-0x1a,-0x1))/0x5+-parseInt(_0x48d915(-0x44,-0x52))/0x6+parseInt(_0x48d915(-0x31,-0x2f))/0x7*(-parseInt(_0x48d915(-0x25,-0x16))/0x8)+parseInt(_0x48d915(-0x4a,-0x6c))/0x9*(-parseInt(_0x48d915(-0x52,-0x30))/0xa);if(_0x22d6c4===_0x53cee2)break;else _0x165570['push'](_0x165570['shift']());}catch(_0x15db91){_0x165570['push'](_0x165570['shift']());}}}(_0x17b3,0x8474f));function _0x17b3(){const _0x39881e=['setStyle','./config.yml','RoleAllHighestRole','customId','remove','The\x20role\x20to\x20add\x20or\x20remove\x20from\x20all\x20users','42fiqoJh','Administrator','utf8','followUp','client','Confirm','position','load','js-yaml','add','518874gLusZl','update','879360rTYOOA','RoleAllSuccessRemove','channel','highest','The\x20interaction\x20has\x20expired\x20or\x20is\x20no\x20longer\x20valid.','The\x20action\x20to\x20perform:\x20add\x20or\x20remove','addChoices','createMessageComponentCollector','roles','Add\x20or\x20remove\x20a\x20role\x20from\x20all\x20users\x20in\x20the\x20guild','setDescription','4056220QECBnl','options','RoleAll','role','Error\x20in\x20executing\x20action:\x20','discord.js','end','cancel','roleall','members','Progress:\x20','error','setRequired','getRole','readFileSync','has','toString','52166yUQtnq','some','{role}','getString','RoleAllInProgress','collect','Cancel','User\x20doesn\x27t\x20have\x20high\x20enough\x20role\x20to\x20perform\x20the\x20action.\x20User\x20Highest\x20Role\x20Position:\x20','resolve','user','557720rfYFWK','setName','code','action','10xstoIo','editReply','setCustomId','ModerationRoles','72FgXNVS',',\x20Target\x20Role\x20Position:\x20','./lang.yml','reply','replace','addComponents','3179526qGHHec','setLabel','message','RoleAllConfirmationAdd','bot','exports','member','cache','3732512qoGGXm','Danger','size','RoleAllError','RoleAllCancelled'];_0x17b3=function(){return _0x39881e;};return _0x17b3();}const {SlashCommandBuilder,ActionRowBuilder,ButtonBuilder,ButtonStyle,PermissionsBitField}=require(_0x4aea02(0x1bb,0x1d8));function _0x4aea02(_0x2fd1a8,_0x21f092){return _0x14bb(_0x21f092-0x16d,_0x2fd1a8);}function _0x14bb(_0x4e8be9,_0x50f369){const _0x17b3f1=_0x17b3();return _0x14bb=function(_0x14bb00,_0x947893){_0x14bb00=_0x14bb00-0x69;let _0x4fbd4c=_0x17b3f1[_0x14bb00];return _0x4fbd4c;},_0x14bb(_0x4e8be9,_0x50f369);}const fs=require('fs'),yaml=require(_0x4aea02(0x22f,0x217)),config=yaml[_0x4aea02(0x22f,0x216)](fs[_0x4aea02(0x1e8,0x1e1)](_0x4aea02(0x1e4,0x20a),_0x4aea02(0x200,0x211))),lang=yaml['load'](fs['readFileSync'](_0x4aea02(0x1e4,0x1f8),_0x4aea02(0x1ff,0x211)));module[_0x4aea02(0x1fc,0x201)]={'data':new SlashCommandBuilder()[_0x4aea02(0x1e4,0x1ef)](_0x4aea02(0x1ff,0x1db))['setDescription'](_0x4aea02(0x1fc,0x224))['addStringOption'](_0x5de3e6=>_0x5de3e6[_0x4aea02(0x1c6,0x1ef)](_0x4aea02(0x218,0x1f1))[_0x4aea02(0x20c,0x225)](_0x4aea02(0x1f7,0x220))[_0x4aea02(0x204,0x1df)](!![])[_0x4aea02(0x23e,0x221)]({'name':_0x4aea02(0x1f4,0x218),'value':_0x4aea02(0x22d,0x218)},{'name':_0x4aea02(0x228,0x20d),'value':_0x4aea02(0x1f2,0x20d)}))['addRoleOption'](_0x56dcff=>_0x56dcff[_0x4aea02(0x1d6,0x1ef)](_0x4aea02(0x1d8,0x1d6))[_0x4aea02(0x203,0x225)](_0x4aea02(0x221,0x20e))['setRequired'](!![])),'category':'Moderation',async 'execute'(_0x1330e6){function _0x51c8f9(_0xcf219b,_0x3a3123){return _0x4aea02(_0xcf219b,_0x3a3123- -0x4e5);}try{const _0x4913a9=config[_0x51c8f9(-0x2cb,-0x2f0)][_0x51c8f9(-0x327,-0x30a)],_0x539691=_0x4913a9[_0x51c8f9(-0x2e0,-0x300)](_0x348b50=>_0x1330e6[_0x51c8f9(-0x2f0,-0x2e3)][_0x51c8f9(-0x2d0,-0x2c2)][_0x51c8f9(-0x2e3,-0x2e2)][_0x51c8f9(-0x306,-0x303)](_0x348b50)),_0x28a1df=_0x1330e6[_0x51c8f9(-0x300,-0x2e3)]['permissions'][_0x51c8f9(-0x301,-0x303)](PermissionsBitField['Flags'][_0x51c8f9(-0x2b7,-0x2d5)]);if(!_0x539691&&!_0x28a1df){await _0x1330e6['reply']({'content':lang['NoPermsMessage'],'ephemeral':!![]});return;}const _0x64a997=_0x1330e6[_0x51c8f9(-0x2dd,-0x2be)][_0x51c8f9(-0x2fb,-0x2fe)](_0x51c8f9(-0x31d,-0x2f4)),_0x3d463a=_0x1330e6['options'][_0x51c8f9(-0x30a,-0x305)](_0x51c8f9(-0x2ec,-0x30f)),_0x338a58=_0x1330e6['guild']['members'][_0x51c8f9(-0x310,-0x2f9)](_0x1330e6[_0x51c8f9(-0x2f4,-0x2d2)][_0x51c8f9(-0x2e8,-0x2f8)]['id']),_0xa6c6a9=_0x338a58[_0x51c8f9(-0x29d,-0x2c2)]['highest']['position'],_0x52e397=_0x3d463a[_0x51c8f9(-0x2a8,-0x2d0)],_0x1e03ed=_0x1330e6[_0x51c8f9(-0x2e3,-0x2e3)][_0x51c8f9(-0x2af,-0x2c2)][_0x51c8f9(-0x2c0,-0x2c7)][_0x51c8f9(-0x2c4,-0x2d0)];if(_0x52e397>=_0xa6c6a9){await _0x1330e6['reply']({'content':lang[_0x51c8f9(-0x2aa,-0x2bd)][_0x51c8f9(-0x2d6,-0x2da)],'ephemeral':!![]}),console[_0x51c8f9(-0x2ec,-0x307)]('Bot\x20doesn\x27t\x20have\x20high\x20enough\x20role\x20to\x20perform\x20the\x20action.\x20Bot\x20Highest\x20Role\x20Position:\x20'+_0xa6c6a9+_0x51c8f9(-0x2d6,-0x2ee)+_0x52e397);return;}if(_0x52e397>=_0x1e03ed){await _0x1330e6[_0x51c8f9(-0x311,-0x2ec)]({'content':lang['RoleAll']['RoleAllUserHighestRole'],'ephemeral':!![]}),console['error'](_0x51c8f9(-0x2ff,-0x2fa)+_0x1e03ed+_0x51c8f9(-0x307,-0x2ee)+_0x52e397);return;}const _0x18f4cb=new ActionRowBuilder()[_0x51c8f9(-0x2e7,-0x2ea)](new ButtonBuilder()[_0x51c8f9(-0x2e8,-0x2f1)]('confirm')[_0x51c8f9(-0x2f5,-0x2e8)](_0x51c8f9(-0x2ef,-0x2d1))[_0x51c8f9(-0x2d3,-0x2dc)](ButtonStyle['Success']),new ButtonBuilder()[_0x51c8f9(-0x2fa,-0x2f1)]('cancel')['setLabel']('Cancel')[_0x51c8f9(-0x2b7,-0x2dc)](ButtonStyle[_0x51c8f9(-0x2f8,-0x2e0)])),_0x4a9fca=_0x64a997===_0x51c8f9(-0x2af,-0x2cd)?lang[_0x51c8f9(-0x2e6,-0x2bd)][_0x51c8f9(-0x2e3,-0x2e6)][_0x51c8f9(-0x2dd,-0x2eb)](_0x51c8f9(-0x2e0,-0x2ff),_0x3d463a[_0x51c8f9(-0x303,-0x302)]()):lang[_0x51c8f9(-0x2d5,-0x2bd)]['RoleAllConfirmationRemove'][_0x51c8f9(-0x2c3,-0x2eb)](_0x51c8f9(-0x2f9,-0x2ff),_0x3d463a[_0x51c8f9(-0x2e5,-0x302)]());await _0x1330e6[_0x51c8f9(-0x309,-0x2ec)]({'content':_0x4a9fca,'components':[_0x18f4cb],'ephemeral':!![]});const _0x189719=_0x192aed=>_0x192aed[_0x51c8f9(-0x2d5,-0x2f8)]['id']===_0x1330e6['user']['id'],_0x4daef1=_0x1330e6['channel'][_0x51c8f9(-0x2b4,-0x2c3)]({'filter':_0x189719,'time':0x3a98});_0x4daef1['on']('collect',async _0x16c9bf=>{function _0x13b4d2(_0x231b05,_0x56ae93){return _0x51c8f9(_0x56ae93,_0x231b05-0x4f);}if(_0x16c9bf[_0x13b4d2(-0x28a,-0x2b1)]==='confirm')try{let _0x24404e=![];const _0x14b322=new ActionRowBuilder()[_0x13b4d2(-0x29b,-0x27c)](new ButtonBuilder()[_0x13b4d2(-0x2a2,-0x278)]('cancel')[_0x13b4d2(-0x299,-0x2a0)](_0x13b4d2(-0x2ac,-0x28e))[_0x13b4d2(-0x28d,-0x275)](ButtonStyle[_0x13b4d2(-0x291,-0x2b4)]));await _0x16c9bf[_0x13b4d2(-0x27c,-0x255)]({'content':lang['RoleAll'][_0x13b4d2(-0x2ae,-0x2c6)],'components':[_0x14b322],'ephemeral':!![]});const _0x22a68e=_0x4499a8=>_0x4499a8[_0x13b4d2(-0x28a,-0x289)]===_0x13b4d2(-0x2bc,-0x2ce)&&_0x4499a8[_0x13b4d2(-0x2a9,-0x285)]['id']===_0x1330e6[_0x13b4d2(-0x2a9,-0x2a3)]['id'],_0x41716f=_0x1330e6[_0x13b4d2(-0x279,-0x277)][_0x13b4d2(-0x274,-0x28c)]({'cancelFilter':_0x22a68e,'time':0x927c0});_0x41716f['on'](_0x13b4d2(-0x2ad,-0x2d1),async _0x20c0c6=>{_0x24404e=!![];function _0xd4f0e9(_0x153e90,_0x2c96ac){return _0x13b4d2(_0x153e90-0x308,_0x2c96ac);}await _0x20c0c6['update']({'content':lang[_0xd4f0e9(0x9a,0x88)][_0xd4f0e9(0x7a,0x99)],'components':[],'ephemeral':!![]}),_0x41716f['stop']();});const _0x5ec27a=await _0x1330e6['guild'][_0x13b4d2(-0x2ba,-0x2bf)]['fetch'](),_0x389326=_0x5ec27a[_0x13b4d2(-0x290,-0x274)];let _0x2c113e=0x0;const _0x1d2555=async()=>{function _0x3dff24(_0x4fc668,_0xbb2e0){return _0x13b4d2(_0x4fc668-0x435,_0xbb2e0);}for(const _0x4526e2 of _0x5ec27a['values']()){if(_0x24404e)break;if(_0x64a997===_0x3dff24(0x1b7,0x1c6)&&!_0x4526e2[_0x3dff24(0x1c2,0x1ce)]['cache'][_0x3dff24(0x181,0x19b)](_0x3d463a['id'])&&!_0x4526e2[_0x3dff24(0x18c,0x18c)][_0x3dff24(0x19f,0x1c5)])await _0x4526e2[_0x3dff24(0x1c2,0x1ac)][_0x3dff24(0x1b7,0x1aa)](_0x3d463a);else _0x64a997===_0x3dff24(0x1ac,0x1b4)&&_0x4526e2[_0x3dff24(0x1c2,0x1e9)][_0x3dff24(0x1a2,0x1b3)][_0x3dff24(0x181,0x17f)](_0x3d463a['id'])&&!_0x4526e2[_0x3dff24(0x18c,0x188)][_0x3dff24(0x19f,0x1a5)]&&await _0x4526e2[_0x3dff24(0x1c2,0x1d7)][_0x3dff24(0x1ac,0x1b7)](_0x3d463a);_0x2c113e++,_0x2c113e%0xf===0x0&&await _0x1330e6[_0x3dff24(0x192,0x185)]({'content':_0x3dff24(0x17c,0x199)+_0x2c113e+'/'+_0x389326+'\x20members\x20processed.','components':[_0x14b322],'ephemeral':!![]}),await new Promise(_0x865ff=>setTimeout(_0x865ff,0x32));}};await _0x1d2555();if(!_0x24404e){const _0x1dcc9e=_0x64a997===_0x13b4d2(-0x27e,-0x26b)?lang[_0x13b4d2(-0x26e,-0x28a)]['RoleAllSuccessAdd'][_0x13b4d2(-0x29c,-0x2a7)](_0x13b4d2(-0x2b0,-0x2da),_0x3d463a['toString']()):lang[_0x13b4d2(-0x26e,-0x278)][_0x13b4d2(-0x27a,-0x254)][_0x13b4d2(-0x29c,-0x278)](_0x13b4d2(-0x2b0,-0x2d2),_0x3d463a[_0x13b4d2(-0x2b3,-0x2d4)]());await _0x1330e6[_0x13b4d2(-0x2a3,-0x282)]({'content':_0x1dcc9e,'components':[],'ephemeral':!![]});}}catch(_0x52d1fa){console[_0x13b4d2(-0x2b8,-0x2cf)](_0x13b4d2(-0x2bf,-0x29e)+_0x52d1fa[_0x13b4d2(-0x298,-0x292)]),await _0x1330e6[_0x13b4d2(-0x2a3,-0x2b9)]({'content':lang['RoleAll'][_0x13b4d2(-0x28f,-0x2a2)],'components':[],'ephemeral':!![]});}else await _0x16c9bf[_0x13b4d2(-0x27c,-0x28e)]({'content':lang[_0x13b4d2(-0x26e,-0x28c)][_0x13b4d2(-0x28e,-0x293)],'components':[],'ephemeral':!![]});}),_0x4daef1['on'](_0x51c8f9(-0x2e4,-0x30c),async _0xf1c02a=>{function _0x149a66(_0x32914a,_0x2409bc){return _0x51c8f9(_0x2409bc,_0x32914a- -0x26);}!_0xf1c02a[_0x149a66(-0x305,-0x2f5)]&&await _0x1330e6[_0x149a66(-0x318,-0x2f1)]({'content':lang[_0x149a66(-0x2e3,-0x2fb)]['RoleAllTimeOut'],'components':[],'ephemeral':!![]});});}catch(_0xf4d7dc){console[_0x51c8f9(-0x312,-0x307)]('An\x20error\x20occurred\x20during\x20command\x20execution:\x20'+_0xf4d7dc[_0x51c8f9(-0x2d0,-0x2e7)]),_0xf4d7dc[_0x51c8f9(-0x311,-0x2f5)]===0x274e?await _0x1330e6[_0x51c8f9(-0x2d8,-0x2d3)]({'content':_0x51c8f9(-0x29f,-0x2c6),'ephemeral':!![]}):await _0x1330e6[_0x51c8f9(-0x2c5,-0x2d3)]({'content':'An\x20error\x20occurred\x20while\x20processing\x20your\x20request.','ephemeral':!![]});}}};