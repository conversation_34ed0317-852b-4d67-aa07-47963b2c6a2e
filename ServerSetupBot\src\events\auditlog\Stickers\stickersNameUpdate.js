const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildStickerUpdate,
    async execute(oldSticker, newSticker, client) {
        try {
            // Only handle name changes
            if (oldSticker.name === newSticker.name) return;
            
            console.log(`✏️ Sticker name updated: ${oldSticker.name} -> ${newSticker.name} in ${newSticker.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newSticker.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'STICKERS_NAME_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'STICKERS_NAME_UPDATE',
                user: 'System',
                userId: null,
                action: 'Tên sticker được cập nhật',
                details: `Tên sticker đã được thay đổi từ **${oldSticker.name}** thành **${newSticker.name}**`,
                target: newSticker.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the sticker name from audit logs
            try {
                const auditLogs = await newSticker.guild.fetchAuditLogs({
                    type: 91, // STICKER_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newSticker.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Tên sticker được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for sticker name update');
            }
            
            // Add to database
            await client.db.addAuditLog(newSticker.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Tên sticker được cập nhật',
                `Vừa có tên sticker được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> ID sticker',
                    value: `- ${newSticker.id}`,
                    inline: true
                },
                {
                    name: '> Tên cũ',
                    value: `- ${oldSticker.name}`,
                    inline: true
                },
                {
                    name: '> Tên mới',
                    value: `- ${newSticker.name}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add sticker info
            if (newSticker.description) {
                embed.addFields({
                    name: '> Mô tả',
                    value: `- ${newSticker.description}`,
                    inline: false
                });
            }
            
            // Add sticker format
            const formatTypes = {
                1: 'PNG',
                2: 'APNG',
                3: 'Lottie',
                4: 'GIF'
            };
            
            embed.addFields({
                name: '> Định dạng',
                value: `- ${formatTypes[newSticker.format] || 'Unknown'}`,
                inline: true
            });
            
            // Add sticker type
            const stickerTypes = {
                1: 'Standard (Discord)',
                2: 'Guild (Server custom)'
            };
            
            embed.addFields({
                name: '> Loại sticker',
                value: `- ${stickerTypes[newSticker.type] || 'Unknown'}`,
                inline: true
            });
            
            // Add sticker tags if available
            if (newSticker.tags) {
                embed.addFields({
                    name: '> Tags',
                    value: `- ${newSticker.tags}`,
                    inline: true
                });
            }
            
            // Add sticker availability
            embed.addFields({
                name: '> Có thể sử dụng',
                value: `- ${newSticker.available ? 'Có' : 'Không'}`,
                inline: true
            });
            
            // Add sticker URL
            if (newSticker.url) {
                embed.addFields({
                    name: '> URL sticker',
                    value: `- [Xem sticker](${newSticker.url})`,
                    inline: true
                });
            }
            
            // Add creation info if available
            if (newSticker.createdTimestamp) {
                embed.addFields({
                    name: '> Được tạo lúc',
                    value: `- <t:${Math.floor(newSticker.createdTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add usage impact
            embed.addFields({
                name: '> 📝 Tác động',
                value: [
                    '• Tên mới sẽ hiển thị trong sticker picker',
                    '• Autocomplete sẽ sử dụng tên mới',
                    '• Tin nhắn cũ vẫn hiển thị sticker bình thường',
                    '• Cần gõ tên mới để sử dụng sticker'
                ].join('\n'),
                inline: false
            });
            
            // Add usage instructions
            embed.addFields({
                name: '> 💡 Cách sử dụng với tên mới',
                value: [
                    `• Gõ \`:${newSticker.name}:\` trong tin nhắn`,
                    '• Sử dụng sticker picker',
                    '• Autocomplete khi gõ tên mới',
                    '• Tìm kiếm trong danh sách stickers'
                ].join('\n'),
                inline: false
            });
            
            // Add server sticker info
            const currentStickerCount = newSticker.guild.stickers.cache.size;
            const maxStickers = newSticker.guild.premiumTier >= 2 ? 60 : 0;
            
            embed.addFields([
                {
                    name: '> Số sticker trong server',
                    value: `- ${currentStickerCount}/${maxStickers} stickers`,
                    inline: true
                },
                {
                    name: '> Server Boost Level',
                    value: `- Level ${newSticker.guild.premiumTier}`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do thay đổi',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set sticker as thumbnail if possible
            if (newSticker.url) {
                embed.setThumbnail(newSticker.url);
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newSticker.guild.name} • Sticker Name Update`,
                iconURL: newSticker.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Sticker name update logged: ${oldSticker.name} -> ${newSticker.name}`);
            
        } catch (error) {
            console.error('Error in stickersNameUpdate audit log:', error);
        }
    }
};
