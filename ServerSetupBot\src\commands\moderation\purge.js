const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('purge')
        .setDescription('Xóa tin nhắn hàng loạt')
        .addIntegerOption(option =>
            option.setName('amount')
                .setDescription('Số tin nhắn cần xóa (1-100)')
                .setRequired(true)
                .setMinValue(1)
                .setMaxValue(100)
        )
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Chỉ xóa tin nhắn của user này')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('filter')
                .setDescription('<PERSON><PERSON><PERSON> tin nhắn theo loại')
                .setRequired(false)
                .addChoices(
                    { name: 'Tất cả', value: 'all' },
                    { name: 'Chỉ bot', value: 'bots' },
                    { name: 'Chỉ người dùng', value: 'humans' },
                    { name: 'Có file đính kèm', value: 'attachments' },
                    { name: 'Có embed', value: 'embeds' },
                    { name: 'Có link', value: 'links' }
                )
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Lý do xóa tin nhắn')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.ManageMessages)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                'Bạn cần quyền `Manage Messages` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageMessages)) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                'Bot cần quyền `Manage Messages` để thực hiện lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const amount = interaction.options.getInteger('amount');
        const targetUser = interaction.options.getUser('user');
        const filter = interaction.options.getString('filter') || 'all';
        const reason = interaction.options.getString('reason') || 'Không có lý do';
        const guildId = interaction.guild.id;
        
        try {
            // Fetch messages
            const messages = await interaction.channel.messages.fetch({ limit: amount });
            
            // Filter messages based on criteria
            let messagesToDelete = messages.filter(msg => {
                // Check if message is older than 14 days (Discord limitation)
                if (Date.now() - msg.createdTimestamp > 14 * 24 * 60 * 60 * 1000) {
                    return false;
                }
                
                // Filter by user
                if (targetUser && msg.author.id !== targetUser.id) {
                    return false;
                }
                
                // Filter by type
                switch (filter) {
                    case 'bots':
                        return msg.author.bot;
                    case 'humans':
                        return !msg.author.bot;
                    case 'attachments':
                        return msg.attachments.size > 0;
                    case 'embeds':
                        return msg.embeds.length > 0;
                    case 'links':
                        return /https?:\/\/[^\s]+/.test(msg.content);
                    case 'all':
                    default:
                        return true;
                }
            });
            
            if (messagesToDelete.size === 0) {
                const errorEmbed = createErrorEmbed(
                    'Không có tin nhắn nào để xóa!',
                    'Không tìm thấy tin nhắn nào phù hợp với tiêu chí đã chọn hoặc tất cả tin nhắn đều quá cũ (>14 ngày).'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Delete messages
            const deletedMessages = await interaction.channel.bulkDelete(messagesToDelete, true);
            
            // Log vào database
            await client.db.addModerationLog(guildId, {
                type: 'purge',
                userId: targetUser?.id || null,
                moderatorId: interaction.user.id,
                reason: reason,
                duration: deletedMessages.size // Store count in duration field
            });
            
            const successEmbed = createSuccessEmbed(
                '🗑️ Tin nhắn đã được xóa!',
                `**Số lượng:** ${deletedMessages.size} tin nhắn\n` +
                `**Kênh:** ${interaction.channel}\n` +
                `**Filter:** ${getFilterDisplayName(filter)}\n` +
                `${targetUser ? `**User:** ${targetUser.tag}\n` : ''}` +
                `**Lý do:** ${reason}\n` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            await interaction.reply({ embeds: [successEmbed], ephemeral: true });
            
            // Gửi log vào channel (nếu khác channel hiện tại)
            await sendModerationLog(client, guildId, {
                type: 'purge',
                user: targetUser || { tag: 'Tất cả', id: 'all' },
                moderator: interaction.user,
                reason: reason,
                channel: interaction.channel,
                count: deletedMessages.size,
                filter: getFilterDisplayName(filter)
            });
            
        } catch (error) {
            console.error('Lỗi khi purge messages:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi xóa tin nhắn!',
                'Đã xảy ra lỗi khi xóa tin nhắn. Có thể do tin nhắn quá cũ (>14 ngày) hoặc lỗi khác.'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};

function getFilterDisplayName(filter) {
    const filters = {
        'all': 'Tất cả',
        'bots': 'Chỉ bot',
        'humans': 'Chỉ người dùng',
        'attachments': 'Có file đính kèm',
        'embeds': 'Có embed',
        'links': 'Có link'
    };
    return filters[filter] || filter;
}
