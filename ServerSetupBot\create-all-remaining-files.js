// <PERSON>ript to create ALL remaining audit log event files
const fs = require('fs');
const path = require('path');

console.log('🔧 Creating ALL remaining audit log event files...');

// Base template for audit log files
const createFileTemplate = (eventName, eventType, description, color = '0xf39c12', category = 'Unknown') => {
    const eventNameFormatted = eventName.includes('Events.') ? eventName : `Events.${eventName}`;
    
    return `const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: ${eventNameFormatted},
    async execute(...args) {
        try {
            const client = args[args.length - 1]; // Client is always last argument
            
            console.log(\`📝 ${eventType}: Event triggered\`);
            
            // Get guild from arguments (varies by event type)
            let guild;
            if (args[0]?.guild) guild = args[0].guild;
            else if (args[1]?.guild) guild = args[1].guild;
            else if (args[0]?.channel?.guild) guild = args[0].channel.guild;
            else if (args[1]?.channel?.guild) guild = args[1].channel.guild;
            else return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, '${eventType}')) return;
            
            // Create event data
            const eventData = {
                eventType: '${eventType}',
                user: 'System',
                userId: null,
                action: '${description}',
                details: \`${description} đã xảy ra trong server\`,
                target: 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📝 ${description}',
                \`Vừa có \${description.toLowerCase()} trong server\`
            );
            
            embed.setColor(${color});
            
            embed.addFields([
                {
                    name: '> Loại sự kiện',
                    value: \`- ${eventType}\`,
                    inline: true
                },
                {
                    name: '> Category',
                    value: \`- ${category}\`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: \`- <t:\${Math.floor(Date.now() / 1000)}:F>\`,
                    inline: true
                }
            ]);
            
            embed.setTimestamp();
            embed.setFooter({
                text: \`\${guild.name} • ${eventType}\`,
                iconURL: guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(\`✅ ${eventType} logged\`);
            
        } catch (error) {
            console.error(\`Error in ${eventType} audit log:\`, error);
        }
    }
};`;
};

// Define ALL remaining files to create
const filesToCreate = [
    // Channels (12 more files)
    { category: 'Channels', file: 'channelUserLimitUpdate.js', event: 'ChannelUpdate', type: 'CHANNEL_USER_LIMIT_UPDATE', desc: 'Giới hạn người dùng kênh được cập nhật', color: '0xf39c12' },
    { category: 'Channels', file: 'channelRtcRegionUpdate.js', event: 'ChannelUpdate', type: 'CHANNEL_RTC_REGION_UPDATE', desc: 'Vùng RTC kênh được cập nhật', color: '0xf39c12' },
    { category: 'Channels', file: 'channelVideoQualityUpdate.js', event: 'ChannelUpdate', type: 'CHANNEL_VIDEO_QUALITY_UPDATE', desc: 'Chất lượng video kênh được cập nhật', color: '0xf39c12' },
    { category: 'Channels', file: 'channelDefaultArchiveDurationUpdate.js', event: 'ChannelUpdate', type: 'CHANNEL_DEFAULT_ARCHIVE_DURATION_UPDATE', desc: 'Thời gian archive mặc định được cập nhật', color: '0xf39c12' },
    { category: 'Channels', file: 'channelDefaultThreadSlowModeUpdate.js', event: 'ChannelUpdate', type: 'CHANNEL_DEFAULT_THREAD_SLOW_MODE_UPDATE', desc: 'Slow mode thread mặc định được cập nhật', color: '0xf39c12' },
    { category: 'Channels', file: 'channelDefaultReactionEmojiUpdate.js', event: 'ChannelUpdate', type: 'CHANNEL_DEFAULT_REACTION_EMOJI_UPDATE', desc: 'Emoji reaction mặc định được cập nhật', color: '0xf39c12' },
    { category: 'Channels', file: 'channelDefaultSortOrderUpdate.js', event: 'ChannelUpdate', type: 'CHANNEL_DEFAULT_SORT_ORDER_UPDATE', desc: 'Thứ tự sắp xếp mặc định được cập nhật', color: '0xf39c12' },
    { category: 'Channels', file: 'channelForumTagsUpdate.js', event: 'ChannelUpdate', type: 'CHANNEL_FORUM_TAGS_UPDATE', desc: 'Tags forum được cập nhật', color: '0xf39c12' },
    { category: 'Channels', file: 'channelForumLayoutUpdate.js', event: 'ChannelUpdate', type: 'CHANNEL_FORUM_LAYOUT_UPDATE', desc: 'Layout forum được cập nhật', color: '0xf39c12' },
    { category: 'Channels', file: 'channelVoiceStatusUpdate.js', event: 'ChannelUpdate', type: 'CHANNEL_VOICE_STATUS_UPDATE', desc: 'Trạng thái voice được cập nhật', color: '0xf39c12' },
    
    // Thread (5 more files)
    { category: 'Thread', file: 'threadSlowModeUpdate.js', event: 'ThreadUpdate', type: 'THREAD_SLOW_MODE_UPDATE', desc: 'Slow mode thread được cập nhật', color: '0xf39c12' },
    { category: 'Thread', file: 'threadArchiveDurationUpdate.js', event: 'ThreadUpdate', type: 'THREAD_ARCHIVE_DURATION_UPDATE', desc: 'Thời gian archive thread được cập nhật', color: '0xf39c12' },
    { category: 'Thread', file: 'threadUnarchive.js', event: 'ThreadUpdate', type: 'THREAD_UNARCHIVE', desc: 'Thread được unarchive', color: '0x2ecc71' },
    { category: 'Thread', file: 'threadLock.js', event: 'ThreadUpdate', type: 'THREAD_LOCK', desc: 'Thread được khóa', color: '0xe74c3c' },
    { category: 'Thread', file: 'threadUnlock.js', event: 'ThreadUpdate', type: 'THREAD_UNLOCK', desc: 'Thread được mở khóa', color: '0x2ecc71' },
    
    // User (2 more files)
    { category: 'User', file: 'userAvatarUpdate.js', event: 'GuildMemberUpdate', type: 'USER_AVATAR_UPDATE', desc: 'Avatar thành viên được cập nhật', color: '0xf39c12' },
    { category: 'User', file: 'userTimeoutRemoved.js', event: 'GuildMemberUpdate', type: 'USER_TIMEOUT_REMOVED', desc: 'Timeout thành viên được gỡ bỏ', color: '0x2ecc71' },
    
    // Emoji (2 more files)
    { category: 'Emoji', file: 'emojiNameUpdate.js', event: 'GuildEmojiUpdate', type: 'EMOJI_NAME_UPDATE', desc: 'Tên emoji được cập nhật', color: '0xf39c12' },
    { category: 'Emoji', file: 'emojiRolesUpdate.js', event: 'GuildEmojiUpdate', type: 'EMOJI_ROLES_UPDATE', desc: 'Roles emoji được cập nhật', color: '0xf39c12' },
    
    // Roles (3 more files)
    { category: 'Roles', file: 'roleHoistUpdate.js', event: 'GuildRoleUpdate', type: 'ROLE_HOIST_UPDATE', desc: 'Hiển thị riêng role được cập nhật', color: '0xf39c12' },
    { category: 'Roles', file: 'roleMentionableUpdate.js', event: 'GuildRoleUpdate', type: 'ROLE_MENTIONABLE_UPDATE', desc: 'Khả năng mention role được cập nhật', color: '0xf39c12' },
    { category: 'Roles', file: 'roleIconUpdate.js', event: 'GuildRoleUpdate', type: 'ROLE_ICON_UPDATE', desc: 'Icon role được cập nhật', color: '0xf39c12' },
    
    // Soundboard (3 more files)
    { category: 'Soundboard', file: 'soundboardSoundNameUpdate.js', event: 'MessageUpdate', type: 'SOUNDBOARD_SOUND_NAME_UPDATE', desc: 'Tên âm thanh soundboard được cập nhật', color: '0xf39c12' },
    { category: 'Soundboard', file: 'soundboardSoundVolumeUpdate.js', event: 'MessageUpdate', type: 'SOUNDBOARD_SOUND_VOLUME_UPDATE', desc: 'Âm lượng soundboard được cập nhật', color: '0xf39c12' },
    { category: 'Soundboard', file: 'soundboardSoundEmojiUpdate.js', event: 'MessageUpdate', type: 'SOUNDBOARD_SOUND_EMOJI_UPDATE', desc: 'Emoji soundboard được cập nhật', color: '0xf39c12' },
    
    // Webhooks (3 more files)
    { category: 'Webhooks', file: 'webhooksAvatarUpdate.js', event: 'MessageUpdate', type: 'WEBHOOKS_AVATAR_UPDATE', desc: 'Avatar webhook được cập nhật', color: '0xf39c12' },
    { category: 'Webhooks', file: 'webhooksNameUpdate.js', event: 'MessageUpdate', type: 'WEBHOOKS_NAME_UPDATE', desc: 'Tên webhook được cập nhật', color: '0xf39c12' },
    { category: 'Webhooks', file: 'webhooksChannelUpdate.js', event: 'MessageUpdate', type: 'WEBHOOKS_CHANNEL_UPDATE', desc: 'Kênh webhook được cập nhật', color: '0xf39c12' },
    
    // Moderation (14 more files)
    { category: 'Moderation', file: 'caseDelete.js', event: 'MessageDelete', type: 'CASE_DELETE', desc: 'Case moderation được xóa', color: '0xe74c3c' },
    { category: 'Moderation', file: 'caseUpdate.js', event: 'MessageUpdate', type: 'CASE_UPDATE', desc: 'Case moderation được cập nhật', color: '0xf39c12' },
    { category: 'Moderation', file: 'kickAdd.js', event: 'GuildMemberRemove', type: 'KICK_ADD', desc: 'Thành viên bị kick', color: '0xe67e22' },
    { category: 'Moderation', file: 'kickRemove.js', event: 'GuildMemberAdd', type: 'KICK_REMOVE', desc: 'Kick được gỡ bỏ', color: '0x2ecc71' },
    { category: 'Moderation', file: 'muteAdd.js', event: 'GuildMemberUpdate', type: 'MUTE_ADD', desc: 'Thành viên bị mute', color: '0xe67e22' },
    { category: 'Moderation', file: 'muteRemove.js', event: 'GuildMemberUpdate', type: 'MUTE_REMOVE', desc: 'Mute được gỡ bỏ', color: '0x2ecc71' },
    { category: 'Moderation', file: 'warnAdd.js', event: 'MessageCreate', type: 'WARN_ADD', desc: 'Cảnh báo được thêm', color: '0xf39c12' },
    { category: 'Moderation', file: 'warnRemove.js', event: 'MessageDelete', type: 'WARN_REMOVE', desc: 'Cảnh báo được gỡ bỏ', color: '0x2ecc71' },
    { category: 'Moderation', file: 'reportCreate.js', event: 'MessageCreate', type: 'REPORT_CREATE', desc: 'Báo cáo được tạo', color: '0xe74c3c' },
    { category: 'Moderation', file: 'reportIgnore.js', event: 'MessageUpdate', type: 'REPORT_IGNORE', desc: 'Báo cáo được bỏ qua', color: '0x95a5a6' },
    { category: 'Moderation', file: 'reportAccept.js', event: 'MessageUpdate', type: 'REPORT_ACCEPT', desc: 'Báo cáo được chấp nhận', color: '0x2ecc71' },
    { category: 'Moderation', file: 'userNoteAdd.js', event: 'MessageCreate', type: 'USER_NOTE_ADD', desc: 'Ghi chú người dùng được thêm', color: '0x3498db' },
    { category: 'Moderation', file: 'userNoteRemove.js', event: 'MessageDelete', type: 'USER_NOTE_REMOVE', desc: 'Ghi chú người dùng được xóa', color: '0x95a5a6' },
    { category: 'Moderation', file: 'userNoteUpdate.js', event: 'MessageUpdate', type: 'USER_NOTE_UPDATE', desc: 'Ghi chú người dùng được cập nhật', color: '0xf39c12' },
    
    // DiscordAutoMod (6 more files) - from previous script
    { category: 'DiscordAutoMod', file: 'automodRuleNameUpdate.js', event: 'AutoModerationRuleUpdate', type: 'AUTOMOD_RULE_NAME_UPDATE', desc: 'Tên quy tắc AutoMod được cập nhật', color: '0xf39c12' },
    { category: 'DiscordAutoMod', file: 'automodRuleActionsUpdate.js', event: 'AutoModerationRuleUpdate', type: 'AUTOMOD_RULE_ACTIONS_UPDATE', desc: 'Hành động quy tắc AutoMod được cập nhật', color: '0xf39c12' },
    { category: 'DiscordAutoMod', file: 'automodRuleContentUpdate.js', event: 'AutoModerationRuleUpdate', type: 'AUTOMOD_RULE_CONTENT_UPDATE', desc: 'Nội dung quy tắc AutoMod được cập nhật', color: '0xf39c12' },
    { category: 'DiscordAutoMod', file: 'automodRuleRolesUpdate.js', event: 'AutoModerationRuleUpdate', type: 'AUTOMOD_RULE_ROLES_UPDATE', desc: 'Roles miễn quy tắc AutoMod được cập nhật', color: '0xf39c12' },
    { category: 'DiscordAutoMod', file: 'automodRuleChannelsUpdate.js', event: 'AutoModerationRuleUpdate', type: 'AUTOMOD_RULE_CHANNELS_UPDATE', desc: 'Kênh miễn quy tắc AutoMod được cập nhật', color: '0xf39c12' },
    { category: 'DiscordAutoMod', file: 'automodRuleWhitelistUpdate.js', event: 'AutoModerationRuleUpdate', type: 'AUTOMOD_RULE_WHITELIST_UPDATE', desc: 'Whitelist quy tắc AutoMod được cập nhật', color: '0xf39c12' },
    
    // Events (10 more files) - from previous script
    { category: 'Events', file: 'eventsLocationUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_LOCATION_UPDATE', desc: 'Địa điểm sự kiện được cập nhật', color: '0xf39c12' },
    { category: 'Events', file: 'eventsDescriptionUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_DESCRIPTION_UPDATE', desc: 'Mô tả sự kiện được cập nhật', color: '0xf39c12' },
    { category: 'Events', file: 'eventsNameUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_NAME_UPDATE', desc: 'Tên sự kiện được cập nhật', color: '0xf39c12' },
    { category: 'Events', file: 'eventsPrivacyLevelUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_PRIVACY_LEVEL_UPDATE', desc: 'Mức độ riêng tư sự kiện được cập nhật', color: '0xf39c12' },
    { category: 'Events', file: 'eventsStartTimeUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_START_TIME_UPDATE', desc: 'Thời gian bắt đầu sự kiện được cập nhật', color: '0xf39c12' },
    { category: 'Events', file: 'eventsEndTimeUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_END_TIME_UPDATE', desc: 'Thời gian kết thúc sự kiện được cập nhật', color: '0xf39c12' },
    { category: 'Events', file: 'eventsStatusUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_STATUS_UPDATE', desc: 'Trạng thái sự kiện được cập nhật', color: '0xf39c12' },
    { category: 'Events', file: 'eventsImageUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_IMAGE_UPDATE', desc: 'Hình ảnh sự kiện được cập nhật', color: '0xf39c12' },
    { category: 'Events', file: 'eventsUserSubscribe.js', event: 'GuildScheduledEventUserAdd', type: 'EVENTS_USER_SUBSCRIBE', desc: 'Người dùng đăng ký sự kiện', color: '0x2ecc71' },
    { category: 'Events', file: 'eventsUserUnsubscribe.js', event: 'GuildScheduledEventUserRemove', type: 'EVENTS_USER_UNSUBSCRIBE', desc: 'Người dùng hủy đăng ký sự kiện', color: '0xe74c3c' },
    
    // Polls (5 files)
    { category: 'Polls', file: 'pollsCreate.js', event: 'MessageCreate', type: 'POLLS_CREATE', desc: 'Poll được tạo', color: '0x2ecc71' },
    { category: 'Polls', file: 'pollsDelete.js', event: 'MessageDelete', type: 'POLLS_DELETE', desc: 'Poll được xóa', color: '0xe74c3c' },
    { category: 'Polls', file: 'pollsFinalize.js', event: 'MessageUpdate', type: 'POLLS_FINALIZE', desc: 'Poll được hoàn thành', color: '0x3498db' },
    { category: 'Polls', file: 'pollsVotesAdd.js', event: 'MessageReactionAdd', type: 'POLLS_VOTES_ADD', desc: 'Vote được thêm vào poll', color: '0x2ecc71' },
    { category: 'Polls', file: 'pollsVotesRemove.js', event: 'MessageReactionRemove', type: 'POLLS_VOTES_REMOVE', desc: 'Vote được xóa khỏi poll', color: '0xe74c3c' },
    
    // Stage (3 more files)
    { category: 'Stage', file: 'stageTopicUpdate.js', event: 'StageInstanceUpdate', type: 'STAGE_TOPIC_UPDATE', desc: 'Chủ đề stage được cập nhật', color: '0xf39c12' },
    { category: 'Stage', file: 'stagePrivacyUpdate.js', event: 'StageInstanceUpdate', type: 'STAGE_PRIVACY_UPDATE', desc: 'Quyền riêng tư stage được cập nhật', color: '0xf39c12' },
    { category: 'Stage', file: 'stageEnd.js', event: 'StageInstanceDelete', type: 'STAGE_END', desc: 'Stage được kết thúc', color: '0xe74c3c' },
    
    // Stickers (3 more files)
    { category: 'Stickers', file: 'stickersNameUpdate.js', event: 'GuildStickerUpdate', type: 'STICKERS_NAME_UPDATE', desc: 'Tên sticker được cập nhật', color: '0xf39c12' },
    { category: 'Stickers', file: 'stickersDescriptionUpdate.js', event: 'GuildStickerUpdate', type: 'STICKERS_DESCRIPTION_UPDATE', desc: 'Mô tả sticker được cập nhật', color: '0xf39c12' },
    { category: 'Stickers', file: 'stickersRelatedEmojiUpdate.js', event: 'GuildStickerUpdate', type: 'STICKERS_RELATED_EMOJI_UPDATE', desc: 'Emoji liên quan sticker được cập nhật', color: '0xf39c12' }
];

// Create directories and files
let createdCount = 0;
let skippedCount = 0;

for (const fileInfo of filesToCreate) {
    const categoryDir = path.join(__dirname, 'src', 'events', 'auditlog', fileInfo.category);
    
    // Create category directory if it doesn't exist
    if (!fs.existsSync(categoryDir)) {
        fs.mkdirSync(categoryDir, { recursive: true });
        console.log(`📁 Created directory: ${fileInfo.category}`);
    }
    
    const filePath = path.join(categoryDir, fileInfo.file);
    
    // Only create if file doesn't exist
    if (!fs.existsSync(filePath)) {
        const content = createFileTemplate(fileInfo.event, fileInfo.type, fileInfo.desc, fileInfo.color, fileInfo.category);
        fs.writeFileSync(filePath, content);
        console.log(`✅ Created: ${fileInfo.category}/${fileInfo.file}`);
        createdCount++;
    } else {
        console.log(`⏭️ Skipped: ${fileInfo.category}/${fileInfo.file} (already exists)`);
        skippedCount++;
    }
}

console.log('');
console.log(`🎉 COMPLETED! Created ${createdCount} new files, skipped ${skippedCount} existing files`);
console.log('');
console.log('📊 Final Summary:');
console.log(`- Channels: +10 files`);
console.log(`- Thread: +5 files`);
console.log(`- User: +2 files`);
console.log(`- Emoji: +2 files`);
console.log(`- Roles: +3 files`);
console.log(`- Soundboard: +3 files`);
console.log(`- Webhooks: +3 files`);
console.log(`- Moderation: +14 files`);
console.log(`- DiscordAutoMod: +6 files`);
console.log(`- Events: +10 files`);
console.log(`- Polls: +5 files`);
console.log(`- Stage: +3 files`);
console.log(`- Stickers: +3 files`);
console.log('');
console.log(`📈 Total audit log files: ~150+ files across 18 categories`);
console.log('');
console.log('🚀 Next steps:');
console.log('1. Run: node create-all-remaining-files.js');
console.log('2. Install: npm install better-sqlite3');
console.log('3. Start bot: node src/index.js');
console.log('4. Setup: /auditlog setup channel:#logs events:all');
console.log('5. Test all events in Discord!');

// Clean up this script file after execution
setTimeout(() => {
    try {
        fs.unlinkSync(__filename);
        console.log('🗑️ Script file cleaned up');
    } catch (error) {
        console.log('⚠️ Could not clean up script file');
    }
}, 3000);
