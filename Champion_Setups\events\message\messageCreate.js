const Discord = require("discord.js");
const Event = require("../../structures/Events");

let cooldownList = [];

module.exports = class Message extends Event {
	constructor(...args) {
		super(...args);
	}

	async run(message) {
    if (message.channel.type == Discord.ChannelType.DM) return;
    let prefix = this.client.config.general.prefix;

    if (message.author.bot) return;

    // <== Commands ==> //
    const prefixMention = new RegExp(`^<@!?${this.client.user.id}> `);
    if (message.content.indexOf(prefix) != 0 && !message.content.match(prefixMention)) return;
  
    prefix = message.content.match(prefixMention) ? message.content.match(prefixMention)[0] : this.client.config.general.prefix;

    const args = message.content
      .slice(prefix.length)
      .trim()
      .split(/ +/g);
    const command = args.shift().toLowerCase();
    
    let cmd = this.client.commands.get(command);
    if (!cmd) cmd = this.client.commands.get(this.client.aliases.get(command));
    if(!cmd) return;

    if(cmd !== undefined) {
      let userPerms = [];
      cmd.permissions.forEach((perm) => {
        if(!message.channel.permissionsFor(message.member).has(perm)) {
          userPerms.push(perm);
        }
      });
      if(userPerms.length > 0) return message.channel.send({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.general.no_perm, this.client.embeds.error_color)]});
    }
    
    if (this.client.cmdConfig[cmd.name]) {
      let cmdConfig = this.client.cmdConfig[cmd.name];
      if(cmdConfig.enabled == false) return message.channel.send({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.general.cmd_disabled, this.client.embeds.error_color)] });
      if (cmdConfig && cmdConfig.roles.length > 0 && !this.client.utils.hasRole(this.client, message.guild, message.member, this.client.config.roles.bypass.permission)) {
        let cmdRoles = cmdConfig.roles.map((x) => this.client.utils.findRole(message.guild, x));
        if (!this.client.utils.hasRole(this.client, message.guild, message.member, cmdConfig.roles)) return message.channel.send({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.general.no_role.replace("<role>", cmdRoles.join(", ").trim()), this.client.embeds.error_color)] });
      }
      let findCooldown = cooldownList.find((c) => c.name == cmd.name && c.id == message.author.id);
      if (!this.client.utils.hasRole(this.client, message.guild, message.member, this.client.config.roles.bypass.cooldown)) {
        if (findCooldown) {
          let time = this.client.utils.formatTime(findCooldown.expiring - Date.now());
          return message.channel.send({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.general.cooldown.replace("<cooldown>", time), this.client.embeds.error_color)] });
        } else if (!findCooldown && this.client.cmdConfig[cmd.name].cooldown > 0) {
          let cooldown = {
            id: message.author.id,
            name: cmd.name,
            expiring: Date.now() + (this.client.cmdConfig[cmd.name].cooldown * 1000),
          };
    
          cooldownList.push(cooldown);
    
          setTimeout(() => {
            cooldownList.splice(cooldownList.indexOf(cooldown), 1);
          }, this.client.cmdConfig[cmd.name].cooldown * 1000);
        }
      }
    }
    
    if(cmd) cmd.run(message, args);
	}
};
