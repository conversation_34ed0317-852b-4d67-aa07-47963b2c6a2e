const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildMemberUpdate,
    async execute(oldMember, newMember, client) {
        try {
            // Check if member was muted (timeout added)
            if (oldMember.communicationDisabledUntil === newMember.communicationDisabledUntil) return;
            if (!newMember.communicationDisabledUntil) return; // Not a mute
            
            console.log(`🔇 User muted: ${newMember.user.tag} in ${newMember.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newMember.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'MUTE_ADD')) return;
            
            // Create event data
            const eventData = {
                eventType: 'MUTE_ADD',
                user: newMember.user.tag,
                userId: newMember.user.id,
                action: 'Thành viên bị mute',
                details: `**${newMember.user.tag}** đã bị mute`,
                target: newMember.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who muted the user from audit logs
            try {
                const auditLogs = await newMember.guild.fetchAuditLogs({
                    type: 24, // MEMBER_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newMember.user.id) {
                    eventData.moderator = auditEntry.executor.tag;
                    eventData.moderatorId = auditEntry.executor.id;
                    eventData.action = `Thành viên bị mute bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for mute add');
            }
            
            // Add to database
            await client.db.addAuditLog(newMember.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔇 Thành viên bị mute',
                `Vừa có thành viên bị mute trong server`
            );
            
            embed.setColor(0x95a5a6); // Gray for mute
            
            // Calculate mute duration
            const muteUntil = newMember.communicationDisabledUntil;
            const muteDuration = muteUntil.getTime() - Date.now();
            const muteDays = Math.floor(muteDuration / (1000 * 60 * 60 * 24));
            const muteHours = Math.floor((muteDuration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const muteMinutes = Math.floor((muteDuration % (1000 * 60 * 60)) / (1000 * 60));
            
            let durationText = '';
            if (muteDays > 0) durationText += `${muteDays} ngày `;
            if (muteHours > 0) durationText += `${muteHours} giờ `;
            if (muteMinutes > 0) durationText += `${muteMinutes} phút`;
            
            embed.addFields([
                {
                    name: '> Thành viên bị mute',
                    value: `- ${newMember.user.tag} (${newMember.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newMember.user.id}`,
                    inline: true
                },
                {
                    name: '> Thời gian mute',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Thời lượng mute',
                    value: `- ${durationText.trim()}`,
                    inline: true
                },
                {
                    name: '> Mute đến',
                    value: `- <t:${Math.floor(muteUntil.getTime() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add moderator info
            if (eventData.moderator) {
                embed.addFields({
                    name: '> Moderator',
                    value: `- ${eventData.moderator}`,
                    inline: true
                });
            }
            
            // Add mute reason if available
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do mute',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Add member info
            const accountAge = Date.now() - newMember.user.createdTimestamp;
            const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
            const memberAge = Date.now() - newMember.joinedTimestamp;
            const memberAgeDays = Math.floor(memberAge / (1000 * 60 * 60 * 24));
            
            embed.addFields([
                {
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                },
                {
                    name: '> Thời gian trong server',
                    value: `- ${memberAgeDays} ngày`,
                    inline: true
                }
            ]);
            
            // Add mute impact
            embed.addFields({
                name: '> 📝 Tác động của mute',
                value: [
                    '• Không thể gửi tin nhắn trong server',
                    '• Không thể tham gia voice channels',
                    '• Không thể tạo threads hoặc reactions',
                    '• Vẫn có thể đọc tin nhắn và xem server'
                ].join('\n'),
                inline: false
            });
            
            // Add duration-based advice
            if (muteDuration > 24 * 60 * 60 * 1000) { // More than 1 day
                embed.addFields({
                    name: '> ⚠️ Mute dài hạn',
                    value: [
                        '• Mute trên 1 ngày cần cân nhắc kỹ',
                        '• Theo dõi hành vi sau khi unmute',
                        '• Có thể cần review trước khi hết hạn',
                        '• Đảm bảo lý do rõ ràng và công bằng'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Set user avatar as thumbnail
            if (newMember.user.displayAvatarURL()) {
                embed.setThumbnail(newMember.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newMember.guild.name} • Member Mute Add`,
                iconURL: newMember.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Mute add logged: ${newMember.user.tag}`);
            
        } catch (error) {
            console.error('Error in muteAdd audit log:', error);
        }
    }
};
