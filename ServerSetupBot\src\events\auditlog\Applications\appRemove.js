const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ApplicationCommandPermissionsUpdate,
    async execute(data, client) {
        try {
            console.log(`📱 Application removed in ${data.guild?.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(data.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'APP_REMOVE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'APP_REMOVE',
                user: 'System',
                userId: null,
                action: 'Ứng dụng được xóa',
                details: `Ứng dụng **${data.applicationId}** đã được xóa khỏi server`,
                target: data.applicationId,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(data.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Ứng dụng được xóa',
                `Vừa có một ứng dụng được xóa khỏi server`
            );
            
            embed.setColor(0xe74c3c); // Red for remove
            
            embed.addFields([
                {
                    name: '> ID ứng dụng',
                    value: `- ${data.applicationId}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${data.guild.name} • Application Remove`,
                iconURL: data.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Application remove logged: ${data.applicationId}`);
            
        } catch (error) {
            console.error('Error in appRemove audit log:', error);
        }
    }
};
