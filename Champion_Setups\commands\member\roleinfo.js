const Command = require("../../structures/Command");
const Discord = require("discord.js");

module.exports = class RoleInfo extends Command {
  constructor(client) {
    super(client, {
      name: "roleinfo",
      description: client.cmdConfig.roleinfo.description,
      usage: client.cmdConfig.roleinfo.usage,
      permissions: client.cmdConfig.roleinfo.permissions,
      aliases: client.cmdConfig.roleinfo.aliases,
      category: "member",
      listed: client.cmdConfig.roleinfo.enabled,
      slash: true,
      options: [{
        name: "role",
        type: Discord.ApplicationCommandOptionType.Role,
        description: "Role which Info to View",
        required: true
      }]
    });
  }

  async run(message, args) {
    let role = message.mentions.roles.first();
    if(!role) return message.channel.send({ embeds: [this.client.utils.usage(this.client, this.client.cmdConfig.roleinfo.usage)], ephemeral: this.client.cmdConfig.roleinfo.ephemeral });
    let name = role.name, 
      position = role.position, color = role.hexColor, hoisted = role.hosited, 
      creation = `<t:${Math.round(role.createdTimestamp / 1000)}:F>`, mentionable = role.mentionable, id = role.id;

    let embed = new Discord.EmbedBuilder()
      .setColor(this.client.embeds.roleInfo.color);

    if (this.client.embeds.roleInfo.title) embed.setTitle(this.client.embeds.roleInfo.title);

    if (this.client.embeds.roleInfo.description) embed.setDescription(this.client.embeds.roleInfo.description.replace("<name>", name)
      .replace("<color>", color)
      .replace("<id>", id)
      .replace("<mentionable>", mentionable)
      .replace("<position>", position)
      .replace("<hoisted>", hoisted)
      .replace("<creation>", creation));

    let field = this.client.embeds.roleInfo.fields;
    for (let i = 0; i < this.client.embeds.roleInfo.fields.length; i++) {
      embed.addFields([{ name: field[i].title, value: field[i].description.replace("<name>", name)
        .replace("<color>", color)
        .replace("<id>", id)
        .replace("<mentionable>", mentionable)
        .replace("<position>", position)
        .replace("<hoisted>", hoisted)
        .replace("<creation>", creation) }]);
    }

    if (this.client.embeds.roleInfo.footer == true) embed.setFooter({ text: message.author.username, iconURL: message.author.displayAvatarURL() }).setTimestamp();
    if (this.client.embeds.roleInfo.thumbnail == true) embed.setThumbnail(message.guild.iconURL());
    
    message.channel.send({ embeds: [embed] });
  }
  async slashRun(interaction, args) {
    let role = interaction.options.getRole("role");
    let name = role.name, 
      position = role.position, color = role.hexColor, hoisted = role.hosited, 
      creation = `<t:${Math.round(role.createdTimestamp / 1000)}:F>`, mentionable = role.mentionable, id = role.id;

    let embed = new Discord.EmbedBuilder()
      .setColor(this.client.embeds.roleInfo.color);

    if (this.client.embeds.roleInfo.title) embed.setTitle(this.client.embeds.roleInfo.title);

    if (this.client.embeds.roleInfo.description) embed.setDescription(this.client.embeds.roleInfo.description.replace("<name>", name)
      .replace("<color>", color)
      .replace("<id>", id)
      .replace("<mentionable>", mentionable)
      .replace("<position>", position)
      .replace("<hoisted>", hoisted)
      .replace("<creation>", creation));

    let field = this.client.embeds.roleInfo.fields;
    for (let i = 0; i < this.client.embeds.roleInfo.fields.length; i++) {
      embed.addFields([{ name: field[i].title, value: field[i].description.replace("<name>", name)
        .replace("<color>", color)
        .replace("<id>", id)
        .replace("<mentionable>", mentionable)
        .replace("<position>", position)
        .replace("<hoisted>", hoisted)
        .replace("<creation>", creation) }]);
    }

    if (this.client.embeds.roleInfo.footer == true) embed.setFooter({ text: interaction.user.username, iconURL: interaction.user.displayAvatarURL() }).setTimestamp();
    if (this.client.embeds.roleInfo.thumbnail == true) embed.setThumbnail(message.guild.iconURL());
    
    interaction.reply({ embeds: [embed], ephemeral: this.client.cmdConfig.roleinfo.ephemeral });
  }
};
