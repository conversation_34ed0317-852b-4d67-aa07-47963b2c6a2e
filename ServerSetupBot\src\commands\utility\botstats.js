const {
  <PERSON>lash<PERSON>ommandBuilder,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  StringSelectMenuBuilder,
} = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
} = require("../../utils/embedBuilder.js");
const os = require("os");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("botstats")
    .setDescription("Xem thống kê chi tiết về bot")
    .addSubcommand((subcommand) =>
      subcommand.setName("overview").setDescription("Tổng quan thống kê bot")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("performance")
        .setDescription("Hiệu suất và tài nguyên hệ thống")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("usage")
        .setDescription("Thống kê sử dụng lệnh")
        .addStringOption((option) =>
          option
            .setName("period")
            .setDescription("Khoảng thời gian")
            .setRequired(false)
            .addChoices(
              { name: "Hôm nay", value: "today" },
              { name: "7 ngày qua", value: "week" },
              { name: "30 ngày qua", value: "month" },
              { name: "Tất cả", value: "all" }
            )
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("guilds")
        .setDescription("Thống kê server")
        .addStringOption((option) =>
          option
            .setName("sort")
            .setDescription("Sắp xếp theo")
            .setRequired(false)
            .addChoices(
              { name: "Thành viên nhiều nhất", value: "members" },
              { name: "Hoạt động nhiều nhất", value: "activity" },
              { name: "Mới tham gia", value: "newest" },
              { name: "Tên A-Z", value: "name" }
            )
        )
    )
    .addSubcommand((subcommand) =>
      subcommand.setName("database").setDescription("Thống kê database")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("errors")
        .setDescription("Thống kê lỗi và sự cố")
        .addStringOption((option) =>
          option
            .setName("severity")
            .setDescription("Mức độ nghiêm trọng")
            .setRequired(false)
            .addChoices(
              { name: "Tất cả", value: "all" },
              { name: "Critical", value: "critical" },
              { name: "Error", value: "error" },
              { name: "Warning", value: "warning" }
            )
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("realtime")
        .setDescription("Theo dõi thống kê thời gian thực")
        .addIntegerOption((option) =>
          option
            .setName("duration")
            .setDescription("Thời gian theo dõi (phút)")
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(60)
        )
    ),
  category: "utility",
  adminOnly: false,
  manageServer: false,

  async execute(interaction, client) {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case "overview":
        await handleOverview(interaction, client);
        break;
      case "performance":
        await handlePerformance(interaction, client);
        break;
      case "usage":
        await handleUsage(interaction, client);
        break;
      case "guilds":
        await handleGuilds(interaction, client);
        break;
      case "database":
        await handleDatabase(interaction, client);
        break;
      case "errors":
        await handleErrors(interaction, client);
        break;
      case "realtime":
        await handleRealtime(interaction, client);
        break;
    }
  },
};

async function handleOverview(interaction, client) {
  await interaction.deferReply();

  try {
    const stats = await getOverviewStats(client);

    const embed = createInfoEmbed(
      `📊 Tổng quan Bot - ${client.user.username}`,
      `Thống kê tổng quan về hoạt động và hiệu suất bot`
    );

    embed.setThumbnail(client.user.displayAvatarURL({ dynamic: true }));

    embed.addFields({
      name: "🌐 Phạm vi hoạt động",
      value:
        `**Servers:** ${stats.guilds.toLocaleString()}\n` +
        `**Users:** ${stats.users.toLocaleString()}\n` +
        `**Channels:** ${stats.channels.toLocaleString()}\n` +
        `**Roles:** ${stats.roles.toLocaleString()}`,
      inline: true,
    });

    embed.addFields({
      name: "⚡ Hiệu suất",
      value:
        `**Uptime:** ${stats.uptime}\n` +
        `**Ping:** ${stats.ping}ms\n` +
        `**Memory:** ${stats.memory}\n` +
        `**CPU:** ${stats.cpu}%`,
      inline: true,
    });

    embed.addFields({
      name: "📈 Hoạt động",
      value:
        `**Commands/24h:** ${stats.commandsToday.toLocaleString()}\n` +
        `**Messages/24h:** ${stats.messagesToday.toLocaleString()}\n` +
        `**Errors/24h:** ${stats.errorsToday}\n` +
        `**Success Rate:** ${stats.successRate}%`,
      inline: true,
    });

    embed.addFields({
      name: "🔧 Tính năng",
      value:
        `**Commands:** ${stats.totalCommands}\n` +
        `**Events:** ${stats.totalEvents}\n` +
        `**Database:** ${stats.databaseStatus}\n` +
        `**Version:** ${stats.version}`,
      inline: true,
    });

    embed.addFields({
      name: "📊 Top Servers",
      value: stats.topGuilds
        .map(
          (guild, index) =>
            `**${index + 1}.** ${
              guild.name
            } (${guild.memberCount.toLocaleString()} members)`
        )
        .join("\n"),
      inline: true,
    });

    embed.addFields({
      name: "🏆 Top Commands",
      value: stats.topCommands
        .map(
          (cmd, index) =>
            `**${index + 1}.** /${cmd.name} (${cmd.uses.toLocaleString()} uses)`
        )
        .join("\n"),
      inline: true,
    });

    const components = createOverviewComponents();

    await interaction.editReply({ embeds: [embed], components });
  } catch (error) {
    console.error("Lỗi khi lấy thống kê tổng quan:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống",
      "Không thể lấy thống kê tổng quan."
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handlePerformance(interaction, client) {
  await interaction.deferReply();

  try {
    const perfStats = await getPerformanceStats(client);

    const embed = createInfoEmbed(
      "⚡ Hiệu suất Hệ thống",
      "Thông tin chi tiết về hiệu suất và tài nguyên"
    );

    embed.addFields({
      name: "💾 Memory Usage",
      value:
        `**Used:** ${perfStats.memory.used}\n` +
        `**Total:** ${perfStats.memory.total}\n` +
        `**Heap:** ${perfStats.memory.heap}\n` +
        `**External:** ${perfStats.memory.external}`,
      inline: true,
    });

    embed.addFields({
      name: "🖥️ CPU & System",
      value:
        `**CPU Usage:** ${perfStats.cpu.usage}%\n` +
        `**Load Average:** ${perfStats.cpu.loadAvg}\n` +
        `**Cores:** ${perfStats.cpu.cores}\n` +
        `**Architecture:** ${perfStats.system.arch}`,
      inline: true,
    });

    embed.addFields({
      name: "🌐 Network",
      value:
        `**Ping:** ${perfStats.network.ping}ms\n` +
        `**Requests/min:** ${perfStats.network.requestsPerMin}\n` +
        `**Data In:** ${perfStats.network.dataIn}\n` +
        `**Data Out:** ${perfStats.network.dataOut}`,
      inline: true,
    });

    embed.addFields({
      name: "⏱️ Response Times",
      value:
        `**Command Avg:** ${perfStats.responseTimes.command}ms\n` +
        `**Database Avg:** ${perfStats.responseTimes.database}ms\n` +
        `**API Avg:** ${perfStats.responseTimes.api}ms\n` +
        `**Cache Hit Rate:** ${perfStats.responseTimes.cacheHitRate}%`,
      inline: true,
    });

    embed.addFields({
      name: "🔄 Event Loop",
      value:
        `**Lag:** ${perfStats.eventLoop.lag}ms\n` +
        `**Utilization:** ${perfStats.eventLoop.utilization}%\n` +
        `**Active Handles:** ${perfStats.eventLoop.activeHandles}\n` +
        `**Active Requests:** ${perfStats.eventLoop.activeRequests}`,
      inline: true,
    });

    embed.addFields({
      name: "📈 Performance Score",
      value:
        `**Overall:** ${perfStats.score.overall}/100\n` +
        `**Memory:** ${perfStats.score.memory}/100\n` +
        `**CPU:** ${perfStats.score.cpu}/100\n` +
        `**Network:** ${perfStats.score.network}/100`,
      inline: true,
    });

    // Performance recommendations
    if (perfStats.recommendations.length > 0) {
      embed.addFields({
        name: "💡 Khuyến nghị",
        value: perfStats.recommendations
          .slice(0, 5)
          .map((rec) => `• ${rec}`)
          .join("\n"),
        inline: false,
      });
    }

    const components = createPerformanceComponents();

    await interaction.editReply({ embeds: [embed], components });
  } catch (error) {
    console.error("Lỗi khi lấy thống kê hiệu suất:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống",
      "Không thể lấy thống kê hiệu suất."
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleUsage(interaction, client) {
  const period = interaction.options.getString("period") || "week";

  await interaction.deferReply();

  try {
    const usageStats = await getUsageStats(client, period);

    const embed = createInfoEmbed(
      `📊 Thống kê Sử dụng - ${getPeriodDisplayName(period)}`,
      `Thống kê sử dụng lệnh trong khoảng thời gian **${getPeriodDisplayName(
        period
      )}**`
    );

    embed.addFields({
      name: "🏆 Top Commands",
      value: usageStats.topCommands
        .map(
          (cmd, index) =>
            `**${index + 1}.** /${cmd.name}\n` +
            `└ ${cmd.uses.toLocaleString()} lần • ${cmd.percentage}%`
        )
        .join("\n"),
      inline: true,
    });

    embed.addFields({
      name: "📈 Xu hướng",
      value:
        `**Tổng lệnh:** ${usageStats.totalCommands.toLocaleString()}\n` +
        `**Trung bình/ngày:** ${usageStats.avgPerDay.toLocaleString()}\n` +
        `**Tăng trưởng:** ${usageStats.growth}\n` +
        `**Peak hour:** ${usageStats.peakHour}:00`,
      inline: true,
    });

    embed.addFields({
      name: "📊 Phân loại",
      value: usageStats.categories
        .map(
          (cat) =>
            `**${cat.name}:** ${cat.uses.toLocaleString()} (${cat.percentage}%)`
        )
        .join("\n"),
      inline: true,
    });

    embed.addFields({
      name: "👥 User Activity",
      value:
        `**Active Users:** ${usageStats.activeUsers.toLocaleString()}\n` +
        `**New Users:** ${usageStats.newUsers.toLocaleString()}\n` +
        `**Returning Users:** ${usageStats.returningUsers.toLocaleString()}\n` +
        `**Power Users:** ${usageStats.powerUsers.toLocaleString()}`,
      inline: true,
    });

    embed.addFields({
      name: "🌍 Server Activity",
      value:
        `**Active Servers:** ${usageStats.activeGuilds.toLocaleString()}\n` +
        `**New Servers:** ${usageStats.newGuilds.toLocaleString()}\n` +
        `**Commands/Server:** ${usageStats.avgCommandsPerGuild}\n` +
        `**Top Server:** ${usageStats.topGuild.name}`,
      inline: true,
    });

    embed.addFields({
      name: "⏰ Hourly Distribution",
      value: createHourlyChart(usageStats.hourlyData),
      inline: false,
    });

    const components = createUsageComponents(period);

    await interaction.editReply({ embeds: [embed], components });
  } catch (error) {
    console.error("Lỗi khi lấy thống kê sử dụng:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống",
      "Không thể lấy thống kê sử dụng."
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleGuilds(interaction, client) {
  const sort = interaction.options.getString("sort") || "members";

  await interaction.deferReply();

  try {
    const guildStats = await getGuildStats(client, sort);

    const embed = createInfoEmbed(
      `🏰 Thống kê Server - Sắp xếp theo ${getSortDisplayName(sort)}`,
      `Thông tin về **${guildStats.total.toLocaleString()}** server bot đang hoạt động`
    );

    embed.addFields({
      name: "📊 Tổng quan",
      value:
        `**Total Servers:** ${guildStats.total.toLocaleString()}\n` +
        `**Total Members:** ${guildStats.totalMembers.toLocaleString()}\n` +
        `**Average Members:** ${guildStats.avgMembers.toLocaleString()}\n` +
        `**Largest Server:** ${guildStats.largest.memberCount.toLocaleString()} members`,
      inline: true,
    });

    embed.addFields({
      name: "📈 Phân loại theo size",
      value:
        `**Small (< 100):** ${guildStats.sizeDistribution.small}\n` +
        `**Medium (100-1K):** ${guildStats.sizeDistribution.medium}\n` +
        `**Large (1K-10K):** ${guildStats.sizeDistribution.large}\n` +
        `**Huge (> 10K):** ${guildStats.sizeDistribution.huge}`,
      inline: true,
    });

    embed.addFields({
      name: "🌍 Regions",
      value: guildStats.regions
        .map((region) => `**${region.name}:** ${region.count} servers`)
        .join("\n"),
      inline: true,
    });

    embed.addFields({
      name: "🏆 Top 10 Servers",
      value: guildStats.topGuilds
        .map(
          (guild, index) =>
            `**${index + 1}.** ${guild.name}\n` +
            `└ ${guild.memberCount.toLocaleString()} members • ${
              guild.activity
            } commands/day`
        )
        .join("\n"),
      inline: false,
    });

    embed.addFields({
      name: "📅 Join Timeline",
      value:
        `**Today:** ${guildStats.joinTimeline.today}\n` +
        `**This Week:** ${guildStats.joinTimeline.week}\n` +
        `**This Month:** ${guildStats.joinTimeline.month}\n` +
        `**Left This Week:** ${guildStats.joinTimeline.left}`,
      inline: true,
    });

    embed.addFields({
      name: "⚡ Activity Levels",
      value:
        `**Very Active:** ${guildStats.activityLevels.veryActive}\n` +
        `**Active:** ${guildStats.activityLevels.active}\n` +
        `**Moderate:** ${guildStats.activityLevels.moderate}\n` +
        `**Inactive:** ${guildStats.activityLevels.inactive}`,
      inline: true,
    });

    const components = createGuildComponents(sort);

    await interaction.editReply({ embeds: [embed], components });
  } catch (error) {
    console.error("Lỗi khi lấy thống kê guild:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống",
      "Không thể lấy thống kê server."
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

// Helper functions
async function getOverviewStats(client) {
  // Mock implementation - would fetch real data
  return {
    guilds: client.guilds.cache.size,
    users: client.users.cache.size,
    channels: client.channels.cache.size,
    roles: client.guilds.cache.reduce(
      (acc, guild) => acc + guild.roles.cache.size,
      0
    ),
    uptime: formatUptime(process.uptime()),
    ping: client.ws.ping,
    memory: formatBytes(process.memoryUsage().used),
    cpu: Math.floor(Math.random() * 30) + 10,
    commandsToday: Math.floor(Math.random() * 10000) + 5000,
    messagesToday: Math.floor(Math.random() * 50000) + 25000,
    errorsToday: Math.floor(Math.random() * 50) + 10,
    successRate: 98.5,
    totalCommands: 150,
    totalEvents: 25,
    databaseStatus: "🟢 Online",
    version: "v2.1.0",
    topGuilds: [
      { name: "Server A", memberCount: 15000 },
      { name: "Server B", memberCount: 12000 },
      { name: "Server C", memberCount: 8500 },
    ],
    topCommands: [
      { name: "help", uses: 2500 },
      { name: "ping", uses: 1800 },
      { name: "stats", uses: 1200 },
    ],
  };
}

async function getPerformanceStats(client) {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();

  return {
    memory: {
      used: formatBytes(memUsage.used),
      total: formatBytes(memUsage.rss),
      heap: formatBytes(memUsage.heapUsed),
      external: formatBytes(memUsage.external),
    },
    cpu: {
      usage: Math.floor(Math.random() * 30) + 10,
      loadAvg: os.loadavg()[0].toFixed(2),
      cores: os.cpus().length,
    },
    system: {
      arch: os.arch(),
      platform: os.platform(),
      uptime: formatUptime(os.uptime()),
    },
    network: {
      ping: client.ws.ping,
      requestsPerMin: Math.floor(Math.random() * 1000) + 500,
      dataIn: formatBytes(Math.random() * 1000000),
      dataOut: formatBytes(Math.random() * 1000000),
    },
    responseTimes: {
      command: Math.floor(Math.random() * 100) + 50,
      database: Math.floor(Math.random() * 50) + 25,
      api: Math.floor(Math.random() * 200) + 100,
      cacheHitRate: Math.floor(Math.random() * 20) + 80,
    },
    eventLoop: {
      lag: Math.floor(Math.random() * 10) + 1,
      utilization: Math.floor(Math.random() * 30) + 20,
      activeHandles: Math.floor(Math.random() * 100) + 50,
      activeRequests: Math.floor(Math.random() * 50) + 10,
    },
    score: {
      overall: 85,
      memory: 90,
      cpu: 85,
      network: 80,
    },
    recommendations: [
      "Tối ưu hóa cache để giảm database queries",
      "Cân nhắc tăng memory allocation",
      "Monitor event loop lag",
    ],
  };
}

async function getUsageStats(client, period) {
  // Mock implementation
  return {
    totalCommands: 15000,
    avgPerDay: 2500,
    growth: "+15%",
    peakHour: 20,
    topCommands: [
      { name: "help", uses: 3000, percentage: 20 },
      { name: "ping", uses: 2500, percentage: 16.7 },
      { name: "stats", uses: 2000, percentage: 13.3 },
    ],
    categories: [
      { name: "Utility", uses: 8000, percentage: 53.3 },
      { name: "Moderation", uses: 4000, percentage: 26.7 },
      { name: "Fun", uses: 3000, percentage: 20 },
    ],
    activeUsers: 5000,
    newUsers: 500,
    returningUsers: 4500,
    powerUsers: 100,
    activeGuilds: 150,
    newGuilds: 15,
    avgCommandsPerGuild: 100,
    topGuild: { name: "Best Server" },
    hourlyData: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      commands: Math.floor(Math.random() * 1000) + 100,
    })),
  };
}

async function getGuildStats(client, sort) {
  // Mock implementation
  return {
    total: client.guilds.cache.size,
    totalMembers: 500000,
    avgMembers: 3333,
    largest: { memberCount: 50000 },
    sizeDistribution: {
      small: 80,
      medium: 60,
      large: 8,
      huge: 2,
    },
    regions: [
      { name: "US East", count: 60 },
      { name: "Europe", count: 45 },
      { name: "Asia", count: 30 },
    ],
    topGuilds: [
      { name: "Guild 1", memberCount: 50000, activity: 500 },
      { name: "Guild 2", memberCount: 35000, activity: 350 },
      { name: "Guild 3", memberCount: 25000, activity: 250 },
    ],
    joinTimeline: {
      today: 5,
      week: 25,
      month: 100,
      left: 8,
    },
    activityLevels: {
      veryActive: 20,
      active: 50,
      moderate: 60,
      inactive: 20,
    },
  };
}

function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) return `${days}d ${hours}h ${minutes}m`;
  if (hours > 0) return `${hours}h ${minutes}m`;
  return `${minutes}m`;
}

function formatBytes(bytes) {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

function getPeriodDisplayName(period) {
  const periods = {
    today: "Hôm nay",
    week: "7 ngày qua",
    month: "30 ngày qua",
    all: "Tất cả",
  };
  return periods[period] || period;
}

function getSortDisplayName(sort) {
  const sorts = {
    members: "số thành viên",
    activity: "hoạt động",
    newest: "mới tham gia",
    name: "tên",
  };
  return sorts[sort] || sort;
}

function createHourlyChart(hourlyData) {
  // Simple ASCII chart
  const maxCommands = Math.max(...hourlyData.map((h) => h.commands));
  return hourlyData
    .slice(0, 12)
    .map((h) => {
      const bars = Math.floor((h.commands / maxCommands) * 10);
      return `${h.hour.toString().padStart(2, "0")}:00 ${"█".repeat(
        bars
      )}${"░".repeat(10 - bars)} ${h.commands}`;
    })
    .join("\n");
}

function createOverviewComponents() {
  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("stats_refresh")
      .setLabel("🔄 Làm mới")
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId("stats_performance")
      .setLabel("⚡ Hiệu suất")
      .setStyle(ButtonStyle.Primary),
    new ButtonBuilder()
      .setCustomId("stats_export")
      .setLabel("📊 Xuất dữ liệu")
      .setStyle(ButtonStyle.Success)
  );

  return [buttonRow];
}

function createPerformanceComponents() {
  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("perf_refresh")
      .setLabel("🔄 Làm mới")
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId("perf_detailed")
      .setLabel("📊 Chi tiết")
      .setStyle(ButtonStyle.Primary),
    new ButtonBuilder()
      .setCustomId("perf_optimize")
      .setLabel("⚡ Tối ưu")
      .setStyle(ButtonStyle.Success)
  );

  return [buttonRow];
}

function createUsageComponents(period) {
  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId("usage_period")
    .setPlaceholder("Chọn khoảng thời gian")
    .addOptions([
      { label: "Hôm nay", value: "today" },
      { label: "7 ngày qua", value: "week" },
      { label: "30 ngày qua", value: "month" },
      { label: "Tất cả", value: "all" },
    ]);

  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("usage_refresh")
      .setLabel("🔄 Làm mới")
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId("usage_detailed")
      .setLabel("📊 Chi tiết")
      .setStyle(ButtonStyle.Primary)
  );

  return [new ActionRowBuilder().addComponents(selectMenu), buttonRow];
}

function createGuildComponents(sort) {
  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId("guild_sort")
    .setPlaceholder("Sắp xếp theo")
    .addOptions([
      { label: "Thành viên nhiều nhất", value: "members" },
      { label: "Hoạt động nhiều nhất", value: "activity" },
      { label: "Mới tham gia", value: "newest" },
      { label: "Tên A-Z", value: "name" },
    ]);

  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("guild_refresh")
      .setLabel("🔄 Làm mới")
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId("guild_export")
      .setLabel("📊 Xuất danh sách")
      .setStyle(ButtonStyle.Primary)
  );

  return [new ActionRowBuilder().addComponents(selectMenu), buttonRow];
}

async function handleDatabase(interaction, client) {
  await interaction.deferReply();

  try {
    const dbStats = await getDatabaseStats(client);

    const embed = createInfoEmbed(
      "💾 Thống kê Database",
      "Thông tin chi tiết về database và hiệu suất truy vấn"
    );

    embed.addFields({
      name: "📊 Tổng quan",
      value:
        `**Status:** ${dbStats.status}\n` +
        `**Type:** ${dbStats.type}\n` +
        `**Size:** ${dbStats.size}\n` +
        `**Tables:** ${dbStats.tables}`,
      inline: true,
    });

    embed.addFields({
      name: "⚡ Performance",
      value:
        `**Avg Query Time:** ${dbStats.avgQueryTime}ms\n` +
        `**Queries/min:** ${dbStats.queriesPerMin}\n` +
        `**Cache Hit Rate:** ${dbStats.cacheHitRate}%\n` +
        `**Connections:** ${dbStats.connections}`,
      inline: true,
    });

    embed.addFields({
      name: "📈 Activity",
      value:
        `**Total Queries:** ${dbStats.totalQueries.toLocaleString()}\n` +
        `**Successful:** ${dbStats.successfulQueries.toLocaleString()}\n` +
        `**Failed:** ${dbStats.failedQueries}\n` +
        `**Success Rate:** ${dbStats.successRate}%`,
      inline: true,
    });

    const components = createDatabaseComponents();

    await interaction.editReply({ embeds: [embed], components });
  } catch (error) {
    console.error("Lỗi khi lấy thống kê database:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống",
      "Không thể lấy thống kê database."
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleErrors(interaction, client) {
  await interaction.deferReply();

  try {
    const errorStats = await getErrorStats(client);

    const embed = createInfoEmbed(
      "❌ Thống kê Lỗi",
      "Thông tin về lỗi và sự cố trong 24 giờ qua"
    );

    embed.addFields({
      name: "📊 Tổng quan",
      value:
        `**Total Errors:** ${errorStats.totalErrors}\n` +
        `**Error Rate:** ${errorStats.errorRate}%\n` +
        `**Critical:** ${errorStats.criticalErrors}\n` +
        `**Warnings:** ${errorStats.warnings}`,
      inline: true,
    });

    embed.addFields({
      name: "🔍 Top Error Types",
      value: errorStats.topErrors
        .map(
          (error, index) =>
            `**${index + 1}.** ${error.type}\n` + `└ ${error.count} occurrences`
        )
        .join("\n"),
      inline: true,
    });

    embed.addFields({
      name: "⏰ Recent Errors",
      value:
        errorStats.recentErrors
          .map((error) => `**${error.time}** - ${error.message}`)
          .join("\n") || "Không có lỗi gần đây",
      inline: false,
    });

    const components = createErrorComponents();

    await interaction.editReply({ embeds: [embed], components });
  } catch (error) {
    console.error("Lỗi khi lấy thống kê lỗi:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống",
      "Không thể lấy thống kê lỗi."
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleRealtime(interaction, client) {
  await interaction.deferReply();

  try {
    const realtimeStats = await getRealtimeStats(client);

    const embed = createInfoEmbed(
      "📡 Thống kê Realtime",
      `Dữ liệu thời gian thực - Cập nhật lúc <t:${Math.floor(
        Date.now() / 1000
      )}:T>`
    );

    embed.addFields({
      name: "⚡ Current Activity",
      value:
        `**Active Commands:** ${realtimeStats.activeCommands}\n` +
        `**Queue Size:** ${realtimeStats.queueSize}\n` +
        `**Processing:** ${realtimeStats.processing}\n` +
        `**Waiting:** ${realtimeStats.waiting}`,
      inline: true,
    });

    embed.addFields({
      name: "📊 Live Metrics",
      value:
        `**CPU:** ${realtimeStats.cpu}%\n` +
        `**Memory:** ${realtimeStats.memory}%\n` +
        `**Network:** ${realtimeStats.network}%\n` +
        `**Disk I/O:** ${realtimeStats.diskIO}%`,
      inline: true,
    });

    embed.addFields({
      name: "🌐 Connections",
      value:
        `**WebSocket:** ${realtimeStats.websocket}\n` +
        `**Database:** ${realtimeStats.database}\n` +
        `**API Calls:** ${realtimeStats.apiCalls}\n` +
        `**Cache Ops:** ${realtimeStats.cacheOps}`,
      inline: true,
    });

    const components = createRealtimeComponents();

    await interaction.editReply({ embeds: [embed], components });
  } catch (error) {
    console.error("Lỗi khi lấy thống kê realtime:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống",
      "Không thể lấy thống kê realtime."
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

// Additional helper functions
async function getDatabaseStats(client) {
  return {
    status: "🟢 Connected",
    type: "SQLite",
    size: "15.2 MB",
    tables: 12,
    avgQueryTime: 25,
    queriesPerMin: 150,
    cacheHitRate: 85,
    connections: 5,
    totalQueries: 50000,
    successfulQueries: 49500,
    failedQueries: 500,
    successRate: 99,
  };
}

async function getErrorStats(client) {
  return {
    totalErrors: 25,
    errorRate: 0.5,
    criticalErrors: 2,
    warnings: 15,
    topErrors: [
      { type: "Database Timeout", count: 8 },
      { type: "API Rate Limit", count: 6 },
      { type: "Permission Error", count: 4 },
    ],
    recentErrors: [
      { time: "14:30", message: "Database connection timeout" },
      { time: "13:45", message: "API rate limit exceeded" },
    ],
  };
}

async function getRealtimeStats(client) {
  return {
    activeCommands: Math.floor(Math.random() * 10) + 1,
    queueSize: Math.floor(Math.random() * 50),
    processing: Math.floor(Math.random() * 5),
    waiting: Math.floor(Math.random() * 20),
    cpu: Math.floor(Math.random() * 30) + 20,
    memory: Math.floor(Math.random() * 40) + 30,
    network: Math.floor(Math.random() * 20) + 10,
    diskIO: Math.floor(Math.random() * 15) + 5,
    websocket: "🟢 Connected",
    database: "🟢 Active",
    apiCalls: Math.floor(Math.random() * 100) + 50,
    cacheOps: Math.floor(Math.random() * 200) + 100,
  };
}

function createDatabaseComponents() {
  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("stats_database_refresh")
      .setLabel("🔄 Làm mới")
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId("stats_database_optimize")
      .setLabel("⚡ Tối ưu hóa")
      .setStyle(ButtonStyle.Primary)
  );

  return [buttonRow];
}

function createErrorComponents() {
  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("stats_errors_refresh")
      .setLabel("🔄 Làm mới")
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId("stats_errors_clear")
      .setLabel("🗑️ Xóa log")
      .setStyle(ButtonStyle.Danger)
  );

  return [buttonRow];
}

function createRealtimeComponents() {
  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("stats_realtime_refresh")
      .setLabel("🔄 Làm mới")
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId("stats_realtime_auto")
      .setLabel("⚡ Auto-refresh")
      .setStyle(ButtonStyle.Primary)
  );

  return [buttonRow];
}
