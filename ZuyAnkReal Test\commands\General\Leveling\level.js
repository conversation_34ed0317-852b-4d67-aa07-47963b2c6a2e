function _0x3389(_0x25b19a,_0x5cb594){const _0x473e93=_0x473e();return _0x3389=function(_0x338916,_0x563f6f){_0x338916=_0x338916-0x112;let _0x138491=_0x473e93[_0x338916];return _0x138491;},_0x3389(_0x25b19a,_0x5cb594);}(function(_0x50e7f0,_0x5bb93c){function _0x35a8d7(_0x50f368,_0x38a71c){return _0x3389(_0x38a71c-0x230,_0x50f368);}const _0x36802a=_0x50e7f0();while(!![]){try{const _0x49ef4e=-parseInt(_0x35a8d7(0x339,0x356))/0x1*(parseInt(_0x35a8d7(0x369,0x36b))/0x2)+-parseInt(_0x35a8d7(0x339,0x345))/0x3+-parseInt(_0x35a8d7(0x375,0x354))/0x4+parseInt(_0x35a8d7(0x386,0x367))/0x5*(parseInt(_0x35a8d7(0x382,0x379))/0x6)+parseInt(_0x35a8d7(0x38d,0x387))/0x7*(-parseInt(_0x35a8d7(0x32d,0x346))/0x8)+parseInt(_0x35a8d7(0x370,0x364))/0x9+-parseInt(_0x35a8d7(0x36f,0x34d))/0xa*(-parseInt(_0x35a8d7(0x372,0x36c))/0xb);if(_0x49ef4e===_0x5bb93c)break;else _0x36802a['push'](_0x36802a['shift']());}catch(_0x52f0b6){_0x36802a['push'](_0x36802a['shift']());}}}(_0x473e,0x9b1c8));function _0x473e(){const _0x5839c8=['give','Level','The\x20user\x20to\x20check\x20XP\x20and\x20level\x20for','129619nEeWbb','Levels','setName','addStringOption','3415452dopfxG','56kXzTNH','UpdatedXP','addUserOption','DataNotFound','addSubcommand','options','UpdatedLevel','7790ZIhcjB','Set\x20the\x20XP\x20or\x20Level\x20of\x20a\x20user','{xp}','readFileSync','./lang.yml','guild','{level}','4441540HWGaSb','save','1KkGLMH','Flags','Permission','utf8','getUser','Manage\x20user\x20levels\x20and\x20XP','setRequired','replace','max','js-yaml','getInteger','Amount\x20of\x20XP\x20or\x20Levels\x20to\x20remove','discord.js','remove','5559138fiiDXD','set','addChoices','270TlIQeG','exports','../../../models/UserData','The\x20user\x20to\x20remove\x20XP\x20or\x20Levels\x20from','1664018BqfmyQ','40381bEYfUs','{user}','reply','check','Remove\x20XP\x20or\x20Levels\x20from\x20a\x20user','NoPermission','load','has','member','addIntegerOption','Give\x20XP\x20or\x20Levels\x20to\x20a\x20user','type','roles','40938nWmsaq','amount','getString','LevelingSystem','username','Choose\x20XP\x20or\x20Level','user','some','level','./config.yml','setDescription'];_0x473e=function(){return _0x5839c8;};return _0x473e();}const {SlashCommandBuilder,EmbedBuilder,PermissionsBitField}=require(_0x16a2d6(0x48f,0x4a4)),UserData=require(_0x16a2d6(0x49b,0x4ab)),fs=require('fs');function _0x16a2d6(_0x528bd0,_0x4f9fe7){return _0x3389(_0x4f9fe7-0x372,_0x528bd0);}const yaml=require(_0x16a2d6(0x4bb,0x4a1)),config=yaml[_0x16a2d6(0x4b2,0x4b4)](fs[_0x16a2d6(0x4a6,0x492)](_0x16a2d6(0x4ad,0x4c4),'utf8')),lang=yaml[_0x16a2d6(0x492,0x4b4)](fs[_0x16a2d6(0x491,0x492)](_0x16a2d6(0x4b1,0x493),_0x16a2d6(0x4b2,0x49b)));module[_0x16a2d6(0x4b1,0x4aa)]={'data':new SlashCommandBuilder()['setName'](_0x16a2d6(0x4c3,0x4c3))[_0x16a2d6(0x4b7,0x4c5)](_0x16a2d6(0x481,0x49d))[_0x16a2d6(0x48b,0x48c)](_0x3a6ff1=>_0x3a6ff1[_0x16a2d6(0x48b,0x485)](_0x16a2d6(0x4cf,0x4c6))[_0x16a2d6(0x4dd,0x4c5)](_0x16a2d6(0x4d0,0x4b8))[_0x16a2d6(0x477,0x48a)](_0xb08f6d=>_0xb08f6d[_0x16a2d6(0x477,0x485)]('user')[_0x16a2d6(0x4a3,0x4c5)]('The\x20user\x20to\x20give\x20XP\x20or\x20Levels\x20to')['setRequired'](!![]))['addStringOption'](_0x49c40a=>_0x49c40a[_0x16a2d6(0x48a,0x485)](_0x16a2d6(0x4a7,0x4b9))[_0x16a2d6(0x4bc,0x4c5)]('Choose\x20XP\x20or\x20Level')[_0x16a2d6(0x4a3,0x49e)](!![])['addChoices']({'name':'XP','value':'xp'},{'name':'Level','value':_0x16a2d6(0x4cb,0x4c3)}))[_0x16a2d6(0x49f,0x4b7)](_0x2c9263=>_0x2c9263[_0x16a2d6(0x478,0x485)]('amount')[_0x16a2d6(0x4c6,0x4c5)]('Amount\x20of\x20XP\x20or\x20Levels\x20to\x20give')[_0x16a2d6(0x47e,0x49e)](!![])))['addSubcommand'](_0x2d2eb9=>_0x2d2eb9[_0x16a2d6(0x46e,0x485)](_0x16a2d6(0x4a7,0x4a5))[_0x16a2d6(0x4a6,0x4c5)](_0x16a2d6(0x4a4,0x4b2))[_0x16a2d6(0x46a,0x48a)](_0x58db14=>_0x58db14[_0x16a2d6(0x4a1,0x485)]('user')[_0x16a2d6(0x4c2,0x4c5)](_0x16a2d6(0x4ca,0x4ac))[_0x16a2d6(0x49c,0x49e)](!![]))[_0x16a2d6(0x469,0x486)](_0x2fa93f=>_0x2fa93f[_0x16a2d6(0x46c,0x485)](_0x16a2d6(0x4b9,0x4b9))['setDescription']('Choose\x20XP\x20or\x20Level')['setRequired'](!![])[_0x16a2d6(0x49c,0x4a8)]({'name':'XP','value':'xp'},{'name':'Level','value':_0x16a2d6(0x4c8,0x4c3)}))[_0x16a2d6(0x4bb,0x4b7)](_0x4cf366=>_0x4cf366[_0x16a2d6(0x4a2,0x485)](_0x16a2d6(0x4c1,0x4bc))[_0x16a2d6(0x4cd,0x4c5)](_0x16a2d6(0x487,0x4a3))[_0x16a2d6(0x4a6,0x49e)](!![])))[_0x16a2d6(0x487,0x48c)](_0x43cb58=>_0x43cb58[_0x16a2d6(0x463,0x485)]('set')[_0x16a2d6(0x4bd,0x4c5)](_0x16a2d6(0x48f,0x490))[_0x16a2d6(0x4a8,0x48a)](_0x2f7193=>_0x2f7193[_0x16a2d6(0x479,0x485)]('user')[_0x16a2d6(0x4b1,0x4c5)]('The\x20user\x20to\x20set\x20XP\x20or\x20Level\x20for')[_0x16a2d6(0x4a2,0x49e)](!![]))[_0x16a2d6(0x483,0x486)](_0x2492fe=>_0x2492fe['setName'](_0x16a2d6(0x4ab,0x4b9))[_0x16a2d6(0x4e6,0x4c5)](_0x16a2d6(0x4a5,0x4c0))[_0x16a2d6(0x4bb,0x49e)](!![])[_0x16a2d6(0x4aa,0x4a8)]({'name':'XP','value':'xp'},{'name':_0x16a2d6(0x4d1,0x4c7),'value':_0x16a2d6(0x4bd,0x4c3)}))[_0x16a2d6(0x4c9,0x4b7)](_0x1df95b=>_0x1df95b[_0x16a2d6(0x47c,0x485)]('amount')[_0x16a2d6(0x4a2,0x4c5)]('Set\x20the\x20amount\x20of\x20XP\x20or\x20Level')[_0x16a2d6(0x49d,0x49e)](!![])))[_0x16a2d6(0x481,0x48c)](_0x59ba8e=>_0x59ba8e['setName'](_0x16a2d6(0x4a6,0x4b1))[_0x16a2d6(0x4c6,0x4c5)]('Check\x20the\x20XP\x20and\x20level\x20of\x20a\x20user')[_0x16a2d6(0x49f,0x48a)](_0x43575c=>_0x43575c[_0x16a2d6(0x482,0x485)](_0x16a2d6(0x4c7,0x4c1))['setDescription'](_0x16a2d6(0x4b7,0x4c8))[_0x16a2d6(0x493,0x49e)](![]))),'category':'General',async 'execute'(_0x430b71){const _0x3db072=_0x430b71[_0xfa064d(0x438,0x420)]['getSubcommand'](),_0x3f7774=_0x430b71[_0xfa064d(0x409,0x420)][_0xfa064d(0x430,0x42f)](_0xfa064d(0x438,0x454))||_0x430b71[_0xfa064d(0x462,0x454)],_0x116ee6=_0x430b71[_0xfa064d(0x41b,0x427)]['id'];if(_0x3db072!==_0xfa064d(0x45c,0x444)){const _0x581197=config[_0xfa064d(0x46e,0x451)][_0xfa064d(0x44e,0x42d)],_0x38daec=_0x430b71[_0xfa064d(0x431,0x449)]['permissions'][_0xfa064d(0x44b,0x448)](PermissionsBitField[_0xfa064d(0x44d,0x42c)]['Administrator'])||_0x581197[_0xfa064d(0x466,0x455)](_0x5928df=>_0x430b71[_0xfa064d(0x467,0x449)][_0xfa064d(0x446,0x44d)]['cache']['has'](_0x5928df));if(!_0x38daec){await _0x430b71['reply']({'content':lang[_0xfa064d(0x408,0x417)][_0xfa064d(0x43a,0x446)],'ephemeral':!![]});return;}}let _0x5932b9=await UserData['findOne']({'userId':_0x3f7774['id'],'guildId':_0x116ee6});function _0xfa064d(_0x43cf17,_0x498b2b){return _0x16a2d6(_0x43cf17,_0x498b2b- -0x6d);}!_0x5932b9&&(_0x5932b9=new UserData({'userId':_0x3f7774['id'],'guildId':_0x116ee6,'xp':0x0,'level':0x1}));switch(_0x3db072){case _0xfa064d(0x447,0x459):case _0xfa064d(0x452,0x438):case _0xfa064d(0x431,0x43a):const _0x33844e=_0x430b71[_0xfa064d(0x401,0x420)][_0xfa064d(0x456,0x450)](_0xfa064d(0x46c,0x44c)),_0x2f570d=_0x430b71['options'][_0xfa064d(0x44d,0x435)](_0xfa064d(0x467,0x44f));_0x33844e==='xp'?_0x5932b9['xp']=_0x3db072===_0xfa064d(0x420,0x43a)?_0x2f570d:_0x3db072===_0xfa064d(0x47a,0x459)?_0x5932b9['xp']+_0x2f570d:Math[_0xfa064d(0x42a,0x433)](0x0,_0x5932b9['xp']-_0x2f570d):_0x5932b9['level']=_0x3db072===_0xfa064d(0x440,0x43a)?_0x2f570d:_0x3db072===_0xfa064d(0x45f,0x459)?_0x5932b9['level']+_0x2f570d:Math['max'](0x1,_0x5932b9[_0xfa064d(0x445,0x456)]-_0x2f570d);await _0x5932b9[_0xfa064d(0x44c,0x42a)](),await _0x430b71[_0xfa064d(0x443,0x443)]({'content':_0x33844e==='xp'?lang[_0xfa064d(0x417,0x417)][_0xfa064d(0x407,0x41c)][_0xfa064d(0x42b,0x432)]('{user}',_0x3f7774[_0xfa064d(0x453,0x452)])[_0xfa064d(0x420,0x432)](_0xfa064d(0x417,0x424),_0x5932b9['xp']):lang[_0xfa064d(0x429,0x417)][_0xfa064d(0x443,0x421)][_0xfa064d(0x441,0x432)]('{user}',_0x3f7774['username'])['replace'](_0xfa064d(0x405,0x428),_0x5932b9[_0xfa064d(0x436,0x456)]),'ephemeral':!![]});break;case _0xfa064d(0x432,0x444):if(!_0x5932b9){await _0x430b71[_0xfa064d(0x45a,0x443)]({'content':lang[_0xfa064d(0x42f,0x417)][_0xfa064d(0x431,0x41e)]['replace'](_0xfa064d(0x455,0x442),_0x3f7774[_0xfa064d(0x471,0x452)]),'ephemeral':!![]});return;}await _0x430b71['reply']({'content':lang['Levels']['CurrentLevelAndXP']['replace'](_0xfa064d(0x45d,0x442),_0x3f7774[_0xfa064d(0x458,0x452)])[_0xfa064d(0x427,0x432)](_0xfa064d(0x414,0x428),_0x5932b9[_0xfa064d(0x45b,0x456)])[_0xfa064d(0x446,0x432)](_0xfa064d(0x413,0x424),_0x5932b9['xp']),'ephemeral':!![]});break;}}};