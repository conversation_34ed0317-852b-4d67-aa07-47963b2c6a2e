const {
  <PERSON><PERSON><PERSON>ommandBuilder,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  StringSelectMenuBuilder,
} = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
} = require("../../utils/embedBuilder.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("searchbot")
    .setDescription("Tìm kiếm bot Discord với nhiều tiêu chí")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("category")
        .setDescription("Tìm bot theo danh mục")
        .addStringOption((option) =>
          option
            .setName("category")
            .setDescription("Danh mục bot")
            .setRequired(true)
            .addChoices(
              { name: "🎵 Music - Nhạc", value: "music" },
              { name: "🔨 Moderation - Ki<PERSON><PERSON>", value: "moderation" },
              { name: "🎮 Gaming - Game", value: "gaming" },
              { name: "🤖 Utility - Tiện ích", value: "utility" },
              { name: "🎨 Fun - Giải trí", value: "fun" },
              { name: "📊 Analytics - Thống kê", value: "analytics" },
              { name: "🛡️ Security - Bảo mật", value: "security" },
              { name: "💰 Economy - Kinh tế", value: "economy" },
              { name: "📝 Logging - Ghi log", value: "logging" },
              { name: "🎭 Roleplay - Nhập vai", value: "roleplay" }
            )
        )
        .addStringOption((option) =>
          option
            .setName("sort")
            .setDescription("Sắp xếp theo")
            .setRequired(false)
            .addChoices(
              { name: "Phổ biến nhất", value: "popular" },
              { name: "Đánh giá cao nhất", value: "rating" },
              { name: "Mới nhất", value: "newest" },
              { name: "Nhiều server nhất", value: "servers" }
            )
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("features")
        .setDescription("Tìm bot theo tính năng cụ thể")
        .addStringOption((option) =>
          option
            .setName("feature")
            .setDescription("Tính năng cần tìm")
            .setRequired(true)
            .addChoices(
              { name: "Slash Commands", value: "slash_commands" },
              { name: "Web Dashboard", value: "web_dashboard" },
              { name: "Custom Commands", value: "custom_commands" },
              { name: "Auto Moderation", value: "auto_moderation" },
              { name: "Music Streaming", value: "music_streaming" },
              { name: "Ticket System", value: "ticket_system" },
              { name: "Leveling System", value: "leveling_system" },
              { name: "Reaction Roles", value: "reaction_roles" },
              { name: "Welcome Messages", value: "welcome_messages" },
              { name: "Giveaways", value: "giveaways" }
            )
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("name")
        .setDescription("Tìm bot theo tên")
        .addStringOption((option) =>
          option
            .setName("query")
            .setDescription("Tên bot cần tìm")
            .setRequired(true)
        )
        .addBooleanOption((option) =>
          option
            .setName("exact_match")
            .setDescription("Tìm kiếm chính xác")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("advanced")
        .setDescription("Tìm kiếm nâng cao với nhiều bộ lọc")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("trending")
        .setDescription("Xem bot đang thịnh hành")
        .addStringOption((option) =>
          option
            .setName("period")
            .setDescription("Khoảng thời gian")
            .setRequired(false)
            .addChoices(
              { name: "Hôm nay", value: "today" },
              { name: "Tuần này", value: "week" },
              { name: "Tháng này", value: "month" },
              { name: "Tất cả thời gian", value: "all" }
            )
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("compare")
        .setDescription("So sánh các bot")
        .addStringOption((option) =>
          option
            .setName("bot1")
            .setDescription("Bot thứ nhất")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option.setName("bot2").setDescription("Bot thứ hai").setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("bot3")
            .setDescription("Bot thứ ba (tùy chọn)")
            .setRequired(false)
        )
    ),
  category: "utility",
  adminOnly: false,
  manageServer: false,

  async execute(interaction, client) {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case "category":
        await handleCategorySearch(interaction, client);
        break;
      case "features":
        await handleFeatureSearch(interaction, client);
        break;
      case "name":
        await handleNameSearch(interaction, client);
        break;
      case "advanced":
        await handleAdvancedSearch(interaction, client);
        break;
      case "trending":
        await handleTrendingSearch(interaction, client);
        break;
      case "compare":
        await handleCompareSearch(interaction, client);
        break;
    }
  },
};

async function handleCategorySearch(interaction, client) {
  const category = interaction.options.getString("category");
  const sort = interaction.options.getString("sort") || "popular";

  await interaction.deferReply();

  try {
    const bots = await getBotsByCategory(category, sort);

    const embed = createInfoEmbed(
      `🔍 Bot ${getCategoryDisplayName(category)}`,
      `Tìm thấy **${bots.length}** bot trong danh mục này`
    );

    embed.addFields({
      name: "📋 Danh sách bot",
      value: bots
        .slice(0, 10)
        .map(
          (bot, index) =>
            `**${index + 1}.** [${bot.name}](${bot.invite_url})\n` +
            `└ ${bot.description}\n` +
            `└ 📊 ${bot.servers.toLocaleString()} servers • ⭐ ${bot.rating}/5`
        )
        .join("\n\n"),
      inline: false,
    });

    const components = createBotListComponents(bots, 0, category);

    await interaction.editReply({ embeds: [embed], components });
  } catch (error) {
    console.error("Lỗi khi tìm kiếm bot:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi tìm kiếm!",
      "Không thể tìm kiếm bot. Vui lòng thử lại sau!"
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleFeatureSearch(interaction, client) {
  const feature = interaction.options.getString("feature");

  await interaction.deferReply();

  try {
    const bots = await getBotsByFeature(feature);

    const embed = createInfoEmbed(
      `🔧 Bot có tính năng: ${getFeatureDisplayName(feature)}`,
      `Tìm thấy **${bots.length}** bot có tính năng này`
    );

    embed.addFields({
      name: "🤖 Danh sách bot",
      value: bots
        .slice(0, 8)
        .map(
          (bot, index) =>
            `**${index + 1}.** [${bot.name}](${bot.invite_url})\n` +
            `└ ${bot.short_description}\n` +
            `└ 🎯 ${
              bot.features.length
            } tính năng • 📊 ${bot.servers.toLocaleString()} servers`
        )
        .join("\n\n"),
      inline: false,
    });

    const components = createBotListComponents(bots, 0, feature);

    await interaction.editReply({ embeds: [embed], components });
  } catch (error) {
    console.error("Lỗi khi tìm kiếm bot theo tính năng:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi tìm kiếm!",
      "Không thể tìm kiếm bot theo tính năng. Vui lòng thử lại sau!"
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleNameSearch(interaction, client) {
  const query = interaction.options.getString("query");
  const exactMatch = interaction.options.getBoolean("exact_match") || false;

  await interaction.deferReply();

  try {
    const bots = await searchBotsByName(query, exactMatch);

    if (bots.length === 0) {
      const embed = createErrorEmbed(
        "Không tìm thấy bot!",
        `Không tìm thấy bot nào với từ khóa: **${query}**\n\n` +
          `💡 **Gợi ý:**\n` +
          `• Thử tìm kiếm với từ khóa khác\n` +
          `• Sử dụng \`/searchbot category\` để duyệt theo danh mục\n` +
          `• Sử dụng \`/searchbot trending\` để xem bot phổ biến`
      );
      return await interaction.editReply({ embeds: [embed] });
    }

    const embed = createInfoEmbed(
      `🔍 Kết quả tìm kiếm: "${query}"`,
      `Tìm thấy **${bots.length}** bot phù hợp`
    );

    embed.addFields({
      name: "🎯 Kết quả",
      value: bots
        .slice(0, 8)
        .map(
          (bot, index) =>
            `**${index + 1}.** [${bot.name}](${bot.invite_url})\n` +
            `└ ${bot.description}\n` +
            `└ 📊 ${bot.servers.toLocaleString()} servers • ⭐ ${
              bot.rating
            }/5 • 🏷️ ${bot.category}`
        )
        .join("\n\n"),
      inline: false,
    });

    const components = createBotListComponents(bots, 0, query);

    await interaction.editReply({ embeds: [embed], components });
  } catch (error) {
    console.error("Lỗi khi tìm kiếm bot theo tên:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi tìm kiếm!",
      "Không thể tìm kiếm bot theo tên. Vui lòng thử lại sau!"
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

function createBotListComponents(bots, currentPage, searchTerm) {
  const components = [];

  if (bots.length > 10) {
    const navigationRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(`bot_search_prev_${currentPage}_${searchTerm}`)
        .setLabel("◀ Trước")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(currentPage === 0),
      new ButtonBuilder()
        .setCustomId(`bot_search_info_${searchTerm}`)
        .setLabel(`Trang ${currentPage + 1}/${Math.ceil(bots.length / 10)}`)
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(true),
      new ButtonBuilder()
        .setCustomId(`bot_search_next_${currentPage}_${searchTerm}`)
        .setLabel("Sau ▶")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(currentPage >= Math.ceil(bots.length / 10) - 1)
    );
    components.push(navigationRow);
  }

  const actionRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("bot_search_refresh")
      .setLabel("🔄 Làm mới")
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId("bot_search_filter")
      .setLabel("🔧 Bộ lọc")
      .setStyle(ButtonStyle.Primary),
    new ButtonBuilder()
      .setCustomId("bot_search_random")
      .setLabel("🎲 Ngẫu nhiên")
      .setStyle(ButtonStyle.Success)
  );
  components.push(actionRow);

  return components;
}

function getCategoryDisplayName(category) {
  const categories = {
    music: "🎵 Nhạc",
    moderation: "🔨 Kiểm duyệt",
    gaming: "🎮 Game",
    utility: "🤖 Tiện ích",
    fun: "🎨 Giải trí",
    analytics: "📊 Thống kê",
    security: "🛡️ Bảo mật",
    economy: "💰 Kinh tế",
    logging: "📝 Ghi log",
    roleplay: "🎭 Nhập vai",
  };
  return categories[category] || category;
}

function getFeatureDisplayName(feature) {
  const features = {
    slash_commands: "Slash Commands",
    web_dashboard: "Web Dashboard",
    custom_commands: "Custom Commands",
    auto_moderation: "Auto Moderation",
    music_streaming: "Music Streaming",
    ticket_system: "Ticket System",
    leveling_system: "Leveling System",
    reaction_roles: "Reaction Roles",
    welcome_messages: "Welcome Messages",
    giveaways: "Giveaways",
  };
  return features[feature] || feature;
}

// Mock data functions - In production, these would connect to real bot APIs
async function getBotsByCategory(category, sort) {
  // Mock implementation
  return generateMockBots(category, sort);
}

async function getBotsByFeature(feature) {
  // Mock implementation
  return generateMockBotsByFeature(feature);
}

async function searchBotsByName(query, exactMatch) {
  // Mock implementation
  return generateMockSearchResults(query, exactMatch);
}

function generateMockBots(category, sort) {
  const mockBots = [
    {
      name: "MEE6",
      description: "Bot moderation và leveling phổ biến nhất",
      short_description: "Moderation + Leveling",
      invite_url: "https://mee6.xyz/",
      servers: 16500000,
      rating: 4.8,
      category: "moderation",
      features: [
        "slash_commands",
        "web_dashboard",
        "auto_moderation",
        "leveling_system",
      ],
    },
    {
      name: "Dyno",
      description: "Bot đa năng với nhiều tính năng moderation",
      short_description: "All-in-one moderation",
      invite_url: "https://dyno.gg/",
      servers: 8200000,
      rating: 4.6,
      category: "moderation",
      features: ["custom_commands", "auto_moderation", "web_dashboard"],
    },
    {
      name: "Groovy",
      description: "Bot nhạc chất lượng cao với nhiều nguồn",
      short_description: "High-quality music bot",
      invite_url: "https://groovy.bot/",
      servers: 16000000,
      rating: 4.9,
      category: "music",
      features: ["music_streaming", "slash_commands"],
    },
  ];

  return mockBots.filter((bot) => bot.category === category);
}

async function handleAdvancedSearch(interaction, client) {
  await interaction.deferReply();

  const embed = createInfoEmbed(
    "🔍 Tìm kiếm nâng cao",
    "Tính năng tìm kiếm nâng cao đang được phát triển.\n\nSẽ bao gồm:\n• Lọc theo nhiều tiêu chí\n• Tìm kiếm theo quyền hạn\n• Sắp xếp tùy chỉnh\n• Lưu bộ lọc"
  );

  await interaction.editReply({ embeds: [embed] });
}

async function handleTrendingSearch(interaction, client) {
  await interaction.deferReply();

  try {
    const trendingBots = generateTrendingBots();

    const embed = createInfoEmbed(
      "🔥 Bot đang thịnh hành",
      `Top **${trendingBots.length}** bot phổ biến nhất hiện tại`
    );

    embed.addFields({
      name: "📈 Trending bots",
      value: trendingBots
        .map(
          (bot, index) =>
            `**${index + 1}.** [${bot.name}](${bot.invite_url})\n` +
            `└ ${bot.description}\n` +
            `└ 📊 ${bot.servers.toLocaleString()} servers • ⭐ ${
              bot.rating
            }/5 • 🔥 ${bot.growth}% tăng trưởng`
        )
        .join("\n\n"),
      inline: false,
    });

    const components = createBotListComponents(trendingBots, 0, "trending");

    await interaction.editReply({ embeds: [embed], components });
  } catch (error) {
    console.error("Lỗi khi lấy trending bots:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi tìm kiếm!",
      "Không thể lấy danh sách bot trending. Vui lòng thử lại sau!"
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleCompareSearch(interaction, client) {
  await interaction.deferReply();

  const embed = createInfoEmbed(
    "⚖️ So sánh bot",
    "Tính năng so sánh bot đang được phát triển.\n\nSẽ bao gồm:\n• So sánh tính năng\n• So sánh hiệu suất\n• Bảng so sánh chi tiết\n• Khuyến nghị"
  );

  await interaction.editReply({ embeds: [embed] });
}

function generateTrendingBots() {
  return [
    {
      name: "Carl-bot",
      description: "Bot đa năng với automod và reaction roles mạnh mẽ",
      invite_url: "https://carl-bot.io/",
      servers: 12000000,
      rating: 4.7,
      growth: 15,
    },
    {
      name: "Ticket Tool",
      description: "Hệ thống ticket chuyên nghiệp cho support",
      invite_url: "https://tickettool.xyz/",
      servers: 3500000,
      rating: 4.9,
      growth: 25,
    },
    {
      name: "Mudae",
      description: "Game waifu và husbando phổ biến",
      invite_url: "https://mudae.net/",
      servers: 2800000,
      rating: 4.6,
      growth: 12,
    },
  ];
}

function generateMockBotsByFeature(feature) {
  const mockBots = [
    {
      name: "Example Bot",
      description: `Bot có tính năng ${feature}`,
      short_description: "Feature-rich bot",
      invite_url: "https://example.com/",
      servers: 1000000,
      rating: 4.5,
      features: [feature],
    },
  ];
  return mockBots;
}

function generateMockSearchResults(query, exactMatch) {
  const mockResults = [
    {
      name: `${query} Bot`,
      description: `Bot liên quan đến ${query}`,
      invite_url: "https://example.com/",
      servers: 500000,
      rating: 4.3,
      category: "utility",
    },
  ];
  return exactMatch
    ? mockResults.filter(
        (bot) => bot.name.toLowerCase() === query.toLowerCase()
      )
    : mockResults;
}
