const { SlashCommandBuilder } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed } = require('../../utils/embedBuilder.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('testaudit')
        .setDescription('Test audit log system manually')
        .addStringOption(option =>
            option.setName('event')
                .setDescription('Event type to test')
                .setRequired(false)
                .addChoices(
                    { name: 'User Join', value: 'USER_JOIN' },
                    { name: 'User Leave', value: 'USER_LEAVE' },
                    { name: 'Channel Create', value: 'CHANNEL_CREATE' },
                    { name: 'Role Create', value: 'ROLE_CREATE' },
                    { name: 'Message Delete', value: 'MESSAGE_DELETE' }
                )
        ),
    category: 'utility',
    adminOnly: false,
    manageServer: true,

    async execute(interaction, client) {
        const eventType = interaction.options.getString('event') || 'USER_JOIN';
        const guildId = interaction.guild.id;

        await interaction.deferReply({ ephemeral: true });

        try {
            // Check if audit log is configured
            console.log(`🧪 Testing audit log for guild: ${guildId}`);
            
            const config = await client.db.getAuditLogConfig(guildId);
            console.log(`📋 Config found:`, config ? 'Yes' : 'No');
            
            if (!config) {
                const errorEmbed = createErrorEmbed(
                    'Audit Log chưa được thiết lập!',
                    'Vui lòng chạy `/auditlog setup` trước khi test.'
                );
                return await interaction.editReply({ embeds: [errorEmbed] });
            }

            if (!config.enabled) {
                const errorEmbed = createErrorEmbed(
                    'Audit Log đã bị tắt!',
                    'Audit log đã được thiết lập nhưng đang bị tắt.'
                );
                return await interaction.editReply({ embeds: [errorEmbed] });
            }

            // Create test event data
            const testEventData = {
                user: interaction.user.tag,
                userId: interaction.user.id,
                action: `Test ${eventType} event`,
                details: `This is a test ${eventType} event triggered by ${interaction.user.tag}`,
                timestamp: new Date().toISOString(),
                channel: interaction.channel.name,
                target: 'Test Target'
            };

            console.log(`🎯 Testing event: ${eventType}`);
            console.log(`📝 Event data:`, testEventData);

            // Test shouldLogEvent
            const shouldLog = client.db.shouldLogEvent(config, eventType);
            console.log(`📊 Should log event:`, shouldLog);

            if (!shouldLog) {
                const errorEmbed = createErrorEmbed(
                    'Event không được log!',
                    `Event type "${eventType}" không được cấu hình để log trong config hiện tại.`
                );
                return await interaction.editReply({ embeds: [errorEmbed] });
            }

            // Add to database
            await client.db.addAuditLog(guildId, {
                eventType,
                ...testEventData,
            });
            console.log(`💾 Event saved to database`);

            // Try to send to audit log channel
            try {
                const channel = await client.channels.fetch(config.channelId);
                console.log(`📡 Channel found: ${channel ? channel.name : 'Not found'}`);
                
                if (!channel) {
                    const errorEmbed = createErrorEmbed(
                        'Kênh audit log không tồn tại!',
                        `Không thể tìm thấy kênh với ID: ${config.channelId}`
                    );
                    return await interaction.editReply({ embeds: [errorEmbed] });
                }

                // Create test embed
                const { createInfoEmbed } = require('../../utils/embedBuilder.js');
                
                let color = 0x3498db; // Default blue
                let emoji = "📝";

                // Set color and emoji based on event type
                if (eventType.includes("DELETE") || eventType.includes("REMOVE")) {
                    color = 0xe74c3c; // Red
                    emoji = "🗑️";
                } else if (eventType.includes("CREATE") || eventType.includes("ADD") || eventType.includes("JOIN")) {
                    color = 0x2ecc71; // Green
                    emoji = "✅";
                } else if (eventType.includes("UPDATE") || eventType.includes("EDIT")) {
                    color = 0xf39c12; // Orange
                    emoji = "✏️";
                }

                const testEmbed = createInfoEmbed(
                    `${emoji} Test ${eventType}`,
                    testEventData.details
                );

                testEmbed.setColor(color);
                testEmbed.addFields(
                    { name: "👤 User", value: testEventData.user, inline: true },
                    { name: "📍 Channel", value: `#${testEventData.channel}`, inline: true },
                    { name: "🎯 Target", value: testEventData.target, inline: true }
                );
                testEmbed.setTimestamp();
                testEmbed.setFooter({
                    text: `Test Event • ${interaction.guild.name}`,
                    iconURL: interaction.guild.iconURL()
                });

                await channel.send({ embeds: [testEmbed] });
                console.log(`✅ Test message sent to channel`);

                // Success response
                const successEmbed = createSuccessEmbed(
                    '✅ Test thành công!',
                    `Đã gửi test event "${eventType}" vào kênh audit log.`
                );

                successEmbed.addFields(
                    { name: '📍 Kênh', value: `${channel}`, inline: true },
                    { name: '📋 Event Type', value: eventType, inline: true },
                    { name: '⏰ Thời gian', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                );

                await interaction.editReply({ embeds: [successEmbed] });

            } catch (channelError) {
                console.error('❌ Error accessing channel:', channelError);
                const errorEmbed = createErrorEmbed(
                    'Lỗi kênh audit log!',
                    `Không thể gửi message vào kênh audit log: ${channelError.message}`
                );
                await interaction.editReply({ embeds: [errorEmbed] });
            }

        } catch (error) {
            console.error('❌ Test audit error:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi test!',
                `Đã xảy ra lỗi khi test audit log: ${error.message}`
            );
            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },
};
