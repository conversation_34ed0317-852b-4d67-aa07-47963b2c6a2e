const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle NSFW changes
            if (oldChannel.nsfw === newChannel.nsfw) return;
            
            console.log(`🔞 Channel NSFW updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_NSFW_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_NSFW_UPDATE',
                user: 'System',
                userId: null,
                action: 'Cài đặt NSFW kênh được thay đổi',
                details: `Cài đặt NSFW của kênh **${newChannel.name}** đã được ${newChannel.nsfw ? 'bật' : 'tắt'}`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the NSFW setting from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Cài đặt NSFW được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for NSFW update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔞 Cài đặt NSFW kênh thay đổi',
                `Vừa có cài đặt NSFW kênh được thay đổi trong server`
            );
            
            embed.setColor(newChannel.nsfw ? 0xe74c3c : 0x2ecc71); // Red if NSFW enabled, green if disabled
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Trạng thái cũ',
                    value: `- ${oldChannel.nsfw ? '🔞 NSFW' : '✅ Safe'}`,
                    inline: true
                },
                {
                    name: '> Trạng thái mới',
                    value: `- ${newChannel.nsfw ? '🔞 NSFW' : '✅ Safe'}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Add warning if NSFW was enabled
            if (newChannel.nsfw) {
                embed.addFields({
                    name: '⚠️ Cảnh báo',
                    value: '- Kênh này hiện đã được đánh dấu là NSFW',
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel NSFW Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel NSFW update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelNsfwUpdate audit log:', error);
        }
    }
};
