const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'voiceUserMove', // Custom event for when user is moved by moderator
    async execute(member, oldChannel, newChannel, executor, client) {
        try {
            console.log(`🔀 User moved in voice: ${member.user.tag} in ${member.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(member.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'VOICE_USER_MOVE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'VOICE_USER_MOVE',
                user: executor?.tag || 'System',
                userId: executor?.id || null,
                action: 'Thành viên được di chuyển voice',
                details: `**${member.user.tag}** đ<PERSON> được di chuyển từ **${oldChannel.name}** sang **${newChannel.name}**`,
                target: member.user.tag,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(member.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔀 Thành viên được di chuyển voice',
                `Vừa có thành viên được di chuyển kênh thoại bởi moderator`
            );
            
            embed.setColor(0x9b59b6); // Purple for move
            
            embed.addFields([
                {
                    name: '> Thành viên được di chuyển',
                    value: `- ${member.user.tag} (${member.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${member.user.id}`,
                    inline: true
                },
                {
                    name: '> Người thực hiện',
                    value: `- ${executor ? `${executor.tag} (${executor})` : 'System'}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add channel information
            embed.addFields([
                {
                    name: '> Kênh cũ',
                    value: `- ${oldChannel.name}`,
                    inline: true
                },
                {
                    name: '> Kênh mới',
                    value: `- ${newChannel.name}`,
                    inline: true
                },
                {
                    name: '> ID kênh mới',
                    value: `- ${newChannel.id}`,
                    inline: true
                }
            ]);
            
            // Add member counts
            embed.addFields([
                {
                    name: '> Số người trong kênh cũ',
                    value: `- ${oldChannel.members.size} thành viên`,
                    inline: true
                },
                {
                    name: '> Số người trong kênh mới',
                    value: `- ${newChannel.members.size} thành viên`,
                    inline: true
                }
            ]);
            
            // Add voice state info if available
            const voiceState = member.voice;
            if (voiceState) {
                const voiceStates = [];
                if (voiceState.mute) voiceStates.push('🔇 Muted');
                if (voiceState.deaf) voiceStates.push('🔇 Deafened');
                if (voiceState.selfMute) voiceStates.push('🔇 Self Muted');
                if (voiceState.selfDeaf) voiceStates.push('🔇 Self Deafened');
                if (voiceState.streaming) voiceStates.push('📺 Streaming');
                if (voiceState.selfVideo) voiceStates.push('📹 Camera On');
                
                if (voiceStates.length > 0) {
                    embed.addFields({
                        name: '> Trạng thái voice',
                        value: `- ${voiceStates.join(', ')}`,
                        inline: false
                    });
                }
            }
            
            // Add moderator action note
            embed.addFields({
                name: '> Loại hành động',
                value: `- Di chuyển bởi moderator`,
                inline: true
            });
            
            // Set user avatar as thumbnail
            if (member.user.displayAvatarURL()) {
                embed.setThumbnail(member.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${member.guild.name} • Voice User Move`,
                iconURL: member.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Voice user move logged: ${member.user.tag}`);
            
        } catch (error) {
            console.error('Error in voiceUserMove audit log:', error);
        }
    }
};
