const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildEmojiDelete,
    async execute(emoji, client) {
        try {
            console.log(`🗑️ Emoji deleted: ${emoji.name} in ${emoji.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(emoji.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EMOJI_DELETE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EMOJI_DELETE',
                user: 'System',
                userId: null,
                action: 'Emoji được xóa',
                details: `Emoji **${emoji.name}** đã được xóa`,
                target: emoji.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the emoji from audit logs
            try {
                const auditLogs = await emoji.guild.fetchAuditLogs({
                    type: 62, // EMOJI_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === emoji.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Emoji được xóa bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for emoji delete');
            }
            
            // Add to database
            await client.db.addAuditLog(emoji.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Emoji được xóa',
                `Vừa có một emoji được xóa khỏi server`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            embed.addFields([
                {
                    name: '> Tên emoji',
                    value: `- ${emoji.name}`,
                    inline: true
                },
                {
                    name: '> ID emoji',
                    value: `- ${emoji.id}`,
                    inline: true
                },
                {
                    name: '> Animated',
                    value: `- ${emoji.animated ? 'Có' : 'Không'}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add roles that could use this emoji
            if (emoji.roles && emoji.roles.cache.size > 0) {
                const rolesList = emoji.roles.cache.map(role => role.name).join(', ');
                embed.addFields({
                    name: '> Roles có thể sử dụng',
                    value: rolesList.length > 1000 ? rolesList.substring(0, 1000) + '...' : rolesList,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> Quyền sử dụng',
                    value: '- Tất cả thành viên',
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set emoji as thumbnail (if still accessible)
            if (emoji.url) {
                embed.setThumbnail(emoji.url);
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${emoji.guild.name} • Emoji Delete`,
                iconURL: emoji.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Emoji delete logged: ${emoji.name}`);
            
        } catch (error) {
            console.error('Error in emojiDelete audit log:', error);
        }
    }
};
