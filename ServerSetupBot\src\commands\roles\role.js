const {
  SlashCommandBuilder,
  PermissionFlagsBits,
  PermissionsBitField,
  ActionRowBuilder,
  StringSelectMenuBuilder,
  ButtonBuilder,
  ButtonStyle,
  ComponentType,
} = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
  createWarningEmbed,
} = require("../../utils/embedBuilder.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("role")
    .setDescription("Quản lý role server")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("create")
        .setDescription("Tạo role mới")
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên role")
            .setRequired(true)
            .setMaxLength(100)
        )
        .addStringOption((option) =>
          option
            .setName("color")
            .setDescription("<PERSON>àu role (hex code, ví dụ: #ff0000)")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("hoist")
            .setDescription("Hiển thị riêng biệt trong danh sách thành viên")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("mentionable")
            .setDescription("Cho phép mention role này")
            .setRequired(false)
        )
        .addStringOption((option) =>
          option
            .setName("permissions")
            .setDescription("Quyền hạn cho role")
            .setRequired(false)
            .addChoices(
              { name: "Không có quyền đặc biệt", value: "none" },
              { name: "Moderator cơ bản", value: "moderator" },
              { name: "Admin cơ bản", value: "admin" },
              { name: "Quản lý kênh", value: "manage_channels" },
              { name: "Quản lý role", value: "manage_roles" },
              { name: "Quản lý tin nhắn", value: "manage_messages" },
              { name: "Tùy chỉnh", value: "custom" }
            )
        )
        .addStringOption((option) =>
          option
            .setName("icon")
            .setDescription("URL icon cho role (chỉ server boost level 2+)")
            .setRequired(false)
        )
        .addIntegerOption((option) =>
          option
            .setName("position")
            .setDescription("Vị trí role (cao hơn = quyền cao hơn)")
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(250)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("delete")
        .setDescription("Xóa role")
        .addRoleOption((option) =>
          option
            .setName("role")
            .setDescription("Role cần xóa")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do xóa role")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("edit")
        .setDescription("Chỉnh sửa role")
        .addRoleOption((option) =>
          option
            .setName("role")
            .setDescription("Role cần chỉnh sửa")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên mới cho role")
            .setRequired(false)
            .setMaxLength(100)
        )
        .addStringOption((option) =>
          option
            .setName("color")
            .setDescription("Màu mới cho role (hex code)")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("hoist")
            .setDescription("Hiển thị riêng biệt")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("mentionable")
            .setDescription("Cho phép mention")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("info")
        .setDescription("Xem thông tin role")
        .addRoleOption((option) =>
          option
            .setName("role")
            .setDescription("Role cần xem thông tin")
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("clone")
        .setDescription("Sao chép role")
        .addRoleOption((option) =>
          option
            .setName("role")
            .setDescription("Role cần sao chép")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên cho role mới")
            .setRequired(true)
            .setMaxLength(100)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("members")
        .setDescription("Xem danh sách thành viên có role")
        .addRoleOption((option) =>
          option
            .setName("role")
            .setDescription("Role cần xem thành viên")
            .setRequired(true)
        )
        .addIntegerOption((option) =>
          option
            .setName("page")
            .setDescription("Trang cần xem (mặc định: 1)")
            .setRequired(false)
            .setMinValue(1)
        )
    ),
  category: "roles",
  adminOnly: false,
  manageServer: false,

  async execute(interaction, client) {
    // Kiểm tra quyền bot
    if (
      !interaction.guild.members.me.permissions.has(
        PermissionFlagsBits.ManageRoles
      )
    ) {
      const errorEmbed = createErrorEmbed(
        "Bot thiếu quyền!",
        "Bot cần quyền `Manage Roles` để quản lý role."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case "create":
        await handleCreateRole(interaction, client);
        break;
      case "delete":
        await handleDeleteRole(interaction, client);
        break;
      case "edit":
        await handleEditRole(interaction, client);
        break;
      case "info":
        await handleRoleInfo(interaction, client);
        break;
      case "clone":
        await handleCloneRole(interaction, client);
        break;
      case "members":
        await handleRoleMembers(interaction, client);
        break;
    }
  },
};

async function handleCreateRole(interaction, client) {
  // Kiểm tra quyền user
  if (!interaction.member.permissions.has(PermissionFlagsBits.ManageRoles)) {
    const errorEmbed = createErrorEmbed(
      "Bạn không có quyền!",
      "Bạn cần quyền `Manage Roles` để tạo role."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const name = interaction.options.getString("name");
  const colorStr = interaction.options.getString("color");
  const hoist = interaction.options.getBoolean("hoist") || false;
  const mentionable = interaction.options.getBoolean("mentionable") || false;
  const permissionsType =
    interaction.options.getString("permissions") || "none";
  const iconUrl = interaction.options.getString("icon");
  const position = interaction.options.getInteger("position");

  // Validate role name
  if (!isValidRoleName(name)) {
    const errorEmbed = createErrorEmbed(
      "Tên role không hợp lệ!",
      "Tên role không được chứa ký tự đặc biệt và phải từ 1-100 ký tự."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Check if role already exists
  const existingRole = interaction.guild.roles.cache.find(
    (role) => role.name.toLowerCase() === name.toLowerCase()
  );
  if (existingRole) {
    const errorEmbed = createErrorEmbed(
      "Role đã tồn tại!",
      `Role **${name}** đã tồn tại trong server.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Parse color
  let color = null;
  if (colorStr) {
    const colorMatch = colorStr.match(/^#?([0-9a-f]{6})$/i);
    if (colorMatch) {
      color = parseInt(colorMatch[1], 16);
    } else {
      const errorEmbed = createErrorEmbed(
        "Màu không hợp lệ!",
        "Vui lòng sử dụng mã màu hex hợp lệ (ví dụ: #ff0000 hoặc ff0000)."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  }

  // Handle custom permissions
  if (permissionsType === "custom") {
    return await handleCustomPermissions(interaction, client, {
      name,
      color,
      hoist,
      mentionable,
      iconUrl,
      position,
    });
  }

  // Get permissions
  const permissions = getRolePermissions(permissionsType);

  try {
    const roleOptions = {
      name: name,
      color: color,
      hoist: hoist,
      mentionable: mentionable,
      permissions: permissions,
      reason: `Role được tạo bởi ${interaction.user.tag}`,
    };

    // Add icon if provided and server supports it
    if (iconUrl && interaction.guild.premiumTier >= 2) {
      roleOptions.icon = iconUrl;
    }

    const newRole = await interaction.guild.roles.create(roleOptions);

    // Set position if specified
    if (position && position < interaction.member.roles.highest.position) {
      await newRole.setPosition(position);
    }

    const successEmbed = createSuccessEmbed(
      "Đã tạo role thành công!",
      `**Role:** ${newRole}\n` +
        `**Màu:** ${
          color ? `#${color.toString(16).padStart(6, "0")}` : "Mặc định"
        }\n` +
        `**Hiển thị riêng:** ${hoist ? "Có" : "Không"}\n` +
        `**Có thể mention:** ${mentionable ? "Có" : "Không"}\n` +
        `**Quyền hạn:** ${getPermissionDisplayName(permissionsType)}\n` +
        `**Vị trí:** ${newRole.position}`
    );

    if (iconUrl && interaction.guild.premiumTier >= 2) {
      successEmbed.addFields({
        name: "🎨 Icon",
        value: "Role đã được thiết lập icon.",
        inline: true,
      });
    } else if (iconUrl && interaction.guild.premiumTier < 2) {
      successEmbed.addFields({
        name: "⚠️ Lưu ý",
        value: "Server cần boost level 2+ để sử dụng icon role.",
        inline: false,
      });
    }

    successEmbed.addFields({
      name: "📝 Bước tiếp theo",
      value:
        "Bạn có thể:\n" +
        "• Gán role cho thành viên bằng `/assign add`\n" +
        "• Thiết lập quyền chi tiết bằng `/permission setup`\n" +
        "• Chỉnh sửa role bằng `/role edit`",
      inline: false,
    });

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi tạo role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi tạo role!",
      "Không thể tạo role. Vui lòng kiểm tra quyền bot và thử lại!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleDeleteRole(interaction, client) {
  // Kiểm tra quyền user
  if (!interaction.member.permissions.has(PermissionFlagsBits.ManageRoles)) {
    const errorEmbed = createErrorEmbed(
      "Bạn không có quyền!",
      "Bạn cần quyền `Manage Roles` để xóa role."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const role = interaction.options.getRole("role");
  const reason = interaction.options.getString("reason") || "Không có lý do";

  // Check if role can be deleted
  if (role.id === interaction.guild.id) {
    const errorEmbed = createErrorEmbed(
      "Không thể xóa role!",
      "Không thể xóa role @everyone."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  if (role.position >= interaction.member.roles.highest.position) {
    const errorEmbed = createErrorEmbed(
      "Không thể xóa role!",
      "Bạn không thể xóa role có vị trí cao hơn hoặc bằng role cao nhất của bạn."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  if (role.position >= interaction.guild.members.me.roles.highest.position) {
    const errorEmbed = createErrorEmbed(
      "Không thể xóa role!",
      "Bot không thể xóa role có vị trí cao hơn hoặc bằng role cao nhất của bot."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  if (!role.editable) {
    const errorEmbed = createErrorEmbed(
      "Không thể xóa role!",
      "Role này không thể bị xóa."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    const roleName = role.name;
    const memberCount = role.members.size;

    await role.delete(reason);

    const successEmbed = createSuccessEmbed(
      "Đã xóa role thành công!",
      `**Role:** ${roleName}\n` +
        `**Thành viên bị ảnh hưởng:** ${memberCount}\n` +
        `**Lý do:** ${reason}\n` +
        `**Bởi:** ${interaction.user.tag}`
    );

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi xóa role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi xóa role!",
      "Không thể xóa role. Vui lòng kiểm tra quyền bot và thử lại!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleEditRole(interaction, client) {
  // Kiểm tra quyền user
  if (!interaction.member.permissions.has(PermissionFlagsBits.ManageRoles)) {
    const errorEmbed = createErrorEmbed(
      "Bạn không có quyền!",
      "Bạn cần quyền `Manage Roles` để chỉnh sửa role."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const role = interaction.options.getRole("role");
  const newName = interaction.options.getString("name");
  const colorStr = interaction.options.getString("color");
  const hoist = interaction.options.getBoolean("hoist");
  const mentionable = interaction.options.getBoolean("mentionable");

  // Check permissions
  if (role.position >= interaction.member.roles.highest.position) {
    const errorEmbed = createErrorEmbed(
      "Không thể chỉnh sửa role!",
      "Bạn không thể chỉnh sửa role có vị trí cao hơn hoặc bằng role cao nhất của bạn."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  if (!role.editable) {
    const errorEmbed = createErrorEmbed(
      "Không thể chỉnh sửa role!",
      "Role này không thể bị chỉnh sửa."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Parse color
  let color = null;
  if (colorStr) {
    const colorMatch = colorStr.match(/^#?([0-9a-f]{6})$/i);
    if (colorMatch) {
      color = parseInt(colorMatch[1], 16);
    } else {
      const errorEmbed = createErrorEmbed(
        "Màu không hợp lệ!",
        "Vui lòng sử dụng mã màu hex hợp lệ (ví dụ: #ff0000 hoặc ff0000)."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  }

  try {
    const oldData = {
      name: role.name,
      color: role.color,
      hoist: role.hoist,
      mentionable: role.mentionable,
    };

    const updates = {};
    if (newName) updates.name = newName;
    if (color !== null) updates.color = color;
    if (hoist !== null) updates.hoist = hoist;
    if (mentionable !== null) updates.mentionable = mentionable;

    if (Object.keys(updates).length === 0) {
      const errorEmbed = createErrorEmbed(
        "Không có thay đổi!",
        "Vui lòng cung cấp ít nhất một thông tin để chỉnh sửa."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    await role.edit(updates, `Role được chỉnh sửa bởi ${interaction.user.tag}`);

    const successEmbed = createSuccessEmbed(
      "Đã chỉnh sửa role thành công!",
      `**Role:** ${role}\n\n**Thay đổi:**`
    );

    const changes = [];
    if (newName) changes.push(`• **Tên:** ${oldData.name} → ${newName}`);
    if (color !== null)
      changes.push(
        `• **Màu:** #${oldData.color.toString(16).padStart(6, "0")} → #${color
          .toString(16)
          .padStart(6, "0")}`
      );
    if (hoist !== null)
      changes.push(
        `• **Hiển thị riêng:** ${oldData.hoist ? "Có" : "Không"} → ${
          hoist ? "Có" : "Không"
        }`
      );
    if (mentionable !== null)
      changes.push(
        `• **Có thể mention:** ${oldData.mentionable ? "Có" : "Không"} → ${
          mentionable ? "Có" : "Không"
        }`
      );

    successEmbed.setDescription(
      successEmbed.data.description + "\n" + changes.join("\n")
    );

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi chỉnh sửa role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi chỉnh sửa role!",
      "Không thể chỉnh sửa role. Vui lòng kiểm tra quyền bot và thử lại!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

// Utility functions
function isValidRoleName(name) {
  return name && name.length >= 1 && name.length <= 100 && !/[@#:`]/.test(name);
}

function getRolePermissions(type) {
  const permissions = new PermissionsBitField();

  switch (type) {
    case "moderator":
      permissions.add([
        PermissionFlagsBits.KickMembers,
        PermissionFlagsBits.ModerateMembers,
        PermissionFlagsBits.ManageMessages,
        PermissionFlagsBits.ViewAuditLog,
        PermissionFlagsBits.ReadMessageHistory,
      ]);
      break;
    case "admin":
      permissions.add([
        PermissionFlagsBits.KickMembers,
        PermissionFlagsBits.BanMembers,
        PermissionFlagsBits.ModerateMembers,
        PermissionFlagsBits.ManageMessages,
        PermissionFlagsBits.ManageChannels,
        PermissionFlagsBits.ViewAuditLog,
        PermissionFlagsBits.ReadMessageHistory,
      ]);
      break;
    case "manage_channels":
      permissions.add([
        PermissionFlagsBits.ManageChannels,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.SendMessages,
      ]);
      break;
    case "manage_roles":
      permissions.add([
        PermissionFlagsBits.ManageRoles,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.SendMessages,
      ]);
      break;
    case "manage_messages":
      permissions.add([
        PermissionFlagsBits.ManageMessages,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ReadMessageHistory,
      ]);
      break;
    case "none":
    default:
      // No special permissions
      break;
  }

  return permissions;
}

function getPermissionDisplayName(type) {
  const names = {
    none: "Không có quyền đặc biệt",
    moderator: "Moderator cơ bản",
    admin: "Admin cơ bản",
    manage_channels: "Quản lý kênh",
    manage_roles: "Quản lý role",
    manage_messages: "Quản lý tin nhắn",
    custom: "Tùy chỉnh",
  };
  return names[type] || type;
}

async function handleRoleInfo(interaction, client) {
  const role = interaction.options.getRole("role");

  try {
    const embed = createInfoEmbed(
      `🎭 Thông tin Role: ${role.name}`,
      `Chi tiết về role **${role.name}**:`
    );

    embed.setColor(role.color || 0x99aab5);

    embed.addFields(
      { name: "📝 Tên", value: role.name, inline: true },
      { name: "🆔 ID", value: role.id, inline: true },
      { name: "🎨 Màu", value: role.hexColor, inline: true },
      { name: "📊 Vị trí", value: role.position.toString(), inline: true },
      {
        name: "👥 Thành viên",
        value: role.members.size.toString(),
        inline: true,
      },
      {
        name: "📅 Ngày tạo",
        value: `<t:${Math.floor(role.createdTimestamp / 1000)}:F>`,
        inline: true,
      },
      {
        name: "🔗 Hiển thị riêng",
        value: role.hoist ? "Có" : "Không",
        inline: true,
      },
      {
        name: "📢 Có thể mention",
        value: role.mentionable ? "Có" : "Không",
        inline: true,
      },
      {
        name: "🤖 Được quản lý bởi bot",
        value: role.managed ? "Có" : "Không",
        inline: true,
      }
    );

    // Permissions
    if (role.permissions.bitfield > 0n) {
      const perms = role.permissions.toArray();
      const permList = perms
        .slice(0, 10)
        .map((p) => `• ${p.replace(/([A-Z])/g, " $1").trim()}`)
        .join("\n");
      embed.addFields({
        name: `🔐 Quyền hạn (${perms.length})`,
        value:
          permList +
          (perms.length > 10
            ? `\n*... và ${perms.length - 10} quyền khác*`
            : ""),
        inline: false,
      });
    }

    // Icon
    if (role.iconURL()) {
      embed.setThumbnail(role.iconURL());
      embed.addFields({
        name: "🎨 Icon",
        value: "[Xem icon](role.iconURL())",
        inline: true,
      });
    }

    // Tags (for bot roles, boost roles, etc.)
    if (role.tags) {
      const tags = [];
      if (role.tags.botId) tags.push(`Bot: <@${role.tags.botId}>`);
      if (role.tags.integrationId) tags.push("Integration role");
      if (role.tags.premiumSubscriberRole) tags.push("Boost role");

      if (tags.length > 0) {
        embed.addFields({
          name: "🏷️ Tags",
          value: tags.join("\n"),
          inline: false,
        });
      }
    }

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error("Lỗi khi xem thông tin role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi xem thông tin!",
      "Không thể lấy thông tin role. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleCloneRole(interaction, client) {
  // Kiểm tra quyền user
  if (!interaction.member.permissions.has(PermissionFlagsBits.ManageRoles)) {
    const errorEmbed = createErrorEmbed(
      "Bạn không có quyền!",
      "Bạn cần quyền `Manage Roles` để sao chép role."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const sourceRole = interaction.options.getRole("role");
  const newName = interaction.options.getString("name");

  // Check if new name already exists
  const existingRole = interaction.guild.roles.cache.find(
    (role) => role.name.toLowerCase() === newName.toLowerCase()
  );
  if (existingRole) {
    const errorEmbed = createErrorEmbed(
      "Tên role đã tồn tại!",
      `Role **${newName}** đã tồn tại trong server.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    const clonedRole = await interaction.guild.roles.create({
      name: newName,
      color: sourceRole.color,
      hoist: sourceRole.hoist,
      mentionable: sourceRole.mentionable,
      permissions: sourceRole.permissions,
      reason: `Role được sao chép từ ${sourceRole.name} bởi ${interaction.user.tag}`,
    });

    const successEmbed = createSuccessEmbed(
      "Đã sao chép role thành công!",
      `**Role gốc:** ${sourceRole}\n` +
        `**Role mới:** ${clonedRole}\n` +
        `**Màu:** ${sourceRole.hexColor}\n` +
        `**Quyền hạn:** ${sourceRole.permissions.toArray().length} quyền\n` +
        `**Vị trí:** ${clonedRole.position}`
    );

    successEmbed.addFields({
      name: "📝 Lưu ý",
      value:
        "Role mới đã được tạo với tất cả thuộc tính của role gốc. Bạn có thể chỉnh sửa thêm bằng `/role edit`.",
      inline: false,
    });

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi sao chép role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi sao chép role!",
      "Không thể sao chép role. Vui lòng kiểm tra quyền bot và thử lại!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleRoleMembers(interaction, client) {
  const role = interaction.options.getRole("role");
  const page = interaction.options.getInteger("page") || 1;

  const membersPerPage = 20;
  const members = Array.from(role.members.values());
  const totalPages = Math.ceil(members.length / membersPerPage);

  if (page > totalPages && totalPages > 0) {
    const errorEmbed = createErrorEmbed(
      "Trang không tồn tại!",
      `Chỉ có ${totalPages} trang. Vui lòng chọn trang từ 1-${totalPages}.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    const embed = createInfoEmbed(
      `👥 Thành viên có role: ${role.name}`,
      `Danh sách thành viên có role **${role.name}**:`
    );

    embed.setColor(role.color || 0x99aab5);

    if (members.length === 0) {
      embed.setDescription("Không có thành viên nào có role này.");
    } else {
      const startIndex = (page - 1) * membersPerPage;
      const endIndex = startIndex + membersPerPage;
      const pageMembers = members.slice(startIndex, endIndex);

      const memberList = pageMembers
        .map((member, index) => {
          const globalIndex = startIndex + index + 1;
          const joinedDate = Math.floor(member.joinedTimestamp / 1000);
          return `**${globalIndex}.** ${member} - <t:${joinedDate}:R>`;
        })
        .join("\n");

      embed.addFields({
        name: `📋 Danh sách (${pageMembers.length}/${members.length})`,
        value: memberList,
        inline: false,
      });

      embed.addFields(
        {
          name: "📊 Tổng thành viên",
          value: members.length.toString(),
          inline: true,
        },
        {
          name: "📄 Trang hiện tại",
          value: `${page}/${totalPages}`,
          inline: true,
        },
        { name: "🎭 Role", value: role.toString(), inline: true }
      );

      if (totalPages > 1) {
        let navigation = "";
        if (page > 1) {
          navigation += `◀️ Trang trước: \`/role members role:${
            role.name
          } page:${page - 1}\`\n`;
        }
        if (page < totalPages) {
          navigation += `▶️ Trang sau: \`/role members role:${role.name} page:${
            page + 1
          }\``;
        }

        if (navigation) {
          embed.addFields({
            name: "📄 Điều hướng",
            value: navigation,
            inline: false,
          });
        }
      }
    }

    embed.setFooter({
      text: `Trang ${page}/${totalPages || 1} • Yêu cầu bởi ${
        interaction.user.tag
      }`,
      iconURL: interaction.user.displayAvatarURL(),
    });

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error("Lỗi khi xem thành viên role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi xem thành viên!",
      "Không thể lấy danh sách thành viên. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

// Custom permissions handler
async function handleCustomPermissions(interaction, client, roleData) {
  const embed = createWarningEmbed(
    "🔐 Thiết lập quyền tùy chỉnh",
    `Chọn các quyền cho role **${roleData.name}**:\n\n` +
      "**Hướng dẫn:**\n" +
      "• Sử dụng menu dropdown để chọn nhóm quyền\n" +
      "• Có thể chọn nhiều quyền cùng lúc\n" +
      '• Nhấn "Hoàn thành" để tạo role\n' +
      '• Nhấn "Hủy" để quay lại'
  );

  const permissionCategories = getPermissionCategories();
  const selectMenus = [];

  // Tạo select menu cho từng category
  for (let i = 0; i < permissionCategories.length; i += 1) {
    const category = permissionCategories[i];
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(`perm_${category.id}`)
      .setPlaceholder(`Chọn ${category.name}`)
      .setMinValues(0)
      .setMaxValues(category.permissions.length)
      .addOptions(
        category.permissions.map((perm) => ({
          label: perm.name,
          value: perm.flag,
          description: perm.description,
          emoji: perm.emoji,
        }))
      );

    selectMenus.push(new ActionRowBuilder().addComponents(selectMenu));
  }

  // Buttons
  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("complete_role")
      .setLabel("Hoàn thành")
      .setStyle(ButtonStyle.Success)
      .setEmoji("✅"),
    new ButtonBuilder()
      .setCustomId("cancel_role")
      .setLabel("Hủy")
      .setStyle(ButtonStyle.Danger)
      .setEmoji("❌"),
    new ButtonBuilder()
      .setCustomId("preview_role")
      .setLabel("Xem trước")
      .setStyle(ButtonStyle.Secondary)
      .setEmoji("👁️")
  );

  const components = [...selectMenus, buttonRow];

  await interaction.reply({
    embeds: [embed],
    components: components,
    ephemeral: true,
  });

  const selectedPermissions = new Set();
  const collector = interaction.channel.createMessageComponentCollector({
    filter: (i) => i.user.id === interaction.user.id,
    time: 300000, // 5 minutes
  });

  collector.on("collect", async (i) => {
    try {
      if (i.customId.startsWith("perm_")) {
        // Handle permission selection
        const categoryId = i.customId.replace("perm_", "");
        const values = i.values || [];

        // Remove old permissions from this category
        const category = permissionCategories.find(
          (cat) => cat.id === categoryId
        );
        category.permissions.forEach((perm) => {
          selectedPermissions.delete(perm.flag);
        });

        // Add new selected permissions
        values.forEach((value) => {
          selectedPermissions.add(value);
        });

        await i.deferUpdate();
      } else if (i.customId === "preview_role") {
        // Show preview of selected permissions
        const previewEmbed = createInfoEmbed(
          "👁️ Xem trước quyền đã chọn",
          selectedPermissions.size > 0
            ? `**Quyền đã chọn (${selectedPermissions.size}):**\n${Array.from(
                selectedPermissions
              )
                .map((perm) => `• ${formatPermissionName(perm)}`)
                .join("\n")}`
            : "Chưa chọn quyền nào."
        );

        await i.reply({ embeds: [previewEmbed], ephemeral: true });
      } else if (i.customId === "complete_role") {
        // Create role with selected permissions
        await i.deferUpdate();
        collector.stop("completed");

        const permissions = new PermissionsBitField();
        selectedPermissions.forEach((perm) => {
          permissions.add(PermissionFlagsBits[perm]);
        });

        await createRoleWithPermissions(interaction, roleData, permissions);
      } else if (i.customId === "cancel_role") {
        await i.deferUpdate();
        collector.stop("cancelled");
      }
    } catch (error) {
      console.error("Lỗi trong permission collector:", error);
    }
  });

  collector.on("end", async (collected, reason) => {
    try {
      if (reason === "cancelled") {
        const cancelEmbed = createErrorEmbed(
          "Đã hủy tạo role",
          "Quá trình tạo role đã bị hủy."
        );
        await interaction.editReply({ embeds: [cancelEmbed], components: [] });
      } else if (reason === "time") {
        const timeoutEmbed = createErrorEmbed(
          "Hết thời gian",
          "Quá trình thiết lập quyền đã hết thời gian. Vui lòng thử lại."
        );
        await interaction.editReply({ embeds: [timeoutEmbed], components: [] });
      }
    } catch (error) {
      console.error("Lỗi khi kết thúc collector:", error);
    }
  });
}

async function createRoleWithPermissions(interaction, roleData, permissions) {
  try {
    const roleOptions = {
      name: roleData.name,
      color: roleData.color,
      hoist: roleData.hoist,
      mentionable: roleData.mentionable,
      permissions: permissions,
      reason: `Role được tạo bởi ${interaction.user.tag}`,
    };

    // Add icon if provided and server supports it
    if (roleData.iconUrl && interaction.guild.premiumTier >= 2) {
      roleOptions.icon = roleData.iconUrl;
    }

    const newRole = await interaction.guild.roles.create(roleOptions);

    // Set position if specified
    if (
      roleData.position &&
      roleData.position < interaction.member.roles.highest.position
    ) {
      await newRole.setPosition(roleData.position);
    }

    const successEmbed = createSuccessEmbed(
      "Đã tạo role thành công!",
      `**Role:** ${newRole}\n` +
        `**Màu:** ${
          roleData.color
            ? `#${roleData.color.toString(16).padStart(6, "0")}`
            : "Mặc định"
        }\n` +
        `**Hiển thị riêng:** ${roleData.hoist ? "Có" : "Không"}\n` +
        `**Có thể mention:** ${roleData.mentionable ? "Có" : "Không"}\n` +
        `**Quyền hạn:** ${permissions.toArray().length} quyền tùy chỉnh\n` +
        `**Vị trí:** ${newRole.position}`
    );

    if (permissions.toArray().length > 0) {
      const permList = permissions
        .toArray()
        .slice(0, 10)
        .map((perm) => `• ${formatPermissionName(perm)}`)
        .join("\n");
      successEmbed.addFields({
        name: `🔐 Quyền đã thiết lập (${permissions.toArray().length})`,
        value:
          permList +
          (permissions.toArray().length > 10
            ? `\n*... và ${permissions.toArray().length - 10} quyền khác*`
            : ""),
        inline: false,
      });
    }

    successEmbed.addFields({
      name: "📝 Bước tiếp theo",
      value:
        "Bạn có thể:\n" +
        "• Gán role cho thành viên bằng `/assign add`\n" +
        "• Chỉnh sửa quyền bằng `/permission setup`\n" +
        "• Xem thông tin role bằng `/role info`",
      inline: false,
    });

    await interaction.editReply({ embeds: [successEmbed], components: [] });
  } catch (error) {
    console.error("Lỗi khi tạo role với quyền tùy chỉnh:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi tạo role!",
      "Không thể tạo role với quyền tùy chỉnh. Vui lòng thử lại!"
    );
    await interaction.editReply({ embeds: [errorEmbed], components: [] });
  }
}

// Permission categories for custom selection
function getPermissionCategories() {
  return [
    {
      id: "general",
      name: "Quyền chung",
      permissions: [
        {
          flag: "ViewChannel",
          name: "Xem kênh",
          description: "Cho phép xem kênh",
          emoji: "👁️",
        },
        {
          flag: "SendMessages",
          name: "Gửi tin nhắn",
          description: "Cho phép gửi tin nhắn",
          emoji: "💬",
        },
        {
          flag: "SendMessagesInThreads",
          name: "Gửi tin nhắn trong thread",
          description: "Cho phép gửi tin nhắn trong thread",
          emoji: "🧵",
        },
        {
          flag: "CreatePublicThreads",
          name: "Tạo thread công khai",
          description: "Cho phép tạo thread công khai",
          emoji: "🔓",
        },
        {
          flag: "CreatePrivateThreads",
          name: "Tạo thread riêng tư",
          description: "Cho phép tạo thread riêng tư",
          emoji: "🔒",
        },
        {
          flag: "EmbedLinks",
          name: "Nhúng liên kết",
          description: "Cho phép nhúng liên kết",
          emoji: "🔗",
        },
        {
          flag: "AttachFiles",
          name: "Đính kèm file",
          description: "Cho phép đính kèm file",
          emoji: "📎",
        },
        {
          flag: "AddReactions",
          name: "Thêm reaction",
          description: "Cho phép thêm reaction",
          emoji: "😀",
        },
        {
          flag: "UseExternalEmojis",
          name: "Dùng emoji ngoài",
          description: "Cho phép sử dụng emoji từ server khác",
          emoji: "😎",
        },
        {
          flag: "UseExternalStickers",
          name: "Dùng sticker ngoài",
          description: "Cho phép sử dụng sticker từ server khác",
          emoji: "🎭",
        },
        {
          flag: "MentionEveryone",
          name: "Mention @everyone",
          description: "Cho phép mention @everyone và @here",
          emoji: "📢",
        },
        {
          flag: "ReadMessageHistory",
          name: "Đọc lịch sử tin nhắn",
          description: "Cho phép đọc tin nhắn cũ",
          emoji: "📜",
        },
      ],
    },
    {
      id: "voice",
      name: "Quyền voice",
      permissions: [
        {
          flag: "Connect",
          name: "Kết nối voice",
          description: "Cho phép vào kênh voice",
          emoji: "🔊",
        },
        {
          flag: "Speak",
          name: "Nói trong voice",
          description: "Cho phép nói trong kênh voice",
          emoji: "🎤",
        },
        {
          flag: "Stream",
          name: "Stream",
          description: "Cho phép stream màn hình",
          emoji: "📺",
        },
        {
          flag: "UseVAD",
          name: "Voice Activity",
          description: "Cho phép sử dụng Voice Activity Detection",
          emoji: "🎵",
        },
        {
          flag: "PrioritySpeaker",
          name: "Ưu tiên phát biểu",
          description: "Ưu tiên khi nói",
          emoji: "📯",
        },
        {
          flag: "MuteMembers",
          name: "Mute thành viên",
          description: "Cho phép mute thành viên khác",
          emoji: "🔇",
        },
        {
          flag: "DeafenMembers",
          name: "Deafen thành viên",
          description: "Cho phép deafen thành viên khác",
          emoji: "🔈",
        },
        {
          flag: "MoveMembers",
          name: "Di chuyển thành viên",
          description: "Cho phép di chuyển thành viên giữa các kênh voice",
          emoji: "↔️",
        },
      ],
    },
    {
      id: "moderation",
      name: "Quyền moderation",
      permissions: [
        {
          flag: "ManageMessages",
          name: "Quản lý tin nhắn",
          description: "Xóa và chỉnh sửa tin nhắn của người khác",
          emoji: "🗑️",
        },
        {
          flag: "KickMembers",
          name: "Kick thành viên",
          description: "Kick thành viên khỏi server",
          emoji: "👢",
        },
        {
          flag: "BanMembers",
          name: "Ban thành viên",
          description: "Ban thành viên khỏi server",
          emoji: "🔨",
        },
        {
          flag: "ModerateMembers",
          name: "Moderate thành viên",
          description: "Timeout thành viên",
          emoji: "⏰",
        },
        {
          flag: "ManageNicknames",
          name: "Quản lý nickname",
          description: "Thay đổi nickname của thành viên khác",
          emoji: "📝",
        },
        {
          flag: "ViewAuditLog",
          name: "Xem audit log",
          description: "Xem lịch sử hành động trong server",
          emoji: "📋",
        },
      ],
    },
    {
      id: "management",
      name: "Quyền quản lý",
      permissions: [
        {
          flag: "ManageChannels",
          name: "Quản lý kênh",
          description: "Tạo, xóa, chỉnh sửa kênh",
          emoji: "📁",
        },
        {
          flag: "ManageRoles",
          name: "Quản lý role",
          description: "Tạo, xóa, chỉnh sửa role",
          emoji: "🎭",
        },
        {
          flag: "ManageGuild",
          name: "Quản lý server",
          description: "Thay đổi cài đặt server",
          emoji: "⚙️",
        },
        {
          flag: "ManageWebhooks",
          name: "Quản lý webhook",
          description: "Tạo, xóa, chỉnh sửa webhook",
          emoji: "🔗",
        },
        {
          flag: "ManageEmojisAndStickers",
          name: "Quản lý emoji & sticker",
          description: "Thêm, xóa emoji và sticker",
          emoji: "😀",
        },
        {
          flag: "ManageEvents",
          name: "Quản lý sự kiện",
          description: "Tạo và quản lý sự kiện server",
          emoji: "📅",
        },
        {
          flag: "CreateInstantInvite",
          name: "Tạo lời mời",
          description: "Tạo lời mời vào server",
          emoji: "📨",
        },
      ],
    },
    {
      id: "advanced",
      name: "Quyền nâng cao",
      permissions: [
        {
          flag: "Administrator",
          name: "Quản trị viên",
          description: "Toàn quyền trong server",
          emoji: "👑",
        },
        {
          flag: "ManageThreads",
          name: "Quản lý thread",
          description: "Quản lý tất cả thread",
          emoji: "🧵",
        },
        {
          flag: "UseApplicationCommands",
          name: "Dùng slash command",
          description: "Sử dụng slash command của bot",
          emoji: "/",
        },
        {
          flag: "RequestToSpeak",
          name: "Yêu cầu phát biểu",
          description: "Yêu cầu phát biểu trong stage channel",
          emoji: "✋",
        },
        {
          flag: "ManageGuildExpressions",
          name: "Quản lý biểu cảm",
          description: "Quản lý emoji, sticker, soundboard",
          emoji: "🎨",
        },
      ],
    },
  ];
}

function formatPermissionName(permissionFlag) {
  const allPermissions = getPermissionCategories().flatMap(
    (cat) => cat.permissions
  );

  const permission = allPermissions.find((p) => p.flag === permissionFlag);
  return permission
    ? permission.name
    : permissionFlag.replace(/([A-Z])/g, " $1").trim();
}
