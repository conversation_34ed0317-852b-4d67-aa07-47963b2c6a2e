const {
  SlashCommandBuilder,
  PermissionFlagsBits,
  PermissionsBitField,
} = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
} = require("../../utils/embedBuilder.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("role")
    .setDescription("Quản lý role server")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("create")
        .setDescription("Tạo role mới")
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên role")
            .setRequired(true)
            .setMaxLength(100)
        )
        .addStringOption((option) =>
          option
            .setName("color")
            .setDescription("Màu role (hex code, ví dụ: #ff0000)")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("hoist")
            .setDescription("Hiển thị riêng biệt trong danh sách thành viên")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("mentionable")
            .setDescription("Cho phép mention role này")
            .setRequired(false)
        )
        .addStringOption((option) =>
          option
            .setName("permissions")
            .setDescription("Quyền hạn cho role")
            .setRequired(false)
            .addChoices(
              { name: "Không có quyền đặc biệt", value: "none" },
              { name: "Moderator cơ bản", value: "moderator" },
              { name: "Admin cơ bản", value: "admin" },
              { name: "Quản lý kênh", value: "manage_channels" },
              { name: "Quản lý role", value: "manage_roles" },
              { name: "Quản lý tin nhắn", value: "manage_messages" },
              { name: "Tùy chỉnh", value: "custom" }
            )
        )
        .addStringOption((option) =>
          option
            .setName("icon")
            .setDescription("URL icon cho role (chỉ server boost level 2+)")
            .setRequired(false)
        )
        .addIntegerOption((option) =>
          option
            .setName("position")
            .setDescription("Vị trí role (cao hơn = quyền cao hơn)")
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(250)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("delete")
        .setDescription("Xóa role")
        .addRoleOption((option) =>
          option
            .setName("role")
            .setDescription("Role cần xóa")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do xóa role")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("edit")
        .setDescription("Chỉnh sửa role")
        .addRoleOption((option) =>
          option
            .setName("role")
            .setDescription("Role cần chỉnh sửa")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên mới cho role")
            .setRequired(false)
            .setMaxLength(100)
        )
        .addStringOption((option) =>
          option
            .setName("color")
            .setDescription("Màu mới cho role (hex code)")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("hoist")
            .setDescription("Hiển thị riêng biệt")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("mentionable")
            .setDescription("Cho phép mention")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("info")
        .setDescription("Xem thông tin role")
        .addRoleOption((option) =>
          option
            .setName("role")
            .setDescription("Role cần xem thông tin")
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("clone")
        .setDescription("Sao chép role")
        .addRoleOption((option) =>
          option
            .setName("role")
            .setDescription("Role cần sao chép")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên cho role mới")
            .setRequired(true)
            .setMaxLength(100)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("members")
        .setDescription("Xem danh sách thành viên có role")
        .addRoleOption((option) =>
          option
            .setName("role")
            .setDescription("Role cần xem thành viên")
            .setRequired(true)
        )
        .addIntegerOption((option) =>
          option
            .setName("page")
            .setDescription("Trang cần xem (mặc định: 1)")
            .setRequired(false)
            .setMinValue(1)
        )
    ),
  category: "roles",
  adminOnly: false,
  manageServer: false,

  async execute(interaction, client) {
    // Kiểm tra quyền bot
    if (
      !interaction.guild.members.me.permissions.has(
        PermissionFlagsBits.ManageRoles
      )
    ) {
      const errorEmbed = createErrorEmbed(
        "Bot thiếu quyền!",
        "Bot cần quyền `Manage Roles` để quản lý role."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case "create":
        await handleCreateRole(interaction, client);
        break;
      case "delete":
        await handleDeleteRole(interaction, client);
        break;
      case "edit":
        await handleEditRole(interaction, client);
        break;
      case "info":
        await handleRoleInfo(interaction, client);
        break;
      case "clone":
        await handleCloneRole(interaction, client);
        break;
      case "members":
        await handleRoleMembers(interaction, client);
        break;
    }
  },
};

async function handleCreateRole(interaction, client) {
  // Kiểm tra quyền user
  if (!interaction.member.permissions.has(PermissionFlagsBits.ManageRoles)) {
    const errorEmbed = createErrorEmbed(
      "Bạn không có quyền!",
      "Bạn cần quyền `Manage Roles` để tạo role."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const name = interaction.options.getString("name");
  const colorStr = interaction.options.getString("color");
  const hoist = interaction.options.getBoolean("hoist") || false;
  const mentionable = interaction.options.getBoolean("mentionable") || false;
  const permissionsType =
    interaction.options.getString("permissions") || "none";
  const iconUrl = interaction.options.getString("icon");
  const position = interaction.options.getInteger("position");

  // Validate role name
  if (!isValidRoleName(name)) {
    const errorEmbed = createErrorEmbed(
      "Tên role không hợp lệ!",
      "Tên role không được chứa ký tự đặc biệt và phải từ 1-100 ký tự."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Check if role already exists
  const existingRole = interaction.guild.roles.cache.find(
    (role) => role.name.toLowerCase() === name.toLowerCase()
  );
  if (existingRole) {
    const errorEmbed = createErrorEmbed(
      "Role đã tồn tại!",
      `Role **${name}** đã tồn tại trong server.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Parse color
  let color = null;
  if (colorStr) {
    const colorMatch = colorStr.match(/^#?([0-9a-f]{6})$/i);
    if (colorMatch) {
      color = parseInt(colorMatch[1], 16);
    } else {
      const errorEmbed = createErrorEmbed(
        "Màu không hợp lệ!",
        "Vui lòng sử dụng mã màu hex hợp lệ (ví dụ: #ff0000 hoặc ff0000)."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  }

  // Get permissions
  const permissions = getRolePermissions(permissionsType);

  try {
    const roleOptions = {
      name: name,
      color: color,
      hoist: hoist,
      mentionable: mentionable,
      permissions: permissions,
      reason: `Role được tạo bởi ${interaction.user.tag}`,
    };

    // Add icon if provided and server supports it
    if (iconUrl && interaction.guild.premiumTier >= 2) {
      roleOptions.icon = iconUrl;
    }

    const newRole = await interaction.guild.roles.create(roleOptions);

    // Set position if specified
    if (position && position < interaction.member.roles.highest.position) {
      await newRole.setPosition(position);
    }

    const successEmbed = createSuccessEmbed(
      "Đã tạo role thành công!",
      `**Role:** ${newRole}\n` +
        `**Màu:** ${
          color ? `#${color.toString(16).padStart(6, "0")}` : "Mặc định"
        }\n` +
        `**Hiển thị riêng:** ${hoist ? "Có" : "Không"}\n` +
        `**Có thể mention:** ${mentionable ? "Có" : "Không"}\n` +
        `**Quyền hạn:** ${getPermissionDisplayName(permissionsType)}\n` +
        `**Vị trí:** ${newRole.position}`
    );

    if (iconUrl && interaction.guild.premiumTier >= 2) {
      successEmbed.addFields({
        name: "🎨 Icon",
        value: "Role đã được thiết lập icon.",
        inline: true,
      });
    } else if (iconUrl && interaction.guild.premiumTier < 2) {
      successEmbed.addFields({
        name: "⚠️ Lưu ý",
        value: "Server cần boost level 2+ để sử dụng icon role.",
        inline: false,
      });
    }

    successEmbed.addFields({
      name: "📝 Bước tiếp theo",
      value:
        "Bạn có thể:\n" +
        "• Gán role cho thành viên bằng `/assign add`\n" +
        "• Thiết lập quyền chi tiết bằng `/permission setup`\n" +
        "• Chỉnh sửa role bằng `/role edit`",
      inline: false,
    });

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi tạo role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi tạo role!",
      "Không thể tạo role. Vui lòng kiểm tra quyền bot và thử lại!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleDeleteRole(interaction, client) {
  // Kiểm tra quyền user
  if (!interaction.member.permissions.has(PermissionFlagsBits.ManageRoles)) {
    const errorEmbed = createErrorEmbed(
      "Bạn không có quyền!",
      "Bạn cần quyền `Manage Roles` để xóa role."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const role = interaction.options.getRole("role");
  const reason = interaction.options.getString("reason") || "Không có lý do";

  // Check if role can be deleted
  if (role.id === interaction.guild.id) {
    const errorEmbed = createErrorEmbed(
      "Không thể xóa role!",
      "Không thể xóa role @everyone."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  if (role.position >= interaction.member.roles.highest.position) {
    const errorEmbed = createErrorEmbed(
      "Không thể xóa role!",
      "Bạn không thể xóa role có vị trí cao hơn hoặc bằng role cao nhất của bạn."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  if (role.position >= interaction.guild.members.me.roles.highest.position) {
    const errorEmbed = createErrorEmbed(
      "Không thể xóa role!",
      "Bot không thể xóa role có vị trí cao hơn hoặc bằng role cao nhất của bot."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  if (!role.editable) {
    const errorEmbed = createErrorEmbed(
      "Không thể xóa role!",
      "Role này không thể bị xóa."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    const roleName = role.name;
    const memberCount = role.members.size;

    await role.delete(reason);

    const successEmbed = createSuccessEmbed(
      "Đã xóa role thành công!",
      `**Role:** ${roleName}\n` +
        `**Thành viên bị ảnh hưởng:** ${memberCount}\n` +
        `**Lý do:** ${reason}\n` +
        `**Bởi:** ${interaction.user.tag}`
    );

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi xóa role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi xóa role!",
      "Không thể xóa role. Vui lòng kiểm tra quyền bot và thử lại!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleEditRole(interaction, client) {
  // Kiểm tra quyền user
  if (!interaction.member.permissions.has(PermissionFlagsBits.ManageRoles)) {
    const errorEmbed = createErrorEmbed(
      "Bạn không có quyền!",
      "Bạn cần quyền `Manage Roles` để chỉnh sửa role."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const role = interaction.options.getRole("role");
  const newName = interaction.options.getString("name");
  const colorStr = interaction.options.getString("color");
  const hoist = interaction.options.getBoolean("hoist");
  const mentionable = interaction.options.getBoolean("mentionable");

  // Check permissions
  if (role.position >= interaction.member.roles.highest.position) {
    const errorEmbed = createErrorEmbed(
      "Không thể chỉnh sửa role!",
      "Bạn không thể chỉnh sửa role có vị trí cao hơn hoặc bằng role cao nhất của bạn."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  if (!role.editable) {
    const errorEmbed = createErrorEmbed(
      "Không thể chỉnh sửa role!",
      "Role này không thể bị chỉnh sửa."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Parse color
  let color = null;
  if (colorStr) {
    const colorMatch = colorStr.match(/^#?([0-9a-f]{6})$/i);
    if (colorMatch) {
      color = parseInt(colorMatch[1], 16);
    } else {
      const errorEmbed = createErrorEmbed(
        "Màu không hợp lệ!",
        "Vui lòng sử dụng mã màu hex hợp lệ (ví dụ: #ff0000 hoặc ff0000)."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  }

  try {
    const oldData = {
      name: role.name,
      color: role.color,
      hoist: role.hoist,
      mentionable: role.mentionable,
    };

    const updates = {};
    if (newName) updates.name = newName;
    if (color !== null) updates.color = color;
    if (hoist !== null) updates.hoist = hoist;
    if (mentionable !== null) updates.mentionable = mentionable;

    if (Object.keys(updates).length === 0) {
      const errorEmbed = createErrorEmbed(
        "Không có thay đổi!",
        "Vui lòng cung cấp ít nhất một thông tin để chỉnh sửa."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    await role.edit(updates, `Role được chỉnh sửa bởi ${interaction.user.tag}`);

    const successEmbed = createSuccessEmbed(
      "Đã chỉnh sửa role thành công!",
      `**Role:** ${role}\n\n**Thay đổi:**`
    );

    const changes = [];
    if (newName) changes.push(`• **Tên:** ${oldData.name} → ${newName}`);
    if (color !== null)
      changes.push(
        `• **Màu:** #${oldData.color.toString(16).padStart(6, "0")} → #${color
          .toString(16)
          .padStart(6, "0")}`
      );
    if (hoist !== null)
      changes.push(
        `• **Hiển thị riêng:** ${oldData.hoist ? "Có" : "Không"} → ${
          hoist ? "Có" : "Không"
        }`
      );
    if (mentionable !== null)
      changes.push(
        `• **Có thể mention:** ${oldData.mentionable ? "Có" : "Không"} → ${
          mentionable ? "Có" : "Không"
        }`
      );

    successEmbed.setDescription(
      successEmbed.data.description + "\n" + changes.join("\n")
    );

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi chỉnh sửa role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi chỉnh sửa role!",
      "Không thể chỉnh sửa role. Vui lòng kiểm tra quyền bot và thử lại!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

// Utility functions
function isValidRoleName(name) {
  return name && name.length >= 1 && name.length <= 100 && !/[@#:`]/.test(name);
}

function getRolePermissions(type) {
  const permissions = new PermissionsBitField();

  switch (type) {
    case "moderator":
      permissions.add([
        PermissionFlagsBits.KickMembers,
        PermissionFlagsBits.ModerateMembers,
        PermissionFlagsBits.ManageMessages,
        PermissionFlagsBits.ViewAuditLog,
        PermissionFlagsBits.ReadMessageHistory,
      ]);
      break;
    case "admin":
      permissions.add([
        PermissionFlagsBits.KickMembers,
        PermissionFlagsBits.BanMembers,
        PermissionFlagsBits.ModerateMembers,
        PermissionFlagsBits.ManageMessages,
        PermissionFlagsBits.ManageChannels,
        PermissionFlagsBits.ViewAuditLog,
        PermissionFlagsBits.ReadMessageHistory,
      ]);
      break;
    case "manage_channels":
      permissions.add([
        PermissionFlagsBits.ManageChannels,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.SendMessages,
      ]);
      break;
    case "manage_roles":
      permissions.add([
        PermissionFlagsBits.ManageRoles,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.SendMessages,
      ]);
      break;
    case "manage_messages":
      permissions.add([
        PermissionFlagsBits.ManageMessages,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.ReadMessageHistory,
      ]);
      break;
    case "none":
    default:
      // No special permissions
      break;
  }

  return permissions;
}

function getPermissionDisplayName(type) {
  const names = {
    none: "Không có quyền đặc biệt",
    moderator: "Moderator cơ bản",
    admin: "Admin cơ bản",
    manage_channels: "Quản lý kênh",
    manage_roles: "Quản lý role",
    manage_messages: "Quản lý tin nhắn",
    custom: "Tùy chỉnh",
  };
  return names[type] || type;
}

async function handleRoleInfo(interaction, client) {
  const role = interaction.options.getRole("role");

  try {
    const embed = createInfoEmbed(
      `🎭 Thông tin Role: ${role.name}`,
      `Chi tiết về role **${role.name}**:`
    );

    embed.setColor(role.color || 0x99aab5);

    embed.addFields(
      { name: "📝 Tên", value: role.name, inline: true },
      { name: "🆔 ID", value: role.id, inline: true },
      { name: "🎨 Màu", value: role.hexColor, inline: true },
      { name: "📊 Vị trí", value: role.position.toString(), inline: true },
      {
        name: "👥 Thành viên",
        value: role.members.size.toString(),
        inline: true,
      },
      {
        name: "📅 Ngày tạo",
        value: `<t:${Math.floor(role.createdTimestamp / 1000)}:F>`,
        inline: true,
      },
      {
        name: "🔗 Hiển thị riêng",
        value: role.hoist ? "Có" : "Không",
        inline: true,
      },
      {
        name: "📢 Có thể mention",
        value: role.mentionable ? "Có" : "Không",
        inline: true,
      },
      {
        name: "🤖 Được quản lý bởi bot",
        value: role.managed ? "Có" : "Không",
        inline: true,
      }
    );

    // Permissions
    if (role.permissions.bitfield > 0n) {
      const perms = role.permissions.toArray();
      const permList = perms
        .slice(0, 10)
        .map((p) => `• ${p.replace(/([A-Z])/g, " $1").trim()}`)
        .join("\n");
      embed.addFields({
        name: `🔐 Quyền hạn (${perms.length})`,
        value:
          permList +
          (perms.length > 10
            ? `\n*... và ${perms.length - 10} quyền khác*`
            : ""),
        inline: false,
      });
    }

    // Icon
    if (role.iconURL()) {
      embed.setThumbnail(role.iconURL());
      embed.addFields({
        name: "🎨 Icon",
        value: "[Xem icon](role.iconURL())",
        inline: true,
      });
    }

    // Tags (for bot roles, boost roles, etc.)
    if (role.tags) {
      const tags = [];
      if (role.tags.botId) tags.push(`Bot: <@${role.tags.botId}>`);
      if (role.tags.integrationId) tags.push("Integration role");
      if (role.tags.premiumSubscriberRole) tags.push("Boost role");

      if (tags.length > 0) {
        embed.addFields({
          name: "🏷️ Tags",
          value: tags.join("\n"),
          inline: false,
        });
      }
    }

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error("Lỗi khi xem thông tin role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi xem thông tin!",
      "Không thể lấy thông tin role. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleCloneRole(interaction, client) {
  // Kiểm tra quyền user
  if (!interaction.member.permissions.has(PermissionFlagsBits.ManageRoles)) {
    const errorEmbed = createErrorEmbed(
      "Bạn không có quyền!",
      "Bạn cần quyền `Manage Roles` để sao chép role."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const sourceRole = interaction.options.getRole("role");
  const newName = interaction.options.getString("name");

  // Check if new name already exists
  const existingRole = interaction.guild.roles.cache.find(
    (role) => role.name.toLowerCase() === newName.toLowerCase()
  );
  if (existingRole) {
    const errorEmbed = createErrorEmbed(
      "Tên role đã tồn tại!",
      `Role **${newName}** đã tồn tại trong server.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    const clonedRole = await interaction.guild.roles.create({
      name: newName,
      color: sourceRole.color,
      hoist: sourceRole.hoist,
      mentionable: sourceRole.mentionable,
      permissions: sourceRole.permissions,
      reason: `Role được sao chép từ ${sourceRole.name} bởi ${interaction.user.tag}`,
    });

    const successEmbed = createSuccessEmbed(
      "Đã sao chép role thành công!",
      `**Role gốc:** ${sourceRole}\n` +
        `**Role mới:** ${clonedRole}\n` +
        `**Màu:** ${sourceRole.hexColor}\n` +
        `**Quyền hạn:** ${sourceRole.permissions.toArray().length} quyền\n` +
        `**Vị trí:** ${clonedRole.position}`
    );

    successEmbed.addFields({
      name: "📝 Lưu ý",
      value:
        "Role mới đã được tạo với tất cả thuộc tính của role gốc. Bạn có thể chỉnh sửa thêm bằng `/role edit`.",
      inline: false,
    });

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi sao chép role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi sao chép role!",
      "Không thể sao chép role. Vui lòng kiểm tra quyền bot và thử lại!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleRoleMembers(interaction, client) {
  const role = interaction.options.getRole("role");
  const page = interaction.options.getInteger("page") || 1;

  const membersPerPage = 20;
  const members = Array.from(role.members.values());
  const totalPages = Math.ceil(members.length / membersPerPage);

  if (page > totalPages && totalPages > 0) {
    const errorEmbed = createErrorEmbed(
      "Trang không tồn tại!",
      `Chỉ có ${totalPages} trang. Vui lòng chọn trang từ 1-${totalPages}.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    const embed = createInfoEmbed(
      `👥 Thành viên có role: ${role.name}`,
      `Danh sách thành viên có role **${role.name}**:`
    );

    embed.setColor(role.color || 0x99aab5);

    if (members.length === 0) {
      embed.setDescription("Không có thành viên nào có role này.");
    } else {
      const startIndex = (page - 1) * membersPerPage;
      const endIndex = startIndex + membersPerPage;
      const pageMembers = members.slice(startIndex, endIndex);

      const memberList = pageMembers
        .map((member, index) => {
          const globalIndex = startIndex + index + 1;
          const joinedDate = Math.floor(member.joinedTimestamp / 1000);
          return `**${globalIndex}.** ${member} - <t:${joinedDate}:R>`;
        })
        .join("\n");

      embed.addFields({
        name: `📋 Danh sách (${pageMembers.length}/${members.length})`,
        value: memberList,
        inline: false,
      });

      embed.addFields(
        {
          name: "📊 Tổng thành viên",
          value: members.length.toString(),
          inline: true,
        },
        {
          name: "📄 Trang hiện tại",
          value: `${page}/${totalPages}`,
          inline: true,
        },
        { name: "🎭 Role", value: role.toString(), inline: true }
      );

      if (totalPages > 1) {
        let navigation = "";
        if (page > 1) {
          navigation += `◀️ Trang trước: \`/role members role:${
            role.name
          } page:${page - 1}\`\n`;
        }
        if (page < totalPages) {
          navigation += `▶️ Trang sau: \`/role members role:${role.name} page:${
            page + 1
          }\``;
        }

        if (navigation) {
          embed.addFields({
            name: "📄 Điều hướng",
            value: navigation,
            inline: false,
          });
        }
      }
    }

    embed.setFooter({
      text: `Trang ${page}/${totalPages || 1} • Yêu cầu bởi ${
        interaction.user.tag
      }`,
      iconURL: interaction.user.displayAvatarURL(),
    });

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error("Lỗi khi xem thành viên role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi xem thành viên!",
      "Không thể lấy danh sách thành viên. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}
