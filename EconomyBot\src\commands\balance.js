const { SlashCommandBuilder, AttachmentBuilder } = require("discord.js");
const User = require("../models/User");
const {
  createProfileEmbed,
  createErrorEmbed,
  createInfoEmbed,
} = require("../utils/embedBuilder");
const { createProfileCard } = require("../utils/canvasBuilder");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("balance")
    .setDescription("Kiểm tra số dư tài khoản của bạn hoặc người khác")
    .addUserOption((option) =>
      option
        .setName("user")
        .setDescription("Người dùng cần kiểm tra số dư")
        .setRequired(false)
    )
    .addBooleanOption((option) =>
      option
        .setName("card")
        .setDescription("Hiển thị dưới dạng card đẹp (mặc định: false)")
        .setRequired(false)
    ),

  cooldown: 3,

  async execute(interaction) {
    try {
      const targetUser =
        interaction.options.getUser("user") || interaction.user;
      const useCard = interaction.options.getBoolean("card") || false;

      // Tạo hoặc lấy thông tin user
      const userData = await User.findOrCreate(targetUser);

      if (useCard) {
        try {
          // Tạo profile card với canvas
          const cardBuffer = await createProfileCard(targetUser, userData);
          const attachment = new AttachmentBuilder(cardBuffer, {
            name: "profile-card.png",
          });

          const cardEmbed = createInfoEmbed(
            "🎨 Profile Card",
            `Hồ sơ của **${targetUser.username}**`
          );

          await interaction.reply({
            embeds: [cardEmbed],
            files: [attachment],
          });
        } catch (canvasError) {
          console.error("Lỗi tạo canvas card:", canvasError);

          // Fallback to normal embed
          const profileEmbed = createProfileEmbed(targetUser, userData);
          await interaction.reply({ embeds: [profileEmbed] });
        }
      } else {
        // Tạo embed profile thông thường
        const profileEmbed = createProfileEmbed(targetUser, userData);
        await interaction.reply({ embeds: [profileEmbed] });
      }
    } catch (error) {
      console.error("Lỗi lệnh balance:", error);

      const errorEmbed = createErrorEmbed(
        "Lỗi",
        "Không thể lấy thông tin số dư. Vui lòng thử lại sau."
      );

      await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  },
};
