const { SlashCommandBuilder } = require("discord.js");
const User = require("../models/User");
const {
  createSuccessEmbed,
  createErrorEmbed,
  formatCurrency,
} = require("../utils/embedBuilder");
const config = require("../config/config");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("crime")
    .setNameLocalizations({
      vi: "phamphap",
    })
    .setDescription("Thực hiện hành vi phạm pháp để kiếm tiền (có rủi ro)"),

  cooldown: 3600, // 1 giờ

  async execute(interaction) {
    try {
      const userData = await User.findOrCreate(interaction.user);

      // Kiểm tra cooldown
      if (userData.lastCrime) {
        const lastCrime = new Date(userData.lastCrime);
        const now = new Date();
        const timeDiff = now - lastCrime;
        const cooldownTime = 3600000; // 1 giờ

        if (timeDiff < cooldownTime) {
          const timeLeft = Math.ceil((cooldownTime - timeDiff) / 1000 / 60);
          const errorEmbed = createErrorEmbed(
            "Đang trốn cảnh sát!",
            `Bạn cần đợi **${timeLeft} phút** nữa trước khi có thể phạm pháp lại.\n\n` +
              `${config.emojis.warning} **Lưu ý:** Hành vi phạm pháp có thể bị phạt tiền!`
          );
          return await interaction.reply({
            embeds: [errorEmbed],
            ephemeral: true,
          });
        }
      }

      // Các loại tội phạm
      const crimes = [
        {
          name: "Ăn cắp kẹo",
          successRate: 85,
          reward: { min: 50, max: 200 },
          fine: { min: 100, max: 300 },
          emoji: "🍭",
        },
        {
          name: "Nhặt ví trên đường",
          successRate: 70,
          reward: { min: 200, max: 800 },
          fine: { min: 300, max: 600 },
          emoji: "👛",
        },
        {
          name: "Hack ATM",
          successRate: 40,
          reward: { min: 1000, max: 5000 },
          fine: { min: 2000, max: 8000 },
          emoji: "🏧",
        },
        {
          name: "Cướp ngân hàng",
          successRate: 20,
          reward: { min: 5000, max: 20000 },
          fine: { min: 10000, max: 30000 },
          emoji: "🏦",
        },
        {
          name: "Buôn lậu kim cương",
          successRate: 10,
          reward: { min: 20000, max: 100000 },
          fine: { min: 50000, max: 150000 },
          emoji: "💎",
        },
      ];

      // Chọn tội phạm ngẫu nhiên dựa trên level
      const availableCrimes = crimes.filter((crime) => {
        if (crime.name === "Ăn cắp kẹo") return userData.level >= 1;
        if (crime.name === "Nhặt ví trên đường") return userData.level >= 5;
        if (crime.name === "Hack ATM") return userData.level >= 15;
        if (crime.name === "Cướp ngân hàng") return userData.level >= 25;
        if (crime.name === "Buôn lậu kim cương") return userData.level >= 40;
        return true;
      });

      const selectedCrime =
        availableCrimes[Math.floor(Math.random() * availableCrimes.length)];
      const success = Math.random() * 100 < selectedCrime.successRate;

      // Cập nhật thời gian crime
      const db = require("../database/database");
      await db.run(
        "UPDATE users SET last_crime = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
        [new Date().toISOString(), userData.id]
      );

      if (success) {
        // Thành công
        const reward = Math.floor(
          Math.random() *
            (selectedCrime.reward.max - selectedCrime.reward.min + 1) +
            selectedCrime.reward.min
        );

        await userData.updateBalance(reward, "crime");
        await userData.addExperience(Math.floor(reward / 10));

        const successEmbed = createSuccessEmbed(
          "Phạm pháp thành công!",
          `${selectedCrime.emoji} **${selectedCrime.name}**\n\n` +
            `Bạn đã kiếm được **${formatCurrency(
              reward
            )}** mà không bị phát hiện!\n\n` +
            `${config.emojis.money} **Số dư hiện tại:** ${formatCurrency(
              userData.balance + reward
            )}\n` +
            `${config.emojis.star} **Kinh nghiệm:** +${Math.floor(
              reward / 10
            )} EXP`
        );

        successEmbed.addFields({
          name: `${config.emojis.warning} Cảnh báo`,
          value:
            "Hành vi phạm pháp chỉ là trò chơi. Trong thực tế, hãy luôn tuân thủ pháp luật!",
          inline: false,
        });

        await interaction.reply({ embeds: [successEmbed] });
      } else {
        // Thất bại - bị bắt
        const fine = Math.floor(
          Math.random() *
            (selectedCrime.fine.max - selectedCrime.fine.min + 1) +
            selectedCrime.fine.min
        );

        // Không thể bị phạt quá số dư hiện có
        const actualFine = Math.min(fine, userData.balance);

        if (actualFine > 0) {
          await userData.updateBalance(-actualFine, "crime_fine");
        }

        const failEmbed = createErrorEmbed(
          "Bị bắt rồi!",
          `${selectedCrime.emoji} **${selectedCrime.name}**\n\n` +
            `Bạn đã bị cảnh sát bắt và phạt **${formatCurrency(
              actualFine
            )}**!\n\n` +
            `${config.emojis.money} **Số dư còn lại:** ${formatCurrency(
              userData.balance - actualFine
            )}\n` +
            `${config.emojis.info} **Bài học:** Tội phạm không bao giờ được đền đáp!`
        );

        failEmbed.addFields({
          name: `${config.emojis.warning} Lời khuyên`,
          value:
            "Hãy thử `/work` để kiếm tiền một cách hợp pháp và an toàn hơn!",
          inline: false,
        });

        await interaction.reply({ embeds: [failEmbed] });
      }

      // Kiểm tra achievements
      const newAchievements = await userData.checkAchievements();
      if (newAchievements.length > 0) {
        // Thông báo achievement mới (có thể thêm sau)
      }
    } catch (error) {
      console.error("Lỗi lệnh crime:", error);

      const errorEmbed = createErrorEmbed(
        "Lỗi hệ thống",
        "Không thể thực hiện hành vi phạm pháp. Có lẽ đây là dấu hiệu tốt!"
      );

      await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  },
};
