const {
  <PERSON><PERSON><PERSON>ommandBuilder,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  StringSelectMenuBuilder,
  PermissionFlagsBits,
} = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
} = require("../../utils/embedBuilder.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("invitebot")
    .setDescription("Tạo link mời bot với quyền tùy chỉnh")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("create")
        .setDescription("Tạo link mời bot")
        .addUserOption((option) =>
          option
            .setName("bot")
            .setDescription("Bot cần tạo link mời")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("preset")
            .setDescription("Preset quyền có sẵn")
            .setRequired(false)
            .addChoices(
              { name: "🔨 Moderation - Kiểm duyệt", value: "moderation" },
              { name: "🎵 Music - Nhạc", value: "music" },
              { name: "🤖 Basic - Cơ bản", value: "basic" },
              { name: "👑 Admin - Quản trị", value: "admin" },
              { name: "🎮 Gaming - Game", value: "gaming" },
              { name: "📊 Analytics - Thống kê", value: "analytics" },
              { name: "🎨 Fun - Giải trí", value: "fun" },
              { name: "🔧 Custom - Tùy chỉnh", value: "custom" }
            )
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("permissions")
        .setDescription("Xem danh sách quyền Discord")
        .addStringOption((option) =>
          option
            .setName("category")
            .setDescription("Danh mục quyền")
            .setRequired(false)
            .addChoices(
              { name: "General - Chung", value: "general" },
              { name: "Text - Tin nhắn", value: "text" },
              { name: "Voice - Âm thanh", value: "voice" },
              { name: "Advanced - Nâng cao", value: "advanced" }
            )
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("analyze")
        .setDescription("Phân tích quyền của bot hiện tại")
        .addUserOption((option) =>
          option
            .setName("bot")
            .setDescription("Bot cần phân tích")
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("compare")
        .setDescription("So sánh quyền giữa các bot")
        .addUserOption((option) =>
          option
            .setName("bot1")
            .setDescription("Bot thứ nhất")
            .setRequired(true)
        )
        .addUserOption((option) =>
          option
            .setName("bot2")
            .setDescription("Bot thứ hai")
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("security")
        .setDescription("Kiểm tra bảo mật quyền bot")
        .addUserOption((option) =>
          option
            .setName("bot")
            .setDescription("Bot cần kiểm tra")
            .setRequired(true)
        )
    ),
  category: "utility",
  adminOnly: false,
  manageServer: false,

  async execute(interaction, client) {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case "create":
        await handleCreateInvite(interaction, client);
        break;
      case "permissions":
        await handlePermissionsList(interaction, client);
        break;
      case "analyze":
        await handleAnalyzeBot(interaction, client);
        break;
      case "compare":
        await handleCompareBots(interaction, client);
        break;
      case "security":
        await handleSecurityCheck(interaction, client);
        break;
    }
  },
};

async function handleCreateInvite(interaction, client) {
  const bot = interaction.options.getUser("bot");
  const preset = interaction.options.getString("preset") || "basic";

  if (!bot.bot) {
    const errorEmbed = createErrorEmbed(
      "Không phải bot!",
      "User bạn chọn không phải là bot. Vui lòng chọn một bot."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  await interaction.deferReply();

  try {
    const permissions = getPresetPermissions(preset);
    const inviteUrl = generateInviteUrl(bot.id, permissions);

    const embed = createSuccessEmbed(
      `🔗 Link mời bot: ${bot.username}`,
      `**Preset:** ${getPresetDisplayName(preset)}\n` +
      `**Bot ID:** \`${bot.id}\`\n` +
      `**Quyền:** ${permissions.length} quyền được cấp`
    );

    embed.addFields({
      name: "📋 Danh sách quyền",
      value: permissions.slice(0, 15).map(perm => `• ${getPermissionDisplayName(perm)}`).join('\n') +
             (permissions.length > 15 ? `\n*... và ${permissions.length - 15} quyền khác*` : ''),
      inline: false
    });

    embed.addFields({
      name: "🔗 Link mời",
      value: `[**Nhấn để mời bot**](${inviteUrl})`,
      inline: false
    });

    const components = createInviteComponents(bot.id, preset);
    
    await interaction.editReply({ embeds: [embed], components });

  } catch (error) {
    console.error("Lỗi khi tạo link mời:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi tạo link!",
      "Không thể tạo link mời bot. Vui lòng thử lại sau!"
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handlePermissionsList(interaction, client) {
  const category = interaction.options.getString("category") || "general";

  const permissions = getPermissionsByCategory(category);
  
  const embed = createInfoEmbed(
    `📋 Quyền Discord - ${getCategoryDisplayName(category)}`,
    `Danh sách các quyền trong danh mục **${getCategoryDisplayName(category)}**`
  );

  permissions.forEach(permGroup => {
    embed.addFields({
      name: permGroup.name,
      value: permGroup.permissions.map(perm => 
        `• **${perm.name}**\n  └ ${perm.description}`
      ).join('\n'),
      inline: false
    });
  });

  const components = createPermissionComponents(category);
  
  await interaction.reply({ embeds: [embed], components, ephemeral: true });
}

async function handleAnalyzeBot(interaction, client) {
  const bot = interaction.options.getUser("bot");

  if (!bot.bot) {
    const errorEmbed = createErrorEmbed(
      "Không phải bot!",
      "User bạn chọn không phải là bot."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  await interaction.deferReply();

  try {
    const member = await interaction.guild.members.fetch(bot.id);
    const permissions = member.permissions.toArray();
    const roles = member.roles.cache.filter(role => role.id !== interaction.guild.id);

    const embed = createInfoEmbed(
      `🔍 Phân tích bot: ${bot.username}`,
      `**Bot ID:** \`${bot.id}\`\n` +
      `**Joined:** ${member.joinedAt.toLocaleDateString('vi-VN')}\n` +
      `**Roles:** ${roles.size} roles\n` +
      `**Permissions:** ${permissions.length} quyền`
    );

    // Categorize permissions
    const categorizedPerms = categorizePermissions(permissions);
    
    Object.entries(categorizedPerms).forEach(([category, perms]) => {
      if (perms.length > 0) {
        embed.addFields({
          name: `${getCategoryIcon(category)} ${getCategoryDisplayName(category)}`,
          value: perms.slice(0, 8).map(perm => `• ${getPermissionDisplayName(perm)}`).join('\n') +
                 (perms.length > 8 ? `\n*... và ${perms.length - 8} quyền khác*` : ''),
          inline: true
        });
      }
    });

    // Security analysis
    const securityRisk = analyzeSecurityRisk(permissions);
    embed.addFields({
      name: "🛡️ Đánh giá bảo mật",
      value: `**Mức độ rủi ro:** ${securityRisk.level}\n` +
             `**Điểm số:** ${securityRisk.score}/100\n` +
             `**Quyền nguy hiểm:** ${securityRisk.dangerousPerms.length}`,
      inline: false
    });

    if (securityRisk.recommendations.length > 0) {
      embed.addFields({
        name: "💡 Khuyến nghị",
        value: securityRisk.recommendations.slice(0, 3).map(rec => `• ${rec}`).join('\n'),
        inline: false
      });
    }

    const components = createAnalysisComponents(bot.id);
    
    await interaction.editReply({ embeds: [embed], components });

  } catch (error) {
    console.error("Lỗi khi phân tích bot:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi phân tích!",
      "Không thể phân tích bot. Bot có thể không có trong server này."
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

function getPresetPermissions(preset) {
  const presets = {
    basic: [
      PermissionFlagsBits.ViewChannel,
      PermissionFlagsBits.SendMessages,
      PermissionFlagsBits.ReadMessageHistory,
      PermissionFlagsBits.UseExternalEmojis,
      PermissionFlagsBits.AddReactions
    ],
    moderation: [
      PermissionFlagsBits.ViewChannel,
      PermissionFlagsBits.SendMessages,
      PermissionFlagsBits.ManageMessages,
      PermissionFlagsBits.KickMembers,
      PermissionFlagsBits.BanMembers,
      PermissionFlagsBits.ModerateMembers,
      PermissionFlagsBits.ManageNicknames,
      PermissionFlagsBits.ViewAuditLog,
      PermissionFlagsBits.ReadMessageHistory
    ],
    music: [
      PermissionFlagsBits.ViewChannel,
      PermissionFlagsBits.SendMessages,
      PermissionFlagsBits.Connect,
      PermissionFlagsBits.Speak,
      PermissionFlagsBits.UseVAD,
      PermissionFlagsBits.ReadMessageHistory,
      PermissionFlagsBits.AddReactions,
      PermissionFlagsBits.UseExternalEmojis
    ],
    admin: [
      PermissionFlagsBits.Administrator
    ],
    gaming: [
      PermissionFlagsBits.ViewChannel,
      PermissionFlagsBits.SendMessages,
      PermissionFlagsBits.Connect,
      PermissionFlagsBits.Speak,
      PermissionFlagsBits.ManageMessages,
      PermissionFlagsBits.ManageNicknames,
      PermissionFlagsBits.ReadMessageHistory,
      PermissionFlagsBits.AddReactions,
      PermissionFlagsBits.UseExternalEmojis,
      PermissionFlagsBits.UseApplicationCommands
    ],
    analytics: [
      PermissionFlagsBits.ViewChannel,
      PermissionFlagsBits.SendMessages,
      PermissionFlagsBits.ReadMessageHistory,
      PermissionFlagsBits.ViewAuditLog,
      PermissionFlagsBits.ManageWebhooks,
      PermissionFlagsBits.UseExternalEmojis,
      PermissionFlagsBits.EmbedLinks
    ],
    fun: [
      PermissionFlagsBits.ViewChannel,
      PermissionFlagsBits.SendMessages,
      PermissionFlagsBits.ReadMessageHistory,
      PermissionFlagsBits.AddReactions,
      PermissionFlagsBits.UseExternalEmojis,
      PermissionFlagsBits.AttachFiles,
      PermissionFlagsBits.EmbedLinks,
      PermissionFlagsBits.UseApplicationCommands
    ]
  };

  return presets[preset] || presets.basic;
}

function generateInviteUrl(botId, permissions) {
  const permissionValue = permissions.reduce((acc, perm) => acc | perm, 0n);
  return `https://discord.com/api/oauth2/authorize?client_id=${botId}&permissions=${permissionValue}&scope=bot%20applications.commands`;
}

function getPresetDisplayName(preset) {
  const presets = {
    basic: "🤖 Cơ bản",
    moderation: "🔨 Kiểm duyệt",
    music: "🎵 Nhạc",
    admin: "👑 Quản trị",
    gaming: "🎮 Game",
    analytics: "📊 Thống kê",
    fun: "🎨 Giải trí",
    custom: "🔧 Tùy chỉnh"
  };
  return presets[preset] || preset;
}

function getPermissionDisplayName(permission) {
  const permissionNames = {
    [PermissionFlagsBits.ViewChannel]: "Xem kênh",
    [PermissionFlagsBits.SendMessages]: "Gửi tin nhắn",
    [PermissionFlagsBits.ManageMessages]: "Quản lý tin nhắn",
    [PermissionFlagsBits.KickMembers]: "Kick thành viên",
    [PermissionFlagsBits.BanMembers]: "Ban thành viên",
    [PermissionFlagsBits.Administrator]: "Quản trị viên",
    [PermissionFlagsBits.ManageChannels]: "Quản lý kênh",
    [PermissionFlagsBits.ManageGuild]: "Quản lý server",
    [PermissionFlagsBits.AddReactions]: "Thêm reaction",
    [PermissionFlagsBits.ViewAuditLog]: "Xem audit log",
    [PermissionFlagsBits.Connect]: "Kết nối voice",
    [PermissionFlagsBits.Speak]: "Nói trong voice",
    [PermissionFlagsBits.ModerateMembers]: "Timeout thành viên",
    [PermissionFlagsBits.ManageNicknames]: "Quản lý nickname",
    [PermissionFlagsBits.ManageRoles]: "Quản lý roles",
    [PermissionFlagsBits.ManageWebhooks]: "Quản lý webhooks",
    [PermissionFlagsBits.UseExternalEmojis]: "Dùng emoji ngoài",
    [PermissionFlagsBits.ReadMessageHistory]: "Đọc lịch sử tin nhắn",
    [PermissionFlagsBits.AttachFiles]: "Đính kèm file",
    [PermissionFlagsBits.EmbedLinks]: "Nhúng link",
    [PermissionFlagsBits.UseApplicationCommands]: "Dùng slash commands"
  };
  
  return permissionNames[permission] || permission.toString();
}

function createInviteComponents(botId, preset) {
  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId(`invite_preset_${botId}`)
    .setPlaceholder("Chọn preset quyền khác")
    .addOptions([
      { label: "🤖 Basic - Cơ bản", value: "basic" },
      { label: "🔨 Moderation - Kiểm duyệt", value: "moderation" },
      { label: "🎵 Music - Nhạc", value: "music" },
      { label: "👑 Admin - Quản trị", value: "admin" },
      { label: "🎮 Gaming - Game", value: "gaming" },
      { label: "📊 Analytics - Thống kê", value: "analytics" },
      { label: "🎨 Fun - Giải trí", value: "fun" }
    ]);

  const buttonRow = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId(`invite_custom_${botId}`)
        .setLabel("🔧 Tùy chỉnh quyền")
        .setStyle(ButtonStyle.Primary),
      new ButtonBuilder()
        .setCustomId(`invite_copy_${botId}`)
        .setLabel("📋 Copy link")
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId(`invite_qr_${botId}`)
        .setLabel("📱 QR Code")
        .setStyle(ButtonStyle.Secondary)
    );

  return [
    new ActionRowBuilder().addComponents(selectMenu),
    buttonRow
  ];
}

function createPermissionComponents(category) {
  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId("permission_category")
    .setPlaceholder("Chọn danh mục quyền")
    .addOptions([
      { label: "General - Chung", value: "general" },
      { label: "Text - Tin nhắn", value: "text" },
      { label: "Voice - Âm thanh", value: "voice" },
      { label: "Advanced - Nâng cao", value: "advanced" }
    ]);

  return [new ActionRowBuilder().addComponents(selectMenu)];
}

function createAnalysisComponents(botId) {
  const buttonRow = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId(`bot_analysis_refresh_${botId}`)
        .setLabel("🔄 Làm mới")
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId(`bot_analysis_security_${botId}`)
        .setLabel("🛡️ Chi tiết bảo mật")
        .setStyle(ButtonStyle.Primary),
      new ButtonBuilder()
        .setCustomId(`bot_analysis_export_${botId}`)
        .setLabel("📊 Xuất báo cáo")
        .setStyle(ButtonStyle.Success)
    );

  return [buttonRow];
}

function getPermissionsByCategory(category) {
  // Mock implementation - would return actual permission data
  return [];
}

function getCategoryDisplayName(category) {
  const categories = {
    general: "Chung",
    text: "Tin nhắn", 
    voice: "Âm thanh",
    advanced: "Nâng cao"
  };
  return categories[category] || category;
}

function getCategoryIcon(category) {
  const icons = {
    general: "⚙️",
    text: "💬",
    voice: "🔊", 
    advanced: "🔧"
  };
  return icons[category] || "📋";
}

function categorizePermissions(permissions) {
  // Mock implementation
  return {
    general: [],
    text: [],
    voice: [],
    advanced: []
  };
}

function analyzeSecurityRisk(permissions) {
  // Mock implementation
  return {
    level: "🟡 Trung bình",
    score: 65,
    dangerousPerms: [],
    recommendations: []
  };
}
