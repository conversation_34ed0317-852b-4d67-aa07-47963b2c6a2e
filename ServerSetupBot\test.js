const fs = require('fs');
const path = require('path');

console.log('🧪 Server Setup Bot - Test Script');
console.log('==================================\n');

// Test 1: Ki<PERSON><PERSON> tra file cấu hình
console.log('📋 Test 1: Kiểm tra file cấu hình...');
try {
    const envPath = path.join(__dirname, '.env');
    if (fs.existsSync(envPath)) {
        console.log('✅ File .env tồn tại');
        
        const envContent = fs.readFileSync(envPath, 'utf8');
        const hasToken = envContent.includes('DISCORD_TOKEN=') && !envContent.includes('your_bot_token_here');
        const hasClientId = envContent.includes('CLIENT_ID=') && !envContent.includes('your_bot_client_id_here');
        
        if (hasToken) {
            console.log('✅ DISCORD_TOKEN đã được thiết lập');
        } else {
            console.log('❌ DISCORD_TOKEN chưa được thiết lập');
        }
        
        if (hasClientId) {
            console.log('✅ CLIENT_ID đã được thiết lập');
        } else {
            console.log('❌ CLIENT_ID chưa được thiết lập');
        }
    } else {
        console.log('❌ File .env không tồn tại');
        console.log('💡 Chạy: cp .env.example .env');
    }
} catch (error) {
    console.log('❌ Lỗi khi kiểm tra file .env:', error.message);
}

// Test 2: Kiểm tra dependencies
console.log('\n📦 Test 2: Kiểm tra dependencies...');
try {
    const packageJson = require('./package.json');
    const requiredDeps = ['discord.js', 'sqlite3', 'dotenv', 'moment', 'moment-timezone'];
    
    for (const dep of requiredDeps) {
        if (packageJson.dependencies[dep]) {
            console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
        } else {
            console.log(`❌ ${dep}: Chưa cài đặt`);
        }
    }
    
    // Kiểm tra node_modules
    const nodeModulesPath = path.join(__dirname, 'node_modules');
    if (fs.existsSync(nodeModulesPath)) {
        console.log('✅ node_modules tồn tại');
    } else {
        console.log('❌ node_modules không tồn tại');
        console.log('💡 Chạy: npm install');
    }
} catch (error) {
    console.log('❌ Lỗi khi kiểm tra dependencies:', error.message);
}

// Test 3: Kiểm tra cấu trúc thư mục
console.log('\n📁 Test 3: Kiểm tra cấu trúc thư mục...');
const requiredDirs = [
    'src',
    'src/commands',
    'src/commands/welcome',
    'src/commands/autoroles',
    'src/commands/utility',
    'src/events',
    'src/database',
    'src/utils',
    'src/config'
];

for (const dir of requiredDirs) {
    const dirPath = path.join(__dirname, dir);
    if (fs.existsSync(dirPath)) {
        console.log(`✅ ${dir}/`);
    } else {
        console.log(`❌ ${dir}/ không tồn tại`);
    }
}

// Test 4: Kiểm tra file quan trọng
console.log('\n📄 Test 4: Kiểm tra file quan trọng...');
const requiredFiles = [
    'src/index.js',
    'src/config/config.js',
    'src/database/database.js',
    'src/utils/embedBuilder.js',
    'src/utils/permissions.js',
    'src/utils/validators.js',
    'src/utils/deployCommands.js',
    'src/events/ready.js',
    'src/events/interactionCreate.js',
    'src/events/guildCreate.js',
    'src/events/guildMemberAdd.js',
    'src/events/guildMemberRemove.js',
    'src/commands/utility/help.js',
    'src/commands/welcome/welcome-setup.js',
    'src/commands/welcome/goodbye-setup.js',
    'src/commands/autoroles/autorole-setup.js'
];

for (const file of requiredFiles) {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} không tồn tại`);
    }
}

// Test 5: Kiểm tra syntax JavaScript
console.log('\n🔍 Test 5: Kiểm tra syntax JavaScript...');
const jsFiles = [
    'src/index.js',
    'src/config/config.js',
    'src/database/database.js'
];

for (const file of jsFiles) {
    try {
        const filePath = path.join(__dirname, file);
        if (fs.existsSync(filePath)) {
            require(filePath);
            console.log(`✅ ${file} - Syntax OK`);
        }
    } catch (error) {
        console.log(`❌ ${file} - Syntax Error: ${error.message}`);
    }
}

// Test 6: Kiểm tra database
console.log('\n🗄️ Test 6: Kiểm tra database...');
try {
    const Database = require('./src/database/database.js');
    const testDbPath = './data/test.db';
    
    // Tạo thư mục data nếu chưa có
    const dataDir = path.dirname(testDbPath);
    if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
        console.log('✅ Đã tạo thư mục data/');
    }
    
    const db = new Database(testDbPath);
    console.log('✅ Database class khởi tạo thành công');
    
    // Xóa file test
    if (fs.existsSync(testDbPath)) {
        fs.unlinkSync(testDbPath);
    }
} catch (error) {
    console.log('❌ Lỗi database:', error.message);
}

// Test 7: Kiểm tra config
console.log('\n⚙️ Test 7: Kiểm tra config...');
try {
    // Tạo file .env tạm thời nếu chưa có
    const envPath = path.join(__dirname, '.env');
    let tempEnv = false;
    
    if (!fs.existsSync(envPath)) {
        fs.writeFileSync(envPath, 'DISCORD_TOKEN=test\nCLIENT_ID=test\n');
        tempEnv = true;
    }
    
    const config = require('./src/config/config.js');
    console.log('✅ Config load thành công');
    console.log(`✅ Colors: ${Object.keys(config.colors).length} màu`);
    console.log(`✅ Emojis: ${Object.keys(config.emojis).length} emoji`);
    console.log(`✅ Text: ${Object.keys(config.text).length} text constant`);
    
    // Xóa file .env tạm thời
    if (tempEnv) {
        fs.unlinkSync(envPath);
    }
} catch (error) {
    console.log('❌ Lỗi config:', error.message);
}

// Kết quả tổng kết
console.log('\n🎯 Kết quả tổng kết:');
console.log('==================');
console.log('✅ = Thành công');
console.log('❌ = Cần khắc phục');
console.log('💡 = Gợi ý khắc phục');

console.log('\n📚 Bước tiếp theo:');
console.log('1. Khắc phục các lỗi ❌ nếu có');
console.log('2. Thiết lập .env với token thật');
console.log('3. Chạy: npm run deploy');
console.log('4. Chạy: npm start');
console.log('5. Test bot trong Discord với /help');

console.log('\n🔗 Tài liệu:');
console.log('- README.md: Hướng dẫn tổng quan');
console.log('- SETUP.md: Hướng dẫn cài đặt chi tiết');

console.log('\n✨ Test hoàn tất!');
