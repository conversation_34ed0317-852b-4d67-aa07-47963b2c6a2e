const fs = require('fs');
const path = require('path');

// Function to recursively load all audit log event files
function loadAuditLogEvents(client) {
    console.log('🔍 Loading audit log events...');
    
    const auditLogPath = path.join(__dirname);
    const categories = fs.readdirSync(auditLogPath, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);
    
    let loadedEvents = 0;
    
    for (const category of categories) {
        const categoryPath = path.join(auditLogPath, category);
        
        try {
            const eventFiles = fs.readdirSync(categoryPath)
                .filter(file => file.endsWith('.js'));
            
            for (const file of eventFiles) {
                const filePath = path.join(categoryPath, file);
                
                try {
                    const event = require(filePath);
                    
                    if (event.name && event.execute) {
                        // Register the event listener
                        client.on(event.name, (...args) => event.execute(...args, client));
                        
                        console.log(`✅ Loaded audit event: ${category}/${file} (${event.name})`);
                        loadedEvents++;
                    } else {
                        console.log(`⚠️ Invalid audit event file: ${category}/${file} - missing name or execute`);
                    }
                } catch (error) {
                    console.error(`❌ Error loading audit event ${category}/${file}:`, error.message);
                }
            }
        } catch (error) {
            console.error(`❌ Error reading category ${category}:`, error.message);
        }
    }
    
    console.log(`🎉 Loaded ${loadedEvents} audit log events from ${categories.length} categories`);
    return loadedEvents;
}

module.exports = { loadAuditLogEvents };
