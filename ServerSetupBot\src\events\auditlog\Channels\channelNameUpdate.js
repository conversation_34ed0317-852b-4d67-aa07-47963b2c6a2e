const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');
const auditLogDB = require('../../../utils/auditLogDatabase.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle name changes
            if (oldChannel.name === newChannel.name) return;
            
            console.log(`✏️ Channel name updated: ${oldChannel.name} -> ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_NAME_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_NAME_UPDATE',
                user: 'System',
                userId: null,
                action: 'Tên kênh được thay đổi',
                details: `Tên kênh đã được thay đổi từ **${oldChannel.name}** thành **${newChannel.name}**`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the channel from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Tên kênh được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for channel update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Tên kênh được thay đổi',
                `Vừa có một kênh được đổi tên trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Kênh được đổi tên',
                    value: `- ${newChannel}`,
                    inline: false
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Tên cũ',
                    value: `- ${oldChannel.name}`,
                    inline: true
                },
                {
                    name: '> Tên mới',
                    value: `- ${newChannel.name}`,
                    inline: true
                },
                {
                    name: '> Trong danh mục',
                    value: `- ${newChannel.parent ? newChannel.parent.name : 'Không có'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Name Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel name update logged: ${oldChannel.name} -> ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelNameUpdate audit log:', error);
        }
    }
};
