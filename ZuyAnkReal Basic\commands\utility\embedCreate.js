const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Row<PERSON><PERSON><PERSON>, <PERSON>tonB<PERSON>er, ButtonStyle, ChannelType, WebhookClient } = require('discord.js');

module.exports = {
    name: "embed",
    description: "Create and send a custom embed.",
    category: "Utility",
    async execute(message, args, client) {
        if (!message.member.permissions.has("ManageMessages")) 
            return message.reply("❌ You need `Manage Messages` permission to use this command.");
        if (!message.guild.members.me.permissions.has("EmbedLinks")) 
            return message.reply("❌ I need `Embed Links` permission to send embeds.");

        let embed = new EmbedBuilder()
            .setDescription("Default Embed Description")
            .setColor(0x2F3136);

        function promptEmbed(desc) {
            return new EmbedBuilder()
                .setDescription(desc)
                .setColor("Blurple");
        }

        function createDisabledButtons() {
            return new ActionRowBuilder().addComponents(
                new ButtonBuilder().setCustomId("body").setLabel("Body").setStyle(ButtonStyle.Secondary).setDisabled(true),
                new ButtonBuilder().setCustomId("preview").setLabel("Preview").setStyle(ButtonStyle.Secondary).setDisabled(true),
                new ButtonBuilder().setCustomId("send").setLabel("Send").setStyle(ButtonStyle.Success).setDisabled(true),
                new ButtonBuilder().setCustomId("abort").setLabel("Abort").setStyle(ButtonStyle.Danger).setDisabled(true)
            );
        }

        let buttons = new ActionRowBuilder().addComponents(
            new ButtonBuilder().setCustomId("body").setLabel("Body").setStyle(ButtonStyle.Secondary),
            new ButtonBuilder().setCustomId("preview").setLabel("Preview").setStyle(ButtonStyle.Secondary),
            new ButtonBuilder().setCustomId("send").setLabel("Send").setStyle(ButtonStyle.Success),
            new ButtonBuilder().setCustomId("abort").setLabel("Abort").setStyle(ButtonStyle.Danger)
        );

        let msg = await message.channel.send({ embeds: [embed], components: [buttons] });

        const filter = (i) => i.user.id === message.author.id;
        const collector = msg.createMessageComponentCollector({ filter, time: 1800000 });

        collector.on("collect", async (interaction) => {
            if (!interaction.isButton()) return;
            await interaction.deferUpdate();

            switch (interaction.customId) {
                case "body": {
                    let editButtons = new ActionRowBuilder().addComponents(
                        new ButtonBuilder().setCustomId("author_text").setLabel("Author Text").setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder().setCustomId("title").setLabel("Title").setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder().setCustomId("description").setLabel("Description").setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder().setCustomId("color").setLabel("Color").setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder().setCustomId("back").setLabel("Back").setStyle(ButtonStyle.Success)
                    );
                    await msg.edit({ components: [editButtons] });
                    break;
                }
                case "preview":
                    await interaction.followUp({ embeds: [embed], ephemeral: true });
                    break;
                case "send": {
                    let sendButtons = new ActionRowBuilder().addComponents(
                        new ButtonBuilder().setCustomId("channel").setLabel("Channel").setStyle(ButtonStyle.Primary),
                        new ButtonBuilder().setCustomId("webhook").setLabel("Webhook").setStyle(ButtonStyle.Primary)
                    );
                    await msg.edit({ content: "📢 **Choose how to send the embed:**", components: [sendButtons] });
                    break;
                }
                case "abort":
                    await msg.delete();
                    await message.reply("🚫 Embed creation aborted.");
                    collector.stop();
                    break;

                case "author_text": {
                    await interaction.followUp({ embeds: [promptEmbed("✏️ Enter the author text:")], ephemeral: true });
                    const authorCollector = message.channel.createMessageCollector({ 
                        filter: m => m.author.id === message.author.id,
                        time: 30000, max: 1
                    });
                    authorCollector.on('collect', m => {
                        embed.setAuthor({ name: m.content });
                        msg.edit({ embeds: [embed] });
                        m.delete().catch(() => {});
                    });
                    break;
                }

                case "title": {
                    await interaction.followUp({ embeds: [promptEmbed("✏️ Enter the title:")], ephemeral: true });
                    const titleCollector = message.channel.createMessageCollector({ 
                        filter: m => m.author.id === message.author.id,
                        time: 30000, max: 1
                    });
                    titleCollector.on('collect', m => {
                        embed.setTitle(m.content);
                        msg.edit({ embeds: [embed] });
                        m.delete().catch(() => {});
                    });
                    break;
                }

                case "description": {
                    await interaction.followUp({ embeds: [promptEmbed("✏️ Enter the description:")], ephemeral: true });
                    const descCollector = message.channel.createMessageCollector({ 
                        filter: m => m.author.id === message.author.id,
                        time: 30000, max: 1
                    });
                    descCollector.on('collect', m => {
                        embed.setDescription(m.content);
                        msg.edit({ embeds: [embed] });
                        m.delete().catch(() => {});
                    });
                    break;
                }

                case "color": {
                    await interaction.followUp({ embeds: [promptEmbed("🎨 Enter the color (hex code, e.g., #FF0000):")], ephemeral: true });
                    const colorCollector = message.channel.createMessageCollector({ 
                        filter: m => m.author.id === message.author.id,
                        time: 30000, max: 1
                    });
                    colorCollector.on('collect', m => {
                        const color = m.content.startsWith('#') ? m.content : `#${m.content}`;
                        if (!/^#[0-9A-F]{6}$/i.test(color)) {
                            interaction.followUp({ embeds: [promptEmbed("❌ Invalid color format. Use hex codes like #FF0000")], ephemeral: true });
                            return;
                        }
                        embed.setColor(color);
                        msg.edit({ embeds: [embed] });
                        m.delete().catch(() => {});
                    });
                    break;
                }

                case "back":
                    await msg.edit({ components: [buttons] });
                    break;

                case "channel":
                    await interaction.followUp({ embeds: [promptEmbed("📌 Enter the **channel ID** where you want to send the embed:")], ephemeral: true });
                    const channelCollector = message.channel.createMessageCollector({ filter: (m) => m.author.id === message.author.id, time: 30000, max: 1 });
                    channelCollector.on("collect", async (m) => {
                        let targetChannel = message.guild.channels.cache.get(m.content);
                        if (!targetChannel || targetChannel.type !== ChannelType.GuildText) {
                            await interaction.followUp({ embeds: [promptEmbed("❌ Invalid channel ID.")], ephemeral: true });
                            return;
                        }
                        await targetChannel.send({ embeds: [embed] });
                        await interaction.followUp({ embeds: [promptEmbed("✅ Embed sent!")], ephemeral: true });
                        channelCollector.stop();
                        m.delete().catch(() => {});
                    });
                    break;

                case "webhook":
                    await interaction.followUp({ embeds: [promptEmbed("🔗 Enter the **webhook URL**:")], ephemeral: true });
                    const webhookCollector = message.channel.createMessageCollector({ filter: (m) => m.author.id === message.author.id, time: 30000, max: 1 });
                    webhookCollector.on("collect", async (m) => {
                        let webhookURL = m.content;
                        try {
                            let webhookClient = new WebhookClient({ url: webhookURL });
                            await webhookClient.send({ embeds: [embed] });
                            await interaction.followUp({ embeds: [promptEmbed("✅ Embed sent via Webhook!")], ephemeral: true });
                            webhookCollector.stop();
                            m.delete().catch(() => {});
                        } catch {
                            await interaction.followUp({ embeds: [promptEmbed("❌ Invalid webhook URL.")], ephemeral: true });
                        }
                    });
                    break;
            }
        });

        collector.on("end", (_, reason) => {
            if (reason === 'time') {
                msg.edit({ components: [createDisabledButtons()] }).catch(() => {});
            }
        });
    }
};