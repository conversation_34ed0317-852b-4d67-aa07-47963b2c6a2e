const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.AutoModerationRuleUpdate,
    async execute(oldRule, newRule, client) {
        try {
            console.log(`🔄 AutoMod rule updated: ${newRule.name} in ${newRule.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newRule.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'AUTOMOD_RULE_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'AUTOMOD_RULE_UPDATE',
                user: 'System',
                userId: null,
                action: 'Quy tắc AutoMod được cập nhật',
                details: `Quy tắc AutoMod **${newRule.name}** đã được cập nhật`,
                target: newRule.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the rule from audit logs
            try {
                const auditLogs = await newRule.guild.fetchAuditLogs({
                    type: 141, // AUTO_MODERATION_RULE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newRule.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Quy tắc AutoMod được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for automod rule update');
            }
            
            // Add to database
            await client.db.addAuditLog(newRule.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔄 Quy tắc AutoMod được cập nhật',
                `Vừa có quy tắc AutoMod được cập nhật trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get trigger type names
            const triggerTypes = {
                1: 'Keyword',
                2: 'Harmful Link',
                3: 'Spam',
                4: 'Keyword Preset',
                5: 'Mention Spam'
            };
            
            embed.addFields([
                {
                    name: '> Tên quy tắc',
                    value: `- ${newRule.name}`,
                    inline: true
                },
                {
                    name: '> ID quy tắc',
                    value: `- ${newRule.id}`,
                    inline: true
                },
                {
                    name: '> Loại trigger',
                    value: `- ${triggerTypes[newRule.triggerType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người cập nhật',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Compare enabled status
            if (oldRule.enabled !== newRule.enabled) {
                embed.addFields([
                    {
                        name: '> Trạng thái cũ',
                        value: `- ${oldRule.enabled ? '✅ Bật' : '❌ Tắt'}`,
                        inline: true
                    },
                    {
                        name: '> Trạng thái mới',
                        value: `- ${newRule.enabled ? '✅ Bật' : '❌ Tắt'}`,
                        inline: true
                    }
                ]);
                
                if (newRule.enabled) {
                    embed.setColor(0x2ecc71); // Green for enabled
                } else {
                    embed.setColor(0xe74c3c); // Red for disabled
                }
            }
            
            // Compare name changes
            if (oldRule.name !== newRule.name) {
                embed.addFields([
                    {
                        name: '> Tên cũ',
                        value: `- ${oldRule.name}`,
                        inline: true
                    },
                    {
                        name: '> Tên mới',
                        value: `- ${newRule.name}`,
                        inline: true
                    }
                ]);
            }
            
            // Add current rule configuration
            embed.addFields({
                name: '> Cấu hình hiện tại',
                value: `- Trạng thái: ${newRule.enabled ? 'Bật' : 'Tắt'}`,
                inline: false
            });
            
            // Add actions if available
            if (newRule.actions && newRule.actions.length > 0) {
                const actionTypes = {
                    1: 'Block Message',
                    2: 'Send Alert Message',
                    3: 'Timeout User'
                };
                
                const actionsList = newRule.actions.map(action => {
                    return actionTypes[action.type] || 'Unknown Action';
                }).join(', ');
                
                embed.addFields({
                    name: '> Hành động',
                    value: `- ${actionsList}`,
                    inline: false
                });
            }
            
            // Add exempt roles if available
            if (newRule.exemptRoles && newRule.exemptRoles.size > 0) {
                const exemptRolesList = newRule.exemptRoles.map(role => role.name).join(', ');
                embed.addFields({
                    name: '> Roles được miễn trừ',
                    value: `- ${exemptRolesList}`,
                    inline: false
                });
            }
            
            // Add exempt channels if available
            if (newRule.exemptChannels && newRule.exemptChannels.size > 0) {
                const exemptChannelsList = newRule.exemptChannels.map(channel => channel.name).join(', ');
                embed.addFields({
                    name: '> Kênh được miễn trừ',
                    value: `- ${exemptChannelsList}`,
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newRule.guild.name} • AutoMod Rule Update`,
                iconURL: newRule.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ AutoMod rule update logged: ${newRule.name}`);
            
        } catch (error) {
            console.error('Error in automodRuleUpdate audit log:', error);
        }
    }
};
