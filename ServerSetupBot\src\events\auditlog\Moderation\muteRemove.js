const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildMemberUpdate,
    async execute(oldMember, newMember, client) {
        try {
            // Check if member was unmuted (timeout removed)
            if (!oldMember.communicationDisabledUntil) return; // Wasn't muted
            if (newMember.communicationDisabledUntil) return; // Still muted
            
            console.log(`🔊 User unmuted: ${newMember.user.tag} in ${newMember.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newMember.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'MUTE_REMOVE')) return;
            
            // Check if this was manual unmute or automatic expiry
            const wasManualUnmute = oldMember.communicationDisabledUntil.getTime() > Date.now();
            
            // Create event data
            const eventData = {
                eventType: 'MUTE_REMOVE',
                user: newMember.user.tag,
                userId: newMember.user.id,
                action: wasManualUnmute ? 'Thành viên được unmute' : 'Mute của thành viên đã hết hạn',
                details: `**${newMember.user.tag}** ${wasManualUnmute ? 'đã được unmute' : 'đã hết thời gian mute'}`,
                target: newMember.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who unmuted the user from audit logs (if manual)
            if (wasManualUnmute) {
                try {
                    const auditLogs = await newMember.guild.fetchAuditLogs({
                        type: 24, // MEMBER_UPDATE
                        limit: 1
                    });
                    
                    const auditEntry = auditLogs.entries.first();
                    if (auditEntry && auditEntry.target?.id === newMember.user.id) {
                        eventData.moderator = auditEntry.executor.tag;
                        eventData.moderatorId = auditEntry.executor.id;
                        eventData.action = `Thành viên được unmute bởi ${auditEntry.executor.tag}`;
                        if (auditEntry.reason) {
                            eventData.reason = auditEntry.reason;
                        }
                    }
                } catch (error) {
                    console.log('Could not fetch audit logs for mute remove');
                }
            }
            
            // Add to database
            await client.db.addAuditLog(newMember.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                wasManualUnmute ? '🔊 Thành viên được unmute' : '⏰ Mute đã hết hạn',
                wasManualUnmute ? 'Vừa có thành viên được unmute' : 'Vừa có mute hết thời gian'
            );
            
            embed.setColor(wasManualUnmute ? 0x2ecc71 : 0x3498db); // Green for manual, blue for auto
            
            // Calculate how long they were muted
            const muteStartTime = await client.db.getMuteStartTime(newMember.guild.id, newMember.user.id);
            let muteDuration = '';
            if (muteStartTime) {
                const duration = Date.now() - new Date(muteStartTime).getTime();
                const days = Math.floor(duration / (1000 * 60 * 60 * 24));
                const hours = Math.floor((duration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
                
                if (days > 0) muteDuration += `${days} ngày `;
                if (hours > 0) muteDuration += `${hours} giờ `;
                if (minutes > 0) muteDuration += `${minutes} phút`;
            }
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${newMember.user.tag} (${newMember.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newMember.user.id}`,
                    inline: true
                },
                {
                    name: '> Thời gian unmute',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Loại unmute',
                    value: `- ${wasManualUnmute ? 'Thủ công' : 'Tự động'}`,
                    inline: true
                }
            ]);
            
            if (muteDuration) {
                embed.addFields({
                    name: '> Thời gian đã mute',
                    value: `- ${muteDuration.trim()}`,
                    inline: true
                });
            }
            
            // Add moderator info if manual unmute
            if (wasManualUnmute && eventData.moderator) {
                embed.addFields({
                    name: '> Moderator unmute',
                    value: `- ${eventData.moderator}`,
                    inline: true
                });
            }
            
            // Add unmute reason if available
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do unmute',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Add original mute info if available
            const originalMuteInfo = await client.db.getOriginalMuteInfo(newMember.guild.id, newMember.user.id);
            if (originalMuteInfo) {
                if (originalMuteInfo.reason) {
                    embed.addFields({
                        name: '> Lý do mute ban đầu',
                        value: `- ${originalMuteInfo.reason}`,
                        inline: false
                    });
                }
                
                if (originalMuteInfo.moderator) {
                    embed.addFields({
                        name: '> Moderator đã mute',
                        value: `- ${originalMuteInfo.moderator}`,
                        inline: true
                    });
                }
            }
            
            // Add unmute impact
            embed.addFields({
                name: '> 📝 Tác động của unmute',
                value: [
                    '• Có thể gửi tin nhắn trong server',
                    '• Có thể tham gia voice channels',
                    '• Có thể tạo threads và reactions',
                    '• Khôi phục đầy đủ quyền giao tiếp'
                ].join('\n'),
                inline: false
            });
            
            // Add monitoring recommendations
            embed.addFields({
                name: '> 👀 Khuyến nghị theo dõi',
                value: [
                    '• Theo dõi hành vi sau khi unmute',
                    '• Kiểm tra xem có vi phạm lại không',
                    '• Có thể cần nhắc nhở quy tắc',
                    '• Ghi chép để tham khảo sau này'
                ].join('\n'),
                inline: false
            });
            
            // Set user avatar as thumbnail
            if (newMember.user.displayAvatarURL()) {
                embed.setThumbnail(newMember.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newMember.guild.name} • Member Mute Remove`,
                iconURL: newMember.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Mute remove logged: ${newMember.user.tag}`);
            
        } catch (error) {
            console.error('Error in muteRemove audit log:', error);
        }
    }
};
