const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');
const auditLogDB = require('../../../utils/auditLogDatabase.js');

module.exports = {
    name: Events.GuildMemberRemove,
    async execute(member, client) {
        try {
            console.log(`👋 Member left: ${member.user.tag} in ${member.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(member.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'USER_LEAVE')) return;
            
            // Create event data
            let eventData = {
                eventType: 'USER_LEAVE',
                user: member.user.tag,
                userId: member.user.id,
                action: 'Thành viên rời server',
                details: `**${member.user.tag}** đã rời server`,
                target: member.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Check if user was kicked or banned
            let leaveType = 'left';
            let executor = null;
            let reason = null;
            
            try {
                // Check for kick
                const kickLogs = await member.guild.fetchAuditLogs({
                    type: 20, // MEMBER_KICK
                    limit: 1
                });
                
                const kickEntry = kickLogs.entries.first();
                if (kickEntry && kickEntry.target?.id === member.user.id) {
                    const timeDiff = Date.now() - kickEntry.createdTimestamp;
                    if (timeDiff < 5000) { // Within 5 seconds
                        leaveType = 'kicked';
                        executor = kickEntry.executor;
                        reason = kickEntry.reason;
                        eventData.eventType = 'USER_KICK';
                        eventData.action = `Thành viên bị kick bởi ${executor.tag}`;
                        eventData.user = executor.tag;
                        eventData.userId = executor.id;
                        eventData.reason = reason;
                    }
                }
                
                // Check for ban if not kicked
                if (leaveType === 'left') {
                    const banLogs = await member.guild.fetchAuditLogs({
                        type: 22, // MEMBER_BAN_ADD
                        limit: 1
                    });
                    
                    const banEntry = banLogs.entries.first();
                    if (banEntry && banEntry.target?.id === member.user.id) {
                        const timeDiff = Date.now() - banEntry.createdTimestamp;
                        if (timeDiff < 5000) { // Within 5 seconds
                            leaveType = 'banned';
                            executor = banEntry.executor;
                            reason = banEntry.reason;
                            eventData.eventType = 'BAN_ADD';
                            eventData.action = `Thành viên bị ban bởi ${executor.tag}`;
                            eventData.user = executor.tag;
                            eventData.userId = executor.id;
                            eventData.reason = reason;
                        }
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for member remove');
            }
            
            // Add to database
            await client.db.addAuditLog(member.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Calculate how long they were in the server
            const timeInServer = Date.now() - member.joinedTimestamp;
            const daysInServer = Math.floor(timeInServer / (1000 * 60 * 60 * 24));
            const hoursInServer = Math.floor((timeInServer % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            
            // Create embed based on leave type
            let title, color;
            switch (leaveType) {
                case 'kicked':
                    title = '👢 Thành viên bị kick';
                    color = 0xe67e22; // Orange
                    break;
                case 'banned':
                    title = '🔨 Thành viên bị ban';
                    color = 0xe74c3c; // Red
                    break;
                default:
                    title = '👋 Thành viên rời server';
                    color = 0x95a5a6; // Gray
            }
            
            const embed = createInfoEmbed(
                title,
                `Vừa có một thành viên ${leaveType === 'left' ? 'rời' : leaveType === 'kicked' ? 'bị kick khỏi' : 'bị ban khỏi'} server`
            );
            
            embed.setColor(color);
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${member.user.tag} (${member.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${member.user.id}`,
                    inline: true
                },
                {
                    name: '> Tham gia lúc',
                    value: `- <t:${Math.floor(member.joinedTimestamp / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Thời gian trong server',
                    value: `- ${daysInServer} ngày, ${hoursInServer} giờ`,
                    inline: true
                },
                {
                    name: '> Tổng thành viên còn lại',
                    value: `- ${member.guild.memberCount} thành viên`,
                    inline: true
                }
            ]);
            
            if (executor) {
                embed.addFields({
                    name: `> Người ${leaveType === 'kicked' ? 'kick' : 'ban'}`,
                    value: `- ${executor.tag} (${executor})`,
                    inline: true
                });
            }
            
            if (reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${reason}`,
                    inline: false
                });
            }
            
            embed.addFields({
                name: '> Thời gian',
                value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                inline: true
            });
            
            // Set user avatar as thumbnail
            if (member.user.displayAvatarURL()) {
                embed.setThumbnail(member.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${member.guild.name} • Member ${leaveType === 'left' ? 'Leave' : leaveType === 'kicked' ? 'Kick' : 'Ban'}`,
                iconURL: member.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Member ${leaveType} logged: ${member.user.tag}`);
            
        } catch (error) {
            console.error('Error in userLeave audit log:', error);
        }
    }
};
