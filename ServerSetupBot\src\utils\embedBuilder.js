const { EmbedBuilder } = require('discord.js');
const config = require('../config/config.js');

/**
 * T<PERSON>o embed thành công với màu xanh lá
 */
function createSuccessEmbed(title, description, fields = []) {
    const embed = new EmbedBuilder()
        .setColor(config.colors.success)
        .setTitle(`${config.emojis.success} ${title}`)
        .setDescription(description)
        .setTimestamp()
        .setFooter({ 
            text: config.text.botName,
            iconURL: null // Sẽ được set trong command
        });

    if (fields.length > 0) {
        embed.addFields(fields);
    }

    return embed;
}

/**
 * Tạo embed lỗi với màu đỏ
 */
function createErrorEmbed(title, description, fields = []) {
    const embed = new EmbedBuilder()
        .setColor(config.colors.error)
        .setTitle(`${config.emojis.error} ${title}`)
        .setDescription(description)
        .setTimestamp()
        .setFooter({ 
            text: config.text.botName,
            iconURL: null
        });

    if (fields.length > 0) {
        embed.addFields(fields);
    }

    return embed;
}

/**
 * Tạo embed cảnh báo với màu vàng
 */
function createWarningEmbed(title, description, fields = []) {
    const embed = new EmbedBuilder()
        .setColor(config.colors.warning)
        .setTitle(`${config.emojis.warning} ${title}`)
        .setDescription(description)
        .setTimestamp()
        .setFooter({ 
            text: config.text.botName,
            iconURL: null
        });

    if (fields.length > 0) {
        embed.addFields(fields);
    }

    return embed;
}

/**
 * Tạo embed thông tin với màu xanh dương
 */
function createInfoEmbed(title, description, fields = []) {
    const embed = new EmbedBuilder()
        .setColor(config.colors.info)
        .setTitle(`${config.emojis.info} ${title}`)
        .setDescription(description)
        .setTimestamp()
        .setFooter({ 
            text: config.text.botName,
            iconURL: null
        });

    if (fields.length > 0) {
        embed.addFields(fields);
    }

    return embed;
}

/**
 * Tạo embed chính với màu primary
 */
function createPrimaryEmbed(title, description, fields = []) {
    const embed = new EmbedBuilder()
        .setColor(config.colors.primary)
        .setTitle(title)
        .setDescription(description)
        .setTimestamp()
        .setFooter({ 
            text: config.text.botName,
            iconURL: null
        });

    if (fields.length > 0) {
        embed.addFields(fields);
    }

    return embed;
}

/**
 * Tạo embed loading với màu secondary
 */
function createLoadingEmbed(title, description) {
    const embed = new EmbedBuilder()
        .setColor(config.colors.secondary)
        .setTitle(`${config.emojis.loading} ${title}`)
        .setDescription(description)
        .setTimestamp()
        .setFooter({ 
            text: config.text.botName,
            iconURL: null
        });

    return embed;
}

/**
 * Tạo embed cho welcome message
 */
function createWelcomeEmbed(member, customMessage = null) {
    const defaultMessage = `Chào mừng **{user}** đến với **{server}**!\n\nHy vọng bạn sẽ có những trải nghiệm tuyệt vời tại đây! ${config.emojis.welcome}`;
    
    const message = customMessage || defaultMessage;
    const processedMessage = message
        .replace(/{user}/g, `<@${member.id}>`)
        .replace(/{username}/g, member.user.username)
        .replace(/{server}/g, member.guild.name)
        .replace(/{membercount}/g, member.guild.memberCount.toString());

    const embed = new EmbedBuilder()
        .setColor(config.colors.success)
        .setTitle(`${config.emojis.welcome} Chào mừng thành viên mới!`)
        .setDescription(processedMessage)
        .setThumbnail(member.user.displayAvatarURL({ dynamic: true }))
        .setTimestamp()
        .setFooter({ 
            text: `Thành viên thứ ${member.guild.memberCount}`,
            iconURL: member.guild.iconURL({ dynamic: true })
        });

    return embed;
}

/**
 * Tạo embed cho goodbye message
 */
function createGoodbyeEmbed(member, customMessage = null) {
    const defaultMessage = `**{username}** đã rời khỏi **{server}**.\n\nChúc bạn may mắn! ${config.emojis.goodbye}`;
    
    const message = customMessage || defaultMessage;
    const processedMessage = message
        .replace(/{user}/g, `<@${member.id}>`)
        .replace(/{username}/g, member.user.username)
        .replace(/{server}/g, member.guild.name)
        .replace(/{membercount}/g, member.guild.memberCount.toString());

    const embed = new EmbedBuilder()
        .setColor(config.colors.warning)
        .setTitle(`${config.emojis.goodbye} Tạm biệt!`)
        .setDescription(processedMessage)
        .setThumbnail(member.user.displayAvatarURL({ dynamic: true }))
        .setTimestamp()
        .setFooter({ 
            text: `Còn lại ${member.guild.memberCount} thành viên`,
            iconURL: member.guild.iconURL({ dynamic: true })
        });

    return embed;
}

/**
 * Tạo embed cho help command
 */
function createHelpEmbed(commands, category = null) {
    const embed = new EmbedBuilder()
        .setColor(config.colors.primary)
        .setTitle(`${config.emojis.info} Hướng dẫn sử dụng Server Setup Bot`)
        .setDescription(
            `Bot chuyên về **thiết lập và cấu hình máy chủ Discord** với đầy đủ tính năng.\n\n` +
            `**Lưu ý:** Chỉ **Quản trị viên** mới có thể sử dụng các lệnh thiết lập.`
        )
        .setTimestamp()
        .setFooter({ 
            text: `Sử dụng /help [tên_lệnh] để xem chi tiết • ${config.text.botName}`,
            iconURL: null
        });

    if (category) {
        // Hiển thị commands của category cụ thể
        const categoryCommands = commands.filter(cmd => cmd.category === category);
        if (categoryCommands.length > 0) {
            embed.setTitle(`${config.emojis.info} Danh sách lệnh - ${category}`);
            
            const commandList = categoryCommands.map(cmd => 
                `**/${cmd.name}** - ${cmd.description}`
            ).join('\n');
            
            embed.setDescription(commandList);
        }
    } else {
        // Hiển thị tất cả categories
        const categories = {
            'Welcome/Goodbye': '👋 Thiết lập hệ thống chào mừng và tạm biệt',
            'Auto Roles': '🎭 Quản lý auto-role và reaction roles',
            'Channel Management': '📝 Tạo và tổ chức kênh',
            'Permissions': '🔒 Quản lý quyền hạn',
            'Verification': '✅ Hệ thống xác minh thành viên',
            'Moderation': '🛡️ Thiết lập công cụ kiểm duyệt',
            'Templates': '📋 Tạo và áp dụng template',
            'Backup': '💾 Sao lưu và khôi phục',
            'Utility': '⚙️ Tiện ích và cấu hình'
        };

        const fields = Object.entries(categories).map(([name, desc]) => ({
            name: name,
            value: desc,
            inline: true
        }));

        embed.addFields(fields);
        embed.addFields({
            name: '📖 Cách sử dụng',
            value: '• Sử dụng `/help [category]` để xem lệnh theo danh mục\n• Sử dụng `/help [command]` để xem chi tiết lệnh',
            inline: false
        });
    }

    return embed;
}

module.exports = {
    createSuccessEmbed,
    createErrorEmbed,
    createWarningEmbed,
    createInfoEmbed,
    createPrimaryEmbed,
    createLoadingEmbed,
    createWelcomeEmbed,
    createGoodbyeEmbed,
    createHelpEmbed
};
