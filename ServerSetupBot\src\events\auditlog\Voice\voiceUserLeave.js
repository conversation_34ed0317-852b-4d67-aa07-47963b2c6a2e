const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.VoiceStateUpdate,
    async execute(oldState, newState, client) {
        try {
            // Only handle user leaving voice channel (was in voice, now is not)
            if (!oldState.channelId || newState.channelId) return;
            
            console.log(`🔇 User left voice: ${oldState.member.user.tag} in ${oldState.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(oldState.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'VOICE_USER_LEAVE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'VOICE_USER_LEAVE',
                user: oldState.member.user.tag,
                userId: oldState.member.user.id,
                action: 'R<PERSON><PERSON> kênh thoại',
                details: `**${oldState.member.user.tag}** đã rời kênh thoại **${oldState.channel.name}**`,
                target: oldState.member.user.tag,
                channel: oldState.channel.name,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(oldState.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔇 Rời kênh thoại',
                `Vừa có thành viên rời kênh thoại`
            );
            
            embed.setColor(0x95a5a6); // Gray for leave
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${oldState.member.user.tag} (${oldState.member.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${oldState.member.user.id}`,
                    inline: true
                },
                {
                    name: '> Kênh thoại',
                    value: `- ${oldState.channel.name}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${oldState.channel.id}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Số người còn lại',
                    value: `- ${oldState.channel.members.size} thành viên`,
                    inline: true
                }
            ]);
            
            // Calculate session duration if possible
            if (oldState.member.voice?.sessionId) {
                // Note: We can't accurately calculate session duration without storing join time
                // This would require a separate tracking system
                embed.addFields({
                    name: '> Thời gian trong kênh',
                    value: `- Không thể tính toán`,
                    inline: true
                });
            }
            
            // Set user avatar as thumbnail
            if (oldState.member.user.displayAvatarURL()) {
                embed.setThumbnail(oldState.member.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${oldState.guild.name} • Voice User Leave`,
                iconURL: oldState.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Voice user leave logged: ${oldState.member.user.tag}`);
            
        } catch (error) {
            console.error('Error in voiceUserLeave audit log:', error);
        }
    }
};
