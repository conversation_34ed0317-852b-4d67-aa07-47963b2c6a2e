const { EmbedBuilder } = require("discord.js");
const db = require("quick.db");

module.exports = {
  name: "guildMemberAdd",
  async execute(member) {
    const channelId = db.get(`welcome_channel_${member.guild.id}`);
    if (!channelId) return;

    const channel = member.guild.channels.cache.get(channelId);
    if (!channel) return;

    const normalMsg = db.get(`welcome_message_${member.guild.id}`);
    const embedMsg = db.get(`welcome_embed_${member.guild.id}`);

    const placeholders = {
      "{user.tag}": member.user.tag,
      "{user.id}": member.id,
      "{user.mention}": `<@${member.id}>`,
      "{user.createdAt}": `<t:${Math.floor(member.user.createdTimestamp / 1000)}:F>`,
      "{user.icon}": member.user.displayAvatarURL({ dynamic: true }),
      "{server.id}": member.guild.id,
      "{server.name}": member.guild.name,
      "{user.joinedAt}": `<t:${Math.floor(member.joinedTimestamp / 1000)}:F>`
    };

    function replace(msg) {
      for (const key in placeholders) {
        msg = msg.replaceAll(key, placeholders[key]);
      }
      return msg;
    }

    if (normalMsg) {
      channel.send({ content: replace(normalMsg) });
    }

    if (embedMsg) {
      const embed = new EmbedBuilder()
        .setDescription(replace(embedMsg))
        .setColor("Random")
        .setThumbnail(member.user.displayAvatarURL({ dynamic: true }))
        .setFooter({ text: member.user.tag, iconURL: member.user.displayAvatarURL({ dynamic: true }) });

      channel.send({ embeds: [embed] });
    }
  }
};