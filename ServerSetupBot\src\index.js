const { Client, GatewayIntentBits, Collection } = require("discord.js");
const fs = require("fs");
const path = require("path");
const config = require("./config/config.js");
const Database = require("./database/database.js");
const { deployCommands } = require("./utils/deployCommands.js");

// Tạo client Discord với các intents cần thiết
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.GuildMessageReactions,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.GuildPresences,
  ],
});

// Khởi tạo collections cho commands
client.commands = new Collection();
client.config = config;

// Khởi tạo database
const database = new Database(config.database.path);
client.db = database;

// Hàm load commands
async function loadCommands() {
  const commandsPath = path.join(__dirname, "commands");

  if (!fs.existsSync(commandsPath)) {
    console.log("📁 Tạo thư mục commands...");
    fs.mkdirSync(commandsPath, { recursive: true });
    return;
  }

  const commandFolders = fs.readdirSync(commandsPath);

  for (const folder of commandFolders) {
    const folderPath = path.join(commandsPath, folder);

    if (fs.statSync(folderPath).isDirectory()) {
      const commandFiles = fs
        .readdirSync(folderPath)
        .filter((file) => file.endsWith(".js"));

      for (const file of commandFiles) {
        const filePath = path.join(folderPath, file);
        try {
          const command = require(filePath);

          if ("data" in command && "execute" in command) {
            client.commands.set(command.data.name, command);
            console.log(`✅ Đã tải lệnh: ${command.data.name}`);
          } else {
            console.log(
              `⚠️ Lệnh tại ${filePath} thiếu thuộc tính "data" hoặc "execute".`
            );
          }
        } catch (error) {
          console.error(`❌ Lỗi khi tải lệnh ${filePath}:`, error);
        }
      }
    }
  }
}

// Hàm load events
async function loadEvents() {
  const eventsPath = path.join(__dirname, "events");

  if (!fs.existsSync(eventsPath)) {
    console.log("📁 Tạo thư mục events...");
    fs.mkdirSync(eventsPath, { recursive: true });
    return;
  }

  const eventFiles = fs
    .readdirSync(eventsPath)
    .filter((file) => file.endsWith(".js"));

  for (const file of eventFiles) {
    const filePath = path.join(eventsPath, file);
    try {
      const event = require(filePath);

      if (event.once) {
        client.once(event.name, (...args) => event.execute(...args, client));
      } else {
        client.on(event.name, (...args) => event.execute(...args, client));
      }

      console.log(`✅ Đã tải sự kiện: ${event.name}`);
    } catch (error) {
      console.error(`❌ Lỗi khi tải sự kiện ${filePath}:`, error);
    }
  }
}

// Hàm khởi tạo bot
async function initializeBot() {
  try {
    console.log("🚀 Đang khởi động Server Setup Bot...");

    // Kiểm tra cấu hình cần thiết
    if (!config.token) {
      console.error(
        "❌ Lỗi: DISCORD_TOKEN không được thiết lập trong file .env"
      );
      console.log(
        "💡 Hướng dẫn: Sao chép .env.example thành .env và điền thông tin bot"
      );
      process.exit(1);
    }

    if (!config.clientId) {
      console.error("❌ Lỗi: CLIENT_ID không được thiết lập trong file .env");
      console.log("💡 Hướng dẫn: Thêm CLIENT_ID của bot vào file .env");
      process.exit(1);
    }

    // Khởi tạo database
    await database.initialize();
    console.log("✅ Database đã được khởi tạo");

    // Load commands và events
    await loadCommands();
    await loadEvents();

    // Auto-deploy commands nếu có lệnh được load
    if (client.commands.size > 0) {
      console.log("🔄 Đang tự động deploy slash commands...");
      try {
        await deployCommands();
        console.log("✅ Slash commands đã được deploy thành công");
      } catch (error) {
        console.error(
          "⚠️ Lỗi khi deploy commands (bot vẫn sẽ chạy):",
          error.message
        );
        console.log("💡 Bạn có thể chạy 'npm run deploy' để deploy thủ công");
      }
    }

    // Đăng nhập bot
    await client.login(config.token);
  } catch (error) {
    console.error("❌ Lỗi khi khởi động bot:", error);
    process.exit(1);
  }
}

// Xử lý lỗi không được bắt
process.on("unhandledRejection", (error) => {
  console.error("❌ Unhandled promise rejection:", error);
});

process.on("uncaughtException", (error) => {
  console.error("❌ Uncaught exception:", error);
  process.exit(1);
});

// Khởi động bot
initializeBot();
