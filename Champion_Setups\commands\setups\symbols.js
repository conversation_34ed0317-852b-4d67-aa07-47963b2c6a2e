const Command = require("../../structures/Command");
const Discord = require("discord.js");
const symbols = require("../../data/symbols.json");

module.exports = class Symbols extends Command {
  constructor(client) {
    super(client, {
      name: "symbols",
      description: client.cmdConfig.symbols.description,
      usage: client.cmdConfig.symbols.usage,
      permissions: client.cmdConfig.symbols.permissions,
      aliases: client.cmdConfig.symbols.aliases,
      category: "setups",
      listed: client.cmdConfig.symbols.enabled,
      slash: true,
      options: [{
        name: 'arrow',
        type: Discord.ApplicationCommandOptionType.Subcommand,
        description: "List of Arrow Symbols",
      }, {
        name: 'line',
        type: Discord.ApplicationCommandOptionType.Subcommand,
        description: "List of Line Symbols",
      }, {
        name: 'bracket',
        type: Discord.ApplicationCommandOptionType.Subcommand,
        description: "List of Bracket Symbols",
      }, {
        name: 'other',
        type: Discord.ApplicationCommandOptionType.Subcommand,
        description: "List of Other Symbols",
      }]
    });
  }

  async run(message, args) {
    let type = args[0];
    
    if(!type) return message.channel.send({ embeds: [this.client.utils.usage(this.client, this.client.cmdConfig.symbols.usage)]});
    
    let findList = symbols.find((x) => x.name.toLowerCase() == type.toLowerCase());
    let symbolList = Object.values(findList.list).map((y) => `\`${y}\``).join(", ").trim();
    
    let embed = new Discord.EmbedBuilder()
      .setColor(this.client.embeds.setup.symbols.color);

    if (this.client.embeds.setup.symbols.title) embed.setTitle(this.client.embeds.setup.symbols.title);

    if (this.client.embeds.setup.symbols.description) embed.setDescription(this.client.embeds.setup.symbols.description.replace("<type>", this.client.utils.capitalizeFirstLetter(type))
      .replace("<count>", Object.values(findList.list).length)
      .replace("<symbols>", symbolList));

    let field = this.client.embeds.setup.symbols.fields;
    for (let i = 0; i < this.client.embeds.setup.symbols.fields.length; i++) {
      embed.addField(field[i].title.replace("<type>", this.client.utils.capitalizeFirstLetter(type))
        .replace("<count>", Object.values(findList.list).length), field[i].description.replace("<type>", this.client.utils.capitalizeFirstLetter(type))
      .replace("<count>", Object.values(findList.list).length)
      .replace("<symbols>", symbolList));
    }

    if (this.client.embeds.setup.symbols.footer == true) embed.setFooter({ text: message.author.username, iconURL: message.author.displayAvatarURL() }).setTimestamp();
    if (this.client.embeds.setup.symbols.thumbnail == true) embed.setThumbnail(message.guild.iconURL());
    
    message.channel.send({ embeds: [embed] });
  }
  async slashRun(interaction, args) {
    let type = args[0];
    let findList = symbols.find((x) => x.name.toLowerCase() == type.toLowerCase());
    let symbolList = Object.values(findList.list).map((y) => `\`${y}\``).join(", ").trim();
    
    let embed = new Discord.EmbedBuilder()
      .setColor(this.client.embeds.setup.symbols.color);

    if (this.client.embeds.setup.symbols.title) embed.setTitle(this.client.embeds.setup.symbols.title);

    if (this.client.embeds.setup.symbols.description) embed.setDescription(this.client.embeds.setup.symbols.description.replace("<type>", this.client.utils.capitalizeFirstLetter(type))
      .replace("<count>", Object.values(findList.list).length)
      .replace("<symbols>", symbolList));

    let field = this.client.embeds.setup.symbols.fields;
    for (let i = 0; i < this.client.embeds.setup.symbols.fields.length; i++) {
      embed.addField(field[i].title.replace("<type>", this.client.utils.capitalizeFirstLetter(type))
        .replace("<count>", Object.values(findList.list).length), field[i].description.replace("<type>", this.client.utils.capitalizeFirstLetter(type))
      .replace("<count>", Object.values(findList.list).length)
      .replace("<symbols>", symbolList));
    }

    if (this.client.embeds.setup.symbols.footer == true) embed.setFooter({ text: interaction.user.username, iconURL: interaction.user.displayAvatarURL() }).setTimestamp();
    if (this.client.embeds.setup.symbols.thumbnail == true) embed.setThumbnail(interaction.guild.iconURL());
    
    interaction.reply({ embeds: [embed], ephemeral: this.client.cmdConfig.symbols.ephemeral });
  }
};
