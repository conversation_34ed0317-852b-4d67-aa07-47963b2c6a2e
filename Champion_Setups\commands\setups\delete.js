const Command = require("../../structures/Command");
const Discord = require("discord.js");

let callData = {};

module.exports = class Delete extends Command {
  constructor(client) {
    super(client, {
      name: "delete",
      description: client.cmdConfig.delete.description,
      usage: client.cmdConfig.delete.usage,
      permissions: client.cmdConfig.delete.permissions,
      aliases: client.cmdConfig.delete.aliases,
      category: "setups",
      listed: client.cmdConfig.delete.enabled,
    });
  }

  async run(message, args) {
    let embed = new Discord.EmbedBuilder()
      .setColor(this.client.embeds.setup.delete.color);
    
    if(this.client.embeds.setup.delete.title) embed.setTitle(this.client.embeds.setup.delete.title);
    
    if(this.client.embeds.setup.delete.description) embed.setDescription(this.client.embeds.setup.delete.description);
    
    let field = this.client.embeds.setup.delete.fields;
    for(let i = 0; i < this.client.embeds.setup.delete.fields.length; i++) {
      embed.addFields([{ name: field[i].title, value: field[i].description }]);
    }

    if(this.client.embeds.setup.delete.footer == true) embed.setFooter({ text: message.author.username, iconURL: message.author.displayAvatarURL() }).setTimestamp();
    if(this.client.embeds.setup.delete.thumbnail == true) embed.setThumbnail(message.guild.iconURL());

      const options = [{
        label: this.client.language.delete.buttons.delete_all, 
        value: "delete_all", 
        emoji: this.client.config.emojis.delete_all
      }, {
        label: this.client.language.delete.buttons.delete_channels, 
        value: "delete_channels", 
        emoji: this.client.config.emojis.delete_channels
      }, {
        label: this.client.language.delete.buttons.delete_roles,
        value: "delete_roles",
        emoji: this.client.config.emojis.delete_roles
      }, {
        label: this.client.language.delete.buttons.delete_emojis,
        value: "delete_emojis",
        emoji: this.client.config.emojis.delete_emojis
      }];
      
      let sMenu = new Discord.StringSelectMenuBuilder()
        .setCustomId("category_select")
        .setPlaceholder(this.client.config.setup.delete_placeholder)
        .addOptions(options);

      let row = new Discord.ActionRowBuilder()
        .addComponents(sMenu);

      let m = await message.channel.send({ embeds: [embed], components: [row] });

      const filter = (i) => i.customId == "category_select" && i.user.id == message.author.id;
      let collector = m.createMessageComponentCollector({ filter, componentType: "SELECT_MENU", time: 120000 });

      callData.embed = embed;
      callData.row = row;
      callData.m = m;
      callData.toggleComponents = toggleComponents;

      collector.on("collect", async(b) => {
        await b.deferUpdate();
        let value = b.values[0];
        await toggleComponents(m);
        if(value == "delete_all") {
          message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.delete.deleting_all, this.client.embeds.success_color)]});
          await this.client.utils.deleteRoles(this.client, message, callData, true);
          setTimeout(async() => {
            await this.client.utils.deleteEmojis(this.client, message, callData, true); 
            await this.client.utils.deleteChannels(this.client, message, callData, true);
          }, 10000);
        } else if(value == "delete_channels") {
          message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.delete.deleting_channels, this.client.embeds.success_color)]});
          await this.client.utils.deleteChannels(this.client, message, callData);
        } else if(value == "delete_roles") {
          message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.delete.deleting_roles, this.client.embeds.success_color)]});
          await this.client.utils.deleteRoles(this.client, message, callData);
        } else if(value == "delete_emojis") {
          message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.delete.deleting_emojis, this.client.embeds.success_color)]});
          await this.client.utils.deleteEmojis(this.client, message, callData);
        }
      });

      collector.on("end", (collected, reason) => {
        if(reason != "time") return;
        row.components[0].setDisabled(true);
          
        m.edit({ embeds: [embed], components: [row] });
      });
  }
  async slashRun(interaction, args) {
    let embed = new Discord.EmbedBuilder()
      .setColor(this.client.embeds.setup.delete.color);
    
    if(this.client.embeds.setup.delete.title) embed.setTitle(this.client.embeds.setup.delete.title);
    
    if(this.client.embeds.setup.delete.description) embed.setDescription(this.client.embeds.setup.delete.description);
    
    let field = this.client.embeds.setup.delete.fields;
    for(let i = 0; i < this.client.embeds.setup.delete.fields.length; i++) {
      embed.addFields([{ name: field[i].title, value: field[i].description }]);
    }

    if(this.client.embeds.setup.delete.footer == true) embed.setFooter({ text: interaction.user.username, iconURL: interaction.user.displayAvatarURL() }).setTimestamp();
    if(this.client.embeds.setup.delete.thumbnail == true) embed.setThumbnail(interaction.guild.iconURL());

    const options = [{
      label: this.client.language.delete.buttons.delete_all,
      value: "delete_all",
      emoji: this.client.config.emojis.delete_all
      }, {
      label: this.client.language.delete.buttons.delete_channels,
      value: "delete_channels",
      emoji: this.client.config.emojis.delete_channels
      }, {
      label: this.client.language.delete.buttons.delete_roles,
      value: "delete_roles",
      emoji: this.client.config.emojis.delete_roles
      }, {
      label: this.client.language.delete.buttons.delete_emojis,
      value: "delete_emojis",
      emoji: this.client.config.emojis.delete_emojis
      }];

    let sMenu = new Discord.StringSelectMenuBuilder()
      .setCustomId("category_select")
      .setPlaceholder(this.client.config.setup.delete_placeholder)
      .addOptions(options);

    let row = new Discord.ActionRowBuilder()
      .addComponents(sMenu);

      let m = await interaction.reply({ embeds: [embed], components: [row] });

      const filter = (i) => i.customId == "category_select" && i.user.id == interaction.user.id;
      let collector = m.createMessageComponentCollector({ filter, componentType: "SELECT_MENU", time: 120000 });

      callData.embed = embed;
      callData.row = row;
      callData.m = m;
      callData.toggleComponents = toggleComponents;

      collector.on("collect", async(b) => {
        await b.deferUpdate();
        let value = b.values[0];
        await toggleComponents(m);
        if(value == "delete_all") {
          interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.delete.deleting_all, this.client.embeds.success_color)]});
          await this.client.utils.deleteRoles(this.client, interaction, callData, true);
          setTimeout(async() => {
            await this.client.utils.deleteEmojis(this.client, interaction, callData, true);
            await this.client.utils.deleteChannels(this.client, interaction, callData, true);
          }, 10000);
        } else if(value == "delete_channels") {
          interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.delete.deleting_channels, this.client.embeds.success_color)]});
          await this.client.utils.deleteChannels(this.client, interaction, callData);
        } else if(value == "delete_roles") {
          interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.delete.deleting_roles, this.client.embeds.success_color)]});
          await this.client.utils.deleteRoles(this.client, interaction, callData);
        } else if(value == "delete_emojis") {
          interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.delete.deleting_emojis, this.client.embeds.success_color)]});
          await this.client.utils.deleteEmojis(this.client, interaction, callData);
        }
      });

      collector.on("end", (collected, reason) => {
        if(reason != "time") return;
        row.components[0].setDisabled(true);
          
        m.edit({ embeds: [embed], components: [row] });
      });
  }
};

const toggleComponents = async(m) => {
  let disabledComponents = callData.row.components.map(c => c.setDisabled(!c.disabled));
  let newRow = new Discord.ActionRowBuilder()
    .addComponents(disabledComponents);

  await m.edit({ embeds: [callData.embed], components: [newRow] });
}
