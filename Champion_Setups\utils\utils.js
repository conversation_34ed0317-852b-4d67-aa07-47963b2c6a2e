const chalk = require("chalk");
const fs = require("fs");
const Discord = require("discord.js");
const jsdom = require("jsdom");
const { JSDOM } = jsdom;
const dom = new JSDOM();
const document = dom.window.document;

const commandsList = (client, category) => {
  let commands = client.commands.filter(
    c => c.category == category && c.listed === true
  );
  let content = "";
  
  commands.forEach(
    c => (content += `\`${c.name}\`, `)
  );
  
  return content.slice(0, -2);
}

const formatTime = (ms) => {
  let roundNumber = ms > 0 ? Math.floor : Math.ceil;
  let days = roundNumber(ms / 86400000),
  hours = roundNumber(ms / 3600000) % 24,
  mins = roundNumber(ms / 60000) % 60,
  secs = roundNumber(ms / 1000) % 60;
  var time = (days > 0) ? `${days}d ` : "";
  time += (hours > 0) ? `${hours}h ` : "";
  time += (mins > 0) ? `${mins}m ` : "";
  time += (secs > 0) ? `${secs}s` : "0s";
  return time;
}

const capitalizeFirstLetter = (string) => {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

const findChannel = (guild, channel) => {
  if(channel == "") return undefined;

  return guild.channels.cache.find(ch => ch.name.toLowerCase().includes(`${channel}`.toLowerCase())) || guild.channels.cache.get(channel);
}

const findRole = (guild, role) => {
  if(role == "") return undefined;

  return guild.roles.cache.find(r => r.name.toLowerCase().includes(`${role}`.toLowerCase())) || guild.roles.cache.get(role);
}

const hasRole = (client, guild, member, roles, checkEmpty = false) => {
  if (checkEmpty == true && roles.length == 0) return true;

  let arr = roles.map((x, i) => {
    let findPerm = client.utils.findRole(guild, x.toLowerCase());
    if (!findPerm) return false;
    if (member.roles.cache.has(findPerm.id)) return true;

    return false;
  });
  if (checkEmpty == true && arr.length == 0) return true;

  return arr.includes(true) ? true : false;
}

const parseArgs = (args, options) => {
  if (!options) return args

  if (typeof options === 'string') options = [options]

  const optionValues = {}

  let i
  for (i = 0; i < args.length; i++) {
    const arg = args[i]
    if (!arg.startsWith('-')) break;

    const label = arg.substr(1)

    if (options.indexOf(label + ':') > -1) {
      const leftover = args.slice(i + 1).join(' ')
      const matches = leftover.match(/^"(.+?)"/)
      if (matches) {
        optionValues[label] = matches[1]
        i += matches[0].split(' ').length
      } else {
        i++
        optionValues[label] = args[i]
      }
    } else if (options.indexOf(label) > -1) {
      optionValues[label] = true
    } else {
      break
    }
  }

  return {
    options: optionValues,
    leftover: args.slice(i)
  }
}

const usage = (client, validUsage) => {
  let embed = client.embedBuilder(client, client.embeds.title, client.language.general.usage.replace("<usage>", validUsage), client.embeds.error_color);
  return embed;
}

const sendError = (error) => {
  console.log(chalk.red("[ERROR] ") + chalk.white(error));

  let errorMessage = `[${new Date().toLocaleString()}] [ERROR] ${error}\n`;
  
  fs.appendFile("./info.txt", errorMessage, (e) => { 
    if(e) console.log(e);
  });
}

const sendWarn = (warn) => {
  console.log(chalk.keyword("orange")("[WARNING] ") + chalk.white(warn));

  let warnMessage = `[${new Date().toLocaleString()}] [WARN] ${warn}\n`;
  
  fs.appendFile("./info.txt", warnMessage, (e) => { 
    if(e) console.log(e);
  });
}

const sendInfo = (info) => {
  console.log(chalk.blue("[INFO] ") + chalk.white(info));
}

const createChannels = async (client, message, template, { toggleComponents, m }, all = false) => {
  let findTemplate = client.template(template);

  if (findTemplate) {
    return new Promise(async (resolve) => {
      setTimeout(async () => {
        findTemplate.channels.forEach(async (ch, i) => {
          let parent = ch.split("|")[0].trim();
          let name = ch.split("|")[1].trim();
          let type = ch.split("|")[2].trim();
          let perm = ch.split("|")[3].trim();
          let topic = ch.split("|")[4];

          let permList = findTemplate.channelPermissions[perm];

          let permArr = [];
          if(perm != null && permList) {
            permList.forEach((p) => {
              let obj = {};
              let permObj = {};
              obj.role = client.utils.findRole(message.guild, p.role);
              p.allow.map(x => permObj[x] = true);
              p.deny.map(x => permObj[x] = false);
              obj.permissions = permObj;
              permArr.push(obj);
            });
          }

          message.guild.channels.create({ name, type: getChannelType(type) }).then(ch => {
            ch.setPosition(i);
            if (perm != "null" && permArr.length > 0) {
              permArr.forEach((a) => {
                ch.permissionOverwrites.create(a.role, a.permissions);
              });
            }
            if (topic != "null" && type == "TEXT") {
              ch.setTopic(topic);
            }
            if (parent != "null" && type != "CATEGORY") {
              let chCategory = message.guild.channels.cache.find(ct => ct.name == parent);
              setTimeout(async () => {
                ch.setParent(chCategory);
              }, 1000);
            }
          });
        });
        if(all == false) {
          message.channel.send({ embeds: [client.embedBuilder(client, 
              client.embeds.title, client.language.setup.created_channels, client.embeds.success_color)]});
        } else if(all == true) {
          message.channel.send({ embeds: [client.embedBuilder(client, 
              client.embeds.title, client.language.setup.created_all, client.embeds.success_color)]});
        }
        all == true ? null : await toggleComponents(m);
        resolve();
      }, 3000);
    })
  }
}

const createRoles = async (client, message, template, { toggleComponents, m }, all = false) => {
  let findTemplate = client.template(template);

  if (findTemplate) {
    return new Promise(async (resolve) => {
      setTimeout(async () => {
        await findTemplate.roles.forEach(async (r, i) => {
          await message.guild.roles.create({
            name: r.name,
            color: r.color,
            hoist: r.hoisted
          }).then((ro) => {
            let defaultPermissions = [
              'CreateInstantInvite',
              'AddReactions',
              'Stream',
              'ViewChannel',
              'SendMessages',
              'SendTTSMessages',
              'EmbedLinks',
              'AttachFiles',
              'ReadMessageHistory',
              'MentionEveryone',
              'UseExternalEmojis',
              'Connect',
              'Speak',
              'UseVad',
              'ChangeNickname'
            ];
            if(r.default) {
              let joinedPerms = defaultPermissions.concat(r.permissions);
              ro.setPermissions(joinedPerms)
            } else {
              ro.setPermissions(r.permissions)
            }
          }).catch(err => {});
        });
        if(all == false) {
          message.channel.send({ embeds: [client.embedBuilder(client, 
            client.embeds.title, client.language.setup.created_roles, client.embeds.success_color)]});
        }
        all == true ? null : await toggleComponents(m);
        resolve();
      }, 3000);
    })
  }
}

const createEmojis = async (client, message, template, { toggleComponents, m }, all = false) => {
  let findTemplate = client.template(template);

  if (findTemplate) {
    return new Promise(async (resolve) => {
      setTimeout(async () => {
        await findTemplate.emojis.forEach(async (e) => {
          await message.guild.emojis.create(e.url, e.name).catch(err => {});
        });
        if(all == false) {
          message.channel.send({ embeds: [client.embedBuilder(client, 
            client.embeds.title, client.language.setup.created_emojis, client.embeds.success_color)]});
        }
        all == true ? null : await toggleComponents(m);
        resolve();
      }, 3000);
    })
  }
}

const createExtras = async(client, message, template, { toggleComponents, m }, all = false) => {
  let findTemplate = client.template(template);

  if (findTemplate) {
    return new Promise(async (resolve) => {
      setTimeout(async () => {
        if(message.guild.features.includes(Discord.GuildFeature.Banner) && findTemplate.extra.banner != null) {
          message.guild.setBanner(findTemplate.extra.banner);
        }
        if(findTemplate.extra.icon != null) {
          message.guild.setIcon(findTemplate.extra.icon);
        }
        if(findTemplate.extra.verificationLevel) {
          message.guild.setVerificationLevel(findTemplate.extra.verificationLevel);
        }
        all == true ? null : await toggleComponents(m);
        resolve();
      }, 1500);
    })
  }
}

const deleteChannels = async(client, message, { toggleComponents, m }, all = false) => {
  return new Promise(async (resolve) => {
    await message.guild.channels.cache.forEach(async(c) => {
      if(c.id == message.channel.id) return;
      await c.delete();
    });
    if(all == false) {
      message.channel.send({ embeds: [client.embedBuilder(client, 
        client.embeds.title, client.language.delete.deleted_channels, client.embeds.success_color)]});
    } else if(all == true) {
      message.channel.send({ embeds: [client.embedBuilder(client, 
        client.embeds.title, client.language.delete.deleted_all, client.embeds.success_color)]});
    }
    all == true ? null : await toggleComponents(m);
    resolve();
  });
}

const deleteRoles = async(client, message, { toggleComponents, m }, all = false) => {
  return new Promise(async (resolve) => {
    await message.guild.roles.cache.forEach(async(r) => {
      if(r.id == message.guild.id) return;
      try {
        await r.delete();
      } catch (e) { 
        this.client.utils.sendError(`Couldn't remove role ${r.name} (Higher Role / Administrator Permissions)`);
      }
    });
    if(all == false) {
      message.channel.send({ embeds: [client.embedBuilder(client, 
        client.embeds.title, client.language.delete.deleted_roles, client.embeds.success_color)]});
    }
    all == true ? null : await toggleComponents(m);
    resolve();
  });
}

const deleteEmojis = async(client, message, { toggleComponents, m }, all = false) => {
  if(message.guild.emojis.cache.size == 0) return;
  return new Promise(async (resolve) => {
    await message.guild.emojis.cache.forEach(async(e) => {
      await e.delete();
    });
    if(all == false) {
      message.channel.send({ embeds: [client.embedBuilder(client, 
        client.embeds.title, client.language.delete.deleted_emojis, client.embeds.success_color)]});
    }
    all == true ? null : await toggleComponents(m);
    resolve();
  });
}

const getChannelType = (type) => {
  let chType = Discord.ChannelType.GuildText;
  if(type == "TEXT") {
    chType = Discord.ChannelType.GuildText;
  } else if(type == "VOICE") {
    chType = Discord.ChannelType.GuildVoice;
  } else if(type == "CATEGORY") {
    chType = Discord.ChannelType.GuildCategory;
  }

  return chType;
}

const generateTranscript = async (channel, msgs) => {
  let data = await fs.readFileSync('./data/template.html', {
    encoding: 'utf-8'
  });
  await fs.writeFileSync(`data/transcripts/channel-${channel.id}.html`, data)
  let guildElement = document.createElement('div');

  let guildNameEl = document.createElement("span");
  let guildText = document.createTextNode(channel.guild.name);

  let openEl = document.createElement("span");
  let openText = document.createTextNode('Channel Name: ' + channel.name)
  openEl.appendChild(openText);
  openEl.style = `display: flex; padding-top: 15px; font-size: 15px;`

  let closeEl = document.createElement("span");
  let closeText = document.createTextNode('Transcript Date: ' + new Date().toLocaleString() || 'N/A')
  closeEl.appendChild(closeText);
  closeEl.style = `display: flex; padding-top: 5px; font-size: 15px;`

  guildNameEl.appendChild(guildText);
  guildNameEl.appendChild(openEl)
  guildNameEl.appendChild(closeEl)
  guildNameEl.style = `margin-left: 43px`
  guildNameEl.style = `margin-top: 45px`

  let guildImg = document.createElement('img');
  guildImg.setAttribute('src', channel.guild.iconURL());
  guildImg.setAttribute('width', '128');
  guildImg.className = "guild-image";
  guildElement.appendChild(guildImg);
  guildElement.appendChild(guildNameEl);
  guildElement.style = "display: flex"
  await fs.appendFileSync(`data/transcripts/channel-${channel.id}.html`, guildElement.outerHTML, (err) => {
    if(err) console.log(err)
  });

  for(const msg of msgs) {
    let parentContainer = document.createElement("div");
    parentContainer.className = "parent-container";

    let avatarDiv = document.createElement("div");
    avatarDiv.className = "avatar-container";
    let img = document.createElement('img');
    img.setAttribute('src', msg.author.displayAvatarURL());
    img.className = "avatar";
    avatarDiv.appendChild(img);

    parentContainer.appendChild(avatarDiv);

    let messageContainer = document.createElement('div');
    messageContainer.className = "message-container";

    let nameElement = document.createElement("span");
    let name = document.createTextNode(`${msg.author.tag} `)
    let dateSpan = document.createElement("span");
    let dateText = document.createTextNode(`${msg.createdAt.toLocaleString()}`)
    dateSpan.appendChild(dateText)
    dateSpan.style = `font-size: 12px; color: #c4c4c4;`
    nameElement.appendChild(name);
    nameElement.appendChild(dateSpan)
    nameElement.style = `padding-bottom: 10px`
    messageContainer.append(nameElement);

    if(msg.content.startsWith("```")) {
      let m = msg.content.slice(3, -3);
      let codeNode = document.createElement("code");
      let textNode = document.createTextNode(m);
      codeNode.appendChild(textNode);
      messageContainer.appendChild(codeNode);
    } 

    let msgNode = document.createElement('span');
    if(msg.content) 
      msgNode.innerHTML = replaceFormatting(channel.guild, msg.content);

    if(msg.attachments && msg.attachments.size > 0 && config.general.save_images == true) {
      for (const attachment of Array.from(msg.attachments.values())) {
        const attDiv = document.createElement("div");
        attDiv.classList.add("chat-image");

        const attachmentType = (attachment.name ?? 'unknown.png')
          .split('.')
          .pop()
          .toLowerCase();

        const formats = ["png", "jpg", "jpeg", "webp", "gif"];
        if(formats.includes(attachmentType)) {
          const attLink = document.createElement("a");
          const attImg = document.createElement("img");

          attImg.classList.add("chat-media");
          attImg.src = await getImage(attachment.proxyURL ?? attachment.url) ?? attachment.proxyURL ?? attachment.url;

          attImg.alt = "Transcript Image";

          attLink.appendChild(attImg);
          attDiv.appendChild(attLink);

          msgNode.appendChild(attDiv)
        }
      }
    }

    messageContainer.appendChild(msgNode);
    
    if(msg.embeds[0]) {
      let fields = [];
      if(msg.embeds[0].data.fields) {
        for (let i = 0; i < msg.embeds[0].data.fields.length; i++) {
          fields.push(
            `<b><font size="+1">${msg.embeds[0].data.fields[i].name}</font></b><br>${msg.embeds[0].data.fields[i].value}<br>`
          )
        }
      }
      let msgEmbed = msg.embeds[0];
      let embedNode = document.createElement("div");
      embedNode.className = "embed";

      let colorNode = document.createElement("div");
      colorNode.className = "embed-color";
      colorNode.style = `background-color: #${Number(msgEmbed.data.color).toString(16)}`;
      embedNode.appendChild(colorNode);
      let embedContent = document.createElement("div");
      embedContent.className = "embed-content";

      let titleNode = document.createElement("span");
      titleNode.className = "embed-title";
      titleNode.innerHTML = msgEmbed.data.title;
      embedContent.appendChild(titleNode);

      if(msgEmbed.data.fields) {
        if(!msgEmbed.data.description) msgEmbed.data.description = "";
        let descNode = document.createElement("span");
        descNode.className = "embed-description";
        descNode.innerHTML = replaceFormatting(channel.guild, msgEmbed.data.description) + "<br><br>" + fields.join("<br>");
        embedContent.appendChild(descNode);
      } else {
        if(!msgEmbed.data.description) msgEmbed.data.description = "";
        let descNode = document.createElement("span");
        descNode.className = "embed-description";
        descNode.innerHTML = replaceFormatting(channel.guild, msgEmbed.data.description);
        embedContent.appendChild(descNode);
      }
      embedNode.appendChild(embedContent);
      messageContainer.append(embedNode);
    }

    parentContainer.appendChild(messageContainer);
    await fs.appendFileSync(`data/transcripts/channel-${channel.id}.html`, parentContainer.outerHTML, (err) => {
      if(err) console.log(err)
    });
  };
}

const replaceFormatting = (guild, text) => {
  return text.replaceAll(/\*\*(.+)\*\*/g, '<b>$1</b>')
    .replaceAll(/\*\*\*(.+)\*\*\*/g, "<i><b>$1</b></i>")
    .replaceAll(/\*(.\n+)\*/g, "<i>$1</i>")
    .replaceAll(/\n/g, "<br>")
    .replaceAll(/<@\d{18}>/g, (user) => guild.members.cache.get(user.match(/\d+/) ? user.match(/\d+/)[0] : '')?.user.tag || 'invalid-user')
    .replaceAll(/<@&\d{18}>/g, (role) => guild.roles.cache.get(role.match(/\d+/) ? role.match(/\d+/)[0] : '')?.name || 'deleted-role')
    .replaceAll(/<#\d{18}>/g, (channel) => guild.channels.cache.get(channel.match(/\d+/) ? channel.match(/\d+/)[0] : '')?.name || 'deleted-channel')
    .replaceAll(/<:(.+):(\d+)>/g, (a, b, c) => `<img src="https://cdn.discordapp.com/emojis/${c}.webp?size=96&quality=lossless" width="${(/^<:(.+):(\d+)>$/).test(text) ? "48px" : "22px"}" height="${(/^<:(.+):(\d+)>$/).test(text) ? "48px" : "22px"}">`)
    .replaceAll(/<a:(.+):(\d+)>/g, (a, b, c) => `<img src="https://cdn.discordapp.com/emojis/${c}.gif?size=96&quality=lossless" width="${(/^<a:(.+):(\d+)>$/).test(text) ? "48px" : "22px"}" height="${(/^<a:(.+):(\d+)>$/).test(text) ? "48px" : "22px"}">`);
}

module.exports = {
  commandsList,
  findChannel,
  findRole,
  createChannels, 
  createRoles, 
  createEmojis, 
  createExtras,
  deleteChannels, 
  deleteRoles, 
  deleteEmojis, 
  usage, 
  sendInfo, 
  sendWarn, 
  sendError, 
  hasRole, 
  capitalizeFirstLetter, 
  formatTime, 
  parseArgs,
  generateTranscript
}
