const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildEmojiUpdate,
    async execute(oldEmoji, newEmoji, client) {
        try {
            // Only handle roles changes
            const oldRoles = oldEmoji.roles.cache;
            const newRoles = newEmoji.roles.cache;
            
            // Check if roles actually changed
            if (oldRoles.size === newRoles.size && 
                oldRoles.every(role => newRoles.has(role.id))) return;
            
            console.log(`🎭 Emoji roles updated: ${newEmoji.name} in ${newEmoji.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newEmoji.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EMOJI_ROLES_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EMOJI_ROLES_UPDATE',
                user: 'System',
                userId: null,
                action: 'Roles emoji được cập nhật',
                details: `Roles của emoji **${newEmoji.name}** đã được cập nhật`,
                target: newEmoji.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the emoji roles from audit logs
            try {
                const auditLogs = await newEmoji.guild.fetchAuditLogs({
                    type: 61, // EMOJI_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newEmoji.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Roles emoji được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for emoji roles update');
            }
            
            // Add to database
            await client.db.addAuditLog(newEmoji.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🎭 Roles emoji được cập nhật',
                `Vừa có roles emoji được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Emoji',
                    value: `- ${newEmoji} (:${newEmoji.name}:)`,
                    inline: false
                },
                {
                    name: '> ID emoji',
                    value: `- ${newEmoji.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Calculate role changes
            const addedRoles = newRoles.filter(role => !oldRoles.has(role.id));
            const removedRoles = oldRoles.filter(role => !newRoles.has(role.id));
            
            // Add roles information
            if (oldRoles.size > 0) {
                const oldRolesList = oldRoles.map(role => role.name).join(', ');
                embed.addFields({
                    name: '> Roles cũ',
                    value: `- ${oldRolesList}`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> Roles cũ',
                    value: `- Tất cả thành viên có thể sử dụng`,
                    inline: false
                });
            }
            
            if (newRoles.size > 0) {
                const newRolesList = newRoles.map(role => role.name).join(', ');
                embed.addFields({
                    name: '> Roles mới',
                    value: `- ${newRolesList}`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> Roles mới',
                    value: `- Tất cả thành viên có thể sử dụng`,
                    inline: false
                });
            }
            
            // Show specific changes
            if (addedRoles.size > 0) {
                const addedRolesList = addedRoles.map(role => role.name).join(', ');
                embed.addFields({
                    name: '> ✅ Roles được thêm',
                    value: `- ${addedRolesList}`,
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for addition
            }
            
            if (removedRoles.size > 0) {
                const removedRolesList = removedRoles.map(role => role.name).join(', ');
                embed.addFields({
                    name: '> ❌ Roles được gỡ bỏ',
                    value: `- ${removedRolesList}`,
                    inline: false
                });
                if (addedRoles.size === 0) {
                    embed.setColor(0xe74c3c); // Red for removal only
                }
            }
            
            // Add impact information
            if (newRoles.size === 0 && oldRoles.size > 0) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: `- Emoji giờ đây có thể được sử dụng bởi tất cả thành viên`,
                    inline: false
                });
            } else if (newRoles.size > 0 && oldRoles.size === 0) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: `- Emoji giờ đây chỉ có thể được sử dụng bởi các roles được chỉ định`,
                    inline: false
                });
            } else if (newRoles.size > oldRoles.size) {
                embed.addFields({
                    name: '> 📊 Tác động',
                    value: `- Thêm roles có thể sử dụng emoji này`,
                    inline: false
                });
            } else if (newRoles.size < oldRoles.size) {
                embed.addFields({
                    name: '> 📊 Tác động',
                    value: `- Giảm roles có thể sử dụng emoji này`,
                    inline: false
                });
            }
            
            // Add emoji info
            embed.addFields([
                {
                    name: '> Loại emoji',
                    value: `- ${newEmoji.animated ? 'Animated' : 'Static'}`,
                    inline: true
                },
                {
                    name: '> Số roles hiện tại',
                    value: `- ${newRoles.size} roles`,
                    inline: true
                },
                {
                    name: '> Được quản lý',
                    value: `- ${newEmoji.managed ? 'Có (bởi app)' : 'Không'}`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set emoji as thumbnail
            embed.setThumbnail(newEmoji.url);
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newEmoji.guild.name} • Emoji Roles Update`,
                iconURL: newEmoji.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Emoji roles update logged: ${newEmoji.name}`);
            
        } catch (error) {
            console.error('Error in emojiRolesUpdate audit log:', error);
        }
    }
};
