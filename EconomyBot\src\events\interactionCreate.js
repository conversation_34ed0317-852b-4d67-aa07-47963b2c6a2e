const { Events } = require('discord.js');
const { createErrorEmbed } = require('../utils/embedBuilder');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        // Xử lý slash commands
        if (interaction.isChatInputCommand()) {
            const command = interaction.client.commands.get(interaction.commandName);

            if (!command) {
                console.error(`❌ Không tìm thấy command: ${interaction.commandName}`);
                return;
            }

            // Kiểm tra cooldown
            const { cooldowns } = interaction.client;
            if (!cooldowns.has(command.data.name)) {
                cooldowns.set(command.data.name, new Map());
            }

            const now = Date.now();
            const timestamps = cooldowns.get(command.data.name);
            const defaultCooldownDuration = 3;
            const cooldownAmount = (command.cooldown ?? defaultCooldownDuration) * 1000;

            if (timestamps.has(interaction.user.id)) {
                const expirationTime = timestamps.get(interaction.user.id) + cooldownAmount;

                if (now < expirationTime) {
                    const expiredTimestamp = Math.round(expirationTime / 1000);
                    const errorEmbed = createErrorEmbed(
                        'Cooldown',
                        `<PERSON>ạn cần đợi <t:${expiredTimestamp}:R> trước khi sử dụng lại lệnh \`${command.data.name}\`.`
                    );
                    return interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
            }

            timestamps.set(interaction.user.id, now);
            setTimeout(() => timestamps.delete(interaction.user.id), cooldownAmount);

            // Thực thi command
            try {
                await command.execute(interaction);
            } catch (error) {
                console.error(`❌ Lỗi thực thi command ${interaction.commandName}:`, error);
                
                const errorEmbed = createErrorEmbed(
                    'Lỗi hệ thống',
                    'Đã xảy ra lỗi khi thực hiện lệnh. Vui lòng thử lại sau.'
                );

                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
                } else {
                    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
            }
        }

        // Xử lý button interactions
        if (interaction.isButton()) {
            // Có thể thêm xử lý button ở đây nếu cần
        }

        // Xử lý select menu interactions
        if (interaction.isStringSelectMenu()) {
            // Có thể thêm xử lý select menu ở đây nếu cần
        }
    },
};
