const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require("discord.js");
const fs = require("fs");
const path = require("path");

const premiumFile = path.join(__dirname, "../../premium.json");

// Read premium data
function readPremiumData() {
    if (!fs.existsSync(premiumFile)) {
        fs.writeFileSync(premiumFile, JSON.stringify({ users: {} }, null, 2));
    }
    return JSON.parse(fs.readFileSync(premiumFile));
}

module.exports = {
    name: "premiumcheck",
    description: "Check if a user has premium access",
    execute(message, args) {
        let user = message.mentions.users.first() || message.guild.members.cache.get(args[0]) || message.author;
        let data = readPremiumData();
        let userPremium = data.users[user.id];

        if (userPremium) {
            let expiry = userPremium.expiry;
            if (expiry < Math.floor(Date.now() / 1000)) {
                delete data.users[user.id];
                fs.writeFileSync(premiumFile, JSON.stringify(data, null, 2));
                userPremium = null;
            }
        }

        if (userPremium) {
            const premiumEmbed = new EmbedBuilder()
                .setColor("#FFD700")
                .setTitle("🌟 Premium Status")
                .setDescription(`**${user.username}** has premium until <t:${userPremium.expiry}:R>.`);

            message.reply({ embeds: [premiumEmbed] });
        } else {
            const buyPremiumEmbed = new EmbedBuilder()
                .setColor("#FF0000")
                .setTitle("❌ No Premium Access!")
                .setDescription(`**${user.username}** doesn't have premium. Buy premium for exclusive benefits!`);

            const row = new ActionRowBuilder().addComponents(
                new ButtonBuilder()
                    .setLabel("Buy Premium")
                    .setStyle(ButtonStyle.Link)
                    .setURL("https://discord.gg/5ZJ9TVNafR")
            );

            message.reply({ embeds: [buyPremiumEmbed], components: [row] });
        }
    }
};