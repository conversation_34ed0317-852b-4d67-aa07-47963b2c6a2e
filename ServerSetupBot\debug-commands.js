const fs = require('fs');
const path = require('path');

// Function to check command options order
function checkCommandOptions(commandData, filePath) {
    const errors = [];
    
    function checkOptions(options, parentName = '') {
        let foundNonRequired = false;
        
        for (let i = 0; i < options.length; i++) {
            const option = options[i];
            const optionName = `${parentName}${option.name}`;
            
            if (option.required === false || option.required === undefined) {
                foundNonRequired = true;
            } else if (option.required === true && foundNonRequired) {
                errors.push({
                    file: filePath,
                    option: optionName,
                    position: i + 1,
                    error: 'Required option after non-required option'
                });
            }
            
            // Check subcommands and subcommand groups
            if (option.options && option.options.length > 0) {
                checkOptions(option.options, `${optionName}.`);
            }
        }
    }
    
    if (commandData.options) {
        checkOptions(commandData.options);
    }
    
    return errors;
}

// Function to scan all command files
function scanCommands() {
    const commandsDir = path.join(__dirname, 'src', 'commands');
    const allErrors = [];
    let commandCount = 0;
    
    function scanDirectory(dir) {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const itemPath = path.join(dir, item);
            const stat = fs.statSync(itemPath);
            
            if (stat.isDirectory()) {
                scanDirectory(itemPath);
            } else if (item.endsWith('.js')) {
                try {
                    const command = require(itemPath);
                    if (command.data && command.data.toJSON) {
                        const commandData = command.data.toJSON();
                        const errors = checkCommandOptions(commandData, itemPath);
                        
                        if (errors.length > 0) {
                            allErrors.push(...errors);
                        }
                        
                        commandCount++;
                        console.log(`✓ Checked: ${path.relative(__dirname, itemPath)} - ${commandData.name}`);
                    }
                } catch (error) {
                    console.error(`❌ Error loading ${itemPath}:`, error.message);
                }
            }
        }
    }
    
    console.log('🔍 Scanning commands for option order issues...\n');
    scanDirectory(commandsDir);
    
    console.log(`\n📊 Scan complete: ${commandCount} commands checked`);
    
    if (allErrors.length > 0) {
        console.log(`\n❌ Found ${allErrors.length} option order errors:\n`);
        
        allErrors.forEach((error, index) => {
            console.log(`${index + 1}. File: ${path.relative(__dirname, error.file)}`);
            console.log(`   Option: ${error.option} (position ${error.position})`);
            console.log(`   Error: ${error.error}\n`);
        });
    } else {
        console.log('\n✅ No option order errors found!');
    }
    
    return allErrors;
}

// Run the scan
if (require.main === module) {
    scanCommands();
}

module.exports = { scanCommands, checkCommandOptions };
