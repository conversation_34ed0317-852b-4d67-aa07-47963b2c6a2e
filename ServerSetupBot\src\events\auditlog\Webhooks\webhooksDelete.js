const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'webhookDelete', // Custom event name
    async execute(webhook, client) {
        try {
            console.log(`🗑️ Webhook deleted: ${webhook.name} in ${webhook.guild?.name || 'Unknown'}`);
            
            // Skip if no guild (DM webhooks)
            if (!webhook.guild) return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(webhook.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'WEBHOOKS_DELETE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'WEBHOOKS_DELETE',
                user: 'System',
                userId: null,
                action: 'Webhook được xóa',
                details: `Webhook **${webhook.name}** đã được xóa`,
                target: webhook.name,
                channel: webhook.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the webhook from audit logs
            try {
                const auditLogs = await webhook.guild.fetchAuditLogs({
                    type: 52, // WEBHOOK_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === webhook.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Webhook được xóa bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for webhook delete');
            }
            
            // Add to database
            await client.db.addAuditLog(webhook.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Webhook được xóa',
                `Vừa có một webhook được xóa khỏi server`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            // Get webhook type names
            const webhookTypes = {
                1: 'Incoming Webhook',
                2: 'Channel Follower Webhook',
                3: 'Application Webhook'
            };
            
            embed.addFields([
                {
                    name: '> Tên webhook',
                    value: `- ${webhook.name || 'Unnamed Webhook'}`,
                    inline: true
                },
                {
                    name: '> ID webhook',
                    value: `- ${webhook.id}`,
                    inline: true
                },
                {
                    name: '> Loại webhook',
                    value: `- ${webhookTypes[webhook.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh đã thiết lập',
                    value: `- ${webhook.channel?.name || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian xóa',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add webhook owner info if available
            if (webhook.owner) {
                embed.addFields({
                    name: '> Chủ sở hữu đã có',
                    value: `- ${webhook.owner.tag || webhook.owner.name}`,
                    inline: true
                });
            }
            
            // Add application info if it was an application webhook
            if (webhook.application) {
                embed.addFields({
                    name: '> Ứng dụng đã liên kết',
                    value: `- ${webhook.application.name}`,
                    inline: true
                });
            }
            
            // Add creation info if available
            if (webhook.createdTimestamp) {
                embed.addFields({
                    name: '> Được tạo lúc',
                    value: `- <t:${Math.floor(webhook.createdTimestamp / 1000)}:F>`,
                    inline: true
                });
                
                const lifetime = Date.now() - webhook.createdTimestamp;
                const lifetimeDays = Math.floor(lifetime / (1000 * 60 * 60 * 24));
                embed.addFields({
                    name: '> Thời gian tồn tại',
                    value: `- ${lifetimeDays} ngày`,
                    inline: true
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do xóa',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set webhook avatar as thumbnail if available
            if (webhook.avatarURL()) {
                embed.setThumbnail(webhook.avatarURL({ dynamic: true, size: 256 }));
            }
            
            // Add impact note
            embed.addFields({
                name: '> Tác động',
                value: '- Webhook này không thể gửi tin nhắn nữa',
                inline: false
            });
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${webhook.guild.name} • Webhook Delete`,
                iconURL: webhook.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Webhook delete logged: ${webhook.name}`);
            
        } catch (error) {
            console.error('Error in webhooksDelete audit log:', error);
        }
    }
};
