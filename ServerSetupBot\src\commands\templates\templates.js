const {
  SlashCommandBuilder,
  <PERSON>bed<PERSON><PERSON>er,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  StringSelectMenuBuilder,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
} = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
  createLoadingEmbed,
} = require("../../utils/embedBuilder.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("templates")
    .setDescription("Quản lý templates server toàn diện")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("browse")
        .setDescription("Duyệt templates có sẵn")
        .addStringOption((option) =>
          option
            .setName("category")
            .setDescription("Danh mục template")
            .setRequired(false)
            .addChoices(
              { name: "🎮 Gaming - Server game", value: "gaming" },
              { name: "👥 Community - Cộng đồng", value: "community" },
              { name: "💼 Business - Do<PERSON>h nghiệp", value: "business" },
              { name: "🎨 Creative - Sáng tạo", value: "creative" },
              { name: "📚 Education - Giáo dục", value: "education" },
              { name: "🎵 Music - Âm nhạc", value: "music" },
              { name: "🔧 Tech - Công nghệ", value: "tech" },
              { name: "🌟 All - Tất cả", value: "all" }
            )
        )
        .addStringOption((option) =>
          option
            .setName("sort")
            .setDescription("Sắp xếp theo")
            .setRequired(false)
            .addChoices(
              { name: "Phổ biến nhất", value: "popular" },
              { name: "Mới nhất", value: "newest" },
              { name: "Đánh giá cao", value: "rating" },
              { name: "Tên A-Z", value: "name" }
            )
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("create")
        .setDescription("Tạo template từ server hiện tại")
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên template")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("description")
            .setDescription("Mô tả template")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("category")
            .setDescription("Danh mục template")
            .setRequired(true)
            .addChoices(
              { name: "🎮 Gaming", value: "gaming" },
              { name: "👥 Community", value: "community" },
              { name: "💼 Business", value: "business" },
              { name: "🎨 Creative", value: "creative" },
              { name: "📚 Education", value: "education" },
              { name: "🎵 Music", value: "music" },
              { name: "🔧 Tech", value: "tech" }
            )
        )
        .addBooleanOption((option) =>
          option
            .setName("public")
            .setDescription("Chia sẻ công khai")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("apply")
        .setDescription("Áp dụng template vào server")
        .addStringOption((option) =>
          option
            .setName("template_id")
            .setDescription("ID hoặc tên template")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("mode")
            .setDescription("Chế độ áp dụng")
            .setRequired(false)
            .addChoices(
              { name: "🔄 Replace - Thay thế hoàn toàn", value: "replace" },
              { name: "➕ Add - Thêm vào hiện tại", value: "add" },
              { name: "🔀 Merge - Kết hợp thông minh", value: "merge" },
              { name: "👁️ Preview - Xem trước", value: "preview" }
            )
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("my")
        .setDescription("Quản lý templates của bạn")
        .addStringOption((option) =>
          option
            .setName("action")
            .setDescription("Hành động")
            .setRequired(false)
            .addChoices(
              { name: "List - Xem danh sách", value: "list" },
              { name: "Edit - Chỉnh sửa", value: "edit" },
              { name: "Delete - Xóa", value: "delete" },
              { name: "Share - Chia sẻ", value: "share" }
            )
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("search")
        .setDescription("Tìm kiếm templates")
        .addStringOption((option) =>
          option
            .setName("query")
            .setDescription("Từ khóa tìm kiếm")
            .setRequired(true)
        )
        .addBooleanOption((option) =>
          option
            .setName("advanced")
            .setDescription("Tìm kiếm nâng cao")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("featured")
        .setDescription("Templates nổi bật và được đề xuất")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("backup")
        .setDescription("Sao lưu cấu hình server hiện tại")
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên backup")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("restore")
        .setDescription("Khôi phục từ backup")
        .addStringOption((option) =>
          option
            .setName("backup_id")
            .setDescription("ID backup")
            .setRequired(true)
        )
    ),
  category: "templates",
  adminOnly: true,
  manageServer: false,

  async execute(interaction, client) {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case "browse":
        await handleBrowse(interaction, client);
        break;
      case "create":
        await handleCreate(interaction, client);
        break;
      case "apply":
        await handleApply(interaction, client);
        break;
      case "my":
        await handleMy(interaction, client);
        break;
      case "search":
        await handleSearch(interaction, client);
        break;
      case "featured":
        await handleFeatured(interaction, client);
        break;
      case "backup":
        await handleBackup(interaction, client);
        break;
      case "restore":
        await handleRestore(interaction, client);
        break;
    }
  },
};

async function handleBrowse(interaction, client) {
  const category = interaction.options.getString("category") || "all";
  const sort = interaction.options.getString("sort") || "popular";

  await interaction.deferReply();

  try {
    const templates = await getTemplates(client, category, sort);
    
    const embed = createInfoEmbed(
      `📋 Templates ${getCategoryDisplayName(category)}`,
      `Tìm thấy **${templates.length}** templates • Sắp xếp theo **${getSortDisplayName(sort)}**`
    );

    if (templates.length === 0) {
      embed.setDescription("Không tìm thấy template nào trong danh mục này.");
      return await interaction.editReply({ embeds: [embed] });
    }

    // Show first 5 templates
    const templateList = templates.slice(0, 5).map((template, index) => 
      `**${index + 1}.** ${template.name}\n` +
      `└ ${template.description}\n` +
      `└ 👤 ${template.author} • ⭐ ${template.rating}/5 • 📥 ${template.downloads.toLocaleString()}`
    ).join('\n\n');

    embed.addFields({
      name: "🎯 Templates",
      value: templateList,
      inline: false
    });

    const components = createBrowseComponents(category, sort, 0, templates.length);
    
    await interaction.editReply({ embeds: [embed], components });

  } catch (error) {
    console.error("Lỗi khi duyệt templates:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống",
      "Không thể tải danh sách templates."
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleCreate(interaction, client) {
  const name = interaction.options.getString("name");
  const description = interaction.options.getString("description");
  const category = interaction.options.getString("category");
  const isPublic = interaction.options.getBoolean("public") || false;

  // Validate inputs
  if (name.length > 50) {
    const errorEmbed = createErrorEmbed(
      "Tên quá dài!",
      "Tên template không được vượt quá 50 ký tự."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  if (description.length > 200) {
    const errorEmbed = createErrorEmbed(
      "Mô tả quá dài!",
      "Mô tả template không được vượt quá 200 ký tự."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const loadingEmbed = createLoadingEmbed(
    "🔄 Đang tạo template...",
    "Vui lòng chờ trong khi bot thu thập dữ liệu server."
  );
  await interaction.reply({ embeds: [loadingEmbed] });

  try {
    // Show creation wizard
    await showCreationWizard(interaction, client, { name, description, category, isPublic });

  } catch (error) {
    console.error("Lỗi khi tạo template:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi tạo template!",
      "Đã xảy ra lỗi khi tạo template. Vui lòng thử lại sau!"
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function showCreationWizard(interaction, client, templateInfo) {
  const embed = createInfoEmbed(
    "🧙‍♂️ Template Creation Wizard",
    `**Tên:** ${templateInfo.name}\n` +
    `**Mô tả:** ${templateInfo.description}\n` +
    `**Danh mục:** ${getCategoryDisplayName(templateInfo.category)}\n` +
    `**Công khai:** ${templateInfo.isPublic ? "Có" : "Không"}`
  );

  embed.addFields({
    name: "📋 Chọn thành phần cần lưu",
    value: "Chọn các thành phần bạn muốn bao gồm trong template:",
    inline: false
  });

  const components = createWizardComponents();
  
  await interaction.editReply({ embeds: [embed], components });

  // Handle component interactions
  const collector = interaction.channel.createMessageComponentCollector({
    filter: (i) => i.user.id === interaction.user.id,
    time: 300000 // 5 minutes
  });

  const selectedComponents = new Set();

  collector.on("collect", async (i) => {
    try {
      if (i.customId.startsWith("wizard_component_")) {
        const component = i.customId.replace("wizard_component_", "");
        
        if (selectedComponents.has(component)) {
          selectedComponents.delete(component);
        } else {
          selectedComponents.add(component);
        }

        // Update embed
        const updatedEmbed = createInfoEmbed(
          "🧙‍♂️ Template Creation Wizard",
          `**Tên:** ${templateInfo.name}\n` +
          `**Mô tả:** ${templateInfo.description}\n` +
          `**Danh mục:** ${getCategoryDisplayName(templateInfo.category)}\n` +
          `**Công khai:** ${templateInfo.isPublic ? "Có" : "Không"}`
        );

        updatedEmbed.addFields({
          name: "📋 Thành phần đã chọn",
          value: Array.from(selectedComponents).map(comp => 
            `✅ ${getComponentDisplayName(comp)}`
          ).join('\n') || "Chưa chọn thành phần nào",
          inline: false
        });

        const updatedComponents = createWizardComponents(selectedComponents);
        
        await i.update({ embeds: [updatedEmbed], components: updatedComponents });

      } else if (i.customId === "wizard_create") {
        if (selectedComponents.size === 0) {
          await i.reply({ 
            content: "❌ Vui lòng chọn ít nhất một thành phần!", 
            ephemeral: true 
          });
          return;
        }

        await i.deferUpdate();
        
        // Create template
        const templateData = await collectTemplateData(
          interaction.guild, 
          Array.from(selectedComponents), 
          client
        );

        const result = await client.db.createServerTemplate({
          name: templateInfo.name,
          description: templateInfo.description,
          category: templateInfo.category,
          data: templateData,
          createdBy: interaction.user.id,
          isPublic: templateInfo.isPublic
        });

        const successEmbed = createSuccessEmbed(
          "✅ Template đã được tạo thành công!",
          `**ID Template:** \`${result.id}\`\n` +
          `**Thành phần:** ${Array.from(selectedComponents).map(getComponentDisplayName).join(', ')}\n` +
          `**Kích thước:** ${formatBytes(JSON.stringify(templateData).length)}`
        );

        successEmbed.addFields({
          name: "📊 Thống kê",
          value: getTemplateStats(templateData),
          inline: false
        });

        if (templateInfo.isPublic) {
          successEmbed.addFields({
            name: "🌍 Template công khai",
            value: "Template này có thể được sử dụng bởi các server khác.",
            inline: false
          });
        }

        await interaction.editReply({ embeds: [successEmbed], components: [] });
        collector.stop("created");

      } else if (i.customId === "wizard_cancel") {
        await i.deferUpdate();
        const cancelEmbed = createErrorEmbed(
          "❌ Đã hủy tạo template",
          "Quá trình tạo template đã bị hủy."
        );
        await interaction.editReply({ embeds: [cancelEmbed], components: [] });
        collector.stop("cancelled");
      }

    } catch (error) {
      console.error("Lỗi trong wizard collector:", error);
    }
  });

  collector.on("end", async (collected, reason) => {
    if (reason === "time") {
      try {
        const timeoutEmbed = createErrorEmbed(
          "⏰ Hết thời gian",
          "Quá trình tạo template đã hết thời gian."
        );
        await interaction.editReply({ embeds: [timeoutEmbed], components: [] });
      } catch (error) {
        console.error("Lỗi khi timeout wizard:", error);
      }
    }
  });
}

function createWizardComponents(selectedComponents = new Set()) {
  const components = [
    {
      id: "channels",
      label: "📁 Channels & Categories",
      description: "Kênh và danh mục"
    },
    {
      id: "roles",
      label: "🎭 Roles & Permissions", 
      description: "Vai trò và quyền hạn"
    },
    {
      id: "settings",
      label: "⚙️ Bot Settings",
      description: "Cấu hình bot"
    },
    {
      id: "emojis",
      label: "😀 Emojis & Stickers",
      description: "Emoji và sticker"
    },
    {
      id: "automod",
      label: "🛡️ Auto Moderation",
      description: "Tự động kiểm duyệt"
    },
    {
      id: "welcome",
      label: "👋 Welcome System",
      description: "Hệ thống chào mừng"
    }
  ];

  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId("wizard_components")
    .setPlaceholder("Chọn thành phần cần lưu")
    .setMinValues(0)
    .setMaxValues(components.length)
    .addOptions(
      components.map(comp => ({
        label: comp.label,
        value: comp.id,
        description: comp.description,
        default: selectedComponents.has(comp.id)
      }))
    );

  const buttonRow = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId("wizard_create")
        .setLabel("✅ Tạo Template")
        .setStyle(ButtonStyle.Success)
        .setDisabled(selectedComponents.size === 0),
      new ButtonBuilder()
        .setCustomId("wizard_preview")
        .setLabel("👁️ Xem trước")
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId("wizard_cancel")
        .setLabel("❌ Hủy")
        .setStyle(ButtonStyle.Danger)
    );

  return [
    new ActionRowBuilder().addComponents(selectMenu),
    buttonRow
  ];
}

// Helper functions
function getCategoryDisplayName(category) {
  const categories = {
    gaming: "🎮 Gaming",
    community: "👥 Community",
    business: "💼 Business",
    creative: "🎨 Creative",
    education: "📚 Education",
    music: "🎵 Music",
    tech: "🔧 Tech",
    all: "🌟 Tất cả"
  };
  return categories[category] || category;
}

function getSortDisplayName(sort) {
  const sorts = {
    popular: "phổ biến",
    newest: "mới nhất",
    rating: "đánh giá cao",
    name: "tên A-Z"
  };
  return sorts[sort] || sort;
}

function getComponentDisplayName(component) {
  const components = {
    channels: "📁 Channels & Categories",
    roles: "🎭 Roles & Permissions",
    settings: "⚙️ Bot Settings",
    emojis: "😀 Emojis & Stickers",
    automod: "🛡️ Auto Moderation",
    welcome: "👋 Welcome System"
  };
  return components[component] || component;
}

function createBrowseComponents(category, sort, page, total) {
  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId("browse_category")
    .setPlaceholder("Chọn danh mục")
    .addOptions([
      { label: "🎮 Gaming", value: "gaming" },
      { label: "👥 Community", value: "community" },
      { label: "💼 Business", value: "business" },
      { label: "🎨 Creative", value: "creative" },
      { label: "📚 Education", value: "education" },
      { label: "🎵 Music", value: "music" },
      { label: "🔧 Tech", value: "tech" },
      { label: "🌟 Tất cả", value: "all" }
    ]);

  const buttonRow = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId("browse_refresh")
        .setLabel("🔄 Làm mới")
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId("browse_featured")
        .setLabel("⭐ Nổi bật")
        .setStyle(ButtonStyle.Primary),
      new ButtonBuilder()
        .setCustomId("browse_search")
        .setLabel("🔍 Tìm kiếm")
        .setStyle(ButtonStyle.Success)
    );

  return [
    new ActionRowBuilder().addComponents(selectMenu),
    buttonRow
  ];
}

async function getTemplates(client, category, sort) {
  // Mock implementation - would fetch from database
  return [
    {
      id: "1",
      name: "Gaming Community",
      description: "Template cho server gaming với voice channels và roles",
      author: "Admin",
      rating: 4.8,
      downloads: 1250,
      category: "gaming"
    },
    {
      id: "2", 
      name: "Business Server",
      description: "Template chuyên nghiệp cho doanh nghiệp",
      author: "Pro User",
      rating: 4.9,
      downloads: 890,
      category: "business"
    }
  ];
}

async function collectTemplateData(guild, components, client) {
  const data = {
    guildName: guild.name,
    guildIcon: guild.iconURL({ dynamic: true }),
    createdAt: new Date().toISOString(),
    components: components
  };

  // Collect data based on selected components
  for (const component of components) {
    switch (component) {
      case "channels":
        data.channels = await collectChannelData(guild);
        break;
      case "roles":
        data.roles = await collectRoleData(guild);
        break;
      case "settings":
        data.settings = await collectSettingsData(guild, client);
        break;
      // Add other components...
    }
  }

  return data;
}

async function collectChannelData(guild) {
  // Implementation from template-create.js
  return { categories: [], channels: [] };
}

async function collectRoleData(guild) {
  // Implementation from template-create.js
  return [];
}

async function collectSettingsData(guild, client) {
  // Implementation from template-create.js
  return {};
}

function getTemplateStats(data) {
  const stats = [];
  
  if (data.channels) {
    stats.push(`• ${data.channels.categories?.length || 0} danh mục`);
    stats.push(`• ${data.channels.channels?.length || 0} kênh`);
  }
  
  if (data.roles) {
    stats.push(`• ${data.roles.length} vai trò`);
  }
  
  if (data.settings) {
    const settingsCount = Object.keys(data.settings).length;
    stats.push(`• ${settingsCount} cấu hình`);
  }
  
  return stats.join('\n') || 'Không có dữ liệu';
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Placeholder handlers for other subcommands
async function handleApply(interaction, client) {
  await interaction.reply({ content: "Apply functionality coming soon!", ephemeral: true });
}

async function handleMy(interaction, client) {
  await interaction.reply({ content: "My templates functionality coming soon!", ephemeral: true });
}

async function handleSearch(interaction, client) {
  await interaction.reply({ content: "Search functionality coming soon!", ephemeral: true });
}

async function handleFeatured(interaction, client) {
  await interaction.reply({ content: "Featured templates coming soon!", ephemeral: true });
}

async function handleBackup(interaction, client) {
  await interaction.reply({ content: "Backup functionality coming soon!", ephemeral: true });
}

async function handleRestore(interaction, client) {
  await interaction.reply({ content: "Restore functionality coming soon!", ephemeral: true });
}
