const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction, client) {
        try {
            // This would be triggered by a custom report accept command or button
            if (!interaction.isCommand() && !interaction.isButton()) return;
            if (interaction.isCommand() && interaction.commandName !== 'report-accept') return;
            if (interaction.isButton() && !interaction.customId.startsWith('accept_report_')) return;
            
            console.log(`✅ Report accepted in ${interaction.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(interaction.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'REPORT_ACCEPT')) return;
            
            // Get report details
            let reportId, acceptReason, actionTaken;
            
            if (interaction.isCommand()) {
                reportId = interaction.options.getString('report_id');
                acceptReason = interaction.options.getString('reason') || 'Report hợp lệ';
                actionTaken = interaction.options.getString('action') || 'Chưa xác định';
            } else if (interaction.isButton()) {
                reportId = interaction.customId.split('_')[2];
                acceptReason = 'Accepted via button';
                actionTaken = 'Sẽ xử lý sau';
            }
            
            const moderator = interaction.user;
            
            // Get report info before accepting
            const reportInfo = await client.db.getReport(interaction.guild.id, reportId);
            if (!reportInfo) return;
            
            // Update report status
            await client.db.updateReportStatus(interaction.guild.id, reportId, 'accepted', moderator.id, acceptReason);
            
            // Create event data
            const eventData = {
                eventType: 'REPORT_ACCEPT',
                user: moderator.tag,
                userId: moderator.id,
                action: `Report được chấp nhận bởi ${moderator.tag}`,
                details: `Report #${reportId} đã được chấp nhận bởi **${moderator.tag}**`,
                target: reportInfo.targetUser || 'Unknown',
                timestamp: new Date().toISOString(),
                reportId: reportId
            };
            
            // Add to database
            await client.db.addAuditLog(interaction.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✅ Report được chấp nhận',
                `Vừa có report được chấp nhận và sẽ được xử lý`
            );
            
            embed.setColor(0x2ecc71); // Green for accepted
            
            embed.addFields([
                {
                    name: '> ID Report',
                    value: `- #${reportId}`,
                    inline: true
                },
                {
                    name: '> Moderator chấp nhận',
                    value: `- ${moderator.tag}`,
                    inline: true
                },
                {
                    name: '> Thời gian chấp nhận',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add original report info
            if (reportInfo) {
                embed.addFields([
                    {
                        name: '> Người report gốc',
                        value: `- ${reportInfo.reporter || 'Unknown'}`,
                        inline: true
                    },
                    {
                        name: '> Đối tượng bị report',
                        value: `- ${reportInfo.targetUser || 'Unknown'}`,
                        inline: true
                    },
                    {
                        name: '> Thời gian tạo report',
                        value: `- <t:${Math.floor(new Date(reportInfo.createdAt).getTime() / 1000)}:F>`,
                        inline: true
                    }
                ]);
                
                if (reportInfo.reason) {
                    embed.addFields({
                        name: '> Lý do report gốc',
                        value: `- ${reportInfo.reason}`,
                        inline: false
                    });
                }
                
                if (reportInfo.type) {
                    const reportTypes = {
                        'spam': 'Spam',
                        'harassment': 'Quấy rối',
                        'inappropriate': 'Nội dung không phù hợp',
                        'scam': 'Lừa đảo',
                        'raid': 'Raid/Tấn công',
                        'general': 'Khác'
                    };
                    
                    embed.addFields({
                        name: '> Loại report',
                        value: `- ${reportTypes[reportInfo.type] || reportInfo.type}`,
                        inline: true
                    });
                }
            }
            
            // Add accept reason
            embed.addFields({
                name: '> Lý do chấp nhận',
                value: `- ${acceptReason}`,
                inline: false
            });
            
            // Add action taken
            embed.addFields({
                name: '> Hành động thực hiện',
                value: `- ${actionTaken}`,
                inline: false
            });
            
            // Calculate report processing time
            if (reportInfo?.createdAt) {
                const processingTime = Date.now() - new Date(reportInfo.createdAt).getTime();
                const days = Math.floor(processingTime / (1000 * 60 * 60 * 24));
                const hours = Math.floor((processingTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((processingTime % (1000 * 60 * 60)) / (1000 * 60));
                
                let timeText = '';
                if (days > 0) timeText += `${days} ngày `;
                if (hours > 0) timeText += `${hours} giờ `;
                if (minutes > 0) timeText += `${minutes} phút`;
                
                embed.addFields({
                    name: '> Thời gian xử lý',
                    value: `- ${timeText.trim()}`,
                    inline: true
                });
                
                // Add processing speed assessment
                if (processingTime < 60 * 60 * 1000) { // Less than 1 hour
                    embed.addFields({
                        name: '> Tốc độ xử lý',
                        value: `- 🟢 Rất nhanh`,
                        inline: true
                    });
                } else if (processingTime < 24 * 60 * 60 * 1000) { // Less than 1 day
                    embed.addFields({
                        name: '> Tốc độ xử lý',
                        value: `- 🟡 Nhanh`,
                        inline: true
                    });
                } else {
                    embed.addFields({
                        name: '> Tốc độ xử lý',
                        value: `- 🟠 Chậm`,
                        inline: true
                    });
                }
            }
            
            // Add impact of accepting
            embed.addFields({
                name: '> 📝 Tác động của việc chấp nhận',
                value: [
                    '• Report được xác nhận là hợp lệ',
                    '• Hành động moderation sẽ được thực hiện',
                    '• Ghi nhận vi phạm của đối tượng',
                    '• Tăng uy tín của người report'
                ].join('\n'),
                inline: false
            });
            
            // Add next steps
            embed.addFields({
                name: '> 🎯 Bước tiếp theo',
                value: [
                    '• Thực hiện hành động moderation',
                    '• Thông báo cho người report (nếu cần)',
                    '• Ghi chép chi tiết vào case',
                    '• Theo dõi hành vi sau xử lý'
                ].join('\n'),
                inline: false
            });
            
            // Add recommended actions based on report type
            if (reportInfo?.type) {
                const actionRecommendations = {
                    'spam': '• Cân nhắc mute hoặc warn\n• Xóa tin nhắn spam\n• Theo dõi hành vi',
                    'harassment': '• Warn hoặc mute nghiêm trọng\n• Có thể cần kick/ban\n• Bảo vệ nạn nhân',
                    'inappropriate': '• Warn và xóa nội dung\n• Giải thích quy tắc\n• Theo dõi chặt chẽ',
                    'scam': '• Ban ngay lập tức\n• Cảnh báo server\n• Report lên Discord',
                    'raid': '• Ban và báo cáo\n• Tăng cường bảo mật\n• Thông báo team'
                };
                
                const recommendation = actionRecommendations[reportInfo.type];
                if (recommendation) {
                    embed.addFields({
                        name: '> 💡 Khuyến nghị hành động',
                        value: recommendation,
                        inline: false
                    });
                }
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${interaction.guild.name} • Report Accept`,
                iconURL: interaction.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Report accept logged: #${reportId}`);
            
        } catch (error) {
            console.error('Error in reportAccept audit log:', error);
        }
    }
};
