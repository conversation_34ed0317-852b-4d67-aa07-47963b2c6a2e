const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ThreadUpdate,
    async execute(oldThread, newThread, client) {
        try {
            // Only handle slow mode changes
            if (oldThread.rateLimitPerUser === newThread.rateLimitPerUser) return;
            
            console.log(`⏱️ Thread slow mode updated: ${newThread.name} in ${newThread.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newThread.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'THREAD_SLOW_MODE_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'THREAD_SLOW_MODE_UPDATE',
                user: 'System',
                userId: null,
                action: 'Slow mode thread được thay đổi',
                details: `Slow mode của thread **${newThread.name}** đã được thay đổi từ ${oldThread.rateLimitPerUser}s thành ${newThread.rateLimitPerUser}s`,
                target: newThread.name,
                channel: newThread.parent?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the slow mode from audit logs
            try {
                const auditLogs = await newThread.guild.fetchAuditLogs({
                    type: 111, // THREAD_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newThread.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Slow mode thread được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for thread slow mode update');
            }
            
            // Add to database
            await client.db.addAuditLog(newThread.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '⏱️ Slow mode thread thay đổi',
                `Vừa có slow mode thread được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Format time display
            const formatTime = (seconds) => {
                if (seconds === 0) return 'Tắt';
                if (seconds < 60) return `${seconds} giây`;
                if (seconds < 3600) return `${Math.floor(seconds / 60)} phút ${seconds % 60} giây`;
                return `${Math.floor(seconds / 3600)} giờ ${Math.floor((seconds % 3600) / 60)} phút`;
            };
            
            // Get thread type names
            const threadTypes = {
                10: 'Announcement Thread',
                11: 'Public Thread',
                12: 'Private Thread'
            };
            
            embed.addFields([
                {
                    name: '> Tên thread',
                    value: `- ${newThread.name}`,
                    inline: true
                },
                {
                    name: '> ID thread',
                    value: `- ${newThread.id}`,
                    inline: true
                },
                {
                    name: '> Loại thread',
                    value: `- ${threadTypes[newThread.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh cha',
                    value: `- ${newThread.parent || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Slow mode cũ',
                    value: `- ${formatTime(oldThread.rateLimitPerUser)}`,
                    inline: true
                },
                {
                    name: '> Slow mode mới',
                    value: `- ${formatTime(newThread.rateLimitPerUser)}`,
                    inline: true
                },
                {
                    name: '> Số thành viên',
                    value: `- ${newThread.memberCount || 0} thành viên`,
                    inline: true
                }
            ]);
            
            // Add thread owner info if available
            if (newThread.ownerId) {
                try {
                    const owner = await newThread.guild.members.fetch(newThread.ownerId);
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- ${owner.user.tag}`,
                        inline: true
                    });
                } catch (error) {
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- Unknown (ID: ${newThread.ownerId})`,
                        inline: true
                    });
                }
            }
            
            // Add impact message
            if (newThread.rateLimitPerUser > oldThread.rateLimitPerUser) {
                embed.addFields({
                    name: '> Tác động',
                    value: `- Thành viên sẽ phải chờ lâu hơn giữa các tin nhắn trong thread`,
                    inline: false
                });
                if (newThread.rateLimitPerUser >= 60) {
                    embed.setColor(0xe74c3c); // Red for heavy restriction
                }
            } else if (newThread.rateLimitPerUser < oldThread.rateLimitPerUser) {
                embed.addFields({
                    name: '> Tác động',
                    value: `- Thành viên có thể gửi tin nhắn nhanh hơn trong thread`,
                    inline: false
                });
                if (newThread.rateLimitPerUser === 0) {
                    embed.setColor(0x2ecc71); // Green for no restriction
                }
            }
            
            // Add thread link
            embed.addFields({
                name: '> Link thread',
                value: `- [Nhấn để xem](https://discord.com/channels/${newThread.guild.id}/${newThread.id})`,
                inline: true
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newThread.guild.name} • Thread Slow Mode Update`,
                iconURL: newThread.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Thread slow mode update logged: ${newThread.name}`);
            
        } catch (error) {
            console.error('Error in threadSlowModeUpdate audit log:', error);
        }
    }
};
