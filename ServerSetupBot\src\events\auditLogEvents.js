const { Events, AuditLogEvent } = require("discord.js");
const {
  createInfoEmbed,
  createWarningEmbed,
  createErrorEmbed,
} = require("../utils/embedBuilder.js");
const auditLogDB = require("../utils/auditLogDatabase.js");

// Smart alert tracking
const alertTracking = new Map();

// Initialize audit log system
function initializeAuditLogSystem(client) {
  console.log("🔍 Audit Log System initialized");

  // Register all audit log event listeners
  registerAuditLogListeners(client);

  // Start smart alert monitoring
  startSmartAlertMonitoring(client);
}

module.exports = {
  initializeAuditLogSystem,
};

function registerAuditLogListeners(client) {
  // Guild events
  client.on(Events.GuildMemberAdd, async (member) => {
    await logEvent(client, member.guild.id, "USER_JOIN", {
      user: member.user.tag,
      userId: member.user.id,
      action: "User joined server",
      details: `${member.user.tag} (${member.user.id}) joined the server`,
      timestamp: new Date().toISOString(),
    });
  });

  client.on(Events.GuildMemberRemove, async (member) => {
    await logEvent(client, member.guild.id, "USER_LEAVE", {
      user: member.user.tag,
      userId: member.user.id,
      action: "User left server",
      details: `${member.user.tag} (${member.user.id}) left the server`,
      timestamp: new Date().toISOString(),
    });
  });

  client.on(Events.GuildBanAdd, async (ban) => {
    const auditLogs = await ban.guild.fetchAuditLogs({
      type: AuditLogEvent.MemberBanAdd,
      limit: 1,
    });

    const banLog = auditLogs.entries.first();
    const executor = banLog?.executor || { tag: "Unknown", id: "unknown" };

    await logEvent(client, ban.guild.id, "BAN_ADD", {
      user: executor.tag,
      userId: executor.id,
      target: ban.user.tag,
      targetId: ban.user.id,
      action: "User banned",
      details: `${ban.user.tag} was banned by ${executor.tag}. Reason: ${
        ban.reason || "No reason provided"
      }`,
      reason: ban.reason,
      timestamp: new Date().toISOString(),
    });

    // Check for mass ban alert
    await checkMassBanAlert(client, ban.guild.id, executor.id);
  });

  client.on(Events.GuildBanRemove, async (ban) => {
    const auditLogs = await ban.guild.fetchAuditLogs({
      type: AuditLogEvent.MemberBanRemove,
      limit: 1,
    });

    const unbanLog = auditLogs.entries.first();
    const executor = unbanLog?.executor || { tag: "Unknown", id: "unknown" };

    await logEvent(client, ban.guild.id, "BAN_REMOVE", {
      user: executor.tag,
      userId: executor.id,
      target: ban.user.tag,
      targetId: ban.user.id,
      action: "User unbanned",
      details: `${ban.user.tag} was unbanned by ${executor.tag}`,
      timestamp: new Date().toISOString(),
    });
  });

  // Channel events
  client.on(Events.ChannelCreate, async (channel) => {
    if (!channel.guild) return;

    const auditLogs = await channel.guild.fetchAuditLogs({
      type: AuditLogEvent.ChannelCreate,
      limit: 1,
    });

    const createLog = auditLogs.entries.first();
    const executor = createLog?.executor || { tag: "Unknown", id: "unknown" };

    await logEvent(client, channel.guild.id, "CHANNEL_CREATE", {
      user: executor.tag,
      userId: executor.id,
      target: channel.name,
      targetId: channel.id,
      action: "Channel created",
      details: `Channel #${channel.name} (${channel.type}) was created by ${executor.tag}`,
      channel: channel.name,
      channelId: channel.id,
      channelType: channel.type,
      timestamp: new Date().toISOString(),
    });

    // Check for mass channel creation alert
    await checkMassChannelCreateAlert(client, channel.guild.id, executor.id);
  });

  client.on(Events.ChannelDelete, async (channel) => {
    if (!channel.guild) return;

    const auditLogs = await channel.guild.fetchAuditLogs({
      type: AuditLogEvent.ChannelDelete,
      limit: 1,
    });

    const deleteLog = auditLogs.entries.first();
    const executor = deleteLog?.executor || { tag: "Unknown", id: "unknown" };

    await logEvent(client, channel.guild.id, "CHANNEL_DELETE", {
      user: executor.tag,
      userId: executor.id,
      target: channel.name,
      targetId: channel.id,
      action: "Channel deleted",
      details: `Channel #${channel.name} (${channel.type}) was deleted by ${executor.tag}`,
      channel: channel.name,
      channelId: channel.id,
      channelType: channel.type,
      timestamp: new Date().toISOString(),
    });
  });

  client.on(Events.ChannelUpdate, async (oldChannel, newChannel) => {
    if (!newChannel.guild) return;

    const changes = [];

    if (oldChannel.name !== newChannel.name) {
      changes.push(`Name: ${oldChannel.name} → ${newChannel.name}`);
    }

    if (oldChannel.topic !== newChannel.topic) {
      changes.push(
        `Topic: ${oldChannel.topic || "None"} → ${newChannel.topic || "None"}`
      );
    }

    if (oldChannel.nsfw !== newChannel.nsfw) {
      changes.push(`NSFW: ${oldChannel.nsfw} → ${newChannel.nsfw}`);
    }

    if (oldChannel.rateLimitPerUser !== newChannel.rateLimitPerUser) {
      changes.push(
        `Slowmode: ${oldChannel.rateLimitPerUser}s → ${newChannel.rateLimitPerUser}s`
      );
    }

    if (changes.length === 0) return;

    const auditLogs = await newChannel.guild.fetchAuditLogs({
      type: AuditLogEvent.ChannelUpdate,
      limit: 1,
    });

    const updateLog = auditLogs.entries.first();
    const executor = updateLog?.executor || { tag: "Unknown", id: "unknown" };

    await logEvent(client, newChannel.guild.id, "CHANNEL_UPDATE", {
      user: executor.tag,
      userId: executor.id,
      target: newChannel.name,
      targetId: newChannel.id,
      action: "Channel updated",
      details: `Channel #${newChannel.name} was updated by ${
        executor.tag
      }: ${changes.join(", ")}`,
      channel: newChannel.name,
      channelId: newChannel.id,
      changes: changes,
      timestamp: new Date().toISOString(),
    });
  });

  // Message events
  client.on(Events.MessageDelete, async (message) => {
    if (!message.guild || message.author?.bot) return;

    await logEvent(client, message.guild.id, "MESSAGE_DELETE", {
      user: message.author?.tag || "Unknown",
      userId: message.author?.id || "unknown",
      action: "Message deleted",
      details: `Message by ${
        message.author?.tag || "Unknown"
      } was deleted in #${message.channel.name}`,
      channel: message.channel.name,
      channelId: message.channel.id,
      messageContent: message.content?.substring(0, 100) || "No content",
      timestamp: new Date().toISOString(),
    });

    // Check for mass message deletion alert
    await checkMassMessageDeleteAlert(
      client,
      message.guild.id,
      message.channel.id
    );
  });

  client.on(Events.MessageBulkDelete, async (messages) => {
    const firstMessage = messages.first();
    if (!firstMessage?.guild) return;

    await logEvent(client, firstMessage.guild.id, "MESSAGE_BULK_DELETE", {
      user: "System/Moderator",
      userId: "system",
      action: "Bulk message delete",
      details: `${messages.size} messages were bulk deleted in #${firstMessage.channel.name}`,
      channel: firstMessage.channel.name,
      channelId: firstMessage.channel.id,
      messageCount: messages.size,
      timestamp: new Date().toISOString(),
    });

    // Trigger alert for bulk delete
    await triggerAlert(client, firstMessage.guild.id, {
      type: "BULK_MESSAGE_DELETE",
      severity: "HIGH",
      message: `${messages.size} messages bulk deleted in #${firstMessage.channel.name}`,
      channel: firstMessage.channel.name,
      channelId: firstMessage.channel.id,
    });
  });

  // Role events
  client.on(Events.GuildRoleCreate, async (role) => {
    const auditLogs = await role.guild.fetchAuditLogs({
      type: AuditLogEvent.RoleCreate,
      limit: 1,
    });

    const createLog = auditLogs.entries.first();
    const executor = createLog?.executor || { tag: "Unknown", id: "unknown" };

    await logEvent(client, role.guild.id, "ROLE_CREATE", {
      user: executor.tag,
      userId: executor.id,
      target: role.name,
      targetId: role.id,
      action: "Role created",
      details: `Role @${role.name} was created by ${executor.tag}`,
      roleName: role.name,
      roleId: role.id,
      roleColor: role.hexColor,
      rolePermissions: role.permissions.toArray(),
      timestamp: new Date().toISOString(),
    });
  });

  client.on(Events.GuildRoleDelete, async (role) => {
    const auditLogs = await role.guild.fetchAuditLogs({
      type: AuditLogEvent.RoleDelete,
      limit: 1,
    });

    const deleteLog = auditLogs.entries.first();
    const executor = deleteLog?.executor || { tag: "Unknown", id: "unknown" };

    await logEvent(client, role.guild.id, "ROLE_DELETE", {
      user: executor.tag,
      userId: executor.id,
      target: role.name,
      targetId: role.id,
      action: "Role deleted",
      details: `Role @${role.name} was deleted by ${executor.tag}`,
      roleName: role.name,
      roleId: role.id,
      timestamp: new Date().toISOString(),
    });
  });

  client.on(Events.GuildMemberUpdate, async (oldMember, newMember) => {
    // Check for role changes
    const addedRoles = newMember.roles.cache.filter(
      (role) => !oldMember.roles.cache.has(role.id)
    );
    const removedRoles = oldMember.roles.cache.filter(
      (role) => !newMember.roles.cache.has(role.id)
    );

    if (addedRoles.size > 0) {
      const auditLogs = await newMember.guild.fetchAuditLogs({
        type: AuditLogEvent.MemberRoleUpdate,
        limit: 1,
      });

      const roleLog = auditLogs.entries.first();
      const executor = roleLog?.executor || { tag: "Unknown", id: "unknown" };

      await logEvent(client, newMember.guild.id, "USER_ROLES_ADD", {
        user: executor.tag,
        userId: executor.id,
        target: newMember.user.tag,
        targetId: newMember.user.id,
        action: "Roles added to user",
        details: `${addedRoles.map((r) => `@${r.name}`).join(", ")} added to ${
          newMember.user.tag
        } by ${executor.tag}`,
        addedRoles: addedRoles.map((r) => ({ name: r.name, id: r.id })),
        timestamp: new Date().toISOString(),
      });

      // Check for mass role grant alert
      await checkMassRoleGrantAlert(client, newMember.guild.id, executor.id);
    }

    if (removedRoles.size > 0) {
      const auditLogs = await newMember.guild.fetchAuditLogs({
        type: AuditLogEvent.MemberRoleUpdate,
        limit: 1,
      });

      const roleLog = auditLogs.entries.first();
      const executor = roleLog?.executor || { tag: "Unknown", id: "unknown" };

      await logEvent(client, newMember.guild.id, "USER_ROLES_REMOVE", {
        user: executor.tag,
        userId: executor.id,
        target: newMember.user.tag,
        targetId: newMember.user.id,
        action: "Roles removed from user",
        details: `${removedRoles
          .map((r) => `@${r.name}`)
          .join(", ")} removed from ${newMember.user.tag} by ${executor.tag}`,
        removedRoles: removedRoles.map((r) => ({ name: r.name, id: r.id })),
        timestamp: new Date().toISOString(),
      });
    }

    // Check for nickname changes
    if (oldMember.nickname !== newMember.nickname) {
      await logEvent(client, newMember.guild.id, "USER_NAME_UPDATE", {
        user: newMember.user.tag,
        userId: newMember.user.id,
        target: newMember.user.tag,
        targetId: newMember.user.id,
        action: "Nickname changed",
        details: `${newMember.user.tag} changed nickname: ${
          oldMember.nickname || "None"
        } → ${newMember.nickname || "None"}`,
        oldNickname: oldMember.nickname,
        newNickname: newMember.nickname,
        timestamp: new Date().toISOString(),
      });
    }
  });

  // Voice events
  client.on(Events.VoiceStateUpdate, async (oldState, newState) => {
    if (!newState.guild) return;

    const member = newState.member;

    // User joined voice channel
    if (!oldState.channel && newState.channel) {
      await logEvent(client, newState.guild.id, "VOICE_USER_JOIN", {
        user: member.user.tag,
        userId: member.user.id,
        action: "User joined voice channel",
        details: `${member.user.tag} joined voice channel ${newState.channel.name}`,
        channel: newState.channel.name,
        channelId: newState.channel.id,
        timestamp: new Date().toISOString(),
      });
    }

    // User left voice channel
    if (oldState.channel && !newState.channel) {
      await logEvent(client, oldState.guild.id, "VOICE_USER_LEAVE", {
        user: member.user.tag,
        userId: member.user.id,
        action: "User left voice channel",
        details: `${member.user.tag} left voice channel ${oldState.channel.name}`,
        channel: oldState.channel.name,
        channelId: oldState.channel.id,
        timestamp: new Date().toISOString(),
      });
    }

    // User switched voice channels
    if (
      oldState.channel &&
      newState.channel &&
      oldState.channel.id !== newState.channel.id
    ) {
      await logEvent(client, newState.guild.id, "VOICE_USER_SWITCH", {
        user: member.user.tag,
        userId: member.user.id,
        action: "User switched voice channel",
        details: `${member.user.tag} moved from ${oldState.channel.name} to ${newState.channel.name}`,
        oldChannel: oldState.channel.name,
        oldChannelId: oldState.channel.id,
        newChannel: newState.channel.name,
        newChannelId: newState.channel.id,
        timestamp: new Date().toISOString(),
      });
    }
  });
}

// Helper functions
async function logEvent(client, guildId, eventType, eventData) {
  try {
    const config = await auditLogDB.getAuditLogConfig(guildId);

    if (!config || !config.enabled) return;

    // Check if this event type should be logged
    if (!auditLogDB.shouldLogEvent(config, eventType)) return;

    // Check if user/role should be ignored
    if (shouldIgnoreUser(config, eventData.userId)) return;

    // Add to database
    await auditLogDB.addAuditLog(guildId, {
      eventType,
      ...eventData,
    });

    // Send to audit log channel
    await sendAuditLogMessage(client, config, eventType, eventData);
  } catch (error) {
    console.error("Error logging audit event:", error);
  }
}

function shouldIgnoreUser(config, userId) {
  if (!config.ignoredUsers || !userId) return false;
  return config.ignoredUsers.includes(userId);
}

async function sendAuditLogMessage(client, config, eventType, eventData) {
  try {
    const channel = await client.channels.fetch(config.channelId);
    if (!channel) return;

    const embed = createAuditLogEmbed(eventType, eventData);
    await channel.send({ embeds: [embed] });
  } catch (error) {
    console.error("Error sending audit log message:", error);
  }
}

function createAuditLogEmbed(eventType, eventData) {
  const eventTypes = auditLogDB.getEventTypes();
  const eventName = eventTypes[eventType] || eventType;

  let color = 0x3498db; // Default blue
  let emoji = "📝";

  // Set color and emoji based on event type
  if (
    eventType.includes("DELETE") ||
    eventType.includes("REMOVE") ||
    eventType.includes("BAN")
  ) {
    color = 0xe74c3c; // Red
    emoji = "🗑️";
  } else if (
    eventType.includes("CREATE") ||
    eventType.includes("ADD") ||
    eventType.includes("JOIN")
  ) {
    color = 0x2ecc71; // Green
    emoji = "✅";
  } else if (eventType.includes("UPDATE") || eventType.includes("EDIT")) {
    color = 0xf39c12; // Orange
    emoji = "✏️";
  } else if (eventType.includes("KICK") || eventType.includes("TIMEOUT")) {
    color = 0xe67e22; // Orange-red
    emoji = "👢";
  }

  const embed = createInfoEmbed(
    `${emoji} ${eventName}`,
    eventData.details || "No details available"
  );

  embed.setColor(color);

  // Add fields based on available data
  if (eventData.user && eventData.user !== "Unknown") {
    embed.addFields({ name: "👤 User", value: eventData.user, inline: true });
  }

  if (eventData.target && eventData.target !== eventData.user) {
    embed.addFields({
      name: "🎯 Target",
      value: eventData.target,
      inline: true,
    });
  }

  if (eventData.channel) {
    embed.addFields({
      name: "📍 Channel",
      value: `#${eventData.channel}`,
      inline: true,
    });
  }

  if (eventData.reason) {
    embed.addFields({
      name: "📝 Reason",
      value: eventData.reason,
      inline: false,
    });
  }

  embed.setTimestamp();
  embed.setFooter({ text: `Event ID: ${eventData.id || "N/A"}` });

  return embed;
}

// Smart alert functions
function startSmartAlertMonitoring(client) {
  // Clean up old tracking data every 15 minutes
  setInterval(() => {
    const now = Date.now();
    for (const [key, data] of alertTracking.entries()) {
      if (now - data.lastActivity > 15 * 60 * 1000) {
        // 15 minutes
        alertTracking.delete(key);
      }
    }
  }, 15 * 60 * 1000);
}

async function checkMassMessageDeleteAlert(client, guildId, channelId) {
  const config = await auditLogDB.getAuditLogConfig(guildId);
  if (!config?.smartAlerts) return;

  const key = `${guildId}_message_delete_${channelId}`;
  const now = Date.now();
  const threshold = config.alertThresholds?.messageDelete || 10;
  const timeWindow = 5 * 60 * 1000; // 5 minutes

  if (!alertTracking.has(key)) {
    alertTracking.set(key, { count: 0, firstEvent: now, lastActivity: now });
  }

  const tracking = alertTracking.get(key);

  // Reset if outside time window
  if (now - tracking.firstEvent > timeWindow) {
    tracking.count = 1;
    tracking.firstEvent = now;
  } else {
    tracking.count++;
  }

  tracking.lastActivity = now;

  if (tracking.count >= threshold) {
    await triggerAlert(client, guildId, {
      type: "MASS_MESSAGE_DELETE",
      severity: "HIGH",
      message: `${tracking.count} messages deleted in 5 minutes in <#${channelId}>`,
      channelId: channelId,
      count: tracking.count,
    });

    // Reset counter after alert
    tracking.count = 0;
    tracking.firstEvent = now;
  }
}

async function checkMassChannelCreateAlert(client, guildId, userId) {
  const config = await auditLogDB.getAuditLogConfig(guildId);
  if (!config?.smartAlerts) return;

  const key = `${guildId}_channel_create_${userId}`;
  const now = Date.now();
  const threshold = config.alertThresholds?.channelCreate || 3;
  const timeWindow = 5 * 60 * 1000; // 5 minutes

  await checkGenericAlert(client, guildId, key, threshold, timeWindow, {
    type: "MASS_CHANNEL_CREATE",
    severity: "MEDIUM",
    message: `${threshold}+ channels created in 5 minutes by <@${userId}>`,
    userId: userId,
  });
}

async function checkMassRoleGrantAlert(client, guildId, userId) {
  const config = await auditLogDB.getAuditLogConfig(guildId);
  if (!config?.smartAlerts) return;

  const key = `${guildId}_role_grant_${userId}`;
  const now = Date.now();
  const threshold = config.alertThresholds?.roleGrant || 5;
  const timeWindow = 10 * 60 * 1000; // 10 minutes

  await checkGenericAlert(client, guildId, key, threshold, timeWindow, {
    type: "MASS_ROLE_GRANT",
    severity: "HIGH",
    message: `${threshold}+ roles granted in 10 minutes by <@${userId}>`,
    userId: userId,
  });
}

async function checkMassBanAlert(client, guildId, userId) {
  const config = await auditLogDB.getAuditLogConfig(guildId);
  if (!config?.smartAlerts) return;

  const key = `${guildId}_ban_${userId}`;
  const now = Date.now();
  const threshold = config.alertThresholds?.memberBan || 3;
  const timeWindow = 10 * 60 * 1000; // 10 minutes

  await checkGenericAlert(client, guildId, key, threshold, timeWindow, {
    type: "MASS_BAN",
    severity: "CRITICAL",
    message: `${threshold}+ members banned in 10 minutes by <@${userId}>`,
    userId: userId,
  });
}

async function checkGenericAlert(
  client,
  guildId,
  key,
  threshold,
  timeWindow,
  alertData
) {
  const now = Date.now();

  if (!alertTracking.has(key)) {
    alertTracking.set(key, { count: 0, firstEvent: now, lastActivity: now });
  }

  const tracking = alertTracking.get(key);

  // Reset if outside time window
  if (now - tracking.firstEvent > timeWindow) {
    tracking.count = 1;
    tracking.firstEvent = now;
  } else {
    tracking.count++;
  }

  tracking.lastActivity = now;

  if (tracking.count >= threshold) {
    await triggerAlert(client, guildId, {
      ...alertData,
      count: tracking.count,
    });

    // Reset counter after alert
    tracking.count = 0;
    tracking.firstEvent = now;
  }
}

async function triggerAlert(client, guildId, alertData) {
  try {
    // Save alert to database
    await auditLogDB.addAlert(guildId, alertData);

    // Send alert to audit log channel
    const config = await auditLogDB.getAuditLogConfig(guildId);
    if (config?.channelId) {
      const channel = await client.channels.fetch(config.channelId);
      if (channel) {
        const embed = createAlertEmbed(alertData);
        await channel.send({ embeds: [embed] });
      }
    }

    // Send DM to server owner
    const guild = client.guilds.cache.get(guildId);
    if (guild?.ownerId) {
      try {
        const owner = await client.users.fetch(guild.ownerId);
        const dmEmbed = createAlertDMEmbed(alertData, guild);
        await owner.send({ embeds: [dmEmbed] });
      } catch (error) {
        console.log("Could not send DM to server owner:", error.message);
      }
    }
  } catch (error) {
    console.error("Error triggering alert:", error);
  }
}

function createAlertEmbed(alertData) {
  let color = 0xf39c12; // Orange
  let emoji = "⚠️";

  if (alertData.severity === "CRITICAL") {
    color = 0xe74c3c; // Red
    emoji = "🚨";
  } else if (alertData.severity === "HIGH") {
    color = 0xe67e22; // Orange-red
    emoji = "⚠️";
  }

  const embed = createWarningEmbed(
    `${emoji} Smart Alert: ${alertData.type.replace(/_/g, " ")}`,
    alertData.message
  );

  embed.setColor(color);
  embed.addFields({
    name: "🔍 Severity",
    value: alertData.severity,
    inline: true,
  });

  if (alertData.count) {
    embed.addFields({
      name: "📊 Count",
      value: alertData.count.toString(),
      inline: true,
    });
  }

  embed.setTimestamp();

  return embed;
}

function createAlertDMEmbed(alertData, guild) {
  const embed = createWarningEmbed(
    `🚨 Security Alert: ${guild.name}`,
    `Suspicious activity detected in your server!`
  );

  embed.addFields(
    {
      name: "⚠️ Alert Type",
      value: alertData.type.replace(/_/g, " "),
      inline: true,
    },
    { name: "🔍 Severity", value: alertData.severity, inline: true },
    { name: "📝 Details", value: alertData.message, inline: false },
    { name: "🏰 Server", value: guild.name, inline: true },
    {
      name: "⏰ Time",
      value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
      inline: true,
    }
  );

  embed.setThumbnail(guild.iconURL({ dynamic: true }));

  return embed;
}
