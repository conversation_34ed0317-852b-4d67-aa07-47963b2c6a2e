const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle RTC region changes for voice channels
            if (oldChannel.rtcRegion === newChannel.rtcRegion || newChannel.type !== 2) return;
            
            console.log(`🌍 Channel RTC region updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_RTC_REGION_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_RTC_REGION_UPDATE',
                user: 'System',
                userId: null,
                action: 'Vùng RTC kênh thoại được thay đổi',
                details: `Vùng RTC của kênh thoại **${newChannel.name}** đã được thay đổi`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the RTC region from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Vùng RTC được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for RTC region update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🌍 Vùng RTC kênh thoại thay đổi',
                `Vừa có vùng RTC kênh thoại được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // RTC region mapping
            const rtcRegions = {
                'us-west': '🇺🇸 US West',
                'us-east': '🇺🇸 US East',
                'us-central': '🇺🇸 US Central',
                'us-south': '🇺🇸 US South',
                'singapore': '🇸🇬 Singapore',
                'southafrica': '🇿🇦 South Africa',
                'sydney': '🇦🇺 Sydney',
                'europe': '🇪🇺 Europe',
                'brazil': '🇧🇷 Brazil',
                'hongkong': '🇭🇰 Hong Kong',
                'russia': '🇷🇺 Russia',
                'japan': '🇯🇵 Japan',
                'india': '🇮🇳 India'
            };
            
            const formatRegion = (region) => {
                if (!region) return 'Tự động (Optimal)';
                return rtcRegions[region] || region;
            };
            
            embed.addFields([
                {
                    name: '> Kênh thoại',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Vùng cũ',
                    value: `- ${formatRegion(oldChannel.rtcRegion)}`,
                    inline: true
                },
                {
                    name: '> Vùng mới',
                    value: `- ${formatRegion(newChannel.rtcRegion)}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Số người trong kênh',
                    value: `- ${newChannel.members.size} thành viên`,
                    inline: true
                }
            ]);
            
            // Add impact message
            if (!newChannel.rtcRegion && oldChannel.rtcRegion) {
                embed.addFields({
                    name: '> Tác động',
                    value: `- Kênh giờ đây sử dụng vùng tối ưu tự động`,
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for automatic
            } else if (newChannel.rtcRegion && !oldChannel.rtcRegion) {
                embed.addFields({
                    name: '> Tác động',
                    value: `- Kênh giờ đây cố định vùng ${formatRegion(newChannel.rtcRegion)}`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> Tác động',
                    value: `- Chất lượng kết nối có thể thay đổi tùy theo vị trí người dùng`,
                    inline: false
                });
            }
            
            // Add latency info
            embed.addFields({
                name: '> Lưu ý',
                value: `- Thay đổi vùng RTC có thể ảnh hưởng đến độ trễ và chất lượng âm thanh`,
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel RTC Region Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel RTC region update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelRtcRegionUpdate audit log:', error);
        }
    }
};
