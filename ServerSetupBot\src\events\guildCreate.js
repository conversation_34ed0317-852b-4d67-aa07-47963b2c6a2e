const { Events } = require('discord.js');
const { createSuccessEmbed, createInfoEmbed } = require('../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildCreate,
    async execute(guild, client) {
        console.log(`🎉 Bot đã được thêm vào máy chủ mới: ${guild.name} (ID: ${guild.id})`);
        console.log(`👥 Máy chủ có ${guild.memberCount} thành viên`);
        
        try {
            // Tạo cấu hình mặc định cho server mới
            await client.db.createServerConfig(guild.id, {
                guildName: guild.name,
                ownerId: guild.ownerId,
                createdAt: new Date().toISOString()
            });
            
            // Tìm channel phù hợp để gửi tin nhắn chào mừng
            let welcomeChannel = null;
            
            // Ưu tiên: general, welcome, chat, hoặc channel đầu tiên có thể gửi tin nhắn
            const channelNames = ['general', 'welcome', 'chat', 'chào-mừng', 'tổng-quát'];
            
            for (const name of channelNames) {
                welcomeChannel = guild.channels.cache.find(channel => 
                    channel.name.toLowerCase().includes(name) && 
                    channel.isTextBased() &&
                    channel.permissionsFor(guild.members.me).has(['SendMessages', 'EmbedLinks'])
                );
                if (welcomeChannel) break;
            }
            
            // Nếu không tìm thấy, lấy channel text đầu tiên có thể gửi tin nhắn
            if (!welcomeChannel) {
                welcomeChannel = guild.channels.cache.find(channel => 
                    channel.isTextBased() &&
                    channel.permissionsFor(guild.members.me).has(['SendMessages', 'EmbedLinks'])
                );
            }
            
            // Gửi tin nhắn chào mừng nếu tìm thấy channel phù hợp
            if (welcomeChannel) {
                const welcomeEmbed = createSuccessEmbed(
                    '🎉 Chào mừng đến với Server Setup Bot!',
                    `Xin chào **${guild.name}**!\n\n` +
                    `Tôi là bot chuyên về **thiết lập và cấu hình máy chủ Discord** với đầy đủ tính năng:\n\n` +
                    `${client.config.emojis.welcome} **Hệ thống chào mừng/tạm biệt**\n` +
                    `${client.config.emojis.role} **Quản lý auto-role và reaction roles**\n` +
                    `${client.config.emojis.channel} **Tạo và tổ chức kênh**\n` +
                    `${client.config.emojis.settings} **Quản lý quyền hạn**\n` +
                    `${client.config.emojis.moderation} **Thiết lập hệ thống kiểm duyệt**\n` +
                    `${client.config.emojis.backup} **Sao lưu và khôi phục cấu hình**\n` +
                    `${client.config.emojis.template} **Tạo và áp dụng template máy chủ**\n\n` +
                    `**Bắt đầu ngay:** Sử dụng lệnh \`/help\` để xem tất cả các lệnh có sẵn!\n` +
                    `**Lưu ý:** Chỉ **Quản trị viên** mới có thể sử dụng các lệnh thiết lập.`
                );
                
                welcomeEmbed.setThumbnail(client.user.displayAvatarURL());
                welcomeEmbed.setFooter({ 
                    text: `Được phát triển bởi ZarTeam • Phiên bản 1.0.0`,
                    iconURL: client.user.displayAvatarURL()
                });
                
                await welcomeChannel.send({ embeds: [welcomeEmbed] });
                
                console.log(`✅ Đã gửi tin nhắn chào mừng tới ${welcomeChannel.name} trong ${guild.name}`);
            }
            
        } catch (error) {
            console.error(`❌ Lỗi khi xử lý server mới ${guild.name}:`, error);
        }
    },
};
