function _0x429f(_0x4500c6,_0x5766e8){const _0x1549b4=_0x1549();return _0x429f=function(_0x429f2f,_0x42365b){_0x429f2f=_0x429f2f-0x1b8;let _0x21e118=_0x1549b4[_0x429f2f];return _0x21e118;},_0x429f(_0x4500c6,_0x5766e8);}(function(_0x11fa8e,_0x2d24d5){const _0x11b946=_0x11fa8e();function _0x3b97d2(_0x299ee8,_0xff5754){return _0x429f(_0x299ee8-0x1ed,_0xff5754);}while(!![]){try{const _0x444dca=-parseInt(_0x3b97d2(0x3ec,0x3e1))/0x1*(parseInt(_0x3b97d2(0x3e7,0x3e2))/0x2)+-parseInt(_0x3b97d2(0x3ba,0x3c4))/0x3+parseInt(_0x3b97d2(0x3e0,0x404))/0x4+-parseInt(_0x3b97d2(0x3d4,0x3d5))/0x5+-parseInt(_0x3b97d2(0x3a9,0x3b7))/0x6+parseInt(_0x3b97d2(0x3bd,0x3bf))/0x7+parseInt(_0x3b97d2(0x3bf,0x3c2))/0x8;if(_0x444dca===_0x2d24d5)break;else _0x11b946['push'](_0x11b946['shift']());}catch(_0x4446b5){_0x11b946['push'](_0x11b946['shift']());}}}(_0x1549,0x473e0));function _0x5eefe9(_0xe47d1f,_0x1c3b77){return _0x429f(_0xe47d1f-0x293,_0x1c3b77);}const {EmbedBuilder,SlashCommandBuilder}=require('discord.js'),axios=require(_0x5eefe9(0x474,0x44f)),fs=require('fs'),yaml=require(_0x5eefe9(0x47c,0x46b)),path=require(_0x5eefe9(0x46e,0x45e)),config=yaml['load'](fs[_0x5eefe9(0x46b,0x448)](_0x5eefe9(0x452,0x45f),_0x5eefe9(0x480,0x48b))),lang=yaml[_0x5eefe9(0x46d,0x452)](fs[_0x5eefe9(0x46b,0x45f)](_0x5eefe9(0x46a,0x48a),'utf8'));module[_0x5eefe9(0x470,0x48e)]={'data':new SlashCommandBuilder()[_0x5eefe9(0x489,0x472)](_0x5eefe9(0x454,0x474))[_0x5eefe9(0x481,0x48f)](_0x5eefe9(0x47f,0x47a))[_0x5eefe9(0x44c,0x44a)](_0x269b8c=>_0x269b8c[_0x5eefe9(0x489,0x483)](_0x5eefe9(0x464,0x487))['setDescription'](_0x5eefe9(0x494,0x48c))[_0x5eefe9(0x45c,0x47d)](!![])),'category':_0x5eefe9(0x44e,0x45b),async 'execute'(_0x58a51c,_0x4817f9){function _0x358b35(_0x1c62e7,_0x4e86d6){return _0x5eefe9(_0x1c62e7-0x8e,_0x4e86d6);}const _0x24f0c7=_0x58a51c[_0x358b35(0x4e9,0x4f3)][_0x358b35(0x4fa,0x4f3)](_0x358b35(0x4f2,0x512)),_0x285d59='https://api.mcsrvstat.us/3/'+_0x24f0c7;try{const _0x4b81a2=await axios[_0x358b35(0x503,0x4e3)](_0x285d59),_0x59c832=_0x4b81a2[_0x358b35(0x4fd,0x50e)];if(!_0x59c832[_0x358b35(0x4ec,0x4f8)])return _0x58a51c[_0x358b35(0x501,0x4f9)]({'content':_0x358b35(0x51c,0x518),'ephemeral':!![]});const _0x117fa3=_0x59c832[_0x358b35(0x507,0x528)][_0x358b35(0x504,0x528)][_0x358b35(0x4e3,0x4dd)]('\x0a'),_0x579dad=_0x59c832[_0x358b35(0x51f,0x53b)]?_0x59c832[_0x358b35(0x51f,0x535)][_0x358b35(0x51d,0x51f)](',')[0x1]:null;let _0x26a0e2=null;if(_0x579dad){const _0x122547=Buffer[_0x358b35(0x4d9,0x4e0)](_0x579dad,_0x358b35(0x4e5,0x4cc)),_0x545fb8=path[_0x358b35(0x4e3,0x4d3)](__dirname,_0x358b35(0x511,0x500));fs[_0x358b35(0x519,0x528)](_0x545fb8,_0x122547),_0x26a0e2=_0x358b35(0x4e1,0x4c0);}const _0x29618a=_0x59c832[_0x358b35(0x515,0x526)]?_0x358b35(0x4e7,0x4cd):'No';let _0x117a40=new EmbedBuilder()[_0x358b35(0x50b,0x50d)](_0x358b35(0x513,0x514)+_0x24f0c7)[_0x358b35(0x500,0x509)](config[_0x358b35(0x51e,0x508)])[_0x358b35(0x50f,0x511)](_0x358b35(0x4f4,0x4d5)+_0x24f0c7+'**')['addFields']({'name':_0x358b35(0x4f5,0x4ec),'value':_0x59c832['online']?_0x358b35(0x4e7,0x4dd):'No','inline':!![]},{'name':'IP','value':_0x59c832['ip']||_0x358b35(0x521,0x53f),'inline':!![]},{'name':_0x358b35(0x4eb,0x50b),'value':_0x59c832[_0x358b35(0x509,0x4ed)]?_0x59c832[_0x358b35(0x509,0x4f2)]['toString']():'N/A','inline':!![]},{'name':_0x358b35(0x518,0x51f),'value':_0x59c832[_0x358b35(0x510,0x51d)]||'N/A','inline':!![]},{'name':_0x358b35(0x51a,0x527),'value':(_0x59c832[_0x358b35(0x4e8,0x4d5)][_0x358b35(0x4ec,0x4e8)]||0x0)+'/'+(_0x59c832[_0x358b35(0x4e8,0x4cf)][_0x358b35(0x4e6,0x503)]||_0x358b35(0x521,0x50b)),'inline':!![]},{'name':_0x358b35(0x4e4,0x4e9),'value':_0x59c832[_0x358b35(0x4ff,0x51f)]||_0x358b35(0x521,0x51e),'inline':!![]},{'name':_0x358b35(0x50c,0x4f0),'value':_0x59c832['protocol']?_0x59c832[_0x358b35(0x4df,0x4cd)][_0x358b35(0x4ef,0x4e1)]:_0x358b35(0x521,0x519),'inline':!![]},{'name':_0x358b35(0x506,0x4ec),'value':_0x59c832[_0x358b35(0x516,0x529)]||_0x358b35(0x521,0x529),'inline':!![]},{'name':'Cracked','value':_0x29618a,'inline':!![]},{'name':_0x358b35(0x4f0,0x4ea),'value':_0x117fa3||_0x358b35(0x521,0x503)})[_0x358b35(0x505,0x4fe)]()['setFooter']({'text':_0x358b35(0x4ed,0x4f6),'iconURL':_0x358b35(0x4f7,0x4eb)}),_0x270460=[];_0x26a0e2&&(_0x117a40[_0x358b35(0x4db,0x4ed)](_0x26a0e2),_0x270460[_0x358b35(0x4f6,0x50f)]({'attachment':path[_0x358b35(0x4e3,0x4d0)](__dirname,_0x358b35(0x511,0x509)),'name':'server-icon.png'})),_0x58a51c[_0x358b35(0x501,0x4f0)]({'embeds':[_0x117a40],'files':_0x270460});}catch(_0x40a53b){console[_0x358b35(0x512,0x513)]('Error\x20fetching\x20server\x20information:\x20',_0x40a53b),_0x58a51c['reply']({'content':_0x358b35(0x4de,0x4fc),'ephemeral':!![]});}}};function _0x1549(){const _0xbf3365=['././config.yml','attachment://server-icon.png','minecraft','join','Version','base64','max','Yes','players','options','setRequired','Port','online','Server\x20information\x20provided\x20by\x20Drako\x20Bot','1302225nmWlYY','name','MOTD','2106216koSrSA','address','5523624AoeWNb','Here\x20is\x20some\x20interesting\x20information\x20about\x20the\x20server\x20**','Online','push','https://static.planetminecraft.com/files/image/minecraft/texture-pack/2023/693/16851220-dirt-block_xl.webp','././lang.yml','readFileSync','getString','load','path','data','exports','version','setColor','reply','axios','get','clean','setTimestamp','Software','motd','1295195vMEMDQ','port','js-yaml','setTitle','Protocol','Get\x20information\x20about\x20a\x20Minecraft\x20server','utf8','setDescription','hostname','server-icon.png','error','Server\x20Information\x20for\x20','2227716pSIrzD','cracked','software','setName','Hostname','writeFileSync','Players','2aERcWO','The\x20server\x20is\x20currently\x20offline\x20or\x20the\x20address\x20is\x20incorrect.','split','EmbedColors','icon','540894IHVFgJ','N/A','The\x20address\x20of\x20the\x20Minecraft\x20server','from','addStringOption','setThumbnail','General','134724LdiMTo','Sorry,\x20there\x20was\x20an\x20error\x20fetching\x20the\x20server\x20information.','protocol'];_0x1549=function(){return _0xbf3365;};return _0x1549();}