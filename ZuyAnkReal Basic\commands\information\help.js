const { Em<PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const fs = require('fs');
const path = require('path');
const { defaultPrefix } = require('../../config.json');

module.exports = {
  name: 'help',
  aliases: ['h'],
  description: 'Shows all available commands',
  async execute(message) {
    const totalCommands = getTotalCommands();

    const categories = [
      { label: 'Server', emoji: '<:server:1362840472963514409>', value: 'server', description: 'Get All Server Command list' },
      { label: 'Moderation', emoji: '<:moderation:1363515858693128413>', value: 'moderation', description: 'Get All Moderation Command list' },
      { label: 'Information', emoji: '<:info2:1362840572519383282>', value: 'information', description: 'Get All Information Command list' },
      { label: 'Giveaway', emoji: '<:giveaway1:1362840598205436005>', value: 'giveaway', description: 'Get All Giveaway Command List' },
      { label: 'Utility', emoji: '<:utility:1362840570216710495>', value: 'utility', description: 'Get All Utility Command list' },
      { label: 'Fun', emoji: '<:fun:1362840579804762202>', value: 'fun', description: 'Get All Fun Command list' },
    ];

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('help-menu')
      .setPlaceholder('〙Select A Category To View Commands')
      .addOptions(categories.map(cat => ({
        label: cat.label,
        emoji: cat.emoji,
        value: cat.value,
        description: cat.description
      })));

    const dropdownRow = new ActionRowBuilder().addComponents(selectMenu);

    const buttonsRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId('help_home')
        .setLabel('Home')
        .setEmoji('<:home:1362840474636910833>')
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId('help_delete')
        .setLabel('Close')
        .setEmoji('<:vanish:1362840581453123594>')
        .setStyle(ButtonStyle.Danger),
      new ButtonBuilder()
        .setCustomId('help_all')
        .setLabel('All Commands')
        .setEmoji('<:slash_commands:1363342683854344402>')
        .setStyle(ButtonStyle.Primary)
    );

    const helpEmbed = new EmbedBuilder()
      .setColor('#2b2d31')
      .setAuthor({
        name: `${message.client.user.username} Help Menu`,
        iconURL: message.client.user.displayAvatarURL(),
        url: `https://discord.com/oauth2/authorize?client_id=${message.client.user.id}&permissions=8&scope=bot`
      })
      .setThumbnail(message.client.user.displayAvatarURL())
      .setDescription('**Stratos bot’s commands will work without a prefix**')
      .addFields({
        name: '<:folder:1362840466495766709> __**Command Categories:**__',
        value: '<:server:1362840472963514409> `∤` **Server**\n<:moderation:1363515858693128413> `∤` **Moderation**\n<:info2:1362840572519383282> `∤` **Information**\n<:giveaway1:1362840598205436005> `∤` **Giveaway**\n<:utility:1362840570216710495> `∤` **Utility**\n<:fun:1362840579804762202> `∤` **Fun**',
        inline: false
      })
      .setImage('https://cdn.discordapp.com/attachments/1355484630232338553/1361699857881895022/Picsart_25-03-28_21-03-16-017.jpg')
      .setFooter({ text: `Stratos Development`, iconURL: message.client.user.displayAvatarURL() });

    const sentMessage = await message.channel.send({ embeds: [helpEmbed], components: [dropdownRow, buttonsRow] });

    const collector = sentMessage.createMessageComponentCollector({ time: 600000 });

    collector.on('collect', async (interaction) => {
      if (interaction.user.id !== message.author.id) {
        return interaction.reply({ content: '❌ | This menu is not for you!', ephemeral: true });
      }

      if (interaction.isStringSelectMenu()) {
        const selectedCategory = interaction.values[0];
        const categoryDetails = getCategoryDetails(selectedCategory);

        const categoryEmbed = new EmbedBuilder()
          .setColor('#2b2d31')
          .setTitle(`${categoryDetails.emoji} ${categoryDetails.title}`)
          .setDescription(categoryDetails.description)
          .setFooter({ text: `${message.client.user.username} Help` });

        await interaction.update({ embeds: [categoryEmbed], components: [dropdownRow, buttonsRow] });
      }

      if (interaction.isButton()) {
        if (interaction.customId === 'help_home') {
          await interaction.update({ embeds: [helpEmbed], components: [dropdownRow, buttonsRow] });
        }

        if (interaction.customId === 'help_delete') {
          await interaction.message.delete().catch(() => {});
        }

        if (interaction.customId === 'help_all') {
          const allCommandsEmbed = new EmbedBuilder()
            .setColor('#2b2d31')
            .setAuthor({
              name: `${message.client.user.tag}`,
              iconURL: message.client.user.displayAvatarURL(),
              url: message.client.user.displayAvatarURL()
            })
            .setTitle('All Commands')
            .setDescription(
              '1. <:server:1362840472963514409> __Server Commands__\n**maintenance on, mt on, maintenance off, mt off, rename, deletechannel**\n\n' +
              '2. <:moderation:1363515858693128413> __Moderation Commands__\n**kick, ban, unban, lock, unlock, hide, unhide, purge, unhideall, hideall, lockall, unlockall**\n\n' +
              '3. <:info2:1362840572519383282> __Information Commands__\n**invite, help, support, ping, uptime, stats**\n\n' +
              '4. <:giveaway1:1362840598205436005> __Giveaway Commands__\n**gstart, gend, greroll**\n\n' +
              '5. <:utility:1362840570216710495> __Utility Commands__\n**list emojis, list bots, list roles, list boosters, list bans, serverinfo**\n\n' +
              '6. <:fun:1362840579804762202> __Game Commands__\n**slap, kiss, hug**'
            );

          await interaction.update({ embeds: [allCommandsEmbed], components: [dropdownRow, buttonsRow] });
        }
      }
    });

    collector.on('end', () => {
      const disabledDropdown = new ActionRowBuilder().addComponents(selectMenu.setDisabled(true));
      const disabledButtons = new ActionRowBuilder().addComponents(
        buttonsRow.components.map(button => button.setDisabled(true))
      );
      sentMessage.edit({ components: [disabledDropdown, disabledButtons] }).catch(() => {});
    });

    function getTotalCommands() {
      const commandBasePath = path.join(__dirname, '../../commands');
      if (!fs.existsSync(commandBasePath)) return 0;

      let count = 0;
      const folders = fs.readdirSync(commandBasePath).filter(folder =>
        fs.statSync(path.join(commandBasePath, folder)).isDirectory()
      );

      for (const folder of folders) {
        count += fs.readdirSync(path.join(commandBasePath, folder)).filter(file => file.endsWith('.js')).length;
      }

      return count;
    }

    function getCategoryDetails(category) {
      const categoryData = {
        server: {
          emoji: '<:server:1362840472963514409>',
          title: 'Server Commands',
          description: '**maintenance on, mt on, maintenance off, mt off, rename, deletechannel**'
        },
        moderation: {
          emoji: '<:moderation:1363515858693128413>',
          title: 'Moderation Commands',
          description: '**kick, ban, unban, lock, unlock, hide, unhide, purge, unhideall, hideall, lockall, unlockall**'
        },
        information: {
          emoji: '<:info2:1362840572519383282>',
          title: 'Information Commands',
          description: '**invite, help, support, ping, uptime, stats**'
        },
        giveaway: {
          emoji: '<:giveaway1:1362840598205436005>',
          title: 'Giveaway Commands',
          description: '**gstart, gend, greroll**'
        },
        utility: {
          emoji: '<:utility:1362840570216710495>',
          title: 'Utility Commands',
          description: '**list emojis, list bots, list roles, list boosters, list bans, serverinfo**'
        },
        fun: {
          emoji: '<:fun:1362840579804762202>',
          title: 'Game Commands',
          description: '**slap, kiss, hug**'
        }
      };
      return categoryData[category];
    }
  }
};