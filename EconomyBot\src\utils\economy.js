const moment = require('moment-timezone');
const config = require('../config/config');
const User = require('../models/User');

/**
 * Kiểm tra xem user có thể nhận daily reward không
 */
function canClaimDaily(lastDaily) {
    if (!lastDaily) return true;
    
    const lastDailyMoment = moment(lastDaily).tz(config.bot.timezone);
    const now = moment().tz(config.bot.timezone);
    
    // Kiểm tra xem đã qua ngày mới chưa
    return !lastDailyMoment.isSame(now, 'day');
}

/**
 * Kiểm tra xem user có thể work không
 */
function canWork(lastWork) {
    if (!lastWork) return true;
    
    const lastWorkMoment = moment(lastWork);
    const now = moment();
    
    // Cooldown 1 giờ cho work
    return now.diff(lastWorkMoment, 'hours') >= 1;
}

/**
 * Tính toán daily reward ngẫu nhiên
 */
function calculateDailyReward() {
    const min = config.economy.dailyReward.min;
    const max = config.economy.dailyReward.max;
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Tính toán work reward ngẫu nhiên
 */
function calculateWorkReward() {
    const jobs = [
        { name: 'Lập trình viên', min: 200, max: 500 },
        { name: 'Bác sĩ', min: 300, max: 600 },
        { name: 'Giáo viên', min: 150, max: 350 },
        { name: 'Tài xế', min: 100, max: 250 },
        { name: 'Đầu bếp', min: 120, max: 280 },
        { name: 'Nhân viên văn phòng', min: 180, max: 400 },
        { name: 'Bảo vệ', min: 80, max: 200 },
        { name: 'Nhân viên bán hàng', min: 90, max: 220 }
    ];

    const randomJob = jobs[Math.floor(Math.random() * jobs.length)];
    const amount = Math.floor(Math.random() * (randomJob.max - randomJob.min + 1)) + randomJob.min;
    
    return {
        job: randomJob.name,
        amount: amount
    };
}

/**
 * Chuyển tiền giữa hai user
 */
async function transferMoney(fromUserId, toUserId, amount, description = 'Chuyển tiền') {
    try {
        const fromUser = await User.findById(fromUserId);
        const toUser = await User.findById(toUserId);

        if (!fromUser || !toUser) {
            throw new Error('Không tìm thấy người dùng');
        }

        if (fromUser.balance < amount) {
            throw new Error('Số dư không đủ');
        }

        if (amount <= 0) {
            throw new Error('Số tiền phải lớn hơn 0');
        }

        // Trừ tiền từ người gửi
        await fromUser.updateBalance(-amount, 'transfer_out');
        
        // Cộng tiền cho người nhận
        await toUser.updateBalance(amount, 'transfer_in');

        return {
            success: true,
            fromBalance: fromUser.balance,
            toBalance: toUser.balance
        };
    } catch (error) {
        console.error('Lỗi chuyển tiền:', error);
        throw error;
    }
}

/**
 * Tính toán lãi suất ngân hàng (nếu có)
 */
function calculateBankInterest(bankAmount, days = 1) {
    const interestRate = 0.001; // 0.1% mỗi ngày
    return Math.floor(bankAmount * interestRate * days);
}

/**
 * Validate số tiền
 */
function validateAmount(amount, maxAmount = null) {
    if (isNaN(amount) || amount <= 0) {
        throw new Error('Số tiền phải là số dương');
    }

    if (maxAmount && amount > maxAmount) {
        throw new Error(`Số tiền không được vượt quá ${maxAmount.toLocaleString('vi-VN')}`);
    }

    if (amount > **********) { // 1 tỷ
        throw new Error('Số tiền quá lớn');
    }

    return Math.floor(amount);
}

/**
 * Parse số tiền từ string
 */
function parseAmount(amountStr) {
    // Xử lý các từ khóa đặc biệt
    if (amountStr.toLowerCase() === 'all' || amountStr.toLowerCase() === 'tất cả') {
        return 'all';
    }

    if (amountStr.toLowerCase() === 'half' || amountStr.toLowerCase() === 'nửa') {
        return 'half';
    }

    // Xử lý số với đơn vị
    const units = {
        'k': 1000,
        'm': 1000000,
        'b': **********,
        'nghìn': 1000,
        'triệu': 1000000,
        'tỷ': **********
    };

    let amount = amountStr.toLowerCase().replace(/[,\s]/g, '');
    
    for (const [unit, multiplier] of Object.entries(units)) {
        if (amount.endsWith(unit)) {
            const number = parseFloat(amount.slice(0, -unit.length));
            if (!isNaN(number)) {
                return Math.floor(number * multiplier);
            }
        }
    }

    // Parse số thông thường
    const parsed = parseInt(amount.replace(/[,\s]/g, ''));
    if (isNaN(parsed)) {
        throw new Error('Số tiền không hợp lệ');
    }

    return parsed;
}

/**
 * Tính thời gian cooldown còn lại
 */
function getCooldownTime(lastTime, cooldownHours) {
    if (!lastTime) return 0;
    
    const lastMoment = moment(lastTime);
    const now = moment();
    const cooldownEnd = lastMoment.add(cooldownHours, 'hours');
    
    return Math.max(0, cooldownEnd.diff(now, 'seconds'));
}

/**
 * Format thời gian cooldown
 */
function formatCooldown(seconds) {
    if (seconds <= 0) return 'Có thể sử dụng ngay';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
        return `${hours} giờ ${minutes} phút`;
    } else if (minutes > 0) {
        return `${minutes} phút ${secs} giây`;
    } else {
        return `${secs} giây`;
    }
}

/**
 * Tạo số ngẫu nhiên trong khoảng
 */
function randomBetween(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Kiểm tra xem có phải số hợp lệ không
 */
function isValidNumber(value) {
    return !isNaN(value) && isFinite(value) && value >= 0;
}

module.exports = {
    canClaimDaily,
    canWork,
    calculateDailyReward,
    calculateWorkReward,
    transferMoney,
    calculateBankInterest,
    validateAmount,
    parseAmount,
    getCooldownTime,
    formatCooldown,
    randomBetween,
    isValidNumber
};
