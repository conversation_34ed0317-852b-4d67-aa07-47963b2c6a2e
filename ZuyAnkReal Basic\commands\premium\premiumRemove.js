const { EmbedBuilder } = require("discord.js");
const fs = require("fs");
const path = require("path");
const config = require("../../config.json");

const premiumFile = path.join(__dirname, "../../premium.json");

// Read premium data
function readPremiumData() {
    if (!fs.existsSync(premiumFile)) {
        fs.writeFileSync(premiumFile, JSON.stringify({ users: {} }, null, 2));
    }
    return JSON.parse(fs.readFileSync(premiumFile));
}

// Write premium data
function writePremiumData(data) {
    fs.writeFileSync(premiumFile, JSON.stringify(data, null, 2));
}

module.exports = {
    name: "premiumremove",
    description: "Remove a user's premium access (Owner Only)",
    execute(message, args) {
        if (message.author.id !== config.ownerID) {
            return message.reply("❌ **Only the bot owner can use this command!**");
        }

        let user = message.mentions.users.first() || message.guild.members.cache.get(args[0]);
        if (!user) return message.reply("❌ **Please mention a user or provide their ID!**");

        let data = readPremiumData();
        
        if (!data.users[user.id]) {
            return message.reply(`❌ **${user.username} does not have premium!**`);
        }

        delete data.users[user.id];
        writePremiumData(data);

        const embed = new EmbedBuilder()
            .setColor("#FF0000")
            .setTitle("🛑 Premium Removed")
            .setDescription(`Premium access has been **removed** for **${user.username}**.`);

        message.reply({ embeds: [embed] });
    }
};