(function(_0x380dd6,_0x4c9380){function _0x1f48dd(_0x4f8de7,_0xee0375){return _0x592f(_0xee0375-0x7f,_0x4f8de7);}const _0xe5f307=_0x380dd6();while(!![]){try{const _0x20d487=parseInt(_0x1f48dd(0x1f8,0x19e))/0x1+-parseInt(_0x1f48dd(0x1b9,0x15d))/0x2*(-parseInt(_0x1f48dd(0x1b0,0x1d8))/0x3)+parseInt(_0x1f48dd(0x16c,0x118))/0x4*(parseInt(_0x1f48dd(0xc3,0x115))/0x5)+parseInt(_0x1f48dd(0x1b6,0x1b4))/0x6+-parseInt(_0x1f48dd(0x1e3,0x195))/0x7+-parseInt(_0x1f48dd(0x1bd,0x201))/0x8+-parseInt(_0x1f48dd(0x17c,0x11b))/0x9*(-parseInt(_0x1f48dd(0x123,0x17d))/0xa);if(_0x20d487===_0x4c9380)break;else _0xe5f307['push'](_0xe5f307['shift']());}catch(_0x5cbe48){_0xe5f307['push'](_0xe5f307['shift']());}}}(_0x47b1,0x8a5a3));const {SlashCommandBuilder,EmbedBuilder,ActionRowBuilder,ButtonBuilder,ButtonStyle,StringSelectMenuBuilder,PermissionsBitField,ModalBuilder,TextInputBuilder,TextInputStyle}=require(_0x425867(0xa6,0xbd)),fs=require('fs'),yaml=require(_0x425867(0xb6,0x54)),mongoose=require('mongoose'),config=yaml[_0x425867(-0x4,0x67)](fs[_0x425867(0xa,-0x40)](_0x425867(0x99,0x51),_0x425867(0xb5,0xfc))),lang=yaml[_0x425867(-0x4,0x79)](fs[_0x425867(0xa,-0x24)](_0x425867(0xae,0x12f),_0x425867(0xb5,0x11f))),embedSchema=new mongoose[(_0x425867(0xdd,0x101))]({'name':String,'embedData':Object,'linkButtons':Array}),EmbedTemplate=mongoose[_0x425867(0x46,0x3)]('EmbedTemplate',embedSchema),activeInteractions=new Map();module[_0x425867(0x58,0x2e)]={'data':new SlashCommandBuilder()['setName'](_0x425867(0xdc,0x8f))[_0x425867(0x1c,0x22)](_0x425867(0x49,0x95))[_0x425867(0xdf,0xec)](_0x5e3f42=>_0x5e3f42[_0x425867(-0x1,-0x5f)](_0x425867(0x92,0x4f))['setDescription']('Create\x20a\x20new\x20embed\x20using\x20buttons'))['addSubcommand'](_0x29fdf2=>_0x29fdf2[_0x425867(-0x1,-0x1)]('edit')[_0x425867(0x1c,-0x63)](_0x425867(0x91,0xeb))[_0x425867(0x2c,0x24)](_0x471602=>_0x471602[_0x425867(-0x1,0x29)](_0x425867(0xd4,0x113))[_0x425867(0x1c,0x3a)](_0x425867(0xc8,0xc8))[_0x425867(0x60,0x6a)](!![]))),'category':_0x425867(0xc0,0x3c),async 'execute'(_0x184c3b){const _0x5e8ed5=_0x184c3b['member'],_0x35127d=_0x5e8ed5[_0x2d7fc3(0x437,0x454)]['cache'][_0x2d7fc3(0x4d8,0x511)](_0x2e81b=>config['ModerationRoles'][_0x2d7fc3(0x4f8,0x536)][_0x2d7fc3(0x4e3,0x550)](_0x2e81b['id'])),_0x408fd9=_0x5e8ed5['permissions'][_0x2d7fc3(0x4c6,0x472)](PermissionsBitField['Flags'][_0x2d7fc3(0x500,0x4f3)]);if(!_0x35127d&&!_0x408fd9)return _0x184c3b[_0x2d7fc3(0x415,0x3e9)]({'content':lang['NoPermsMessage'],'ephemeral':!![]});const _0x30a6a0=_0x184c3b[_0x2d7fc3(0x41e,0x43e)]['id'];function _0x2d7fc3(_0x586cea,_0x340bc0){return _0x425867(_0x586cea-0x41c,_0x340bc0);}if(activeInteractions[_0x2d7fc3(0x4c6,0x50a)](_0x30a6a0)){const _0x50d5c5=activeInteractions[_0x2d7fc3(0x40c,0x3ff)](_0x30a6a0);_0x50d5c5[_0x2d7fc3(0x412,0x397)]();}const _0x45d06b=_0x184c3b[_0x2d7fc3(0x495,0x45e)][_0x2d7fc3(0x46c,0x490)]();let _0x5ac82d=new EmbedBuilder()['setAuthor']({'name':_0x2d7fc3(0x4fd,0x4f3)})[_0x2d7fc3(0x4c9,0x4f0)](config['EmbedColors'])[_0x2d7fc3(0x438,0x3d0)](_0x2d7fc3(0x44b,0x3d4)),_0x9e57f4=null,_0x408909=[];if(_0x45d06b===_0x2d7fc3(0x4dd,0x4d9)){_0x9e57f4=_0x184c3b[_0x2d7fc3(0x495,0x50f)][_0x2d7fc3(0x4fa,0x48e)](_0x2d7fc3(0x4f0,0x4c8));try{const _0x100e30=await _0x184c3b[_0x2d7fc3(0x41a,0x462)][_0x2d7fc3(0x49d,0x4ea)][_0x2d7fc3(0x4ff,0x4e3)](_0x9e57f4);if(_0x100e30&&_0x100e30[_0x2d7fc3(0x42d,0x3f1)][0x0])_0x5ac82d=EmbedBuilder[_0x2d7fc3(0x4c3,0x4fe)](_0x100e30[_0x2d7fc3(0x42d,0x454)][0x0]),_0x408909=_0x100e30[_0x2d7fc3(0x4e5,0x526)][_0x2d7fc3(0x466,0x47f)](_0x14b676=>_0x14b676[_0x2d7fc3(0x4e5,0x4e3)][_0x2d7fc3(0x483,0x42c)](_0x9727ce=>_0x9727ce[_0x2d7fc3(0x456,0x446)]===_0x2d7fc3(0x4b1,0x4e0)&&_0x9727ce[_0x2d7fc3(0x40e,0x408)]===_0x2d7fc3(0x4bd,0x4e6)));else return _0x184c3b[_0x2d7fc3(0x415,0x416)]({'content':_0x2d7fc3(0x49e,0x4ac),'ephemeral':!![]});}catch(_0x4a623f){return _0x184c3b[_0x2d7fc3(0x415,0x46a)]({'content':_0x2d7fc3(0x40b,0x458),'ephemeral':!![]});}}const _0x11ef7c=Date['now']()[_0x2d7fc3(0x401,0x400)](),_0x501995=[..._0x408909],_0x42d88b=[new ButtonBuilder()[_0x2d7fc3(0x48a,0x479)](_0x2d7fc3(0x4f4,0x47e)+_0x11ef7c)[_0x2d7fc3(0x40d,0x3b3)](_0x2d7fc3(0x42a,0x450))[_0x2d7fc3(0x402,0x484)](ButtonStyle['Secondary']),new ButtonBuilder()['setCustomId'](_0x2d7fc3(0x434,0x464)+_0x11ef7c)[_0x2d7fc3(0x40d,0x3f2)](_0x2d7fc3(0x444,0x453))[_0x2d7fc3(0x402,0x3aa)](ButtonStyle[_0x2d7fc3(0x4ce,0x4c5)]),new ButtonBuilder()[_0x2d7fc3(0x48a,0x40f)](_0x2d7fc3(0x403,0x3f2)+_0x11ef7c)[_0x2d7fc3(0x40d,0x3c4)](_0x2d7fc3(0x4df,0x4f2))[_0x2d7fc3(0x402,0x424)](ButtonStyle[_0x2d7fc3(0x4ce,0x548)]),new ButtonBuilder()['setCustomId'](_0x2d7fc3(0x460,0x3f6)+_0x11ef7c)[_0x2d7fc3(0x40d,0x451)](_0x2d7fc3(0x4bb,0x44e))['setStyle'](ButtonStyle[_0x2d7fc3(0x4ce,0x536)]),new ButtonBuilder()[_0x2d7fc3(0x48a,0x42d)](_0x2d7fc3(0x46b,0x4e5)+_0x11ef7c)[_0x2d7fc3(0x40d,0x41d)]('Thumbnail\x20Image')[_0x2d7fc3(0x402,0x3ec)](ButtonStyle[_0x2d7fc3(0x4ce,0x4cf)]),new ButtonBuilder()['setCustomId']('image_'+_0x11ef7c)[_0x2d7fc3(0x40d,0x48a)](_0x2d7fc3(0x4c0,0x50d))['setStyle'](ButtonStyle[_0x2d7fc3(0x4ce,0x4e8)]),new ButtonBuilder()[_0x2d7fc3(0x48a,0x49b)](_0x2d7fc3(0x4e7,0x51d)+_0x11ef7c)[_0x2d7fc3(0x40d,0x39f)](_0x2d7fc3(0x470,0x495))['setStyle'](ButtonStyle[_0x2d7fc3(0x4ce,0x4a8)]),new ButtonBuilder()[_0x2d7fc3(0x48a,0x49f)](_0x2d7fc3(0x487,0x454)+_0x11ef7c)[_0x2d7fc3(0x40d,0x3d4)](_0x2d7fc3(0x46a,0x44c))[_0x2d7fc3(0x402,0x478)](ButtonStyle[_0x2d7fc3(0x4ce,0x515)]),new ButtonBuilder()['setCustomId'](_0x2d7fc3(0x4d9,0x4c6)+_0x11ef7c)[_0x2d7fc3(0x40d,0x441)]('Add\x20Link\x20Button')[_0x2d7fc3(0x402,0x47e)](ButtonStyle[_0x2d7fc3(0x4ce,0x49e)]),new ButtonBuilder()[_0x2d7fc3(0x48a,0x4cf)](_0x2d7fc3(0x424,0x48b)+_0x11ef7c)[_0x2d7fc3(0x40d,0x400)](_0x2d7fc3(0x4cc,0x4b7))[_0x2d7fc3(0x402,0x3a6)](ButtonStyle['Secondary']),new ButtonBuilder()['setCustomId'](_0x2d7fc3(0x4da,0x542)+_0x11ef7c)[_0x2d7fc3(0x40d,0x400)](_0x2d7fc3(0x44d,0x3f8))[_0x2d7fc3(0x402,0x3fa)](ButtonStyle[_0x2d7fc3(0x4ce,0x485)]),new ButtonBuilder()[_0x2d7fc3(0x48a,0x439)](_0x2d7fc3(0x4ef,0x479)+_0x11ef7c)[_0x2d7fc3(0x40d,0x427)](_0x2d7fc3(0x488,0x474))[_0x2d7fc3(0x402,0x38f)](ButtonStyle['Success']),new ButtonBuilder()['setCustomId'](_0x2d7fc3(0x4e0,0x511)+_0x11ef7c)[_0x2d7fc3(0x40d,0x3dc)](_0x2d7fc3(0x4be,0x440))[_0x2d7fc3(0x402,0x3f1)](ButtonStyle[_0x2d7fc3(0x4a2,0x4b7)]),new ButtonBuilder()[_0x2d7fc3(0x48a,0x506)](_0x2d7fc3(0x43e,0x417)+_0x11ef7c)[_0x2d7fc3(0x40d,0x3f9)](_0x2d7fc3(0x481,0x419))[_0x2d7fc3(0x402,0x402)](ButtonStyle[_0x2d7fc3(0x411,0x39e)])],_0x2decfb=[];for(let _0x351b19=0x0;_0x351b19<_0x42d88b[_0x2d7fc3(0x49f,0x4f1)];_0x351b19+=0x5){_0x2decfb[_0x2d7fc3(0x436,0x45c)](new ActionRowBuilder()[_0x2d7fc3(0x485,0x440)](_0x42d88b['slice'](_0x351b19,_0x351b19+0x5)));}const _0x46fe27=await EmbedTemplate[_0x2d7fc3(0x497,0x448)]()['select'](_0x2d7fc3(0x4cb,0x549));if(_0x46fe27[_0x2d7fc3(0x49f,0x45a)]>0x0){const _0x58c9f7=_0x46fe27['map']((_0xc54947,_0x338337)=>({'label':_0xc54947[_0x2d7fc3(0x4cb,0x4b5)],'value':_0x2d7fc3(0x502,0x4f8)+_0x338337})),_0x38b187=new StringSelectMenuBuilder()[_0x2d7fc3(0x48a,0x4c7)](_0x2d7fc3(0x443,0x3d9)+_0x11ef7c)[_0x2d7fc3(0x472,0x480)](_0x2d7fc3(0x4a9,0x43a))[_0x2d7fc3(0x43d,0x434)](_0x58c9f7);_0x2decfb['unshift'](new ActionRowBuilder()[_0x2d7fc3(0x485,0x4fc)](_0x38b187));}await _0x184c3b[_0x2d7fc3(0x415,0x3d1)]({'embeds':[_0x5ac82d],'components':combineComponents(_0x2decfb,_0x501995),'ephemeral':!![]});const _0x12e94f=_0x1d2b9d=>_0x1d2b9d['user']['id']===_0x184c3b['user']['id'],_0x552849=_0x184c3b['channel'][_0x2d7fc3(0x44a,0x4c3)]({'filter':_0x12e94f,'time':0xdbba0});activeInteractions[_0x2d7fc3(0x42c,0x465)](_0x30a6a0,_0x552849),_0x552849['on']('collect',async _0x12385a=>{function _0x382e39(_0x3df569,_0x52060b){return _0x2d7fc3(_0x3df569- -0x2cc,_0x52060b);}try{await handleButtonInteraction(_0x12385a,_0x5ac82d,_0x2decfb,_0x501995,_0x552849,_0x9e57f4);}catch(_0x8166a4){console['error'](_0x382e39(0x1ed,0x1e1),_0x8166a4),!_0x12385a[_0x382e39(0x1cc,0x1d9)]&&!_0x12385a[_0x382e39(0x1f8,0x1d9)]&&await _0x12385a['reply']({'content':_0x382e39(0x221,0x1f3),'ephemeral':!![]})['catch'](console['error']);}}),_0x552849['on']('end',async()=>{function _0x29b8e8(_0x4498ae,_0x20547b){return _0x2d7fc3(_0x20547b- -0x88,_0x4498ae);}activeInteractions[_0x29b8e8(0x3e4,0x40c)](_0x30a6a0);});}};async function handleButtonInteraction(_0x1612ef,_0x1d53ee,_0x2e3680,_0x39fdd6,_0xed7aa7,_0x498e4a){function _0x423946(_0x5e1349,_0x3f2764){return _0x425867(_0x5e1349-0x72,_0x3f2764);}switch(_0x1612ef[_0x423946(0x7b,0x5c)][_0x423946(0x8b,0x7e)]('_')[0x0]){case'title':await showTitleModal(_0x1612ef,_0x1d53ee,_0x2e3680,_0x39fdd6);break;case _0x423946(0xf2,0x143):await showDescriptionModal(_0x1612ef,_0x1d53ee,_0x2e3680,_0x39fdd6);break;case _0x423946(0x5d,0x5f):await showAuthorModal(_0x1612ef,_0x1d53ee,_0x2e3680,_0x39fdd6);break;case _0x423946(0x97,0x111):await showFooterModal(_0x1612ef,_0x1d53ee,_0x2e3680,_0x39fdd6);break;case _0x423946(0x90,0x32):await showThumbnailModal(_0x1612ef,_0x1d53ee,_0x2e3680,_0x39fdd6);break;case _0x423946(0xcb,0x120):await showImageModal(_0x1612ef,_0x1d53ee,_0x2e3680,_0x39fdd6);break;case'color':await showColorModal(_0x1612ef,_0x1d53ee,_0x2e3680,_0x39fdd6);break;case _0x423946(0xa4,0xbb):_0x1d53ee[_0x423946(0x11e,0x12b)][_0x423946(0xa4,0x26)]?_0x1d53ee['setTimestamp'](null):_0x1d53ee['setTimestamp']();await _0x1612ef[_0x423946(0x5a,-0x4)]({'embeds':[_0x1d53ee],'components':combineComponents(_0x2e3680,_0x39fdd6),'ephemeral':!![]});break;case _0x423946(0x134,0x100):await postEmbed(_0x1612ef,_0x1d53ee,_0x39fdd6,_0x498e4a);break;case _0x423946(0x5b,0x6):await saveTemplate(_0x1612ef,_0x1d53ee,_0x39fdd6);break;case _0x423946(0x92,0xf8):await loadTemplate(_0x1612ef,_0x1d53ee,_0x2e3680,_0x39fdd6);break;case _0x423946(0x131,0x15b):await promptAndDeleteTemplate(_0x1612ef);break;case _0x423946(0x8f,0xf7):await addLinkButton(_0x1612ef,_0x1d53ee,_0x39fdd6,_0x2e3680);break;case _0x423946(0xc9,0x7d):await removeLinkButton(_0x1612ef,_0x1d53ee,_0x39fdd6,_0x2e3680);break;case _0x423946(0xe7,0x105):await showAboveTextModal(_0x1612ef,_0x1d53ee,_0x2e3680,_0x39fdd6);break;}}async function showAboveTextModal(_0x5c154b,_0xe708c,_0x3b2749,_0x3ff425){function _0x5d1ea5(_0x4b9d14,_0x46e360){return _0x425867(_0x4b9d14-0x112,_0x46e360);}const _0x5db18e=new ModalBuilder()[_0x5d1ea5(0x180,0x16c)](_0x5d1ea5(0x1a6,0x12e))[_0x5d1ea5(0x14a,0x19c)](_0x5d1ea5(0x143,0x134)),_0x531985=new TextInputBuilder()['setCustomId']('aboveText')['setLabel']('Text\x20Above\x20Embed')[_0x5d1ea5(0xf8,0x113)](TextInputStyle[_0x5d1ea5(0x150,0x132)])[_0x5d1ea5(0x168,0x18e)](_0x5d1ea5(0xfe,0xc5))[_0x5d1ea5(0x172,0x1b2)](![]);_0x5db18e[_0x5d1ea5(0x17b,0x1c0)](new ActionRowBuilder()[_0x5d1ea5(0x17b,0x12b)](_0x531985));try{await _0x5c154b[_0x5d1ea5(0x10f,0x131)](_0x5db18e);const _0x43b5e6=await _0x5c154b['awaitModalSubmit']({'filter':_0x25e1d6=>_0x25e1d6[_0x5d1ea5(0x11b,0xa4)]==='abovetext_modal','time':0xea60}),_0x565fd9=_0x43b5e6[_0x5d1ea5(0x121,0xa3)]['getTextInputValue'](_0x5d1ea5(0x147,0xe5));!_0x5c154b[_0x5d1ea5(0x1eb,0x23f)][_0x5d1ea5(0x199,0x192)]&&(_0x5c154b[_0x5d1ea5(0x1eb,0x183)][_0x5d1ea5(0x199,0x14b)]={}),_0x5c154b[_0x5d1ea5(0x1eb,0x1b8)][_0x5d1ea5(0x199,0x204)][_0x5c154b[_0x5d1ea5(0x114,0x157)]['id']]={'aboveText':_0x565fd9},await _0x43b5e6['update']({'content':_0x565fd9?_0x5d1ea5(0x1cc,0x1e3)+_0x565fd9+'\x22':_0x5d1ea5(0x1aa,0x207),'embeds':[_0xe708c],'components':combineComponents(_0x3b2749,_0x3ff425),'ephemeral':!![]});}catch(_0x34c63e){console['error'](_0x5d1ea5(0x1c5,0x162),_0x34c63e),!_0x5c154b[_0x5d1ea5(0x1ba,0x1d6)]&&!_0x5c154b['deferred']?await _0x5c154b[_0x5d1ea5(0x10b,0xfd)]({'content':'An\x20error\x20occurred\x20while\x20setting\x20the\x20text\x20above\x20the\x20embed.\x20Please\x20try\x20again.','ephemeral':!![]}):await _0x5c154b[_0x5d1ea5(0x1f4,0x210)]({'content':_0x5d1ea5(0x13c,0x1aa),'ephemeral':!![]});}}async function showTitleModal(_0x1daf39,_0x405a0d,_0x588028,_0x3df5bf){const _0x37595f=new ModalBuilder()[_0x2709da(0x162,0x1bd)](_0x2709da(0x215,0x21b))['setTitle']('Set\x20Embed\x20Title');function _0x2709da(_0x2f25d6,_0x14753f){return _0x425867(_0x14753f-0x14f,_0x2f25d6);}const _0x4747e4=new TextInputBuilder()['setCustomId']('titleText')['setLabel'](_0x2709da(0x196,0x166))[_0x2709da(0x17c,0x135)](TextInputStyle[_0x2709da(0x150,0x18c)])[_0x2709da(0x20b,0x1eb)](_0x405a0d[_0x2709da(0x18d,0x1fb)][_0x2709da(0x24b,0x1d9)]||'')[_0x2709da(0x18e,0x1af)](![]);_0x37595f['addComponents'](new ActionRowBuilder()[_0x2709da(0x1ac,0x1b8)](_0x4747e4)),await _0x1daf39[_0x2709da(0x10e,0x14c)](_0x37595f);try{const _0x498cd6=await _0x1daf39['awaitModalSubmit']({'filter':_0x406a71=>_0x406a71[_0x2709da(0x1b2,0x158)]===_0x2709da(0x213,0x21b),'time':0xea60}),_0x5da56f=_0x498cd6[_0x2709da(0x109,0x15e)]['getTextInputValue'](_0x2709da(0x12e,0x1ad));_0x405a0d[_0x2709da(0x131,0x187)](_0x5da56f||null),await _0x498cd6[_0x2709da(0xc1,0x137)]({'embeds':[_0x405a0d],'components':combineComponents(_0x588028,_0x3df5bf),'ephemeral':!![]});}catch(_0x2ca3f8){console[_0x2709da(0x187,0x20a)](_0x2709da(0x13d,0x1b1),_0x2ca3f8),_0x2ca3f8[_0x2709da(0x17f,0x192)]===_0x2709da(0x275,0x200)?await _0x1daf39[_0x2709da(0x240,0x231)]({'content':_0x2709da(0x1af,0x1b2),'ephemeral':!![]}):await _0x1daf39['followUp']({'content':_0x2709da(0x108,0x15c),'ephemeral':!![]});}}async function showDescriptionModal(_0x39cf7d,_0xc0ec48,_0x3e48c8,_0x4461a1){const _0x214e53=new ModalBuilder()[_0x179118(0x188,0x1e4)](_0x179118(0x1e9,0x238))['setTitle'](_0x179118(0x13d,0x11d));function _0x179118(_0x19560b,_0x2b78f4){return _0x425867(_0x19560b-0x11a,_0x2b78f4);}const _0x3cd540=new TextInputBuilder()[_0x179118(0x188,0x1e4)](_0x179118(0x1c3,0x1ac))[_0x179118(0x10b,0xb5)]('Description')[_0x179118(0x100,0xd6)](TextInputStyle['Paragraph'])[_0x179118(0x1b6,0x1f8)](_0xc0ec48[_0x179118(0x1c6,0x154)][_0x179118(0x19a,0x1ea)]||'')['setRequired'](![]);_0x214e53[_0x179118(0x183,0x1cd)](new ActionRowBuilder()[_0x179118(0x183,0x19a)](_0x3cd540)),await _0x39cf7d[_0x179118(0x117,0xdc)](_0x214e53);try{const _0x1d197f=await _0x39cf7d[_0x179118(0x184,0x134)]({'filter':_0x4998e5=>_0x4998e5[_0x179118(0x123,0x182)]===_0x179118(0x1e9,0x259),'time':0xea60}),_0x53f61b=_0x1d197f[_0x179118(0x129,0x164)][_0x179118(0x187,0x176)](_0x179118(0x1c3,0x242));_0xc0ec48[_0x179118(0x136,0x1b4)](_0x53f61b||null),await _0x1d197f[_0x179118(0x102,0x103)]({'embeds':[_0xc0ec48],'components':combineComponents(_0x3e48c8,_0x4461a1),'ephemeral':!![]});}catch(_0x7a1365){console['error'](_0x179118(0x112,0x14c),_0x7a1365),_0x7a1365[_0x179118(0x15d,0x19f)]===_0x179118(0x1cb,0x1f0)?await _0x39cf7d[_0x179118(0x1fc,0x267)]({'content':_0x179118(0x1bd,0x1fc),'ephemeral':!![]}):await _0x39cf7d['followUp']({'content':_0x179118(0x11d,0x163),'ephemeral':!![]});}}async function showAuthorModal(_0xb39c15,_0x34a547,_0x29c4c4,_0x338866){const _0x1301af=new ModalBuilder()['setCustomId'](_0x2a38cc(0x243,0x1ed))[_0x2a38cc(0x23b,0x226)](_0x2a38cc(0x218,0x1b9)),_0x5b7144=new TextInputBuilder()[_0x2a38cc(0x271,0x245)](_0x2a38cc(0x1e5,0x185))['setLabel'](_0x2a38cc(0x2d1,0x332))['setStyle'](TextInputStyle[_0x2a38cc(0x240,0x1c1)])['setValue'](_0x34a547['data'][_0x2a38cc(0x1ee,0x186)]?.[_0x2a38cc(0x2b2,0x24a)]||'')[_0x2a38cc(0x263,0x2ca)](![]),_0x237b2f=new TextInputBuilder()[_0x2a38cc(0x271,0x298)](_0x2a38cc(0x2ae,0x271))[_0x2a38cc(0x1f4,0x222)](_0x2a38cc(0x24a,0x210))['setStyle'](TextInputStyle[_0x2a38cc(0x240,0x250)])[_0x2a38cc(0x29f,0x24c)](_0x34a547[_0x2a38cc(0x2af,0x277)][_0x2a38cc(0x1ee,0x241)]?.[_0x2a38cc(0x272,0x28a)]||'')[_0x2a38cc(0x263,0x232)](![]);function _0x2a38cc(_0x5ce62d,_0x322a6f){return _0x425867(_0x5ce62d-0x203,_0x322a6f);}_0x1301af[_0x2a38cc(0x26c,0x2e7)](new ActionRowBuilder()[_0x2a38cc(0x26c,0x1f8)](_0x5b7144),new ActionRowBuilder()['addComponents'](_0x237b2f));try{await _0xb39c15[_0x2a38cc(0x200,0x17e)](_0x1301af);}catch(_0x2bc21e){console[_0x2a38cc(0x2be,0x2dd)](_0x2a38cc(0x288,0x21a),_0x2bc21e);return;}try{const _0x5035b4=await _0xb39c15[_0x2a38cc(0x26d,0x24f)]({'filter':_0x54064e=>_0x54064e[_0x2a38cc(0x20c,0x221)]===_0x2a38cc(0x243,0x1f7),'time':0xea60}),_0x5e313e=_0x5035b4[_0x2a38cc(0x212,0x1f9)][_0x2a38cc(0x270,0x273)](_0x2a38cc(0x1e5,0x23b)),_0x2721ae=_0x5035b4[_0x2a38cc(0x212,0x1a4)][_0x2a38cc(0x270,0x256)](_0x2a38cc(0x2ae,0x2b9));if(_0x2721ae&&!isValidHttpUrl(_0x2721ae)){await _0x5035b4['reply']({'content':_0x2a38cc(0x24b,0x27e),'ephemeral':!![]});return;}try{_0x5e313e||_0x2721ae?_0x34a547[_0x2a38cc(0x2c9,0x256)]({'name':_0x5e313e||null,'iconURL':_0x2721ae||null}):_0x34a547[_0x2a38cc(0x2c9,0x32e)](null),await _0x5035b4[_0x2a38cc(0x1eb,0x265)]({'embeds':[_0x34a547],'components':combineComponents(_0x29c4c4,_0x338866),'ephemeral':!![]});}catch(_0x3911a2){console[_0x2a38cc(0x2be,0x307)]('Error\x20updating\x20interaction:',_0x3911a2),!_0x5035b4[_0x2a38cc(0x2ab,0x2fa)]&&await _0x5035b4[_0x2a38cc(0x1fc,0x1e8)]({'content':_0x2a38cc(0x207,0x227),'ephemeral':!![]});}}catch(_0x213595){console['error'](_0x2a38cc(0x29d,0x2df),_0x213595),_0x213595[_0x2a38cc(0x246,0x1d2)]===_0x2a38cc(0x2b4,0x28b)?await _0xb39c15[_0x2a38cc(0x2e5,0x2d0)]({'content':_0x2a38cc(0x27a,0x265),'ephemeral':!![]}):await _0xb39c15['followUp']({'content':'An\x20error\x20occurred\x20while\x20setting\x20the\x20author.\x20Please\x20try\x20again.','ephemeral':!![]});}}function _0x47b1(){const _0x4b930a=['title','Image\x20URL','protocol','Load\x20a\x20template','Error\x20setting\x20footer:','The\x20add\x20link\x20button\x20modal\x20timed\x20out.\x20Please\x20try\x20again.','templateName','Edit\x20an\x20existing\x20embed','create','2773122sLlwtc','abovetext_modal','BUTTON','Template\x20not\x20found.','Successfully\x20posted\x20the\x20embed!','Text\x20above\x20embed\x20has\x20been\x20cleared.','./config.yml','Error\x20in\x20author\x20modal:','An\x20error\x20occurred\x20while\x20setting\x20the\x20footer.\x20Please\x20try\x20again.','setValue','Error\x20handling\x20button\x20interaction:','splice','Footer','Template\x20Name','LINK','Save\x20Template','The\x20description\x20modal\x20timed\x20out.\x20Please\x20try\x20again.','Large\x20Image','imageUrl','discord.js','from','replied','descriptionText','has','authorIcon','data','setColor','./lang.yml','name','Remove\x20Link\x20Button','InteractionCollectorError','Secondary','Error\x20in\x20above\x20text\x20modal:','footer_modal','utf8','js-yaml','69KJqnEi','assign','removelink_select','Text\x20above\x20embed\x20has\x20been\x20set\x20to:\x20\x22','error','some','addlink_','abovetext_','deletetemplate','Utility','edit','post','Author','save_','Footer\x20Settings','setAuthor','includes','The\x20ID\x20of\x20the\x20message\x20to\x20edit','components','colorValue','color_','title_modal','image_modal','Author\x20Name','description_modal','color','An\x20unexpected\x20error\x20occurred.\x20Please\x20try\x20again.','buttonUrl','post_','messageid','Thumbnail\x20URL','Error\x20in\x20save\x20template\x20modal:','Save\x20Embed\x20Template','title_','client','setEmoji','map','embed','Schema','getString','addSubcommand','2991088rUpSIy','Embed\x20Builder','followUp','fetch','Administrator','Invalid\x20icon\x20URL.\x20Please\x20provide\x20a\x20valid\x20URL\x20for\x20the\x20footer\x20icon.','template_','There\x20are\x20no\x20templates\x20to\x20delete.','add_link_button_modal','authorName','Select\x20a\x20link\x20button\x20to\x20remove','Invalid\x20image\x20URL.\x20Please\x20provide\x20a\x20valid\x20image\x20URL.','toString','setStyle','author_','update','save','The\x20thumbnail\x20modal\x20timed\x20out.\x20Please\x20try\x20again.','author','Enter\x20text\x20to\x20appear\x20above\x20the\x20embed\x20(e.g.,\x20role\x20mentions)','findOne','text','Failed\x20to\x20fetch\x20the\x20message.\x20Please\x20ensure\x20the\x20message\x20ID\x20is\x20correct.','get','setLabel','style','Add\x20Link\x20Button','160430GgAyZZ','Danger','stop','88yYuUFB','Error\x20in\x20description\x20modal:','reply','710757PWlqEM','end','load','showModal','channel','setName','values','The\x20save\x20template\x20modal\x20timed\x20out.\x20Please\x20try\x20again.','user','An\x20error\x20occurred\x20while\x20setting\x20the\x20description.\x20Please\x20try\x20again.','An\x20error\x20occurred\x20while\x20updating\x20the\x20embed.\x20Please\x20try\x20again.','send','save_template_modal','The\x20color\x20modal\x20timed\x20out.\x20Please\x20try\x20again.','removelink_','customId','readFileSync','No\x20button\x20selected,\x20operation\x20cancelled.','The\x20message\x20was\x20not\x20found.\x20It\x20might\x20have\x20been\x20deleted.','An\x20error\x20occurred\x20while\x20setting\x20the\x20title.\x20Please\x20try\x20again.','Title\x20Text','fields','set','embeds','label','url','setImage','Author\x20Settings','Error\x20in\x20add\x20link\x20button\x20modal:','Title','description_','split','push','roles','setDescription','addlink','thumbnail','Template\x20\x27','loadtemplate','addOptions','deletetemplate_','Set\x20Embed\x20Description','footerText','footer','Error\x20in\x20footer\x20modal:','loadtemplate_','Description\x20Text','footerIcon','An\x20error\x20occurred\x20while\x20setting\x20the\x20text\x20above\x20the\x20embed.\x20Please\x20try\x20again.','thumbnail_modal','addStringOption','Successfully\x20edited\x20the\x20embed!','createMessageComponentCollector','Welcome\x20to\x20the\x20**interactive\x20embed\x20builder**.\x20Use\x20the\x20buttons\x20below\x20to\x20build\x20the\x20embed,\x20when\x20you\x27re\x20done\x20click\x20**Post\x20Embed**!','Footer\x20Icon\x20URL','Set\x20Text\x20Above\x20Embed','timestamp','Error\x20in\x20image\x20modal:','thumbnailUrl','aboveText','select','The\x20image\x20modal\x20timed\x20out.\x20Please\x20try\x20again.','setTitle','delete_template_select','type','test','33100GmpmFg','Short','Paragraph','Color\x20(Hex\x20code\x20or\x20integer)','author_modal','Template\x20deletion\x20cancelled.','collect','code','footer_','size','model','Author\x20Icon\x20URL','Invalid\x20icon\x20URL.\x20Please\x20provide\x20a\x20valid\x20URL\x20for\x20the\x20author\x20icon.','Manage\x20embeds','flatMap','Error\x20in\x20thumbnail\x20modal:','An\x20error\x20occurred\x20while\x20saving\x20the\x20template.\x20Please\x20try\x20again.','catch','Add\x20Timestamp','thumbnail_','getSubcommand','Error\x20posting\x20embed:','An\x20error\x20occurred\x20while\x20setting\x20the\x20image.\x20Please\x20try\x20again.','Set\x20Thumbnail\x20Image','Embed\x20Color','Invalid\x20color\x20value.\x20Please\x20provide\x20a\x20valid\x20hex\x20code\x20or\x20integer.','setPlaceholder','removelink','exports','image','buttonEmoji','setThumbnail','30IMpmMQ','An\x20error\x20occurred\x20while\x20setting\x20the\x20color.\x20Please\x20try\x20again.','titleText','An\x20error\x20occurred\x20while\x20posting\x20the\x20embed.\x20Please\x20try\x20again.','setRequired','setFooter','Error\x20in\x20title\x20modal:','The\x20title\x20modal\x20timed\x20out.\x20Please\x20try\x20again.','An\x20error\x20occurred\x20while\x20adding\x20the\x20link\x20button.\x20Please\x20try\x20again.','Delete\x20Template','editReply','filter','An\x20error\x20occurred\x20while\x20setting\x20the\x20thumbnail.\x20Please\x20try\x20again.','addComponents','awaitModalSubmit','timestamp_','Post\x20Embed','getTextInputValue','setCustomId','iconURL','There\x20are\x20no\x20link\x20buttons\x20to\x20remove.','https:','Button\x20URL','Select\x20a\x20link\x20button\x20to\x20remove:','7394002NEnYKb','abovetext','Template\x20saved\x20successfully!','The\x20author\x20modal\x20timed\x20out.\x20Please\x20try\x20again.','delete','options','Link','find','deferred','211215huNCiN','Set\x20Embed\x20Color','then','description','messages','Message\x20not\x20found\x20or\x20does\x20not\x20contain\x20an\x20embed.','length','\x27\x20deleted\x20successfully.','Error\x20showing\x20modal:','Primary','embedData','An\x20unexpected\x20error\x20occurred\x20while\x20setting\x20the\x20footer.\x20Please\x20try\x20again.','buttonLabel'];_0x47b1=function(){return _0x4b930a;};return _0x47b1();}async function showFooterModal(_0x2f2145,_0x5a37ba,_0x4d7516,_0x4727f8){const _0x45de9d=new ModalBuilder()['setCustomId'](_0x246d41(-0x7,-0x26))[_0x246d41(-0x83,-0x7f)](_0x246d41(0xa,-0x61)),_0x38e6f3=new TextInputBuilder()[_0x246d41(-0x4d,-0x76)]('footerText')[_0x246d41(-0xca,-0x97)]('Footer\x20Text')[_0x246d41(-0xd5,-0x7e)](TextInputStyle[_0x246d41(-0x7e,-0xce)])[_0x246d41(-0x1f,-0x4d)](_0x5a37ba[_0x246d41(-0xf,0x2c)]['footer']?.[_0x246d41(-0xcd,-0xd1)]||'')[_0x246d41(-0x5b,0x27)](![]),_0x17e87a=new TextInputBuilder()['setCustomId'](_0x246d41(-0x92,-0xdd))[_0x246d41(-0xca,-0x9f)](_0x246d41(-0x8b,-0xd3))[_0x246d41(-0xd5,-0x9d)](TextInputStyle[_0x246d41(-0x7e,-0x8a)])['setValue'](_0x5a37ba[_0x246d41(-0xf,0x6)][_0x246d41(-0x96,-0xf0)]?.[_0x246d41(-0x4c,-0x67)]||'')[_0x246d41(-0x5b,0x11)](![]);_0x45de9d[_0x246d41(-0x52,-0x1f)](new ActionRowBuilder()[_0x246d41(-0x52,-0x2f)](_0x38e6f3),new ActionRowBuilder()[_0x246d41(-0x52,-0x6e)](_0x17e87a));function _0x246d41(_0x281f2d,_0x20bf18){return _0x425867(_0x281f2d- -0xbb,_0x20bf18);}await _0x2f2145['showModal'](_0x45de9d);try{const _0x2e7f39=await _0x2f2145[_0x246d41(-0x51,0x2b)]({'filter':_0x386ba7=>_0x386ba7[_0x246d41(-0xb2,-0x86)]==='footer_modal','time':0xea60}),_0x57e435=_0x2e7f39[_0x246d41(-0xac,-0x9d)]['getTextInputValue'](_0x246d41(-0x97,-0x60)),_0xc2033e=_0x2e7f39[_0x246d41(-0xac,-0xf3)][_0x246d41(-0x4e,-0x15)](_0x246d41(-0x92,-0x81));if(_0xc2033e&&!isValidHttpUrl(_0xc2033e)){await _0x2e7f39['reply']({'content':_0x246d41(0x2a,0x40),'ephemeral':!![]});return;}try{_0x57e435||_0xc2033e?_0x5a37ba['setFooter']({'text':_0x57e435||null,'iconURL':_0xc2033e||null}):_0x5a37ba[_0x246d41(-0x5a,-0x2)](null),await _0x2e7f39[_0x246d41(-0xd3,-0x8c)]({'embeds':[_0x5a37ba],'components':combineComponents(_0x4d7516,_0x4727f8),'ephemeral':!![]});}catch(_0x588af2){console['error'](_0x246d41(-0x2d,0x4a),_0x588af2);let _0x2e45e8=_0x246d41(-0x33,0x36);await _0x2e7f39['reply']({'content':_0x2e45e8,'ephemeral':!![]});}}catch(_0x718dc){console[_0x246d41(0x0,0x3e)](_0x246d41(-0x95,-0x116),_0x718dc),_0x718dc[_0x246d41(-0x78,-0x19)]===_0x246d41(-0xa,0x55)?await _0x2f2145[_0x246d41(0x27,0x8f)]({'content':'The\x20footer\x20modal\x20timed\x20out.\x20Please\x20try\x20again.','ephemeral':!![]}):await _0x2f2145['followUp']({'content':_0x246d41(-0x20,0x28),'ephemeral':!![]});}}function _0x425867(_0x41c80f,_0xce160e){return _0x592f(_0x41c80f- -0xa2,_0xce160e);}async function showThumbnailModal(_0x3999c9,_0x86b3e6,_0x45508e,_0xa62a35){const _0x5d924d=new ModalBuilder()[_0x3abf30(-0x151,-0x14b)]('thumbnail_modal')[_0x3abf30(-0x187,-0x160)](_0x3abf30(-0x16c,-0x157));function _0x3abf30(_0x3fcc65,_0x5c961b){return _0x425867(_0x3fcc65- -0x1bf,_0x5c961b);}const _0x1bd18d=new TextInputBuilder()[_0x3abf30(-0x151,-0x14e)](_0x3abf30(-0x18b,-0x15a))[_0x3abf30(-0x1ce,-0x173)](_0x3abf30(-0xea,-0x10e))[_0x3abf30(-0x1d9,-0x24d)](TextInputStyle[_0x3abf30(-0x182,-0x1d7)])[_0x3abf30(-0x123,-0x186)](_0x86b3e6['data'][_0x3abf30(-0x1a1,-0x1e9)]?.[_0x3abf30(-0x1ac,-0x135)]||'')['setRequired'](![]);_0x5d924d['addComponents'](new ActionRowBuilder()[_0x3abf30(-0x156,-0x104)](_0x1bd18d)),await _0x3999c9[_0x3abf30(-0x1c2,-0x150)](_0x5d924d);const _0x3ef363=_0x561a4f=>_0x561a4f['customId']===_0x3abf30(-0x194,-0x211);_0x3999c9[_0x3abf30(-0x155,-0x15a)]({'filter':_0x3ef363,'time':0xea60})['then'](async _0x4cbc0c=>{function _0x4be114(_0x404a08,_0x5801d5){return _0x3abf30(_0x5801d5-0x12c,_0x404a08);}const _0x2b5a4e=_0x4cbc0c[_0x4be114(-0xcd,-0x84)]['getTextInputValue'](_0x4be114(0x5,-0x5f));if(_0x2b5a4e&&isValidHttpUrl(_0x2b5a4e)&&isImageUrl(_0x2b5a4e))_0x86b3e6[_0x4be114(-0x30,-0x38)](_0x2b5a4e);else{if(!_0x2b5a4e)_0x86b3e6[_0x4be114(-0x42,-0x38)](null);else{await _0x4cbc0c[_0x4be114(-0x39,-0x9a)]({'content':_0x4be114(-0x3c,-0xaf),'ephemeral':!![]});return;}}await _0x4cbc0c['update']({'embeds':[_0x86b3e6],'components':combineComponents(_0x45508e,_0xa62a35),'ephemeral':!![]});})[_0x3abf30(-0x172,-0x15e)](_0x16982c=>{function _0x3fe750(_0x4f5c8d,_0x113e7f){return _0x3abf30(_0x4f5c8d-0x171,_0x113e7f);}console[_0x3fe750(0x6d,0x85)](_0x3fe750(-0x3,-0x2c),_0x16982c),_0x16982c[_0x3fe750(-0xb,-0x47)]===_0x3fe750(0x63,0x21)?_0x3999c9[_0x3fe750(0x94,0x9f)]({'content':_0x3fe750(-0x64,-0x7f),'ephemeral':!![]}):_0x3999c9[_0x3fe750(0x94,0x1f)]({'content':_0x3fe750(0x1a,-0x13),'ephemeral':!![]});});}async function showImageModal(_0xa73726,_0x272ea2,_0x541165,_0x5dff9b){const _0x2f64e2=new ModalBuilder()[_0x5916d0(0x46a,0x4ab)]('image_modal')[_0x5916d0(0x434,0x48f)]('Set\x20Large\x20Image'),_0x2b2b95=new TextInputBuilder()[_0x5916d0(0x46a,0x47a)](_0x5916d0(0x4a1,0x4dd))[_0x5916d0(0x3ed,0x40f)](_0x5916d0(0x487,0x4e5))[_0x5916d0(0x3e2,0x378)](TextInputStyle[_0x5916d0(0x439,0x482)])['setValue'](_0x272ea2[_0x5916d0(0x4a8,0x4c0)]['image']?.[_0x5916d0(0x40f,0x3e3)]||'')[_0x5916d0(0x45c,0x417)](![]);_0x2f64e2[_0x5916d0(0x465,0x44e)](new ActionRowBuilder()[_0x5916d0(0x465,0x43c)](_0x2b2b95)),await _0xa73726[_0x5916d0(0x3f9,0x46c)](_0x2f64e2);function _0x5916d0(_0x244150,_0x3a6535){return _0x425867(_0x244150-0x3fc,_0x3a6535);}const _0xa4b96a=_0x70315d=>_0x70315d[_0x5916d0(0x405,0x40c)]===_0x5916d0(0x4c9,0x447);_0xa73726['awaitModalSubmit']({'filter':_0xa4b96a,'time':0xea60})['then'](async _0x5442a2=>{function _0x5eb689(_0x49836c,_0x30ea50){return _0x5916d0(_0x49836c- -0x104,_0x30ea50);}const _0x32de4b=_0x5442a2['fields'][_0x5eb689(0x365,0x33c)](_0x5eb689(0x39d,0x362));if(_0x32de4b&&isValidHttpUrl(_0x32de4b)&&isImageUrl(_0x32de4b))_0x272ea2[_0x5eb689(0x30c,0x2ac)](_0x32de4b);else{if(!_0x32de4b)_0x272ea2['setImage'](null);else{await _0x5442a2[_0x5eb689(0x2f1,0x32b)]({'content':_0x5eb689(0x2dc,0x305),'ephemeral':!![]});return;}}await _0x5442a2[_0x5eb689(0x2e0,0x273)]({'embeds':[_0x272ea2],'components':combineComponents(_0x541165,_0x5dff9b),'ephemeral':!![]});})[_0x5916d0(0x449,0x4c2)](_0x51d4c7=>{function _0xf78503(_0x3c68d4,_0x48877c){return _0x5916d0(_0x48877c- -0x47,_0x3c68d4);}console[_0xf78503(0x416,0x470)](_0xf78503(0x3b0,0x3e8),_0x51d4c7),_0x51d4c7['code']==='InteractionCollectorError'?_0xa73726[_0xf78503(0x50f,0x497)]({'content':_0xf78503(0x377,0x3ec),'ephemeral':!![]}):_0xa73726['followUp']({'content':_0xf78503(0x45d,0x407),'ephemeral':!![]});});}async function showColorModal(_0x45d648,_0xa468ba,_0x2aff13,_0x335fe8){const _0x4a4c15=new ModalBuilder()['setCustomId']('color_modal')[_0x1a326b(-0xe1,-0x11b)](_0x1a326b(-0x92,-0xd5));function _0x1a326b(_0x2ba063,_0x39c337){return _0x425867(_0x39c337- -0x153,_0x2ba063);}const _0x49bbaa=new TextInputBuilder()[_0x1a326b(-0x8e,-0xe5)](_0x1a326b(-0x99,-0x89))[_0x1a326b(-0x167,-0x162)](_0x1a326b(-0xb5,-0x114))['setStyle'](TextInputStyle[_0x1a326b(-0x107,-0x116)])[_0x1a326b(-0x10a,-0xb7)](_0xa468ba['data'][_0x1a326b(-0xc3,-0x83)]?_0xa468ba[_0x1a326b(-0x90,-0xa7)][_0x1a326b(-0x73,-0x83)][_0x1a326b(-0x157,-0x16e)](0x10):'')[_0x1a326b(-0x130,-0xf3)](![]);_0x4a4c15[_0x1a326b(-0x93,-0xea)](new ActionRowBuilder()[_0x1a326b(-0x84,-0xea)](_0x49bbaa)),await _0x45d648['showModal'](_0x4a4c15);const _0x3fbe25=_0x2b7b5f=>_0x2b7b5f[_0x1a326b(-0x146,-0x14a)]==='color_modal';_0x45d648['awaitModalSubmit']({'filter':_0x3fbe25,'time':0xea60})[_0x1a326b(-0xd5,-0xd4)](async _0x34b41d=>{function _0x226919(_0x1028f3,_0x150375){return _0x1a326b(_0x150375,_0x1028f3-0x83);}const _0x413aec=_0x34b41d[_0x226919(-0xc1,-0xd1)]['getTextInputValue'](_0x226919(-0x6,0x38));if(_0x413aec)try{_0xa468ba[_0x226919(-0x23,-0x4a)](_0x413aec);}catch(_0x1459aa){await _0x34b41d[_0x226919(-0xd7,-0x80)]({'content':_0x226919(-0x7b,-0x2c),'ephemeral':!![]});return;}else _0xa468ba[_0x226919(-0x23,0x46)](null);await _0x34b41d[_0x226919(-0xe8,-0x146)]({'embeds':[_0xa468ba],'components':combineComponents(_0x2aff13,_0x335fe8),'ephemeral':!![]});})[_0x1a326b(-0x13c,-0x106)](_0x13bbad=>{function _0x228ebd(_0x44d8d6,_0x401fdc){return _0x1a326b(_0x401fdc,_0x44d8d6-0x4f8);}console[_0x228ebd(0x460,0x41a)]('Error\x20in\x20color\x20modal:',_0x13bbad),_0x13bbad[_0x228ebd(0x3e8,0x3d2)]==='InteractionCollectorError'?_0x45d648['followUp']({'content':_0x228ebd(0x3ac,0x42c),'ephemeral':!![]}):_0x45d648[_0x228ebd(0x487,0x4c1)]({'content':_0x228ebd(0x402,0x3f2),'ephemeral':!![]});});}async function saveTemplate(_0x3b8b21,_0x199583,_0x271da2){const _0xea3818=new ModalBuilder()[_0x12d699(0x22a,0x206)](_0x12d699(0x1c2,0x165))[_0x12d699(0x1f4,0x1f4)](_0x12d699(0x293,0x256)),_0x12459e=new TextInputBuilder()[_0x12d699(0x22a,0x214)](_0x12d699(0x24c,0x1f2))[_0x12d699(0x1ad,0x18b)](_0x12d699(0x25c,0x2c8))[_0x12d699(0x1a2,0x207)](TextInputStyle[_0x12d699(0x1f9,0x206)])['setRequired'](!![]);_0xea3818[_0x12d699(0x225,0x1ba)](new ActionRowBuilder()['addComponents'](_0x12459e)),await _0x3b8b21[_0x12d699(0x1b9,0x1a7)](_0xea3818);function _0x12d699(_0x271476,_0x22b002){return _0x425867(_0x271476-0x1bc,_0x22b002);}const _0x136649=_0x192393=>_0x192393[_0x12d699(0x1c5,0x1a9)]===_0x12d699(0x1c2,0x1db);_0x3b8b21[_0x12d699(0x226,0x298)]({'filter':_0x136649,'time':0xea60})[_0x12d699(0x23b,0x27c)](async _0x2524a5=>{const _0x2bc53d=_0x2524a5['fields'][_0x5462c9(0x96,0x88)]('templateName'),_0x389ce4=await EmbedTemplate['findOne']({'name':_0x2bc53d});if(_0x389ce4){await _0x2524a5[_0x5462c9(0x8,0x14)]({'content':'A\x20template\x20with\x20this\x20name\x20already\x20exists.\x20Please\x20choose\x20a\x20different\x20name.','ephemeral':!![]});return;}const _0x139b32=new EmbedTemplate({'name':_0x2bc53d,'embedData':_0x199583['toJSON'](),'linkButtons':_0x271da2[_0x5462c9(0x136,0xf6)](_0x46f84a=>_0x46f84a['toJSON']())});function _0x5462c9(_0x4aaf45,_0x4c9ea0){return _0x12d699(_0x4c9ea0- -0x1a1,_0x4aaf45);}await _0x139b32['save'](),await _0x2524a5['reply']({'content':_0x5462c9(0xa6,0x91),'ephemeral':!![]});})[_0x12d699(0x209,0x242)](_0x2afffe=>{function _0x28b084(_0x41a196,_0xed62ee){return _0x12d699(_0xed62ee-0x27d,_0x41a196);}console[_0x28b084(0x559,0x4f4)](_0x28b084(0x563,0x50f),_0x2afffe),_0x2afffe['code']===_0x28b084(0x46c,0x4ea)?_0x3b8b21[_0x28b084(0x4b6,0x51b)]({'content':_0x28b084(0x3f1,0x43a),'ephemeral':!![]}):_0x3b8b21[_0x28b084(0x4c3,0x51b)]({'content':_0x28b084(0x4c5,0x485),'ephemeral':!![]});});}function _0x592f(_0x27ad73,_0x13b5a4){const _0x47b10f=_0x47b1();return _0x592f=function(_0x592f76,_0xcd4ca0){_0x592f76=_0x592f76-0x82;let _0x1b96f5=_0x47b10f[_0x592f76];return _0x1b96f5;},_0x592f(_0x27ad73,_0x13b5a4);}async function loadTemplate(_0x30431e,_0x2598a5,_0x1d9206,_0x1773a6){const _0x3362c2=parseInt(_0x30431e[_0x456966(0x3a6,0x3c9)][0x0][_0x456966(0x36a,0x3e2)]('_')[0x1]),_0x2bf73a=await EmbedTemplate[_0x456966(0x3f5,0x444)]()[_0x456966(0x441,0x3ff)](_0x456966(0x420,0x478));function _0x456966(_0xef816b,_0x170492){return _0x425867(_0x170492-0x3c9,_0xef816b);}const _0x3f1e9f=await EmbedTemplate[_0x456966(0x3ba,0x3b6)]({'name':_0x2bf73a[_0x3362c2][_0x456966(0x4fa,0x478)]});_0x3f1e9f?(Object[_0x456966(0x505,0x481)](_0x2598a5,new EmbedBuilder(_0x3f1e9f['embedData'])),_0x1773a6[_0x456966(0x420,0x467)](0x0,_0x1773a6[_0x456966(0x3f2,0x44c)],..._0x3f1e9f['linkButtons'][_0x456966(0x483,0x4a4)](_0x1d883a=>ButtonBuilder[_0x456966(0x44e,0x470)](_0x1d883a))),await _0x30431e['update']({'embeds':[_0x2598a5],'components':combineComponents(_0x1d9206,_0x1773a6),'ephemeral':!![]})):await _0x30431e['reply']({'content':_0x456966(0x485,0x45f),'ephemeral':!![]});}async function promptAndDeleteTemplate(_0x6fa13d){const _0x4a569f=await EmbedTemplate[_0x4c6291(0xbb,0x50)]()[_0x4c6291(0x76,0x69)](_0x4c6291(0xef,0x162));if(_0x4a569f[_0x4c6291(0xc3,0x118)]===0x0){await _0x6fa13d[_0x4c6291(0x39,0x9b)]({'content':_0x4c6291(0x20,-0x4d),'ephemeral':!![]});return;}function _0x4c6291(_0x58fcf1,_0x19c1ad){return _0x425867(_0x58fcf1-0x40,_0x19c1ad);}const _0x24dc9e=_0x4a569f[_0x4c6291(0x11b,0xea)](_0x41cc26=>({'label':_0x41cc26[_0x4c6291(0xef,0x117)],'value':_0x41cc26[_0x4c6291(0xef,0xd2)]})),_0x379014=new StringSelectMenuBuilder()[_0x4c6291(0xae,0xb9)](_0x4c6291(0x79,0x11))[_0x4c6291(0x96,0x1a)]('Select\x20a\x20template\x20to\x20delete')[_0x4c6291(0x61,-0x18)](_0x24dc9e),_0x316601=new ActionRowBuilder()[_0x4c6291(0xa9,0x2a)](_0x379014);await _0x6fa13d[_0x4c6291(0x39,0xba)]({'content':'Select\x20a\x20template\x20to\x20delete:','components':[_0x316601],'ephemeral':!![]});const _0x37e5e2=_0x102711=>_0x102711[_0x4c6291(0x42,-0x33)]['id']===_0x6fa13d[_0x4c6291(0x42,0x81)]['id']&&_0x102711[_0x4c6291(0x49,0x12)]===_0x4c6291(0x79,-0x8),_0x1a6624=_0x6fa13d[_0x4c6291(0x3e,0x3)]['createMessageComponentCollector']({'filter':_0x37e5e2,'time':0x7530,'max':0x1});_0x1a6624['on'](_0x4c6291(0x82,0x16),async _0x257af2=>{const _0x34bfab=_0x257af2['values'][0x0];function _0x349fb2(_0x4a1f7e,_0x31b630){return _0x4c6291(_0x4a1f7e-0x25f,_0x31b630);}const _0xb8249e=await EmbedTemplate['deleteOne']({'name':_0x34bfab});_0xb8249e['deletedCount']>0x0?await _0x257af2[_0x349fb2(0x287,0x28a)]({'content':'Template\x20\x27'+_0x34bfab+_0x349fb2(0x323,0x2b6),'components':[],'ephemeral':!![]}):await _0x257af2[_0x349fb2(0x287,0x22d)]({'content':_0x349fb2(0x2be,0x242)+_0x34bfab+'\x27\x20not\x20found.','components':[],'ephemeral':!![]});}),_0x1a6624['on'](_0x4c6291(0x3b,0x31),_0x1243ab=>{function _0x5c6aea(_0x53ec55,_0x3606c0){return _0x4c6291(_0x53ec55- -0x4,_0x3606c0);}_0x1243ab[_0x5c6aea(0x81,0x6b)]===0x0&&_0x6fa13d[_0x5c6aea(0xa2,0xe6)]({'content':_0x5c6aea(0x7d,0x97),'components':[],'ephemeral':!![]});});}async function addLinkButton(_0x5594cf,_0x153cbd,_0x1701cd,_0x18519a){const _0x2dd889=new ModalBuilder()[_0x5544bd(0x19,0x25)](_0x5544bd(-0x74,-0x9a))[_0x5544bd(-0x1d,0x1b)](_0x5544bd(-0x62,-0xca));function _0x5544bd(_0x59b941,_0x564430){return _0x425867(_0x59b941- -0x55,_0x564430);}const _0x140cc7=new TextInputBuilder()['setCustomId'](_0x5544bd(0x7d,0xa8))[_0x5544bd(-0x64,-0x69)](_0x5544bd(0x1d,-0x3b))[_0x5544bd(-0x6f,-0x5)](TextInputStyle[_0x5544bd(-0x18,-0x2a)])[_0x5544bd(0xb,0x8d)](!![]),_0x332740=new TextInputBuilder()['setCustomId'](_0x5544bd(0x34,-0x27))[_0x5544bd(-0x64,-0x7)]('Button\x20Label')[_0x5544bd(-0x6f,-0x84)](TextInputStyle['Short'])['setRequired'](!![]),_0x7dd375=new TextInputBuilder()['setCustomId'](_0x5544bd(0x5,-0x59))['setLabel']('Button\x20Emoji')['setStyle'](TextInputStyle['Short'])[_0x5544bd(0xb,-0x21)](![]);_0x2dd889[_0x5544bd(0x14,-0x64)](new ActionRowBuilder()[_0x5544bd(0x14,0x91)](_0x140cc7),new ActionRowBuilder()[_0x5544bd(0x14,0x2d)](_0x332740),new ActionRowBuilder()[_0x5544bd(0x14,-0x4b)](_0x7dd375)),await _0x5594cf[_0x5544bd(-0x58,0x1)](_0x2dd889);const _0x1f2bb1=_0x2ceef3=>_0x2ceef3[_0x5544bd(-0x4c,0xe)]==='add_link_button_modal';_0x5594cf['awaitModalSubmit']({'filter':_0x1f2bb1,'time':0xea60})[_0x5544bd(0x2a,0xae)](async _0x34cbd4=>{const _0x476907=_0x34cbd4[_0x5e9365(-0x314,-0x2f2)]['getTextInputValue'](_0x5e9365(-0x1e5,-0x22f)),_0x45694b=_0x34cbd4[_0x5e9365(-0x340,-0x2f2)][_0x5e9365(-0x2b3,-0x294)](_0x5e9365(-0x200,-0x278)),_0x267bfc=_0x34cbd4[_0x5e9365(-0x2d7,-0x2f2)]['getTextInputValue'](_0x5e9365(-0x2a7,-0x2a7));if(!isValidHttpUrl(_0x476907)){await _0x34cbd4['reply']({'content':'Invalid\x20URL.\x20Please\x20provide\x20a\x20valid\x20URL.','ephemeral':!![]});return;}function _0x5e9365(_0x434bcf,_0x49928c){return _0x5544bd(_0x49928c- -0x2ac,_0x434bcf);}const _0x1a53f2=new ButtonBuilder()['setURL'](_0x476907)[_0x5e9365(-0x35e,-0x310)](_0x45694b)['setStyle'](ButtonStyle[_0x5e9365(-0x2ae,-0x287)]);_0x267bfc&&_0x1a53f2[_0x5e9365(-0x246,-0x227)](_0x267bfc),_0x1701cd['push'](_0x1a53f2),await _0x34cbd4['update']({'embeds':[_0x153cbd],'components':combineComponents(_0x18519a,_0x1701cd),'ephemeral':!![]});})['catch'](_0x27e2df=>{function _0x550b6d(_0x5eca3c,_0x1959f1){return _0x5544bd(_0x1959f1-0x19c,_0x5eca3c);}console[_0x550b6d(0x194,0x202)](_0x550b6d(0x1dc,0x15d),_0x27e2df),_0x27e2df[_0x550b6d(0x12b,0x18a)]===_0x550b6d(0x27a,0x1f8)?_0x5594cf[_0x550b6d(0x267,0x229)]({'content':_0x550b6d(0x160,0x1d6),'ephemeral':!![]}):_0x5594cf[_0x550b6d(0x25a,0x229)]({'content':_0x550b6d(0x13d,0x1ab),'ephemeral':!![]});});}async function removeLinkButton(_0x1e2af7,_0x340cfe,_0x5c7f0f,_0x2d7952){function _0x104192(_0x11e961,_0x1daf45){return _0x425867(_0x1daf45-0x2ad,_0x11e961);}if(_0x5c7f0f[_0x104192(0x39d,0x330)]===0x0){await _0x1e2af7[_0x104192(0x257,0x2a6)]({'content':_0x104192(0x364,0x31d),'ephemeral':!![]});return;}const _0x4c8571=_0x5c7f0f['map']((_0x5edd82,_0x52cd24)=>({'label':_0x5edd82[_0x104192(0x2ea,0x359)][_0x104192(0x343,0x2bf)],'value':_0x52cd24[_0x104192(0x222,0x292)]()})),_0x55d545=new StringSelectMenuBuilder()[_0x104192(0x2c7,0x31b)](_0x104192(0x313,0x366))['setPlaceholder'](_0x104192(0x2d5,0x290))['addOptions'](_0x4c8571),_0x4383b6=[new ActionRowBuilder()['addComponents'](_0x55d545)];await _0x1e2af7[_0x104192(0x251,0x2a6)]({'content':_0x104192(0x2d3,0x320),'components':_0x4383b6,'ephemeral':!![]});const _0x3fe5b4=_0x237db1=>_0x237db1[_0x104192(0x2e6,0x2af)]['id']===_0x1e2af7[_0x104192(0x2be,0x2af)]['id']&&_0x237db1[_0x104192(0x30f,0x2b6)]===_0x104192(0x37a,0x366),_0x5b3820=_0x1e2af7['channel'][_0x104192(0x2e9,0x2db)]({'filter':_0x3fe5b4,'max':0x1,'time':0xea60});_0x5b3820['on'](_0x104192(0x2bd,0x2ef),async _0x14ae95=>{const _0x263642=parseInt(_0x14ae95[_0x8b92bb(-0x289,-0x2e1)][0x0],0xa);function _0x8b92bb(_0x3d2d7c,_0x3191a6){return _0x104192(_0x3d2d7c,_0x3191a6- -0x58e);}_0x5c7f0f['splice'](_0x263642,0x1),await _0x14ae95['update']({'embeds':[_0x340cfe],'components':combineComponents(_0x2d7952,_0x5c7f0f),'ephemeral':!![]});}),_0x5b3820['on']('end',async _0x372d29=>{function _0x466102(_0x513c4e,_0x4ec685){return _0x104192(_0x4ec685,_0x513c4e-0x178);}_0x372d29[_0x466102(0x46a,0x413)]===0x0&&await _0x1e2af7[_0x466102(0x507,0x53e)]({'content':_0x466102(0x430,0x428),'ephemeral':!![]});});}async function postEmbed(_0x26bded,_0x2dc168,_0x589bc9,_0x35f93e){function _0x90e55f(_0x592a20,_0x4cb678){return _0x425867(_0x592a20- -0x26d,_0x4cb678);}const _0x3efce1=linkButtonsToComponents(_0x589bc9),_0x35b777=_0x26bded[_0x90e55f(-0x194,-0x152)][_0x90e55f(-0x1e6,-0x247)]&&_0x26bded['client'][_0x90e55f(-0x1e6,-0x1e8)][_0x26bded[_0x90e55f(-0x26b,-0x214)]['id']]?_0x26bded[_0x90e55f(-0x194,-0x180)][_0x90e55f(-0x1e6,-0x1ad)][_0x26bded['user']['id']][_0x90e55f(-0x238,-0x2a6)]||'':'';try{if(_0x35f93e)try{const _0x4d2d22=await _0x26bded['channel']['messages'][_0x90e55f(-0x18a,-0x1af)](_0x35f93e);await _0x4d2d22[_0x90e55f(-0x1ac,-0x1de)]({'content':_0x35b777,'embeds':[_0x2dc168],'components':_0x3efce1}),await _0x26bded[_0x90e55f(-0x274,-0x252)]({'content':_0x90e55f(-0x240,-0x21a),'ephemeral':!![]});}catch(_0x4f4694){if(_0x4f4694[_0x90e55f(-0x22a,-0x27f)]===0xc355)await _0x26bded[_0x90e55f(-0x274,-0x200)]({'content':'I\x20do\x20not\x20have\x20permission\x20to\x20edit\x20this\x20message.\x20Please\x20ensure\x20I\x20have\x20the\x20necessary\x20permissions.','ephemeral':!![]});else{if(_0x4f4694['code']===0x2718)await _0x26bded['reply']({'content':_0x90e55f(-0x261,-0x222),'ephemeral':!![]});else throw _0x4f4694;}}else await _0x26bded[_0x90e55f(-0x26f,-0x218)][_0x90e55f(-0x268,-0x2af)]({'content':_0x35b777,'embeds':[_0x2dc168],'components':_0x3efce1}),await _0x26bded['reply']({'content':_0x90e55f(-0x1d6,-0x244),'ephemeral':!![]});_0x26bded['client'][_0x90e55f(-0x1e6,-0x1e9)]&&_0x26bded['client'][_0x90e55f(-0x1e6,-0x1b5)][_0x26bded['user']['id']]&&delete _0x26bded[_0x90e55f(-0x194,-0x136)][_0x90e55f(-0x1e6,-0x20b)][_0x26bded[_0x90e55f(-0x26b,-0x21a)]['id']];}catch(_0x1468fe){console[_0x90e55f(-0x1b2,-0x201)](_0x90e55f(-0x21c,-0x24d),_0x1468fe),!_0x26bded[_0x90e55f(-0x1c5,-0x248)]&&!_0x26bded['deferred']?await _0x26bded[_0x90e55f(-0x274,-0x2e1)]({'content':_0x90e55f(-0x20e,-0x1c4),'ephemeral':!![]}):await _0x26bded[_0x90e55f(-0x18b,-0x118)]({'content':'An\x20error\x20occurred\x20while\x20posting\x20the\x20embed.\x20Please\x20try\x20again.','ephemeral':!![]});}}function combineComponents(_0x548705,_0xaac367){function _0x3d47d5(_0x5d5620,_0x2ca75c){return _0x425867(_0x5d5620- -0x234,_0x2ca75c);}const _0x59a1dd=[..._0x548705],_0x4f0285=linkButtonsToComponents(_0xaac367);return _0x59a1dd[_0x3d47d5(-0x21a,-0x21d)](..._0x4f0285),_0x59a1dd;}function linkButtonsToComponents(_0x2c8106){const _0x1d7104=[];for(let _0x39a761=0x0;_0x39a761<_0x2c8106[_0x5f2be7(0x256,0x239)];_0x39a761+=0x5){_0x1d7104[_0x5f2be7(0x1b8,0x1d0)](new ActionRowBuilder()[_0x5f2be7(0x20c,0x21f)](_0x2c8106['slice'](_0x39a761,_0x39a761+0x5)));}function _0x5f2be7(_0x32a4b8,_0x3e3e62){return _0x425867(_0x3e3e62-0x1b6,_0x32a4b8);}return _0x1d7104;}function isValidHttpUrl(_0xf5aded){function _0x3f67f5(_0x139d0b,_0x3fb191){return _0x425867(_0x139d0b-0x272,_0x3fb191);}try{const _0xe7ce4b=new URL(_0xf5aded);return _0xe7ce4b[_0x3f67f5(0x2fe,0x301)]==='http:'||_0xe7ce4b[_0x3f67f5(0x2fe,0x364)]===_0x3f67f5(0x2e3,0x286);}catch(_0x569b04){return![];}}function isImageUrl(_0x2ff2ac){function _0x244b0f(_0x41ab76,_0x335a66){return _0x425867(_0x41ab76-0xc5,_0x335a66);}return/\.(jpeg|jpg|gif|png|svg)$/i[_0x244b0f(0x100,0x12f)](_0x2ff2ac);}