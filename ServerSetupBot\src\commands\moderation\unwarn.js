const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('unwarn')
        .setDescription('Hủy cảnh báo cho thành viên')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Thành viên cần hủy cảnh báo')
                .setRequired(true)
        )
        .addIntegerOption(option =>
            option.setName('warning_id')
                .setDescription('ID cảnh báo cần hủy (để trống để hủy tất cả)')
                .setRequired(false)
                .setMinValue(1)
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Lý do hủy cảnh báo')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.ModerateMembers)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                'Bạn cần quyền `Moderate Members` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const user = interaction.options.getUser('user');
        const warningId = interaction.options.getInteger('warning_id');
        const reason = interaction.options.getString('reason') || 'Không có lý do';
        const guildId = interaction.guild.id;
        
        try {
            // Kiểm tra hierarchy nếu member tồn tại
            const member = await interaction.guild.members.fetch(user.id).catch(() => null);
            if (member) {
                if (member.roles.highest.position >= interaction.member.roles.highest.position) {
                    const errorEmbed = createErrorEmbed(
                        'Không thể hủy cảnh báo!',
                        'Bạn không thể hủy cảnh báo cho thành viên có role cao hơn hoặc bằng bạn.'
                    );
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
            }
            
            let result;
            let removedCount = 0;
            
            if (warningId) {
                // Hủy cảnh báo cụ thể
                result = await client.db.removeWarning(guildId, warningId, interaction.user.id, reason);
                if (result.changes > 0) {
                    removedCount = 1;
                } else {
                    const errorEmbed = createErrorEmbed(
                        'Không tìm thấy cảnh báo!',
                        `Không tìm thấy cảnh báo với ID ${warningId} hoặc cảnh báo đã bị hủy.`
                    );
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
            } else {
                // Hủy tất cả cảnh báo
                const activeWarnings = await client.db.getWarnings(guildId, user.id, true);
                if (activeWarnings.length === 0) {
                    const errorEmbed = createErrorEmbed(
                        'Không có cảnh báo!',
                        'Thành viên này không có cảnh báo đang hoạt động.'
                    );
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
                
                result = await client.db.removeAllWarnings(guildId, user.id, interaction.user.id, reason);
                removedCount = activeWarnings.length;
            }
            
            // Log vào moderation logs
            await client.db.addModerationLog(guildId, {
                type: 'unwarn',
                userId: user.id,
                moderatorId: interaction.user.id,
                reason: reason,
                warningId: warningId || null
            });
            
            // Lấy số lượng warning còn lại
            const remainingWarnings = await client.db.getWarningCount(guildId, user.id);
            
            // Gửi DM cho user
            try {
                const dmEmbed = createInfoEmbed(
                    `Cảnh báo của bạn đã được hủy trong ${interaction.guild.name}`,
                    `**Số lượng hủy:** ${removedCount} cảnh báo\n` +
                    `**Cảnh báo còn lại:** ${remainingWarnings}\n` +
                    `**Lý do:** ${reason}\n` +
                    `**Bởi:** ${interaction.user.tag}\n` +
                    `**Thời gian:** <t:${Math.floor(Date.now() / 1000)}:F>`
                );
                await user.send({ embeds: [dmEmbed] });
            } catch (error) {
                console.log('Không thể gửi DM cho user được unwarn');
            }
            
            const successEmbed = createSuccessEmbed(
                '✅ Cảnh báo đã được hủy!',
                `**Thành viên:** ${user.tag} (${user.id})\n` +
                `**Số lượng hủy:** ${removedCount} cảnh báo\n` +
                `**Cảnh báo còn lại:** ${remainingWarnings}\n` +
                `${warningId ? `**ID cảnh báo:** ${warningId}\n` : ''}` +
                `**Lý do:** ${reason}\n` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            await interaction.reply({ embeds: [successEmbed] });
            
            // Gửi log vào channel
            await sendModerationLog(client, guildId, {
                type: 'unwarn',
                user: user,
                moderator: interaction.user,
                reason: reason,
                count: removedCount,
                remaining: remainingWarnings
            });
            
        } catch (error) {
            console.error('Lỗi khi unwarn user:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi hủy cảnh báo!',
                'Đã xảy ra lỗi khi hủy cảnh báo. Vui lòng thử lại sau!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
