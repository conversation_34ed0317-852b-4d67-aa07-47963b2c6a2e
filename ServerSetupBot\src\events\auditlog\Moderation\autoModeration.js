const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.AutoModerationActionExecution,
    async execute(autoModerationActionExecution, client) {
        try {
            console.log(`🛡️ AutoMod action executed in ${autoModerationActionExecution.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(autoModerationActionExecution.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'AUTO_MODERATION')) return;
            
            // Create event data
            const eventData = {
                eventType: 'AUTO_MODERATION',
                user: autoModerationActionExecution.user?.tag || 'Unknown',
                userId: autoModerationActionExecution.userId,
                action: 'AutoMod đã thực hiện hành động',
                details: `AutoMod đã thực hiện hành động đối với **${autoModerationActionExecution.user?.tag || 'Unknown'}**`,
                target: autoModerationActionExecution.user?.tag || 'Unknown',
                channel: autoModerationActionExecution.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(autoModerationActionExecution.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🛡️ AutoMod thực hiện hành động',
                `AutoMod vừa thực hiện một hành động trong server`
            );
            
            embed.setColor(0xe67e22); // Orange for moderation
            
            // Get action type names
            const actionTypes = {
                1: 'Chặn tin nhắn',
                2: 'Gửi cảnh báo',
                3: 'Timeout người dùng'
            };
            
            // Get trigger type names
            const triggerTypes = {
                1: 'Từ khóa',
                2: 'Link có hại',
                3: 'Spam',
                4: 'Từ khóa có sẵn',
                5: 'Mention spam'
            };
            
            embed.addFields([
                {
                    name: '> Người vi phạm',
                    value: `- ${autoModerationActionExecution.user?.tag || 'Unknown'} (${autoModerationActionExecution.user || 'Unknown'})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${autoModerationActionExecution.userId}`,
                    inline: true
                },
                {
                    name: '> Kênh vi phạm',
                    value: `- ${autoModerationActionExecution.channel || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add rule info
            if (autoModerationActionExecution.rule) {
                embed.addFields([
                    {
                        name: '> Quy tắc vi phạm',
                        value: `- ${autoModerationActionExecution.rule.name}`,
                        inline: true
                    },
                    {
                        name: '> Loại trigger',
                        value: `- ${triggerTypes[autoModerationActionExecution.rule.triggerType] || 'Unknown'}`,
                        inline: true
                    }
                ]);
            }
            
            // Add action info
            if (autoModerationActionExecution.action) {
                embed.addFields({
                    name: '> Hành động thực hiện',
                    value: `- ${actionTypes[autoModerationActionExecution.action.type] || 'Unknown'}`,
                    inline: true
                });
                
                // Add timeout duration if applicable
                if (autoModerationActionExecution.action.type === 3 && autoModerationActionExecution.action.metadata?.durationSeconds) {
                    const duration = autoModerationActionExecution.action.metadata.durationSeconds;
                    const minutes = Math.floor(duration / 60);
                    embed.addFields({
                        name: '> Thời gian timeout',
                        value: `- ${minutes} phút`,
                        inline: true
                    });
                }
                
                // Add alert channel if applicable
                if (autoModerationActionExecution.action.type === 2 && autoModerationActionExecution.action.metadata?.channelId) {
                    embed.addFields({
                        name: '> Kênh cảnh báo',
                        value: `- <#${autoModerationActionExecution.action.metadata.channelId}>`,
                        inline: true
                    });
                }
            }
            
            // Add content info if available
            if (autoModerationActionExecution.content) {
                const content = autoModerationActionExecution.content.length > 500 
                    ? autoModerationActionExecution.content.substring(0, 500) + '...'
                    : autoModerationActionExecution.content;
                
                embed.addFields({
                    name: '> Nội dung vi phạm',
                    value: `\`\`\`${content}\`\`\``,
                    inline: false
                });
            }
            
            // Add matched keyword if available
            if (autoModerationActionExecution.matchedKeyword) {
                embed.addFields({
                    name: '> Từ khóa vi phạm',
                    value: `- ||${autoModerationActionExecution.matchedKeyword}||`,
                    inline: true
                });
            }
            
            // Set user avatar as thumbnail
            if (autoModerationActionExecution.user?.displayAvatarURL()) {
                embed.setThumbnail(autoModerationActionExecution.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${autoModerationActionExecution.guild.name} • AutoMod Action`,
                iconURL: autoModerationActionExecution.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ AutoMod action logged: ${autoModerationActionExecution.user?.tag}`);
            
            // Smart alert for repeated violations
            if (config.smartAlerts) {
                try {
                    const owner = await autoModerationActionExecution.guild.fetchOwner();
                    const alertEmbed = createInfoEmbed(
                        '🚨 Smart Alert: AutoMod Action',
                        `AutoMod đã thực hiện hành động trong **${autoModerationActionExecution.guild.name}**`
                    );
                    
                    alertEmbed.setColor(0xe67e22);
                    alertEmbed.addFields([
                        { name: '🎯 Loại', value: 'AUTOMOD_ACTION', inline: true },
                        { name: '📊 Mức độ', value: 'MEDIUM', inline: true },
                        { name: '👤 Người vi phạm', value: autoModerationActionExecution.user?.tag || 'Unknown', inline: true },
                        { name: '🛡️ Quy tắc', value: autoModerationActionExecution.rule?.name || 'Unknown', inline: true },
                        { name: '⚡ Hành động', value: actionTypes[autoModerationActionExecution.action?.type] || 'Unknown', inline: true }
                    ]);
                    
                    await owner.send({ embeds: [alertEmbed] });
                } catch (dmError) {
                    console.log('Could not send AutoMod alert to owner');
                }
            }
            
        } catch (error) {
            console.error('Error in autoModeration audit log:', error);
        }
    }
};
