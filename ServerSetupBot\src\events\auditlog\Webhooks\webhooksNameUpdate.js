const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'webhookUpdate', // Custom event name
    async execute(oldWebhook, newWebhook, client) {
        try {
            // Only handle name changes
            if (oldWebhook.name === newWebhook.name) return;
            
            console.log(`✏️ Webhook name updated: ${oldWebhook.name} -> ${newWebhook.name} in ${newWebhook.guild?.name || 'Unknown'}`);
            
            // Skip if no guild (DM webhooks)
            if (!newWebhook.guild) return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newWebhook.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'WEBHOOKS_NAME_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'WEBHOOKS_NAME_UPDATE',
                user: 'System',
                userId: null,
                action: 'Tên webhook được cập nhật',
                details: `Tên webhook đã được thay đổi từ **${oldWebhook.name}** thành **${newWebhook.name}**`,
                target: newWebhook.name,
                channel: newWebhook.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the webhook name from audit logs
            try {
                const auditLogs = await newWebhook.guild.fetchAuditLogs({
                    type: 51, // WEBHOOK_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newWebhook.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Tên webhook được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for webhook name update');
            }
            
            // Add to database
            await client.db.addAuditLog(newWebhook.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Tên webhook được cập nhật',
                `Vừa có tên webhook được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get webhook type names
            const webhookTypes = {
                1: 'Incoming Webhook',
                2: 'Channel Follower Webhook',
                3: 'Application Webhook'
            };
            
            embed.addFields([
                {
                    name: '> ID webhook',
                    value: `- ${newWebhook.id}`,
                    inline: true
                },
                {
                    name: '> Loại webhook',
                    value: `- ${webhookTypes[newWebhook.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${newWebhook.channel || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Tên cũ',
                    value: `- ${oldWebhook.name || 'Unnamed Webhook'}`,
                    inline: true
                },
                {
                    name: '> Tên mới',
                    value: `- ${newWebhook.name || 'Unnamed Webhook'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add webhook owner info if available
            if (newWebhook.owner) {
                embed.addFields({
                    name: '> Chủ sở hữu',
                    value: `- ${newWebhook.owner.tag || newWebhook.owner.name}`,
                    inline: true
                });
            }
            
            // Add application info if it's an application webhook
            if (newWebhook.application) {
                embed.addFields({
                    name: '> Ứng dụng',
                    value: `- ${newWebhook.application.name}`,
                    inline: true
                });
            }
            
            // Add creation info if available
            if (newWebhook.createdTimestamp) {
                embed.addFields({
                    name: '> Được tạo lúc',
                    value: `- <t:${Math.floor(newWebhook.createdTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add impact information
            embed.addFields({
                name: '> 📝 Tác động',
                value: [
                    '• Tin nhắn mới từ webhook sẽ hiển thị tên mới',
                    '• Dễ nhận biết nguồn gốc tin nhắn',
                    '• Cập nhật thông tin hiển thị',
                    '• Có thể ảnh hưởng đến automation scripts'
                ].join('\n'),
                inline: false
            });
            
            // Add webhook URL info (partially hidden for security)
            if (newWebhook.url) {
                const hiddenUrl = `https://discord.com/api/webhooks/${newWebhook.id}/***`;
                embed.addFields({
                    name: '> URL webhook',
                    value: `- ${hiddenUrl}`,
                    inline: false
                });
            }
            
            // Add usage note
            embed.addFields({
                name: '> 💡 Lưu ý',
                value: [
                    '• Tên webhook hiển thị khi gửi tin nhắn',
                    '• Nên đặt tên mô tả rõ chức năng',
                    '• Tên có thể chứa emoji và ký tự đặc biệt',
                    '• Tối đa 80 ký tự'
                ].join('\n'),
                inline: false
            });
            
            // Add security note
            embed.addFields({
                name: '> 🔒 Bảo mật',
                value: `- Tên webhook không ảnh hưởng đến bảo mật nhưng nên rõ ràng để dễ quản lý`,
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set webhook avatar as thumbnail if available
            if (newWebhook.avatarURL()) {
                embed.setThumbnail(newWebhook.avatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newWebhook.guild.name} • Webhook Name Update`,
                iconURL: newWebhook.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Webhook name update logged: ${oldWebhook.name} -> ${newWebhook.name}`);
            
        } catch (error) {
            console.error('Error in webhooksNameUpdate audit log:', error);
        }
    }
};
