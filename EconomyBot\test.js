const { deployCommands } = require("./src/utils/deployCommands");

async function test() {
  try {
    console.log("🚀 Bắt đầu test...");

    // Test database connection
    const database = require("./src/database/database");
    await database.init();
    console.log("✅ Database connection thành công!");

    // Test user creation and level system
    const User = require("./src/models/User");
    const testUser = {
      id: "test123",
      username: "TestUser",
      discriminator: "0001",
      avatar: null,
    };

    const userData = await User.findOrCreate(testUser);
    console.log("✅ User creation thành công!");

    // Test level info
    const levelInfo = userData.getLevelInfo();
    console.log("📊 Level Info:", levelInfo);

    // Test adding experience
    await userData.addExperience(50);
    console.log("✅ Add experience thành công!");

    // Test deploy commands
    console.log("🚀 Bắt đầu deploy commands...");
    await deployCommands();
    console.log("✅ Deploy commands thành công!");

    console.log("🎉 Tất cả tests đã pass!");
    process.exit(0);
  } catch (error) {
    console.error("❌ Test failed:", error);
    console.error(error.stack);
    process.exit(1);
  }
}

test();
