const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle slow mode changes
            if (oldChannel.rateLimitPerUser === newChannel.rateLimitPerUser) return;
            
            console.log(`⏱️ Channel slow mode updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_SLOW_MODE_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_SLOW_MODE_UPDATE',
                user: 'System',
                userId: null,
                action: 'Slow mode kênh được thay đổi',
                details: `Slow mode của kênh **${newChannel.name}** đã được thay đổi từ ${oldChannel.rateLimitPerUser}s thành ${newChannel.rateLimitPerUser}s`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the slow mode from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Slow mode kênh được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for slow mode update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '⏱️ Slow mode kênh thay đổi',
                `Vừa có slow mode kênh được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Format time display
            const formatTime = (seconds) => {
                if (seconds === 0) return 'Tắt';
                if (seconds < 60) return `${seconds} giây`;
                if (seconds < 3600) return `${Math.floor(seconds / 60)} phút ${seconds % 60} giây`;
                return `${Math.floor(seconds / 3600)} giờ ${Math.floor((seconds % 3600) / 60)} phút`;
            };
            
            // Determine restriction level
            const getRestrictionLevel = (seconds) => {
                if (seconds === 0) return '🟢 Không hạn chế';
                if (seconds <= 5) return '🟡 Nhẹ (≤5s)';
                if (seconds <= 30) return '🟠 Vừa (≤30s)';
                if (seconds <= 120) return '🔴 Nặng (≤2m)';
                return '⚫ Rất nặng (>2m)';
            };
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Slow mode cũ',
                    value: `- ${formatTime(oldChannel.rateLimitPerUser)}`,
                    inline: true
                },
                {
                    name: '> Slow mode mới',
                    value: `- ${formatTime(newChannel.rateLimitPerUser)}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Mức độ hạn chế cũ',
                    value: `- ${getRestrictionLevel(oldChannel.rateLimitPerUser)}`,
                    inline: true
                },
                {
                    name: '> Mức độ hạn chế mới',
                    value: `- ${getRestrictionLevel(newChannel.rateLimitPerUser)}`,
                    inline: true
                }
            ]);
            
            // Add impact message
            if (newChannel.rateLimitPerUser > oldChannel.rateLimitPerUser) {
                embed.addFields({
                    name: '> Tác động',
                    value: `- Thành viên sẽ phải chờ lâu hơn giữa các tin nhắn`,
                    inline: false
                });
                if (newChannel.rateLimitPerUser >= 60) {
                    embed.setColor(0xe74c3c); // Red for heavy restriction
                }
            } else if (newChannel.rateLimitPerUser < oldChannel.rateLimitPerUser) {
                embed.addFields({
                    name: '> Tác động',
                    value: `- Thành viên có thể gửi tin nhắn nhanh hơn`,
                    inline: false
                });
                if (newChannel.rateLimitPerUser === 0) {
                    embed.setColor(0x2ecc71); // Green for no restriction
                }
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Slow Mode Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel slow mode update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelSlowModeUpdate audit log:', error);
        }
    }
};
