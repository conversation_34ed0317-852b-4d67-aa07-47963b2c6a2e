// Script to create all remaining audit log event files
const fs = require('fs');
const path = require('path');

console.log('🔧 Creating remaining audit log event files...');

// Base template for audit log files
const createFileTemplate = (eventName, eventType, description, color = '0xf39c12') => {
    return `const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.${eventName},
    async execute(...args) {
        try {
            const client = args[args.length - 1]; // Client is always last argument
            
            console.log(\`📝 \${eventType}: Event triggered\`);
            
            // Get guild from arguments (varies by event type)
            let guild;
            if (args[0]?.guild) guild = args[0].guild;
            else if (args[1]?.guild) guild = args[1].guild;
            else return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, '${eventType}')) return;
            
            // Create event data
            const eventData = {
                eventType: '${eventType}',
                user: 'System',
                userId: null,
                action: '${description}',
                details: \`\${description} đã xảy ra trong server\`,
                target: 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📝 ${description}',
                \`Vừa có \${description.toLowerCase()} trong server\`
            );
            
            embed.setColor(${color});
            
            embed.addFields([
                {
                    name: '> Loại sự kiện',
                    value: \`- \${eventType}\`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: \`- <t:\${Math.floor(Date.now() / 1000)}:F>\`,
                    inline: true
                }
            ]);
            
            embed.setTimestamp();
            embed.setFooter({
                text: \`\${guild.name} • \${eventType}\`,
                iconURL: guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(\`✅ \${eventType} logged\`);
            
        } catch (error) {
            console.error(\`Error in \${eventType} audit log:\`, error);
        }
    }
};`;
};

// Define all remaining files to create
const filesToCreate = [
    // DiscordAutoMod (6 more files)
    { category: 'DiscordAutoMod', file: 'automodRuleNameUpdate.js', event: 'AutoModerationRuleUpdate', type: 'AUTOMOD_RULE_NAME_UPDATE', desc: 'Tên quy tắc AutoMod được cập nhật' },
    { category: 'DiscordAutoMod', file: 'automodRuleActionsUpdate.js', event: 'AutoModerationRuleUpdate', type: 'AUTOMOD_RULE_ACTIONS_UPDATE', desc: 'Hành động quy tắc AutoMod được cập nhật' },
    { category: 'DiscordAutoMod', file: 'automodRuleContentUpdate.js', event: 'AutoModerationRuleUpdate', type: 'AUTOMOD_RULE_CONTENT_UPDATE', desc: 'Nội dung quy tắc AutoMod được cập nhật' },
    { category: 'DiscordAutoMod', file: 'automodRuleRolesUpdate.js', event: 'AutoModerationRuleUpdate', type: 'AUTOMOD_RULE_ROLES_UPDATE', desc: 'Roles miễn quy tắc AutoMod được cập nhật' },
    { category: 'DiscordAutoMod', file: 'automodRuleChannelsUpdate.js', event: 'AutoModerationRuleUpdate', type: 'AUTOMOD_RULE_CHANNELS_UPDATE', desc: 'Kênh miễn quy tắc AutoMod được cập nhật' },
    { category: 'DiscordAutoMod', file: 'automodRuleWhitelistUpdate.js', event: 'AutoModerationRuleUpdate', type: 'AUTOMOD_RULE_WHITELIST_UPDATE', desc: 'Whitelist quy tắc AutoMod được cập nhật' },
    
    // Events (10 more files)
    { category: 'Events', file: 'eventsLocationUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_LOCATION_UPDATE', desc: 'Địa điểm sự kiện được cập nhật' },
    { category: 'Events', file: 'eventsDescriptionUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_DESCRIPTION_UPDATE', desc: 'Mô tả sự kiện được cập nhật' },
    { category: 'Events', file: 'eventsNameUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_NAME_UPDATE', desc: 'Tên sự kiện được cập nhật' },
    { category: 'Events', file: 'eventsPrivacyLevelUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_PRIVACY_LEVEL_UPDATE', desc: 'Mức độ riêng tư sự kiện được cập nhật' },
    { category: 'Events', file: 'eventsStartTimeUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_START_TIME_UPDATE', desc: 'Thời gian bắt đầu sự kiện được cập nhật' },
    { category: 'Events', file: 'eventsEndTimeUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_END_TIME_UPDATE', desc: 'Thời gian kết thúc sự kiện được cập nhật' },
    { category: 'Events', file: 'eventsStatusUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_STATUS_UPDATE', desc: 'Trạng thái sự kiện được cập nhật' },
    { category: 'Events', file: 'eventsImageUpdate.js', event: 'GuildScheduledEventUpdate', type: 'EVENTS_IMAGE_UPDATE', desc: 'Hình ảnh sự kiện được cập nhật' },
    { category: 'Events', file: 'eventsUserSubscribe.js', event: 'GuildScheduledEventUserAdd', type: 'EVENTS_USER_SUBSCRIBE', desc: 'Người dùng đăng ký sự kiện' },
    { category: 'Events', file: 'eventsUserUnsubscribe.js', event: 'GuildScheduledEventUserRemove', type: 'EVENTS_USER_UNSUBSCRIBE', desc: 'Người dùng hủy đăng ký sự kiện' },
    
    // Polls (5 files)
    { category: 'Polls', file: 'pollsCreate.js', event: 'MessageCreate', type: 'POLLS_CREATE', desc: 'Poll được tạo' },
    { category: 'Polls', file: 'pollsDelete.js', event: 'MessageDelete', type: 'POLLS_DELETE', desc: 'Poll được xóa' },
    { category: 'Polls', file: 'pollsFinalize.js', event: 'MessageUpdate', type: 'POLLS_FINALIZE', desc: 'Poll được hoàn thành' },
    { category: 'Polls', file: 'pollsVotesAdd.js', event: 'MessageReactionAdd', type: 'POLLS_VOTES_ADD', desc: 'Vote được thêm vào poll' },
    { category: 'Polls', file: 'pollsVotesRemove.js', event: 'MessageReactionRemove', type: 'POLLS_VOTES_REMOVE', desc: 'Vote được xóa khỏi poll' },
    
    // Stage (4 files)
    { category: 'Stage', file: 'stageStart.js', event: 'StageInstanceCreate', type: 'STAGE_START', desc: 'Stage được bắt đầu' },
    { category: 'Stage', file: 'stageEnd.js', event: 'StageInstanceDelete', type: 'STAGE_END', desc: 'Stage được kết thúc' },
    { category: 'Stage', file: 'stageTopicUpdate.js', event: 'StageInstanceUpdate', type: 'STAGE_TOPIC_UPDATE', desc: 'Chủ đề stage được cập nhật' },
    { category: 'Stage', file: 'stagePrivacyUpdate.js', event: 'StageInstanceUpdate', type: 'STAGE_PRIVACY_UPDATE', desc: 'Quyền riêng tư stage được cập nhật' },
    
    // Stickers (5 files)
    { category: 'Stickers', file: 'stickersCreate.js', event: 'GuildStickerCreate', type: 'STICKERS_CREATE', desc: 'Sticker được tạo' },
    { category: 'Stickers', file: 'stickersDelete.js', event: 'GuildStickerDelete', type: 'STICKERS_DELETE', desc: 'Sticker được xóa' },
    { category: 'Stickers', file: 'stickersNameUpdate.js', event: 'GuildStickerUpdate', type: 'STICKERS_NAME_UPDATE', desc: 'Tên sticker được cập nhật' },
    { category: 'Stickers', file: 'stickersDescriptionUpdate.js', event: 'GuildStickerUpdate', type: 'STICKERS_DESCRIPTION_UPDATE', desc: 'Mô tả sticker được cập nhật' },
    { category: 'Stickers', file: 'stickersRelatedEmojiUpdate.js', event: 'GuildStickerUpdate', type: 'STICKERS_RELATED_EMOJI_UPDATE', desc: 'Emoji liên quan sticker được cập nhật' }
];

// Create directories and files
let createdCount = 0;
for (const fileInfo of filesToCreate) {
    const categoryDir = path.join(__dirname, 'src', 'events', 'auditlog', fileInfo.category);
    
    // Create category directory if it doesn't exist
    if (!fs.existsSync(categoryDir)) {
        fs.mkdirSync(categoryDir, { recursive: true });
        console.log(`📁 Created directory: ${fileInfo.category}`);
    }
    
    const filePath = path.join(categoryDir, fileInfo.file);
    
    // Only create if file doesn't exist
    if (!fs.existsSync(filePath)) {
        const content = createFileTemplate(fileInfo.event, fileInfo.type, fileInfo.desc);
        fs.writeFileSync(filePath, content);
        console.log(`✅ Created: ${fileInfo.category}/${fileInfo.file}`);
        createdCount++;
    } else {
        console.log(`⏭️ Skipped: ${fileInfo.category}/${fileInfo.file} (already exists)`);
    }
}

console.log(`🎉 Created ${createdCount} new audit log event files!`);
console.log('');
console.log('📋 Summary:');
console.log(`- DiscordAutoMod: 6 additional files`);
console.log(`- Events: 10 additional files`);
console.log(`- Polls: 5 files`);
console.log(`- Stage: 4 files`);
console.log(`- Stickers: 5 files`);
console.log('');
console.log('🚀 Next: Run this script with: node create-remaining-audit-files.js');
console.log('Then start your bot to test all the new events!');

// Clean up this script file after execution
setTimeout(() => {
    try {
        fs.unlinkSync(__filename);
        console.log('🗑️ Script file cleaned up');
    } catch (error) {
        console.log('⚠️ Could not clean up script file');
    }
}, 2000);
