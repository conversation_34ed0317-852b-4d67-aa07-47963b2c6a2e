const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createWarningEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog, parseDuration, formatDuration } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('mute')
        .setDescription('Mute thành viên (role mute)')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Thành viên cần mute')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('duration')
                .setDescription('Thời gian mute (ví dụ: 1h, 30m, 2d)')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Lý do mute')
                .setRequired(false)
        )
        .addBooleanOption(option =>
            option.setName('silent')
                .setDescription('Không gửi DM cho người bị mute')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.ManageRoles)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                'Bạn cần quyền `Manage Roles` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                'Bot cần quyền `Manage Roles` để thực hiện lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const user = interaction.options.getUser('user');
        const durationStr = interaction.options.getString('duration');
        const reason = interaction.options.getString('reason') || 'Không có lý do';
        const silent = interaction.options.getBoolean('silent') || false;
        const guildId = interaction.guild.id;
        
        try {
            const member = await interaction.guild.members.fetch(user.id);
            
            // Kiểm tra hierarchy
            if (member.roles.highest.position >= interaction.member.roles.highest.position) {
                const errorEmbed = createErrorEmbed(
                    'Không thể mute!',
                    'Bạn không thể mute thành viên có role cao hơn hoặc bằng bạn.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            if (member.roles.highest.position >= interaction.guild.members.me.roles.highest.position) {
                const errorEmbed = createErrorEmbed(
                    'Không thể mute!',
                    'Bot không thể mute thành viên có role cao hơn hoặc bằng bot.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Lấy hoặc tạo mute role
            let muteRole = interaction.guild.roles.cache.find(role => role.name === 'Muted');
            if (!muteRole) {
                muteRole = await interaction.guild.roles.create({
                    name: 'Muted',
                    color: 0x818386,
                    permissions: [],
                    reason: 'Tạo role mute tự động'
                });
                
                // Thiết lập permissions cho tất cả channels
                for (const channel of interaction.guild.channels.cache.values()) {
                    try {
                        await channel.permissionOverwrites.create(muteRole, {
                            SendMessages: false,
                            Speak: false,
                            AddReactions: false,
                            SendMessagesInThreads: false,
                            CreatePublicThreads: false,
                            CreatePrivateThreads: false
                        });
                    } catch (error) {
                        console.log(`Không thể thiết lập permission cho channel ${channel.name}`);
                    }
                }
            }
            
            // Kiểm tra nếu đã bị mute
            if (member.roles.cache.has(muteRole.id)) {
                const errorEmbed = createErrorEmbed(
                    'Thành viên đã bị mute!',
                    'Thành viên này đã có role mute.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Parse duration
            let durationMinutes = null;
            let expiresAt = null;
            if (durationStr) {
                durationMinutes = parseDuration(durationStr);
                if (durationMinutes <= 0) {
                    const errorEmbed = createErrorEmbed(
                        'Thời gian không hợp lệ!',
                        'Vui lòng sử dụng định dạng như: 1h, 30m, 2d'
                    );
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
                expiresAt = new Date(Date.now() + durationMinutes * 60000);
            }
            
            // Gửi DM trước khi mute
            if (!silent) {
                const dmEmbed = createWarningEmbed(
                    `Bạn đã bị mute trong ${interaction.guild.name}`,
                    `**Lý do:** ${reason}\n` +
                    `${durationMinutes ? `**Thời gian:** ${formatDuration(durationMinutes)}\n**Hết hạn:** <t:${Math.floor(expiresAt.getTime() / 1000)}:F>\n` : '**Thời gian:** Vĩnh viễn\n'}` +
                    `**Bởi:** ${interaction.user.tag}`
                );
                
                try {
                    await user.send({ embeds: [dmEmbed] });
                } catch (error) {
                    console.log('Không thể gửi DM cho user bị mute');
                }
            }
            
            // Thực hiện mute
            await member.roles.add(muteRole, `${reason} | Bởi: ${interaction.user.tag}`);
            
            // Lưu vào database
            await client.db.addMute(guildId, user.id, interaction.user.id, reason, expiresAt?.toISOString());
            
            // Log vào moderation logs
            await client.db.addModerationLog(guildId, {
                type: 'mute',
                userId: user.id,
                moderatorId: interaction.user.id,
                reason: reason,
                duration: durationMinutes,
                expiresAt: expiresAt?.toISOString()
            });
            
            const successEmbed = createSuccessEmbed(
                '🔇 Thành viên đã bị mute!',
                `**Thành viên:** ${user.tag} (${user.id})\n` +
                `**Lý do:** ${reason}\n` +
                `${durationMinutes ? `**Thời gian:** ${formatDuration(durationMinutes)}\n**Hết hạn:** <t:${Math.floor(expiresAt.getTime() / 1000)}:F>\n` : '**Thời gian:** Vĩnh viễn\n'}` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            await interaction.reply({ embeds: [successEmbed] });
            
            // Gửi log vào channel
            await sendModerationLog(client, guildId, {
                type: 'mute',
                user: user,
                moderator: interaction.user,
                reason: reason,
                duration: durationMinutes ? formatDuration(durationMinutes) : 'Vĩnh viễn'
            });
            
        } catch (error) {
            console.error('Lỗi khi mute user:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi mute thành viên!',
                'Đã xảy ra lỗi khi mute thành viên. Vui lòng thử lại sau!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
