const { SlashCommandBuilder } = require('discord.js');
const { createInfoEmbed, createErrorEmbed } = require('../../utils/embedBuilder.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('emojis')
        .setDescription('Tạo danh sách emoji với nhiều tùy chọn tùy chỉnh')
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('Tạo danh sách emoji với các tùy chọn nâng cao')
                .addIntegerOption(option =>
                    option.setName('page')
                        .setDescription('Trang cần xem (mặc định: 1)')
                        .setRequired(false)
                        .setMinValue(1)
                )
                .addBooleanOption(option =>
                    option.setName('auto_update_list')
                        .setDescription('Tự động cập nhật danh sách khi có emoji mới')
                        .setRequired(false)
                )
                .addStringOption(option =>
                    option.setName('ignore_emojis')
                        .setDescription('Bỏ qua emoji cụ thể (cách nhau bằng dấu phẩy)')
                        .setRequired(false)
                )
                .addStringOption(option =>
                    option.setName('sort_after')
                        .setDescription('Sắp xếp emoji theo thứ tự')
                        .setRequired(false)
                        .addChoices(
                            { name: 'Tên (A-Z)', value: 'name_asc' },
                            { name: 'Tên (Z-A)', value: 'name_desc' },
                            { name: 'Ngày tạo (Cũ nhất)', value: 'date_asc' },
                            { name: 'Ngày tạo (Mới nhất)', value: 'date_desc' },
                            { name: 'Animated trước', value: 'animated_first' },
                            { name: 'Static trước', value: 'static_first' }
                        )
                )
                .addBooleanOption(option =>
                    option.setName('exclude_non_or_animated_emojis')
                        .setDescription('Loại trừ emoji animated hoặc static')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('exclude_locked_emojis')
                        .setDescription('Loại trừ emoji bị khóa cho role')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('show_full_emoji_id')
                        .setDescription('Hiển thị ID đầy đủ của emoji')
                        .setRequired(false)
                )
                .addStringOption(option =>
                    option.setName('message_before_emojis')
                        .setDescription('Thêm tin nhắn tùy chỉnh trước danh sách emoji')
                        .setRequired(false)
                )
                .addStringOption(option =>
                    option.setName('webhook_name')
                        .setDescription('Tên webhook để gửi tin nhắn')
                        .setRequired(false)
                )
                .addStringOption(option =>
                    option.setName('webhook_avatar')
                        .setDescription('Avatar URL cho webhook')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('webhook')
                        .setDescription('Gửi qua webhook thay vì bot')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('divider')
                        .setDescription('Thêm đường phân cách giữa các emoji')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('update_timestamp')
                        .setDescription('Hiển thị thời gian cập nhật')
                        .setRequired(false)
                )
                .addStringOption(option =>
                    option.setName('update_timestamp_type')
                        .setDescription('Loại timestamp hiển thị')
                        .setRequired(false)
                        .addChoices(
                            { name: 'Thời gian tương đối', value: 'relative' },
                            { name: 'Thời gian đầy đủ', value: 'full' },
                            { name: 'Chỉ ngày', value: 'date_only' },
                            { name: 'Chỉ giờ', value: 'time_only' }
                        )
                )
        ),
    category: 'content',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();
        
        switch (subcommand) {
            case 'list':
                await handleEmojisList(interaction, client);
                break;
        }
    },
};

async function handleEmojisList(interaction, client) {
    const page = interaction.options.getInteger('page') || 1;
    const autoUpdate = interaction.options.getBoolean('auto_update_list') || false;
    const ignoreEmojis = interaction.options.getString('ignore_emojis') || '';
    const sortAfter = interaction.options.getString('sort_after') || 'name_asc';
    const excludeAnimated = interaction.options.getBoolean('exclude_non_or_animated_emojis') || false;
    const excludeLocked = interaction.options.getBoolean('exclude_locked_emojis') || false;
    const showFullId = interaction.options.getBoolean('show_full_emoji_id') || false;
    const messageBefore = interaction.options.getString('message_before_emojis') || '';
    const webhookName = interaction.options.getString('webhook_name') || '';
    const webhookAvatar = interaction.options.getString('webhook_avatar') || '';
    const useWebhook = interaction.options.getBoolean('webhook') || false;
    const useDivider = interaction.options.getBoolean('divider') || false;
    const showTimestamp = interaction.options.getBoolean('update_timestamp') || false;
    const timestampType = interaction.options.getString('update_timestamp_type') || 'relative';
    
    let emojis = interaction.guild.emojis.cache;
    
    if (emojis.size === 0) {
        const infoEmbed = createInfoEmbed(
            'Chưa có emoji tùy chỉnh',
            'Server chưa có emoji tùy chỉnh nào.'
        );
        return await interaction.reply({ embeds: [infoEmbed] });
    }
    
    // Filter ignored emojis
    if (ignoreEmojis) {
        const ignoredList = ignoreEmojis.split(',').map(name => name.trim());
        emojis = emojis.filter(emoji => !ignoredList.includes(emoji.name));
    }
    
    // Filter animated/static
    if (excludeAnimated) {
        emojis = emojis.filter(emoji => !emoji.animated);
    }
    
    // Filter locked emojis
    if (excludeLocked) {
        emojis = emojis.filter(emoji => emoji.roles.cache.size === 0);
    }
    
    // Sort emojis
    const sortedEmojis = Array.from(emojis.values()).sort((a, b) => {
        switch (sortAfter) {
            case 'name_asc':
                return a.name.localeCompare(b.name);
            case 'name_desc':
                return b.name.localeCompare(a.name);
            case 'date_asc':
                return a.createdTimestamp - b.createdTimestamp;
            case 'date_desc':
                return b.createdTimestamp - a.createdTimestamp;
            case 'animated_first':
                return b.animated - a.animated;
            case 'static_first':
                return a.animated - b.animated;
            default:
                return a.name.localeCompare(b.name);
        }
    });
    
    const itemsPerPage = 20;
    const totalPages = Math.ceil(sortedEmojis.length / itemsPerPage);
    
    if (page > totalPages) {
        const errorEmbed = createErrorEmbed(
            'Trang không tồn tại!',
            `Chỉ có ${totalPages} trang. Vui lòng chọn trang từ 1 đến ${totalPages}.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageEmojis = sortedEmojis.slice(startIndex, endIndex);
    
    // Build emoji list
    let emojiList = '';
    if (messageBefore) {
        emojiList += `${messageBefore}\n\n`;
    }
    
    for (let i = 0; i < pageEmojis.length; i++) {
        const emoji = pageEmojis[i];
        const emojiDisplay = showFullId ? `${emoji} \`<:${emoji.name}:${emoji.id}>\`` : `${emoji} \`${emoji.name}\``;
        
        emojiList += emojiDisplay;
        
        if (useDivider && i < pageEmojis.length - 1) {
            emojiList += '\n━━━━━━━━━━━━━━━━━━━━\n';
        } else {
            emojiList += '\n';
        }
    }
    
    // Create embed
    const embed = createInfoEmbed(
        `📝 Danh sách Emoji (Trang ${page}/${totalPages})`,
        emojiList.slice(0, 4000)
    );
    
    // Add timestamp
    if (showTimestamp) {
        const now = new Date();
        let timestampText = '';
        
        switch (timestampType) {
            case 'relative':
                timestampText = `<t:${Math.floor(now.getTime() / 1000)}:R>`;
                break;
            case 'full':
                timestampText = `<t:${Math.floor(now.getTime() / 1000)}:F>`;
                break;
            case 'date_only':
                timestampText = `<t:${Math.floor(now.getTime() / 1000)}:D>`;
                break;
            case 'time_only':
                timestampText = `<t:${Math.floor(now.getTime() / 1000)}:T>`;
                break;
        }
        
        embed.addFields({
            name: '🕒 Cập nhật lần cuối',
            value: timestampText,
            inline: false
        });
    }
    
    // Add statistics
    const staticCount = sortedEmojis.filter(e => !e.animated).length;
    const animatedCount = sortedEmojis.filter(e => e.animated).length;
    const lockedCount = sortedEmojis.filter(e => e.roles.cache.size > 0).length;
    
    embed.addFields({
        name: '📊 Thống kê',
        value: `**Tổng:** ${sortedEmojis.length}\n**Static:** ${staticCount}\n**Animated:** ${animatedCount}\n**Bị khóa:** ${lockedCount}`,
        inline: true
    });
    
    embed.setFooter({
        text: `Trang ${page}/${totalPages} • Sắp xếp: ${getSortDisplayName(sortAfter)}${autoUpdate ? ' • Auto-update: ON' : ''}`,
        iconURL: interaction.guild.iconURL({ dynamic: true })
    });
    
    // Send via webhook or normal reply
    if (useWebhook) {
        try {
            // Create webhook if specified
            const webhook = await interaction.channel.createWebhook({
                name: webhookName || 'Emoji List Bot',
                avatar: webhookAvatar || client.user.displayAvatarURL()
            });
            
            await webhook.send({ embeds: [embed] });
            await webhook.delete();
            
            await interaction.reply({ 
                content: '✅ Danh sách emoji đã được gửi qua webhook!', 
                ephemeral: true 
            });
        } catch (error) {
            console.error('Lỗi webhook:', error);
            await interaction.reply({ embeds: [embed] });
        }
    } else {
        await interaction.reply({ embeds: [embed] });
    }
}

function getSortDisplayName(sortType) {
    const sortNames = {
        'name_asc': 'Tên A-Z',
        'name_desc': 'Tên Z-A',
        'date_asc': 'Cũ nhất',
        'date_desc': 'Mới nhất',
        'animated_first': 'Animated trước',
        'static_first': 'Static trước'
    };
    return sortNames[sortType] || 'Tên A-Z';
}
