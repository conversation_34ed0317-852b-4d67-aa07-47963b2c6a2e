const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const os = require('os');
const moment = require('moment');

module.exports = {
    name: "stats",
    aliases: ["botinfo", "information", "st", "bi"],
    description: "Show bot statistics",
    execute: async (message, args, client) => {
        if (!client.user) {
            return message.reply("Bot data is not available. Try again in a few seconds.");
        }

        const uptime = moment.duration(client.uptime).humanize();
        const createdOn = moment(client.user.createdAt).format('MMMM Do YYYY, h:mm:ss a');

        const row = new ActionRowBuilder().addComponents(
            new ButtonBuilder().setCustomId('general').setLabel('General').setStyle(ButtonStyle.Primary),
            new ButtonBuilder().setCustomId('system').setLabel('System').setStyle(ButtonStyle.Danger),
            new ButtonBuilder().setCustomId('team').setLabel('Team').setStyle(ButtonStyle.Success),
        );

        const linkRow = new ActionRowBuilder().addComponents(
            new ButtonBuilder().setLabel('Invite Bot').setStyle(ButtonStyle.Link).setURL('https://discord.com/oauth2/authorize?client_id=1294987004483862528&permissions=8&response_type=code&redirect_uri=https%3A%2F%2Fdiscord.gg%2FS6AjkyQKNZ&integration_type=0&scope=bot+applications.commands+guilds'),
            new ButtonBuilder().setLabel('Support Server').setStyle(ButtonStyle.Link).setURL('https://discord.gg/S6AjkyQKNZ')
        );

        const generalEmbed = new EmbedBuilder()
            .setAuthor({ name: `${client.user.tag}`, iconURL: client.user.displayAvatarURL() })
            .setTitle("General Bot Information")
            .setColor("#FFA500")
            .setThumbnail(client.user.displayAvatarURL())
            .setDescription(`**Bot Tag:** ${client.user.tag}\n` +
                `**Bot Version:** v1.0.0\n` +
                `**Created On:** ${createdOn}\n` +
                `**Discord.js:** v14\n` +
                `**Servers:** ${client.guilds.cache.size} servers\n` +
                `**Users:** ${client.users.cache.size} users\n` +
                `**Channels:** ${client.channels.cache.size}\n` +
                `**Uptime:** ${uptime}`)
            .setFooter({ text: `Requested by ${message.author.username}`, iconURL: message.author.displayAvatarURL() });

        const teamEmbed = new EmbedBuilder()
            .setAuthor({ name: `${client.user.tag}`, iconURL: client.user.displayAvatarURL() })
            .setColor("#FFA500")
            .setThumbnail(client.user.displayAvatarURL())
            .addFields(
                { name: "__Devlopers__", value: "[arnab.irl](https://discord.com/users/1105408192537698334), [axizz4sure](https://discord.com/users/1064033717561081856)", inline: true },
                { name: "__Core Team__", value: "[01priyanshu](https://discord.com/users/1074333930553086063)", inline: true },
                { name: "__Additional Support__", value: "[oreo_yk](https://discord.com/users/1099232674977153084)", inline: false }
            )
            .setFooter({ text: `Requested by ${message.author.username}`, iconURL: message.author.displayAvatarURL() });

        const msg = await message.reply({ embeds: [generalEmbed], components: [row, linkRow] });

        const filter = (interaction) => interaction.isButton() && interaction.user.id === message.author.id;
        const collector = msg.createMessageComponentCollector({ filter, time: 180000 });

        collector.on('collect', async (interaction) => {
            await interaction.deferUpdate();

            if (interaction.customId === 'general') {
                await interaction.editReply({ embeds: [generalEmbed], components: [row, linkRow] });
            }

            if (interaction.customId === 'team') {
                await interaction.editReply({ embeds: [teamEmbed], components: [row, linkRow] });
            }

            if (interaction.customId === 'system') {
                const loadingEmbed = new EmbedBuilder()
                    .setColor("#FAA61A")
                    .setDescription("<:emoji_33:1361362182495015042> | **Fetching** all the **resources**...");

                await interaction.editReply({ embeds: [loadingEmbed], components: [row, linkRow] });

                setTimeout(async () => {
                    const totalMem = os.totalmem() / 1024 / 1024 / 1024;
                    const usedMem = (os.totalmem() - os.freemem()) / 1024 / 1024 / 1024;
                    const cpu = os.cpus()[0];
                    const cpuTimes = cpu.times;

                    const systemEmbed = new EmbedBuilder()
                        .setAuthor({ name: `${client.user.tag}`, iconURL: client.user.displayAvatarURL() })
                        .setColor("#FAA61A")
                        .setThumbnail(client.user.displayAvatarURL())
                        .setDescription(`**__System Informations__**\n` +
                            `System Latency: ${client.ws.ping}ms\n` +
                            `Platform: ${os.platform()}\n` +
                            `Architecture: ${os.arch()}\n` +
                            `Memory Usage: ${usedMem.toFixed(2)} GB / ${totalMem.toFixed(2)} GB\n` +
                            `Processor 1:\n` +
                            ` Model: ${cpu.model}\n` +
                            ` Speed: ${cpu.speed} MHz\n` +
                            `Times:\n` +
                            ` User: ${cpuTimes.user} ms\n` +
                            ` Sys: ${cpuTimes.sys} ms\n` +
                            ` Idle: ${cpuTimes.idle} ms\n` +
                            ` IRQ: ${cpuTimes.irq} ms\n` +
                            `Database Latency: ${(Math.random() * 30 + 5).toFixed(2)}ms`)
                        .setFooter({ text: `Requested by ${message.author.username}`, iconURL: message.author.displayAvatarURL() });

                    await msg.edit({ embeds: [systemEmbed], components: [row, linkRow] });
                }, 500);
            }
        });

        collector.on('end', () => {
            msg.edit({ components: [] }).catch(() => {});
        });
    }
};