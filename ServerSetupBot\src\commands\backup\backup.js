const { SlashCommandBuilder, PermissionFlagsBits } = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
  createLoadingEmbed,
} = require("../../utils/embedBuilder.js");
const { isValidBackupName } = require("../../utils/validators.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("backup")
    .setDescription("Hệ thống quản lý backup server toàn diện")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("info")
        .setDescription("Xem thông tin chi tiết về backup")
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên backup cần xem thông tin")
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("create")
        .setDescription("Tạo backup mới cho server")
        .addStringOption((option) =>
          option.setName("name").setDescription("Tên backup").setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("type")
            .setDescription("Loại backup")
            .setRequired(false)
            .addChoices(
              { name: "🏗️ Full - Toàn bộ server", value: "full" },
              { name: "📝 Channels - Chỉ channels", value: "channels" },
              { name: "🎭 Roles - Chỉ roles", value: "roles" },
              { name: "⚙️ Settings - Chỉ cấu hình bot", value: "settings" },
              { name: "🎨 Content - Emoji và stickers", value: "content" },
              { name: "🔧 Permissions - Quyền hạn", value: "permissions" }
            )
        )
        .addStringOption((option) =>
          option
            .setName("description")
            .setDescription("Mô tả backup (tùy chọn)")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("include_messages")
            .setDescription("Bao gồm tin nhắn gần đây (100 tin nhắn/kênh)")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("compress")
            .setDescription("Nén backup để tiết kiệm dung lượng")
            .setRequired(false)
        )
    )
    .addSubcommandGroup((group) =>
      group
        .setName("interval")
        .setDescription("Quản lý backup tự động theo lịch")
        .addSubcommand((subcommand) =>
          subcommand
            .setName("show")
            .setDescription("Hiển thị lịch backup hiện tại")
        )
        .addSubcommand((subcommand) =>
          subcommand
            .setName("on")
            .setDescription("Bật backup tự động")
            .addStringOption((option) =>
              option
                .setName("interval")
                .setDescription("Khoảng thời gian backup")
                .setRequired(true)
                .addChoices(
                  { name: "Hàng ngày", value: "daily" },
                  { name: "Hàng tuần", value: "weekly" },
                  { name: "Hàng tháng", value: "monthly" },
                  { name: "Tùy chỉnh (giờ)", value: "custom" }
                )
            )
            .addIntegerOption((option) =>
              option
                .setName("hours")
                .setDescription("Số giờ cho backup tùy chỉnh (1-168)")
                .setRequired(false)
                .setMinValue(1)
                .setMaxValue(168)
            )
            .addIntegerOption((option) =>
              option
                .setName("max_backups")
                .setDescription("Số backup tối đa giữ lại (1-50)")
                .setRequired(false)
                .setMinValue(1)
                .setMaxValue(50)
            )
        )
        .addSubcommand((subcommand) =>
          subcommand.setName("off").setDescription("Tắt backup tự động")
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("status")
        .setDescription("Xem trạng thái backup hiện tại")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("cancel")
        .setDescription("Hủy quá trình backup đang chạy")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("delete")
        .setDescription("Xóa backup (KHÔNG THỂ HOÀN TÁC)")
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên backup cần xóa")
            .setRequired(true)
        )
        .addBooleanOption((option) =>
          option
            .setName("confirm")
            .setDescription("Xác nhận xóa backup (bắt buộc)")
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("list")
        .setDescription("Xem danh sách tất cả backup")
        .addStringOption((option) =>
          option
            .setName("filter")
            .setDescription("Lọc backup theo loại")
            .setRequired(false)
            .addChoices(
              { name: "Tất cả", value: "all" },
              { name: "Full backup", value: "full" },
              { name: "Channels only", value: "channels" },
              { name: "Roles only", value: "roles" },
              { name: "Settings only", value: "settings" }
            )
        )
        .addStringOption((option) =>
          option
            .setName("sort")
            .setDescription("Sắp xếp backup")
            .setRequired(false)
            .addChoices(
              { name: "Mới nhất", value: "newest" },
              { name: "Cũ nhất", value: "oldest" },
              { name: "Tên A-Z", value: "name_asc" },
              { name: "Tên Z-A", value: "name_desc" },
              { name: "Kích thước lớn nhất", value: "size_desc" },
              { name: "Kích thước nhỏ nhất", value: "size_asc" }
            )
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("load")
        .setDescription("Khôi phục backup vào server")
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên backup cần khôi phục")
            .setRequired(true)
        )
        .addBooleanOption((option) =>
          option
            .setName("overwrite")
            .setDescription("Ghi đè lên cấu hình hiện tại")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("dry_run")
            .setDescription("Chỉ kiểm tra không thực hiện khôi phục")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("purge")
        .setDescription("Xóa nhiều backup cùng lúc (KHÔNG THỂ HOÀN TÁC)")
        .addStringOption((option) =>
          option
            .setName("criteria")
            .setDescription("Tiêu chí xóa backup")
            .setRequired(true)
            .addChoices(
              { name: "Tất cả backup", value: "all" },
              { name: "Backup cũ hơn 30 ngày", value: "older_30" },
              { name: "Backup cũ hơn 7 ngày", value: "older_7" },
              { name: "Chỉ giữ 5 backup mới nhất", value: "keep_5" },
              { name: "Chỉ giữ 10 backup mới nhất", value: "keep_10" }
            )
        )
        .addBooleanOption((option) =>
          option
            .setName("confirm")
            .setDescription("Xác nhận xóa backup (bắt buộc)")
            .setRequired(true)
        )
    ),
  category: "backup",
  adminOnly: true,
  manageServer: false,

  async execute(interaction, client) {
    // Kiểm tra quyền bot
    if (
      !interaction.guild.members.me.permissions.has([
        PermissionFlagsBits.ManageChannels,
        PermissionFlagsBits.ManageRoles,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.ReadMessageHistory,
      ])
    ) {
      const errorEmbed = createErrorEmbed(
        "Bot thiếu quyền!",
        "Bot cần các quyền sau để tạo backup:\n• Manage Channels\n• Manage Roles\n• View Channel\n• Read Message History"
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    const subcommandGroup = interaction.options.getSubcommandGroup();
    const subcommand = interaction.options.getSubcommand();

    // Handle interval subcommand group
    if (subcommandGroup === "interval") {
      switch (subcommand) {
        case "show":
          await handleIntervalShow(interaction, client);
          break;
        case "on":
          await handleIntervalOn(interaction, client);
          break;
        case "off":
          await handleIntervalOff(interaction, client);
          break;
      }
      return;
    }

    // Handle regular subcommands
    switch (subcommand) {
      case "info":
        await handleBackupInfo(interaction, client);
        break;
      case "create":
        await handleBackupCreate(interaction, client);
        break;
      case "status":
        await handleBackupStatus(interaction, client);
        break;
      case "cancel":
        await handleBackupCancel(interaction, client);
        break;
      case "delete":
        await handleBackupDelete(interaction, client);
        break;
      case "list":
        await handleBackupList(interaction, client);
        break;
      case "load":
        await handleBackupLoad(interaction, client);
        break;
      case "purge":
        await handleBackupPurge(interaction, client);
        break;
    }
  },
};

async function handleBackupInfo(interaction, client) {
  const name = interaction.options.getString("name");
  const guildId = interaction.guild.id;

  try {
    const backups = await client.db.getServerBackups(guildId);
    const backup = backups.find((b) => b.backup_name === name);

    if (!backup) {
      const errorEmbed = createErrorEmbed(
        "Backup không tồn tại!",
        `Không tìm thấy backup với tên "${name}".`
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    const backupData = JSON.parse(backup.backup_data);
    const createdDate = new Date(backup.created_at);
    const fileSize = formatFileSize(backup.file_size || 0);

    const embed = createInfoEmbed(
      `📦 Thông tin Backup: ${backup.backup_name}`,
      `Chi tiết về backup **${backup.backup_name}**:`
    );

    embed.addFields(
      { name: "📝 Tên", value: backup.backup_name, inline: true },
      {
        name: "🏷️ Loại",
        value: getTypeDescription(backup.backup_type),
        inline: true,
      },
      { name: "📊 Kích thước", value: fileSize, inline: true },
      { name: "👤 Tạo bởi", value: `<@${backup.created_by}>`, inline: true },
      {
        name: "📅 Ngày tạo",
        value: `<t:${Math.floor(createdDate.getTime() / 1000)}:F>`,
        inline: true,
      },
      {
        name: "⏰ Thời gian tương đối",
        value: `<t:${Math.floor(createdDate.getTime() / 1000)}:R>`,
        inline: true,
      }
    );

    if (backupData.description) {
      embed.addFields({
        name: "📄 Mô tả",
        value: backupData.description,
        inline: false,
      });
    }

    // Thống kê chi tiết
    const stats = getDetailedStats(backupData, backup.backup_type);
    if (stats) {
      embed.addFields({
        name: "📊 Thống kê chi tiết",
        value: stats,
        inline: false,
      });
    }

    // Thông tin server gốc
    if (backupData.guildName) {
      embed.addFields({
        name: "🏠 Server gốc",
        value: `**Tên:** ${backupData.guildName}\n**ID:** ${
          backupData.guildId
        }\n**Thành viên:** ${backupData.memberCount || "N/A"}`,
        inline: false,
      });
    }

    embed.addFields({
      name: "🔄 Khôi phục",
      value: `\`/backup load name:${backup.backup_name}\``,
      inline: false,
    });

    if (backupData.guildIcon) {
      embed.setThumbnail(backupData.guildIcon);
    }

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error("Lỗi khi xem thông tin backup:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể lấy thông tin backup. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleBackupCreate(interaction, client) {
  const name = interaction.options.getString("name");
  const type = interaction.options.getString("type") || "full";
  const description = interaction.options.getString("description") || "";
  const includeMessages =
    interaction.options.getBoolean("include_messages") || false;
  const compress = interaction.options.getBoolean("compress") || false;
  const guildId = interaction.guild.id;

  // Validate backup name
  if (!isValidBackupName(name)) {
    const errorEmbed = createErrorEmbed(
      "Tên backup không hợp lệ!",
      "Tên backup chỉ được chứa:\n• Chữ cái (a-z, A-Z)\n• Số (0-9)\n• Khoảng trắng\n• Dấu gạch ngang (-)\n• Dấu gạch dưới (_)\n• Dấu ngoặc đơn ()\n• Độ dài: 1-50 ký tự"
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Check if backup name already exists
  const existingBackups = await client.db.getServerBackups(guildId);
  const nameExists = existingBackups.some(
    (backup) => backup.backup_name === name
  );

  if (nameExists) {
    const errorEmbed = createErrorEmbed(
      "Tên backup đã tồn tại!",
      `Backup với tên "${name}" đã tồn tại.\n\nVui lòng chọn tên khác hoặc xóa backup cũ trước.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Show loading message
  const loadingEmbed = createLoadingEmbed(
    "Đang tạo backup...",
    `Đang sao lưu ${getTypeDescription(type)} của server.\n${
      includeMessages ? "📝 Bao gồm tin nhắn gần đây\n" : ""
    }${
      compress ? "🗜️ Đang nén dữ liệu\n" : ""
    }Quá trình này có thể mất vài phút...`
  );
  await interaction.reply({ embeds: [loadingEmbed] });

  try {
    // Set backup status
    await client.db.setBackupStatus(guildId, "creating", {
      name,
      type,
      progress: 0,
    });

    // Create backup data
    const backupData = await createAdvancedBackupData(
      interaction.guild,
      type,
      client,
      {
        includeMessages,
        compress,
        progressCallback: async (progress) => {
          await client.db.setBackupStatus(guildId, "creating", {
            name,
            type,
            progress,
          });
        },
      }
    );

    // Calculate file size
    const dataString = JSON.stringify(backupData);
    const fileSize = Buffer.byteLength(dataString, "utf8");

    // Save backup to database
    const result = await client.db.createServerBackup(guildId, {
      name: name,
      data: {
        ...backupData,
        description: description,
        createdAt: new Date().toISOString(),
        guildName: interaction.guild.name,
        guildIcon: interaction.guild.iconURL({ dynamic: true }),
        options: { includeMessages, compress },
      },
      type: type,
      createdBy: interaction.user.id,
      fileSize: fileSize,
    });

    // Clear backup status
    await client.db.clearBackupStatus(guildId);

    const successEmbed = createSuccessEmbed(
      "Backup đã được tạo thành công!",
      `**Tên:** ${name}\n` +
        `**Loại:** ${getTypeDescription(type)}\n` +
        `**Kích thước:** ${formatFileSize(fileSize)}\n` +
        `**ID Backup:** ${result.id}\n` +
        `**Mô tả:** ${description || "Không có"}`
    );

    successEmbed.addFields({
      name: "📊 Thống kê backup",
      value: getBackupStats(backupData, type),
      inline: false,
    });

    if (includeMessages || compress) {
      let options = [];
      if (includeMessages) options.push("📝 Bao gồm tin nhắn");
      if (compress) options.push("🗜️ Đã nén");

      successEmbed.addFields({
        name: "⚙️ Tùy chọn",
        value: options.join("\n"),
        inline: true,
      });
    }

    successEmbed.addFields({
      name: "🔄 Khôi phục",
      value: `Sử dụng \`/backup load name:${name}\` để khôi phục backup này.`,
      inline: false,
    });

    await interaction.editReply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi tạo backup:", error);
    await client.db.clearBackupStatus(guildId);

    const errorEmbed = createErrorEmbed(
      "Lỗi tạo backup!",
      "Đã xảy ra lỗi khi tạo backup. Vui lòng thử lại sau!"
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleBackupStatus(interaction, client) {
  const guildId = interaction.guild.id;

  try {
    const status = await client.db.getBackupStatus(guildId);

    if (!status) {
      const infoEmbed = createInfoEmbed(
        "Không có tiến trình backup nào",
        "Hiện tại không có tiến trình backup nào đang chạy."
      );
      return await interaction.reply({ embeds: [infoEmbed] });
    }

    const statusData = JSON.parse(status.status_data);
    const startTime = new Date(status.started_at);
    const elapsed = Math.floor((Date.now() - startTime.getTime()) / 1000);

    const embed = createInfoEmbed(
      `⏳ Trạng thái Backup`,
      `Tiến trình backup đang chạy:`
    );

    embed.addFields(
      { name: "📝 Tên backup", value: statusData.name || "N/A", inline: true },
      {
        name: "🏷️ Loại",
        value: getTypeDescription(statusData.type),
        inline: true,
      },
      {
        name: "📊 Tiến độ",
        value: `${statusData.progress || 0}%`,
        inline: true,
      },
      { name: "⏰ Thời gian đã trôi", value: `${elapsed} giây`, inline: true },
      {
        name: "🔄 Trạng thái",
        value: getStatusDescription(status.status),
        inline: true,
      }
    );

    if (statusData.currentStep) {
      embed.addFields({
        name: "🎯 Bước hiện tại",
        value: statusData.currentStep,
        inline: false,
      });
    }

    embed.addFields({
      name: "🛑 Hủy backup",
      value: "Sử dụng `/backup cancel` để hủy tiến trình.",
      inline: false,
    });

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error("Lỗi khi xem trạng thái backup:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể lấy trạng thái backup. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

// Utility functions
function getTypeDescription(type) {
  const descriptions = {
    full: "🏗️ Toàn bộ server",
    channels: "📝 Channels và categories",
    roles: "🎭 Roles và permissions",
    settings: "⚙️ Cấu hình bot",
    content: "🎨 Emoji và stickers",
    permissions: "🔧 Quyền hạn",
  };
  return descriptions[type] || type;
}

function formatFileSize(bytes) {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

function getDetailedStats(backupData) {
  let stats = [];

  if (backupData.channels) {
    const channels = backupData.channels;
    stats.push(
      `📝 **Channels:** ${
        (channels.textChannels?.length || 0) +
        (channels.voiceChannels?.length || 0)
      }`
    );
    stats.push(`📁 **Categories:** ${channels.categories?.length || 0}`);
  }

  if (backupData.roles) {
    stats.push(`🎭 **Roles:** ${backupData.roles.length || 0}`);
  }

  if (backupData.settings) {
    let settingsCount = 0;
    if (backupData.settings.welcome) settingsCount++;
    if (backupData.settings.autoroles) settingsCount++;
    if (backupData.settings.moderation) settingsCount++;
    stats.push(`⚙️ **Cấu hình:** ${settingsCount} module`);
  }

  if (backupData.content) {
    stats.push(`🎨 **Emoji:** ${backupData.content.emojis?.length || 0}`);
    stats.push(`🏷️ **Stickers:** ${backupData.content.stickers?.length || 0}`);
  }

  if (backupData.messages) {
    const totalMessages = Object.values(backupData.messages).reduce(
      (sum, msgs) => sum + (msgs?.length || 0),
      0
    );
    stats.push(`💬 **Tin nhắn:** ${totalMessages}`);
  }

  return stats.join("\n") || "Không có dữ liệu";
}

function getBackupStats(backupData, type) {
  let stats = [];

  if (type === "full" || type === "channels") {
    const channels = backupData.channels;
    if (channels) {
      stats.push(`📝 ${channels.textChannels?.length || 0} text channels`);
      stats.push(`🔊 ${channels.voiceChannels?.length || 0} voice channels`);
      stats.push(`📁 ${channels.categories?.length || 0} categories`);
    }
  }

  if (type === "full" || type === "roles") {
    if (backupData.roles) {
      stats.push(`🎭 ${backupData.roles.length} roles`);
    }
  }

  if (type === "full" || type === "settings") {
    if (backupData.settings) {
      let configCount = Object.keys(backupData.settings).length;
      stats.push(`⚙️ ${configCount} cấu hình`);
    }
  }

  return stats.join("\n") || "Không có dữ liệu";
}

function getStatusDescription(status) {
  const descriptions = {
    creating: "🔄 Đang tạo backup",
    loading: "📥 Đang khôi phục",
    deleting: "🗑️ Đang xóa",
    completed: "✅ Hoàn thành",
    failed: "❌ Thất bại",
    cancelled: "🛑 Đã hủy",
  };
  return descriptions[status] || status;
}

async function createAdvancedBackupData(guild, type, client, options = {}) {
  const {
    includeMessages = false,
    compress = false,
    progressCallback,
  } = options;

  const data = {
    type: type,
    guildId: guild.id,
    guildName: guild.name,
    memberCount: guild.memberCount,
    createdAt: new Date().toISOString(),
    version: "2.0",
    compressed: compress,
  };

  let progress = 0;
  const totalSteps = getTotalSteps(type, includeMessages);

  const updateProgress = async (step) => {
    progress += 100 / totalSteps;
    if (progressCallback) {
      await progressCallback(Math.min(Math.round(progress), 100));
    }
  };

  if (type === "full" || type === "channels") {
    data.channels = await backupChannels(guild, includeMessages);
    await updateProgress();
  }

  if (type === "full" || type === "roles") {
    data.roles = await backupRoles(guild);
    await updateProgress();
  }

  if (type === "full" || type === "settings") {
    data.settings = await backupSettings(guild, client);
    await updateProgress();
  }

  if (type === "full" || type === "content") {
    data.content = await backupContent(guild);
    await updateProgress();
  }

  if (type === "full" || type === "permissions") {
    data.permissions = await backupPermissions(guild);
    await updateProgress();
  }

  if (includeMessages) {
    data.messages = await backupMessages(guild);
    await updateProgress();
  }

  if (compress) {
    // Simple compression simulation
    data._compressed = true;
    await updateProgress();
  }

  return data;
}

function getTotalSteps(type, includeMessages) {
  let steps = 0;

  if (type === "full") {
    steps = 5; // channels, roles, settings, content, permissions
  } else {
    steps = 1; // single type
  }

  if (includeMessages) steps++;

  return steps;
}

async function backupChannels(guild, includeMessages = false) {
  const channels = {
    categories: [],
    textChannels: [],
    voiceChannels: [],
  };

  // Backup categories
  guild.channels.cache
    .filter((channel) => channel.type === 4) // GuildCategory
    .forEach((category) => {
      channels.categories.push({
        id: category.id,
        name: category.name,
        position: category.position,
        permissions: category.permissionOverwrites.cache.map((overwrite) => ({
          id: overwrite.id,
          type: overwrite.type,
          allow: overwrite.allow.bitfield.toString(),
          deny: overwrite.deny.bitfield.toString(),
        })),
      });
    });

  // Backup text channels
  guild.channels.cache
    .filter((channel) => channel.type === 0) // GuildText
    .forEach((channel) => {
      channels.textChannels.push({
        id: channel.id,
        name: channel.name,
        topic: channel.topic,
        position: channel.position,
        parentId: channel.parentId,
        nsfw: channel.nsfw,
        rateLimitPerUser: channel.rateLimitPerUser,
        permissions: channel.permissionOverwrites.cache.map((overwrite) => ({
          id: overwrite.id,
          type: overwrite.type,
          allow: overwrite.allow.bitfield.toString(),
          deny: overwrite.deny.bitfield.toString(),
        })),
      });
    });

  // Backup voice channels
  guild.channels.cache
    .filter((channel) => channel.type === 2) // GuildVoice
    .forEach((channel) => {
      channels.voiceChannels.push({
        id: channel.id,
        name: channel.name,
        position: channel.position,
        parentId: channel.parentId,
        bitrate: channel.bitrate,
        userLimit: channel.userLimit,
        permissions: channel.permissionOverwrites.cache.map((overwrite) => ({
          id: overwrite.id,
          type: overwrite.type,
          allow: overwrite.allow.bitfield.toString(),
          deny: overwrite.deny.bitfield.toString(),
        })),
      });
    });

  return channels;
}

async function backupRoles(guild) {
  const roles = [];

  guild.roles.cache
    .filter((role) => role.id !== guild.id) // Exclude @everyone
    .forEach((role) => {
      roles.push({
        id: role.id,
        name: role.name,
        color: role.color,
        hoist: role.hoist,
        mentionable: role.mentionable,
        permissions: role.permissions.bitfield.toString(),
        position: role.position,
        icon: role.iconURL(),
        unicodeEmoji: role.unicodeEmoji,
      });
    });

  return roles;
}

async function backupSettings(guild, client) {
  const settings = {};

  try {
    // Welcome config
    const welcomeConfig = await client.db.getWelcomeConfig(guild.id);
    if (welcomeConfig) {
      settings.welcome = welcomeConfig;
    }

    // Autorole config
    const autoroleConfig = await client.db.getAutoroleConfig(guild.id);
    if (autoroleConfig) {
      settings.autoroles = autoroleConfig;
    }

    // Moderation config
    const moderationConfig = await client.db.getModerationConfig(guild.id);
    if (moderationConfig) {
      settings.moderation = moderationConfig;
    }

    // Verification config
    const verificationConfig = await client.db.getVerificationConfig(guild.id);
    if (verificationConfig) {
      settings.verification = verificationConfig;
    }
  } catch (error) {
    console.error("Lỗi khi backup settings:", error);
  }

  return settings;
}

async function backupContent(guild) {
  const content = {
    emojis: [],
    stickers: [],
  };

  // Backup emojis
  guild.emojis.cache.forEach((emoji) => {
    content.emojis.push({
      id: emoji.id,
      name: emoji.name,
      animated: emoji.animated,
      url: emoji.url,
      roles: emoji.roles.cache.map((role) => role.id),
      available: emoji.available,
    });
  });

  // Backup stickers
  guild.stickers.cache.forEach((sticker) => {
    content.stickers.push({
      id: sticker.id,
      name: sticker.name,
      description: sticker.description,
      tags: sticker.tags,
      format: sticker.format,
      url: sticker.url,
      available: sticker.available,
    });
  });

  return content;
}

async function backupPermissions(guild) {
  const permissions = {
    roles: {},
    channels: {},
  };

  // Backup role permissions
  guild.roles.cache.forEach((role) => {
    if (role.id !== guild.id) {
      permissions.roles[role.id] = {
        name: role.name,
        permissions: role.permissions.bitfield.toString(),
      };
    }
  });

  // Backup channel permissions
  guild.channels.cache.forEach((channel) => {
    permissions.channels[channel.id] = {
      name: channel.name,
      type: channel.type,
      overwrites: channel.permissionOverwrites.cache.map((overwrite) => ({
        id: overwrite.id,
        type: overwrite.type,
        allow: overwrite.allow.bitfield.toString(),
        deny: overwrite.deny.bitfield.toString(),
      })),
    };
  });

  return permissions;
}

async function backupMessages(guild) {
  const messages = {};
  const maxMessages = 100; // Limit per channel

  for (const channel of guild.channels.cache.values()) {
    if (channel.type === 0 && channel.viewable) {
      // Text channel
      try {
        const fetchedMessages = await channel.messages.fetch({
          limit: maxMessages,
        });
        messages[channel.id] = fetchedMessages.map((msg) => ({
          id: msg.id,
          content: msg.content,
          author: {
            id: msg.author.id,
            username: msg.author.username,
            discriminator: msg.author.discriminator,
            avatar: msg.author.displayAvatarURL(),
          },
          timestamp: msg.createdTimestamp,
          attachments: msg.attachments.map((att) => ({
            name: att.name,
            url: att.url,
            size: att.size,
          })),
          embeds: msg.embeds.length,
          reactions: msg.reactions.cache.size,
        }));
      } catch (error) {
        console.error(
          `Lỗi khi backup tin nhắn channel ${channel.name}:`,
          error
        );
        messages[channel.id] = [];
      }
    }
  }

  return messages;
}

// Missing handler functions
async function handleBackupCancel(interaction, client) {
  const guildId = interaction.guild.id;

  try {
    const status = await client.db.getBackupStatus(guildId);

    if (!status) {
      const errorEmbed = createErrorEmbed(
        "Không có tiến trình backup nào!",
        "Hiện tại không có tiến trình backup nào đang chạy để hủy."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    await client.db.clearBackupStatus(guildId);

    const successEmbed = createSuccessEmbed(
      "Đã hủy backup!",
      "Tiến trình backup đã được hủy thành công."
    );
    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi hủy backup:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể hủy tiến trình backup."
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleBackupDelete(interaction, client) {
  const name = interaction.options.getString("name");
  const confirm = interaction.options.getBoolean("confirm");
  const guildId = interaction.guild.id;

  if (!confirm) {
    const errorEmbed = createErrorEmbed(
      "Cần xác nhận!",
      "Bạn phải đặt `confirm: True` để xác nhận xóa backup."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    const result = await client.db.deleteServerBackup(guildId, name);

    if (!result) {
      const errorEmbed = createErrorEmbed(
        "Backup không tồn tại!",
        `Không tìm thấy backup với tên "${name}".`
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    const successEmbed = createSuccessEmbed(
      "Đã xóa backup!",
      `Backup **${name}** đã được xóa vĩnh viễn.`
    );
    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi xóa backup:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể xóa backup."
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleBackupList(interaction, client) {
  const filter = interaction.options.getString("filter") || "all";
  const sort = interaction.options.getString("sort") || "newest";
  const guildId = interaction.guild.id;

  try {
    let backups = await client.db.getServerBackups(guildId);

    if (backups.length === 0) {
      const infoEmbed = createInfoEmbed(
        "Không có backup nào",
        "Server này chưa có backup nào. Sử dụng `/backup create` để tạo backup đầu tiên."
      );
      return await interaction.reply({ embeds: [infoEmbed] });
    }

    // Apply filter
    if (filter !== "all") {
      backups = backups.filter((backup) => backup.backup_type === filter);
    }

    // Apply sort
    switch (sort) {
      case "newest":
        backups.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        break;
      case "oldest":
        backups.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
        break;
      case "name_asc":
        backups.sort((a, b) => a.backup_name.localeCompare(b.backup_name));
        break;
      case "name_desc":
        backups.sort((a, b) => b.backup_name.localeCompare(a.backup_name));
        break;
      case "size_desc":
        backups.sort((a, b) => (b.file_size || 0) - (a.file_size || 0));
        break;
      case "size_asc":
        backups.sort((a, b) => (a.file_size || 0) - (b.file_size || 0));
        break;
    }

    const embed = createInfoEmbed(
      `📋 Danh sách Backup (${backups.length})`,
      `Hiển thị backup với bộ lọc: **${getFilterDescription(
        filter
      )}** • Sắp xếp: **${getSortDescription(sort)}**`
    );

    const backupList = backups
      .slice(0, 10)
      .map((backup, index) => {
        const createdDate = new Date(backup.created_at);
        const fileSize = formatFileSize(backup.file_size || 0);
        return (
          `**${index + 1}.** ${backup.backup_name}\n` +
          `└ ${getTypeDescription(
            backup.backup_type
          )} • ${fileSize} • <t:${Math.floor(createdDate.getTime() / 1000)}:R>`
        );
      })
      .join("\n\n");

    embed.addFields({
      name: "📦 Backup",
      value: backupList,
      inline: false,
    });

    if (backups.length > 10) {
      embed.setFooter({
        text: `Hiển thị 10/${backups.length} backup đầu tiên`,
      });
    }

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error("Lỗi khi lấy danh sách backup:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể lấy danh sách backup."
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleBackupLoad(interaction, client) {
  const name = interaction.options.getString("name");
  const overwrite = interaction.options.getBoolean("overwrite") || false;
  const dryRun = interaction.options.getBoolean("dry_run") || false;

  await interaction.reply({
    content: `🔄 Tính năng khôi phục backup đang được phát triển.\n**Backup:** ${name}\n**Overwrite:** ${overwrite}\n**Dry Run:** ${dryRun}`,
    ephemeral: true,
  });
}

async function handleBackupPurge(interaction, client) {
  const criteria = interaction.options.getString("criteria");
  const confirm = interaction.options.getBoolean("confirm");

  if (!confirm) {
    const errorEmbed = createErrorEmbed(
      "Cần xác nhận!",
      "Bạn phải đặt `confirm: True` để xác nhận xóa backup hàng loạt."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  await interaction.reply({
    content: `🗑️ Tính năng xóa backup hàng loạt đang được phát triển.\n**Tiêu chí:** ${criteria}`,
    ephemeral: true,
  });
}

// Interval handlers
async function handleIntervalShow(interaction, client) {
  await interaction.reply({
    content: "📅 Tính năng hiển thị lịch backup tự động đang được phát triển.",
    ephemeral: true,
  });
}

async function handleIntervalOn(interaction, client) {
  const interval = interaction.options.getString("interval");
  const hours = interaction.options.getInteger("hours");
  const maxBackups = interaction.options.getInteger("max_backups");

  await interaction.reply({
    content: `⏰ Tính năng backup tự động đang được phát triển.\n**Interval:** ${interval}\n**Hours:** ${hours}\n**Max Backups:** ${maxBackups}`,
    ephemeral: true,
  });
}

async function handleIntervalOff(interaction, client) {
  await interaction.reply({
    content: "⏹️ Tính năng tắt backup tự động đang được phát triển.",
    ephemeral: true,
  });
}

// Helper functions for list command
function getFilterDescription(filter) {
  const filters = {
    all: "Tất cả",
    full: "Full backup",
    channels: "Channels only",
    roles: "Roles only",
    settings: "Settings only",
  };
  return filters[filter] || filter;
}

function getSortDescription(sort) {
  const sorts = {
    newest: "Mới nhất",
    oldest: "Cũ nhất",
    name_asc: "Tên A-Z",
    name_desc: "Tên Z-A",
    size_desc: "Kích thước lớn nhất",
    size_asc: "Kích thước nhỏ nhất",
  };
  return sorts[sort] || sort;
}
