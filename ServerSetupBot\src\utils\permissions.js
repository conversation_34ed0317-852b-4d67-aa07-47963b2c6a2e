const { PermissionFlagsBits } = require('discord.js');

/**
 * <PERSON><PERSON><PERSON> tra quyền Administrator
 */
function hasAdminPermission(member) {
    return member.permissions.has(PermissionFlagsBits.Administrator);
}

/**
 * Kiểm tra quyền Manage Guild
 */
function hasManageGuildPermission(member) {
    return member.permissions.has(PermissionFlagsBits.ManageGuild);
}

/**
 * Kiểm tra quyền Manage Roles
 */
function hasManageRolesPermission(member) {
    return member.permissions.has(PermissionFlagsBits.ManageRoles);
}

/**
 * Kiểm tra quyền Manage Channels
 */
function hasManageChannelsPermission(member) {
    return member.permissions.has(PermissionFlagsBits.ManageChannels);
}

/**
 * Kiểm tra quyền Manage Messages
 */
function hasManageMessagesPermission(member) {
    return member.permissions.has(PermissionFlagsBits.ManageMessages);
}

/**
 * Kiểm tra quyền Kick Members
 */
function hasKickPermission(member) {
    return member.permissions.has(PermissionFlagsBits.KickMembers);
}

/**
 * Kiểm tra quyền Ban Members
 */
function hasBanPermission(member) {
    return member.permissions.has(PermissionFlagsBits.BanMembers);
}

/**
 * Kiểm tra bot có quyền cần thiết không
 */
function botHasPermission(guild, permission) {
    const botMember = guild.members.me;
    return botMember.permissions.has(permission);
}

/**
 * Kiểm tra bot có thể quản lý role không
 */
function canManageRole(guild, role) {
    const botMember = guild.members.me;
    
    // Bot phải có quyền Manage Roles
    if (!botMember.permissions.has(PermissionFlagsBits.ManageRoles)) {
        return { canManage: false, reason: 'Bot không có quyền **Quản lý vai trò**' };
    }
    
    // Role của bot phải cao hơn role cần quản lý
    if (botMember.roles.highest.position <= role.position) {
        return { canManage: false, reason: 'Vai trò của bot phải cao hơn vai trò cần quản lý' };
    }
    
    // Không thể quản lý @everyone role
    if (role.id === guild.id) {
        return { canManage: false, reason: 'Không thể quản lý vai trò @everyone' };
    }
    
    return { canManage: true, reason: null };
}

/**
 * Kiểm tra user có thể quản lý role không
 */
function userCanManageRole(member, role) {
    // Admin có thể quản lý tất cả
    if (hasAdminPermission(member)) {
        return { canManage: true, reason: null };
    }
    
    // Phải có quyền Manage Roles
    if (!hasManageRolesPermission(member)) {
        return { canManage: false, reason: 'Bạn không có quyền **Quản lý vai trò**' };
    }
    
    // Role của user phải cao hơn role cần quản lý
    if (member.roles.highest.position <= role.position) {
        return { canManage: false, reason: 'Vai trò của bạn phải cao hơn vai trò cần quản lý' };
    }
    
    return { canManage: true, reason: null };
}

/**
 * Kiểm tra bot có thể tạo/quản lý channel không
 */
function canManageChannel(guild, channelType = null) {
    const botMember = guild.members.me;
    
    if (!botMember.permissions.has(PermissionFlagsBits.ManageChannels)) {
        return { canManage: false, reason: 'Bot không có quyền **Quản lý kênh**' };
    }
    
    return { canManage: true, reason: null };
}

/**
 * Kiểm tra bot có thể gửi tin nhắn trong channel không
 */
function canSendMessages(channel) {
    const botMember = channel.guild.members.me;
    const permissions = channel.permissionsFor(botMember);
    
    if (!permissions.has(PermissionFlagsBits.SendMessages)) {
        return { canSend: false, reason: 'Bot không có quyền **Gửi tin nhắn** trong kênh này' };
    }
    
    if (!permissions.has(PermissionFlagsBits.EmbedLinks)) {
        return { canSend: false, reason: 'Bot không có quyền **Nhúng liên kết** trong kênh này' };
    }
    
    return { canSend: true, reason: null };
}

/**
 * Kiểm tra bot có thể thêm reaction không
 */
function canAddReactions(channel) {
    const botMember = channel.guild.members.me;
    const permissions = channel.permissionsFor(botMember);
    
    if (!permissions.has(PermissionFlagsBits.AddReactions)) {
        return { canReact: false, reason: 'Bot không có quyền **Thêm phản ứng** trong kênh này' };
    }
    
    return { canReact: true, reason: null };
}

/**
 * Lấy danh sách quyền cần thiết cho bot
 */
function getRequiredBotPermissions() {
    return [
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.EmbedLinks,
        PermissionFlagsBits.AddReactions,
        PermissionFlagsBits.ManageRoles,
        PermissionFlagsBits.ManageChannels,
        PermissionFlagsBits.ManageMessages,
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.ReadMessageHistory
    ];
}

/**
 * Kiểm tra bot có đủ quyền cơ bản không
 */
function checkBotPermissions(guild) {
    const botMember = guild.members.me;
    const requiredPermissions = getRequiredBotPermissions();
    const missingPermissions = [];
    
    for (const permission of requiredPermissions) {
        if (!botMember.permissions.has(permission)) {
            missingPermissions.push(permission);
        }
    }
    
    return {
        hasAllPermissions: missingPermissions.length === 0,
        missingPermissions: missingPermissions
    };
}

/**
 * Chuyển đổi permission flags thành tên tiếng Việt
 */
function getPermissionName(permission) {
    const permissionNames = {
        [PermissionFlagsBits.Administrator]: 'Quản trị viên',
        [PermissionFlagsBits.ManageGuild]: 'Quản lý máy chủ',
        [PermissionFlagsBits.ManageRoles]: 'Quản lý vai trò',
        [PermissionFlagsBits.ManageChannels]: 'Quản lý kênh',
        [PermissionFlagsBits.ManageMessages]: 'Quản lý tin nhắn',
        [PermissionFlagsBits.KickMembers]: 'Kick thành viên',
        [PermissionFlagsBits.BanMembers]: 'Ban thành viên',
        [PermissionFlagsBits.SendMessages]: 'Gửi tin nhắn',
        [PermissionFlagsBits.EmbedLinks]: 'Nhúng liên kết',
        [PermissionFlagsBits.AddReactions]: 'Thêm phản ứng',
        [PermissionFlagsBits.ViewChannel]: 'Xem kênh',
        [PermissionFlagsBits.ReadMessageHistory]: 'Đọc lịch sử tin nhắn'
    };
    
    return permissionNames[permission] || 'Quyền không xác định';
}

module.exports = {
    hasAdminPermission,
    hasManageGuildPermission,
    hasManageRolesPermission,
    hasManageChannelsPermission,
    hasManageMessagesPermission,
    hasKickPermission,
    hasBanPermission,
    botHasPermission,
    canManageRole,
    userCanManageRole,
    canManageChannel,
    canSendMessages,
    canAddReactions,
    getRequiredBotPermissions,
    checkBotPermissions,
    getPermissionName
};
