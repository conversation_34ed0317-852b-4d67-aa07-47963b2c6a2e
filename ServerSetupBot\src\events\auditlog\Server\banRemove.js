const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildBanRemove,
    async execute(ban, client) {
        try {
            console.log(`🔓 User unbanned: ${ban.user.tag} in ${ban.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(ban.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'BAN_REMOVE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'BAN_REMOVE',
                user: 'System',
                userId: null,
                action: 'Thành viên được unban',
                details: `**${ban.user.tag}** đã được unban khỏi server`,
                target: ban.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who unbanned the user from audit logs
            try {
                const auditLogs = await ban.guild.fetchAuditLogs({
                    type: 23, // MEMBER_BAN_REMOVE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === ban.user.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Thành viên được unban bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for ban remove');
            }
            
            // Add to database
            await client.db.addAuditLog(ban.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔓 Thành viên được unban',
                `Vừa có một thành viên được unban khỏi server`
            );
            
            embed.setColor(0x2ecc71); // Green for unban
            
            embed.addFields([
                {
                    name: '> Thành viên được unban',
                    value: `- ${ban.user.tag} (${ban.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${ban.user.id}`,
                    inline: true
                },
                {
                    name: '> Người thực hiện unban',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian unban',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add account info
            const accountAge = Date.now() - ban.user.createdTimestamp;
            const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
            
            embed.addFields([
                {
                    name: '> Tài khoản tạo',
                    value: `- <t:${Math.floor(ban.user.createdTimestamp / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                },
                {
                    name: '> Có thể tham gia lại',
                    value: `- Có (nếu có invite)`,
                    inline: true
                }
            ]);
            
            // Add original ban reason if available
            if (ban.reason) {
                embed.addFields({
                    name: '> Lý do ban ban đầu',
                    value: `- ${ban.reason}`,
                    inline: false
                });
            }
            
            // Add unban reason
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do unban',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> Lý do unban',
                    value: `- Không có lý do`,
                    inline: false
                });
            }
            
            // Set user avatar as thumbnail
            if (ban.user.displayAvatarURL()) {
                embed.setThumbnail(ban.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${ban.guild.name} • Member Unban`,
                iconURL: ban.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Ban remove logged: ${ban.user.tag}`);
            
        } catch (error) {
            console.error('Error in banRemove audit log:', error);
        }
    }
};
