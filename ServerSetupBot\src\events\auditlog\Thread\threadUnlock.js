const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ThreadUpdate,
    async execute(oldThread, newThread, client) {
        try {
            // Only handle unlocking (was locked, now is not)
            if (!oldThread.locked || newThread.locked) return;
            
            console.log(`🔓 Thread unlocked: ${newThread.name} in ${newThread.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newThread.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'THREAD_UNLOCK')) return;
            
            // Create event data
            const eventData = {
                eventType: 'THREAD_UNLOCK',
                user: 'System',
                userId: null,
                action: 'Thread được mở khóa',
                details: `Thread **${newThread.name}** đã được mở khóa`,
                target: newThread.name,
                channel: newThread.parent?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who unlocked the thread from audit logs
            try {
                const auditLogs = await newThread.guild.fetchAuditLogs({
                    type: 111, // THREAD_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newThread.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Thread được mở khóa bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for thread unlock');
            }
            
            // Add to database
            await client.db.addAuditLog(newThread.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔓 Thread được mở khóa',
                `Vừa có một thread được mở khóa trong server`
            );
            
            embed.setColor(0x2ecc71); // Green for unlock/restore
            
            // Get thread type names
            const threadTypes = {
                10: 'Announcement Thread',
                11: 'Public Thread',
                12: 'Private Thread'
            };
            
            embed.addFields([
                {
                    name: '> Tên thread',
                    value: `- ${newThread.name}`,
                    inline: true
                },
                {
                    name: '> ID thread',
                    value: `- ${newThread.id}`,
                    inline: true
                },
                {
                    name: '> Loại thread',
                    value: `- ${threadTypes[newThread.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh cha',
                    value: `- ${newThread.parent || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người mở khóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian mở khóa',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Số tin nhắn',
                    value: `- ${newThread.messageCount || 0} tin nhắn`,
                    inline: true
                },
                {
                    name: '> Số thành viên',
                    value: `- ${newThread.memberCount || 0} thành viên`,
                    inline: true
                },
                {
                    name: '> Trạng thái archive',
                    value: `- ${newThread.archived ? '📁 Archived' : '🔓 Active'}`,
                    inline: true
                }
            ]);
            
            // Add thread owner info if available
            if (newThread.ownerId) {
                try {
                    const owner = await newThread.guild.members.fetch(newThread.ownerId);
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- ${owner.user.tag}`,
                        inline: true
                    });
                } catch (error) {
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- Unknown (ID: ${newThread.ownerId})`,
                        inline: true
                    });
                }
            }
            
            // Add unlock effects
            embed.addFields({
                name: '> Hiệu ứng mở khóa',
                value: [
                    '• Tất cả thành viên có thể gửi tin nhắn',
                    '• Thread hoạt động bình thường',
                    '• Có thể reply và tương tác',
                    '• Slow mode vẫn áp dụng (nếu có)'
                ].join('\n'),
                inline: false
            });
            
            // Add slow mode info if applicable
            if (newThread.rateLimitPerUser > 0) {
                const formatTime = (seconds) => {
                    if (seconds < 60) return `${seconds} giây`;
                    return `${Math.floor(seconds / 60)} phút`;
                };
                
                embed.addFields({
                    name: '> Slow mode hiện tại',
                    value: `- ${formatTime(newThread.rateLimitPerUser)}`,
                    inline: true
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do mở khóa',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Add thread link
            embed.addFields({
                name: '> Link thread',
                value: `- [Nhấn để xem](https://discord.com/channels/${newThread.guild.id}/${newThread.id})`,
                inline: true
            });
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newThread.guild.name} • Thread Unlock`,
                iconURL: newThread.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Thread unlock logged: ${newThread.name}`);
            
        } catch (error) {
            console.error('Error in threadUnlock audit log:', error);
        }
    }
};
