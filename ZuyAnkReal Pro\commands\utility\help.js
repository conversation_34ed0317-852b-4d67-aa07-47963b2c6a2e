const {
  ActionRowBuilder,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  StringSelectMenuBuilder,
  StringSelectMenuOptionBuilder,
  ComponentType,
  MessageFlags,
} = require("discord.js");
const Settings = require("../../settings.js");
const emoji = require("../../emoji.js");
const owner = Settings.bot.credits.developerId;

module.exports = {
  name: "help",
  aliases: ["h"],
  BotPerms: ["EmbedLinks"],
  voteOnly: false,
  run: async function (client, message, args) {
    const prefix =
      (await client.db8.get(`${message.guild.id}_prefix`)) ||
      Settings.bot.info.prefix;
    const ray = await client.users.fetch(owner);
    const premium = await client.db12.get(`${message.guild.id}_premium`);

    const menuOptionsPrefix = new ActionRowBuilder().addComponents(
      new StringSelectMenuBuilder()
        .setCustomId("helpOptionPrefix")
        .setPlaceholder("Prefix Commands")
        .addOptions([
          new StringSelectMenuOptionBuilder()
            .setLabel("Automod")
            .setValue("automod")
            .setEmoji("1329500050077913210")
            .setDescription("Explore Automod Commands"),
          new StringSelectMenuOptionBuilder()
            .setLabel("Moderation")
            .setValue("moderation")
            .setEmoji("1329500053135425546")
            .setDescription("Explore Moderation Commands"),
          new StringSelectMenuOptionBuilder()
            .setLabel("Server")
            .setValue("server")
            .setEmoji("1329500147008405555")
            .setDescription("Explore Server Commands"),
          new StringSelectMenuOptionBuilder()
            .setLabel("Giveaway")
            .setValue("giveaway")
            .setEmoji("1329500142004600954")
            .setDescription("Explore Giveaway Commands"),
          new StringSelectMenuOptionBuilder()
            .setLabel("Utility")
            .setValue("utility")
            .setEmoji("1329500150166454272")
            .setDescription("Explore Utility Commands"),
          new StringSelectMenuOptionBuilder()
            .setLabel("Sticky Message")
            .setValue("sticky")
            .setEmoji("1329500159222091817")
            .setDescription("Explore Sticky Message Commands"),
          new StringSelectMenuOptionBuilder()
            .setLabel("Vanity")
            .setValue("vanity")
            .setEmoji("1329500059607236639")
            .setDescription("Explore Vanity Commands"),
        ])
    );

    const menuOptionsSlash = new ActionRowBuilder().addComponents(
      new StringSelectMenuBuilder()
        .setCustomId("helpOptionSlash")
        .setPlaceholder("Slash Commands")
        .addOptions([
          new StringSelectMenuOptionBuilder()
            .setLabel("Home")
            .setValue("home")
            .setEmoji("1275390594893611009")
            .setDescription("Explore Home Page"),
          new StringSelectMenuOptionBuilder()
            .setLabel("Welcome")
            .setValue("welcome")
            .setEmoji("1329500056092414052")
            .setDescription("Explore Welcome Commands"),
          new StringSelectMenuOptionBuilder()
            .setLabel("Ticket")
            .setValue("ticket")
            .setEmoji("1329500139286691913")
            .setDescription("Explore Ticket Commands"),
          new StringSelectMenuOptionBuilder()
            .setLabel("Music")
            .setValue("music")
            .setEmoji("1329500136669450301")
            .setDescription("Explore Music Commands"),
          new StringSelectMenuOptionBuilder()
            .setLabel("AI")
            .setValue("ai")
            .setEmoji("1329500167564689630")
            .setDescription("Explore AI Commands"),
        ])
    );

    const embed1 = new EmbedBuilder()
      .setColor("#090000")
      .setDescription(
        "<a:121_Flower:1329498987128029314> **Welcome to Rabbit! Your ultimate Versatile bot.** \n" +
        "<:projects:1329498999660744735> *Use the dropdown menu below to explore various command categories.*"
      )
      .addFields({
        name: "<:Category:1329501312957550714> __Categories__",
        value:
          "> <:eg_shield:1329500050077913210> - **Automod**\n" +
          "> <:ntools:1329500053135425546> - **Moderation**\n" +
          "> <:eg_wave:1329500056092414052> - **Welcome**\n" +
          "> <:nmusic:1329500136669450301> - **Music**\n" +
          "> <:eg_ticket:1329500139286691913> - **Ticket**\n" +
          "> <:_giveaway2:1329500142004600954> - **Giveaway**\n" +
          "> <:eg_discovery:1329500147008405555> - **Server**\n" +
          "> <:eg_wrench:1329500150166454272> - **Utility**\n" +
          "> <:eg_fire:1329500159222091817> - **Sticky Message**\n" +
          "> <:eg_premium:1329500167564689630> - **AI**\n" +
          "> <:nlink:1329500059607236639> - **Vanity**",
        inline: false,
      })
      .setImage(
        "https://github.com/RayDev07/images/blob/main/codex.png?raw=true"
      );

    const embeds = {
      automod: new EmbedBuilder().setColor(client.color).addFields({
        name: `${emoji.id.automod} Automod Commands`,
        value:
          "automod, automod anti message spam enable/disable, automod anti mention spam enable/disable, automod anti toxicity enable/disable, automod config, automod reset",
        inline: false,
      }),
      moderation: new EmbedBuilder().setColor(client.color).addFields({
        name: `${emoji.id.moderation} Moderation Commands`,
        value:
          "timeout <user>, untimeout <user>, clear bots, clear humans, clear embeds, clear files, clear mentions, clear pins, ban <user>, unban <user>, kick <user>, hide <channel>, unhide <channel>, lock <channel>, unlock <channel>, nuke, purge, voice, voice muteall, voice unmuteall, voice deafenall, voice undeafenall, voice mute <user>, voice unmute <user>, voice deafen <user>, voice undeafen <user>",
        inline: false,
      }),
      welcome: new EmbedBuilder().setColor(client.color).addFields({
        name: `${emoji.id.welcome} Welcome Commands`,
        value:
          "welcome setup, welcome variable, autorole, autorole humans add <role mention/id>, autorole humans remove <role mention/id>, autorole bots add <role mention/id>, autorole bots remove <role mention/id>, autorole config, autorole reset",
        inline: false,
      }),
      server: new EmbedBuilder().setColor(client.color).addFields({
        name: `${emoji.id.ignore} Server Commands`,
        value:
          "extra, extra owner add <user mention/id>, extra admin add <user mention/id>, extra owner remove <user mention/id>, extra admin remove <user mention/id>, extra owner show, extra admin show, extra owner reset, extra admin reset, premium, premium add <guild id>, premium remove <guild id>, resetall",
        inline: false,
      }),
      giveaway: new EmbedBuilder().setColor(client.color).addFields({
        name: `${emoji.id.giveaway} Giveaway Commands`,
        value: "gcreate, reroll, end",
        inline: false,
      }),
      ticket: new EmbedBuilder().setColor(client.color).addFields({
        name: `${emoji.id.ticket} Ticket Commands`,
        value: "ticket-panel",
        inline: false,
      }),
      utility: new EmbedBuilder().setColor(client.color).addFields({
        name: `${emoji.id.utility} Utility Commands`,
        value:
          "help, invite, ping, prefix, support, uptime, userinfo, serverinfo, avatar user, botinfo, afk, report, roles, membercount, vote",
        inline: false,
      }),
      music: new EmbedBuilder().setColor(client.color).addFields({
        name: `${emoji.id.music} Music Commands`,
        value:
          "play, pause, resume, stop, skip, volume, loop, nowplaying, queue, remove, clear, leave, search, forward, rewind",
        inline: false,
      }),
      sticky: new EmbedBuilder().setColor(client.color).addFields({
        name: `${emoji.id.sticky} Sticky Message Commands`,
        value: "stickyadd, stickyremove",
        inline: false,
      }),
      ai: new EmbedBuilder().setColor(client.color).addFields({
        name: `${emoji.id.ai} AI Commands`,
        value: "random",
        inline: false,
      }),
      vanity: new EmbedBuilder().setColor(client.color).addFields({
        name: `${emoji.id.vanity} Vanity Commands`,
        value: "vanity set, vanity remove, vanity list",
        inline: false,
      }),
    };

    await message.reply({
      components: [menuOptionsPrefix, menuOptionsSlash],
      embeds: [embed1],
    });

    const collector = message.channel.createMessageComponentCollector({
      filter: (interaction) =>
        interaction.user.id === message.author.id &&
        (interaction.customId === "helpOptionPrefix" ||
          interaction.customId === "helpOptionSlash"),
      componentType: ComponentType.StringSelect,
      time: 120000,
    });

    collector.on("collect", async (interaction) => {
      const selectedEmbed = embeds[interaction.values[0]];
      await interaction.reply({
        embeds: [selectedEmbed],
        ephemeral: true,
      });
    });
  },
};
