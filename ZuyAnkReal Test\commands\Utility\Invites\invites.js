(function(_0x35651f,_0x51f230){function _0x3a10f0(_0x1e5024,_0x3aba92){return _0x4c4f(_0x3aba92- -0x27e,_0x1e5024);}const _0x1e31c9=_0x35651f();while(!![]){try{const _0x587324=-parseInt(_0x3a10f0(-0x68,-0x78))/0x1*(parseInt(_0x3a10f0(-0x77,-0x6d))/0x2)+parseInt(_0x3a10f0(-0x72,-0x77))/0x3*(parseInt(_0x3a10f0(-0x61,-0x6f))/0x4)+parseInt(_0x3a10f0(-0x8a,-0x7a))/0x5+parseInt(_0x3a10f0(-0x73,-0x7f))/0x6+parseInt(_0x3a10f0(-0x80,-0x84))/0x7+-parseInt(_0x3a10f0(-0x99,-0x88))/0x8*(parseInt(_0x3a10f0(-0x85,-0x85))/0x9)+-parseInt(_0x3a10f0(-0x7e,-0x87))/0xa*(parseInt(_0x3a10f0(-0x7b,-0x73))/0xb);if(_0x587324===_0x51f230)break;else _0x1e31c9['push'](_0x1e31c9['shift']());}catch(_0x309342){_0x1e31c9['push'](_0x1e31c9['shift']());}}}(_0x4208,0xe2aed));const {EmbedBuilder,SlashCommandBuilder}=require('discord.js'),Invite=require(_0x520328(0x5d3,0x5db));function _0x4c4f(_0x2e2875,_0x3b9850){const _0x4208bb=_0x4208();return _0x4c4f=function(_0x4c4fd8,_0x2e42a5){_0x4c4fd8=_0x4c4fd8-0x1f1;let _0x51d65f=_0x4208bb[_0x4c4fd8];return _0x51d65f;},_0x4c4f(_0x2e2875,_0x3b9850);}function _0x520328(_0x573ee5,_0x41a852){return _0x4c4f(_0x573ee5-0x3de,_0x41a852);}function _0x4208(){const _0x545f70=['Invite\x20Stats\x20for\x20','guild','setName','Error\x20fetching\x20invite\x20data:','../../../models/inviteSchema','472408XuJGNF','9817410grwpLI','setDescription','63gsRKHz','4622121jrthpw','editReply','Utility','error','joinedUsers','2528922mKnsxO','\x20invite(s).','setRequired','user','getUser','6824430dPTBmz','getSubcommand','1072213LahHYs','118641WzgUgf','options','There\x20was\x20an\x20error\x20fetching\x20the\x20invite\x20data.','>\x20has\x20','11jDNAqc','setTimestamp','The\x20user\x20to\x20check\x20invites\x20for','exports','96OhDvyH','reduce','2jWVdlH','length','#0099ff','setColor','addSubcommand'];_0x4208=function(){return _0x545f70;};return _0x4208();}module[_0x520328(0x5ec,0x5f4)]={'data':new SlashCommandBuilder()['setName']('invites')[_0x520328(0x5d6,0x5e2)]('Check\x20invites')[_0x520328(0x5f3,0x5ec)](_0x5d95ca=>_0x5d95ca[_0x520328(0x5d1,0x5c3)]('user')[_0x520328(0x5d6,0x5e7)]('Check\x20how\x20many\x20invites\x20a\x20user\x20has')['addUserOption'](_0x4d2e92=>_0x4d2e92[_0x520328(0x5d1,0x5d8)]('user')[_0x520328(0x5d6,0x5cd)](_0x520328(0x5eb,0x5de))[_0x520328(0x5df,0x5d4)](![]))),'category':_0x520328(0x5da,0x5c8),async 'execute'(_0x89a1ce){await _0x89a1ce['deferReply']({'ephemeral':!![]});function _0x5cd53d(_0xb538dd,_0x5b8de9){return _0x520328(_0xb538dd- -0x700,_0x5b8de9);}const _0x13ea46=_0x89a1ce[_0x5cd53d(-0x130,-0x11f)]['id'];if(_0x89a1ce[_0x5cd53d(-0x11a,-0x11f)][_0x5cd53d(-0x11d,-0x121)]()===_0x5cd53d(-0x120,-0x127)){const _0x280354=_0x89a1ce[_0x5cd53d(-0x11a,-0x113)][_0x5cd53d(-0x11f,-0x119)](_0x5cd53d(-0x120,-0x11d))||_0x89a1ce[_0x5cd53d(-0x120,-0x10f)];try{const _0x3de914=await Invite['find']({'guildID':_0x13ea46,'inviterID':_0x280354['id']}),_0x5ee358=_0x3de914[_0x5cd53d(-0x112,-0x11f)]((_0x251772,_0xcee69e)=>_0x251772+_0xcee69e[_0x5cd53d(-0x124,-0x11e)][_0x5cd53d(-0x110,-0x10d)],0x0),_0x117068=new EmbedBuilder()[_0x5cd53d(-0x10e,-0x114)](_0x5cd53d(-0x10f,-0x118))['setTitle'](_0x5cd53d(-0x131,-0x136)+_0x280354['tag'])['setDescription']('<@'+_0x280354['id']+_0x5cd53d(-0x118,-0x10e)+_0x5ee358+_0x5cd53d(-0x122,-0x134))[_0x5cd53d(-0x116,-0x11c)]();return _0x89a1ce[_0x5cd53d(-0x127,-0x124)]({'embeds':[_0x117068],'ephemeral':!![]});}catch(_0xd120b7){return console[_0x5cd53d(-0x125,-0x132)](_0x5cd53d(-0x12e,-0x140),_0xd120b7),_0x89a1ce[_0x5cd53d(-0x127,-0x135)]({'content':_0x5cd53d(-0x119,-0x119),'ephemeral':!![]});}}}};