const { SlashCommandBuilder, ChannelType, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { canManageChannel, userCanManageRole } = require('../../utils/permissions.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('permission')
        .setDescription('Quản lý quyền hạn kênh')
        .addSubcommand(subcommand =>
            subcommand
                .setName('setup')
                .setDescription('Thiết lập quyền cho role trong kênh cụ thể')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Kênh cần thiết lập quyền')
                        .setRequired(true)
                        .addChannelTypes(ChannelType.GuildText, ChannelType.GuildVoice, ChannelType.GuildCategory)
                )
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role cần thiết lập quyền')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('reset')
                .setDescription('Reset quyền của role về mặc định')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Kênh cần reset quyền')
                        .setRequired(true)
                        .addChannelTypes(ChannelType.GuildText, ChannelType.GuildVoice, ChannelType.GuildCategory)
                )
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role cần reset quyền')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('view')
                .setDescription('Xem quyền hiện tại của role trong kênh')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Kênh cần xem quyền')
                        .setRequired(true)
                        .addChannelTypes(ChannelType.GuildText, ChannelType.GuildVoice, ChannelType.GuildCategory)
                )
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role cần xem quyền')
                        .setRequired(true)
                )
        ),
    category: 'channels',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();
        
        // Kiểm tra quyền bot
        const canManage = canManageChannel(interaction.guild);
        if (!canManage.canManage) {
            const errorEmbed = createErrorEmbed(
                'Bot không có quyền!',
                canManage.reason
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        switch (subcommand) {
            case 'setup':
                await handleSetup(interaction, client);
                break;
            case 'reset':
                await handleReset(interaction, client);
                break;
            case 'view':
                await handleView(interaction, client);
                break;
        }
    },
};

async function handleSetup(interaction, client) {
    const channel = interaction.options.getChannel('channel');
    const role = interaction.options.getRole('role');
    
    // Kiểm tra quyền của user
    const userCanManage = userCanManageRole(interaction.member, role);
    if (!userCanManage.canManage) {
        const errorEmbed = createErrorEmbed(
            'Không có quyền!',
            userCanManage.reason
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    // Tạo select menu cho quyền
    const permissionOptions = getPermissionOptions(channel.type);
    
    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`channel_permission_${channel.id}_${role.id}`)
        .setPlaceholder('Chọn quyền cho role trong kênh này')
        .addOptions(permissionOptions);
    
    const row = new ActionRowBuilder().addComponents(selectMenu);
    
    const embed = createInfoEmbed(
        '🔒 Thiết lập quyền kênh',
        `**Kênh:** ${channel}\n` +
        `**Role:** ${role}\n` +
        `**Loại kênh:** ${getChannelTypeName(channel.type)}\n\n` +
        `Chọn quyền bạn muốn thiết lập cho role này trong kênh:`
    );
    
    embed.addFields({
        name: '📝 Hướng dẫn',
        value: '• **Cho phép tất cả** - Role có thể làm mọi thứ\n' +
               '• **Từ chối tất cả** - Role không thể làm gì\n' +
               '• **Chỉ xem** - Role chỉ có thể xem kênh\n' +
               '• **Xem và chat** - Role có thể xem và gửi tin nhắn\n' +
               '• **Voice cơ bản** - Role có thể tham gia voice\n' +
               '• **Voice đầy đủ** - Role có thể tham gia và nói',
        inline: false
    });
    
    await interaction.reply({ embeds: [embed], components: [row] });
}

async function handleReset(interaction, client) {
    const channel = interaction.options.getChannel('channel');
    const role = interaction.options.getRole('role');
    
    // Kiểm tra quyền của user
    const userCanManage = userCanManageRole(interaction.member, role);
    if (!userCanManage.canManage) {
        const errorEmbed = createErrorEmbed(
            'Không có quyền!',
            userCanManage.reason
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    try {
        // Xóa permission overwrite cho role
        await channel.permissionOverwrites.delete(role, `Quyền được reset bởi ${interaction.user.tag}`);
        
        const successEmbed = createSuccessEmbed(
            'Đã reset quyền thành công!',
            `**Kênh:** ${channel}\n` +
            `**Role:** ${role}\n\n` +
            `Quyền của role đã được reset về mặc định. Role này giờ sẽ kế thừa quyền từ @everyone và các role khác.`
        );
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi reset quyền:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi reset quyền!',
            'Không thể reset quyền. Vui lòng kiểm tra quyền bot và thử lại!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleView(interaction, client) {
    const channel = interaction.options.getChannel('channel');
    const role = interaction.options.getRole('role');
    
    try {
        const permissionOverwrite = channel.permissionOverwrites.cache.get(role.id);
        
        const embed = createInfoEmbed(
            `🔍 Quyền của ${role.name} trong ${channel.name}`,
            `Quyền hiện tại của role **${role.name}** trong kênh ${channel}:`
        );
        
        if (!permissionOverwrite) {
            embed.addFields({
                name: '📋 Trạng thái',
                value: 'Role này không có quyền đặc biệt trong kênh.\nSẽ kế thừa quyền từ @everyone và các role khác.',
                inline: false
            });
        } else {
            const allowedPerms = permissionOverwrite.allow.toArray();
            const deniedPerms = permissionOverwrite.deny.toArray();
            
            if (allowedPerms.length > 0) {
                embed.addFields({
                    name: '✅ Quyền được cho phép',
                    value: allowedPerms.map(perm => `• ${getPermissionName(perm)}`).join('\n'),
                    inline: false
                });
            }
            
            if (deniedPerms.length > 0) {
                embed.addFields({
                    name: '❌ Quyền bị từ chối',
                    value: deniedPerms.map(perm => `• ${getPermissionName(perm)}`).join('\n'),
                    inline: false
                });
            }
            
            if (allowedPerms.length === 0 && deniedPerms.length === 0) {
                embed.addFields({
                    name: '📋 Trạng thái',
                    value: 'Role có permission overwrite nhưng không có quyền cụ thể nào được thiết lập.',
                    inline: false
                });
            }
        }
        
        embed.addFields({
            name: '🔧 Quản lý',
            value: `• \`/permission setup\` - Thiết lập quyền\n` +
                   `• \`/permission reset\` - Reset quyền`,
            inline: false
        });
        
        await interaction.reply({ embeds: [embed] });
        
    } catch (error) {
        console.error('Lỗi khi xem quyền:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi xem quyền!',
            'Không thể lấy thông tin quyền. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

function getPermissionOptions(channelType) {
    const baseOptions = [
        {
            label: '✅ Cho phép tất cả',
            description: 'Role có thể làm mọi thứ trong kênh này',
            value: 'allow_all'
        },
        {
            label: '❌ Từ chối tất cả',
            description: 'Role không thể làm gì trong kênh này',
            value: 'deny_all'
        },
        {
            label: '👀 Chỉ xem',
            description: 'Role chỉ có thể xem kênh',
            value: 'view_only'
        }
    ];
    
    if (channelType === ChannelType.GuildText) {
        baseOptions.push(
            {
                label: '💬 Xem và chat',
                description: 'Role có thể xem và gửi tin nhắn',
                value: 'view_chat'
            }
        );
    } else if (channelType === ChannelType.GuildVoice) {
        baseOptions.push(
            {
                label: '🔊 Voice cơ bản',
                description: 'Role có thể tham gia voice nhưng không nói',
                value: 'voice_basic'
            },
            {
                label: '🎤 Voice đầy đủ',
                description: 'Role có thể tham gia và nói trong voice',
                value: 'voice_full'
            }
        );
    }
    
    return baseOptions;
}

function getChannelTypeName(type) {
    const types = {
        [ChannelType.GuildText]: 'Kênh Text',
        [ChannelType.GuildVoice]: 'Kênh Voice',
        [ChannelType.GuildCategory]: 'Danh mục'
    };
    return types[type] || 'Không xác định';
}

function getPermissionName(permission) {
    const names = {
        'ViewChannel': 'Xem kênh',
        'SendMessages': 'Gửi tin nhắn',
        'ReadMessageHistory': 'Đọc lịch sử tin nhắn',
        'AddReactions': 'Thêm reaction',
        'UseExternalEmojis': 'Sử dụng emoji ngoài',
        'EmbedLinks': 'Nhúng link',
        'AttachFiles': 'Đính kèm file',
        'Connect': 'Kết nối voice',
        'Speak': 'Nói trong voice',
        'UseVAD': 'Sử dụng Voice Activity',
        'ManageMessages': 'Quản lý tin nhắn',
        'ManageChannels': 'Quản lý kênh'
    };
    return names[permission] || permission;
}
