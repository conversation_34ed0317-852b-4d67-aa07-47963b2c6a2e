const { REST, Routes } = require("discord.js");
const fs = require("fs");
const path = require("path");
const config = require("../config/config");

async function deployCommands(client) {
  const commands = [];
  const commandsPath = path.join(__dirname, "../commands");

  if (!fs.existsSync(commandsPath)) {
    console.log("⚠️ Thư mục commands không tồn tại");
    return;
  }

  const commandFiles = fs
    .readdirSync(commandsPath)
    .filter((file) => file.endsWith(".js"));

  // L<PERSON>y tất cả commands
  for (const file of commandFiles) {
    const filePath = path.join(commandsPath, file);
    const command = require(filePath);

    if ("data" in command && "execute" in command) {
      commands.push(command.data.toJSON());
    } else {
      console.log(`⚠️ Command ${file} thiếu thuộc tính "data" hoặc "execute"`);
    }
  }

  // Tạo REST instance
  const rest = new REST().setToken(config.discord.token);

  try {
    console.log("🗑️ Đang xóa tất cả slash commands cũ...");

    // Xóa tất cả commands cũ trước
    await rest.put(Routes.applicationCommands(config.discord.clientId), {
      body: [],
    });

    console.log("✅ Đã xóa tất cả commands cũ");

    console.log(`🔄 Đang deploy ${commands.length} slash command(s) mới...`);

    // Deploy commands mới
    const data = await rest.put(
      Routes.applicationCommands(config.discord.clientId),
      { body: commands }
    );

    console.log(`✅ Đã deploy thành công ${data.length} slash command(s)`);
  } catch (error) {
    console.error("❌ Lỗi deploy commands:", error);
    throw error;
  }
}

module.exports = { deployCommands };
