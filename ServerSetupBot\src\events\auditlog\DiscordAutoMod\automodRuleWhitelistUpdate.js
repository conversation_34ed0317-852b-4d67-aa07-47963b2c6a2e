const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.AutoModerationRuleUpdate,
    async execute(oldRule, newRule, client) {
        try {
            // Only handle allow list (whitelist) changes in trigger metadata
            const oldAllowList = oldRule.triggerMetadata?.allowList || [];
            const newAllowList = newRule.triggerMetadata?.allowList || [];
            
            if (JSON.stringify(oldAllowList.sort()) === JSON.stringify(newAllowList.sort())) return;
            
            console.log(`📝 AutoMod rule whitelist updated: ${newRule.name} in ${newRule.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newRule.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'AUTOMOD_RULE_WHITELIST_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'AUTOMOD_RULE_WHITELIST_UPDATE',
                user: 'System',
                userId: null,
                action: 'Whitelist quy tắc AutoMod được cập nhật',
                details: `Whitelist của quy tắc AutoMod **${newRule.name}** đã được cập nhật`,
                target: newRule.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the rule whitelist from audit logs
            try {
                const auditLogs = await newRule.guild.fetchAuditLogs({
                    type: 141, // AUTO_MODERATION_RULE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newRule.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Whitelist quy tắc AutoMod được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for automod rule whitelist update');
            }
            
            // Add to database
            await client.db.addAuditLog(newRule.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📝 Whitelist quy tắc AutoMod được cập nhật',
                `Vừa có whitelist của quy tắc AutoMod được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get trigger type names
            const triggerTypes = {
                1: 'Keyword',
                2: 'Harmful Link',
                3: 'Spam',
                4: 'Keyword Preset',
                5: 'Mention Spam'
            };
            
            embed.addFields([
                {
                    name: '> Tên quy tắc',
                    value: `- ${newRule.name}`,
                    inline: true
                },
                {
                    name: '> ID quy tắc',
                    value: `- ${newRule.id}`,
                    inline: true
                },
                {
                    name: '> Loại trigger',
                    value: `- ${triggerTypes[newRule.triggerType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${newRule.enabled ? '✅ Đang hoạt động' : '❌ Đã tắt'}`,
                    inline: true
                }
            ]);
            
            // Analyze whitelist changes
            const addedItems = newAllowList.filter(item => !oldAllowList.includes(item));
            const removedItems = oldAllowList.filter(item => !newAllowList.includes(item));
            
            // Add whitelist counts
            embed.addFields([
                {
                    name: '> Số items cũ',
                    value: `- ${oldAllowList.length} items`,
                    inline: true
                },
                {
                    name: '> Số items mới',
                    value: `- ${newAllowList.length} items`,
                    inline: true
                },
                {
                    name: '> Thay đổi',
                    value: `- ${newAllowList.length - oldAllowList.length >= 0 ? '+' : ''}${newAllowList.length - oldAllowList.length} items`,
                    inline: true
                }
            ]);
            
            // Show added items
            if (addedItems.length > 0) {
                const addedItemsList = addedItems.map(item => `• \`${item}\``).join('\n');
                const displayList = addedItemsList.length > 1000 ? addedItemsList.substring(0, 1000) + '...' : addedItemsList;
                
                embed.addFields({
                    name: '> ✅ Items được thêm vào whitelist',
                    value: displayList,
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for additions
            }
            
            // Show removed items
            if (removedItems.length > 0) {
                const removedItemsList = removedItems.map(item => `• \`${item}\``).join('\n');
                const displayList = removedItemsList.length > 1000 ? removedItemsList.substring(0, 1000) + '...' : removedItemsList;
                
                embed.addFields({
                    name: '> ❌ Items được xóa khỏi whitelist',
                    value: displayList,
                    inline: false
                });
                if (addedItems.length === 0) {
                    embed.setColor(0xe74c3c); // Red for removals only
                }
            }
            
            // Show current whitelist (if not too many items)
            if (newAllowList.length <= 10 && newAllowList.length > 0) {
                const currentItemsList = newAllowList.map(item => `• \`${item}\``).join('\n');
                embed.addFields({
                    name: '> 📋 Whitelist hiện tại',
                    value: currentItemsList,
                    inline: false
                });
            } else if (newAllowList.length > 10) {
                embed.addFields({
                    name: '> 📋 Whitelist hiện tại',
                    value: `- Tổng cộng ${newAllowList.length} items (quá nhiều để hiển thị)`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> 📋 Whitelist hiện tại',
                    value: `- Không có items nào trong whitelist`,
                    inline: false
                });
            }
            
            // Add impact explanation
            if (addedItems.length > 0) {
                embed.addFields({
                    name: '> 📈 Tác động khi thêm vào whitelist',
                    value: [
                        '• Các từ/cụm từ này sẽ không bị kiểm soát',
                        '• Có thể sử dụng tự do mà không vi phạm quy tắc',
                        '• Giúp tránh false positive',
                        '• Cần cân nhắc kỹ để tránh lỗ hổng bảo mật'
                    ].join('\n'),
                    inline: false
                });
            }
            
            if (removedItems.length > 0) {
                embed.addFields({
                    name: '> 📉 Tác động khi xóa khỏi whitelist',
                    value: [
                        '• Các từ/cụm từ này sẽ bị kiểm soát lại',
                        '• Có thể bị xử lý nếu vi phạm quy tắc',
                        '• Tăng cường bảo mật',
                        '• Có thể gây false positive'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add whitelist explanation
            embed.addFields({
                name: '> 💡 Về Whitelist (Allow List)',
                value: [
                    '• Whitelist chứa các từ/cụm từ được cho phép',
                    '• Nội dung trong whitelist sẽ không bị kiểm soát',
                    '• Hữu ích để tránh false positive',
                    '• Chỉ áp dụng cho keyword triggers',
                    '• Cần cập nhật thường xuyên'
                ].join('\n'),
                inline: false
            });
            
            // Add best practices
            embed.addFields({
                name: '> 📝 Khuyến nghị',
                value: [
                    '• Chỉ thêm từ thực sự cần thiết',
                    '• Tránh thêm từ có thể bị lạm dụng',
                    '• Kiểm tra định kỳ và cập nhật',
                    '• Test kỹ trước khi áp dụng',
                    '• Ghi chép lý do cho mỗi item'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newRule.guild.name} • AutoMod Rule Whitelist Update`,
                iconURL: newRule.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ AutoMod rule whitelist update logged: ${newRule.name}`);
            
        } catch (error) {
            console.error('Error in automodRuleWhitelistUpdate audit log:', error);
        }
    }
};
