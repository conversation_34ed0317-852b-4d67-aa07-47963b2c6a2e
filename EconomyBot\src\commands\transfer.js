const { SlashCommandBuilder } = require("discord.js");
const User = require("../models/User");
const {
  transferMoney,
  parseAmount,
  validateAmount,
} = require("../utils/economy");
const {
  createSuccessEmbed,
  createErrorEmbed,
  formatCurrency,
} = require("../utils/embedBuilder");
const config = require("../config/config");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("transfer")
    .setDescription("Chuyển tiền cho người dùng khác")
    .addUserOption((option) =>
      option.setName("user").setDescription("Người nhận tiền").setRequired(true)
    )
    .addStringOption((option) =>
      option
        .setName("amount")
        .setDescription(
          "Số tiền cần chuyển (có thể dùng: all, half, 1k, 1m, v.v.)"
        )
        .setRequired(true)
    ),

  cooldown: 5,

  async execute(interaction) {
    try {
      const targetUser = interaction.options.getUser("user");
      const amountStr = interaction.options.getString("amount");

      // Kiểm tra không thể chuyển tiền cho chính mình
      if (targetUser.id === interaction.user.id) {
        const errorEmbed = createErrorEmbed(
          "Lỗi",
          "Bạn không thể chuyển tiền cho chính mình!"
        );
        return await interaction.reply({
          embeds: [errorEmbed],
          ephemeral: true,
        });
      }

      // Kiểm tra không thể chuyển tiền cho bot
      if (targetUser.bot) {
        const errorEmbed = createErrorEmbed(
          "Lỗi",
          "Bạn không thể chuyển tiền cho bot!"
        );
        return await interaction.reply({
          embeds: [errorEmbed],
          ephemeral: true,
        });
      }

      // Lấy thông tin người gửi
      const senderData = await User.findOrCreate(interaction.user);

      // Parse số tiền
      let amount;
      try {
        const parsedAmount = parseAmount(amountStr);

        if (parsedAmount === "all") {
          amount = senderData.balance;
        } else if (parsedAmount === "half") {
          amount = Math.floor(senderData.balance / 2);
        } else {
          amount = parsedAmount;
        }

        // Validate số tiền
        amount = validateAmount(amount, senderData.balance);
      } catch (error) {
        const errorEmbed = createErrorEmbed(
          "Số tiền không hợp lệ",
          `${error.message}\n\n**Ví dụ hợp lệ:** 1000, 1k, 1m, all, half`
        );
        return await interaction.reply({
          embeds: [errorEmbed],
          ephemeral: true,
        });
      }

      // Kiểm tra số dư
      if (senderData.balance < amount) {
        const errorEmbed = createErrorEmbed(
          "Số dư không đủ",
          `Bạn cần **${formatCurrency(
            amount
          )}** nhưng chỉ có **${formatCurrency(senderData.balance)}**`
        );
        return await interaction.reply({
          embeds: [errorEmbed],
          ephemeral: true,
        });
      }

      // Kiểm tra số tiền tối thiểu
      if (amount < 1) {
        const errorEmbed = createErrorEmbed(
          "Số tiền quá nhỏ",
          "Số tiền chuyển phải ít nhất là 1 xu!"
        );
        return await interaction.reply({
          embeds: [errorEmbed],
          ephemeral: true,
        });
      }

      // Tạo hoặc lấy thông tin người nhận
      await User.findOrCreate(targetUser);

      // Thực hiện chuyển tiền
      const result = await transferMoney(
        interaction.user.id,
        targetUser.id,
        amount,
        `Chuyển tiền từ ${interaction.user.username} đến ${targetUser.username}`
      );

      // Tạo embed thành công
      const successEmbed = createSuccessEmbed(
        "Chuyển tiền thành công!",
        `Bạn đã chuyển **${formatCurrency(amount)}** cho **${
          targetUser.username
        }**\n\n` +
          `${config.emojis.money} **Số dư còn lại:** ${formatCurrency(
            result.fromBalance
          )}`
      );

      successEmbed.addFields({
        name: `${config.emojis.info} Thông tin giao dịch`,
        value:
          `**Người gửi:** ${interaction.user.username}\n` +
          `**Người nhận:** ${targetUser.username}\n` +
          `**Số tiền:** ${formatCurrency(amount)}\n` +
          `**Thời gian:** <t:${Math.floor(Date.now() / 1000)}:F>`,
        inline: false,
      });

      await interaction.reply({ embeds: [successEmbed] });
    } catch (error) {
      console.error("Lỗi lệnh transfer:", error);

      const errorEmbed = createErrorEmbed(
        "Lỗi giao dịch",
        error.message || "Không thể thực hiện giao dịch. Vui lòng thử lại sau."
      );

      await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  },
};
