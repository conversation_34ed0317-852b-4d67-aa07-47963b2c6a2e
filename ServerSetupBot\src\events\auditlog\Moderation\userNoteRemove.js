const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction, client) {
        try {
            // This would be triggered by a custom note remove command
            if (!interaction.isCommand() || interaction.commandName !== 'note-remove') return;
            
            console.log(`🗑️ User note removed in ${interaction.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(interaction.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'USER_NOTE_REMOVE')) return;
            
            // Get note removal details from interaction
            const noteId = interaction.options.getString('note_id');
            const removeReason = interaction.options.getString('reason') || 'Không có lý do cụ thể';
            const moderator = interaction.user;
            
            // Get note info before removing
            const noteInfo = await client.db.getUserNote(interaction.guild.id, noteId);
            if (!noteInfo) return;
            
            // Remove note from database
            await client.db.removeUserNote(interaction.guild.id, noteId);
            
            // Create event data
            const eventData = {
                eventType: 'USER_NOTE_REMOVE',
                user: moderator.tag,
                userId: moderator.id,
                action: `Note được xóa khỏi ${noteInfo.targetUser || 'Unknown'}`,
                details: `**${moderator.tag}** đã xóa note #${noteId} của **${noteInfo.targetUser || 'Unknown'}**`,
                target: noteInfo.targetUser || 'Unknown',
                timestamp: new Date().toISOString(),
                noteId: noteId
            };
            
            // Add to database
            await client.db.addAuditLog(interaction.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Note được xóa khỏi thành viên',
                `Vừa có note được xóa khỏi hồ sơ thành viên`
            );
            
            embed.setColor(0xe74c3c); // Red for removal
            
            // Get note type names
            const noteTypes = {
                'general': 'Ghi chú chung',
                'warning': 'Cảnh báo',
                'positive': 'Tích cực',
                'behavior': 'Hành vi',
                'history': 'Lịch sử',
                'other': 'Khác'
            };
            
            embed.addFields([
                {
                    name: '> ID Note đã xóa',
                    value: `- #${noteId}`,
                    inline: true
                },
                {
                    name: '> Đối tượng',
                    value: `- ${noteInfo.targetUser || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Moderator xóa',
                    value: `- ${moderator.tag}`,
                    inline: true
                },
                {
                    name: '> Thời gian xóa',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add original note info
            if (noteInfo) {
                embed.addFields([
                    {
                        name: '> Moderator tạo note gốc',
                        value: `- ${noteInfo.moderator || 'Unknown'}`,
                        inline: true
                    },
                    {
                        name: '> Thời gian tạo note',
                        value: `- <t:${Math.floor(new Date(noteInfo.createdAt).getTime() / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Loại note',
                        value: `- ${noteTypes[noteInfo.type] || noteInfo.type}`,
                        inline: true
                    },
                    {
                        name: '> Quyền riêng tư',
                        value: `- ${noteInfo.isPrivate ? '🔒 Riêng tư' : '👁️ Công khai'}`,
                        inline: true
                    }
                ]);
                
                // Add original note content (truncated)
                if (noteInfo.content) {
                    const displayContent = noteInfo.content.length > 300 
                        ? noteInfo.content.substring(0, 300) + '...'
                        : noteInfo.content;
                    
                    embed.addFields({
                        name: '> Nội dung note đã xóa',
                        value: `\`\`\`${displayContent}\`\`\``,
                        inline: false
                    });
                }
                
                // Calculate note lifetime
                const noteLifetime = Date.now() - new Date(noteInfo.createdAt).getTime();
                const days = Math.floor(noteLifetime / (1000 * 60 * 60 * 24));
                const hours = Math.floor((noteLifetime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                
                embed.addFields({
                    name: '> Thời gian tồn tại',
                    value: `- ${days} ngày ${hours} giờ`,
                    inline: true
                });
            }
            
            // Add removal reason
            embed.addFields({
                name: '> Lý do xóa',
                value: `- ${removeReason}`,
                inline: false
            });
            
            // Get remaining note count for this user
            const remainingNotes = await client.db.getUserNoteCount(interaction.guild.id, noteInfo.targetUserId);
            embed.addFields({
                name: '> Số notes còn lại',
                value: `- ${remainingNotes} notes`,
                inline: true
            });
            
            // Add impact of removal
            embed.addFields({
                name: '> 📝 Tác động của việc xóa note',
                value: [
                    '• Thông tin note không còn trong hồ sơ',
                    '• Có thể ảnh hưởng đến quyết định tương lai',
                    '• Giảm context về lịch sử thành viên',
                    '• Cần cân nhắc kỹ trước khi xóa'
                ].join('\n'),
                inline: false
            });
            
            // Add common reasons for note removal
            embed.addFields({
                name: '> 🤔 Lý do thường gặp khi xóa note',
                value: [
                    '• Thông tin không chính xác',
                    '• Note trùng lặp',
                    '• Thành viên đã cải thiện lâu dài',
                    '• Yêu cầu từ thành viên có lý do hợp lệ'
                ].join('\n'),
                inline: false
            });
            
            // Add guidelines for note removal
            embed.addFields({
                name: '> 💡 Lưu ý khi xóa notes',
                value: [
                    '• Chỉ xóa khi thực sự cần thiết',
                    '• Ghi chép lý do xóa rõ ràng',
                    '• Cân nhắc edit thay vì xóa',
                    '• Thảo luận với team nếu note quan trọng'
                ].join('\n'),
                inline: false
            });
            
            // Add warning if this was a warning note
            if (noteInfo?.type === 'warning') {
                embed.addFields({
                    name: '> ⚠️ Cảnh báo',
                    value: [
                        '• Đây là note cảnh báo',
                        '• Việc xóa có thể ảnh hưởng đến moderation',
                        '• Cần đảm bảo lý do xóa hợp lệ',
                        '• Có thể cần backup thông tin'
                    ].join('\n'),
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${interaction.guild.name} • User Note Remove`,
                iconURL: interaction.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ User note remove logged: #${noteId}`);
            
        } catch (error) {
            console.error('Error in userNoteRemove audit log:', error);
        }
    }
};
