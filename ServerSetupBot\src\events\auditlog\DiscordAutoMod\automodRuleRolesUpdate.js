const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.AutoModerationRuleUpdate,
    async execute(oldRule, newRule, client) {
        try {
            // Only handle exempt roles changes
            const oldRoleIds = Array.from(oldRule.exemptRoles?.keys() || []).sort();
            const newRoleIds = Array.from(newRule.exemptRoles?.keys() || []).sort();
            
            if (JSON.stringify(oldRoleIds) === JSON.stringify(newRoleIds)) return;
            
            console.log(`👥 AutoMod rule roles updated: ${newRule.name} in ${newRule.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newRule.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'AUTOMOD_RULE_ROLES_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'AUTOMOD_RULE_ROLES_UPDATE',
                user: 'System',
                userId: null,
                action: 'Roles miễn trừ quy tắc AutoMod được cập nhật',
                details: `Roles miễn trừ của quy tắc AutoMod **${newRule.name}** đã được cập nhật`,
                target: newRule.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the rule roles from audit logs
            try {
                const auditLogs = await newRule.guild.fetchAuditLogs({
                    type: 141, // AUTO_MODERATION_RULE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newRule.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Roles miễn trừ quy tắc AutoMod được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for automod rule roles update');
            }
            
            // Add to database
            await client.db.addAuditLog(newRule.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '👥 Roles miễn trừ quy tắc AutoMod được cập nhật',
                `Vừa có roles miễn trừ của quy tắc AutoMod được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get trigger type names
            const triggerTypes = {
                1: 'Keyword',
                2: 'Harmful Link',
                3: 'Spam',
                4: 'Keyword Preset',
                5: 'Mention Spam'
            };
            
            embed.addFields([
                {
                    name: '> Tên quy tắc',
                    value: `- ${newRule.name}`,
                    inline: true
                },
                {
                    name: '> ID quy tắc',
                    value: `- ${newRule.id}`,
                    inline: true
                },
                {
                    name: '> Loại trigger',
                    value: `- ${triggerTypes[newRule.triggerType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${newRule.enabled ? '✅ Đang hoạt động' : '❌ Đã tắt'}`,
                    inline: true
                }
            ]);
            
            // Analyze role changes
            const oldRoles = Array.from(oldRule.exemptRoles?.values() || []);
            const newRoles = Array.from(newRule.exemptRoles?.values() || []);
            
            const addedRoles = newRoles.filter(newRole => !oldRoles.find(oldRole => oldRole.id === newRole.id));
            const removedRoles = oldRoles.filter(oldRole => !newRoles.find(newRole => newRole.id === oldRole.id));
            
            // Add role counts
            embed.addFields([
                {
                    name: '> Số roles cũ',
                    value: `- ${oldRoles.length} roles`,
                    inline: true
                },
                {
                    name: '> Số roles mới',
                    value: `- ${newRoles.length} roles`,
                    inline: true
                },
                {
                    name: '> Thay đổi',
                    value: `- ${newRoles.length - oldRoles.length >= 0 ? '+' : ''}${newRoles.length - oldRoles.length} roles`,
                    inline: true
                }
            ]);
            
            // Show added roles
            if (addedRoles.length > 0) {
                const addedRolesList = addedRoles.map(role => `• ${role.name}`).join('\n');
                embed.addFields({
                    name: '> ✅ Roles được thêm vào miễn trừ',
                    value: addedRolesList.length > 1000 ? addedRolesList.substring(0, 1000) + '...' : addedRolesList,
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for additions
            }
            
            // Show removed roles
            if (removedRoles.length > 0) {
                const removedRolesList = removedRoles.map(role => `• ${role.name}`).join('\n');
                embed.addFields({
                    name: '> ❌ Roles được xóa khỏi miễn trừ',
                    value: removedRolesList.length > 1000 ? removedRolesList.substring(0, 1000) + '...' : removedRolesList,
                    inline: false
                });
                if (addedRoles.length === 0) {
                    embed.setColor(0xe74c3c); // Red for removals only
                }
            }
            
            // Show current exempt roles (if not too many)
            if (newRoles.length <= 10 && newRoles.length > 0) {
                const currentRolesList = newRoles.map(role => `• ${role.name}`).join('\n');
                embed.addFields({
                    name: '> 📋 Roles miễn trừ hiện tại',
                    value: currentRolesList,
                    inline: false
                });
            } else if (newRoles.length > 10) {
                embed.addFields({
                    name: '> 📋 Roles miễn trừ hiện tại',
                    value: `- Tổng cộng ${newRoles.length} roles (quá nhiều để hiển thị)`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> 📋 Roles miễn trừ hiện tại',
                    value: `- Không có roles nào được miễn trừ`,
                    inline: false
                });
            }
            
            // Add impact explanation
            if (addedRoles.length > 0) {
                embed.addFields({
                    name: '> 📈 Tác động khi thêm roles',
                    value: [
                        '• Thành viên có roles này sẽ không bị kiểm soát',
                        '• Có thể gửi nội dung vi phạm mà không bị xử lý',
                        '• Tăng quyền tự do cho roles tin cậy',
                        '• Cần cân nhắc kỹ trước khi thêm'
                    ].join('\n'),
                    inline: false
                });
            }
            
            if (removedRoles.length > 0) {
                embed.addFields({
                    name: '> 📉 Tác động khi xóa roles',
                    value: [
                        '• Thành viên có roles này sẽ bị kiểm soát lại',
                        '• Nội dung vi phạm sẽ được xử lý',
                        '• Tăng cường bảo mật cho server',
                        '• Có thể ảnh hưởng đến moderators'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add best practices
            embed.addFields({
                name: '> 💡 Khuyến nghị',
                value: [
                    '• Chỉ miễn trừ roles moderator và admin',
                    '• Tránh miễn trừ quá nhiều roles',
                    '• Kiểm tra định kỳ danh sách miễn trừ',
                    '• Cân nhắc tạo role riêng cho AutoMod exempt'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newRule.guild.name} • AutoMod Rule Roles Update`,
                iconURL: newRule.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ AutoMod rule roles update logged: ${newRule.name}`);
            
        } catch (error) {
            console.error('Error in automodRuleRolesUpdate audit log:', error);
        }
    }
};
