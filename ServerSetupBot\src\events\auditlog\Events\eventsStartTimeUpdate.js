const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildScheduledEventUpdate,
    async execute(oldEvent, newEvent, client) {
        try {
            // Only handle start time changes
            if (oldEvent.scheduledStartTimestamp === newEvent.scheduledStartTimestamp) return;
            
            console.log(`⏰ Event start time updated: ${newEvent.name} in ${newEvent.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newEvent.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EVENTS_START_TIME_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EVENTS_START_TIME_UPDATE',
                user: 'System',
                userId: null,
                action: 'Thời gian bắt đầu sự kiện được cập nhật',
                details: `Thời gian bắt đầu của sự kiện **${newEvent.name}** đã được cập nhật`,
                target: newEvent.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the event start time from audit logs
            try {
                const auditLogs = await newEvent.guild.fetchAuditLogs({
                    type: 102, // GUILD_SCHEDULED_EVENT_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newEvent.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Thời gian bắt đầu sự kiện được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for event start time update');
            }
            
            // Add to database
            await client.db.addAuditLog(newEvent.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '⏰ Thời gian bắt đầu sự kiện được cập nhật',
                `Vừa có thời gian bắt đầu sự kiện được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get event entity type names
            const entityTypes = {
                1: 'Stage Instance',
                2: 'Voice Channel',
                3: 'External Location'
            };
            
            // Get event status names
            const statusTypes = {
                1: 'Scheduled',
                2: 'Active',
                3: 'Completed',
                4: 'Canceled'
            };
            
            embed.addFields([
                {
                    name: '> Tên sự kiện',
                    value: `- ${newEvent.name}`,
                    inline: true
                },
                {
                    name: '> ID sự kiện',
                    value: `- ${newEvent.id}`,
                    inline: true
                },
                {
                    name: '> Loại sự kiện',
                    value: `- ${entityTypes[newEvent.entityType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian thay đổi',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${statusTypes[newEvent.status] || 'Unknown'}`,
                    inline: true
                }
            ]);
            
            // Add start time comparison
            const oldStartTime = oldEvent.scheduledStartTimestamp 
                ? `<t:${Math.floor(oldEvent.scheduledStartTimestamp / 1000)}:F>`
                : 'Không thiết lập';
            const newStartTime = newEvent.scheduledStartTimestamp 
                ? `<t:${Math.floor(newEvent.scheduledStartTimestamp / 1000)}:F>`
                : 'Không thiết lập';
            
            embed.addFields([
                {
                    name: '> Thời gian bắt đầu cũ',
                    value: `- ${oldStartTime}`,
                    inline: true
                },
                {
                    name: '> Thời gian bắt đầu mới',
                    value: `- ${newStartTime}`,
                    inline: true
                }
            ]);
            
            // Calculate time difference
            if (oldEvent.scheduledStartTimestamp && newEvent.scheduledStartTimestamp) {
                const timeDiff = newEvent.scheduledStartTimestamp - oldEvent.scheduledStartTimestamp;
                const diffMinutes = Math.floor(timeDiff / (1000 * 60));
                const diffHours = Math.floor(diffMinutes / 60);
                const diffDays = Math.floor(diffHours / 24);
                
                let diffText = '';
                if (Math.abs(diffDays) >= 1) {
                    diffText = `${diffDays > 0 ? '+' : ''}${diffDays} ngày`;
                } else if (Math.abs(diffHours) >= 1) {
                    diffText = `${diffHours > 0 ? '+' : ''}${diffHours} giờ`;
                } else {
                    diffText = `${diffMinutes > 0 ? '+' : ''}${diffMinutes} phút`;
                }
                
                embed.addFields({
                    name: '> Thay đổi thời gian',
                    value: `- ${diffText}`,
                    inline: true
                });
                
                // Set color based on time change
                if (timeDiff > 0) {
                    embed.setColor(0x3498db); // Blue for later
                } else {
                    embed.setColor(0xe67e22); // Orange for earlier
                }
            }
            
            // Add end time if available
            if (newEvent.scheduledEndTimestamp) {
                embed.addFields({
                    name: '> Thời gian kết thúc',
                    value: `- <t:${Math.floor(newEvent.scheduledEndTimestamp / 1000)}:F>`,
                    inline: true
                });
                
                // Calculate duration
                if (newEvent.scheduledStartTimestamp) {
                    const duration = newEvent.scheduledEndTimestamp - newEvent.scheduledStartTimestamp;
                    const durationHours = Math.floor(duration / (1000 * 60 * 60));
                    const durationMinutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
                    
                    embed.addFields({
                        name: '> Thời lượng sự kiện',
                        value: `- ${durationHours}h ${durationMinutes}m`,
                        inline: true
                    });
                }
            }
            
            // Add creator info if available
            if (newEvent.creator) {
                embed.addFields({
                    name: '> Người tạo sự kiện',
                    value: `- ${newEvent.creator.tag}`,
                    inline: true
                });
            }
            
            // Add subscriber count if available
            if (newEvent.userCount !== null) {
                embed.addFields({
                    name: '> Số người quan tâm',
                    value: `- ${newEvent.userCount} người`,
                    inline: true
                });
            }
            
            // Add impact explanation
            if (newEvent.scheduledStartTimestamp && oldEvent.scheduledStartTimestamp) {
                const timeDiff = newEvent.scheduledStartTimestamp - oldEvent.scheduledStartTimestamp;
                
                if (timeDiff > 0) {
                    embed.addFields({
                        name: '> 📅 Tác động (Dời muộn hơn)',
                        value: [
                            '• Sự kiện sẽ bắt đầu muộn hơn dự kiến',
                            '• Thành viên có thêm thời gian chuẩn bị',
                            '• Cần thông báo cho người quan tâm',
                            '• Có thể ảnh hưởng đến lịch trình khác'
                        ].join('\n'),
                        inline: false
                    });
                } else {
                    embed.addFields({
                        name: '> ⏰ Tác động (Dời sớm hơn)',
                        value: [
                            '• Sự kiện sẽ bắt đầu sớm hơn dự kiến',
                            '• Thành viên cần chuẩn bị nhanh hơn',
                            '• Cần thông báo khẩn cấp',
                            '• Có thể một số người bỏ lỡ'
                        ].join('\n'),
                        inline: false
                    });
                }
            }
            
            // Add time management tips
            embed.addFields({
                name: '> 💡 Lưu ý về thời gian sự kiện',
                value: [
                    '• Thông báo sớm khi thay đổi thời gian',
                    '• Cân nhắc múi giờ của thành viên',
                    '• Tránh thay đổi quá gần giờ diễn ra',
                    '• Kiểm tra xung đột với sự kiện khác'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set event image as thumbnail if available
            if (newEvent.coverImageURL()) {
                embed.setThumbnail(newEvent.coverImageURL({ size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newEvent.guild.name} • Event Start Time Update`,
                iconURL: newEvent.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Event start time update logged: ${newEvent.name}`);
            
        } catch (error) {
            console.error('Error in eventsStartTimeUpdate audit log:', error);
        }
    }
};
