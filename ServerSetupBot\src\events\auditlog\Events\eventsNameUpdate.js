const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildScheduledEventUpdate,
    async execute(oldEvent, newEvent, client) {
        try {
            // Only handle name changes
            if (oldEvent.name === newEvent.name) return;
            
            console.log(`✏️ Event name updated: ${oldEvent.name} -> ${newEvent.name} in ${newEvent.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newEvent.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EVENTS_NAME_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EVENTS_NAME_UPDATE',
                user: 'System',
                userId: null,
                action: 'Tên sự kiện đư<PERSON> cập nhật',
                details: `<PERSON>ên sự kiện đã được thay đổi từ **${oldEvent.name}** thành **${newEvent.name}**`,
                target: newEvent.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the event name from audit logs
            try {
                const auditLogs = await newEvent.guild.fetchAuditLogs({
                    type: 102, // GUILD_SCHEDULED_EVENT_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newEvent.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Tên sự kiện được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for event name update');
            }
            
            // Add to database
            await client.db.addAuditLog(newEvent.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Tên sự kiện được cập nhật',
                `Vừa có tên sự kiện được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get event entity type names
            const entityTypes = {
                1: 'Stage Instance',
                2: 'Voice Channel',
                3: 'External Location'
            };
            
            // Get event status names
            const statusTypes = {
                1: 'Scheduled',
                2: 'Active',
                3: 'Completed',
                4: 'Canceled'
            };
            
            embed.addFields([
                {
                    name: '> ID sự kiện',
                    value: `- ${newEvent.id}`,
                    inline: true
                },
                {
                    name: '> Loại sự kiện',
                    value: `- ${entityTypes[newEvent.entityType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${statusTypes[newEvent.status] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Tên cũ',
                    value: `- ${oldEvent.name}`,
                    inline: true
                },
                {
                    name: '> Tên mới',
                    value: `- ${newEvent.name}`,
                    inline: true
                }
            ]);
            
            // Add event timing info
            if (newEvent.scheduledStartTimestamp) {
                embed.addFields({
                    name: '> Thời gian bắt đầu',
                    value: `- <t:${Math.floor(newEvent.scheduledStartTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            if (newEvent.scheduledEndTimestamp) {
                embed.addFields({
                    name: '> Thời gian kết thúc',
                    value: `- <t:${Math.floor(newEvent.scheduledEndTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add creator info if available
            if (newEvent.creator) {
                embed.addFields({
                    name: '> Người tạo sự kiện',
                    value: `- ${newEvent.creator.tag}`,
                    inline: true
                });
            }
            
            // Add subscriber count if available
            if (newEvent.userCount !== null) {
                embed.addFields({
                    name: '> Số người quan tâm',
                    value: `- ${newEvent.userCount} người`,
                    inline: true
                });
            }
            
            // Add location info if available
            if (newEvent.entityMetadata?.location) {
                embed.addFields({
                    name: '> Địa điểm',
                    value: `- ${newEvent.entityMetadata.location}`,
                    inline: true
                });
            }
            
            // Add channel info if it's a voice/stage event
            if (newEvent.channel) {
                embed.addFields({
                    name: '> Kênh',
                    value: `- ${newEvent.channel}`,
                    inline: true
                });
            }
            
            // Add name analysis
            const nameChanges = [];
            if (oldEvent.name.length !== newEvent.name.length) {
                const lengthDiff = newEvent.name.length - oldEvent.name.length;
                nameChanges.push(`Độ dài: ${lengthDiff > 0 ? '+' : ''}${lengthDiff} ký tự`);
            }
            
            if (nameChanges.length > 0) {
                embed.addFields({
                    name: '> 📊 Thay đổi',
                    value: `- ${nameChanges.join('\n- ')}`,
                    inline: false
                });
            }
            
            // Add impact explanation
            embed.addFields({
                name: '> 📝 Tác động',
                value: [
                    '• Tên mới sẽ hiển thị trong danh sách sự kiện',
                    '• Thông báo sẽ sử dụng tên mới',
                    '• Có thể ảnh hưởng đến nhận diện sự kiện',
                    '• Người quan tâm sẽ thấy tên cập nhật'
                ].join('\n'),
                inline: false
            });
            
            // Add naming guidelines
            embed.addFields({
                name: '> 💡 Lưu ý về tên sự kiện',
                value: [
                    '• Tên nên ngắn gọn và dễ hiểu',
                    '• Thể hiện rõ nội dung sự kiện',
                    '• Tránh ký tự đặc biệt không cần thiết',
                    '• Cân nhắc kỹ trước khi thay đổi'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set event image as thumbnail if available
            if (newEvent.coverImageURL()) {
                embed.setThumbnail(newEvent.coverImageURL({ size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newEvent.guild.name} • Event Name Update`,
                iconURL: newEvent.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Event name update logged: ${oldEvent.name} -> ${newEvent.name}`);
            
        } catch (error) {
            console.error('Error in eventsNameUpdate audit log:', error);
        }
    }
};
