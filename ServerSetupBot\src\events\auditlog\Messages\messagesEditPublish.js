const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');
const auditLogDB = require('../../../utils/auditLogDatabase.js');

module.exports = {
    name: Events.MessageUpdate,
    async execute(oldMessage, newMessage, client) {
        try {
            // Skip if message is from bot or system
            if (!newMessage.author || newMessage.author.bot) return;
            
            // Skip DMs
            if (!newMessage.guild) return;
            
            // Skip if content didn't change (could be embed update, etc.)
            if (oldMessage.content === newMessage.content) return;
            
            console.log(`✏️ Message edited in ${newMessage.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newMessage.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'MESSAGE_EDIT')) return;
            
            // Create event data
            const eventData = {
                eventType: 'MESSAGE_EDIT',
                user: newMessage.author.tag,
                userId: newMessage.author.id,
                action: 'Tin nhắn được chỉnh sửa',
                details: `Tin nhắn của **${newMessage.author.tag}** đã được chỉnh sửa trong ${newMessage.channel}`,
                target: newMessage.author.tag,
                channel: newMessage.channel.name,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(newMessage.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Tin nhắn được chỉnh sửa',
                `Vừa có một tin nhắn được chỉnh sửa trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for edit
            
            // Truncate message content if too long
            let oldContent = oldMessage.content || '*Không có nội dung văn bản*';
            let newContent = newMessage.content || '*Không có nội dung văn bản*';
            
            if (oldContent.length > 500) {
                oldContent = oldContent.substring(0, 500) + '...';
            }
            if (newContent.length > 500) {
                newContent = newContent.substring(0, 500) + '...';
            }
            
            embed.addFields([
                {
                    name: '> Tác giả tin nhắn',
                    value: `- ${newMessage.author.tag} (${newMessage.author})`,
                    inline: false
                },
                {
                    name: '> Kênh',
                    value: `- ${newMessage.channel}`,
                    inline: true
                },
                {
                    name: '> ID tin nhắn',
                    value: `- ${newMessage.id}`,
                    inline: true
                },
                {
                    name: '> Link tin nhắn',
                    value: `- [Nhấn để xem](${newMessage.url})`,
                    inline: true
                },
                {
                    name: '> Nội dung cũ',
                    value: `\`\`\`${oldContent}\`\`\``,
                    inline: false
                },
                {
                    name: '> Nội dung mới',
                    value: `\`\`\`${newContent}\`\`\``,
                    inline: false
                },
                {
                    name: '> Thời gian chỉnh sửa',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newMessage.guild.name} • Message Edit`,
                iconURL: newMessage.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Message edit logged in ${newMessage.channel.name}`);
            
        } catch (error) {
            console.error('Error in messageUpdate audit log:', error);
        }
    }
};
