const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createWarningEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('warn')
        .setDescription('Cảnh báo thành viên')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Thành viên cần cảnh báo')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Lý do cảnh báo')
                .setRequired(true)
        )
        .addBooleanOption(option =>
            option.setName('silent')
                .setDescription('Không gửi DM cho người bị cảnh báo')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.ModerateMembers)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                'Bạn cần quyền `Moderate Members` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const user = interaction.options.getUser('user');
        const reason = interaction.options.getString('reason');
        const silent = interaction.options.getBoolean('silent') || false;
        const guildId = interaction.guild.id;
        
        try {
            const member = await interaction.guild.members.fetch(user.id).catch(() => null);
            
            // Kiểm tra hierarchy nếu member tồn tại
            if (member) {
                if (member.roles.highest.position >= interaction.member.roles.highest.position) {
                    const errorEmbed = createErrorEmbed(
                        'Không thể cảnh báo!',
                        'Bạn không thể cảnh báo thành viên có role cao hơn hoặc bằng bạn.'
                    );
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
            }
            
            // Thêm warning vào database
            const warningResult = await client.db.addWarning(guildId, user.id, interaction.user.id, reason);
            
            // Lấy số lượng warning hiện tại
            const warningCount = await client.db.getWarningCount(guildId, user.id);
            
            // Log vào moderation logs
            await client.db.addModerationLog(guildId, {
                type: 'warn',
                userId: user.id,
                moderatorId: interaction.user.id,
                reason: reason,
                warningId: warningResult.id
            });
            
            // Gửi DM cho user
            if (!silent) {
                const dmEmbed = createWarningEmbed(
                    `Bạn đã nhận cảnh báo trong ${interaction.guild.name}`,
                    `**Lý do:** ${reason}\n**Bởi:** ${interaction.user.tag}\n**Tổng cảnh báo:** ${warningCount}\n**Thời gian:** <t:${Math.floor(Date.now() / 1000)}:F>`
                );
                
                try {
                    await user.send({ embeds: [dmEmbed] });
                } catch (error) {
                    console.log('Không thể gửi DM cho user bị cảnh báo');
                }
            }
            
            const successEmbed = createSuccessEmbed(
                '⚠️ Thành viên đã bị cảnh báo!',
                `**Thành viên:** ${user.tag} (${user.id})\n` +
                `**Lý do:** ${reason}\n` +
                `**Tổng cảnh báo:** ${warningCount}\n` +
                `**ID Cảnh báo:** ${warningResult.id}\n` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            // Kiểm tra auto actions
            const moderationConfig = await client.db.getModerationConfig(guildId);
            if (moderationConfig) {
                let autoAction = '';
                
                if (moderationConfig.auto_ban_threshold && warningCount >= moderationConfig.auto_ban_threshold) {
                    // Auto ban
                    try {
                        if (member && member.bannable) {
                            await member.ban({ reason: `Đạt ${warningCount} cảnh báo | Auto ban` });
                            autoAction = `\n\n🔨 **Auto Ban:** Thành viên đã bị ban tự động do đạt ${warningCount} cảnh báo.`;
                            
                            await client.db.addModerationLog(guildId, {
                                type: 'ban',
                                userId: user.id,
                                moderatorId: client.user.id,
                                reason: `Auto ban - Đạt ${warningCount} cảnh báo`,
                                duration: null
                            });
                        }
                    } catch (error) {
                        console.error('Lỗi auto ban:', error);
                    }
                } else if (moderationConfig.warning_threshold && warningCount >= moderationConfig.warning_threshold) {
                    // Auto kick
                    try {
                        if (member && member.kickable) {
                            await member.kick(`Đạt ${warningCount} cảnh báo | Auto kick`);
                            autoAction = `\n\n👢 **Auto Kick:** Thành viên đã bị kick tự động do đạt ${warningCount} cảnh báo.`;
                            
                            await client.db.addModerationLog(guildId, {
                                type: 'kick',
                                userId: user.id,
                                moderatorId: client.user.id,
                                reason: `Auto kick - Đạt ${warningCount} cảnh báo`,
                                duration: null
                            });
                        }
                    } catch (error) {
                        console.error('Lỗi auto kick:', error);
                    }
                }
                
                if (autoAction) {
                    successEmbed.setDescription(successEmbed.data.description + autoAction);
                }
            }
            
            await interaction.reply({ embeds: [successEmbed] });
            
            // Gửi log vào channel
            await sendModerationLog(client, guildId, {
                type: 'warn',
                user: user,
                moderator: interaction.user,
                reason: reason,
                warningCount: warningCount
            });
            
        } catch (error) {
            console.error('Lỗi khi cảnh báo user:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi cảnh báo thành viên!',
                'Đã xảy ra lỗi khi cảnh báo thành viên. Vui lòng thử lại sau!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
