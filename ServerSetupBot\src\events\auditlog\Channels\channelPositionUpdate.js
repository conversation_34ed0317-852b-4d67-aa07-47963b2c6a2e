const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle position changes
            if (oldChannel.position === newChannel.position) return;
            
            console.log(`📍 Channel position updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_POSITION_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_POSITION_UPDATE',
                user: 'System',
                userId: null,
                action: '<PERSON><PERSON> trí kênh được cập nhật',
                details: `<PERSON>ị trí của kênh **${newChannel.name}** đ<PERSON> được thay đổi từ ${oldChannel.position} thành ${newChannel.position}`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the channel position from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Vị trí kênh được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for channel position update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📍 Vị trí kênh được cập nhật',
                `Vừa có vị trí kênh được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get channel type names
            const channelTypes = {
                0: 'Kênh văn bản',
                2: 'Kênh thoại',
                4: 'Danh mục',
                5: 'Kênh tin tức',
                10: 'Diễn đàn tin tức',
                11: 'Diễn đàn công khai',
                12: 'Diễn đàn riêng tư',
                13: 'Kênh sân khấu',
                15: 'Forum',
                16: 'Media'
            };
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Loại kênh',
                    value: `- ${channelTypes[newChannel.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Vị trí cũ',
                    value: `- ${oldChannel.position}`,
                    inline: true
                },
                {
                    name: '> Vị trí mới',
                    value: `- ${newChannel.position}`,
                    inline: true
                }
            ]);
            
            // Add parent category info if available
            if (newChannel.parent) {
                embed.addFields({
                    name: '> Danh mục',
                    value: `- ${newChannel.parent.name}`,
                    inline: true
                });
            }
            
            // Calculate position change
            const positionChange = newChannel.position - oldChannel.position;
            const direction = positionChange > 0 ? 'xuống' : 'lên';
            const changeAmount = Math.abs(positionChange);
            
            embed.addFields({
                name: '> Thay đổi vị trí',
                value: `- Di chuyển ${direction} ${changeAmount} vị trí`,
                inline: true
            });
            
            // Add impact explanation
            if (positionChange > 0) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Kênh di chuyển xuống trong danh sách',
                        '• Hiển thị thấp hơn so với trước',
                        '• Có thể ít được chú ý hơn',
                        '• Thứ tự sắp xếp thay đổi'
                    ].join('\n'),
                    inline: false
                });
            } else if (positionChange < 0) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Kênh di chuyển lên trong danh sách',
                        '• Hiển thị cao hơn so với trước',
                        '• Có thể được chú ý nhiều hơn',
                        '• Thứ tự sắp xếp thay đổi'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add position context
            const totalChannels = newChannel.guild.channels.cache.filter(ch => ch.type === newChannel.type && ch.parentId === newChannel.parentId).size;
            embed.addFields({
                name: '> Vị trí trong danh mục',
                value: `- ${newChannel.position + 1}/${totalChannels} kênh cùng loại`,
                inline: true
            });
            
            // Add organization note
            embed.addFields({
                name: '> 💡 Lưu ý về vị trí kênh',
                value: [
                    '• Vị trí ảnh hưởng đến thứ tự hiển thị',
                    '• Kênh ở trên thường được xem nhiều hơn',
                    '• Có thể sắp xếp theo mức độ quan trọng',
                    '• Thay đổi vị trí không ảnh hưởng đến permissions'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Position Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel position update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelPositionUpdate audit log:', error);
        }
    }
};
