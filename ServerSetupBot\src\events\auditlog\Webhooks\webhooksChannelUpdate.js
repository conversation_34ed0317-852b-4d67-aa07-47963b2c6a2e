const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'webhookUpdate', // Custom event name
    async execute(oldWebhook, newWebhook, client) {
        try {
            // Only handle channel changes
            if (oldWebhook.channelId === newWebhook.channelId) return;
            
            console.log(`📍 Webhook channel updated: ${newWebhook.name} in ${newWebhook.guild?.name || 'Unknown'}`);
            
            // Skip if no guild (DM webhooks)
            if (!newWebhook.guild) return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newWebhook.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'WEBHOOKS_CHANNEL_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'WEBHOOKS_CHANNEL_UPDATE',
                user: 'System',
                userId: null,
                action: 'Kênh webhook được cập nhật',
                details: `Kênh của webhook **${newWebhook.name}** đã được thay đổi`,
                target: newWebhook.name,
                channel: newWebhook.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the webhook channel from audit logs
            try {
                const auditLogs = await newWebhook.guild.fetchAuditLogs({
                    type: 51, // WEBHOOK_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newWebhook.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Kênh webhook được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for webhook channel update');
            }
            
            // Add to database
            await client.db.addAuditLog(newWebhook.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📍 Kênh webhook được cập nhật',
                `Vừa có kênh webhook được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get webhook type names
            const webhookTypes = {
                1: 'Incoming Webhook',
                2: 'Channel Follower Webhook',
                3: 'Application Webhook'
            };
            
            // Get old and new channel info
            const oldChannel = oldWebhook.channel;
            const newChannel = newWebhook.channel;
            
            embed.addFields([
                {
                    name: '> Webhook',
                    value: `- ${newWebhook.name || 'Unnamed Webhook'}`,
                    inline: true
                },
                {
                    name: '> ID webhook',
                    value: `- ${newWebhook.id}`,
                    inline: true
                },
                {
                    name: '> Loại webhook',
                    value: `- ${webhookTypes[newWebhook.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh cũ',
                    value: `- ${oldChannel ? `${oldChannel} (${oldChannel.name})` : 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh mới',
                    value: `- ${newChannel ? `${newChannel} (${newChannel.name})` : 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add channel type information
            if (oldChannel && newChannel) {
                const channelTypes = {
                    0: 'Kênh văn bản',
                    2: 'Kênh thoại',
                    4: 'Danh mục',
                    5: 'Kênh tin tức',
                    10: 'Diễn đàn tin tức',
                    11: 'Diễn đàn công khai',
                    12: 'Diễn đàn riêng tư',
                    13: 'Kênh sân khấu',
                    15: 'Forum',
                    16: 'Media'
                };
                
                embed.addFields([
                    {
                        name: '> Loại kênh cũ',
                        value: `- ${channelTypes[oldChannel.type] || 'Unknown'}`,
                        inline: true
                    },
                    {
                        name: '> Loại kênh mới',
                        value: `- ${channelTypes[newChannel.type] || 'Unknown'}`,
                        inline: true
                    }
                ]);
            }
            
            // Add webhook owner info if available
            if (newWebhook.owner) {
                embed.addFields({
                    name: '> Chủ sở hữu',
                    value: `- ${newWebhook.owner.tag || newWebhook.owner.name}`,
                    inline: true
                });
            }
            
            // Add application info if it's an application webhook
            if (newWebhook.application) {
                embed.addFields({
                    name: '> Ứng dụng',
                    value: `- ${newWebhook.application.name}`,
                    inline: true
                });
            }
            
            // Add creation info if available
            if (newWebhook.createdTimestamp) {
                embed.addFields({
                    name: '> Được tạo lúc',
                    value: `- <t:${Math.floor(newWebhook.createdTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add impact information
            embed.addFields({
                name: '> 📝 Tác động',
                value: [
                    '• Tin nhắn từ webhook sẽ gửi đến kênh mới',
                    '• URL webhook vẫn giữ nguyên',
                    '• Cần cập nhật automation nếu có',
                    '• Permissions có thể khác giữa các kênh'
                ].join('\n'),
                inline: false
            });
            
            // Add permission warning if applicable
            if (newChannel) {
                const botMember = newWebhook.guild.members.me;
                if (botMember && !newChannel.permissionsFor(botMember).has('ManageWebhooks')) {
                    embed.addFields({
                        name: '⚠️ Cảnh báo',
                        value: `- Bot có thể không có quyền quản lý webhook trong kênh mới`,
                        inline: false
                    });
                }
            }
            
            // Add webhook URL info (partially hidden for security)
            if (newWebhook.url) {
                const hiddenUrl = `https://discord.com/api/webhooks/${newWebhook.id}/***`;
                embed.addFields({
                    name: '> URL webhook',
                    value: `- ${hiddenUrl}`,
                    inline: false
                });
            }
            
            // Add usage note
            embed.addFields({
                name: '> 💡 Lưu ý',
                value: [
                    '• Webhook chỉ có thể gửi tin nhắn đến kênh được chỉ định',
                    '• Thay đổi kênh không ảnh hưởng đến URL webhook',
                    '• Cần kiểm tra permissions trong kênh mới',
                    '• Automation scripts có thể cần cập nhật'
                ].join('\n'),
                inline: false
            });
            
            // Add security note
            embed.addFields({
                name: '> 🔒 Bảo mật',
                value: `- Đảm bảo kênh mới có permissions phù hợp cho webhook`,
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set webhook avatar as thumbnail if available
            if (newWebhook.avatarURL()) {
                embed.setThumbnail(newWebhook.avatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newWebhook.guild.name} • Webhook Channel Update`,
                iconURL: newWebhook.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Webhook channel update logged: ${newWebhook.name}`);
            
        } catch (error) {
            console.error('Error in webhooksChannelUpdate audit log:', error);
        }
    }
};
