const fs = require('fs');
const path = require('path');
const ms = require('ms');
const { EmbedBuilder } = require('discord.js');

const filePath = path.resolve(__dirname, '../../data/giveaway.json');

module.exports = {
  name: 'gstart',
  description: 'Start a giveaway',

  async execute(message, args) {
    const duration = args[0];
    const winnersCount = parseInt(args[1]);
    const prize = args.slice(2).join(' ');

    if (!duration || !winnersCount || !prize) return message.reply('Usage: `gstart <duration> <winners> <prize>`');

    const endTime = Date.now() + ms(duration);
    const embed = new EmbedBuilder()
      .setTitle(prize)
      .setDescription(`React with 🎉 to enter!\nWinners: **${winnersCount}**\nHosted by: ${message.author}`)
      .setFooter({ text: `Ends in ${duration}` })
      .setTimestamp(endTime)
      .setColor('Random');

    const giveawayMsg = await message.channel.send({ embeds: [embed] });
    await giveawayMsg.react('🎉');

    const giveaways = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    giveaways.push({
      messageId: giveawayMsg.id,
      channelId: message.channel.id,
      guildId: message.guild.id,
      prize,
      winners: winnersCount,
      endTime,
      host: message.author.id,
      ended: false
    });

    fs.writeFileSync(filePath, JSON.stringify(giveaways, null, 2));
  }
};