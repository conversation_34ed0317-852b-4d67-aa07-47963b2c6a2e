const { SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder } = require('discord.js');
const { createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { getModerationEmoji, formatDuration } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('modlogs')
        .setDescription('Xem lịch sử moderation của thành viên')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Thành viên cần xem lịch sử')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('type')
                .setDescription('Loại hành động moderation')
                .setRequired(false)
                .addChoices(
                    { name: 'Tất cả', value: 'all' },
                    { name: 'Ban', value: 'ban' },
                    { name: 'Kick', value: 'kick' },
                    { name: 'Timeout', value: 'timeout' },
                    { name: 'Warn', value: 'warn' },
                    { name: 'Mute', value: 'mute' },
                    { name: 'Purge', value: 'purge' }
                )
        )
        .addIntegerOption(option =>
            option.setName('days')
                .setDescription('Số ngày gần đây (1-365)')
                .setRequired(false)
                .setMinValue(1)
                .setMaxValue(365)
        )
        .addIntegerOption(option =>
            option.setName('page')
                .setDescription('Trang cần xem (mặc định: 1)')
                .setRequired(false)
                .setMinValue(1)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.ModerateMembers)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                'Bạn cần quyền `Moderate Members` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const user = interaction.options.getUser('user');
        const type = interaction.options.getString('type') || 'all';
        const days = interaction.options.getInteger('days') || 30;
        const page = interaction.options.getInteger('page') || 1;
        const guildId = interaction.guild.id;
        
        const itemsPerPage = 10;
        const offset = (page - 1) * itemsPerPage;
        
        try {
            // Lấy moderation logs
            const options = {
                userId: user.id,
                days: days,
                limit: itemsPerPage + 1, // +1 để check có trang tiếp theo không
                offset: offset
            };
            
            if (type !== 'all') {
                options.type = type;
            }
            
            const logs = await client.db.getModerationLogs(guildId, options);
            const hasNextPage = logs.length > itemsPerPage;
            const displayLogs = logs.slice(0, itemsPerPage);
            
            // Lấy tổng số logs để tính tổng số trang
            const totalLogsOptions = { userId: user.id, days: days };
            if (type !== 'all') {
                totalLogsOptions.type = type;
            }
            const totalLogs = await client.db.getModerationLogs(guildId, totalLogsOptions);
            const totalPages = Math.ceil(totalLogs.length / itemsPerPage);
            
            const embed = new EmbedBuilder()
                .setColor(0xff6b6b)
                .setTitle(`📋 Lịch sử Moderation - ${user.tag}`)
                .setThumbnail(user.displayAvatarURL({ dynamic: true }))
                .setTimestamp()
                .setFooter({ 
                    text: `Trang ${page}/${totalPages} • Yêu cầu bởi ${interaction.user.tag}`, 
                    iconURL: interaction.user.displayAvatarURL() 
                });
            
            if (displayLogs.length === 0) {
                embed.setDescription(
                    `Không tìm thấy lịch sử moderation nào cho ${user.tag} trong ${days} ngày qua` +
                    (type !== 'all' ? ` với loại "${type}"` : '') + '.'
                );
                embed.setColor(0x00ff00);
            } else {
                // Thống kê tổng quan
                const stats = {};
                totalLogs.forEach(log => {
                    stats[log.type] = (stats[log.type] || 0) + 1;
                });
                
                const statsText = Object.entries(stats)
                    .map(([logType, count]) => `${getModerationEmoji(logType)} **${logType}:** ${count}`)
                    .join('\n');
                
                embed.addFields({
                    name: `📊 Thống kê ${days} ngày qua`,
                    value: statsText || 'Không có dữ liệu',
                    inline: true
                });
                
                embed.addFields({
                    name: '🔍 Bộ lọc',
                    value: `**Loại:** ${type === 'all' ? 'Tất cả' : type}\n**Thời gian:** ${days} ngày\n**Tổng:** ${totalLogs.length} logs`,
                    inline: true
                });
                
                // Hiển thị logs
                const logsList = displayLogs.map((log, index) => {
                    const date = new Date(log.created_at);
                    const relativeTime = `<t:${Math.floor(date.getTime() / 1000)}:R>`;
                    const moderator = `<@${log.moderator_id}>`;
                    
                    let logText = `**${offset + index + 1}.** ${getModerationEmoji(log.type)} **${log.type.toUpperCase()}** ${relativeTime}\n`;
                    logText += `   👮 ${moderator} | 📝 ${log.reason}`;
                    
                    if (log.duration && log.type !== 'purge') {
                        logText += `\n   ⏰ ${formatDuration(log.duration)}`;
                    }
                    
                    if (log.type === 'ban' && log.delete_days) {
                        logText += `\n   🗑️ Xóa ${log.delete_days} ngày tin nhắn`;
                    }
                    
                    if (log.type === 'purge' && log.duration) {
                        logText += `\n   📊 ${log.duration} tin nhắn`;
                    }
                    
                    if (log.expires_at) {
                        const expiresTime = `<t:${Math.floor(new Date(log.expires_at).getTime() / 1000)}:F>`;
                        logText += `\n   ⏳ Hết hạn: ${expiresTime}`;
                    }
                    
                    return logText;
                }).join('\n\n');
                
                embed.addFields({
                    name: `📜 Lịch sử (${displayLogs.length}/${totalLogs.length})`,
                    value: logsList,
                    inline: false
                });
                
                // Navigation
                if (totalPages > 1) {
                    let navigation = '';
                    if (page > 1) {
                        navigation += `◀️ Trang trước: \`/modlogs user:${user.tag} page:${page - 1}\`\n`;
                    }
                    if (hasNextPage) {
                        navigation += `▶️ Trang sau: \`/modlogs user:${user.tag} page:${page + 1}\``;
                    }
                    
                    if (navigation) {
                        embed.addFields({
                            name: '📄 Điều hướng',
                            value: navigation,
                            inline: false
                        });
                    }
                }
            }
            
            // Thêm thông tin về warnings hiện tại
            const activeWarnings = await client.db.getWarnings(guildId, user.id, true);
            if (activeWarnings.length > 0) {
                embed.addFields({
                    name: '⚠️ Cảnh báo hiện tại',
                    value: `${activeWarnings.length} cảnh báo đang hoạt động`,
                    inline: true
                });
            }
            
            // Thêm thông tin về mute/timeout hiện tại
            const currentMute = await client.db.getMute(guildId, user.id);
            if (currentMute && currentMute.active) {
                const muteText = currentMute.expires_at 
                    ? `Hết hạn: <t:${Math.floor(new Date(currentMute.expires_at).getTime() / 1000)}:R>`
                    : 'Vĩnh viễn';
                embed.addFields({
                    name: '🔇 Trạng thái mute',
                    value: muteText,
                    inline: true
                });
            }
            
            const member = await interaction.guild.members.fetch(user.id).catch(() => null);
            if (member && member.communicationDisabledUntil && member.communicationDisabledUntil > Date.now()) {
                embed.addFields({
                    name: '⏰ Trạng thái timeout',
                    value: `Hết hạn: <t:${Math.floor(member.communicationDisabledUntil / 1000)}:R>`,
                    inline: true
                });
            }
            
            await interaction.reply({ embeds: [embed] });
            
        } catch (error) {
            console.error('Lỗi khi xem moderation logs:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi xem lịch sử!',
                'Đã xảy ra lỗi khi lấy lịch sử moderation. Vui lòng thử lại sau!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
