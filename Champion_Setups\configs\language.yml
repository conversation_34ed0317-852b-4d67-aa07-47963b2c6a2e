# ________________________________________________
#
#         CHAMPION SETUPS BOT LANGUAGE FILE
#
# Here you can customise <PERSON><PERSON>'s Messages.
#
# ________________________________________________

general:
  no_perm: "You don't have permission to perform this command."
  # Valid Placeholders: <role>
  no_role: "You need <role> Role to use this command."
  usage: "Invalid Usage for Command. Usage: `<usage>`"
  cooldown: "You're on cooldown on that command for <cooldown>"
  cmd_disabled: "This command is disabled on this server."
  invalid_bot: "You have entered Invalid Bot Name."
  not_number: "You need to provide Number." 
  invalid_category: "Channel need to be category."
  channel_category: "Channel cannot be Category."
  getid: "ID of <type> is `<id>`."
  ping_loading: "Ping.."
  ping: "Bot Uptime is <uptime>, bot ping <bot>ms, api ping <api>ms"
  invalid_cmd: "You have provided Invalid Command."
  no_desc: "You need to provide embed description."
  transcript_start: "Last 100 messages from <channel> is being saved, please wait.."
  transcript_end: "Last 100 messages from <channel> have been saved."
titles:
  help: "🚀 · Help Menu"
  error: "Error"
setup:
  buttons:
    create_all: "Create Whole Server"
    create_roles: "Create Roles"
    create_channels: "Create Channels"
    create_emojis: "Create Emojis"
  no_template: "You didn't provide config template"
  invalid_template: "You have provided Invalid Template."
  creating_all: "Started Creation of Whole Server. Roles will be created first. Please be patient."
  creating_channels: "Started Creation of Channels. Please be patient."
  creating_roles: "Started Creation of Roles. Please be patient."
  creating_emojis: "Started Creation of Emojis. Please be patient."
  created_all: "Server Creation has finished and Server is now ready"
  created_channels: "All Channels have been created."
  created_roles: "All Roles have been created."
  created_emojis: "All Emojis have been uploaded."
  server_saving: "Server have started saving, please be patient. If file with same name already exist it'll be overwritten"
  server_saved: "Server have been saved successfully `(configs/templates/<name>)`"
delete:
  buttons:
    delete_all: "Delete Everything"
    delete_roles: "Delete Roles"
    delete_channels: "Delete Channels"
    delete_emojis: "Delete Emojis"
  deleting_all: "Deletation of whole Server has started. All Channels, Roles & Emojis will be erased."
  deleting_channels: "Started Deletation of Channels. Please be patient."
  deleting_roles: "Started Deletation of Roles. Please be patient."
  deleting_emojis: "Started Deletation of Emojis. Please be patient."
  deleted_all: "Server Deletation has finished and Server is now empty."
  deleted_channels: "All Channels have been deleted."
  deleted_roles: "All Roles have been deleted."
  deleted_emojis: "All Emojis have been deleted."
edit:
  name: "Channel name have been changed to <name>"
  position: "Position of Channel <channel> have been changed to <position>."
  topic: "Topic of Channel <channel> have been changed to <topic>."
  parent: "Parent of Channel <channel> have been changed to <parent>."