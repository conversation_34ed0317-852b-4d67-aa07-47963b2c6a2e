const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ThreadDelete,
    async execute(thread, client) {
        try {
            console.log(`🗑️ Thread deleted: ${thread.name} in ${thread.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(thread.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'THREAD_DELETE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'THREAD_DELETE',
                user: 'System',
                userId: null,
                action: 'Thread được xóa',
                details: `Thread **${thread.name}** đã được xóa`,
                target: thread.name,
                channel: thread.parent?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the thread from audit logs
            try {
                const auditLogs = await thread.guild.fetchAuditLogs({
                    type: 12, // THREAD_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === thread.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Thread được xóa bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for thread delete');
            }
            
            // Add to database
            await client.db.addAuditLog(thread.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Thread được xóa',
                `Vừa có một thread được xóa khỏi server`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            // Get thread type names
            const threadTypes = {
                10: 'Announcement Thread',
                11: 'Public Thread',
                12: 'Private Thread'
            };
            
            embed.addFields([
                {
                    name: '> Tên thread',
                    value: `- ${thread.name}`,
                    inline: true
                },
                {
                    name: '> ID thread',
                    value: `- ${thread.id}`,
                    inline: true
                },
                {
                    name: '> Loại thread',
                    value: `- ${threadTypes[thread.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh cha',
                    value: `- ${thread.parent?.name || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian xóa',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add thread owner info if available
            if (thread.ownerId) {
                try {
                    const owner = await thread.guild.members.fetch(thread.ownerId);
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- ${owner.user.tag}`,
                        inline: true
                    });
                } catch (error) {
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- Unknown (ID: ${thread.ownerId})`,
                        inline: true
                    });
                }
            }
            
            // Add thread stats
            embed.addFields([
                {
                    name: '> Số tin nhắn',
                    value: `- ${thread.messageCount || 0} tin nhắn`,
                    inline: true
                },
                {
                    name: '> Số thành viên',
                    value: `- ${thread.memberCount || 0} thành viên`,
                    inline: true
                }
            ]);
            
            // Add thread settings that were configured
            embed.addFields([
                {
                    name: '> Auto archive đã thiết lập',
                    value: `- ${thread.autoArchiveDuration} phút`,
                    inline: true
                },
                {
                    name: '> Slow mode đã thiết lập',
                    value: `- ${thread.rateLimitPerUser || 0} giây`,
                    inline: true
                },
                {
                    name: '> Trạng thái trước khi xóa',
                    value: `- ${thread.archived ? '📁 Archived' : '🔓 Active'}`,
                    inline: true
                }
            ]);
            
            // Add creation and archive times if available
            if (thread.createdTimestamp) {
                embed.addFields({
                    name: '> Được tạo lúc',
                    value: `- <t:${Math.floor(thread.createdTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            if (thread.archiveTimestamp) {
                embed.addFields({
                    name: '> Được archive lúc',
                    value: `- <t:${Math.floor(thread.archiveTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do xóa',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${thread.guild.name} • Thread Delete`,
                iconURL: thread.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Thread delete logged: ${thread.name}`);
            
        } catch (error) {
            console.error('Error in threadDelete audit log:', error);
        }
    }
};
