const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildMemberUpdate,
    async execute(oldMember, newMember, client) {
        try {
            // Only handle avatar changes (server-specific avatar)
            if (oldMember.avatar === newMember.avatar) return;
            
            console.log(`🖼️ Member avatar updated: ${newMember.user.tag} in ${newMember.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newMember.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'USER_AVATAR_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'USER_AVATAR_UPDATE',
                user: newMember.user.tag,
                userId: newMember.user.id,
                action: 'Avatar server thành viên đ<PERSON> cập nhật',
                details: `**${newMember.user.tag}** đã cập nhật avatar server`,
                target: newMember.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(newMember.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🖼️ Avatar server thành viên cập nhật',
                `Vừa có thành viên cập nhật avatar server`
            );
            
            embed.setColor(0x3498db); // Blue for avatar update
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${newMember.user.tag} (${newMember.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newMember.user.id}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add avatar URLs
            const oldAvatarURL = oldMember.avatar 
                ? `https://cdn.discordapp.com/guilds/${newMember.guild.id}/users/${newMember.user.id}/avatars/${oldMember.avatar}.png?size=256`
                : newMember.user.displayAvatarURL({ dynamic: true, size: 256 });
                
            const newAvatarURL = newMember.avatar 
                ? `https://cdn.discordapp.com/guilds/${newMember.guild.id}/users/${newMember.user.id}/avatars/${newMember.avatar}.png?size=256`
                : newMember.user.displayAvatarURL({ dynamic: true, size: 256 });
            
            embed.addFields([
                {
                    name: '> Avatar cũ',
                    value: `- [Xem avatar cũ](${oldAvatarURL})`,
                    inline: true
                },
                {
                    name: '> Avatar mới',
                    value: `- [Xem avatar mới](${newAvatarURL})`,
                    inline: true
                }
            ]);
            
            // Determine avatar type
            if (newMember.avatar && oldMember.avatar) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Thay đổi avatar server`,
                    inline: true
                });
            } else if (newMember.avatar && !oldMember.avatar) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Thiết lập avatar server mới`,
                    inline: true
                });
                embed.setColor(0x2ecc71); // Green for new avatar
            } else if (!newMember.avatar && oldMember.avatar) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Xóa avatar server (dùng avatar global)`,
                    inline: true
                });
                embed.setColor(0xe74c3c); // Red for removed avatar
            }
            
            // Add account info
            const accountAge = Date.now() - newMember.user.createdTimestamp;
            const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
            
            embed.addFields([
                {
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                },
                {
                    name: '> Tham gia server lúc',
                    value: `- <t:${Math.floor(newMember.joinedTimestamp / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add roles info
            const roleCount = newMember.roles.cache.size - 1; // -1 for @everyone
            if (roleCount > 0) {
                embed.addFields({
                    name: '> Số roles',
                    value: `- ${roleCount} roles`,
                    inline: true
                });
            }
            
            // Set new avatar as thumbnail
            embed.setThumbnail(newAvatarURL);
            
            // Add note about server-specific avatars
            embed.addFields({
                name: '> Lưu ý',
                value: `- Đây là avatar riêng cho server này, khác với avatar global`,
                inline: false
            });
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newMember.guild.name} • User Avatar Update`,
                iconURL: newMember.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ User avatar update logged: ${newMember.user.tag}`);
            
        } catch (error) {
            console.error('Error in userAvatarUpdate audit log:', error);
        }
    }
};
