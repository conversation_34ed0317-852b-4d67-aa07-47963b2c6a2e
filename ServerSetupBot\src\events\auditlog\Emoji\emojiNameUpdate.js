const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildEmojiUpdate,
    async execute(oldEmoji, newEmoji, client) {
        try {
            // Only handle name changes
            if (oldEmoji.name === newEmoji.name) return;
            
            console.log(`✏️ Emoji name updated: ${oldEmoji.name} -> ${newEmoji.name} in ${newEmoji.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newEmoji.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EMOJI_NAME_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EMOJI_NAME_UPDATE',
                user: 'System',
                userId: null,
                action: 'Tên emoji được cập nhật',
                details: `Tên emoji đã được thay đổi từ **${oldEmoji.name}** thành **${newEmoji.name}**`,
                target: newEmoji.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the emoji name from audit logs
            try {
                const auditLogs = await newEmoji.guild.fetchAuditLogs({
                    type: 61, // EMOJI_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newEmoji.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Tên emoji được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for emoji name update');
            }
            
            // Add to database
            await client.db.addAuditLog(newEmoji.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Tên emoji được cập nhật',
                `Vừa có tên emoji được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> ID emoji',
                    value: `- ${newEmoji.id}`,
                    inline: true
                },
                {
                    name: '> Tên cũ',
                    value: `- :${oldEmoji.name}:`,
                    inline: true
                },
                {
                    name: '> Tên mới',
                    value: `- :${newEmoji.name}:`,
                    inline: true
                },
                {
                    name: '> Emoji hiển thị',
                    value: `- ${newEmoji}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add emoji type info
            embed.addFields([
                {
                    name: '> Loại emoji',
                    value: `- ${newEmoji.animated ? 'Animated' : 'Static'}`,
                    inline: true
                },
                {
                    name: '> Có thể sử dụng',
                    value: `- ${newEmoji.available ? 'Có' : 'Không'}`,
                    inline: true
                },
                {
                    name: '> Được quản lý',
                    value: `- ${newEmoji.managed ? 'Có (bởi app)' : 'Không'}`,
                    inline: true
                }
            ]);
            
            // Add roles restriction info if any
            if (newEmoji.roles.cache.size > 0) {
                const rolesList = newEmoji.roles.cache.map(role => role.name).join(', ');
                embed.addFields({
                    name: '> Giới hạn roles',
                    value: `- ${rolesList}`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> Giới hạn roles',
                    value: `- Tất cả thành viên có thể sử dụng`,
                    inline: false
                });
            }
            
            // Add emoji URL
            embed.addFields({
                name: '> URL emoji',
                value: `- [Xem emoji](${newEmoji.url})`,
                inline: true
            });
            
            // Add creation info
            if (newEmoji.createdTimestamp) {
                embed.addFields({
                    name: '> Được tạo lúc',
                    value: `- <t:${Math.floor(newEmoji.createdTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set emoji as thumbnail
            embed.setThumbnail(newEmoji.url);
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newEmoji.guild.name} • Emoji Name Update`,
                iconURL: newEmoji.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Emoji name update logged: ${oldEmoji.name} -> ${newEmoji.name}`);
            
        } catch (error) {
            console.error('Error in emojiNameUpdate audit log:', error);
        }
    }
};
