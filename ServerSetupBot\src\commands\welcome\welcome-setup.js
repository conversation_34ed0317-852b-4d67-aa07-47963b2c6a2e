const { SlashCommandBuilder, ChannelType } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { canSendMessages } = require('../../utils/permissions.js');
const { validateWelcomeMessage } = require('../../utils/validators.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('welcome-setup')
        .setDescription('Thiết lập hệ thống chào mừng thành viên mới')
        .addSubcommand(subcommand =>
            subcommand
                .setName('enable')
                .setDescription('Bật hệ thống chào mừng')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Kênh gửi tin nhắn chào mừng')
                        .setRequired(true)
                        .addChannelTypes(ChannelType.GuildText)
                )
                .addStringOption(option =>
                    option.setName('message')
                        .setDescription('Tin nhắn chào mừng (sử dụng {user}, {username}, {server}, {membercount})')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('embed')
                        .setDescription('Sử dụng embed cho tin nhắn (mặc định: true)')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('dm')
                        .setDescription('Gửi tin nhắn chào mừng riêng tư (mặc định: false)')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('disable')
                .setDescription('Tắt hệ thống chào mừng')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('test')
                .setDescription('Kiểm tra tin nhắn chào mừng')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('config')
                .setDescription('Xem cấu hình hiện tại của hệ thống chào mừng')
        ),
    category: 'welcome',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();
        const guildId = interaction.guild.id;
        
        switch (subcommand) {
            case 'enable':
                await handleEnable(interaction, client, guildId);
                break;
            case 'disable':
                await handleDisable(interaction, client, guildId);
                break;
            case 'test':
                await handleTest(interaction, client, guildId);
                break;
            case 'config':
                await handleConfig(interaction, client, guildId);
                break;
        }
    },
};

async function handleEnable(interaction, client, guildId) {
    const channel = interaction.options.getChannel('channel');
    const customMessage = interaction.options.getString('message');
    const useEmbed = interaction.options.getBoolean('embed') ?? true;
    const dmWelcome = interaction.options.getBoolean('dm') ?? false;
    
    // Kiểm tra quyền bot trong channel
    const canSend = canSendMessages(channel);
    if (!canSend.canSend) {
        const errorEmbed = createErrorEmbed(
            'Lỗi quyền hạn!',
            `${canSend.reason}\n\nVui lòng cấp quyền cho bot hoặc chọn kênh khác.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    // Validate custom message nếu có
    if (customMessage) {
        const validation = validateWelcomeMessage(customMessage);
        if (!validation.isValid) {
            const errorEmbed = createErrorEmbed(
                'Tin nhắn không hợp lệ!',
                `**Lỗi:**\n${validation.errors.join('\n')}\n\n` +
                `**Placeholder hợp lệ:**\n${validation.validPlaceholders.map(p => `\`${p}\``).join(', ')}`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    }
    
    try {
        // Cập nhật cấu hình welcome
        await client.db.updateWelcomeConfig(guildId, {
            welcome_enabled: 1,
            welcome_channel_id: channel.id,
            welcome_message: customMessage,
            welcome_embed: useEmbed ? 1 : 0,
            dm_welcome: dmWelcome ? 1 : 0,
            dm_welcome_message: dmWelcome ? (customMessage || null) : null
        });
        
        const successEmbed = createSuccessEmbed(
            'Hệ thống chào mừng đã được bật!',
            `**Kênh:** ${channel}\n` +
            `**Định dạng:** ${useEmbed ? 'Embed' : 'Tin nhắn thường'}\n` +
            `**Tin nhắn riêng tư:** ${dmWelcome ? 'Có' : 'Không'}\n` +
            `**Tin nhắn tùy chỉnh:** ${customMessage ? 'Có' : 'Sử dụng mặc định'}`
        );
        
        if (customMessage) {
            successEmbed.addFields({
                name: '📝 Tin nhắn tùy chỉnh',
                value: `\`\`\`${customMessage}\`\`\``,
                inline: false
            });
        }
        
        successEmbed.addFields({
            name: '💡 Mẹo',
            value: 'Sử dụng `/welcome-setup test` để kiểm tra tin nhắn chào mừng!',
            inline: false
        });
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi cập nhật welcome config:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể lưu cấu hình. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleDisable(interaction, client, guildId) {
    try {
        await client.db.updateWelcomeConfig(guildId, {
            welcome_enabled: 0
        });
        
        const successEmbed = createSuccessEmbed(
            'Hệ thống chào mừng đã được tắt!',
            'Bot sẽ không gửi tin nhắn chào mừng cho thành viên mới nữa.'
        );
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi tắt welcome:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể tắt hệ thống chào mừng. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleTest(interaction, client, guildId) {
    try {
        const config = await client.db.getWelcomeConfig(guildId);
        
        if (!config || !config.welcome_enabled) {
            const errorEmbed = createErrorEmbed(
                'Hệ thống chưa được bật!',
                'Vui lòng sử dụng `/welcome-setup enable` để bật hệ thống chào mừng trước.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const channel = interaction.guild.channels.cache.get(config.welcome_channel_id);
        if (!channel) {
            const errorEmbed = createErrorEmbed(
                'Kênh không tồn tại!',
                'Kênh chào mừng đã bị xóa. Vui lòng thiết lập lại.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        // Tạo tin nhắn test với thông tin của user hiện tại
        const { createWelcomeEmbed } = require('../../utils/embedBuilder.js');
        
        if (config.welcome_embed) {
            const welcomeEmbed = createWelcomeEmbed(interaction.member, config.welcome_message);
            welcomeEmbed.setTitle('🧪 [TEST] Chào mừng thành viên mới!');
            await channel.send({ embeds: [welcomeEmbed] });
        } else {
            const defaultMessage = `Chào mừng **{user}** đến với **{server}**!\n\nHy vọng bạn sẽ có những trải nghiệm tuyệt vời tại đây! ${client.config.emojis.welcome}`;
            const message = config.welcome_message || defaultMessage;
            const processedMessage = message
                .replace(/{user}/g, `<@${interaction.user.id}>`)
                .replace(/{username}/g, interaction.user.username)
                .replace(/{server}/g, interaction.guild.name)
                .replace(/{membercount}/g, interaction.guild.memberCount.toString());
            
            await channel.send(`🧪 **[TEST]** ${processedMessage}`);
        }
        
        const successEmbed = createSuccessEmbed(
            'Tin nhắn test đã được gửi!',
            `Kiểm tra tin nhắn chào mừng tại ${channel}`
        );
        
        await interaction.reply({ embeds: [successEmbed], ephemeral: true });
        
    } catch (error) {
        console.error('Lỗi khi test welcome:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể gửi tin nhắn test. Vui lòng kiểm tra quyền bot!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleConfig(interaction, client, guildId) {
    try {
        const config = await client.db.getWelcomeConfig(guildId);
        
        if (!config) {
            const infoEmbed = createInfoEmbed(
                'Chưa có cấu hình',
                'Hệ thống chào mừng chưa được thiết lập.\n\nSử dụng `/welcome-setup enable` để bắt đầu!'
            );
            return await interaction.reply({ embeds: [infoEmbed] });
        }
        
        const channel = interaction.guild.channels.cache.get(config.welcome_channel_id);
        const channelText = channel ? `${channel}` : '❌ Kênh đã bị xóa';
        
        const embed = createInfoEmbed(
            'Cấu hình hệ thống chào mừng',
            `**Trạng thái:** ${config.welcome_enabled ? '✅ Đã bật' : '❌ Đã tắt'}\n` +
            `**Kênh:** ${channelText}\n` +
            `**Định dạng:** ${config.welcome_embed ? 'Embed' : 'Tin nhắn thường'}\n` +
            `**Tin nhắn riêng tư:** ${config.dm_welcome ? 'Có' : 'Không'}`
        );
        
        if (config.welcome_message) {
            embed.addFields({
                name: '📝 Tin nhắn tùy chỉnh',
                value: `\`\`\`${config.welcome_message}\`\`\``,
                inline: false
            });
        }
        
        embed.addFields({
            name: '🔧 Quản lý',
            value: '• `/welcome-setup enable` - Bật/cập nhật cấu hình\n' +
                   '• `/welcome-setup disable` - Tắt hệ thống\n' +
                   '• `/welcome-setup test` - Kiểm tra tin nhắn',
            inline: false
        });
        
        await interaction.reply({ embeds: [embed] });
        
    } catch (error) {
        console.error('Lỗi khi lấy config:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể lấy cấu hình. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}
