const { EmbedBuilder, PermissionsBitField } = require('discord.js');

module.exports = {
    name: 'unban',
    aliases: ['ub'],
    description: 'Unban a banned member from the server.',
    async execute(message, args) {
        // Permission Check
        if (!message.member.permissions.has(PermissionsBitField.Flags.BanMembers)) {
            return message.channel.send({
                embeds: [
                    new EmbedBuilder()
                        .setColor('#FF0000')
                        .setDescription('❌ | You need the `Ban Members` permission to use this command.')
                ]
            });
        }

        // User ID Fetching
        const userId = args[0];
        if (!userId || isNaN(userId)) {
            return message.channel.send({
                embeds: [
                    new EmbedBuilder()
                        .setColor('#FF0000')
                        .setDescription('❌ | Please provide a valid User ID to unban.')
                ]
            });
        }

        try {
            await message.guild.members.unban(userId);
            message.channel.send({
                embeds: [
                    new EmbedBuilder()
                        .setColor('#00FF00')
                        .setDescription(`✅ | Successfully unbanned user with ID: **${userId}**`)
                ]
            });
        } catch (error) {
            message.channel.send({
                embeds: [
                    new EmbedBuilder()
                        .setColor('#FF0000')
                        .setDescription('❌ | Could not unban the user. Make sure the ID is correct and the user is banned.')
                ]
            });
        }
    }
};