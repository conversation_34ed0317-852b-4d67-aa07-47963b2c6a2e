const { SlashCommandBuilder, ChannelType } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed } = require('../../utils/embedBuilder.js');
const { canManageChannel } = require('../../utils/permissions.js');
const { isValidChannelName } = require('../../utils/validators.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('channel')
        .setDescription('Quản lý kênh server')
        .addSubcommand(subcommand =>
            subcommand
                .setName('text')
                .setDescription('Tạo kênh text mới')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('Tên kênh text')
                        .setRequired(true)
                )
                .addChannelOption(option =>
                    option.setName('category')
                        .setDescription('<PERSON>h mục chứa kênh')
                        .setRequired(false)
                        .addChannelTypes(ChannelType.GuildCategory)
                )
                .addStringOption(option =>
                    option.setName('topic')
                        .setDescription('Chủ đề của kênh')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('nsfw')
                        .setDescription('Kênh NSFW (mặc định: false)')
                        .setRequired(false)
                )
                .addIntegerOption(option =>
                    option.setName('slowmode')
                        .setDescription('Thời gian slowmode (giây, 0-21600)')
                        .setRequired(false)
                        .setMinValue(0)
                        .setMaxValue(21600)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('voice')
                .setDescription('Tạo kênh voice mới')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('Tên kênh voice')
                        .setRequired(true)
                )
                .addChannelOption(option =>
                    option.setName('category')
                        .setDescription('Danh mục chứa kênh')
                        .setRequired(false)
                        .addChannelTypes(ChannelType.GuildCategory)
                )
                .addIntegerOption(option =>
                    option.setName('userlimit')
                        .setDescription('Giới hạn số người (0 = không giới hạn)')
                        .setRequired(false)
                        .setMinValue(0)
                        .setMaxValue(99)
                )
                .addIntegerOption(option =>
                    option.setName('bitrate')
                        .setDescription('Chất lượng âm thanh (kbps, 8-384)')
                        .setRequired(false)
                        .setMinValue(8)
                        .setMaxValue(384)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('Xóa kênh')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Kênh cần xóa')
                        .setRequired(true)
                        .addChannelTypes(ChannelType.GuildText, ChannelType.GuildVoice)
                )
                .addBooleanOption(option =>
                    option.setName('confirm')
                        .setDescription('Xác nhận xóa kênh (bắt buộc)')
                        .setRequired(true)
                )
        ),
    category: 'channels',
    adminOnly: true,
    manageServer: false,

    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();

        // Kiểm tra quyền bot
        const canManage = canManageChannel(interaction.guild);
        if (!canManage.canManage) {
            const errorEmbed = createErrorEmbed(
                'Bot không có quyền!',
                canManage.reason
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        switch (subcommand) {
            case 'text':
                await handleTextChannel(interaction, client);
                break;
            case 'voice':
                await handleVoiceChannel(interaction, client);
                break;
            case 'delete':
                await handleDelete(interaction, client);
                break;
        }
    },
};

async function handleTextChannel(interaction, client) {
    const name = interaction.options.getString('name');
    const category = interaction.options.getChannel('category');
    const topic = interaction.options.getString('topic');
    const nsfw = interaction.options.getBoolean('nsfw') || false;
    const slowmode = interaction.options.getInteger('slowmode') || 0;

    // Validate channel name
    if (!isValidChannelName(name)) {
        const errorEmbed = createErrorEmbed(
            'Tên kênh không hợp lệ!',
            'Tên kênh chỉ được chứa:\n• Chữ thường (a-z)\n• Số (0-9)\n• Dấu gạch ngang (-)\n• Dấu gạch dưới (_)\n• Độ dài: 1-100 ký tự'
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    // Check if channel already exists
    const existingChannel = interaction.guild.channels.cache.find(ch => ch.name === name);
    if (existingChannel) {
        const errorEmbed = createErrorEmbed(
            'Kênh đã tồn tại!',
            `Kênh **${name}** đã tồn tại trong server.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    try {
        const channelOptions = {
            name: name,
            type: ChannelType.GuildText,
            parent: category?.id || null,
            topic: topic || null,
            nsfw: nsfw,
            rateLimitPerUser: slowmode,
            reason: `Kênh text được tạo bởi ${interaction.user.tag}`
        };

        const newChannel = await interaction.guild.channels.create(channelOptions);

        const successEmbed = createSuccessEmbed(
            'Đã tạo kênh text!',
            `**Kênh:** ${newChannel}\n` +
            `**Danh mục:** ${category ? category.name : 'Không có'}\n` +
            `**Chủ đề:** ${topic || 'Không có'}\n` +
            `**NSFW:** ${nsfw ? 'Có' : 'Không'}\n` +
            `**Slowmode:** ${slowmode > 0 ? `${slowmode} giây` : 'Tắt'}`
        );

        successEmbed.addFields({
            name: '📝 Bước tiếp theo',
            value: 'Bạn có thể:\n' +
                   '• Thiết lập quyền cho kênh bằng `/channel-permission setup`\n' +
                   '• Chỉnh sửa thông tin kênh trong Discord\n' +
                   '• Thêm webhook hoặc bot vào kênh',
            inline: false
        });

        await interaction.reply({ embeds: [successEmbed] });
    } catch (error) {
        console.error('Lỗi khi tạo text channel:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi tạo kênh!',
            'Không thể tạo kênh text. Vui lòng kiểm tra quyền bot và thử lại!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleVoiceChannel(interaction, client) {
    const name = interaction.options.getString('name');
    const category = interaction.options.getChannel('category');
    const userLimit = interaction.options.getInteger('userlimit') || 0;
    const bitrate = interaction.options.getInteger('bitrate') || 64;

    // Check if channel already exists
    const existingChannel = interaction.guild.channels.cache.find(ch => ch.name === name);
    if (existingChannel) {
        const errorEmbed = createErrorEmbed(
            'Kênh đã tồn tại!',
            `Kênh **${name}** đã tồn tại trong server.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    try {
        const channelOptions = {
            name: name,
            type: ChannelType.GuildVoice,
            parent: category?.id || null,
            userLimit: userLimit,
            bitrate: bitrate * 1000, // Convert to bits
            reason: `Kênh voice được tạo bởi ${interaction.user.tag}`
        };

        const newChannel = await interaction.guild.channels.create(channelOptions);

        const successEmbed = createSuccessEmbed(
            'Đã tạo kênh voice!',
            `**Kênh:** ${newChannel.name}\n` +
            `**Danh mục:** ${category ? category.name : 'Không có'}\n` +
            `**Giới hạn người dùng:** ${userLimit > 0 ? `${userLimit} người` : 'Không giới hạn'}\n` +
            `**Chất lượng âm thanh:** ${bitrate} kbps`
        );

        successEmbed.addFields({
            name: '📝 Bước tiếp theo',
            value: 'Bạn có thể:\n' +
                   '• Thiết lập quyền cho kênh bằng `/channel-permission setup`\n' +
                   '• Tạo kênh text tương ứng cho thảo luận\n' +
                   '• Thiết lập bot nhạc hoặc các tính năng khác',
            inline: false
        });

        await interaction.reply({ embeds: [successEmbed] });
    } catch (error) {
        console.error('Lỗi khi tạo voice channel:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi tạo kênh!',
            'Không thể tạo kênh voice. Vui lòng kiểm tra quyền bot và thử lại!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleDelete(interaction, client) {
    const channel = interaction.options.getChannel('channel');
    const confirm = interaction.options.getBoolean('confirm');

    if (!confirm) {
        const errorEmbed = createErrorEmbed(
            'Cần xác nhận!',
            'Bạn phải đặt `confirm:true` để xác nhận xóa kênh.\n\n⚠️ **Cảnh báo:** Hành động này không thể hoàn tác!'
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    // Kiểm tra quyền xóa kênh
    if (!interaction.guild.members.me.permissionsIn(channel).has('ManageChannels')) {
        const errorEmbed = createErrorEmbed(
            'Bot không có quyền!',
            `Bot không có quyền xóa kênh ${channel}.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    try {
        const channelName = channel.name;
        const channelType = channel.type === ChannelType.GuildText ? 'text' : 'voice';
        const categoryName = channel.parent?.name || 'Không có danh mục';

        await channel.delete(`Kênh được xóa bởi ${interaction.user.tag}`);

        const successEmbed = createSuccessEmbed(
            'Đã xóa kênh thành công!',
            `**Kênh:** ${channelName}\n` +
            `**Loại:** ${channelType}\n` +
            `**Danh mục:** ${categoryName}\n\n` +
            `⚠️ Kênh đã bị xóa vĩnh viễn và không thể khôi phục.`
        );

        await interaction.reply({ embeds: [successEmbed] });
    } catch (error) {
        console.error('Lỗi khi xóa channel:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi xóa kênh!',
            'Không thể xóa kênh. Vui lòng kiểm tra quyền bot và thử lại!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}
