const fs = require("fs");
const path = require("path");
const readline = require("readline");

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

function question(query) {
  return new Promise((resolve) => rl.question(query, resolve));
}

async function setupBot() {
  console.log("🤖 Server Setup Bot - Thiết lập ban đầu");
  console.log("=====================================\n");

  // Kiểm tra file .env
  const envPath = path.join(__dirname, ".env");
  const envExamplePath = path.join(__dirname, ".env.example");

  if (fs.existsSync(envPath)) {
    console.log("📄 File .env đã tồn tại");
    const overwrite = await question(
      "Bạn có muốn ghi đè file .env hiện tại? (y/N): "
    );
    if (overwrite.toLowerCase() !== "y" && overwrite.toLowerCase() !== "yes") {
      console.log("✅ Giữ nguyên file .env hiện tại");
      rl.close();
      return;
    }
  }

  console.log("\n📝 Vui lòng nhập thông tin bot Discord:\n");

  // Thu thập thông tin
  const botToken = await question("🔑 Discord Bot Token: ");
  if (!botToken) {
    console.log("❌ Bot token là bắt buộc!");
    rl.close();
    return;
  }

  const clientId = await question("🆔 Client ID (Application ID): ");
  if (!clientId) {
    console.log("❌ Client ID là bắt buộc!");
    rl.close();
    return;
  }

  const guildId = await question("🏠 Guild ID (để test - có thể bỏ trống): ");
  const ownerId = await question(
    "👤 Owner ID (User ID của bạn - có thể bỏ trống): "
  );

  // Tạo nội dung .env
  let envContent = `# Discord Bot Configuration
DISCORD_TOKEN=${botToken}
CLIENT_ID=${clientId}
${guildId ? `GUILD_ID=${guildId}` : "# GUILD_ID=your_guild_id_for_development"}

# Database Configuration
DATABASE_PATH=./data/serversetup.db

# Bot Configuration
BOT_PREFIX=!
${ownerId ? `OWNER_ID=${ownerId}` : "# OWNER_ID=your_user_id_here"}
DEBUG=false
NODE_ENV=production

# Optional Configuration
# WEBHOOK_URL=your_webhook_url_for_logging
# LOG_LEVEL=info
`;

  // Ghi file .env
  try {
    fs.writeFileSync(envPath, envContent);
    console.log("\n✅ Đã tạo file .env thành công!");
  } catch (error) {
    console.error("❌ Lỗi khi tạo file .env:", error);
    rl.close();
    return;
  }

  // Tạo thư mục data nếu chưa có
  const dataDir = path.join(__dirname, "data");
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
    console.log("✅ Đã tạo thư mục data");
  }

  console.log("\n🎉 Thiết lập hoàn tất!");
  console.log("\n📋 Các bước tiếp theo:");
  console.log('1. Chạy "npm install" để cài đặt dependencies (nếu chưa)');
  console.log('2. Chạy "npm run deploy" để deploy slash commands');
  console.log('3. Chạy "npm start" để khởi động bot');
  console.log(
    '\n💡 Hoặc chỉ cần chạy "npm start" - bot sẽ tự động deploy commands'
  );

  // Hỏi có muốn deploy commands ngay không
  const deployNow = await question(
    "\n🚀 Bạn có muốn deploy commands ngay bây giờ? (Y/n): "
  );
  if (deployNow.toLowerCase() !== "n" && deployNow.toLowerCase() !== "no") {
    console.log("\n🔄 Đang deploy commands...");
    try {
      const { deployCommands } = require("./src/utils/deployCommands.js");
      await deployCommands();
      console.log("✅ Deploy commands thành công!");
    } catch (error) {
      console.error("❌ Lỗi khi deploy commands:", error.message);
      console.log('💡 Bạn có thể chạy "npm run deploy" sau để thử lại');
    }
  }

  // Hỏi có muốn khởi động bot ngay không
  const startNow = await question(
    "\n▶️ Bạn có muốn khởi động bot ngay bây giờ? (Y/n): "
  );
  if (startNow.toLowerCase() !== "n" && startNow.toLowerCase() !== "no") {
    console.log("\n🚀 Đang khởi động bot...");
    rl.close();

    // Khởi động bot
    require("./src/index.js");
  } else {
    console.log(
      '\n✨ Thiết lập hoàn tất! Chạy "npm start" khi bạn sẵn sàng khởi động bot.'
    );
    rl.close();
  }
}

// Xử lý lỗi
process.on("SIGINT", () => {
  console.log("\n\n👋 Đã hủy thiết lập");
  rl.close();
  process.exit(0);
});

// Chạy setup
setupBot().catch((error) => {
  console.error("❌ Lỗi trong quá trình thiết lập:", error);
  rl.close();
  process.exit(1);
});
