const { SlashCommandBuilder, ChannelType } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('id')
        .setDescription('Lấy ID của user, channel, role hoặc emoji')
        .addSubcommand(subcommand =>
            subcommand
                .setName('user')
                .setDescription('Lấy ID của user')
                .addUserOption(option =>
                    option.setName('target')
                        .setDescription('User cần lấy ID')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('channel')
                .setDescription('Lấy ID của channel')
                .addChannelOption(option =>
                    option.setName('target')
                        .setDescription('Channel cần lấy ID')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('role')
                .setDescription('Lấy ID của role')
                .addRoleOption(option =>
                    option.setName('target')
                        .setDescription('Role cần lấy ID')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('emoji')
                .setDescription('Lấy ID của emoji')
                .addStringOption(option =>
                    option.setName('target')
                        .setDescription('Emoji cần lấy ID (gửi emoji hoặc tên emoji)')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('server')
                .setDescription('Lấy ID của server hiện tại')
        ),
    category: 'utility',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();
        
        switch (subcommand) {
            case 'user':
                await handleUser(interaction, client);
                break;
            case 'channel':
                await handleChannel(interaction, client);
                break;
            case 'role':
                await handleRole(interaction, client);
                break;
            case 'emoji':
                await handleEmoji(interaction, client);
                break;
            case 'server':
                await handleServer(interaction, client);
                break;
        }
    },
};

async function handleUser(interaction, client) {
    const user = interaction.options.getUser('target');
    const member = interaction.guild.members.cache.get(user.id);
    
    const embed = createInfoEmbed(
        `👤 ID của ${user.username}`,
        `Thông tin ID của user **${user.username}**:`
    );
    
    embed.addFields(
        { name: '🆔 User ID', value: `\`${user.id}\``, inline: true },
        { name: '📛 Username', value: user.username, inline: true },
        { name: '🏷️ Display Name', value: user.displayName || 'Không có', inline: true }
    );
    
    if (member) {
        embed.addFields(
            { name: '📅 Tham gia server', value: member.joinedAt.toLocaleString('vi-VN'), inline: true },
            { name: '🎭 Role cao nhất', value: member.roles.highest.name, inline: true },
            { name: '🤖 Bot', value: user.bot ? 'Có' : 'Không', inline: true }
        );
    }
    
    embed.addFields(
        { name: '📅 Tạo tài khoản', value: user.createdAt.toLocaleString('vi-VN'), inline: false },
        { name: '🔗 Mention', value: `<@${user.id}>`, inline: false }
    );
    
    if (user.avatarURL()) {
        embed.setThumbnail(user.avatarURL({ dynamic: true, size: 256 }));
    }
    
    await interaction.reply({ embeds: [embed] });
}

async function handleChannel(interaction, client) {
    const channel = interaction.options.getChannel('target');
    
    const channelTypes = {
        [ChannelType.GuildText]: 'Text Channel',
        [ChannelType.GuildVoice]: 'Voice Channel',
        [ChannelType.GuildCategory]: 'Category',
        [ChannelType.GuildNews]: 'News Channel',
        [ChannelType.GuildStageVoice]: 'Stage Channel',
        [ChannelType.GuildForum]: 'Forum Channel'
    };
    
    const embed = createInfoEmbed(
        `📝 ID của ${channel.name}`,
        `Thông tin ID của channel **${channel.name}**:`
    );
    
    embed.addFields(
        { name: '🆔 Channel ID', value: `\`${channel.id}\``, inline: true },
        { name: '📛 Tên channel', value: channel.name, inline: true },
        { name: '📂 Loại', value: channelTypes[channel.type] || 'Không xác định', inline: true }
    );
    
    if (channel.parent) {
        embed.addFields(
            { name: '📁 Danh mục', value: channel.parent.name, inline: true },
            { name: '🆔 ID Danh mục', value: `\`${channel.parent.id}\``, inline: true }
        );
    }
    
    embed.addFields(
        { name: '📍 Vị trí', value: channel.position.toString(), inline: true },
        { name: '📅 Tạo lúc', value: channel.createdAt.toLocaleString('vi-VN'), inline: false },
        { name: '🔗 Mention', value: `<#${channel.id}>`, inline: false }
    );
    
    if (channel.topic) {
        embed.addFields({ name: '📝 Chủ đề', value: channel.topic, inline: false });
    }
    
    await interaction.reply({ embeds: [embed] });
}

async function handleRole(interaction, client) {
    const role = interaction.options.getRole('target');
    
    const embed = createInfoEmbed(
        `🎭 ID của ${role.name}`,
        `Thông tin ID của role **${role.name}**:`
    );
    
    embed.addFields(
        { name: '🆔 Role ID', value: `\`${role.id}\``, inline: true },
        { name: '📛 Tên role', value: role.name, inline: true },
        { name: '🎨 Màu', value: role.hexColor, inline: true },
        { name: '📍 Vị trí', value: role.position.toString(), inline: true },
        { name: '👥 Thành viên', value: role.members.size.toString(), inline: true },
        { name: '🔒 Hoisted', value: role.hoist ? 'Có' : 'Không', inline: true }
    );
    
    const permissions = role.permissions.toArray();
    const isAdmin = permissions.includes('Administrator');
    const keyPerms = permissions.filter(p => 
        ['ManageGuild', 'ManageChannels', 'ManageRoles', 'BanMembers', 'KickMembers'].includes(p)
    );
    
    let permissionText = '';
    if (isAdmin) {
        permissionText = 'Administrator (Tất cả quyền)';
    } else if (keyPerms.length > 0) {
        permissionText = keyPerms.slice(0, 5).join(', ') + (keyPerms.length > 5 ? '...' : '');
    } else {
        permissionText = 'Quyền cơ bản';
    }
    
    embed.addFields(
        { name: '📅 Tạo lúc', value: role.createdAt.toLocaleString('vi-VN'), inline: false },
        { name: '🔑 Quyền chính', value: permissionText, inline: false },
        { name: '🔗 Mention', value: `<@&${role.id}>`, inline: false }
    );
    
    await interaction.reply({ embeds: [embed] });
}

async function handleEmoji(interaction, client) {
    const emojiInput = interaction.options.getString('target');
    
    // Tìm emoji trong server
    let emoji = null;
    
    // Thử tìm bằng ID hoặc tên
    emoji = interaction.guild.emojis.cache.find(e => 
        e.id === emojiInput || 
        e.name === emojiInput ||
        e.toString() === emojiInput
    );
    
    // Thử parse emoji từ string
    if (!emoji) {
        const emojiMatch = emojiInput.match(/<a?:(\w+):(\d+)>/);
        if (emojiMatch) {
            emoji = interaction.guild.emojis.cache.get(emojiMatch[2]);
        }
    }
    
    if (!emoji) {
        const errorEmbed = createErrorEmbed(
            'Không tìm thấy emoji!',
            `Không thể tìm thấy emoji **${emojiInput}** trong server.\n\n` +
            `**Cách sử dụng:**\n` +
            `• Gửi emoji: \`:emoji_name:\`\n` +
            `• Tên emoji: \`emoji_name\`\n` +
            `• ID emoji: \`123456789\``
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    const embed = createInfoEmbed(
        `😀 ID của ${emoji.name}`,
        `Thông tin ID của emoji **${emoji.name}**:`
    );
    
    embed.addFields(
        { name: '🆔 Emoji ID', value: `\`${emoji.id}\``, inline: true },
        { name: '📛 Tên emoji', value: emoji.name, inline: true },
        { name: '🎬 Animated', value: emoji.animated ? 'Có' : 'Không', inline: true },
        { name: '📅 Tạo lúc', value: emoji.createdAt.toLocaleString('vi-VN'), inline: false },
        { name: '🔗 URL', value: `[Link ảnh](${emoji.url})`, inline: false },
        { name: '📝 Sử dụng', value: `\`${emoji.toString()}\``, inline: false }
    );
    
    embed.setThumbnail(emoji.url);
    
    await interaction.reply({ embeds: [embed] });
}

async function handleServer(interaction, client) {
    const guild = interaction.guild;
    
    const embed = createInfoEmbed(
        `🏠 ID của ${guild.name}`,
        `Thông tin ID của server **${guild.name}**:`
    );
    
    embed.addFields(
        { name: '🆔 Server ID', value: `\`${guild.id}\``, inline: true },
        { name: '📛 Tên server', value: guild.name, inline: true },
        { name: '👑 Owner ID', value: `\`${guild.ownerId}\``, inline: true },
        { name: '👥 Thành viên', value: guild.memberCount.toString(), inline: true },
        { name: '🎭 Roles', value: guild.roles.cache.size.toString(), inline: true },
        { name: '📝 Channels', value: guild.channels.cache.size.toString(), inline: true },
        { name: '😀 Emojis', value: guild.emojis.cache.size.toString(), inline: true },
        { name: '🚀 Boost Level', value: guild.premiumTier.toString(), inline: true },
        { name: '💎 Boosts', value: guild.premiumSubscriptionCount?.toString() || '0', inline: true },
        { name: '📅 Tạo lúc', value: guild.createdAt.toLocaleString('vi-VN'), inline: false }
    );
    
    if (guild.description) {
        embed.addFields({ name: '📝 Mô tả', value: guild.description, inline: false });
    }
    
    if (guild.iconURL()) {
        embed.setThumbnail(guild.iconURL({ dynamic: true, size: 256 }));
    }
    
    await interaction.reply({ embeds: [embed] });
}
