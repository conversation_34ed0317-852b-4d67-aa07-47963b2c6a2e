const { PermissionsBitField, EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'hide',
    description: 'Hides the current channel for @everyone',
    run: async (message, args, client) => {
        if (!message.member.permissions.has(PermissionsBitField.Flags.ManageChannels)) {
            return message.reply('❌ You need `Manage Channels` permission to use this command.');
        }

        await message.channel.permissionOverwrites.edit(message.guild.roles.everyone, {
            ViewChannel: false
        });

        const hideEmbed = new EmbedBuilder()
            .setColor('#FF0000')
            .setDescription(`🔒 This channel is now hidden for @everyone.`)
            .setTimestamp();

        message.channel.send({ embeds: [hideEmbed] });
    }
};