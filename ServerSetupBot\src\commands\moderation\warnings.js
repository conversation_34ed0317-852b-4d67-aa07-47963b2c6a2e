const { SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder } = require('discord.js');
const { createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { formatWarningList } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('warnings')
        .setDescription('Xem danh sách cảnh báo của thành viên')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Thành viên cần xem cảnh báo')
                .setRequired(true)
        )
        .addBooleanOption(option =>
            option.setName('include_removed')
                .setDescription('Bao gồm cảnh báo đã bị hủy')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // <PERSON><PERSON><PERSON> tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.ModerateMembers)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                'Bạn cần quyền `Moderate Members` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const user = interaction.options.getUser('user');
        const includeRemoved = interaction.options.getBoolean('include_removed') || false;
        const guildId = interaction.guild.id;
        
        try {
            // Lấy danh sách warnings
            const warnings = await client.db.getWarnings(guildId, user.id, !includeRemoved);
            
            const embed = new EmbedBuilder()
                .setColor(warnings.length > 0 ? 0xff4444 : 0x00ff00)
                .setTitle(`⚠️ Danh sách cảnh báo - ${user.tag}`)
                .setThumbnail(user.displayAvatarURL({ dynamic: true }))
                .setTimestamp()
                .setFooter({ 
                    text: `Yêu cầu bởi ${interaction.user.tag}`, 
                    iconURL: interaction.user.displayAvatarURL() 
                });
            
            if (warnings.length === 0) {
                embed.setDescription(
                    includeRemoved 
                        ? 'Thành viên này không có cảnh báo nào.' 
                        : 'Thành viên này không có cảnh báo đang hoạt động.'
                );
                embed.setColor(0x00ff00);
            } else {
                // Phân loại warnings
                const activeWarnings = warnings.filter(w => w.active);
                const removedWarnings = warnings.filter(w => !w.active);
                
                // Thống kê
                embed.addFields({
                    name: '📊 Thống kê',
                    value: `**Tổng cảnh báo:** ${warnings.length}\n` +
                           `**Đang hoạt động:** ${activeWarnings.length}\n` +
                           `**Đã hủy:** ${removedWarnings.length}`,
                    inline: true
                });
                
                // Hiển thị cảnh báo đang hoạt động
                if (activeWarnings.length > 0) {
                    const activeList = activeWarnings.slice(0, 10).map((warning, index) => {
                        const date = new Date(warning.created_at).toLocaleDateString('vi-VN');
                        return `**${index + 1}.** ${warning.reason}\n` +
                               `   📅 ${date} | 👮 <@${warning.moderator_id}> | ID: ${warning.id}`;
                    }).join('\n\n');
                    
                    embed.addFields({
                        name: '🔴 Cảnh báo đang hoạt động',
                        value: activeList + (activeWarnings.length > 10 ? `\n\n*... và ${activeWarnings.length - 10} cảnh báo khác*` : ''),
                        inline: false
                    });
                }
                
                // Hiển thị cảnh báo đã hủy (nếu được yêu cầu)
                if (includeRemoved && removedWarnings.length > 0) {
                    const removedList = removedWarnings.slice(0, 5).map((warning, index) => {
                        const createdDate = new Date(warning.created_at).toLocaleDateString('vi-VN');
                        const removedDate = new Date(warning.removed_at).toLocaleDateString('vi-VN');
                        return `**${index + 1}.** ${warning.reason}\n` +
                               `   📅 ${createdDate} | 👮 <@${warning.moderator_id}>\n` +
                               `   ❌ Hủy: ${removedDate} | <@${warning.removed_by}>\n` +
                               `   📝 Lý do hủy: ${warning.remove_reason || 'Không có'}`;
                    }).join('\n\n');
                    
                    embed.addFields({
                        name: '⚪ Cảnh báo đã hủy',
                        value: removedList + (removedWarnings.length > 5 ? `\n\n*... và ${removedWarnings.length - 5} cảnh báo khác*` : ''),
                        inline: false
                    });
                }
                
                // Thêm thông tin về auto actions
                const moderationConfig = await client.db.getModerationConfig(guildId);
                if (moderationConfig && activeWarnings.length > 0) {
                    let autoInfo = '';
                    
                    if (moderationConfig.warning_threshold) {
                        const remaining = moderationConfig.warning_threshold - activeWarnings.length;
                        if (remaining > 0) {
                            autoInfo += `🔶 **Auto Kick:** ${remaining} cảnh báo nữa\n`;
                        } else {
                            autoInfo += `🔶 **Auto Kick:** Đã đạt ngưỡng!\n`;
                        }
                    }
                    
                    if (moderationConfig.auto_ban_threshold) {
                        const remaining = moderationConfig.auto_ban_threshold - activeWarnings.length;
                        if (remaining > 0) {
                            autoInfo += `🔴 **Auto Ban:** ${remaining} cảnh báo nữa`;
                        } else {
                            autoInfo += `🔴 **Auto Ban:** Đã đạt ngưỡng!`;
                        }
                    }
                    
                    if (autoInfo) {
                        embed.addFields({
                            name: '⚡ Auto Actions',
                            value: autoInfo,
                            inline: false
                        });
                    }
                }
            }
            
            // Thêm hướng dẫn
            embed.addFields({
                name: '🔧 Quản lý',
                value: `• \`/unwarn user:${user.tag} warning_id:[ID]\` - Hủy cảnh báo cụ thể\n` +
                       `• \`/unwarn user:${user.tag}\` - Hủy tất cả cảnh báo\n` +
                       `• \`/warn user:${user.tag} reason:[lý do]\` - Thêm cảnh báo mới`,
                inline: false
            });
            
            await interaction.reply({ embeds: [embed] });
            
        } catch (error) {
            console.error('Lỗi khi xem warnings:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi xem cảnh báo!',
                'Đã xảy ra lỗi khi lấy danh sách cảnh báo. Vui lòng thử lại sau!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
