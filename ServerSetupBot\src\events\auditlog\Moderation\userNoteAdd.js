const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction, client) {
        try {
            // This would be triggered by a custom note command
            if (!interaction.isCommand() || interaction.commandName !== 'note') return;
            
            console.log(`📝 User note added in ${interaction.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(interaction.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'USER_NOTE_ADD')) return;
            
            // Get note details from interaction
            const targetUser = interaction.options.getUser('user');
            const noteContent = interaction.options.getString('note');
            const noteType = interaction.options.getString('type') || 'general';
            const isPrivate = interaction.options.getBoolean('private') || false;
            const moderator = interaction.user;
            
            // Create note ID and save to database
            const noteId = await client.db.addUserNote({
                guildId: interaction.guild.id,
                userId: targetUser.id,
                moderatorId: moderator.id,
                content: noteContent,
                type: noteType,
                isPrivate: isPrivate
            });
            
            // Create event data
            const eventData = {
                eventType: 'USER_NOTE_ADD',
                user: moderator.tag,
                userId: moderator.id,
                action: `Note được thêm cho ${targetUser.tag}`,
                details: `**${moderator.tag}** đã thêm note cho **${targetUser.tag}**`,
                target: targetUser.tag,
                timestamp: new Date().toISOString(),
                noteId: noteId
            };
            
            // Add to database
            await client.db.addAuditLog(interaction.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📝 Note được thêm cho thành viên',
                `Vừa có note mới được thêm vào hồ sơ thành viên`
            );
            
            embed.setColor(0x3498db); // Blue for note
            
            // Get note type names
            const noteTypes = {
                'general': 'Ghi chú chung',
                'warning': 'Cảnh báo',
                'positive': 'Tích cực',
                'behavior': 'Hành vi',
                'history': 'Lịch sử',
                'other': 'Khác'
            };
            
            embed.addFields([
                {
                    name: '> ID Note',
                    value: `- #${noteId}`,
                    inline: true
                },
                {
                    name: '> Đối tượng',
                    value: `- ${targetUser.tag} (${targetUser})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${targetUser.id}`,
                    inline: true
                },
                {
                    name: '> Moderator',
                    value: `- ${moderator.tag}`,
                    inline: true
                },
                {
                    name: '> Thời gian tạo',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Loại note',
                    value: `- ${noteTypes[noteType] || noteType}`,
                    inline: true
                },
                {
                    name: '> Quyền riêng tư',
                    value: `- ${isPrivate ? '🔒 Riêng tư' : '👁️ Công khai'}`,
                    inline: true
                }
            ]);
            
            // Add note content (truncated if too long)
            const displayContent = noteContent.length > 500 
                ? noteContent.substring(0, 500) + '...'
                : noteContent;
            
            embed.addFields({
                name: '> Nội dung note',
                value: `\`\`\`${displayContent}\`\`\``,
                inline: false
            });
            
            // Add target user info
            const targetMember = interaction.guild.members.cache.get(targetUser.id);
            if (targetMember) {
                const accountAge = Date.now() - targetUser.createdTimestamp;
                const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
                const memberAge = Date.now() - targetMember.joinedTimestamp;
                const memberAgeDays = Math.floor(memberAge / (1000 * 60 * 60 * 24));
                
                embed.addFields([
                    {
                        name: '> Tuổi tài khoản',
                        value: `- ${accountAgeDays} ngày`,
                        inline: true
                    },
                    {
                        name: '> Thời gian trong server',
                        value: `- ${memberAgeDays} ngày`,
                        inline: true
                    }
                ]);
                
                // Add roles info
                const roles = targetMember.roles.cache.filter(role => role.id !== interaction.guild.id);
                if (roles.size > 0) {
                    const rolesList = roles.size > 3 
                        ? `${roles.first(3).map(role => role.name).join(', ')} và ${roles.size - 3} roles khác`
                        : roles.map(role => role.name).join(', ');
                    
                    embed.addFields({
                        name: '> Roles hiện tại',
                        value: `- ${rolesList}`,
                        inline: false
                    });
                }
            }
            
            // Get total note count for this user
            const totalNotes = await client.db.getUserNoteCount(interaction.guild.id, targetUser.id);
            embed.addFields({
                name: '> Tổng số notes',
                value: `- ${totalNotes} notes`,
                inline: true
            });
            
            // Add note type specific information
            if (noteType === 'warning') {
                embed.addFields({
                    name: '> ⚠️ Note cảnh báo',
                    value: [
                        '• Ghi nhận hành vi cần chú ý',
                        '• Có thể dẫn đến hành động moderation',
                        '• Theo dõi chặt chẽ hơn',
                        '• Tham khảo cho quyết định tương lai'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0xe67e22); // Orange for warning note
            } else if (noteType === 'positive') {
                embed.addFields({
                    name: '> ✅ Note tích cực',
                    value: [
                        '• Ghi nhận hành vi tốt',
                        '• Có thể cân nhắc khi có vi phạm nhỏ',
                        '• Khuyến khích tiếp tục',
                        '• Tham khảo cho rewards/roles'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for positive note
            }
            
            // Add note usage guidelines
            embed.addFields({
                name: '> 💡 Lưu ý về notes',
                value: [
                    '• Notes giúp theo dõi lịch sử thành viên',
                    '• Nên ghi chép chi tiết và khách quan',
                    '• Private notes chỉ moderators thấy được',
                    '• Sử dụng để đưa ra quyết định công bằng'
                ].join('\n'),
                inline: false
            });
            
            // Set target user avatar as thumbnail
            if (targetUser.displayAvatarURL()) {
                embed.setThumbnail(targetUser.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${interaction.guild.name} • User Note Add`,
                iconURL: interaction.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ User note add logged: ${targetUser.tag}`);
            
        } catch (error) {
            console.error('Error in userNoteAdd audit log:', error);
        }
    }
};
