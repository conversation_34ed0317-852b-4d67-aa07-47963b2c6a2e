function _0x555b(_0x15108a,_0x3f8965){const _0x6aef17=_0x6aef();return _0x555b=function(_0x555b8e,_0x4d00bd){_0x555b8e=_0x555b8e-0x79;let _0x5bbf56=_0x6aef17[_0x555b8e];return _0x5bbf56;},_0x555b(_0x15108a,_0x3f8965);}(function(_0x402a9d,_0x4429ce){function _0x973def(_0x5aec53,_0x3b7b8f){return _0x555b(_0x3b7b8f-0x2d7,_0x5aec53);}const _0x315ed8=_0x402a9d();while(!![]){try{const _0x25f490=parseInt(_0x973def(0x382,0x38f))/0x1*(parseInt(_0x973def(0x366,0x355))/0x2)+-parseInt(_0x973def(0x35d,0x387))/0x3*(parseInt(_0x973def(0x36b,0x37a))/0x4)+parseInt(_0x973def(0x3be,0x392))/0x5+parseInt(_0x973def(0x388,0x36c))/0x6+parseInt(_0x973def(0x3a9,0x3a3))/0x7+parseInt(_0x973def(0x39c,0x3b9))/0x8+-parseInt(_0x973def(0x34e,0x372))/0x9*(parseInt(_0x973def(0x372,0x3a2))/0xa);if(_0x25f490===_0x4429ce)break;else _0x315ed8['push'](_0x315ed8['shift']());}catch(_0xadd9d0){_0x315ed8['push'](_0x315ed8['shift']());}}}(_0x6aef,0xe72f2));const {SlashCommandBuilder,EmbedBuilder,AttachmentBuilder,ChannelType}=require('discord.js'),fs=require('fs'),yaml=require(_0x1349c9(-0x1ae,-0x1b7)),moment=require(_0x1349c9(-0x1ac,-0x190)),{Chart,registerables,CategoryScale,LinearScale,LineController,LineElement,PointElement}=require(_0x1349c9(-0x195,-0x1be)),{createCanvas}=require('canvas'),config=yaml[_0x1349c9(-0x1d9,-0x1d9)](fs[_0x1349c9(-0x172,-0x192)](_0x1349c9(-0x186,-0x18f),'utf8'));function _0x6aef(){const _0x22d0b2=['setAuthor','Server\x20locale','attachment://member_chart.png','format','toBuffer','beginPath','arc','height','channels','setDate','toString','replace','Date','2NpdcUV','[png](','.jpg','cache','member_chart.png','createLinearGradient','raw','tag','EmbedColors','getDatasetMeta','#156fff','width','fillRect','Members','exports','load','error','setName','white','memberCount','type','reply','General','1357944ewauOl','ctx','forEach','user','bold','Voice\x20Channels','1809qXCkLm','Error\x20fetching\x20channels:\x20','datasets','Requested\x20by\x20','round','#FFFFFF','Server\x20Information','setTimestamp','640cnwEmn','name','Server\x20created','serverinfo','rgb(255,\x20255,\x20255)','chart.js','fillText','members','DD/MM/YYYY\x20HH:mm','Server\x20icon','center','register','js-yaml','17301XLAzhK','\x20members','labels','getTime','GuildVoice','roles','createdAt','data','158443BjPcoi','GuildNews','middle','3685185sxbZHd','setDescription','Error\x20in\x20serverinfo\x20command:\x20','filter','description','addColorStop','Roles','setColor','Sorry,\x20there\x20was\x20an\x20error\x20retrieving\x20the\x20server\x20information.','\x0a>\x20**Server\x20description:**\x20','bot','Member\x20Count','size','joinedAt','toFixed','#382bff','39910QxLvSa','7332997kkieLF','setTitle','Text\x20Channels','Server\x20id','chart','Server\x20boosts','setThumbnail','Total\x20members\x20(','readFileSync','map','moment','./config.yml','Server\x20owner','tooltipPosition','getContext','fill','fillStyle','canvas','>\x20**Server\x20name:**\x20','textAlign','guild','#101010','4019728RFdThg','Member\x20Join\x20Dates','undefined','iconURL','addFields','1\x20Week','font','Get\x20detailed\x20information\x20about\x20the\x20server','#202020'];_0x6aef=function(){return _0x22d0b2;};return _0x6aef();}Chart[_0x1349c9(-0x180,-0x1b8)](...registerables,CategoryScale,LinearScale,LineController,LineElement,PointElement),module[_0x1349c9(-0x1b6,-0x1da)]={'data':new SlashCommandBuilder()[_0x1349c9(-0x1cf,-0x1d7)](_0x1349c9(-0x1e9,-0x1c0))['setDescription'](_0x1349c9(-0x171,-0x17d)),'category':_0x1349c9(-0x1d9,-0x1d2),async 'execute'(_0x2b691e){function _0x273757(_0x3fdb6d,_0x3f160a){return _0x1349c9(_0x3fdb6d,_0x3f160a- -0xb8);}try{const _0x47f26e=_0x2b691e[_0x273757(-0x232,-0x23e)],_0x4b6e2e=_0x47f26e[_0x273757(-0x25b,-0x268)],_0x4fabc8=moment(_0x4b6e2e)['format'](_0x273757(-0x249,-0x273)),_0x55ce11=_0x47f26e[_0x273757(-0x28a,-0x25f)]||'No\x20description\x20set',_0x1419b7=_0x47f26e[_0x273757(-0x235,-0x239)](),_0x5874a9=await _0x47f26e['fetchOwner']();let _0x2f6a71=0x0,_0xf5f02=0x0,_0x1c3dd6=0x0;try{const _0x2dcd98=await _0x47f26e[_0x273757(-0x2cb,-0x2a5)]['fetch']();_0x2dcd98['forEach'](_0x56b2c3=>{function _0x2da0eb(_0x8b40bd,_0xb6880e){return _0x273757(_0x8b40bd,_0xb6880e-0xf3);}if(_0x56b2c3[_0x2da0eb(-0x182,-0x199)]===ChannelType['GuildText']||_0x56b2c3['type']===ChannelType[_0x2da0eb(-0x197,-0x172)])_0x2f6a71++;else _0x56b2c3['type']===ChannelType[_0x2da0eb(-0x19f,-0x177)]?_0xf5f02++:_0x1c3dd6++;});}catch(_0xcd5ad4){console[_0x273757(-0x276,-0x290)](_0x273757(-0x2a9,-0x282),_0xcd5ad4);}const _0x456085=new EmbedBuilder()[_0x273757(-0x26f,-0x233)]({'name':_0x273757(-0x2a1,-0x280)+_0x2b691e['user'][_0x273757(-0x286,-0x299)],'iconURL':_0x2b691e[_0x273757(-0x292,-0x286)]['displayAvatarURL']()})[_0x273757(-0x241,-0x251)](_0x273757(-0x297,-0x27d))[_0x273757(-0x287,-0x262)](_0x273757(-0x219,-0x240)+_0x47f26e[_0x273757(-0x2b7,-0x27a)]+_0x273757(-0x249,-0x25a)+_0x55ce11)[_0x273757(-0x252,-0x25c)](config[_0x273757(-0x2b3,-0x298)]);_0x1419b7&&_0x456085[_0x273757(-0x251,-0x24c)](_0x1419b7);const _0x56fd97=_0x1419b7?_0x273757(-0x271,-0x29f)+_0x1419b7+'?size=1024)\x20|\x20[jpg]('+_0x1419b7[_0x273757(-0x2ba,-0x2a2)]('.png',_0x273757(-0x278,-0x29e))+'?size=1024)\x20|\x20[gif]('+_0x1419b7[_0x273757(-0x266,-0x2a2)]('.png','.gif')+'?size=1024)':'No\x20icon\x20available';_0x456085[_0x273757(-0x22a,-0x238)]({'name':_0x273757(-0x257,-0x250),'value':_0x2f6a71[_0x273757(-0x2b9,-0x2a3)](),'inline':!![]},{'name':_0x273757(-0x29b,-0x284),'value':_0xf5f02['toString'](),'inline':!![]},{'name':'Other\x20Channels','value':_0x1c3dd6[_0x273757(-0x294,-0x2a3)](),'inline':!![]},{'name':_0x273757(-0x2b1,-0x279),'value':_0x4fabc8,'inline':!![]},{'name':_0x273757(-0x224,-0x246),'value':'<@'+_0x5874a9['id']+'>','inline':!![]},{'name':_0x273757(-0x25f,-0x24f),'value':_0x47f26e['id'],'inline':!![]},{'name':_0x273757(-0x25f,-0x232),'value':_0x47f26e['preferredLocale'],'inline':!![]},{'name':_0x273757(-0x235,-0x24d),'value':''+_0x47f26e['premiumSubscriptionCount'],'inline':!![]},{'name':_0x273757(-0x2a4,-0x272),'value':_0x56fd97,'inline':!![]},{'name':_0x273757(-0x224,-0x25d),'value':_0x47f26e[_0x273757(-0x24f,-0x269)][_0x273757(-0x288,-0x29d)][_0x273757(-0x252,-0x257)][_0x273757(-0x2b2,-0x2a3)](),'inline':!![]},{'name':_0x273757(-0x2a3,-0x293),'value':_0x47f26e[_0x273757(-0x2a4,-0x28d)][_0x273757(-0x2b1,-0x2a3)](),'inline':!![]},{'name':'Bots','value':_0x47f26e[_0x273757(-0x298,-0x274)][_0x273757(-0x2ac,-0x29d)]['filter'](_0x3894ff=>_0x3894ff[_0x273757(-0x296,-0x286)][_0x273757(-0x290,-0x259)])[_0x273757(-0x25f,-0x257)][_0x273757(-0x281,-0x2a3)](),'inline':!![]});const _0x5c884e=await generateMemberChart(_0x47f26e),_0x4aae9b=new AttachmentBuilder(_0x5c884e,{'name':_0x273757(-0x2c6,-0x29c)});_0x456085['setImage'](_0x273757(-0x247,-0x231)),_0x456085[_0x273757(-0x24e,-0x27c)](),_0x2b691e[_0x273757(-0x2c4,-0x28b)]({'embeds':[_0x456085],'files':[_0x4aae9b]});}catch(_0x3604b0){console[_0x273757(-0x298,-0x290)](_0x273757(-0x236,-0x261),_0x3604b0),_0x2b691e['reply']({'content':_0x273757(-0x23d,-0x25b),'ephemeral':!![]});}}};async function generateMemberChart(_0x395329){const _0xa86266=new Date(),_0xb93737=new Date();_0xb93737[_0x1be3dc(-0x181,-0x187)](_0xa86266['getDate']()-0x7);const _0x3e6301=await _0x395329['members']['fetch']();function _0x1be3dc(_0x3c60f8,_0x201a53){return _0x1349c9(_0x201a53,_0x3c60f8-0x6b);}const _0xdcb9a=_0x3e6301[_0x1be3dc(-0x13d,-0x123)](_0xc4da1d=>_0xc4da1d[_0x1be3dc(-0x133,-0x112)]<_0xb93737)[_0x1be3dc(-0x134,-0x114)],_0xad760a=_0x3e6301[_0x1be3dc(-0x13d,-0x170)](_0x54ba51=>_0x54ba51[_0x1be3dc(-0x133,-0x15c)]>=_0xb93737&&_0x54ba51[_0x1be3dc(-0x133,-0x153)]<=_0xa86266),_0x1a743a=[{'x':formatDate(_0xb93737),'y':_0xdcb9a}],_0x4b2ae8=_0xa86266-_0xb93737,_0x1a330c=_0x4b2ae8/0xa;for(let _0x1c99ad=0x1;_0x1c99ad<=0xa;_0x1c99ad++){const _0x39f654=new Date(_0xb93737[_0x1be3dc(-0x148,-0x17a)]()+_0x1c99ad*_0x1a330c),_0x959320=_0xdcb9a+_0xad760a['filter'](_0x48a081=>_0x48a081[_0x1be3dc(-0x133,-0x136)]<=_0x39f654)['size'];_0x1a743a['push']({'x':formatDate(_0x39f654),'y':Math[_0x1be3dc(-0x15c,-0x151)](_0x959320)});}const _0x292155={'labels':_0x1a743a[_0x1be3dc(-0x126,-0x143)](_0x4f5643=>_0x4f5643['x']),'datasets':[{'label':_0x1be3dc(-0x118,-0x110),'data':_0x1a743a[_0x1be3dc(-0x126,-0x154)](_0x528b0e=>_0x528b0e['y']),'borderColor':_0x1be3dc(-0x154,-0x153),'backgroundColor':'#382bff'}]};return generateChartImage(_0x292155,_0x1be3dc(-0x114,-0x10c));}async function generateChartImage(_0x34e8d9,_0x3ddde4){const _0x3fdb82=0x4b0,_0x2901a0=0x320,_0x27a71d=createCanvas(_0x3fdb82,_0x2901a0),_0x3abf1f=_0x27a71d[_0x4bc274(0x31f,0x325)]('2d'),_0x29e604=(_0x5e60c0,_0xd4f37c)=>{function _0x14bf5d(_0xf7f20a,_0x1ea49f){return _0x4bc274(_0xf7f20a,_0x1ea49f- -0x13f);}const _0x32277c=_0x5e60c0[_0x14bf5d(0x1ae,0x18f)](0x0,0x0,_0xd4f37c['width'],0x0);return _0x32277c[_0x14bf5d(0x1d6,0x1cc)](0x0,_0x14bf5d(0x1c2,0x1d6)),_0x32277c['addColorStop'](0x1,_0x14bf5d(0x16a,0x194)),_0x32277c;},_0x30b88e=_0x29e604(_0x3abf1f,_0x27a71d),_0x6472cb=[{'label':_0x4bc274(0x2f2,0x2d6),'data':_0x34e8d9['datasets'][0x0]['data'],'borderColor':'rgb(255,\x20255,\x20255)','backgroundColor':_0x30b88e,'tension':0.15,'fill':!![]}],_0x44c31b={'type':'line','data':{'labels':_0x34e8d9[_0x4bc274(0x2d4,0x2fd)],'datasets':_0x6472cb},'options':{'scales':{'y':{'beginAtZero':![],'title':{'display':!![],'text':_0x4bc274(0x2ed,0x311),'font':{'size':0x1e,'weight':_0x4bc274(0x2e2,0x2e4)},'color':_0x2184b3=>_0x29e604(_0x2184b3['chart'][_0x4bc274(0x2b5,0x2e1)],_0x2184b3[_0x4bc274(0x34f,0x31b)][_0x4bc274(0x34c,0x328)])},'grid':{'display':![]},'ticks':{'callback':function(_0x3919a5){function _0x107dc1(_0x38cd4d,_0x4a106b){return _0x4bc274(_0x38cd4d,_0x4a106b- -0x107);}return _0x3919a5[_0x107dc1(0x1d9,0x20d)](0x0);},'font':{'size':0x12,'weight':_0x4bc274(0x2c0,0x2e4)},'color':'white'},'offset':!![]},'x':{'title':{'display':!![],'text':_0x4bc274(0x2a4,0x2c8),'font':{'size':0x1e,'weight':_0x4bc274(0x2bd,0x2e4)},'color':_0x537989=>_0x29e604(_0x537989[_0x4bc274(0x358,0x31b)][_0x4bc274(0x2a7,0x2e1)],_0x537989['chart'][_0x4bc274(0x31f,0x328)])},'grid':{'display':![]},'ticks':{'autoSkip':!![],'maxTicksLimit':0x14,'maxRotation':0x0,'minRotation':0x0,'font':{'size':0x12,'weight':'bold'},'color':_0x4bc274(0x2be,0x2db)},'offset':!![]}},'plugins':{'legend':{'display':!![],'labels':{'color':_0x4bc274(0x2be,0x2eb),'font':{'size':0x18,'weight':'bold'}}},'title':{'display':!![],'text':_0x4bc274(0x335,0x31e)+_0x3ddde4+')','font':{'size':0x2c,'weight':'bold'},'color':_0x4bc274(0x2d1,0x2db),'padding':{'top':0xa,'bottom':0x1e}},'tooltip':{'enabled':!![],'callbacks':{'label':function(_0x40f62f){function _0x79cf1c(_0x48690f,_0x3b0481){return _0x4bc274(_0x48690f,_0x3b0481- -0x18c);}return _0x40f62f[_0x79cf1c(0x16b,0x143)]+_0x79cf1c(0x13d,0x170);}},'titleFont':{'size':0x10,'weight':_0x4bc274(0x2cb,0x2e4)},'bodyFont':{'size':0xe,'weight':_0x4bc274(0x313,0x2e4)}}},'responsive':![],'maintainAspectRatio':![],'layout':{'padding':{'top':0x32,'bottom':0x32,'left':0x14,'right':0x32}}},'plugins':[{'id':'backgroundColor','beforeDraw':_0x9bc479=>{const _0x15960c=_0x9bc479[_0x4f646d(-0xc8,-0xba)];function _0x4f646d(_0x2f7579,_0x1f30a9){return _0x4bc274(_0x2f7579,_0x1f30a9- -0x39b);}_0x15960c['save'](),_0x15960c[_0x4f646d(-0x67,-0x74)]=_0x4f646d(-0x61,-0x6f),_0x15960c[_0x4f646d(-0xa1,-0xc6)](0x0,0x0,_0x9bc479[_0x4f646d(-0xb1,-0xc7)],_0x9bc479[_0x4f646d(-0x32,-0x5e)]),_0x15960c['restore']();}},{'id':'dataLabel','afterDatasetsDraw':_0x1cc449=>{const _0x34d358=_0x1cc449[_0x584e87(-0x166,-0x170)],_0x13f6c7=0x0,_0x38c97e=_0x1cc449[_0x584e87(-0x132,-0x14f)][_0x584e87(-0x131,-0x169)][_0x13f6c7];function _0x584e87(_0x1a990b,_0x3b0359){return _0x4bc274(_0x1a990b,_0x3b0359- -0x451);}const _0x16966c=_0x1cc449[_0x584e87(-0x16e,-0x17f)](_0x13f6c7);_0x16966c[_0x584e87(-0x15f,-0x14f)][_0x584e87(-0x16e,-0x16f)]((_0x591cdd,_0x42b1f5)=>{const {x:_0x2ce70f,y:_0x308208}=_0x591cdd[_0x29b2d4(0x2d3,0x2a8)]();function _0x29b2d4(_0x5bba5d,_0x5380fe){return _0x584e87(_0x5bba5d,_0x5380fe-0x3d5);}const _0x28c491=_0x38c97e[_0x29b2d4(0x2b5,0x286)][_0x42b1f5];typeof _0x28c491!==_0x29b2d4(0x2ce,0x2b3)&&(_0x34d358[_0x29b2d4(0x286,0x2ab)]=_0x29b2d4(0x2c4,0x2b9),_0x34d358[_0x29b2d4(0x2e4,0x2bf)](),_0x34d358[_0x29b2d4(0x2c1,0x2c0)](_0x2ce70f,_0x308208,0x1a,0x0,0x2*Math['PI']),_0x34d358[_0x29b2d4(0x2c1,0x2aa)](),_0x34d358[_0x29b2d4(0x2d1,0x2ab)]='white',_0x34d358[_0x29b2d4(0x2ab,0x2b7)]='18px',_0x34d358[_0x29b2d4(0x299,0x2ae)]=_0x29b2d4(0x26b,0x27c),_0x34d358['textBaseline']=_0x29b2d4(0x293,0x289),_0x34d358[_0x29b2d4(0x267,0x278)](Math['round'](_0x28c491)['toString'](),_0x2ce70f,_0x308208));});}}]};function _0x4bc274(_0x407707,_0x36ea9f){return _0x1349c9(_0x407707,_0x36ea9f-0x4b1);}const _0x3eec1d=new Chart(_0x3abf1f,_0x44c31b),_0x27e03e=_0x27a71d[_0x4bc274(0x336,0x33a)]('image/png');return _0x3eec1d['destroy'](),_0x27e03e;}function _0x1349c9(_0x44511b,_0x322487){return _0x555b(_0x322487- -0x266,_0x44511b);}function formatDate(_0x37b0f5){function _0x5ba9c1(_0x1e74a2,_0x54d341){return _0x1349c9(_0x1e74a2,_0x54d341-0x191);}return moment(_0x37b0f5)[_0x5ba9c1(0x2f,0x19)]('D\x20MMM');}