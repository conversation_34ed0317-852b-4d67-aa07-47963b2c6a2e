const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction, client) {
        try {
            // This would be triggered by a custom unwarn command
            // Check if this is an unwarn command interaction
            if (!interaction.isCommand() || interaction.commandName !== 'unwarn') return;
            
            console.log(`✅ Warning removed in ${interaction.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(interaction.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'WARN_REMOVE')) return;
            
            // Get unwarn details from interaction
            const targetUser = interaction.options.getUser('user');
            const warnId = interaction.options.getString('warn_id');
            const reason = interaction.options.getString('reason') || 'Không có lý do';
            const moderator = interaction.user;
            
            // Get warning info before removing
            const warningInfo = await client.db.getWarning(interaction.guild.id, warnId);
            
            // Create event data
            const eventData = {
                eventType: 'WARN_REMOVE',
                user: targetUser.tag,
                userId: targetUser.id,
                moderator: moderator.tag,
                moderatorId: moderator.id,
                action: `Cảnh báo được gỡ bỏ bởi ${moderator.tag}`,
                details: `Cảnh báo của **${targetUser.tag}** đã được gỡ bỏ bởi **${moderator.tag}**`,
                target: targetUser.tag,
                reason: reason,
                timestamp: new Date().toISOString()
            };
            
            // Remove warning from database and get new count
            const newWarnCount = await client.db.removeWarning(interaction.guild.id, warnId);
            await client.db.addAuditLog(interaction.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✅ Cảnh báo được gỡ bỏ',
                `Vừa có cảnh báo được gỡ bỏ`
            );
            
            embed.setColor(0x2ecc71); // Green for removal
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${targetUser.tag} (${targetUser})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${targetUser.id}`,
                    inline: true
                },
                {
                    name: '> Moderator gỡ bỏ',
                    value: `- ${moderator.tag}`,
                    inline: true
                },
                {
                    name: '> Thời gian gỡ bỏ',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> ID cảnh báo',
                    value: `- ${warnId}`,
                    inline: true
                },
                {
                    name: '> Số cảnh báo còn lại',
                    value: `- ${newWarnCount} cảnh báo`,
                    inline: true
                }
            ]);
            
            // Add removal reason
            embed.addFields({
                name: '> Lý do gỡ bỏ',
                value: `- ${reason}`,
                inline: false
            });
            
            // Add original warning info if available
            if (warningInfo) {
                embed.addFields([
                    {
                        name: '> Lý do cảnh báo gốc',
                        value: `- ${warningInfo.reason || 'Không có lý do'}`,
                        inline: false
                    },
                    {
                        name: '> Moderator đã cảnh báo',
                        value: `- ${warningInfo.moderator || 'Unknown'}`,
                        inline: true
                    },
                    {
                        name: '> Thời gian cảnh báo',
                        value: `- <t:${Math.floor(new Date(warningInfo.createdAt).getTime() / 1000)}:F>`,
                        inline: true
                    }
                ]);
                
                // Calculate how long the warning existed
                const warningDuration = Date.now() - new Date(warningInfo.createdAt).getTime();
                const days = Math.floor(warningDuration / (1000 * 60 * 60 * 24));
                const hours = Math.floor((warningDuration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                
                embed.addFields({
                    name: '> Thời gian tồn tại',
                    value: `- ${days} ngày ${hours} giờ`,
                    inline: true
                });
            }
            
            // Add member info
            const member = interaction.guild.members.cache.get(targetUser.id);
            if (member) {
                const accountAge = Date.now() - targetUser.createdTimestamp;
                const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
                
                embed.addFields({
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                });
            }
            
            // Add warning level assessment after removal
            let warningLevel = '';
            if (newWarnCount === 0) {
                warningLevel = '🟢 Không còn cảnh báo';
            } else if (newWarnCount === 1) {
                warningLevel = '🟡 Còn 1 cảnh báo';
            } else if (newWarnCount === 2) {
                warningLevel = '🟠 Còn 2 cảnh báo';
            } else {
                warningLevel = '🔴 Còn nhiều cảnh báo';
            }
            
            embed.addFields({
                name: '> Tình trạng sau gỡ bỏ',
                value: `- ${warningLevel}`,
                inline: true
            });
            
            // Add impact of removal
            embed.addFields({
                name: '> 📝 Tác động của việc gỡ bỏ',
                value: [
                    '• Giảm mức độ theo dõi',
                    '• Cải thiện hồ sơ của thành viên',
                    '• Có thể là dấu hiệu cải thiện hành vi',
                    '• Ghi nhận sự công bằng trong moderation'
                ].join('\n'),
                inline: false
            });
            
            // Add guidelines for warning removal
            embed.addFields({
                name: '> 💡 Lưu ý về gỡ bỏ cảnh báo',
                value: [
                    '• Chỉ gỡ bỏ khi có lý do chính đáng',
                    '• Cân nhắc kỹ trước khi gỡ bỏ',
                    '• Ghi chép lý do rõ ràng',
                    '• Thông báo cho team moderation'
                ].join('\n'),
                inline: false
            });
            
            // Set user avatar as thumbnail
            if (targetUser.displayAvatarURL()) {
                embed.setThumbnail(targetUser.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${interaction.guild.name} • Member Warning Remove`,
                iconURL: interaction.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Warning remove logged: ${targetUser.tag}`);
            
        } catch (error) {
            console.error('Error in warnRemove audit log:', error);
        }
    }
};
