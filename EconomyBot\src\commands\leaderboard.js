    const { SlashCommandBuilder } = require("discord.js");
const User = require("../models/User");
const {
  createLeaderboardEmbed,
  createErrorEmbed,
} = require("../utils/embedBuilder");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("leaderboard")
    .setNameLocalizations({
      vi: "bxh",
    })
    .setDescription("Xem bảng xếp hạng người dùng")
    .addStringOption((option) =>
      option
        .setName("type")
        .setNameLocalizations({
          vi: "loai",
        })
        .setDescription("Loại bảng xếp hạng")
        .setRequired(false)
        .addChoices(
          { name: "<PERSON><PERSON><PERSON>ản (Wealth)", value: "wealth" },
          { name: "Số dư ví (Balance)", value: "balance" },
          { name: "<PERSON><PERSON>àng (Bank)", value: "bank" }
        )
    ),

  cooldown: 10,

  async execute(interaction) {
    try {
      const type = interaction.options.getString("type") || "wealth";

      let users;
      let leaderboardType;

      switch (type) {
        case "wealth":
          users = await User.getLeaderboard(10);
          leaderboardType = "wealth";
          break;
        case "balance":
          users = await getBalanceLeaderboard(10);
          leaderboardType = "balance";
          break;
        case "bank":
          users = await getBankLeaderboard(10);
          leaderboardType = "bank";
          break;
        default:
          users = await User.getLeaderboard(10);
          leaderboardType = "wealth";
      }

      // Tạo embed leaderboard
      const leaderboardEmbed = createLeaderboardEmbed(users, leaderboardType);

      // Tùy chỉnh title và description theo loại
      switch (type) {
        case "balance":
          leaderboardEmbed.setTitle("🏆 Bảng xếp hạng số dư ví");
          leaderboardEmbed.setDescription(
            "Top 10 người dùng có số dư ví cao nhất"
          );
          break;
        case "bank":
          leaderboardEmbed.setTitle("🏦 Bảng xếp hạng ngân hàng");
          leaderboardEmbed.setDescription(
            "Top 10 người dùng có số tiền trong ngân hàng nhiều nhất"
          );
          break;
      }

      // Tìm vị trí của user hiện tại
      const currentUserRank = await getCurrentUserRank(
        interaction.user.id,
        type
      );
      if (currentUserRank) {
        leaderboardEmbed.addFields({
          name: "📍 Vị trí của bạn",
          value: `Hạng ${currentUserRank.rank} với ${currentUserRank.value}`,
          inline: false,
        });
      }

      await interaction.reply({ embeds: [leaderboardEmbed] });
    } catch (error) {
      console.error("Lỗi lệnh leaderboard:", error);

      const errorEmbed = createErrorEmbed(
        "Lỗi bảng xếp hạng",
        "Không thể tải bảng xếp hạng. Vui lòng thử lại sau."
      );

      await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  },
};

async function getCurrentUserRank(userId, type) {
  try {
    const db = require("../database/database");
    let query, valueField;

    switch (type) {
      case "balance":
        query = `
                    SELECT COUNT(*) + 1 as rank, balance as value
                    FROM users 
                    WHERE balance > (SELECT balance FROM users WHERE id = ?)
                `;
        valueField = "balance";
        break;
      case "bank":
        query = `
                    SELECT COUNT(*) + 1 as rank, bank as value
                    FROM users 
                    WHERE bank > (SELECT bank FROM users WHERE id = ?)
                `;
        valueField = "bank";
        break;
      default: // wealth
        query = `
                    SELECT COUNT(*) + 1 as rank, (balance + bank) as value
                    FROM users 
                    WHERE (balance + bank) > (SELECT (balance + bank) FROM users WHERE id = ?)
                `;
        valueField = "total_wealth";
    }

    const result = await db.get(query, [userId]);

    if (result && result.value !== null) {
      const { formatCurrency } = require("../utils/embedBuilder");
      return {
        rank: result.rank,
        value: formatCurrency(result.value),
      };
    }

    return null;
  } catch (error) {
    console.error("Lỗi lấy rank user:", error);
    return null;
  }
}

async function getBalanceLeaderboard(limit) {
  try {
    const db = require("../database/database");
    const users = await db.all(
      `
            SELECT id, username, balance, bank, balance as sort_value
            FROM users
            ORDER BY balance DESC
            LIMIT ?
        `,
      [limit]
    );

    return users;
  } catch (error) {
    console.error("Lỗi lấy balance leaderboard:", error);
    return [];
  }
}

async function getBankLeaderboard(limit) {
  try {
    const db = require("../database/database");
    const users = await db.all(
      `
            SELECT id, username, balance, bank, bank as sort_value
            FROM users
            ORDER BY bank DESC
            LIMIT ?
        `,
      [limit]
    );

    return users;
  } catch (error) {
    console.error("Lỗi lấy bank leaderboard:", error);
    return [];
  }
}
