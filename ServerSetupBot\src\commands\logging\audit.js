// const {
//   SlashCommandBuilder,
//   ChannelType,
//   PermissionFlagsBits,
//   ActionRowBuilder,
//   ButtonBuilder,
//   ButtonStyle,
//   StringSelectMenuBuilder,
// } = require("discord.js");
// const {
//   createSuccessEmbed,
//   createErrorEmbed,
//   createInfoEmbed,
// } = require("../../utils/embedBuilder.js");

// module.exports = {
//   data: new SlashCommandBuilder()
//     .setName("audit")
//     .setDescription("Hệ thống audit log và logging nâng cao")
//     .addSubcommand((subcommand) =>
//       subcommand
//         .setName("setup")
//         .setDescription("Thiết lập hệ thống audit log")
//         .addChannelOption((option) =>
//           option
//             .setName("channel")
//             .setDescription("Kênh nhận audit logs")
//             .setRequired(true)
//             .addChannelTypes(ChannelType.GuildText)
//         )
//         .addStringOption((option) =>
//           option
//             .setName("events")
//             .setDescription("Loại sự kiện cần log")
//             .setRequired(false)
//             .addChoices(
//               { name: "🔧 All - Tất cả sự kiện", value: "all" },
//               { name: "👥 Members - Thành viên", value: "members" },
//               { name: "📝 Messages - Tin nhắn", value: "messages" },
//               { name: "🎭 Roles - Roles", value: "roles" },
//               { name: "📁 Channels - Kênh", value: "channels" },
//               { name: "⚙️ Server - Server settings", value: "server" },
//               { name: "🔨 Moderation - Kiểm duyệt", value: "moderation" },
//               { name: "🎵 Voice - Voice channels", value: "voice" },
//               { name: "🔗 Invites - Lời mời", value: "invites" },
//               { name: "🎨 Emojis - Emoji/Stickers", value: "emojis" }
//             )
//         )
//         .addBooleanOption((option) =>
//           option
//             .setName("include_bots")
//             .setDescription("Bao gồm hoạt động của bot")
//             .setRequired(false)
//         )
//         .addBooleanOption((option) =>
//           option
//             .setName("detailed_logs")
//             .setDescription("Log chi tiết (bao gồm before/after)")
//             .setRequired(false)
//         )
//         .addBooleanOption((option) =>
//           option
//             .setName("webhook_mode")
//             .setDescription("Sử dụng webhook để gửi logs")
//             .setRequired(false)
//         )
//     )
//     .addSubcommand((subcommand) =>
//       subcommand.setName("disable").setDescription("Tắt hệ thống audit log")
//     )
//     .addSubcommand((subcommand) =>
//       subcommand
//         .setName("config")
//         .setDescription("Xem cấu hình audit log hiện tại")
//     )
//     .addSubcommand((subcommand) =>
//       subcommand
//         .setName("events")
//         .setDescription("Quản lý các sự kiện được log")
//         .addStringOption((option) =>
//           option
//             .setName("action")
//             .setDescription("Hành động")
//             .setRequired(true)
//             .addChoices(
//               { name: "Enable - Bật sự kiện", value: "enable" },
//               { name: "Disable - Tắt sự kiện", value: "disable" },
//               { name: "List - Xem danh sách", value: "list" }
//             )
//         )
//         .addStringOption((option) =>
//           option
//             .setName("event_type")
//             .setDescription("Loại sự kiện")
//             .setRequired(false)
//             .addChoices(
//               { name: "Member Join", value: "memberAdd" },
//               { name: "Member Leave", value: "memberRemove" },
//               { name: "Member Update", value: "memberUpdate" },
//               { name: "Message Delete", value: "messageDelete" },
//               { name: "Message Edit", value: "messageUpdate" },
//               { name: "Message Bulk Delete", value: "messageDeleteBulk" },
//               { name: "Role Create", value: "roleCreate" },
//               { name: "Role Delete", value: "roleDelete" },
//               { name: "Role Update", value: "roleUpdate" },
//               { name: "Channel Create", value: "channelCreate" },
//               { name: "Channel Delete", value: "channelDelete" },
//               { name: "Channel Update", value: "channelUpdate" },
//               { name: "Ban Add", value: "banAdd" },
//               { name: "Ban Remove", value: "banRemove" },
//               { name: "Voice State Update", value: "voiceStateUpdate" },
//               { name: "Invite Create", value: "inviteCreate" },
//               { name: "Invite Delete", value: "inviteDelete" },
//               { name: "Emoji Create", value: "emojiCreate" },
//               { name: "Emoji Delete", value: "emojiDelete" },
//               { name: "Emoji Update", value: "emojiUpdate" }
//             )
//         )
//     )
//     .addSubcommand((subcommand) =>
//       subcommand
//         .setName("channels")
//         .setDescription("Quản lý kênh log cho từng loại sự kiện")
//         .addStringOption((option) =>
//           option
//             .setName("event_category")
//             .setDescription("Danh mục sự kiện")
//             .setRequired(true)
//             .addChoices(
//               { name: "Members", value: "members" },
//               { name: "Messages", value: "messages" },
//               { name: "Roles", value: "roles" },
//               { name: "Channels", value: "channels" },
//               { name: "Moderation", value: "moderation" },
//               { name: "Voice", value: "voice" },
//               { name: "Server", value: "server" }
//             )
//         )
//         .addChannelOption((option) =>
//           option
//             .setName("channel")
//             .setDescription("Kênh log cho danh mục này")
//             .setRequired(true)
//             .addChannelTypes(ChannelType.GuildText)
//         )
//     )
//     .addSubcommand((subcommand) =>
//       subcommand
//         .setName("ignore")
//         .setDescription("Quản lý danh sách bỏ qua")
//         .addStringOption((option) =>
//           option
//             .setName("type")
//             .setDescription("Loại đối tượng bỏ qua")
//             .setRequired(true)
//             .addChoices(
//               { name: "Channel", value: "channel" },
//               { name: "Role", value: "role" },
//               { name: "User", value: "user" }
//             )
//         )
//         .addStringOption((option) =>
//           option
//             .setName("action")
//             .setDescription("Hành động")
//             .setRequired(true)
//             .addChoices(
//               { name: "Add - Thêm vào danh sách bỏ qua", value: "add" },
//               { name: "Remove - Xóa khỏi danh sách", value: "remove" },
//               { name: "List - Xem danh sách", value: "list" }
//             )
//         )
//         .addStringOption((option) =>
//           option
//             .setName("target")
//             .setDescription("ID của channel/role/user (chỉ cho add/remove)")
//             .setRequired(false)
//         )
//     )
//     .addSubcommand((subcommand) =>
//       subcommand
//         .setName("stats")
//         .setDescription("Xem thống kê audit log")
//         .addStringOption((option) =>
//           option
//             .setName("period")
//             .setDescription("Khoảng thời gian")
//             .setRequired(false)
//             .addChoices(
//               { name: "Hôm nay", value: "today" },
//               { name: "7 ngày qua", value: "week" },
//               { name: "30 ngày qua", value: "month" },
//               { name: "Tất cả", value: "all" }
//             )
//         )
//     )
//     .addSubcommand((subcommand) =>
//       subcommand
//         .setName("search")
//         .setDescription("Tìm kiếm trong audit log")
//         .addStringOption((option) =>
//           option
//             .setName("query")
//             .setDescription("Từ khóa tìm kiếm")
//             .setRequired(true)
//         )
//         .addStringOption((option) =>
//           option
//             .setName("event_type")
//             .setDescription("Loại sự kiện")
//             .setRequired(false)
//         )
//         .addUserOption((option) =>
//           option
//             .setName("user")
//             .setDescription("Người dùng cụ thể")
//             .setRequired(false)
//         )
//         .addIntegerOption((option) =>
//           option
//             .setName("days")
//             .setDescription("Số ngày tìm kiếm (1-30)")
//             .setRequired(false)
//             .setMinValue(1)
//             .setMaxValue(30)
//         )
//     )
//     .addSubcommand((subcommand) =>
//       subcommand
//         .setName("export")
//         .setDescription("Xuất audit log ra file")
//         .addStringOption((option) =>
//           option
//             .setName("format")
//             .setDescription("Định dạng file")
//             .setRequired(false)
//             .addChoices(
//               { name: "JSON", value: "json" },
//               { name: "CSV", value: "csv" },
//               { name: "TXT", value: "txt" }
//             )
//         )
//         .addIntegerOption((option) =>
//           option
//             .setName("days")
//             .setDescription("Số ngày xuất (1-90)")
//             .setRequired(false)
//             .setMinValue(1)
//             .setMaxValue(90)
//         )
//     )
//     .addSubcommand((subcommand) =>
//       subcommand
//         .setName("alerts")
//         .setDescription("Thiết lập cảnh báo tự động")
//         .addStringOption((option) =>
//           option
//             .setName("alert_type")
//             .setDescription("Loại cảnh báo")
//             .setRequired(true)
//             .addChoices(
//               { name: "Spam Actions", value: "spam_actions" },
//               { name: "Mass Delete", value: "mass_delete" },
//               { name: "Permission Changes", value: "permission_changes" },
//               { name: "Suspicious Activity", value: "suspicious_activity" },
//               { name: "Failed Actions", value: "failed_actions" }
//             )
//         )
//         .addBooleanOption((option) =>
//           option
//             .setName("enabled")
//             .setDescription("Bật/tắt cảnh báo")
//             .setRequired(true)
//         )
//         .addIntegerOption((option) =>
//           option
//             .setName("threshold")
//             .setDescription("Ngưỡng kích hoạt cảnh báo")
//             .setRequired(false)
//             .setMinValue(1)
//             .setMaxValue(100)
//         )
//         .addChannelOption((option) =>
//           option
//             .setName("alert_channel")
//             .setDescription("Kênh nhận cảnh báo")
//             .setRequired(false)
//             .addChannelTypes(ChannelType.GuildText)
//         )
//     )
//     .addSubcommand((subcommand) =>
//       subcommand
//         .setName("realtime")
//         .setDescription("Xem audit logs thời gian thực")
//         .addStringOption((option) =>
//           option
//             .setName("event_filter")
//             .setDescription("Lọc sự kiện")
//             .setRequired(false)
//             .addChoices(
//               { name: "Tất cả", value: "all" },
//               { name: "High Priority", value: "high" },
//               { name: "Medium Priority", value: "medium" },
//               { name: "Low Priority", value: "low" }
//             )
//         )
//         .addIntegerOption((option) =>
//           option
//             .setName("duration")
//             .setDescription("Thời gian theo dõi (phút)")
//             .setRequired(false)
//             .setMinValue(1)
//             .setMaxValue(60)
//         )
//     )
//     .addSubcommand((subcommand) =>
//       subcommand
//         .setName("analytics")
//         .setDescription("Phân tích và thống kê audit logs")
//         .addStringOption((option) =>
//           option
//             .setName("report_type")
//             .setDescription("Loại báo cáo")
//             .setRequired(true)
//             .addChoices(
//               { name: "Activity Summary", value: "activity" },
//               { name: "User Activity", value: "user_activity" },
//               { name: "Risk Assessment", value: "risk" },
//               { name: "Trend Analysis", value: "trends" },
//               { name: "Performance Report", value: "performance" }
//             )
//         )
//         .addIntegerOption((option) =>
//           option
//             .setName("period")
//             .setDescription("Khoảng thời gian (ngày)")
//             .setRequired(false)
//             .setMinValue(1)
//             .setMaxValue(365)
//         )
//         .addUserOption((option) =>
//           option
//             .setName("target_user")
//             .setDescription("User cụ thể (cho user activity report)")
//             .setRequired(false)
//         )
//     )
//     .addSubcommand((subcommand) =>
//       subcommand
//         .setName("filter")
//         .setDescription("Thiết lập bộ lọc sự kiện chi tiết")
//         .addStringOption((option) =>
//           option
//             .setName("action")
//             .setDescription("Hành động")
//             .setRequired(true)
//             .addChoices(
//               { name: "Setup - Thiết lập bộ lọc", value: "setup" },
//               { name: "View - Xem cấu hình hiện tại", value: "view" },
//               { name: "Reset - Reset về mặc định", value: "reset" }
//             )
//         )
//     )
//     .addSubcommand((subcommand) =>
//       subcommand
//         .setName("templates")
//         .setDescription("Sử dụng template cấu hình có sẵn")
//         .addStringOption((option) =>
//           option
//             .setName("template")
//             .setDescription("Template cấu hình")
//             .setRequired(true)
//             .addChoices(
//               {
//                 name: "🔒 Security Focus - Tập trung bảo mật",
//                 value: "security",
//               },
//               {
//                 name: "👥 Community Focus - Tập trung cộng đồng",
//                 value: "community",
//               },
//               { name: "🎮 Gaming Focus - Tập trung gaming", value: "gaming" },
//               {
//                 name: "💼 Business Focus - Tập trung doanh nghiệp",
//                 value: "business",
//               },
//               {
//                 name: "🎨 Creative Focus - Tập trung sáng tạo",
//                 value: "creative",
//               }
//             )
//         )
//         .addChannelOption((option) =>
//           option
//             .setName("main_channel")
//             .setDescription("Kênh log chính")
//             .setRequired(true)
//             .addChannelTypes(ChannelType.GuildText)
//         )
//     ),
//   category: "logging",
//   adminOnly: true,
//   manageServer: false,

//   async execute(interaction, client) {
//     // Kiểm tra quyền bot
//     if (
//       !interaction.guild.members.me.permissions.has([
//         PermissionFlagsBits.ViewAuditLog,
//         PermissionFlagsBits.ManageWebhooks,
//         PermissionFlagsBits.ViewChannel,
//         PermissionFlagsBits.SendMessages,
//       ])
//     ) {
//       const errorEmbed = createErrorEmbed(
//         "Bot thiếu quyền!",
//         "Bot cần các quyền sau:\n• View Audit Log\n• Manage Webhooks\n• View Channel\n• Send Messages"
//       );
//       return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
//     }

//     const subcommand = interaction.options.getSubcommand();
//     const guildId = interaction.guild.id;

//     switch (subcommand) {
//       case "setup":
//         await handleSetup(interaction, client, guildId);
//         break;
//       case "disable":
//         await handleDisable(interaction, client, guildId);
//         break;
//       case "config":
//         await handleConfig(interaction, client, guildId);
//         break;
//       case "events":
//         await handleEvents(interaction, client, guildId);
//         break;
//       case "channels":
//         await handleChannels(interaction, client, guildId);
//         break;
//       case "ignore":
//         await handleIgnore(interaction, client, guildId);
//         break;
//       case "stats":
//         await handleStats(interaction, client, guildId);
//         break;
//       case "search":
//         await handleSearch(interaction, client, guildId);
//         break;
//       case "export":
//         await handleExport(interaction, client, guildId);
//         break;
//       case "alerts":
//         await handleAlerts(interaction, client, guildId);
//         break;
//       case "realtime":
//         await handleRealtime(interaction, client, guildId);
//         break;
//       case "analytics":
//         await handleAnalytics(interaction, client, guildId);
//         break;
//       case "filter":
//         await handleFilter(interaction, client, guildId);
//         break;
//       case "templates":
//         await handleTemplates(interaction, client, guildId);
//         break;
//     }
//   },
// };

// async function handleSetup(interaction, client, guildId) {
//   const channel = interaction.options.getChannel("channel");
//   const events = interaction.options.getString("events") || "all";
//   const includeBots = interaction.options.getBoolean("include_bots") || false;
//   const detailedLogs = interaction.options.getBoolean("detailed_logs") || false;
//   const webhookMode = interaction.options.getBoolean("webhook_mode") || false;

//   try {
//     // Tạo webhook nếu cần
//     let webhookId = null;
//     if (webhookMode) {
//       const webhook = await channel.createWebhook({
//         name: "Audit Log Bot",
//         avatar: client.user.displayAvatarURL(),
//         reason: "Audit log webhook",
//       });
//       webhookId = webhook.id;
//     }

//     // Lưu cấu hình vào database
//     await client.db.updateAuditConfig(guildId, {
//       enabled: 1,
//       log_channel_id: channel.id,
//       events: events,
//       include_bots: includeBots ? 1 : 0,
//       detailed_logs: detailedLogs ? 1 : 0,
//       webhook_mode: webhookMode ? 1 : 0,
//       webhook_id: webhookId,
//     });

//     const successEmbed = createSuccessEmbed(
//       "Hệ thống audit log đã được thiết lập!",
//       `**Kênh log:** ${channel}\n` +
//         `**Sự kiện:** ${getEventDisplayName(events)}\n` +
//         `**Bao gồm bot:** ${includeBots ? "Có" : "Không"}\n` +
//         `**Log chi tiết:** ${detailedLogs ? "Có" : "Không"}\n` +
//         `**Webhook mode:** ${webhookMode ? "Có" : "Không"}`
//     );

//     successEmbed.addFields({
//       name: "📝 Bước tiếp theo",
//       value:
//         "• Sử dụng `/audit events` để quản lý sự kiện cụ thể\n" +
//         "• Sử dụng `/audit channels` để thiết lập kênh riêng cho từng loại\n" +
//         "• Sử dụng `/audit ignore` để bỏ qua channel/role/user cụ thể",
//       inline: false,
//     });

//     await interaction.reply({ embeds: [successEmbed] });
//   } catch (error) {
//     console.error("Lỗi khi thiết lập audit log:", error);
//     const errorEmbed = createErrorEmbed(
//       "Lỗi hệ thống!",
//       "Không thể thiết lập hệ thống audit log. Vui lòng thử lại sau!"
//     );
//     await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
//   }
// }

// function getEventDisplayName(event) {
//   const events = {
//     all: "🔧 Tất cả sự kiện",
//     members: "👥 Thành viên",
//     messages: "📝 Tin nhắn",
//     roles: "🎭 Roles",
//     channels: "📁 Kênh",
//     server: "⚙️ Server settings",
//     moderation: "🔨 Kiểm duyệt",
//     voice: "🎵 Voice channels",
//     invites: "🔗 Lời mời",
//     emojis: "🎨 Emoji/Stickers",
//   };
//   return events[event] || event;
// }

// // Handler for alerts subcommand
// async function handleAlerts(interaction, client, guildId) {
//   const alertType = interaction.options.getString("alert_type");
//   const enabled = interaction.options.getBoolean("enabled");
//   const threshold =
//     interaction.options.getInteger("threshold") ||
//     getDefaultThreshold(alertType);
//   const alertChannel = interaction.options.getChannel("alert_channel");

//   try {
//     // Lưu cấu hình alert vào database
//     await client.db.updateAuditAlert(guildId, {
//       alert_type: alertType,
//       enabled: enabled ? 1 : 0,
//       threshold: threshold,
//       alert_channel_id: alertChannel?.id || null,
//     });

//     const alertNames = {
//       spam_actions: "Spam Actions",
//       mass_delete: "Mass Delete",
//       permission_changes: "Permission Changes",
//       suspicious_activity: "Suspicious Activity",
//       failed_actions: "Failed Actions",
//     };

//     const embed = enabled
//       ? createSuccessEmbed(
//           "🚨 Cảnh báo đã được bật!",
//           `**Loại:** ${alertNames[alertType]}\n` +
//             `**Ngưỡng:** ${threshold}\n` +
//             `**Kênh cảnh báo:** ${alertChannel || "Kênh audit log chính"}\n\n` +
//             `Hệ thống sẽ tự động gửi cảnh báo khi phát hiện hoạt động bất thường.`
//         )
//       : createSuccessEmbed(
//           "🔕 Cảnh báo đã được tắt!",
//           `Cảnh báo **${alertNames[alertType]}** đã được vô hiệu hóa.`
//         );

//     if (enabled) {
//       embed.addFields({
//         name: "📋 Mô tả cảnh báo",
//         value: getAlertDescription(alertType),
//         inline: false,
//       });

//       embed.addFields({
//         name: "⚙️ Cấu hình",
//         value:
//           `• Sử dụng \`/audit alerts\` để thay đổi cài đặt\n` +
//           `• Kiểm tra \`/audit analytics\` để xem báo cáo rủi ro\n` +
//           `• Xem \`/audit realtime\` để theo dõi trực tiếp`,
//         inline: false,
//       });
//     }

//     await interaction.reply({ embeds: [embed] });
//   } catch (error) {
//     console.error("Lỗi khi thiết lập cảnh báo:", error);
//     const errorEmbed = createErrorEmbed(
//       "Lỗi thiết lập cảnh báo!",
//       "Không thể thiết lập cảnh báo. Vui lòng thử lại sau!"
//     );
//     await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
//   }
// }

// function getDefaultThreshold(alertType) {
//   const thresholds = {
//     spam_actions: 10,
//     mass_delete: 20,
//     permission_changes: 5,
//     suspicious_activity: 3,
//     failed_actions: 15,
//   };
//   return thresholds[alertType] || 10;
// }

// function getAlertDescription(alertType) {
//   const descriptions = {
//     spam_actions:
//       "Phát hiện khi có quá nhiều hành động liên tiếp từ cùng một user trong thời gian ngắn",
//     mass_delete:
//       "Cảnh báo khi có nhiều tin nhắn bị xóa cùng lúc hoặc trong thời gian ngắn",
//     permission_changes:
//       "Theo dõi thay đổi quyền hạn quan trọng của roles hoặc channels",
//     suspicious_activity:
//       "Phát hiện hoạt động bất thường như tạo/xóa nhiều channels/roles",
//     failed_actions:
//       "Cảnh báo khi có nhiều hành động thất bại (có thể là dấu hiệu tấn công)",
//   };
//   return descriptions[alertType] || "Mô tả không có sẵn";
// }

// // Handler for realtime monitoring
// async function handleRealtime(interaction, client, guildId) {
//   const eventFilter = interaction.options.getString("event_filter") || "all";
//   const duration = interaction.options.getInteger("duration") || 10;

//   try {
//     const embed = createInfoEmbed(
//       "📡 Theo dõi Audit Log thời gian thực",
//       `**Filter:** ${getFilterDisplayName(eventFilter)}\n` +
//         `**Thời gian:** ${duration} phút\n` +
//         `**Trạng thái:** Đang theo dõi...\n\n` +
//         `Hệ thống sẽ hiển thị các sự kiện audit log trong thời gian thực.`
//     );

//     embed.addFields({
//       name: "🎛️ Điều khiển",
//       value:
//         "• Nhấn 🛑 để dừng theo dõi\n" +
//         "• Nhấn ⏸️ để tạm dừng\n" +
//         "• Nhấn ▶️ để tiếp tục\n" +
//         "• Nhấn 📊 để xem thống kê",
//       inline: false,
//     });

//     const row = new ActionRowBuilder().addComponents(
//       new ButtonBuilder()
//         .setCustomId("realtime_stop")
//         .setLabel("Dừng")
//         .setStyle(ButtonStyle.Danger)
//         .setEmoji("🛑"),
//       new ButtonBuilder()
//         .setCustomId("realtime_pause")
//         .setLabel("Tạm dừng")
//         .setStyle(ButtonStyle.Secondary)
//         .setEmoji("⏸️"),
//       new ButtonBuilder()
//         .setCustomId("realtime_resume")
//         .setLabel("Tiếp tục")
//         .setStyle(ButtonStyle.Success)
//         .setEmoji("▶️"),
//       new ButtonBuilder()
//         .setCustomId("realtime_stats")
//         .setLabel("Thống kê")
//         .setStyle(ButtonStyle.Primary)
//         .setEmoji("📊")
//     );

//     await interaction.reply({ embeds: [embed], components: [row] });

//     // Start realtime monitoring
//     const monitoringData = {
//       guildId: guildId,
//       channelId: interaction.channel.id,
//       userId: interaction.user.id,
//       filter: eventFilter,
//       startTime: Date.now(),
//       duration: duration * 60 * 1000,
//       events: [],
//       paused: false,
//     };

//     // Store monitoring session
//     client.realtimeMonitors = client.realtimeMonitors || new Map();
//     client.realtimeMonitors.set(interaction.user.id, monitoringData);

//     // Set up collector for buttons
//     const collector = interaction.channel.createMessageComponentCollector({
//       filter: (i) => i.user.id === interaction.user.id,
//       time: duration * 60 * 1000,
//     });

//     collector.on("collect", async (i) => {
//       const monitor = client.realtimeMonitors.get(interaction.user.id);
//       if (!monitor) return;

//       switch (i.customId) {
//         case "realtime_stop":
//           collector.stop("stopped");
//           break;
//         case "realtime_pause":
//           monitor.paused = true;
//           await i.reply({
//             content: "⏸️ Đã tạm dừng theo dõi.",
//             ephemeral: true,
//           });
//           break;
//         case "realtime_resume":
//           monitor.paused = false;
//           await i.reply({
//             content: "▶️ Đã tiếp tục theo dõi.",
//             ephemeral: true,
//           });
//           break;
//         case "realtime_stats":
//           await showRealtimeStats(i, monitor);
//           break;
//       }
//     });

//     collector.on("end", async (collected, reason) => {
//       const monitor = client.realtimeMonitors.get(interaction.user.id);
//       if (monitor) {
//         const finalEmbed = createSuccessEmbed(
//           "📡 Theo dõi hoàn thành",
//           `**Thời gian:** ${Math.round(
//             (Date.now() - monitor.startTime) / 1000 / 60
//           )} phút\n` +
//             `**Sự kiện ghi nhận:** ${monitor.events.length}\n` +
//             `**Lý do kết thúc:** ${
//               reason === "stopped" ? "Dừng thủ công" : "Hết thời gian"
//             }`
//         );

//         await interaction.editReply({ embeds: [finalEmbed], components: [] });
//         client.realtimeMonitors.delete(interaction.user.id);
//       }
//     });
//   } catch (error) {
//     console.error("Lỗi khi theo dõi realtime:", error);
//     const errorEmbed = createErrorEmbed(
//       "Lỗi theo dõi realtime!",
//       "Không thể bắt đầu theo dõi thời gian thực. Vui lòng thử lại sau!"
//     );
//     await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
//   }
// }

// async function showRealtimeStats(interaction, monitor) {
//   const eventCounts = {};
//   monitor.events.forEach((event) => {
//     eventCounts[event.type] = (eventCounts[event.type] || 0) + 1;
//   });

//   const statsText =
//     Object.entries(eventCounts)
//       .sort(([, a], [, b]) => b - a)
//       .slice(0, 10)
//       .map(([type, count]) => `• **${type}:** ${count}`)
//       .join("\n") || "Chưa có sự kiện nào";

//   const embed = createInfoEmbed(
//     "📊 Thống kê theo dõi",
//     `**Thời gian chạy:** ${Math.round(
//       (Date.now() - monitor.startTime) / 1000 / 60
//     )} phút\n` +
//       `**Tổng sự kiện:** ${monitor.events.length}\n` +
//       `**Trạng thái:** ${monitor.paused ? "Tạm dừng" : "Đang chạy"}\n\n` +
//       `**Top sự kiện:**\n${statsText}`
//   );

//   await interaction.reply({ embeds: [embed], ephemeral: true });
// }

// function getFilterDisplayName(filter) {
//   const filters = {
//     all: "Tất cả sự kiện",
//     high: "Ưu tiên cao",
//     medium: "Ưu tiên trung bình",
//     low: "Ưu tiên thấp",
//   };
//   return filters[filter] || filter;
// }

// // Handler for analytics subcommand
// async function handleAnalytics(interaction, client, guildId) {
//   const reportType = interaction.options.getString("report_type");
//   const period = interaction.options.getInteger("period") || 30;
//   const targetUser = interaction.options.getUser("target_user");

//   await interaction.deferReply();

//   try {
//     switch (reportType) {
//       case "activity":
//         await generateActivityReport(interaction, client, guildId, period);
//         break;
//       case "user_activity":
//         await generateUserActivityReport(
//           interaction,
//           client,
//           guildId,
//           period,
//           targetUser
//         );
//         break;
//       case "risk":
//         await generateRiskAssessment(interaction, client, guildId, period);
//         break;
//       case "trends":
//         await generateTrendAnalysis(interaction, client, guildId, period);
//         break;
//       case "performance":
//         await generatePerformanceReport(interaction, client, guildId, period);
//         break;
//     }
//   } catch (error) {
//     console.error("Lỗi khi tạo báo cáo analytics:", error);
//     const errorEmbed = createErrorEmbed(
//       "Lỗi tạo báo cáo!",
//       "Không thể tạo báo cáo analytics. Vui lòng thử lại sau!"
//     );
//     await interaction.editReply({ embeds: [errorEmbed] });
//   }
// }

// async function generateActivityReport(interaction, client, guildId, period) {
//   // Lấy dữ liệu audit logs từ database
//   const logs = await client.db.getAuditLogs(guildId, { days: period });

//   // Phân tích dữ liệu
//   const eventCounts = {};
//   const dailyActivity = {};
//   const userActivity = {};

//   logs.forEach((log) => {
//     // Count events by type
//     eventCounts[log.action] = (eventCounts[log.action] || 0) + 1;

//     // Count daily activity
//     const date = new Date(log.created_at).toDateString();
//     dailyActivity[date] = (dailyActivity[date] || 0) + 1;

//     // Count user activity
//     if (log.executor_id) {
//       userActivity[log.executor_id] = (userActivity[log.executor_id] || 0) + 1;
//     }
//   });

//   const embed = createInfoEmbed(
//     "📊 Báo cáo hoạt động Server",
//     `**Khoảng thời gian:** ${period} ngày\n` +
//       `**Tổng sự kiện:** ${logs.length}\n` +
//       `**Trung bình/ngày:** ${Math.round(logs.length / period)}`
//   );

//   // Top events
//   const topEvents =
//     Object.entries(eventCounts)
//       .sort(([, a], [, b]) => b - a)
//       .slice(0, 10)
//       .map(([event, count]) => `• **${event}:** ${count}`)
//       .join("\n") || "Không có dữ liệu";

//   embed.addFields({
//     name: "🔥 Top sự kiện",
//     value: topEvents,
//     inline: true,
//   });

//   // Most active users
//   const topUsers =
//     Object.entries(userActivity)
//       .sort(([, a], [, b]) => b - a)
//       .slice(0, 5)
//       .map(([userId, count]) => `• <@${userId}>: ${count}`)
//       .join("\n") || "Không có dữ liệu";

//   embed.addFields({
//     name: "👥 User hoạt động nhất",
//     value: topUsers,
//     inline: true,
//   });

//   // Activity trend
//   const recentDays =
//     Object.entries(dailyActivity)
//       .slice(-7)
//       .map(
//         ([date, count]) =>
//           `• ${new Date(date).toLocaleDateString("vi-VN")}: ${count}`
//       )
//       .join("\n") || "Không có dữ liệu";

//   embed.addFields({
//     name: "📈 Hoạt động 7 ngày gần đây",
//     value: recentDays,
//     inline: false,
//   });

//   await interaction.editReply({ embeds: [embed] });
// }

// async function generateUserActivityReport(
//   interaction,
//   client,
//   guildId,
//   period,
//   targetUser
// ) {
//   if (!targetUser) {
//     const errorEmbed = createErrorEmbed(
//       "Thiếu thông tin!",
//       "Vui lòng chọn user để tạo báo cáo hoạt động cá nhân."
//     );
//     return await interaction.editReply({ embeds: [errorEmbed] });
//   }

//   const logs = await client.db.getAuditLogs(guildId, {
//     days: period,
//     executorId: targetUser.id,
//   });

//   const actionCounts = {};
//   const dailyActivity = {};

//   logs.forEach((log) => {
//     actionCounts[log.action] = (actionCounts[log.action] || 0) + 1;

//     const date = new Date(log.created_at).toDateString();
//     dailyActivity[date] = (dailyActivity[date] || 0) + 1;
//   });

//   const embed = createInfoEmbed(
//     `👤 Báo cáo hoạt động - ${targetUser.tag}`,
//     `**Khoảng thời gian:** ${period} ngày\n` +
//       `**Tổng hành động:** ${logs.length}\n` +
//       `**Trung bình/ngày:** ${Math.round(logs.length / period)}`
//   );

//   embed.setThumbnail(targetUser.displayAvatarURL({ dynamic: true }));

//   // User actions
//   const actions =
//     Object.entries(actionCounts)
//       .sort(([, a], [, b]) => b - a)
//       .slice(0, 10)
//       .map(([action, count]) => `• **${action}:** ${count}`)
//       .join("\n") || "Không có hoạt động";

//   embed.addFields({
//     name: "🎯 Hành động thực hiện",
//     value: actions,
//     inline: false,
//   });

//   // Risk assessment for this user
//   const riskScore = calculateUserRiskScore(logs, period);
//   const riskLevel = getRiskLevel(riskScore);

//   embed.addFields({
//     name: "⚠️ Đánh giá rủi ro",
//     value: `**Điểm số:** ${riskScore}/100\n**Mức độ:** ${riskLevel}`,
//     inline: true,
//   });

//   await interaction.editReply({ embeds: [embed] });
// }

// async function generateRiskAssessment(interaction, client, guildId, period) {
//   const logs = await client.db.getAuditLogs(guildId, { days: period });

//   // Analyze risk factors
//   const riskFactors = {
//     massActions: 0,
//     permissionChanges: 0,
//     suspiciousPatterns: 0,
//     failedActions: 0,
//     unusualTiming: 0,
//   };

//   // Analyze logs for risk patterns
//   const userActions = {};
//   logs.forEach((log) => {
//     if (!userActions[log.executor_id]) {
//       userActions[log.executor_id] = [];
//     }
//     userActions[log.executor_id].push(log);
//   });

//   // Check for mass actions
//   Object.values(userActions).forEach((actions) => {
//     const timeWindows = {};
//     actions.forEach((action) => {
//       const timeWindow = Math.floor(
//         new Date(action.created_at).getTime() / (5 * 60 * 1000)
//       ); // 5-minute windows
//       timeWindows[timeWindow] = (timeWindows[timeWindow] || 0) + 1;
//     });

//     Object.values(timeWindows).forEach((count) => {
//       if (count > 10) riskFactors.massActions++;
//     });
//   });

//   // Check for permission changes
//   riskFactors.permissionChanges = logs.filter(
//     (log) =>
//       log.action.includes("ROLE_UPDATE") ||
//       log.action.includes("CHANNEL_OVERWRITE")
//   ).length;

//   const totalRiskScore = Object.values(riskFactors).reduce(
//     (sum, score) => sum + score,
//     0
//   );
//   const riskLevel = getRiskLevel(totalRiskScore);

//   const embed = createInfoEmbed(
//     "🛡️ Đánh giá rủi ro Server",
//     `**Khoảng thời gian:** ${period} ngày\n` +
//       `**Điểm rủi ro tổng:** ${totalRiskScore}\n` +
//       `**Mức độ rủi ro:** ${riskLevel}`
//   );

//   embed.addFields({
//     name: "📊 Phân tích rủi ro",
//     value:
//       `• **Mass Actions:** ${riskFactors.massActions}\n` +
//       `• **Permission Changes:** ${riskFactors.permissionChanges}\n` +
//       `• **Suspicious Patterns:** ${riskFactors.suspiciousPatterns}\n` +
//       `• **Failed Actions:** ${riskFactors.failedActions}\n` +
//       `• **Unusual Timing:** ${riskFactors.unusualTiming}`,
//     inline: false,
//   });

//   // Recommendations
//   const recommendations = generateSecurityRecommendations(riskFactors);
//   if (recommendations.length > 0) {
//     embed.addFields({
//       name: "💡 Khuyến nghị",
//       value: recommendations.join("\n"),
//       inline: false,
//     });
//   }

//   await interaction.editReply({ embeds: [embed] });
// }

// function calculateUserRiskScore(logs, period) {
//   let score = 0;

//   // High frequency actions
//   if (logs.length > period * 10) score += 20;

//   // Permission-related actions
//   const permissionActions = logs.filter(
//     (log) => log.action.includes("ROLE") || log.action.includes("PERMISSION")
//   ).length;
//   score += Math.min(permissionActions * 5, 30);

//   // Deletion actions
//   const deleteActions = logs.filter((log) =>
//     log.action.includes("DELETE")
//   ).length;
//   score += Math.min(deleteActions * 3, 25);

//   return Math.min(score, 100);
// }

// function getRiskLevel(score) {
//   if (score >= 70) return "🔴 Cao";
//   if (score >= 40) return "🟡 Trung bình";
//   if (score >= 20) return "🟠 Thấp";
//   return "🟢 An toàn";
// }

// function generateSecurityRecommendations(riskFactors) {
//   const recommendations = [];

//   if (riskFactors.massActions > 5) {
//     recommendations.push(
//       "• Thiết lập rate limiting cho các hành động quan trọng"
//     );
//   }

//   if (riskFactors.permissionChanges > 10) {
//     recommendations.push("• Xem xét lại quyền hạn của các role quan trọng");
//     recommendations.push("• Thiết lập cảnh báo cho thay đổi quyền hạn");
//   }

//   if (riskFactors.failedActions > 20) {
//     recommendations.push(
//       "• Kiểm tra log để phát hiện các cuộc tấn công tiềm ẩn"
//     );
//   }

//   return recommendations;
// }

// // Handler for filter subcommand
// async function handleFilter(interaction, client, guildId) {
//   const action = interaction.options.getString("action");

//   try {
//     switch (action) {
//       case "setup":
//         await showFilterSetup(interaction, client, guildId);
//         break;
//       case "view":
//         await showCurrentFilters(interaction, client, guildId);
//         break;
//       case "reset":
//         await resetFilters(interaction, client, guildId);
//         break;
//     }
//   } catch (error) {
//     console.error("Lỗi khi xử lý filter:", error);
//     const errorEmbed = createErrorEmbed(
//       "Lỗi xử lý bộ lọc!",
//       "Không thể xử lý bộ lọc. Vui lòng thử lại sau!"
//     );
//     await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
//   }
// }

// async function showFilterSetup(interaction, client, guildId) {
//   const embed = createInfoEmbed(
//     "🔧 Thiết lập bộ lọc sự kiện",
//     "Chọn các sự kiện bạn muốn theo dõi. Sử dụng menu dropdown bên dưới để bật/tắt từng loại sự kiện."
//   );

//   const eventCategories = [
//     {
//       id: "channels",
//       name: "📁 Channels",
//       events: ["Channel creation", "Updated channel", "Channel deletion"],
//       description: "Theo dõi tạo, sửa, xóa kênh",
//     },
//     {
//       id: "roles",
//       name: "🎭 Roles",
//       events: ["Role creation", "Role updates", "Role deletion"],
//       description: "Theo dõi thay đổi roles",
//     },
//     {
//       id: "members",
//       name: "👥 Members",
//       events: [
//         "Members joining",
//         "Members leaving",
//         "Name changes",
//         "Avatar changes",
//         "Member bans",
//         "Member unbans",
//         "Member timeout",
//         "Member remove timeout",
//       ],
//       description: "Theo dõi hoạt động thành viên",
//     },
//     {
//       id: "messages",
//       name: "📝 Messages",
//       events: ["Deleted messages", "Edited messages", "Purged messages"],
//       description: "Theo dõi tin nhắn",
//     },
//     {
//       id: "voice",
//       name: "🔊 Voice",
//       events: [
//         "Join voice channel",
//         "Move between voice channels",
//         "Leave voice channel",
//       ],
//       description: "Theo dõi hoạt động voice",
//     },
//     {
//       id: "server",
//       name: "⚙️ Server",
//       events: ["Server updates", "Emoji changes"],
//       description: "Theo dõi thay đổi server",
//     },
//   ];

//   // Tạo select menus cho từng category
//   const components = [];
//   for (let i = 0; i < eventCategories.length; i++) {
//     const category = eventCategories[i];
//     const selectMenu = new StringSelectMenuBuilder()
//       .setCustomId(`filter_${category.id}`)
//       .setPlaceholder(`${category.name} - ${category.description}`)
//       .setMinValues(0)
//       .setMaxValues(category.events.length)
//       .addOptions(
//         category.events.map((event, index) => ({
//           label: event,
//           value: `${category.id}_${index}`,
//           description: `Bật/tắt ${event.toLowerCase()}`,
//         }))
//       );

//     components.push(new ActionRowBuilder().addComponents(selectMenu));
//   }

//   // Buttons
//   const buttonRow = new ActionRowBuilder().addComponents(
//     new ButtonBuilder()
//       .setCustomId("filter_save")
//       .setLabel("Lưu cấu hình")
//       .setStyle(ButtonStyle.Success)
//       .setEmoji("💾"),
//     new ButtonBuilder()
//       .setCustomId("filter_preview")
//       .setLabel("Xem trước")
//       .setStyle(ButtonStyle.Secondary)
//       .setEmoji("👁️"),
//     new ButtonBuilder()
//       .setCustomId("filter_cancel")
//       .setLabel("Hủy")
//       .setStyle(ButtonStyle.Danger)
//       .setEmoji("❌")
//   );

//   components.push(buttonRow);

//   await interaction.reply({
//     embeds: [embed],
//     components: components,
//     ephemeral: true,
//   });

//   // Handle interactions
//   const selectedEvents = new Set();
//   const collector = interaction.channel.createMessageComponentCollector({
//     filter: (i) => i.user.id === interaction.user.id,
//     time: 300000, // 5 minutes
//   });

//   collector.on("collect", async (i) => {
//     try {
//       if (
//         i.customId.startsWith("filter_") &&
//         !i.customId.includes("_save") &&
//         !i.customId.includes("_preview") &&
//         !i.customId.includes("_cancel")
//       ) {
//         // Handle event selection
//         const categoryId = i.customId.replace("filter_", "");
//         const values = i.values || [];

//         // Remove old events from this category
//         Array.from(selectedEvents).forEach((event) => {
//           if (event.startsWith(categoryId + "_")) {
//             selectedEvents.delete(event);
//           }
//         });

//         // Add new selected events
//         values.forEach((value) => {
//           selectedEvents.add(value);
//         });

//         await i.deferUpdate();
//       } else if (i.customId === "filter_preview") {
//         await showFilterPreview(i, selectedEvents, eventCategories);
//       } else if (i.customId === "filter_save") {
//         await saveFilterConfig(
//           i,
//           client,
//           guildId,
//           selectedEvents,
//           eventCategories
//         );
//         collector.stop("saved");
//       } else if (i.customId === "filter_cancel") {
//         await i.deferUpdate();
//         collector.stop("cancelled");
//       }
//     } catch (error) {
//       console.error("Lỗi trong filter collector:", error);
//     }
//   });

//   collector.on("end", async (collected, reason) => {
//     try {
//       if (reason === "cancelled") {
//         const cancelEmbed = createErrorEmbed(
//           "Đã hủy thiết lập",
//           "Thiết lập bộ lọc đã bị hủy."
//         );
//         await interaction.editReply({ embeds: [cancelEmbed], components: [] });
//       } else if (reason === "time") {
//         const timeoutEmbed = createErrorEmbed(
//           "Hết thời gian",
//           "Thiết lập bộ lọc đã hết thời gian. Vui lòng thử lại."
//         );
//         await interaction.editReply({ embeds: [timeoutEmbed], components: [] });
//       }
//     } catch (error) {
//       console.error("Lỗi khi kết thúc filter collector:", error);
//     }
//   });
// }

// async function showFilterPreview(interaction, selectedEvents, eventCategories) {
//   const eventMap = {};
//   eventCategories.forEach((category) => {
//     category.events.forEach((event, index) => {
//       eventMap[`${category.id}_${index}`] = `${category.name}: ${event}`;
//     });
//   });

//   const selectedList =
//     Array.from(selectedEvents)
//       .map((eventKey) => `• ${eventMap[eventKey]}`)
//       .join("\n") || "Chưa chọn sự kiện nào";

//   const embed = createInfoEmbed(
//     "👁️ Xem trước bộ lọc",
//     `**Sự kiện đã chọn (${selectedEvents.size}):**\n${selectedList}`
//   );

//   await interaction.reply({ embeds: [embed], ephemeral: true });
// }

// async function saveFilterConfig(
//   interaction,
//   client,
//   guildId,
//   selectedEvents,
//   eventCategories
// ) {
//   try {
//     // Convert selected events to config format
//     const config = {};
//     eventCategories.forEach((category) => {
//       config[category.id] = [];
//       category.events.forEach((event, index) => {
//         if (selectedEvents.has(`${category.id}_${index}`)) {
//           config[category.id].push(event);
//         }
//       });
//     });

//     // Save to database
//     await client.db.updateEventFilters(guildId, config);

//     const successEmbed = createSuccessEmbed(
//       "💾 Đã lưu cấu hình bộ lọc!",
//       `**Tổng sự kiện:** ${selectedEvents.size}\n` +
//         `**Cấu hình:** Đã được lưu vào database\n\n` +
//         `Hệ thống sẽ chỉ log các sự kiện đã chọn.`
//     );

//     await interaction.editReply({ embeds: [successEmbed], components: [] });
//   } catch (error) {
//     console.error("Lỗi khi lưu filter config:", error);
//     const errorEmbed = createErrorEmbed(
//       "Lỗi lưu cấu hình!",
//       "Không thể lưu cấu hình bộ lọc. Vui lòng thử lại!"
//     );
//     await interaction.editReply({ embeds: [errorEmbed], components: [] });
//   }
// }

// // Handler for templates subcommand
// async function handleTemplates(interaction, client, guildId) {
//   const template = interaction.options.getString("template");
//   const mainChannel = interaction.options.getChannel("main_channel");

//   try {
//     const templateConfig = getTemplateConfig(template);

//     // Apply template configuration
//     await applyTemplate(client, guildId, templateConfig, mainChannel);

//     const embed = createSuccessEmbed(
//       `${templateConfig.emoji} Template đã được áp dụng!`,
//       `**Template:** ${templateConfig.name}\n` +
//         `**Kênh chính:** ${mainChannel}\n` +
//         `**Sự kiện được bật:** ${templateConfig.events.length}\n\n` +
//         `${templateConfig.description}`
//     );

//     embed.addFields({
//       name: "📋 Sự kiện được theo dõi",
//       value:
//         templateConfig.events
//           .slice(0, 10)
//           .map((event) => `• ${event}`)
//           .join("\n") +
//         (templateConfig.events.length > 10
//           ? `\n*... và ${templateConfig.events.length - 10} sự kiện khác*`
//           : ""),
//       inline: false,
//     });

//     embed.addFields({
//       name: "⚙️ Cấu hình đặc biệt",
//       value: templateConfig.specialConfig
//         .map((config) => `• ${config}`)
//         .join("\n"),
//       inline: false,
//     });

//     await interaction.reply({ embeds: [embed] });
//   } catch (error) {
//     console.error("Lỗi khi áp dụng template:", error);
//     const errorEmbed = createErrorEmbed(
//       "Lỗi áp dụng template!",
//       "Không thể áp dụng template. Vui lòng thử lại sau!"
//     );
//     await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
//   }
// }

// function getTemplateConfig(template) {
//   const templates = {
//     security: {
//       name: "Security Focus",
//       emoji: "🔒",
//       description: "Tập trung vào bảo mật và theo dõi các hoạt động nguy hiểm",
//       events: [
//         "Role creation",
//         "Role updates",
//         "Role deletion",
//         "Channel creation",
//         "Channel deletion",
//         "Updated channel",
//         "Member bans",
//         "Member unbans",
//         "Member timeout",
//         "Permission changes",
//         "Server updates",
//         "Failed login attempts",
//         "Suspicious activity",
//       ],
//       specialConfig: [
//         "Bật cảnh báo cho thay đổi quyền hạn",
//         "Theo dõi hoạt động admin",
//         "Log chi tiết cho tất cả hành động",
//         "Cảnh báo spam actions",
//         "Theo dõi mass delete",
//       ],
//       alerts: ["permission_changes", "suspicious_activity", "failed_actions"],
//     },
//     community: {
//       name: "Community Focus",
//       emoji: "👥",
//       description: "Tập trung vào hoạt động cộng đồng và tương tác thành viên",
//       events: [
//         "Members joining",
//         "Members leaving",
//         "Name changes",
//         "Avatar changes",
//         "Deleted messages",
//         "Edited messages",
//         "Join voice channel",
//         "Leave voice channel",
//         "Emoji changes",
//       ],
//       specialConfig: [
//         "Kênh riêng cho member logs",
//         "Kênh riêng cho message logs",
//         "Kênh riêng cho voice logs",
//         "Bao gồm hoạt động bot",
//         "Log đơn giản, dễ đọc",
//       ],
//       alerts: ["mass_delete"],
//     },
//     gaming: {
//       name: "Gaming Focus",
//       emoji: "🎮",
//       description:
//         "Tối ưu cho server gaming với focus vào voice và hoạt động real-time",
//       events: [
//         "Join voice channel",
//         "Move between voice channels",
//         "Leave voice channel",
//         "Members joining",
//         "Members leaving",
//         "Role updates",
//         "Channel creation",
//         "Deleted messages",
//         "Member timeout",
//       ],
//       specialConfig: [
//         "Voice logs ưu tiên cao",
//         "Real-time monitoring",
//         "Compact log format",
//         "Bỏ qua bot activity",
//         "Focus vào moderation events",
//       ],
//       alerts: ["spam_actions"],
//     },
//     business: {
//       name: "Business Focus",
//       emoji: "💼",
//       description:
//         "Chuyên nghiệp cho môi trường doanh nghiệp với audit trail đầy đủ",
//       events: [
//         "Role creation",
//         "Role updates",
//         "Role deletion",
//         "Channel creation",
//         "Updated channel",
//         "Channel deletion",
//         "Members joining",
//         "Members leaving",
//         "Name changes",
//         "Deleted messages",
//         "Edited messages",
//         "Purged messages",
//         "Server updates",
//         "Permission changes",
//       ],
//       specialConfig: [
//         "Log chi tiết với before/after",
//         "Webhook mode cho reliability",
//         "Backup tự động",
//         "Compliance ready",
//         "Full audit trail",
//       ],
//       alerts: ["permission_changes", "suspicious_activity", "mass_delete"],
//     },
//     creative: {
//       name: "Creative Focus",
//       emoji: "🎨",
//       description:
//         "Dành cho server sáng tạo với focus vào content và collaboration",
//       events: [
//         "Channel creation",
//         "Updated channel",
//         "Emoji changes",
//         "Server updates",
//         "Members joining",
//         "Members leaving",
//         "Role updates",
//         "Deleted messages",
//         "Join voice channel",
//         "Leave voice channel",
//       ],
//       specialConfig: [
//         "Emoji/sticker logs",
//         "Channel activity tracking",
//         "Creative content focus",
//         "Collaboration monitoring",
//         "Minimal noise",
//       ],
//       alerts: ["mass_delete"],
//     },
//   };

//   return templates[template];
// }

// async function applyTemplate(client, guildId, templateConfig, mainChannel) {
//   // Apply basic audit config
//   await client.db.updateAuditConfig(guildId, {
//     enabled: 1,
//     log_channel_id: mainChannel.id,
//     events: "custom",
//     include_bots: templateConfig.name === "Community Focus" ? 1 : 0,
//     detailed_logs: templateConfig.name === "Business Focus" ? 1 : 0,
//     webhook_mode: templateConfig.name === "Business Focus" ? 1 : 0,
//     webhook_id: null,
//   });

//   // Apply event filters
//   const eventConfig = {};
//   templateConfig.events.forEach((event) => {
//     // Map events to categories
//     if (event.includes("Role")) eventConfig.roles = eventConfig.roles || [];
//     if (event.includes("Channel"))
//       eventConfig.channels = eventConfig.channels || [];
//     if (event.includes("Member"))
//       eventConfig.members = eventConfig.members || [];
//     if (event.includes("message"))
//       eventConfig.messages = eventConfig.messages || [];
//     if (event.includes("voice")) eventConfig.voice = eventConfig.voice || [];
//     if (event.includes("Server") || event.includes("Emoji"))
//       eventConfig.server = eventConfig.server || [];

//     // Add to appropriate category
//     Object.keys(eventConfig).forEach((category) => {
//       if (
//         event.toLowerCase().includes(category.slice(0, -1)) ||
//         (category === "server" &&
//           (event.includes("Server") || event.includes("Emoji")))
//       ) {
//         if (!eventConfig[category].includes(event)) {
//           eventConfig[category].push(event);
//         }
//       }
//     });
//   });

//   await client.db.updateEventFilters(guildId, eventConfig);

//   // Apply alerts
//   if (templateConfig.alerts) {
//     for (const alertType of templateConfig.alerts) {
//       await client.db.updateAuditAlert(guildId, {
//         alert_type: alertType,
//         enabled: 1,
//         threshold: getDefaultThreshold(alertType),
//         alert_channel_id: mainChannel.id,
//       });
//     }
//   }
// }

// async function showCurrentFilters(interaction, client, guildId) {
//   try {
//     const filters = await client.db.getEventFilters(guildId);

//     if (!filters || Object.keys(filters).length === 0) {
//       const embed = createInfoEmbed(
//         "🔍 Bộ lọc hiện tại",
//         "Chưa có bộ lọc nào được thiết lập. Sử dụng `/audit filter setup` để thiết lập."
//       );
//       return await interaction.reply({ embeds: [embed] });
//     }

//     const embed = createInfoEmbed(
//       "🔍 Bộ lọc sự kiện hiện tại",
//       "Danh sách các sự kiện đang được theo dõi:"
//     );

//     Object.entries(filters).forEach(([category, events]) => {
//       if (events && events.length > 0) {
//         const categoryNames = {
//           channels: "📁 Channels",
//           roles: "🎭 Roles",
//           members: "👥 Members",
//           messages: "📝 Messages",
//           voice: "🔊 Voice",
//           server: "⚙️ Server",
//         };

//         embed.addFields({
//           name: categoryNames[category] || category,
//           value: events.map((event) => `• ${event}`).join("\n"),
//           inline: true,
//         });
//       }
//     });

//     embed.addFields({
//       name: "⚙️ Quản lý",
//       value:
//         "• `/audit filter setup` - Thiết lập lại\n• `/audit filter reset` - Reset về mặc định",
//       inline: false,
//     });

//     await interaction.reply({ embeds: [embed] });
//   } catch (error) {
//     console.error("Lỗi khi xem filters:", error);
//     const errorEmbed = createErrorEmbed(
//       "Lỗi xem bộ lọc!",
//       "Không thể lấy thông tin bộ lọc. Vui lòng thử lại sau!"
//     );
//     await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
//   }
// }

// async function resetFilters(interaction, client, guildId) {
//   try {
//     await client.db.resetEventFilters(guildId);

//     const embed = createSuccessEmbed(
//       "🔄 Đã reset bộ lọc!",
//       "Bộ lọc sự kiện đã được reset về mặc định. Tất cả sự kiện sẽ được log."
//     );

//     embed.addFields({
//       name: "📝 Bước tiếp theo",
//       value: "Sử dụng `/audit filter setup` để thiết lập bộ lọc mới.",
//       inline: false,
//     });

//     await interaction.reply({ embeds: [embed] });
//   } catch (error) {
//     console.error("Lỗi khi reset filters:", error);
//     const errorEmbed = createErrorEmbed(
//       "Lỗi reset bộ lọc!",
//       "Không thể reset bộ lọc. Vui lòng thử lại sau!"
//     );
//     await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
//   }
// }

// async function generateTrendAnalysis(interaction, client, guildId, period) {
//   const logs = await client.db.getAuditLogs(guildId, { days: period });

//   // Analyze trends over time
//   const dailyStats = {};
//   const weeklyStats = {};
//   const actionTrends = {};

//   logs.forEach((log) => {
//     const date = new Date(log.created_at);
//     const dayKey = date.toDateString();
//     const weekKey = `Week ${Math.ceil(date.getDate() / 7)}-${
//       date.getMonth() + 1
//     }`;

//     // Daily stats
//     if (!dailyStats[dayKey]) {
//       dailyStats[dayKey] = { total: 0, actions: {} };
//     }
//     dailyStats[dayKey].total++;
//     dailyStats[dayKey].actions[log.action] =
//       (dailyStats[dayKey].actions[log.action] || 0) + 1;

//     // Weekly stats
//     if (!weeklyStats[weekKey]) {
//       weeklyStats[weekKey] = { total: 0, actions: {} };
//     }
//     weeklyStats[weekKey].total++;

//     // Action trends
//     actionTrends[log.action] = actionTrends[log.action] || [];
//     actionTrends[log.action].push(date);
//   });

//   const embed = createInfoEmbed(
//     "📈 Phân tích xu hướng",
//     `**Khoảng thời gian:** ${period} ngày\n` +
//       `**Tổng sự kiện:** ${logs.length}`
//   );

//   // Activity trend
//   const recentDays =
//     Object.entries(dailyStats)
//       .slice(-7)
//       .map(([date, stats]) => {
//         const formattedDate = new Date(date).toLocaleDateString("vi-VN");
//         return `• ${formattedDate}: ${stats.total} sự kiện`;
//       })
//       .join("\n") || "Không có dữ liệu";

//   embed.addFields({
//     name: "📊 Xu hướng 7 ngày gần đây",
//     value: recentDays,
//     inline: false,
//   });

//   // Most growing actions
//   const growingActions =
//     Object.entries(actionTrends)
//       .map(([action, dates]) => {
//         const recent = dates.filter(
//           (date) => Date.now() - date.getTime() < 7 * 24 * 60 * 60 * 1000
//         ).length;
//         const older = dates.length - recent;
//         const growth = older > 0 ? ((recent - older) / older) * 100 : 0;
//         return { action, growth, recent, total: dates.length };
//       })
//       .sort((a, b) => b.growth - a.growth)
//       .slice(0, 5)
//       .map(
//         (item) =>
//           `• **${item.action}:** ${
//             item.growth > 0 ? "+" : ""
//           }${item.growth.toFixed(1)}%`
//       )
//       .join("\n") || "Không có dữ liệu";

//   embed.addFields({
//     name: "🚀 Sự kiện tăng trưởng",
//     value: growingActions,
//     inline: true,
//   });

//   // Peak activity times
//   const hourlyStats = {};
//   logs.forEach((log) => {
//     const hour = new Date(log.created_at).getHours();
//     hourlyStats[hour] = (hourlyStats[hour] || 0) + 1;
//   });

//   const peakHours =
//     Object.entries(hourlyStats)
//       .sort(([, a], [, b]) => b - a)
//       .slice(0, 3)
//       .map(([hour, count]) => `• ${hour}:00 - ${count} sự kiện`)
//       .join("\n") || "Không có dữ liệu";

//   embed.addFields({
//     name: "⏰ Giờ cao điểm",
//     value: peakHours,
//     inline: true,
//   });

//   await interaction.editReply({ embeds: [embed] });
// }

// async function generatePerformanceReport(interaction, client, guildId, period) {
//   const logs = await client.db.getAuditLogs(guildId, { days: period });
//   const config = await client.db.getAuditConfig(guildId);

//   // Performance metrics
//   const totalEvents = logs.length;
//   const avgPerDay = Math.round(totalEvents / period);
//   const peakDay = Math.max(
//     ...Object.values(
//       logs.reduce((acc, log) => {
//         const date = new Date(log.created_at).toDateString();
//         acc[date] = (acc[date] || 0) + 1;
//         return acc;
//       }, {})
//     )
//   );

//   // Storage analysis
//   const avgLogSize = 150; // bytes per log entry (estimate)
//   const totalStorage = totalEvents * avgLogSize;
//   const storagePerDay = avgPerDay * avgLogSize;

//   // System health
//   const errorRate =
//     (logs.filter((log) => log.action.includes("ERROR")).length / totalEvents) *
//     100;
//   const successRate = 100 - errorRate;

//   const embed = createInfoEmbed(
//     "⚡ Báo cáo hiệu suất",
//     `**Khoảng thời gian:** ${period} ngày\n` +
//       `**Trạng thái hệ thống:** ${config?.enabled ? "🟢 Hoạt động" : "🔴 Tắt"}`
//   );

//   embed.addFields({
//     name: "📊 Thống kê hoạt động",
//     value:
//       `• **Tổng sự kiện:** ${totalEvents.toLocaleString()}\n` +
//       `• **Trung bình/ngày:** ${avgPerDay.toLocaleString()}\n` +
//       `• **Ngày cao điểm:** ${peakDay.toLocaleString()} sự kiện\n` +
//       `• **Tỷ lệ thành công:** ${successRate.toFixed(1)}%`,
//     inline: true,
//   });

//   embed.addFields({
//     name: "💾 Phân tích lưu trữ",
//     value:
//       `• **Dung lượng hiện tại:** ${formatBytes(totalStorage)}\n` +
//       `• **Tăng trưởng/ngày:** ${formatBytes(storagePerDay)}\n` +
//       `• **Dự kiến/tháng:** ${formatBytes(storagePerDay * 30)}\n` +
//       `• **Dự kiến/năm:** ${formatBytes(storagePerDay * 365)}`,
//     inline: true,
//   });

//   // Performance recommendations
//   const recommendations = [];

//   if (avgPerDay > 1000) {
//     recommendations.push("• Cân nhắc tăng retention period");
//   }
//   if (totalStorage > 10 * 1024 * 1024) {
//     // 10MB
//     recommendations.push("• Nên cleanup logs cũ");
//   }
//   if (errorRate > 5) {
//     recommendations.push("• Kiểm tra cấu hình hệ thống");
//   }
//   if (peakDay > avgPerDay * 5) {
//     recommendations.push("• Thiết lập rate limiting");
//   }

//   if (recommendations.length > 0) {
//     embed.addFields({
//       name: "💡 Khuyến nghị tối ưu",
//       value: recommendations.join("\n"),
//       inline: false,
//     });
//   }

//   // System status
//   const systemStatus = [];
//   systemStatus.push(
//     `• **Webhook mode:** ${config?.webhook_mode ? "Bật" : "Tắt"}`
//   );
//   systemStatus.push(
//     `• **Chi tiết logs:** ${config?.detailed_logs ? "Bật" : "Tắt"}`
//   );
//   systemStatus.push(
//     `• **Bao gồm bots:** ${config?.include_bots ? "Có" : "Không"}`
//   );

//   embed.addFields({
//     name: "⚙️ Cấu hình hệ thống",
//     value: systemStatus.join("\n"),
//     inline: false,
//   });

//   await interaction.editReply({ embeds: [embed] });
// }

// function formatBytes(bytes) {
//   if (bytes === 0) return "0 Bytes";
//   const k = 1024;
//   const sizes = ["Bytes", "KB", "MB", "GB"];
//   const i = Math.floor(Math.log(bytes) / Math.log(k));
//   return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
// }
