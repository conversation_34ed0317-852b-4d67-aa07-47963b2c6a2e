const { SlashCommandBuilder, ChannelType, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('audit')
        .setDescription('Hệ thống audit log và logging nâng cao')
        .addSubcommand(subcommand =>
            subcommand
                .setName('setup')
                .setDescription('Thiết lập hệ thống audit log')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Kênh nhận audit logs')
                        .setRequired(true)
                        .addChannelTypes(ChannelType.GuildText)
                )
                .addStringOption(option =>
                    option.setName('events')
                        .setDescription('Loại sự kiện cần log')
                        .setRequired(false)
                        .addChoices(
                            { name: '🔧 All - <PERSON><PERSON><PERSON> cả sự kiện', value: 'all' },
                            { name: '👥 Members - Thành viên', value: 'members' },
                            { name: '📝 Messages - Tin nhắn', value: 'messages' },
                            { name: '🎭 Roles - Roles', value: 'roles' },
                            { name: '📁 Channels - Kênh', value: 'channels' },
                            { name: '⚙️ Server - Server settings', value: 'server' },
                            { name: '🔨 Moderation - Kiểm duyệt', value: 'moderation' },
                            { name: '🎵 Voice - Voice channels', value: 'voice' },
                            { name: '🔗 Invites - Lời mời', value: 'invites' },
                            { name: '🎨 Emojis - Emoji/Stickers', value: 'emojis' }
                        )
                )
                .addBooleanOption(option =>
                    option.setName('include_bots')
                        .setDescription('Bao gồm hoạt động của bot')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('detailed_logs')
                        .setDescription('Log chi tiết (bao gồm before/after)')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('webhook_mode')
                        .setDescription('Sử dụng webhook để gửi logs')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('disable')
                .setDescription('Tắt hệ thống audit log')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('config')
                .setDescription('Xem cấu hình audit log hiện tại')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('events')
                .setDescription('Quản lý các sự kiện được log')
                .addStringOption(option =>
                    option.setName('action')
                        .setDescription('Hành động')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Enable - Bật sự kiện', value: 'enable' },
                            { name: 'Disable - Tắt sự kiện', value: 'disable' },
                            { name: 'List - Xem danh sách', value: 'list' }
                        )
                )
                .addStringOption(option =>
                    option.setName('event_type')
                        .setDescription('Loại sự kiện')
                        .setRequired(false)
                        .addChoices(
                            { name: 'Member Join', value: 'memberAdd' },
                            { name: 'Member Leave', value: 'memberRemove' },
                            { name: 'Member Update', value: 'memberUpdate' },
                            { name: 'Message Delete', value: 'messageDelete' },
                            { name: 'Message Edit', value: 'messageUpdate' },
                            { name: 'Message Bulk Delete', value: 'messageDeleteBulk' },
                            { name: 'Role Create', value: 'roleCreate' },
                            { name: 'Role Delete', value: 'roleDelete' },
                            { name: 'Role Update', value: 'roleUpdate' },
                            { name: 'Channel Create', value: 'channelCreate' },
                            { name: 'Channel Delete', value: 'channelDelete' },
                            { name: 'Channel Update', value: 'channelUpdate' },
                            { name: 'Ban Add', value: 'banAdd' },
                            { name: 'Ban Remove', value: 'banRemove' },
                            { name: 'Voice State Update', value: 'voiceStateUpdate' },
                            { name: 'Invite Create', value: 'inviteCreate' },
                            { name: 'Invite Delete', value: 'inviteDelete' },
                            { name: 'Emoji Create', value: 'emojiCreate' },
                            { name: 'Emoji Delete', value: 'emojiDelete' },
                            { name: 'Emoji Update', value: 'emojiUpdate' }
                        )
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('channels')
                .setDescription('Quản lý kênh log cho từng loại sự kiện')
                .addStringOption(option =>
                    option.setName('event_category')
                        .setDescription('Danh mục sự kiện')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Members', value: 'members' },
                            { name: 'Messages', value: 'messages' },
                            { name: 'Roles', value: 'roles' },
                            { name: 'Channels', value: 'channels' },
                            { name: 'Moderation', value: 'moderation' },
                            { name: 'Voice', value: 'voice' },
                            { name: 'Server', value: 'server' }
                        )
                )
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Kênh log cho danh mục này')
                        .setRequired(true)
                        .addChannelTypes(ChannelType.GuildText)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('ignore')
                .setDescription('Quản lý danh sách bỏ qua')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('Loại đối tượng bỏ qua')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Channel', value: 'channel' },
                            { name: 'Role', value: 'role' },
                            { name: 'User', value: 'user' }
                        )
                )
                .addStringOption(option =>
                    option.setName('action')
                        .setDescription('Hành động')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Add - Thêm vào danh sách bỏ qua', value: 'add' },
                            { name: 'Remove - Xóa khỏi danh sách', value: 'remove' },
                            { name: 'List - Xem danh sách', value: 'list' }
                        )
                )
                .addStringOption(option =>
                    option.setName('target')
                        .setDescription('ID của channel/role/user (chỉ cho add/remove)')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('stats')
                .setDescription('Xem thống kê audit log')
                .addStringOption(option =>
                    option.setName('period')
                        .setDescription('Khoảng thời gian')
                        .setRequired(false)
                        .addChoices(
                            { name: 'Hôm nay', value: 'today' },
                            { name: '7 ngày qua', value: 'week' },
                            { name: '30 ngày qua', value: 'month' },
                            { name: 'Tất cả', value: 'all' }
                        )
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('search')
                .setDescription('Tìm kiếm trong audit log')
                .addStringOption(option =>
                    option.setName('query')
                        .setDescription('Từ khóa tìm kiếm')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('event_type')
                        .setDescription('Loại sự kiện')
                        .setRequired(false)
                )
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('Người dùng cụ thể')
                        .setRequired(false)
                )
                .addIntegerOption(option =>
                    option.setName('days')
                        .setDescription('Số ngày tìm kiếm (1-30)')
                        .setRequired(false)
                        .setMinValue(1)
                        .setMaxValue(30)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('export')
                .setDescription('Xuất audit log ra file')
                .addStringOption(option =>
                    option.setName('format')
                        .setDescription('Định dạng file')
                        .setRequired(false)
                        .addChoices(
                            { name: 'JSON', value: 'json' },
                            { name: 'CSV', value: 'csv' },
                            { name: 'TXT', value: 'txt' }
                        )
                )
                .addIntegerOption(option =>
                    option.setName('days')
                        .setDescription('Số ngày xuất (1-90)')
                        .setRequired(false)
                        .setMinValue(1)
                        .setMaxValue(90)
                )
        ),
    category: 'logging',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền bot
        if (!interaction.guild.members.me.permissions.has([
            PermissionFlagsBits.ViewAuditLog,
            PermissionFlagsBits.ManageWebhooks,
            PermissionFlagsBits.ViewChannel,
            PermissionFlagsBits.SendMessages
        ])) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                'Bot cần các quyền sau:\n• View Audit Log\n• Manage Webhooks\n• View Channel\n• Send Messages'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const subcommand = interaction.options.getSubcommand();
        const guildId = interaction.guild.id;
        
        switch (subcommand) {
            case 'setup':
                await handleSetup(interaction, client, guildId);
                break;
            case 'disable':
                await handleDisable(interaction, client, guildId);
                break;
            case 'config':
                await handleConfig(interaction, client, guildId);
                break;
            case 'events':
                await handleEvents(interaction, client, guildId);
                break;
            case 'channels':
                await handleChannels(interaction, client, guildId);
                break;
            case 'ignore':
                await handleIgnore(interaction, client, guildId);
                break;
            case 'stats':
                await handleStats(interaction, client, guildId);
                break;
            case 'search':
                await handleSearch(interaction, client, guildId);
                break;
            case 'export':
                await handleExport(interaction, client, guildId);
                break;
        }
    },
};

async function handleSetup(interaction, client, guildId) {
    const channel = interaction.options.getChannel('channel');
    const events = interaction.options.getString('events') || 'all';
    const includeBots = interaction.options.getBoolean('include_bots') || false;
    const detailedLogs = interaction.options.getBoolean('detailed_logs') || false;
    const webhookMode = interaction.options.getBoolean('webhook_mode') || false;
    
    try {
        // Tạo webhook nếu cần
        let webhookId = null;
        if (webhookMode) {
            const webhook = await channel.createWebhook({
                name: 'Audit Log Bot',
                avatar: client.user.displayAvatarURL(),
                reason: 'Audit log webhook'
            });
            webhookId = webhook.id;
        }
        
        // Lưu cấu hình vào database
        await client.db.updateAuditConfig(guildId, {
            enabled: 1,
            log_channel_id: channel.id,
            events: events,
            include_bots: includeBots ? 1 : 0,
            detailed_logs: detailedLogs ? 1 : 0,
            webhook_mode: webhookMode ? 1 : 0,
            webhook_id: webhookId
        });
        
        const successEmbed = createSuccessEmbed(
            'Hệ thống audit log đã được thiết lập!',
            `**Kênh log:** ${channel}\n` +
            `**Sự kiện:** ${getEventDisplayName(events)}\n` +
            `**Bao gồm bot:** ${includeBots ? 'Có' : 'Không'}\n` +
            `**Log chi tiết:** ${detailedLogs ? 'Có' : 'Không'}\n` +
            `**Webhook mode:** ${webhookMode ? 'Có' : 'Không'}`
        );
        
        successEmbed.addFields({
            name: '📝 Bước tiếp theo',
            value: '• Sử dụng `/audit events` để quản lý sự kiện cụ thể\n' +
                   '• Sử dụng `/audit channels` để thiết lập kênh riêng cho từng loại\n' +
                   '• Sử dụng `/audit ignore` để bỏ qua channel/role/user cụ thể',
            inline: false
        });
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi thiết lập audit log:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể thiết lập hệ thống audit log. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

function getEventDisplayName(event) {
    const events = {
        'all': '🔧 Tất cả sự kiện',
        'members': '👥 Thành viên',
        'messages': '📝 Tin nhắn',
        'roles': '🎭 Roles',
        'channels': '📁 Kênh',
        'server': '⚙️ Server settings',
        'moderation': '🔨 Kiểm duyệt',
        'voice': '🎵 Voice channels',
        'invites': '🔗 Lời mời',
        'emojis': '🎨 Emoji/Stickers'
    };
    return events[event] || event;
}
