const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'soundboardSoundCreate', // Custom event name
    async execute(sound, client) {
        try {
            console.log(`🔊 Soundboard sound uploaded: ${sound.name} in ${sound.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(sound.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'SOUNDBOARD_SOUND_UPLOAD')) return;
            
            // Create event data
            const eventData = {
                eventType: 'SOUNDBOARD_SOUND_UPLOAD',
                user: 'System',
                userId: null,
                action: 'Âm thanh soundboard được tải lên',
                details: `Âm thanh soundboard **${sound.name}** đã được tả<PERSON> lên`,
                target: sound.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who uploaded the sound from audit logs
            try {
                const auditLogs = await sound.guild.fetchAuditLogs({
                    type: 130, // SOUNDBOARD_SOUND_CREATE (hypothetical)
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === sound.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Âm thanh soundboard được tải lên bởi ${auditEntry.executor.tag}`;
                }
            } catch (error) {
                console.log('Could not fetch audit logs for soundboard upload');
            }
            
            // Add to database
            await client.db.addAuditLog(sound.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔊 Âm thanh soundboard được tải lên',
                `Vừa có âm thanh soundboard mới được tải lên trong server`
            );
            
            embed.setColor(0x2ecc71); // Green for upload
            
            embed.addFields([
                {
                    name: '> Tên âm thanh',
                    value: `- ${sound.name}`,
                    inline: true
                },
                {
                    name: '> ID âm thanh',
                    value: `- ${sound.id}`,
                    inline: true
                },
                {
                    name: '> Người tải lên',
                    value: `- ${eventData.user}`,
                    inline: true
                }
            ]);
            
            // Add sound properties if available
            if (sound.volume !== undefined) {
                embed.addFields({
                    name: '> Âm lượng',
                    value: `- ${Math.round(sound.volume * 100)}%`,
                    inline: true
                });
            }
            
            if (sound.emoji) {
                embed.addFields({
                    name: '> Emoji',
                    value: `- ${sound.emoji}`,
                    inline: true
                });
            }
            
            embed.addFields({
                name: '> Thời gian tải lên',
                value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                inline: true
            });
            
            // Add usage info
            embed.addFields({
                name: '> Cách sử dụng',
                value: `- Nhấn vào biểu tượng soundboard trong voice chat`,
                inline: false
            });
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${sound.guild.name} • Soundboard Upload`,
                iconURL: sound.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Soundboard upload logged: ${sound.name}`);
            
        } catch (error) {
            console.error('Error in soundboardSoundUpload audit log:', error);
        }
    }
};
