# AIO v4 | Evolution X - By CodeX

Support Server: https://discord.gg/codexdev

## Setup Guide

### Prerequisites
- Node.j v18
- Discord Bot Token
- Required API Keys

### Environment Variables
You'll need to set up the following API keys:
- OpenAI API Key: https://platform.openai.com/account/api-keys
- OpenAI Organization Key/ID: https://platform.openai.com/account/org-settings
- Image Generation API Key: https://app.prodia.com

### Installation Steps
1. Clone or download this repository
2. Install dependencies:
   ```
   npm install
   ```

3. Create a `.env` file in the root directory with the following variables:
   ```
   token=
   mongodb=
   NODE_ENV=
   ```

   ## Optional Variables [Some Commands Might Not Work If Empty]
   ```
   openaiorgapi=
   openaiapi=
   ```
4. Configure your bot settings in the config files

### Running the Bot
1. Start the bot with:
   ```
   node index.js
   ```
2. Invite the bot to your server using the OAuth2 URL from the Discord Developer Portal

### Basic Commands
- Use `/help` to see all available commands
- Use `/bot` to get information about the bot
- Check out the Information category for more useful commands

## Features
- Powerful moderation tools
- Customizable server setup
- Fun and engaging commands
- Essential utilities for Discord servers

For more help, join our support server: https://discord.gg/codexdev