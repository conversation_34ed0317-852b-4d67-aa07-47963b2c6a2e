const { SlashCommandBuilder, ChannelType } = require("discord.js");
const {
  createInfoEmbed,
  createErrorEmbed,
} = require("../../utils/embedBuilder.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("list")
    .setDescription("Hiển thị danh sách các thành phần của server")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("emoji")
        .setDescription("Hiển thị tất cả emoji của server với phân trang")
        .addIntegerOption((option) =>
          option
            .setName("page")
            .setDescription("Trang cần xem (mặc định: 1)")
            .setRequired(false)
            .setMinValue(1)
        )
        .addStringOption((option) =>
          option
            .setName("filter")
            .setDescription("Lọc emoji theo loại")
            .setRequired(false)
            .addChoices(
              { name: "<PERSON><PERSON><PERSON> cả", value: "all" },
              { name: "Chỉ Static", value: "static" },
              { name: "Chỉ Animated", value: "animated" },
              { name: "Chỉ emoji bị khóa", value: "locked" },
              { name: "Chỉ emoji không bị khóa", value: "unlocked" }
            )
        )
        .addStringOption((option) =>
          option
            .setName("sort")
            .setDescription("Sắp xếp emoji theo")
            .setRequired(false)
            .addChoices(
              { name: "Tên A-Z", value: "name_asc" },
              { name: "Tên Z-A", value: "name_desc" },
              { name: "Ngày tạo (Mới nhất)", value: "date_desc" },
              { name: "Ngày tạo (Cũ nhất)", value: "date_asc" }
            )
        )
        .addBooleanOption((option) =>
          option
            .setName("show_ids")
            .setDescription("Hiển thị ID của emoji")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("role")
        .setDescription("Hiển thị tất cả roles với thông tin quyền hạn")
        .addIntegerOption((option) =>
          option
            .setName("page")
            .setDescription("Trang cần xem (mặc định: 1)")
            .setRequired(false)
            .setMinValue(1)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("channel")
        .setDescription("Hiển thị tất cả channels theo category")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("member")
        .setDescription("Hiển thị danh sách thành viên với roles")
        .addIntegerOption((option) =>
          option
            .setName("page")
            .setDescription("Trang cần xem (mặc định: 1)")
            .setRequired(false)
            .setMinValue(1)
        )
        .addBooleanOption((option) =>
          option
            .setName("online_only")
            .setDescription("Chỉ hiển thị thành viên online (mặc định: false)")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("bot")
        .setDescription("Hiển thị tất cả bots trong server")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("banned")
        .setDescription("Hiển thị danh sách thành viên bị ban")
        .addIntegerOption((option) =>
          option
            .setName("page")
            .setDescription("Trang cần xem (mặc định: 1)")
            .setRequired(false)
            .setMinValue(1)
        )
    ),
  category: "utility",
  adminOnly: false,
  manageServer: false,

  async execute(interaction, client) {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case "emoji":
        await handleListEmoji(interaction, client);
        break;
      case "role":
        await handleListRole(interaction, client);
        break;
      case "channel":
        await handleListChannel(interaction, client);
        break;
      case "member":
        await handleListMember(interaction, client);
        break;
      case "bot":
        await handleListBot(interaction, client);
        break;
      case "banned":
        await handleListBanned(interaction, client);
        break;
    }
  },
};

async function handleListEmoji(interaction, client) {
  const page = interaction.options.getInteger("page") || 1;
  const filter = interaction.options.getString("filter") || "all";
  const sort = interaction.options.getString("sort") || "name_asc";
  const showIds = interaction.options.getBoolean("show_ids") || false;

  let emojis = interaction.guild.emojis.cache;

  if (emojis.size === 0) {
    const infoEmbed = createInfoEmbed(
      "Chưa có emoji tùy chỉnh",
      "Server chưa có emoji tùy chỉnh nào."
    );
    return await interaction.reply({ embeds: [infoEmbed] });
  }

  // Apply filters
  switch (filter) {
    case "static":
      emojis = emojis.filter((emoji) => !emoji.animated);
      break;
    case "animated":
      emojis = emojis.filter((emoji) => emoji.animated);
      break;
    case "locked":
      emojis = emojis.filter((emoji) => emoji.roles.cache.size > 0);
      break;
    case "unlocked":
      emojis = emojis.filter((emoji) => emoji.roles.cache.size === 0);
      break;
    default:
      // "all" - no filter
      break;
  }

  // Sort emojis
  const sortedEmojis = Array.from(emojis.values()).sort((a, b) => {
    switch (sort) {
      case "name_asc":
        return a.name.localeCompare(b.name);
      case "name_desc":
        return b.name.localeCompare(a.name);
      case "date_asc":
        return a.createdTimestamp - b.createdTimestamp;
      case "date_desc":
        return b.createdTimestamp - a.createdTimestamp;
      default:
        return a.name.localeCompare(b.name);
    }
  });

  const itemsPerPage = 20;
  const totalPages = Math.ceil(sortedEmojis.length / itemsPerPage);

  if (page > totalPages && totalPages > 0) {
    const errorEmbed = createErrorEmbed(
      "Trang không tồn tại!",
      `Chỉ có ${totalPages} trang. Vui lòng chọn trang từ 1 đến ${totalPages}.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  if (sortedEmojis.length === 0) {
    const infoEmbed = createInfoEmbed(
      "Không có emoji nào",
      `Không tìm thấy emoji nào với bộ lọc **${getFilterDisplayName(filter)}**.`
    );
    return await interaction.reply({ embeds: [infoEmbed] });
  }

  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const pageEmojis = sortedEmojis.slice(startIndex, endIndex);

  const embed = createInfoEmbed(
    `📝 Danh sách Emoji (Trang ${page}/${totalPages})`,
    `Hiển thị ${pageEmojis.length} emoji trong trang này:`
  );

  // Group emojis by animated/static if showing all
  if (filter === "all") {
    const staticEmojis = pageEmojis.filter((emoji) => !emoji.animated);
    const animatedEmojis = pageEmojis.filter((emoji) => emoji.animated);

    if (staticEmojis.length > 0) {
      const emojiList = staticEmojis
        .map((emoji) =>
          showIds
            ? `${emoji} \`<:${emoji.name}:${emoji.id}>\``
            : `${emoji} \`${emoji.name}\``
        )
        .join(" ");
      embed.addFields({
        name: `🖼️ Static Emojis (${staticEmojis.length})`,
        value: emojiList.slice(0, 1024),
        inline: false,
      });
    }

    if (animatedEmojis.length > 0) {
      const emojiList = animatedEmojis
        .map((emoji) =>
          showIds
            ? `${emoji} \`<a:${emoji.name}:${emoji.id}>\``
            : `${emoji} \`${emoji.name}\``
        )
        .join(" ");
      embed.addFields({
        name: `🎬 Animated Emojis (${animatedEmojis.length})`,
        value: emojiList.slice(0, 1024),
        inline: false,
      });
    }
  } else {
    // Show filtered emojis in one group
    const emojiList = pageEmojis
      .map((emoji) => {
        if (showIds) {
          return emoji.animated
            ? `${emoji} \`<a:${emoji.name}:${emoji.id}>\``
            : `${emoji} \`<:${emoji.name}:${emoji.id}>\``;
        }
        return `${emoji} \`${emoji.name}\``;
      })
      .join(" ");

    embed.addFields({
      name: `${getFilterIcon(filter)} ${getFilterDisplayName(filter)} (${
        pageEmojis.length
      })`,
      value: emojiList.slice(0, 1024),
      inline: false,
    });
  }

  // Add filter and sort info
  const filterInfo =
    filter !== "all" ? ` • Lọc: ${getFilterDisplayName(filter)}` : "";
  const sortInfo =
    sort !== "name_asc" ? ` • Sắp xếp: ${getSortDisplayName(sort)}` : "";

  embed.setFooter({
    text: `Tổng: ${sortedEmojis.length} emoji • Trang ${page}/${totalPages}${filterInfo}${sortInfo}`,
    iconURL: interaction.guild.iconURL({ dynamic: true }),
  });

  await interaction.reply({ embeds: [embed] });
}

function getFilterDisplayName(filter) {
  const filterNames = {
    all: "Tất cả",
    static: "Static",
    animated: "Animated",
    locked: "Bị khóa",
    unlocked: "Không bị khóa",
  };
  return filterNames[filter] || "Tất cả";
}

function getFilterIcon(filter) {
  const filterIcons = {
    all: "📝",
    static: "🖼️",
    animated: "🎬",
    locked: "🔒",
    unlocked: "🔓",
  };
  return filterIcons[filter] || "📝";
}

function getSortDisplayName(sort) {
  const sortNames = {
    name_asc: "Tên A-Z",
    name_desc: "Tên Z-A",
    date_asc: "Cũ nhất",
    date_desc: "Mới nhất",
  };
  return sortNames[sort] || "Tên A-Z";
}

async function handleListRole(interaction, client) {
  const page = interaction.options.getInteger("page") || 1;
  const roles = interaction.guild.roles.cache.filter(
    (role) => role.id !== interaction.guild.id
  );

  if (roles.size === 0) {
    const infoEmbed = createInfoEmbed(
      "Chưa có role nào",
      "Server chưa có role tùy chỉnh nào."
    );
    return await interaction.reply({ embeds: [infoEmbed] });
  }

  const itemsPerPage = 10;
  const totalPages = Math.ceil(roles.size / itemsPerPage);

  if (page > totalPages) {
    const errorEmbed = createErrorEmbed(
      "Trang không tồn tại!",
      `Chỉ có ${totalPages} trang. Vui lòng chọn trang từ 1 đến ${totalPages}.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const sortedRoles = Array.from(roles.values()).sort(
    (a, b) => b.position - a.position
  );
  const pageRoles = sortedRoles.slice(startIndex, endIndex);

  const embed = createInfoEmbed(
    `🎭 Danh sách Role (Trang ${page}/${totalPages})`,
    `Hiển thị ${pageRoles.length} role trong trang này:`
  );

  for (const role of pageRoles) {
    const memberCount = role.members.size;
    const permissions = role.permissions.toArray();
    const isAdmin = permissions.includes("Administrator");
    const keyPerms = permissions.filter((p) =>
      [
        "ManageGuild",
        "ManageChannels",
        "ManageRoles",
        "BanMembers",
        "KickMembers",
      ].includes(p)
    );

    let roleInfo = `**Thành viên:** ${memberCount}\n`;
    roleInfo += `**Màu:** ${role.hexColor}\n`;
    roleInfo += `**Vị trí:** ${role.position}\n`;

    if (isAdmin) {
      roleInfo += `**Quyền:** Administrator (Tất cả quyền)`;
    } else if (keyPerms.length > 0) {
      roleInfo += `**Quyền chính:** ${keyPerms.slice(0, 3).join(", ")}${
        keyPerms.length > 3 ? "..." : ""
      }`;
    } else {
      roleInfo += `**Quyền:** Cơ bản`;
    }

    embed.addFields({
      name: `${role.name}`,
      value: roleInfo,
      inline: true,
    });
  }

  embed.setFooter({
    text: `Tổng: ${roles.size} role • Trang ${page}/${totalPages}`,
    iconURL: interaction.guild.iconURL({ dynamic: true }),
  });

  await interaction.reply({ embeds: [embed] });
}

async function handleListChannel(interaction, client) {
  const channels = interaction.guild.channels.cache;
  const categories = channels
    .filter((ch) => ch.type === ChannelType.GuildCategory)
    .sort((a, b) => a.position - b.position);
  const uncategorizedChannels = channels
    .filter(
      (ch) =>
        (ch.type === ChannelType.GuildText ||
          ch.type === ChannelType.GuildVoice) &&
        !ch.parentId
    )
    .sort((a, b) => a.position - b.position);

  const embed = createInfoEmbed(
    `📝 Danh sách Kênh`,
    `Tổng quan về tất cả kênh trong server:`
  );

  // Uncategorized channels
  if (uncategorizedChannels.size > 0) {
    let channelList = "";
    for (const channel of uncategorizedChannels.values()) {
      const icon = channel.type === ChannelType.GuildText ? "💬" : "🔊";
      channelList += `${icon} ${channel.name}\n`;
    }

    embed.addFields({
      name: "📂 Kênh không có danh mục",
      value: channelList.slice(0, 1024),
      inline: false,
    });
  }

  // Categorized channels
  for (const category of categories.values()) {
    const categoryChannels = channels
      .filter((ch) => ch.parentId === category.id)
      .sort((a, b) => a.position - b.position);

    if (categoryChannels.size > 0) {
      let channelList = "";
      for (const channel of categoryChannels.values()) {
        const icon = channel.type === ChannelType.GuildText ? "💬" : "🔊";
        channelList += `${icon} ${channel.name}\n`;
      }

      embed.addFields({
        name: `📁 ${category.name} (${categoryChannels.size})`,
        value: channelList.slice(0, 1024),
        inline: true,
      });
    }
  }

  // Statistics
  const textChannels = channels.filter(
    (ch) => ch.type === ChannelType.GuildText
  ).size;
  const voiceChannels = channels.filter(
    (ch) => ch.type === ChannelType.GuildVoice
  ).size;
  const totalChannels = textChannels + voiceChannels;

  embed.addFields({
    name: "📊 Thống kê",
    value: `**Tổng kênh:** ${totalChannels}\n**Text:** ${textChannels}\n**Voice:** ${voiceChannels}\n**Danh mục:** ${categories.size}`,
    inline: true,
  });

  await interaction.reply({ embeds: [embed] });
}

async function handleListMember(interaction, client) {
  const page = interaction.options.getInteger("page") || 1;
  const onlineOnly = interaction.options.getBoolean("online_only") || false;

  await interaction.guild.members.fetch(); // Fetch all members

  let members = interaction.guild.members.cache.filter(
    (member) => !member.user.bot
  );

  if (onlineOnly) {
    members = members.filter(
      (member) =>
        member.presence?.status && member.presence.status !== "offline"
    );
  }

  if (members.size === 0) {
    const infoEmbed = createInfoEmbed(
      "Không có thành viên nào",
      onlineOnly
        ? "Không có thành viên nào đang online."
        : "Server không có thành viên nào."
    );
    return await interaction.reply({ embeds: [infoEmbed] });
  }

  const itemsPerPage = 15;
  const totalPages = Math.ceil(members.size / itemsPerPage);

  if (page > totalPages) {
    const errorEmbed = createErrorEmbed(
      "Trang không tồn tại!",
      `Chỉ có ${totalPages} trang. Vui lòng chọn trang từ 1 đến ${totalPages}.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const sortedMembers = Array.from(members.values()).sort(
    (a, b) => b.joinedTimestamp - a.joinedTimestamp
  );
  const pageMembers = sortedMembers.slice(startIndex, endIndex);

  const embed = createInfoEmbed(
    `👥 Danh sách Thành viên${
      onlineOnly ? " Online" : ""
    } (Trang ${page}/${totalPages})`,
    `Hiển thị ${pageMembers.length} thành viên trong trang này:`
  );

  let memberList = "";
  for (const member of pageMembers) {
    const status = getStatusEmoji(member.presence?.status);
    const topRole =
      member.roles.highest.id !== interaction.guild.id
        ? member.roles.highest.name
        : "Không có";
    const joinDate = member.joinedAt.toLocaleDateString("vi-VN");

    memberList += `${status} **${member.displayName}** (${member.user.username})\n`;
    memberList += `└ Role cao nhất: ${topRole} • Tham gia: ${joinDate}\n\n`;
  }

  embed.addFields({
    name: "📋 Danh sách",
    value: memberList.slice(0, 4000),
    inline: false,
  });

  embed.setFooter({
    text: `Tổng: ${members.size} thành viên${
      onlineOnly ? " online" : ""
    } • Trang ${page}/${totalPages}`,
    iconURL: interaction.guild.iconURL({ dynamic: true }),
  });

  await interaction.reply({ embeds: [embed] });
}

async function handleListBot(interaction, client) {
  const bots = interaction.guild.members.cache.filter(
    (member) => member.user.bot
  );

  if (bots.size === 0) {
    const infoEmbed = createInfoEmbed(
      "Chưa có bot nào",
      "Server chưa có bot nào."
    );
    return await interaction.reply({ embeds: [infoEmbed] });
  }

  const embed = createInfoEmbed(
    `🤖 Danh sách Bot (${bots.size})`,
    `Server có ${bots.size} bot:`
  );

  let botList = "";
  for (const bot of bots.values()) {
    const status = getStatusEmoji(bot.presence?.status);
    const topRole =
      bot.roles.highest.id !== interaction.guild.id
        ? bot.roles.highest.name
        : "Không có";
    const joinDate = bot.joinedAt.toLocaleDateString("vi-VN");

    botList += `${status} **${bot.displayName}** (${bot.user.username})\n`;
    botList += `└ Role cao nhất: ${topRole} • Tham gia: ${joinDate}\n\n`;
  }

  // Chia nhỏ nếu quá dài
  if (botList.length > 4000) {
    botList =
      botList.substring(0, 3900) +
      "\n...\n\n*Danh sách quá dài, chỉ hiển thị một phần*";
  }

  embed.addFields({
    name: "📋 Danh sách Bot",
    value: botList,
    inline: false,
  });

  embed.addFields({
    name: "📊 Thống kê",
    value: `**Tổng bot:** ${bots.size}\n**Online:** ${
      bots.filter((b) => b.presence?.status === "online").size
    }\n**Offline:** ${
      bots.filter((b) => !b.presence?.status || b.presence.status === "offline")
        .size
    }`,
    inline: false,
  });

  await interaction.reply({ embeds: [embed] });
}

async function handleListBanned(interaction, client) {
  const page = interaction.options.getInteger("page") || 1;

  // Kiểm tra quyền xem ban list
  if (!interaction.member.permissions.has("BanMembers")) {
    const errorEmbed = createErrorEmbed(
      "Không có quyền!",
      "Bạn cần quyền **Ban Members** để xem danh sách thành viên bị ban."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    const bans = await interaction.guild.bans.fetch();

    if (bans.size === 0) {
      const infoEmbed = createInfoEmbed(
        "Không có ai bị ban",
        "Server chưa có thành viên nào bị ban."
      );
      return await interaction.reply({ embeds: [infoEmbed] });
    }

    const itemsPerPage = 10;
    const totalPages = Math.ceil(bans.size / itemsPerPage);

    if (page > totalPages) {
      const errorEmbed = createErrorEmbed(
        "Trang không tồn tại!",
        `Chỉ có ${totalPages} trang. Vui lòng chọn trang từ 1 đến ${totalPages}.`
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const banArray = Array.from(bans.values());
    const pageBans = banArray.slice(startIndex, endIndex);

    const embed = createInfoEmbed(
      `🔨 Danh sách Thành viên bị Ban (Trang ${page}/${totalPages})`,
      `Hiển thị ${pageBans.length} thành viên bị ban trong trang này:`
    );

    for (const ban of pageBans) {
      const user = ban.user;
      const reason = ban.reason || "Không có lý do";

      embed.addFields({
        name: `${user.username}#${user.discriminator}`,
        value: `**ID:** ${user.id}\n**Lý do:** ${reason}`,
        inline: true,
      });
    }

    embed.setFooter({
      text: `Tổng: ${bans.size} thành viên bị ban • Trang ${page}/${totalPages}`,
      iconURL: interaction.guild.iconURL({ dynamic: true }),
    });

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error("Lỗi khi lấy ban list:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể lấy danh sách thành viên bị ban. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

function getStatusEmoji(status) {
  const statusEmojis = {
    online: "🟢",
    idle: "🟡",
    dnd: "🔴",
    offline: "⚫",
  };
  return statusEmojis[status] || "⚫";
}
