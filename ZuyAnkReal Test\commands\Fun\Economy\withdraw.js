function _0x3e09(){const _0x35867e=['2303010VcHJMk','79598hWDlfm','amount','invalidAmount','setName','setColor','getInteger','addIntegerOption','#00FF00','22MwMqKB','546csUBbe','../../../models/UserData','488599wCxXzO','21894BvbCPj','315860FeXTZE','8141748NjdtAd','#FF0000','26ODChWN','footer','readFileSync','setDescription','Economy','exports','save','2754981ShjdIV','reply','utf8','./lang.yml','js-yaml','guild','Messages','8wHgqAx','24OBdSbY','./config.yml','Withdraw\x20coins\x20from\x20the\x20bank','bank','load','options','withdraw','Amount\x20to\x20withdraw','user','16LHrspC'];_0x3e09=function(){return _0x35867e;};return _0x3e09();}(function(_0x3ffbaf,_0xa059c0){function _0x18994c(_0x463e3d,_0x5d241f){return _0x3eae(_0x5d241f- -0x34e,_0x463e3d);}const _0x171468=_0x3ffbaf();while(!![]){try{const _0x362225=parseInt(_0x18994c(-0x24b,-0x241))/0x1+parseInt(_0x18994c(-0x256,-0x24c))/0x2*(parseInt(_0x18994c(-0x25f,-0x257))/0x3)+-parseInt(_0x18994c(-0x245,-0x24e))/0x4*(parseInt(_0x18994c(-0x22c,-0x23f))/0x5)+-parseInt(_0x18994c(-0x239,-0x240))/0x6*(-parseInt(_0x18994c(-0x249,-0x243))/0x7)+-parseInt(_0x18994c(-0x255,-0x258))/0x8*(-parseInt(_0x18994c(-0x225,-0x235))/0x9)+parseInt(_0x18994c(-0x248,-0x24d))/0xa*(parseInt(_0x18994c(-0x22f,-0x244))/0xb)+-parseInt(_0x18994c(-0x24e,-0x23e))/0xc*(parseInt(_0x18994c(-0x24c,-0x23c))/0xd);if(_0x362225===_0xa059c0)break;else _0x171468['push'](_0x171468['shift']());}catch(_0x4b9719){_0x171468['push'](_0x171468['shift']());}}}(_0x3e09,0x3cb66));const {SlashCommandBuilder,EmbedBuilder}=require('discord.js'),User=require(_0x19ed42(-0x1a3,-0x1b6));function _0x19ed42(_0x5b1eba,_0x3dcf29){return _0x3eae(_0x3dcf29- -0x2c2,_0x5b1eba);}const fs=require('fs'),yaml=require(_0x19ed42(-0x1a9,-0x1a5)),config=yaml[_0x19ed42(-0x1dc,-0x1c7)](fs[_0x19ed42(-0x1be,-0x1ae)](_0x19ed42(-0x1d4,-0x1ca),'utf8')),lang=yaml[_0x19ed42(-0x1d6,-0x1c7)](fs['readFileSync'](_0x19ed42(-0x1b6,-0x1a6),_0x19ed42(-0x1a3,-0x1a7))),{replacePlaceholders}=require('./Utility/helpers');function _0x3eae(_0x513abd,_0x8eac03){const _0x3e09c4=_0x3e09();return _0x3eae=function(_0x3eae82,_0x5d506b){_0x3eae82=_0x3eae82-0xf6;let _0x2e78c0=_0x3e09c4[_0x3eae82];return _0x2e78c0;},_0x3eae(_0x513abd,_0x8eac03);}module[_0x19ed42(-0x1bf,-0x1ab)]={'data':new SlashCommandBuilder()['setName'](_0x19ed42(-0x1d1,-0x1c5))[_0x19ed42(-0x199,-0x1ad)](_0x19ed42(-0x1ce,-0x1c9))[_0x19ed42(-0x1c6,-0x1ba)](_0x508d76=>_0x508d76[_0x19ed42(-0x1ab,-0x1bd)](_0x19ed42(-0x1b6,-0x1bf))[_0x19ed42(-0x1af,-0x1ad)](_0x19ed42(-0x1bc,-0x1c4))['setRequired'](!![])),'category':'Economy',async 'execute'(_0x4bb3dd){const _0x54f702=_0x4bb3dd[_0x55ee71(0x4f,0x53)][_0x55ee71(0x5a,0x69)](_0x55ee71(0x56,0x5e));if(_0x54f702<=0x0)return _0x4bb3dd[_0x55ee71(0x6d,0x72)]({'content':lang['Economy'][_0x55ee71(0x72,0x78)][_0x55ee71(0x57,0x60)],'ephemeral':!![]});const _0x9b6b1a=await User['findOne']({'userId':_0x4bb3dd[_0x55ee71(0x52,0x45)]['id'],'guildId':_0x4bb3dd[_0x55ee71(0x71,0x78)]['id']},{'bank':0x1,'balance':0x1});if(!_0x9b6b1a||_0x9b6b1a['bank']<_0x54f702){const _0x5ed2d7=new EmbedBuilder()['setDescription'](lang[_0x55ee71(0x69,0x6a)]['Messages']['noMoney'])[_0x55ee71(0x59,0x50)](_0x55ee71(0x64,0x61));return _0x4bb3dd[_0x55ee71(0x6d,0x62)]({'embeds':[_0x5ed2d7]});}_0x9b6b1a[_0x55ee71(0x4d,0x62)]-=_0x54f702,_0x9b6b1a['balance']+=_0x54f702,await _0x9b6b1a[_0x55ee71(0x6b,0x62)]();function _0x55ee71(_0x341066,_0x321f01){return _0x19ed42(_0x321f01,_0x341066-0x215);}const _0x5ac042=new EmbedBuilder()[_0x55ee71(0x68,0x68)](replacePlaceholders(lang['Economy']['Messages'][_0x55ee71(0x50,0x55)],{'coins':_0x54f702}))[_0x55ee71(0x59,0x68)](_0x55ee71(0x5c,0x6b))['setFooter']({'text':replacePlaceholders(lang[_0x55ee71(0x69,0x59)][_0x55ee71(0x72,0x76)][_0x55ee71(0x66,0x62)],{'balance':_0x9b6b1a['balance']})});return _0x4bb3dd[_0x55ee71(0x6d,0x6d)]({'embeds':[_0x5ac042]});}};