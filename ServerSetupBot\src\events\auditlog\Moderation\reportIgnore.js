const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction, client) {
        try {
            // This would be triggered by a custom report ignore command or button
            if (!interaction.isCommand() && !interaction.isButton()) return;
            if (interaction.isCommand() && interaction.commandName !== 'report-ignore') return;
            if (interaction.isButton() && !interaction.customId.startsWith('ignore_report_')) return;
            
            console.log(`❌ Report ignored in ${interaction.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(interaction.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'REPORT_IGNORE')) return;
            
            // Get report details
            let reportId, ignoreReason;
            
            if (interaction.isCommand()) {
                reportId = interaction.options.getString('report_id');
                ignoreReason = interaction.options.getString('reason') || 'Không có lý do cụ thể';
            } else if (interaction.isButton()) {
                reportId = interaction.customId.split('_')[2];
                ignoreReason = 'Ignored via button';
            }
            
            const moderator = interaction.user;
            
            // Get report info before ignoring
            const reportInfo = await client.db.getReport(interaction.guild.id, reportId);
            if (!reportInfo) return;
            
            // Update report status
            await client.db.updateReportStatus(interaction.guild.id, reportId, 'ignored', moderator.id, ignoreReason);
            
            // Create event data
            const eventData = {
                eventType: 'REPORT_IGNORE',
                user: moderator.tag,
                userId: moderator.id,
                action: `Report được từ chối bởi ${moderator.tag}`,
                details: `Report #${reportId} đã được từ chối bởi **${moderator.tag}**`,
                target: reportInfo.targetUser || 'Unknown',
                timestamp: new Date().toISOString(),
                reportId: reportId
            };
            
            // Add to database
            await client.db.addAuditLog(interaction.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '❌ Report được từ chối',
                `Vừa có report được từ chối`
            );
            
            embed.setColor(0x95a5a6); // Gray for ignored
            
            embed.addFields([
                {
                    name: '> ID Report',
                    value: `- #${reportId}`,
                    inline: true
                },
                {
                    name: '> Moderator từ chối',
                    value: `- ${moderator.tag}`,
                    inline: true
                },
                {
                    name: '> Thời gian từ chối',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add original report info
            if (reportInfo) {
                embed.addFields([
                    {
                        name: '> Người report gốc',
                        value: `- ${reportInfo.reporter || 'Unknown'}`,
                        inline: true
                    },
                    {
                        name: '> Đối tượng bị report',
                        value: `- ${reportInfo.targetUser || 'Unknown'}`,
                        inline: true
                    },
                    {
                        name: '> Thời gian tạo report',
                        value: `- <t:${Math.floor(new Date(reportInfo.createdAt).getTime() / 1000)}:F>`,
                        inline: true
                    }
                ]);
                
                if (reportInfo.reason) {
                    embed.addFields({
                        name: '> Lý do report gốc',
                        value: `- ${reportInfo.reason}`,
                        inline: false
                    });
                }
                
                if (reportInfo.type) {
                    const reportTypes = {
                        'spam': 'Spam',
                        'harassment': 'Quấy rối',
                        'inappropriate': 'Nội dung không phù hợp',
                        'scam': 'Lừa đảo',
                        'raid': 'Raid/Tấn công',
                        'general': 'Khác'
                    };
                    
                    embed.addFields({
                        name: '> Loại report',
                        value: `- ${reportTypes[reportInfo.type] || reportInfo.type}`,
                        inline: true
                    });
                }
            }
            
            // Add ignore reason
            embed.addFields({
                name: '> Lý do từ chối',
                value: `- ${ignoreReason}`,
                inline: false
            });
            
            // Calculate report lifetime
            if (reportInfo?.createdAt) {
                const reportLifetime = Date.now() - new Date(reportInfo.createdAt).getTime();
                const days = Math.floor(reportLifetime / (1000 * 60 * 60 * 24));
                const hours = Math.floor((reportLifetime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((reportLifetime % (1000 * 60 * 60)) / (1000 * 60));
                
                let lifetimeText = '';
                if (days > 0) lifetimeText += `${days} ngày `;
                if (hours > 0) lifetimeText += `${hours} giờ `;
                if (minutes > 0) lifetimeText += `${minutes} phút`;
                
                embed.addFields({
                    name: '> Thời gian xử lý',
                    value: `- ${lifetimeText.trim()}`,
                    inline: true
                });
            }
            
            // Add impact of ignoring
            embed.addFields({
                name: '> 📝 Tác động của việc từ chối',
                value: [
                    '• Report được đánh dấu là không hợp lệ',
                    '• Không có hành động nào được thực hiện',
                    '• Ghi nhận quyết định của moderator',
                    '• Có thể ảnh hưởng đến uy tín người report'
                ].join('\n'),
                inline: false
            });
            
            // Add common reasons for ignoring
            embed.addFields({
                name: '> 🤔 Lý do thường gặp khi từ chối',
                value: [
                    '• Thiếu bằng chứng rõ ràng',
                    '• Không vi phạm quy tắc server',
                    '• Report trùng lặp',
                    '• Vấn đề đã được giải quyết'
                ].join('\n'),
                inline: false
            });
            
            // Add guidelines
            embed.addFields({
                name: '> 💡 Lưu ý khi từ chối report',
                value: [
                    '• Giải thích rõ lý do từ chối',
                    '• Cân nhắc feedback cho người report',
                    '• Ghi chép quyết định chi tiết',
                    '• Có thể cần thảo luận với team'
                ].join('\n'),
                inline: false
            });
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${interaction.guild.name} • Report Ignore`,
                iconURL: interaction.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Report ignore logged: #${reportId}`);
            
        } catch (error) {
            console.error('Error in reportIgnore audit log:', error);
        }
    }
};
