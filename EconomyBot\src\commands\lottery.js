const { SlashCommandBuilder } = require('discord.js');
const User = require('../models/User');
const { parseAmount, validateAmount } = require('../utils/economy');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed, formatCurrency } = require('../utils/embedBuilder');
const config = require('../config/config');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('lottery')
        .setNameLocalizations({
            'vi': 'xoso'
        })
        .setDescription('Mua vé số và thử vận may với giải thưởng lớn')
        .addSubcommand(subcommand =>
            subcommand
                .setName('buy')
                .setNameLocalizations({
                    'vi': 'muave'
                })
                .setDescription('Mua vé số với 6 con số')
                .addStringOption(option =>
                    option.setName('numbers')
                        .setNameLocalizations({
                            'vi': 'conso'
                        })
                        .setDescription('6 con số từ 1-49, cách nhau bằng dấu cách (VD: 1 15 23 31 42 49)')
                        .setRequired(true)
                )
                .addIntegerOption(option =>
                    option.setName('quantity')
                        .setNameLocalizations({
                            'vi': 'soluong'
                        })
                        .setDescription('Số lượng vé muốn mua (mỗi vé 1000 xu)')
                        .setRequired(false)
                        .setMinValue(1)
                        .setMaxValue(10)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('quick')
                .setNameLocalizations({
                    'vi': 'nhanh'
                })
                .setDescription('Mua vé số với con số ngẫu nhiên')
                .addIntegerOption(option =>
                    option.setName('quantity')
                        .setNameLocalizations({
                            'vi': 'soluong'
                        })
                        .setDescription('Số lượng vé muốn mua (mỗi vé 1000 xu)')
                        .setRequired(false)
                        .setMinValue(1)
                        .setMaxValue(10)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('check')
                .setNameLocalizations({
                    'vi': 'kiemtra'
                })
                .setDescription('Kiểm tra kết quả xổ số ngay lập tức')
        ),
    
    cooldown: 10,

    async execute(interaction) {
        try {
            const subcommand = interaction.options.getSubcommand();

            switch (subcommand) {
                case 'buy':
                    await handleBuyTicket(interaction);
                    break;
                case 'quick':
                    await handleQuickBuy(interaction);
                    break;
                case 'check':
                    await handleCheckResults(interaction);
                    break;
            }
            
        } catch (error) {
            console.error('Lỗi lệnh lottery:', error);
            
            const errorEmbed = createErrorEmbed(
                'Lỗi xổ số',
                'Không thể thực hiện giao dịch xổ số. Vui lòng thử lại sau!'
            );
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};

async function handleBuyTicket(interaction) {
    const numbersStr = interaction.options.getString('numbers');
    const quantity = interaction.options.getInteger('quantity') || 1;
    
    try {
        const userData = await User.findOrCreate(interaction.user);
        const ticketPrice = 1000;
        const totalCost = ticketPrice * quantity;
        
        // Kiểm tra số dư
        if (userData.balance < totalCost) {
            const errorEmbed = createErrorEmbed(
                'Số dư không đủ',
                `Bạn cần **${formatCurrency(totalCost)}** để mua ${quantity} vé số\n` +
                `Số dư hiện tại: **${formatCurrency(userData.balance)}**`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Parse và validate số
        const numbers = numbersStr.split(' ').map(n => parseInt(n.trim()));
        
        if (numbers.length !== 6) {
            const errorEmbed = createErrorEmbed(
                'Số lượng con số không đúng',
                'Bạn phải chọn đúng 6 con số!\n**Ví dụ:** 1 15 23 31 42 49'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Kiểm tra số hợp lệ (1-49)
        for (const num of numbers) {
            if (isNaN(num) || num < 1 || num > 49) {
                const errorEmbed = createErrorEmbed(
                    'Con số không hợp lệ',
                    'Tất cả con số phải từ 1 đến 49!\n**Ví dụ:** 1 15 23 31 42 49'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }

        // Kiểm tra số trùng lặp
        const uniqueNumbers = [...new Set(numbers)];
        if (uniqueNumbers.length !== 6) {
            const errorEmbed = createErrorEmbed(
                'Con số bị trùng lặp',
                'Tất cả 6 con số phải khác nhau!\n**Ví dụ:** 1 15 23 31 42 49'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Sắp xếp số theo thứ tự
        numbers.sort((a, b) => a - b);

        // Trừ tiền
        await userData.updateBalance(-totalCost, 'lottery_buy');

        // Tạo embed xác nhận
        const confirmEmbed = createSuccessEmbed(
            'Đã mua vé số thành công!',
            `🎫 **Số vé:** ${quantity}\n` +
            `🔢 **Con số của bạn:** ${numbers.join(' - ')}\n` +
            `💰 **Tổng chi phí:** ${formatCurrency(totalCost)}\n\n` +
            `${config.emojis.money} **Số dư còn lại:** ${formatCurrency(userData.balance - totalCost)}\n\n` +
            `🍀 **Chúc bạn may mắn!** Sử dụng \`/lottery check\` để xem kết quả ngay!`
        );

        confirmEmbed.addFields({
            name: `${config.emojis.info} Cách thức trúng thưởng`,
            value: '🏆 **6 số đúng:** x1000 (Jackpot!)\n' +
                   '🥈 **5 số đúng:** x100\n' +
                   '🥉 **4 số đúng:** x20\n' +
                   '🎖️ **3 số đúng:** x5\n' +
                   '🎗️ **2 số đúng:** x2',
            inline: false
        });

        await interaction.reply({ embeds: [confirmEmbed] });
        
        // Tự động kiểm tra kết quả sau 3 giây
        setTimeout(async () => {
            await checkLotteryResult(interaction, numbers, quantity, ticketPrice);
        }, 3000);
        
    } catch (error) {
        const errorEmbed = createErrorEmbed(
            'Lỗi mua vé',
            error.message || 'Không thể mua vé số'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleQuickBuy(interaction) {
    const quantity = interaction.options.getInteger('quantity') || 1;
    
    try {
        const userData = await User.findOrCreate(interaction.user);
        const ticketPrice = 1000;
        const totalCost = ticketPrice * quantity;
        
        // Kiểm tra số dư
        if (userData.balance < totalCost) {
            const errorEmbed = createErrorEmbed(
                'Số dư không đủ',
                `Bạn cần **${formatCurrency(totalCost)}** để mua ${quantity} vé số\n` +
                `Số dư hiện tại: **${formatCurrency(userData.balance)}**`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Tạo số ngẫu nhiên
        const numbers = [];
        while (numbers.length < 6) {
            const num = Math.floor(Math.random() * 49) + 1;
            if (!numbers.includes(num)) {
                numbers.push(num);
            }
        }
        numbers.sort((a, b) => a - b);

        // Trừ tiền
        await userData.updateBalance(-totalCost, 'lottery_quick');

        // Tạo embed xác nhận
        const confirmEmbed = createSuccessEmbed(
            'Đã mua vé số nhanh!',
            `🎫 **Số vé:** ${quantity}\n` +
            `🎲 **Con số ngẫu nhiên:** ${numbers.join(' - ')}\n` +
            `💰 **Tổng chi phí:** ${formatCurrency(totalCost)}\n\n` +
            `${config.emojis.money} **Số dư còn lại:** ${formatCurrency(userData.balance - totalCost)}\n\n` +
            `🍀 **Chúc bạn may mắn!** Đang kiểm tra kết quả...`
        );

        await interaction.reply({ embeds: [confirmEmbed] });
        
        // Tự động kiểm tra kết quả sau 2 giây
        setTimeout(async () => {
            await checkLotteryResult(interaction, numbers, quantity, ticketPrice);
        }, 2000);
        
    } catch (error) {
        const errorEmbed = createErrorEmbed(
            'Lỗi mua vé nhanh',
            error.message || 'Không thể mua vé số nhanh'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleCheckResults(interaction) {
    // Tạo số trúng thưởng ngẫu nhiên
    const winningNumbers = [];
    while (winningNumbers.length < 6) {
        const num = Math.floor(Math.random() * 49) + 1;
        if (!winningNumbers.includes(num)) {
            winningNumbers.push(num);
        }
    }
    winningNumbers.sort((a, b) => a - b);

    const resultEmbed = createInfoEmbed(
        'Kết quả xổ số hôm nay!',
        `🎰 **Số trúng thưởng:** ${winningNumbers.join(' - ')}\n\n` +
        `🏆 **Giải thưởng:**\n` +
        `• 6 số đúng: x1000 (Jackpot!)\n` +
        `• 5 số đúng: x100\n` +
        `• 4 số đúng: x20\n` +
        `• 3 số đúng: x5\n` +
        `• 2 số đúng: x2\n\n` +
        `💡 **Mẹo:** Sử dụng \`/lottery buy\` hoặc \`/lottery quick\` để mua vé!`
    );

    await interaction.reply({ embeds: [resultEmbed] });
}

async function checkLotteryResult(interaction, playerNumbers, quantity, ticketPrice) {
    try {
        // Tạo số trúng thưởng
        const winningNumbers = [];
        while (winningNumbers.length < 6) {
            const num = Math.floor(Math.random() * 49) + 1;
            if (!winningNumbers.includes(num)) {
                winningNumbers.push(num);
            }
        }
        winningNumbers.sort((a, b) => a - b);

        // Đếm số trùng khớp
        const matches = playerNumbers.filter(num => winningNumbers.includes(num)).length;
        
        // Tính giải thưởng
        const multipliers = {
            6: 1000, // Jackpot
            5: 100,
            4: 20,
            3: 5,
            2: 2
        };

        const multiplier = multipliers[matches] || 0;
        const prize = multiplier > 0 ? ticketPrice * multiplier * quantity : 0;

        let resultEmbed;

        if (prize > 0) {
            // Thắng giải
            const userData = await User.findOrCreate(interaction.user);
            await userData.updateBalance(prize, 'lottery_win');
            await userData.addExperience(Math.floor(prize / 50));

            let prizeTitle = '';
            if (matches === 6) prizeTitle = '🏆 JACKPOT! 🏆';
            else if (matches === 5) prizeTitle = '🥈 Giải nhì!';
            else if (matches === 4) prizeTitle = '🥉 Giải ba!';
            else if (matches === 3) prizeTitle = '🎖️ Giải khuyến khích!';
            else if (matches === 2) prizeTitle = '🎗️ Giải may mắn!';

            resultEmbed = createSuccessEmbed(
                prizeTitle,
                `🎰 **Số trúng thưởng:** ${winningNumbers.join(' - ')}\n` +
                `🎫 **Số của bạn:** ${playerNumbers.join(' - ')}\n\n` +
                `✅ **Trùng khớp:** ${matches}/6 số\n` +
                `💰 **Giải thưởng:** ${formatCurrency(prize)} (x${multiplier})\n\n` +
                `${config.emojis.money} **Số dư hiện tại:** ${formatCurrency(userData.balance + prize)}`
            );

            if (matches === 6) {
                resultEmbed.addFields({
                    name: `${config.emojis.crown} Chúc mừng!`,
                    value: 'Bạn đã trúng Jackpot! Đây là giải thưởng cao nhất!',
                    inline: false
                });
            }
        } else {
            // Không trúng giải
            resultEmbed = createErrorEmbed(
                'Chưa trúng giải!',
                `🎰 **Số trúng thưởng:** ${winningNumbers.join(' - ')}\n` +
                `🎫 **Số của bạn:** ${playerNumbers.join(' - ')}\n\n` +
                `❌ **Trùng khớp:** ${matches}/6 số\n` +
                `💸 **Giải thưởng:** Không có\n\n` +
                `🍀 **Đừng nản lòng!** Thử lại lần sau nhé!`
            );

            resultEmbed.addFields({
                name: `${config.emojis.info} Mẹo`,
                value: 'Cần ít nhất 2 số trùng khớp để có giải thưởng!',
                inline: false
            });
        }

        await interaction.followUp({ embeds: [resultEmbed] });
        
    } catch (error) {
        console.error('Lỗi kiểm tra kết quả xổ số:', error);
    }
}
