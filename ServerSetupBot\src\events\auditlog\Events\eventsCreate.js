const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildScheduledEventCreate,
    async execute(guildScheduledEvent, client) {
        try {
            console.log(`📅 Scheduled event created: ${guildScheduledEvent.name} in ${guildScheduledEvent.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(guildScheduledEvent.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EVENTS_CREATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EVENTS_CREATE',
                user: 'System',
                userId: null,
                action: 'Sự kiện được tạo',
                details: `Sự kiện **${guildScheduledEvent.name}** đ<PERSON> được tạo`,
                target: guildScheduledEvent.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who created the event from audit logs
            try {
                const auditLogs = await guildScheduledEvent.guild.fetchAuditLogs({
                    type: 100, // GUILD_SCHEDULED_EVENT_CREATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === guildScheduledEvent.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Sự kiện được tạo bởi ${auditEntry.executor.tag}`;
                }
            } catch (error) {
                console.log('Could not fetch audit logs for event create');
            }
            
            // Add to database
            await client.db.addAuditLog(guildScheduledEvent.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📅 Sự kiện được tạo',
                `Vừa có một sự kiện mới được tạo trong server`
            );
            
            embed.setColor(0x2ecc71); // Green for create
            
            // Get entity type names
            const entityTypes = {
                1: 'Stage Instance',
                2: 'Voice Channel',
                3: 'External Location'
            };
            
            // Get status names
            const statusTypes = {
                1: 'Scheduled',
                2: 'Active',
                3: 'Completed',
                4: 'Cancelled'
            };
            
            embed.addFields([
                {
                    name: '> Tên sự kiện',
                    value: `- ${guildScheduledEvent.name}`,
                    inline: true
                },
                {
                    name: '> ID sự kiện',
                    value: `- ${guildScheduledEvent.id}`,
                    inline: true
                },
                {
                    name: '> Loại sự kiện',
                    value: `- ${entityTypes[guildScheduledEvent.entityType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${statusTypes[guildScheduledEvent.status] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người tạo',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian tạo',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add event timing
            if (guildScheduledEvent.scheduledStartTimestamp) {
                embed.addFields([
                    {
                        name: '> Thời gian bắt đầu',
                        value: `- <t:${Math.floor(guildScheduledEvent.scheduledStartTimestamp / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Bắt đầu sau',
                        value: `- <t:${Math.floor(guildScheduledEvent.scheduledStartTimestamp / 1000)}:R>`,
                        inline: true
                    }
                ]);
            }
            
            if (guildScheduledEvent.scheduledEndTimestamp) {
                embed.addFields({
                    name: '> Thời gian kết thúc',
                    value: `- <t:${Math.floor(guildScheduledEvent.scheduledEndTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add location/channel info
            if (guildScheduledEvent.channel) {
                embed.addFields({
                    name: '> Kênh',
                    value: `- ${guildScheduledEvent.channel}`,
                    inline: true
                });
            } else if (guildScheduledEvent.entityMetadata?.location) {
                embed.addFields({
                    name: '> Địa điểm',
                    value: `- ${guildScheduledEvent.entityMetadata.location}`,
                    inline: true
                });
            }
            
            // Add description if available
            if (guildScheduledEvent.description) {
                const description = guildScheduledEvent.description.length > 500 
                    ? guildScheduledEvent.description.substring(0, 500) + '...'
                    : guildScheduledEvent.description;
                
                embed.addFields({
                    name: '> Mô tả',
                    value: `\`\`\`${description}\`\`\``,
                    inline: false
                });
            }
            
            // Add event image if available
            if (guildScheduledEvent.image) {
                embed.setImage(guildScheduledEvent.image);
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${guildScheduledEvent.guild.name} • Event Create`,
                iconURL: guildScheduledEvent.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Event create logged: ${guildScheduledEvent.name}`);
            
        } catch (error) {
            console.error('Error in eventsCreate audit log:', error);
        }
    }
};
