const { EmbedBuilder } = require('discord.js');

// Gửi log moderation vào channel
async function sendModerationLog(client, guildId, logData) {
    try {
        const config = await client.db.getModerationConfig(guildId);
        if (!config || !config.log_channel_id) return;
        
        const channel = client.channels.cache.get(config.log_channel_id);
        if (!channel) return;
        
        const embed = new EmbedBuilder()
            .setColor(getModerationColor(logData.type))
            .setTitle(`${getModerationEmoji(logData.type)} ${getModerationTitle(logData.type)}`)
            .addFields(
                { name: '👤 Thành viên', value: `${logData.user.tag} (${logData.user.id})`, inline: true },
                { name: '🛡️ Moderator', value: `${logData.moderator.tag}`, inline: true },
                { name: '📝 Lý do', value: logData.reason, inline: false }
            )
            .setThumbnail(logData.user.displayAvatarURL({ dynamic: true }))
            .setTimestamp()
            .setFooter({ text: 'Moderation Log', iconURL: client.user.displayAvatarURL() });
        
        if (logData.duration) {
            embed.addFields({ name: '⏰ Thời gian', value: logData.duration, inline: true });
        }
        
        if (logData.deleteDays !== undefined) {
            embed.addFields({ name: '🗑️ Xóa tin nhắn', value: `${logData.deleteDays} ngày`, inline: true });
        }
        
        if (logData.warningCount) {
            embed.addFields({ name: '⚠️ Tổng cảnh báo', value: `${logData.warningCount}`, inline: true });
        }
        
        if (logData.count) {
            embed.addFields({ name: '📊 Số lượng', value: `${logData.count}`, inline: true });
        }
        
        if (logData.filter) {
            embed.addFields({ name: '🔍 Filter', value: logData.filter, inline: true });
        }
        
        if (logData.channel) {
            embed.addFields({ name: '📍 Kênh', value: `${logData.channel}`, inline: true });
        }
        
        await channel.send({ embeds: [embed] });
    } catch (error) {
        console.error('Lỗi khi gửi moderation log:', error);
    }
}

// Lấy màu sắc cho từng loại moderation
function getModerationColor(type) {
    const colors = {
        'ban': 0xff0000,      // Red
        'unban': 0x00ff00,    // Green
        'kick': 0xff8800,     // Orange
        'timeout': 0xffff00,  // Yellow
        'untimeout': 0x00ffff, // Cyan
        'warn': 0xff4444,     // Light Red
        'unwarn': 0x44ff44,   // Light Green
        'mute': 0x8800ff,     // Purple
        'unmute': 0x88ff88,   // Light Green
        'purge': 0x444444     // Gray
    };
    return colors[type] || 0x0099ff;
}

// Lấy emoji cho từng loại moderation
function getModerationEmoji(type) {
    const emojis = {
        'ban': '🔨',
        'unban': '✅',
        'kick': '👢',
        'timeout': '⏰',
        'untimeout': '🔓',
        'warn': '⚠️',
        'unwarn': '✅',
        'mute': '🔇',
        'unmute': '🔊',
        'purge': '🗑️'
    };
    return emojis[type] || '📝';
}

// Lấy tiêu đề cho từng loại moderation
function getModerationTitle(type) {
    const titles = {
        'ban': 'Thành viên bị ban',
        'unban': 'Thành viên được unban',
        'kick': 'Thành viên bị kick',
        'timeout': 'Thành viên bị timeout',
        'untimeout': 'Thành viên được hủy timeout',
        'warn': 'Thành viên bị cảnh báo',
        'unwarn': 'Cảnh báo được hủy',
        'mute': 'Thành viên bị mute',
        'unmute': 'Thành viên được unmute',
        'purge': 'Tin nhắn bị xóa'
    };
    return titles[type] || 'Hành động moderation';
}

// Format thời gian từ phút sang chuỗi đẹp
function formatDuration(minutes) {
    if (minutes < 60) {
        return `${minutes} phút`;
    } else if (minutes < 1440) {
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        return remainingMinutes > 0 ? `${hours} giờ ${remainingMinutes} phút` : `${hours} giờ`;
    } else {
        const days = Math.floor(minutes / 1440);
        const remainingHours = Math.floor((minutes % 1440) / 60);
        return remainingHours > 0 ? `${days} ngày ${remainingHours} giờ` : `${days} ngày`;
    }
}

// Parse chuỗi thời gian thành phút
function parseDuration(durationStr) {
    const regex = /(\d+)([smhd])/g;
    let totalMinutes = 0;
    let match;
    
    while ((match = regex.exec(durationStr)) !== null) {
        const value = parseInt(match[1]);
        const unit = match[2];
        
        switch (unit) {
            case 's':
                totalMinutes += Math.ceil(value / 60);
                break;
            case 'm':
                totalMinutes += value;
                break;
            case 'h':
                totalMinutes += value * 60;
                break;
            case 'd':
                totalMinutes += value * 1440;
                break;
        }
    }
    
    return totalMinutes;
}

// Kiểm tra hierarchy giữa 2 members
function checkHierarchy(executor, target, botMember) {
    // Kiểm tra executor vs target
    if (target.roles.highest.position >= executor.roles.highest.position) {
        return {
            canExecute: false,
            reason: 'Bạn không thể thực hiện hành động này với thành viên có role cao hơn hoặc bằng bạn.'
        };
    }
    
    // Kiểm tra bot vs target
    if (target.roles.highest.position >= botMember.roles.highest.position) {
        return {
            canExecute: false,
            reason: 'Bot không thể thực hiện hành động này với thành viên có role cao hơn hoặc bằng bot.'
        };
    }
    
    return { canExecute: true };
}

// Validate user input
function validateModerationInput(type, options) {
    const errors = [];
    
    switch (type) {
        case 'ban':
            if (options.deleteDays < 0 || options.deleteDays > 7) {
                errors.push('Số ngày xóa tin nhắn phải từ 0-7');
            }
            break;
            
        case 'timeout':
            if (options.duration < 1 || options.duration > 40320) {
                errors.push('Thời gian timeout phải từ 1 phút đến 28 ngày (40320 phút)');
            }
            break;
            
        case 'purge':
            if (options.amount < 1 || options.amount > 100) {
                errors.push('Số tin nhắn xóa phải từ 1-100');
            }
            break;
    }
    
    if (options.reason && options.reason.length > 512) {
        errors.push('Lý do không được vượt quá 512 ký tự');
    }
    
    return errors;
}

// Format warning list
function formatWarningList(warnings) {
    if (!warnings || warnings.length === 0) {
        return 'Không có cảnh báo nào.';
    }
    
    return warnings.map((warning, index) => {
        const date = new Date(warning.created_at).toLocaleDateString('vi-VN');
        return `**${index + 1}.** ${warning.reason}\n` +
               `   📅 ${date} | 👮 <@${warning.moderator_id}> | ID: ${warning.id}`;
    }).join('\n\n');
}

// Check if user can be moderated
function canModerate(member, action) {
    switch (action) {
        case 'ban':
            return member.bannable;
        case 'kick':
            return member.kickable;
        case 'timeout':
            return member.moderatable;
        case 'mute':
            return member.manageable;
        default:
            return true;
    }
}

module.exports = {
    sendModerationLog,
    getModerationColor,
    getModerationEmoji,
    getModerationTitle,
    formatDuration,
    parseDuration,
    checkHierarchy,
    validateModerationInput,
    formatWarningList,
    canModerate
};
