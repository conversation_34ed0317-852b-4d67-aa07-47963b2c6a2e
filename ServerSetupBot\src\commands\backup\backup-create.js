const { SlashCommandBuilder } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createLoadingEmbed } = require('../../utils/embedBuilder.js');
const { isValidBackupName } = require('../../utils/validators.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('backup-create')
        .setDescription('Tạo bản sao lưu cấu hình server')
        .addStringOption(option =>
            option.setName('name')
                .setDescription('Tên backup')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('type')
                .setDescription('Loại backup')
                .setRequired(false)
                .addChoices(
                    { name: '🏗️ Full - Toàn bộ server', value: 'full' },
                    { name: '📝 Channels - Chỉ channels', value: 'channels' },
                    { name: '🎭 Roles - Chỉ roles', value: 'roles' },
                    { name: '⚙️ Settings - Chỉ cấu hình bot', value: 'settings' }
                )
        )
        .addStringOption(option =>
            option.setName('description')
                .setDescription('Mô tả backup (tùy chọn)')
                .setRequired(false)
        ),
    category: 'backup',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        const name = interaction.options.getString('name');
        const type = interaction.options.getString('type') || 'full';
        const description = interaction.options.getString('description') || '';
        const guildId = interaction.guild.id;
        
        // Validate backup name
        if (!isValidBackupName(name)) {
            const errorEmbed = createErrorEmbed(
                'Tên backup không hợp lệ!',
                'Tên backup chỉ được chứa:\n• Chữ cái (a-z, A-Z)\n• Số (0-9)\n• Khoảng trắng\n• Dấu gạch ngang (-)\n• Dấu gạch dưới (_)\n• Dấu ngoặc đơn ()\n• Độ dài: 1-50 ký tự'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        // Check if backup name already exists
        const existingBackups = await client.db.getServerBackups(guildId);
        const nameExists = existingBackups.some(backup => backup.backup_name === name);
        
        if (nameExists) {
            const errorEmbed = createErrorEmbed(
                'Tên backup đã tồn tại!',
                `Backup với tên "${name}" đã tồn tại.\n\nVui lòng chọn tên khác hoặc xóa backup cũ trước.`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        // Show loading message
        const loadingEmbed = createLoadingEmbed(
            'Đang tạo backup...',
            `Đang sao lưu ${getTypeDescription(type)} của server.\nQuá trình này có thể mất vài phút...`
        );
        await interaction.reply({ embeds: [loadingEmbed] });
        
        try {
            // Create backup data
            const backupData = await createBackupData(interaction.guild, type, client);
            
            // Calculate file size (approximate)
            const dataString = JSON.stringify(backupData);
            const fileSize = Buffer.byteLength(dataString, 'utf8');
            
            // Save backup to database
            const result = await client.db.createServerBackup(guildId, {
                name: name,
                data: {
                    ...backupData,
                    description: description,
                    createdAt: new Date().toISOString(),
                    guildName: interaction.guild.name,
                    guildIcon: interaction.guild.iconURL({ dynamic: true })
                },
                type: type,
                createdBy: interaction.user.id,
                fileSize: fileSize
            });
            
            const successEmbed = createSuccessEmbed(
                'Backup đã được tạo thành công!',
                `**Tên:** ${name}\n` +
                `**Loại:** ${getTypeDescription(type)}\n` +
                `**Kích thước:** ${formatFileSize(fileSize)}\n` +
                `**ID Backup:** ${result.id}\n` +
                `**Mô tả:** ${description || 'Không có'}`
            );
            
            successEmbed.addFields({
                name: '📊 Thống kê backup',
                value: getBackupStats(backupData, type),
                inline: false
            });
            
            successEmbed.addFields({
                name: '🔄 Khôi phục',
                value: `Sử dụng \`/backup-restore name:${name}\` để khôi phục backup này.`,
                inline: false
            });
            
            await interaction.editReply({ embeds: [successEmbed] });
            
        } catch (error) {
            console.error('Lỗi khi tạo backup:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi tạo backup!',
                'Đã xảy ra lỗi khi tạo backup. Vui lòng thử lại sau!'
            );
            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },
};

async function createBackupData(guild, type, client) {
    const data = {
        type: type,
        guildId: guild.id,
        guildName: guild.name,
        memberCount: guild.memberCount,
        createdAt: new Date().toISOString()
    };
    
    if (type === 'full' || type === 'channels') {
        data.channels = await backupChannels(guild);
    }
    
    if (type === 'full' || type === 'roles') {
        data.roles = await backupRoles(guild);
    }
    
    if (type === 'full' || type === 'settings') {
        data.settings = await backupSettings(guild, client);
    }
    
    return data;
}

async function backupChannels(guild) {
    const channels = {
        categories: [],
        textChannels: [],
        voiceChannels: []
    };
    
    // Backup categories
    guild.channels.cache
        .filter(channel => channel.type === 4) // GuildCategory
        .forEach(category => {
            channels.categories.push({
                id: category.id,
                name: category.name,
                position: category.position,
                permissions: category.permissionOverwrites.cache.map(overwrite => ({
                    id: overwrite.id,
                    type: overwrite.type,
                    allow: overwrite.allow.bitfield.toString(),
                    deny: overwrite.deny.bitfield.toString()
                }))
            });
        });
    
    // Backup text channels
    guild.channels.cache
        .filter(channel => channel.type === 0) // GuildText
        .forEach(channel => {
            channels.textChannels.push({
                id: channel.id,
                name: channel.name,
                topic: channel.topic,
                nsfw: channel.nsfw,
                rateLimitPerUser: channel.rateLimitPerUser,
                position: channel.position,
                parentId: channel.parentId,
                permissions: channel.permissionOverwrites.cache.map(overwrite => ({
                    id: overwrite.id,
                    type: overwrite.type,
                    allow: overwrite.allow.bitfield.toString(),
                    deny: overwrite.deny.bitfield.toString()
                }))
            });
        });
    
    // Backup voice channels
    guild.channels.cache
        .filter(channel => channel.type === 2) // GuildVoice
        .forEach(channel => {
            channels.voiceChannels.push({
                id: channel.id,
                name: channel.name,
                userLimit: channel.userLimit,
                bitrate: channel.bitrate,
                position: channel.position,
                parentId: channel.parentId,
                permissions: channel.permissionOverwrites.cache.map(overwrite => ({
                    id: overwrite.id,
                    type: overwrite.type,
                    allow: overwrite.allow.bitfield.toString(),
                    deny: overwrite.deny.bitfield.toString()
                }))
            });
        });
    
    return channels;
}

async function backupRoles(guild) {
    const roles = [];
    
    guild.roles.cache
        .filter(role => role.id !== guild.id) // Exclude @everyone
        .forEach(role => {
            roles.push({
                id: role.id,
                name: role.name,
                color: role.color,
                hoist: role.hoist,
                mentionable: role.mentionable,
                permissions: role.permissions.bitfield.toString(),
                position: role.position
            });
        });
    
    return roles;
}

async function backupSettings(guild, client) {
    const settings = {};
    
    try {
        // Server config
        const serverConfig = await client.db.getServerConfig(guild.id);
        if (serverConfig) {
            settings.server = serverConfig;
        }
        
        // Welcome/Goodbye config
        const welcomeConfig = await client.db.getWelcomeConfig(guild.id);
        if (welcomeConfig) {
            settings.welcome = welcomeConfig;
        }
        
        // Auto roles
        const autoRoles = await client.db.getAutoRoles(guild.id);
        if (autoRoles.length > 0) {
            settings.autoRoles = autoRoles;
        }
        
        // Reaction roles
        const reactionRoles = await client.db.getReactionRoles(guild.id);
        if (reactionRoles.length > 0) {
            settings.reactionRoles = reactionRoles;
        }
        
        // Verification config
        const verificationConfig = await client.db.getVerificationConfig(guild.id);
        if (verificationConfig) {
            settings.verification = verificationConfig;
        }
        
        // Moderation config
        const moderationConfig = await client.db.getModerationConfig(guild.id);
        if (moderationConfig) {
            settings.moderation = moderationConfig;
        }
        
    } catch (error) {
        console.error('Lỗi khi backup settings:', error);
    }
    
    return settings;
}

function getTypeDescription(type) {
    const descriptions = {
        'full': '🏗️ Toàn bộ server',
        'channels': '📝 Channels và categories',
        'roles': '🎭 Roles và permissions',
        'settings': '⚙️ Cấu hình bot'
    };
    return descriptions[type] || type;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getBackupStats(data, type) {
    const stats = [];
    
    if (data.channels) {
        stats.push(`• ${data.channels.categories.length} danh mục`);
        stats.push(`• ${data.channels.textChannels.length} kênh text`);
        stats.push(`• ${data.channels.voiceChannels.length} kênh voice`);
    }
    
    if (data.roles) {
        stats.push(`• ${data.roles.length} vai trò`);
    }
    
    if (data.settings) {
        const settingsCount = Object.keys(data.settings).length;
        stats.push(`• ${settingsCount} cấu hình bot`);
    }
    
    return stats.join('\n') || 'Không có dữ liệu';
}
