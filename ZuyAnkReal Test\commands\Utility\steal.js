(function(_0x2d2259,_0xff1a21){const _0x84ecf5=_0x2d2259();function _0x8a0001(_0x529b06,_0x40d392){return _0x2660(_0x529b06-0x2c2,_0x40d392);}while(!![]){try{const _0x481609=-parseInt(_0x8a0001(0x3e9,0x3e1))/0x1*(-parseInt(_0x8a0001(0x3cc,0x3d3))/0x2)+parseInt(_0x8a0001(0x411,0x3f6))/0x3+parseInt(_0x8a0001(0x3f8,0x3dc))/0x4*(parseInt(_0x8a0001(0x3e4,0x3e2))/0x5)+-parseInt(_0x8a0001(0x414,0x419))/0x6*(-parseInt(_0x8a0001(0x416,0x434))/0x7)+-parseInt(_0x8a0001(0x3e8,0x3cb))/0x8+-parseInt(_0x8a0001(0x3d5,0x3d2))/0x9*(-parseInt(_0x8a0001(0x3de,0x3d1))/0xa)+-parseInt(_0x8a0001(0x403,0x41b))/0xb;if(_0x481609===_0xff1a21)break;else _0x84ecf5['push'](_0x84ecf5['shift']());}catch(_0x1b7046){_0x84ecf5['push'](_0x84ecf5['shift']());}}}(_0x5694,0x4ddde));const {SlashCommandBuilder}=require(_0x4a72a6(0x262,0x25d)),{PermissionsBitField,MessageAttachment}=require(_0x4a72a6(0x258,0x253)),fs=require('fs'),axios=require('axios'),yaml=require('js-yaml'),config=yaml[_0x4a72a6(0x26d,0x25c)](fs[_0x4a72a6(0x22a,0x24c)](_0x4a72a6(0x257,0x230),'utf8')),lang=yaml[_0x4a72a6(0x26d,0x26a)](fs[_0x4a72a6(0x22a,0x214)](_0x4a72a6(0x26e,0x261),_0x4a72a6(0x22c,0x215)));function _0x5694(){const _0x1d7776=['./config.yml','discord.js','6159791drDvEr','name','Sticker\x20\x22','Here\x27s\x20the\x20sticker\x20\x22','Utility','fetched','.png','steal','matchAll','@discordjs/builders','exports','The\x20emoji(s)\x20you\x20want\x20to\x20borrow\x20(separate\x20multiple\x20with\x20spaces)','has','The\x20ID\x20of\x20the\x20message\x20containing\x20the\x20sticker','528342xuYnIv','Please\x20provide\x20valid\x20custom\x20emojis.','addtoserver','6sKmtAb','fetch','2260496ezfabg','load','./lang.yml','Optionally\x20add\x20the\x20sticker\x20to\x20your\x20server','permissions','Error\x20processing\x20emoji:','create','messages','The\x20specified\x20message\x20doesn\x27t\x20contain\x20a\x20sticker.','setName','message','1022uYPOrH','getString','stolen_sticker','ManageGuild','https://cdn.discordapp.com/emojis/','\x20-\x20[','error','binary','readFileSync','21897OPhizE','utf8','emoji','Optionally\x20add\x20the\x20emoji(s)\x20to\x20your\x20server','Couldn\x27t\x20find\x20a\x20message\x20with\x20that\x20ID\x20in\x20this\x20channel.','guild','join','Steal\x20one\x20or\x20more\x20emojis','options','1970LeTjeT','tags','No\x20emojis\x20were\x20processed\x20successfully.','toString','Borrows\x20emojis\x20or\x20stickers\x20from\x20other\x20sources','reply','1930925uZmKAD','data','from','length','4444896GqXEGA','137UvdAVD','addSubcommand','member','png','getSubcommand','first','Steal\x20a\x20sticker\x20from\x20a\x20specific\x20message','channel','Error\x20processing\x20sticker:','addBooleanOption','push','setDescription','sticker','gif','emojis','4rppCtU','arraybuffer','addStringOption','stickers','getBoolean','messageid','Flags','url','Successfully\x20'];_0x5694=function(){return _0x1d7776;};return _0x5694();}function _0x4a72a6(_0x44b4c9,_0x452c5b){return _0x2660(_0x44b4c9-0x118,_0x452c5b);}module[_0x4a72a6(0x263,0x26a)]={'data':new SlashCommandBuilder()['setName'](_0x4a72a6(0x260,0x279))['setDescription'](_0x4a72a6(0x238,0x21d))[_0x4a72a6(0x240,0x23e)](_0x16efda=>_0x16efda['setName'](_0x4a72a6(0x22d,0x20e))['setDescription'](_0x4a72a6(0x232,0x24c))[_0x4a72a6(0x250,0x277)](_0x4a4e16=>_0x4a4e16['setName'](_0x4a72a6(0x24d,0x242))[_0x4a72a6(0x24a,0x23e)](_0x4a72a6(0x264,0x285))['setRequired'](!![]))[_0x4a72a6(0x248,0x24b)](_0x5b468e=>_0x5b468e[_0x4a72a6(0x220,0x243)]('addtoserver')[_0x4a72a6(0x24a,0x256)](_0x4a72a6(0x22e,0x239))))[_0x4a72a6(0x240,0x239)](_0x19895b=>_0x19895b[_0x4a72a6(0x220,0x230)](_0x4a72a6(0x24b,0x250))[_0x4a72a6(0x24a,0x24a)](_0x4a72a6(0x245,0x22e))['addStringOption'](_0x7c3484=>_0x7c3484[_0x4a72a6(0x220,0x232)](_0x4a72a6(0x253,0x236))[_0x4a72a6(0x24a,0x24f)](_0x4a72a6(0x266,0x260))['setRequired'](!![]))[_0x4a72a6(0x248,0x250)](_0x3c4f4a=>_0x3c4f4a[_0x4a72a6(0x220,0x218)](_0x4a72a6(0x269,0x292))[_0x4a72a6(0x24a,0x238)](_0x4a72a6(0x26f,0x251)))),'category':_0x4a72a6(0x25d,0x27e),async 'execute'(_0x506037,_0x44c9e2){if(!_0x506037[_0x54e175(0x15b,0x13e)][_0x54e175(0x18a,0x1ad)][_0x54e175(0x17f,0x174)](PermissionsBitField[_0x54e175(0x16e,0x182)][_0x54e175(0x13f,0x12a)]))return _0x506037[_0x54e175(0x153,0x160)]({'content':'You\x20do\x20not\x20have\x20permission\x20to\x20use\x20this\x20command.','ephemeral':!![]});function _0x54e175(_0xc455ea,_0x5c6a22){return _0x4a72a6(_0xc455ea- -0xe6,_0x5c6a22);}const _0x1eccbf=_0x506037['options'][_0x54e175(0x15d,0x15e)]();if(_0x1eccbf===_0x54e175(0x147,0x15b))await handleEmojiSteal(_0x506037);else _0x1eccbf==='sticker'&&await handleStickerSteal(_0x506037);}};async function handleEmojiSteal(_0x191e60){const _0x3cab45=_0x191e60[_0x988580(-0x107,-0x10d)][_0x988580(-0x115,-0x11d)]('emojis');function _0x988580(_0x37b4b2,_0x2f88f4){return _0x4a72a6(_0x2f88f4- -0x340,_0x37b4b2);}const _0x53fb58=_0x191e60[_0x988580(-0x109,-0x10d)][_0x988580(-0xc9,-0xee)](_0x988580(-0xc2,-0xd7)),_0x2b5086=/<(a?):(\w+):(\d+)>/g,_0x1e1e26=[..._0x3cab45[_0x988580(-0xba,-0xdf)](_0x2b5086)];if(_0x1e1e26['length']===0x0)return _0x191e60['reply']({'content':_0x988580(-0xb9,-0xd8),'ephemeral':!![]});const _0x4baff9=[],_0x3642b9=[];for(const [_0x2c5c59,_0x30c4ee,_0x349737,_0xbc8ac8]of _0x1e1e26){const _0x2a60d3=_0x30c4ee==='a',_0x2af33c=_0x988580(-0x133,-0x11a)+_0xbc8ac8+'.'+(_0x2a60d3?_0x988580(-0x10f,-0xf4):_0x988580(-0x122,-0xfe));try{const _0x307c6d=await axios['get'](_0x2af33c,{'responseType':_0x988580(-0x110,-0xf1)}),_0x35a1bf=Buffer[_0x988580(-0x104,-0x104)](_0x307c6d[_0x988580(-0x105,-0x105)],'binary');if(_0x53fb58){const _0x3cd16b=await _0x191e60[_0x988580(-0xfb,-0x110)][_0x988580(-0xe4,-0xf3)][_0x988580(-0x146,-0x123)]({'attachment':_0x35a1bf,'name':_0x349737});_0x4baff9[_0x988580(-0x101,-0xf7)](_0x3cd16b[_0x988580(-0x126,-0x109)]()+'\x20('+_0x349737+')');}else _0x4baff9[_0x988580(-0x110,-0xf7)](_0x2c5c59+_0x988580(-0x114,-0x119)+_0x2af33c+']');}catch(_0x46f654){console[_0x988580(-0x105,-0x118)](_0x988580(-0xd6,-0xcf),_0x46f654[_0x988580(-0x137,-0x11f)]),_0x3642b9['push'](_0x2c5c59+'\x20('+_0x46f654[_0x988580(-0x116,-0x11f)]+')');}}let _0x2ac05f='';_0x4baff9['length']>0x0&&(_0x2ac05f+=_0x988580(-0xc3,-0xea)+(_0x53fb58?'added':_0x988580(-0xbe,-0xe2))+':\x0a'+_0x4baff9[_0x988580(-0xff,-0x10f)]('\x0a')+'\x0a\x0a'),_0x3642b9[_0x988580(-0x122,-0x103)]>0x0&&(_0x2ac05f+='Failed\x20to\x20process:\x0a'+_0x3642b9[_0x988580(-0xfb,-0x10f)]('\x0a')),await _0x191e60[_0x988580(-0x131,-0x107)]({'content':_0x2ac05f||_0x988580(-0x11e,-0x10a),'ephemeral':!![]});}function _0x2660(_0x2bd0b2,_0x5a7130){const _0x5694e3=_0x5694();return _0x2660=function(_0x2660e0,_0x454fb6){_0x2660e0=_0x2660e0-0x105;let _0x40f010=_0x5694e3[_0x2660e0];return _0x40f010;},_0x2660(_0x2bd0b2,_0x5a7130);}async function handleStickerSteal(_0x4dee6d){function _0x20679e(_0x4ae67f,_0x174e67){return _0x4a72a6(_0x174e67-0xb4,_0x4ae67f);}const _0x23bda6=_0x4dee6d[_0x20679e(0x2e6,0x2e7)][_0x20679e(0x2f7,0x2d7)]('messageid'),_0x6d58bb=_0x4dee6d['options'][_0x20679e(0x31a,0x306)]('addtoserver');try{const _0x41d772=await _0x4dee6d[_0x20679e(0x2d5,0x2fa)][_0x20679e(0x2e5,0x2d2)][_0x20679e(0x329,0x31f)](_0x23bda6);if(!_0x41d772)return _0x4dee6d[_0x20679e(0x2d6,0x2ed)]({'content':_0x20679e(0x2fe,0x2e3),'ephemeral':!![]});const _0x236459=_0x41d772['stickers'][_0x20679e(0x2f7,0x2f8)]();if(!_0x236459)return _0x4dee6d['reply']({'content':_0x20679e(0x2d7,0x2d3),'ephemeral':!![]});const _0xb44d9d=_0x236459[_0x20679e(0x31f,0x309)],_0x4e3ad9=await axios['get'](_0xb44d9d,{'responseType':'arraybuffer'}),_0x5d3161=Buffer[_0x20679e(0x2cd,0x2f0)](_0x4e3ad9[_0x20679e(0x2ca,0x2ef)],_0x20679e(0x2e2,0x2dd));if(_0x6d58bb){const _0x5a1eca=await _0x4dee6d[_0x20679e(0x2de,0x2e4)][_0x20679e(0x2f1,0x305)]['create']({'file':_0x5d3161,'name':_0x236459[_0x20679e(0x312,0x30e)],'tags':_0x236459[_0x20679e(0x2f2,0x2e9)]&&_0x236459[_0x20679e(0x301,0x2e9)]['length']>0x0?_0x236459[_0x20679e(0x2de,0x2e9)][_0x20679e(0x2cb,0x2e5)](','):_0x20679e(0x302,0x2d8)});await _0x4dee6d[_0x20679e(0x30c,0x2ed)]({'content':_0x20679e(0x332,0x30f)+_0x236459[_0x20679e(0x324,0x30e)]+'\x22\x20has\x20been\x20added\x20to\x20the\x20server!','ephemeral':!![]});}else{const _0x4fa04=new MessageAttachment(_0x5d3161,_0x236459['name']+_0x20679e(0x333,0x313));await _0x4dee6d[_0x20679e(0x303,0x2ed)]({'content':_0x20679e(0x333,0x310)+_0x236459[_0x20679e(0x2eb,0x30e)]+'\x22:','files':[_0x4fa04],'ephemeral':!![]});}}catch(_0x207f7e){console[_0x20679e(0x2d1,0x2dc)](_0x20679e(0x2d8,0x2fb),_0x207f7e),await _0x4dee6d[_0x20679e(0x2cb,0x2ed)]({'content':'Failed\x20to\x20process\x20the\x20sticker:\x20'+_0x207f7e[_0x20679e(0x2c9,0x2d5)],'ephemeral':!![]});}}