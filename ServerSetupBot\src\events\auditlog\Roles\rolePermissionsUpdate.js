const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildRoleUpdate,
    async execute(oldRole, newRole, client) {
        try {
            // Only handle permission changes
            if (oldRole.permissions.bitfield === newRole.permissions.bitfield) return;
            
            console.log(`🔐 Role permissions updated: ${newRole.name} in ${newRole.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newRole.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'ROLE_PERMISSIONS_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'ROLE_PERMISSIONS_UPDATE',
                user: 'System',
                userId: null,
                action: 'Quyền role đượ<PERSON> cập nhật',
                details: `<PERSON><PERSON><PERSON><PERSON> của role **${newRole.name}** đ<PERSON> được cập nhật`,
                target: newRole.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the role permissions from audit logs
            try {
                const auditLogs = await newRole.guild.fetchAuditLogs({
                    type: 31, // ROLE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newRole.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Quyền role được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for role permissions update');
            }
            
            // Add to database
            await client.db.addAuditLog(newRole.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔐 Quyền role cập nhật',
                `Vừa có quyền role được cập nhật trong server`
            );
            
            embed.setColor(newRole.color || 0xf39c12); // Use role color or orange
            
            embed.addFields([
                {
                    name: '> Tên role',
                    value: `- ${newRole.name}`,
                    inline: true
                },
                {
                    name: '> ID role',
                    value: `- ${newRole.id}`,
                    inline: true
                },
                {
                    name: '> Role mention',
                    value: `- ${newRole}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Số thành viên có role',
                    value: `- ${newRole.members.size} thành viên`,
                    inline: true
                }
            ]);
            
            // Calculate permission changes
            const oldPermissions = oldRole.permissions.toArray();
            const newPermissions = newRole.permissions.toArray();
            
            const addedPermissions = newPermissions.filter(perm => !oldPermissions.includes(perm));
            const removedPermissions = oldPermissions.filter(perm => !newPermissions.includes(perm));
            
            if (addedPermissions.length > 0) {
                embed.addFields({
                    name: '> ✅ Quyền được thêm',
                    value: `- ${addedPermissions.slice(0, 10).join(', ')}${addedPermissions.length > 10 ? ` và ${addedPermissions.length - 10} quyền khác` : ''}`,
                    inline: false
                });
            }
            
            if (removedPermissions.length > 0) {
                embed.addFields({
                    name: '> ❌ Quyền được xóa',
                    value: `- ${removedPermissions.slice(0, 10).join(', ')}${removedPermissions.length > 10 ? ` và ${removedPermissions.length - 10} quyền khác` : ''}`,
                    inline: false
                });
            }
            
            embed.addFields({
                name: '> 📊 Tổng quyền hiện tại',
                value: `- ${newPermissions.length} quyền`,
                inline: true
            });
            
            // Check for dangerous permissions
            const dangerousPerms = ['Administrator', 'ManageGuild', 'ManageRoles', 'ManageChannels', 'BanMembers', 'KickMembers'];
            const addedDangerous = addedPermissions.filter(perm => dangerousPerms.includes(perm));
            
            if (addedDangerous.length > 0) {
                embed.addFields({
                    name: '⚠️ Cảnh báo',
                    value: `- Đã thêm quyền nguy hiểm: ${addedDangerous.join(', ')}`,
                    inline: false
                });
                embed.setColor(0xe74c3c); // Red for dangerous permissions
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newRole.guild.name} • Role Permissions Update`,
                iconURL: newRole.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Role permissions update logged: ${newRole.name}`);
            
            // Smart alert for dangerous permission changes
            if (config.smartAlerts && addedDangerous.length > 0) {
                try {
                    const owner = await newRole.guild.fetchOwner();
                    const alertEmbed = createInfoEmbed(
                        '🚨 Smart Alert: Dangerous Permissions',
                        `Quyền nguy hiểm đã được thêm vào role **${newRole.name}** trong **${newRole.guild.name}**`
                    );
                    
                    alertEmbed.setColor(0xe74c3c);
                    alertEmbed.addFields([
                        { name: '🎯 Loại', value: 'DANGEROUS_PERMISSIONS', inline: true },
                        { name: '📊 Mức độ', value: 'HIGH', inline: true },
                        { name: '👤 Thực hiện bởi', value: eventData.user, inline: true },
                        { name: '🎭 Role', value: newRole.name, inline: true },
                        { name: '⚠️ Quyền nguy hiểm', value: addedDangerous.join(', '), inline: true }
                    ]);
                    
                    await owner.send({ embeds: [alertEmbed] });
                } catch (dmError) {
                    console.log('Could not send dangerous permissions alert to owner');
                }
            }
            
        } catch (error) {
            console.error('Error in rolePermissionsUpdate audit log:', error);
        }
    }
};
