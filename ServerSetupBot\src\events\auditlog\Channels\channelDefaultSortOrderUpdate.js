const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle default sort order changes for forum channels
            if (oldChannel.defaultSortOrder === newChannel.defaultSortOrder) return;
            
            console.log(`📊 Channel default sort order updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_DEFAULT_SORT_ORDER_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_DEFAULT_SORT_ORDER_UPDATE',
                user: 'System',
                userId: null,
                action: 'Th<PERSON> tự sắp xếp mặc định của kênh được cập nhật',
                details: `Thứ tự sắp xếp mặc định của kênh **${newChannel.name}** đã được cập nhật`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the default sort order from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Thứ tự sắp xếp mặc định được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for channel default sort order update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📊 Thứ tự sắp xếp mặc định cập nhật',
                `Vừa có thứ tự sắp xếp mặc định của kênh được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Sort order mapping
            const sortOrders = {
                0: 'Latest Activity (Hoạt động gần nhất)',
                1: 'Creation Time (Thời gian tạo)'
            };
            
            // Get channel type names
            const channelTypes = {
                0: 'Kênh văn bản',
                2: 'Kênh thoại',
                4: 'Danh mục',
                5: 'Kênh tin tức',
                10: 'Diễn đàn tin tức',
                11: 'Diễn đàn công khai',
                12: 'Diễn đàn riêng tư',
                13: 'Kênh sân khấu',
                15: 'Forum',
                16: 'Media'
            };
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Loại kênh',
                    value: `- ${channelTypes[newChannel.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Sắp xếp cũ',
                    value: `- ${sortOrders[oldChannel.defaultSortOrder] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Sắp xếp mới',
                    value: `- ${sortOrders[newChannel.defaultSortOrder] || 'Unknown'}`,
                    inline: true
                }
            ]);
            
            // Add parent category info if available
            if (newChannel.parent) {
                embed.addFields({
                    name: '> Danh mục',
                    value: `- ${newChannel.parent.name}`,
                    inline: true
                });
            }
            
            // Add impact explanation based on sort order
            if (newChannel.defaultSortOrder === 0 && oldChannel.defaultSortOrder === 1) {
                embed.addFields({
                    name: '> 📈 Tác động (Latest Activity)',
                    value: [
                        '• Posts có hoạt động gần nhất hiển thị trên đầu',
                        '• Khuyến khích thảo luận liên tục',
                        '• Posts hot sẽ luôn ở vị trí nổi bật',
                        '• Phù hợp cho forum thảo luận sôi nổi'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for activity-based
            } else if (newChannel.defaultSortOrder === 1 && oldChannel.defaultSortOrder === 0) {
                embed.addFields({
                    name: '> 📉 Tác động (Creation Time)',
                    value: [
                        '• Posts mới nhất hiển thị trên đầu',
                        '• Thứ tự thời gian tạo rõ ràng',
                        '• Posts cũ có thể bị chìm xuống',
                        '• Phù hợp cho announcements hoặc news'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x3498db); // Blue for time-based
            }
            
            // Add sort order explanations
            embed.addFields({
                name: '> 💡 Giải thích các loại sắp xếp',
                value: [
                    '**Latest Activity:**',
                    '• Posts có tin nhắn mới nhất ở trên',
                    '• Tạo động lực thảo luận',
                    '• Posts inactive sẽ chìm xuống',
                    '',
                    '**Creation Time:**',
                    '• Posts mới tạo ở trên',
                    '• Thứ tự chronological rõ ràng',
                    '• Dễ theo dõi timeline'
                ].join('\n'),
                inline: false
            });
            
            // Add forum-specific information
            if (newChannel.type === 15) { // Forum channel
                embed.addFields({
                    name: '> 📋 Đặc biệt cho Forum Channel',
                    value: [
                        '• Thay đổi cách hiển thị tất cả posts',
                        '• Ảnh hưởng đến trải nghiệm người dùng',
                        '• Có thể thay đổi cách thành viên tương tác',
                        '• Thành viên có thể tự thay đổi sort order'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add user experience note
            embed.addFields({
                name: '> 👥 Trải nghiệm người dùng',
                value: [
                    '• Thành viên sẽ thấy thứ tự mới khi vào forum',
                    '• Có thể tự thay đổi sort order cá nhân',
                    '• Setting này chỉ là mặc định cho người mới',
                    '• Không ảnh hưởng đến posts đã tồn tại'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Default Sort Order Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel default sort order update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelDefaultSortOrderUpdate audit log:', error);
        }
    }
};
