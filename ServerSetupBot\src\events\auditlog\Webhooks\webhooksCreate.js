const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'webhookCreate', // Custom event name
    async execute(webhook, client) {
        try {
            console.log(`🔗 Webhook created: ${webhook.name} in ${webhook.guild?.name || 'Unknown'}`);
            
            // Skip if no guild (DM webhooks)
            if (!webhook.guild) return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(webhook.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'WEBHOOKS_CREATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'WEBHOOKS_CREATE',
                user: 'System',
                userId: null,
                action: 'Webhook được tạo',
                details: `Webhook **${webhook.name}** đã được tạo trong ${webhook.channel}`,
                target: webhook.name,
                channel: webhook.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who created the webhook from audit logs
            try {
                const auditLogs = await webhook.guild.fetchAuditLogs({
                    type: 50, // WEBHOOK_CREATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === webhook.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Webhook được tạo bởi ${auditEntry.executor.tag}`;
                }
            } catch (error) {
                console.log('Could not fetch audit logs for webhook create');
            }
            
            // Add to database
            await client.db.addAuditLog(webhook.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔗 Webhook được tạo',
                `Vừa có một webhook mới được tạo trong server`
            );
            
            embed.setColor(0x2ecc71); // Green for create
            
            // Get webhook type names
            const webhookTypes = {
                1: 'Incoming Webhook',
                2: 'Channel Follower Webhook',
                3: 'Application Webhook'
            };
            
            embed.addFields([
                {
                    name: '> Tên webhook',
                    value: `- ${webhook.name || 'Unnamed Webhook'}`,
                    inline: true
                },
                {
                    name: '> ID webhook',
                    value: `- ${webhook.id}`,
                    inline: true
                },
                {
                    name: '> Loại webhook',
                    value: `- ${webhookTypes[webhook.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${webhook.channel || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người tạo',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian tạo',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add webhook owner info if available
            if (webhook.owner) {
                embed.addFields({
                    name: '> Chủ sở hữu',
                    value: `- ${webhook.owner.tag || webhook.owner.name}`,
                    inline: true
                });
            }
            
            // Add application info if it's an application webhook
            if (webhook.application) {
                embed.addFields({
                    name: '> Ứng dụng',
                    value: `- ${webhook.application.name}`,
                    inline: true
                });
            }
            
            // Add webhook URL info (partially hidden for security)
            if (webhook.url) {
                const urlParts = webhook.url.split('/');
                const hiddenUrl = `https://discord.com/api/webhooks/${webhook.id}/***`;
                embed.addFields({
                    name: '> URL webhook',
                    value: `- ${hiddenUrl}`,
                    inline: false
                });
            }
            
            // Set webhook avatar as thumbnail if available
            if (webhook.avatarURL()) {
                embed.setThumbnail(webhook.avatarURL({ dynamic: true, size: 256 }));
            }
            
            // Add security warning
            embed.addFields({
                name: '⚠️ Bảo mật',
                value: '- Webhook có thể gửi tin nhắn thay mặt server. Hãy bảo vệ URL webhook.',
                inline: false
            });
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${webhook.guild.name} • Webhook Create`,
                iconURL: webhook.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Webhook create logged: ${webhook.name}`);
            
        } catch (error) {
            console.error('Error in webhooksCreate audit log:', error);
        }
    }
};
