module.exports = {
    name: 'guildCreate',
    async execute(guild, client) {
        try {
            // Fetch audit logs to find who added the bot
            const fetchedLogs = await guild.fetchAuditLogs({
                type: 28, // BOT_ADD
                limit: 1
            });

            const botAddLog = fetchedLogs.entries.first();
            const inviter = botAddLog?.executor;

            if (!inviter) {
                console.log(`Bot added to ${guild.name}, but inviter not found.`);
                return;
            }

            const embed = {
                color: 0x2b2d31,
                author: {
                    name: guild.name,
                    icon_url: guild.iconURL({ dynamic: true, size: 1024 }) || client.user.displayAvatarURL()
                },
                title: 'Thank you for adding Strato<PERSON>!',
                description: [
                    '<:arrow:1363342559371595887> My commands work without a prefix.',
                    '<:arrow:1363342559371595887> Use the `help` command to see all features.',
                    '<:arrow:1363342559371595887> Need help? Visit our [**Support Server**](https://discord.gg/S6AjkyQKNZ)'
                ].join('\n'),
                thumbnail: {
                    url: inviter.displayAvatarURL({ dynamic: true, size: 1024 })
                },
                footer: {
                    text: 'Developed With ❤️ By The Arnab',
                    icon_url: 'https://cdn.discordapp.com/attachments/1362812827500544287/1363573045381431336/ae1999b6f8564c029ef93fd0b38d43f0.jpg?ex=680685db&is=6805345b&hm=e3cd775528343394c80e6f68df68fdcb463eddf25aac7e8a6f5c15271eb2daa0&'
                }
            };

            const buttons = {
                type: 1,
                components: [
                    {
                        type: 2,
                        style: 5,
                        label: 'Invite Me',
                        url: 'https://discord.com/oauth2/authorize?client_id=1294987004483862528&permissions=8&scope=bot+applications.commands',
                        emoji: {
                            id: '1362840568362831922',
                            name: 'invite'
                        }
                    },
                    {
                        type: 2,
                        style: 5,
                        label: 'Support',
                        url: 'https://discord.gg/S6AjkyQKNZ',
                        emoji: {
                            id: '1362840474636910833',
                            name: 'home'
                        }
                    }
                ]
            };

            await inviter.send({
                embeds: [embed],
                components: [buttons]
            });

            console.log(`🔸 Sent welcome message to ${inviter.tag}`);
        } catch (error) {
            console.error('Failed to send welcome message to inviter:', error);
        }
    }
};