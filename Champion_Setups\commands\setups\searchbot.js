const Command = require("../../structures/Command");
const Discord = require("discord.js");
const fetch = require("node-fetch");

module.exports = class SearchBot extends Command {
  constructor(client) {
    super(client, {
      name: "searchbot",
      description: client.cmdConfig.searchbot.description,
      usage: client.cmdConfig.searchbot.usage,
      permissions: client.cmdConfig.searchbot.permissions,
      aliases: client.cmdConfig.searchbot.aliases,
      category: "setups",
      listed: client.cmdConfig.searchbot.enabled,
      slash: true,
      options: [{
        name: "term", 
        type: Discord.ApplicationCommandOptionType.String, 
        description: "Term for Search", 
        required: true
      }]
    });
  }

  async run(message, args) {
    let term = args.join(" ");
    
    if(!args[0]) return message.channel.send({ embeds: [this.client.utils.usage(this.client, this.client.cmdConfig.searchbot.usage)]});

    let regex = / /g;

    fetch(`https://discord.bots.gg/api/v1/bots?limit=10&q=${term.replace(regex, "%20")}`).then(async (res) => {
      let data = await res.json();

      let content = "";
      for(let i = 0; i < data.bots.length; i++) {
        content += `\`#${i+1}\` **${data.bots[i].username}** - ${data.bots[i].shortDescription.length > 130 ? data.bots[i].shortDescription.slice(0, 130) + "..." : data.bots[i].shortDescription}\n`;
      }

      if(data.bots.length < 1) content = `> No Bots Found`;
      
      message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, `>>> ${content}`, this.client.embeds.general_color)]}); 
    });
  }
  async slashRun(interaction, args) {
    let term = interaction.options.getString("term");
    let regex = / /g;

    fetch(`https://discord.bots.gg/api/v1/bots?limit=10&q=${term.replace(regex, "%20")}`).then(async (res) => {
      let data = await res.json();

      let content = "";
      for(let i = 0; i < data.bots.length; i++) {
        content += `\`#${i+1}\` **${data.bots[i].username}** - ${data.bots[i].shortDescription.length > 130 ? data.bots[i].shortDescription.slice(0, 130) + "..." : data.bots[i].shortDescription}\n`;
      }

      if(data.bots.length < 1) content = `> No Bots Found`;
      
      interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, `>>> ${content}`, this.client.embeds.general_color)], ephemeral: this.client.cmdConfig.templates.ephemeral }); 
    });
  }
};
