(function(_0xf341ad,_0xa7a198){const _0x3fb4d6=_0xf341ad();function _0x2cd35c(_0x42bc14,_0x25c93c){return _0x5a19(_0x42bc14- -0x2fb,_0x25c93c);}while(!![]){try{const _0x2a47=-parseInt(_0x2cd35c(-0x160,-0x14f))/0x1*(parseInt(_0x2cd35c(-0x16b,-0x166))/0x2)+parseInt(_0x2cd35c(-0x159,-0x14e))/0x3+parseInt(_0x2cd35c(-0x165,-0x15a))/0x4+parseInt(_0x2cd35c(-0x16f,-0x16d))/0x5+parseInt(_0x2cd35c(-0x14d,-0x14c))/0x6+parseInt(_0x2cd35c(-0x161,-0x14d))/0x7+parseInt(_0x2cd35c(-0x169,-0x16a))/0x8*(-parseInt(_0x2cd35c(-0x14f,-0x14a))/0x9);if(_0x2a47===_0xa7a198)break;else _0x3fb4d6['push'](_0x3fb4d6['shift']());}catch(_0xa7e706){_0x3fb4d6['push'](_0x3fb4d6['shift']());}}}(_0x248a,0xdfe82));const {SlashCommandBuilder,EmbedBuilder}=require(_0x19e841(0x1ff,0x1eb));function _0x19e841(_0x563e6b,_0xff71da){return _0x5a19(_0x563e6b-0x68,_0xff71da);}function _0x5a19(_0x5e507a,_0x28288e){const _0x248a89=_0x248a();return _0x5a19=function(_0x5a194a,_0x2283f4){_0x5a194a=_0x5a194a-0x17e;let _0x5dadfe=_0x248a89[_0x5a194a];return _0x5dadfe;},_0x5a19(_0x5e507a,_0x28288e);}const User=require(_0x19e841(0x1e8,0x1d4)),fs=require('fs'),yaml=require(_0x19e841(0x20e,0x1fa)),config=yaml['load'](fs[_0x19e841(0x201,0x219)](_0x19e841(0x1eb,0x1f6),'utf8')),lang=yaml['load'](fs[_0x19e841(0x201,0x1fb)](_0x19e841(0x1fd,0x1f1),_0x19e841(0x1ea,0x1e9))),{replacePlaceholders}=require('./Utility/helpers');module[_0x19e841(0x1f2,0x201)]={'data':new SlashCommandBuilder()['setName'](_0x19e841(0x1e7,0x1e7))[_0x19e841(0x20f,0x211)](_0x19e841(0x1e9,0x1f3))[_0x19e841(0x1ef,0x1e5)](_0x2b2c09=>_0x2b2c09[_0x19e841(0x200,0x1eb)](_0x19e841(0x215,0x21b))[_0x19e841(0x20f,0x21f)](_0x19e841(0x1f6,0x1ff))[_0x19e841(0x1f1,0x200)](!![]))[_0x19e841(0x207,0x20e)](_0xc9c248=>_0xc9c248[_0x19e841(0x200,0x1fa)](_0x19e841(0x1f5,0x205))[_0x19e841(0x20f,0x20d)](_0x19e841(0x213,0x206))[_0x19e841(0x1f1,0x1e7)](!![])),'category':_0x19e841(0x1fc,0x1ea),async 'execute'(_0x6b19e6){const _0x3b669a=_0x6b19e6[_0x419751(-0x16a,-0x163)][_0x419751(-0x15b,-0x16a)](_0x419751(-0x172,-0x15a)),_0x4816c4=_0x6b19e6[_0x419751(-0x161,-0x163)][_0x419751(-0x18f,-0x176)](_0x419751(-0x175,-0x17a));if(_0x3b669a['id']===_0x6b19e6[_0x419751(-0x151,-0x169)]['id']){const _0x386bc1=new EmbedBuilder()[_0x419751(-0x154,-0x160)](lang['Economy'][_0x419751(-0x15e,-0x16b)]['cannotTransferToSelf']||_0x419751(-0x184,-0x17f))[_0x419751(-0x16b,-0x182)](_0x419751(-0x164,-0x162));return _0x6b19e6[_0x419751(-0x164,-0x158)]({'embeds':[_0x386bc1],'ephemeral':!![]});}function _0x419751(_0x1888f3,_0x3e73b6){return _0x19e841(_0x3e73b6- -0x36f,_0x1888f3);}if(_0x4816c4<=0x0){const _0x397637=new EmbedBuilder()[_0x419751(-0x147,-0x160)](lang[_0x419751(-0x189,-0x173)][_0x419751(-0x179,-0x16b)]['invalidTransferAmount']||_0x419751(-0x174,-0x15d))[_0x419751(-0x183,-0x182)](_0x419751(-0x162,-0x162));return _0x6b19e6['reply']({'embeds':[_0x397637],'ephemeral':!![]});}const _0x268a1c=await User[_0x419751(-0x16c,-0x174)]({'userId':_0x6b19e6['user']['id'],'guildId':_0x6b19e6[_0x419751(-0x174,-0x17c)]['id']},{'balance':0x1,'transactionLogs':0x1}),_0x5e114d=await User[_0x419751(-0x171,-0x174)]({'userId':_0x3b669a['id'],'guildId':_0x6b19e6[_0x419751(-0x178,-0x17c)]['id']},{'balance':0x1,'transactionLogs':0x1});if(!_0x268a1c||_0x268a1c['balance']<_0x4816c4){const _0x3c2ffc=new EmbedBuilder()[_0x419751(-0x175,-0x160)](lang['Economy'][_0x419751(-0x175,-0x16b)][_0x419751(-0x17c,-0x164)])[_0x419751(-0x18b,-0x182)](_0x419751(-0x153,-0x162));return _0x6b19e6[_0x419751(-0x152,-0x158)]({'embeds':[_0x3c2ffc]});}_0x268a1c[_0x419751(-0x181,-0x178)]-=_0x4816c4,_0x268a1c[_0x419751(-0x176,-0x15f)][_0x419751(-0x169,-0x167)]({'type':_0x419751(-0x16a,-0x15e),'amount':-_0x4816c4,'timestamp':new Date()});!_0x5e114d?await new User({'userId':_0x3b669a['id'],'guildId':_0x6b19e6['guild']['id'],'balance':_0x4816c4,'transactionLogs':[{'type':_0x419751(-0x162,-0x166),'amount':_0x4816c4,'timestamp':new Date()}]})[_0x419751(-0x16e,-0x183)]():(_0x5e114d['balance']+=_0x4816c4,_0x5e114d[_0x419751(-0x16c,-0x15f)][_0x419751(-0x153,-0x167)]({'type':_0x419751(-0x16b,-0x166),'amount':_0x4816c4,'timestamp':new Date()}),await _0x5e114d[_0x419751(-0x18b,-0x183)]());await _0x268a1c['save']();const _0x204010=new EmbedBuilder()[_0x419751(-0x154,-0x160)](replacePlaceholders(lang[_0x419751(-0x165,-0x173)]['Messages'][_0x419751(-0x18b,-0x188)],{'amount':_0x4816c4,'target':'<@'+_0x3b669a['id']+'>'}))[_0x419751(-0x16e,-0x182)](_0x419751(-0x171,-0x189))['setFooter']({'text':replacePlaceholders(lang[_0x419751(-0x16b,-0x173)][_0x419751(-0x17f,-0x16b)][_0x419751(-0x19a,-0x181)],{'balance':_0x268a1c[_0x419751(-0x176,-0x178)]})});return _0x6b19e6[_0x419751(-0x142,-0x158)]({'embeds':[_0x204010]});}};function _0x248a(){const _0x20c80f=['footer','addUserOption','You\x20cannot\x20transfer\x20coins\x20to\x20yourself.','setRequired','exports','guild','4295205eeTcWx','amount','User\x20to\x20transfer\x20to','balance','66ZvxFJL','getInteger','657304ckvniB','findOne','Economy','./lang.yml','4264844NJyHiP','discord.js','setName','readFileSync','9232230cCScRf','38248iSNjab','Messages','getUser','user','addIntegerOption','push','transfer_in','4829499QjOCeo','noMoney','options','#FF0000','js-yaml','setDescription','transactionLogs','transfer_out','The\x20transfer\x20amount\x20must\x20be\x20greater\x20than\x200.','Amount\x20to\x20transfer','297bMJGMI','target','220260bEyLIa','reply','#00FF00','transfer','../../../models/UserData','Transfer\x20coins\x20to\x20another\x20user','utf8','./config.yml','save','setColor'];_0x248a=function(){return _0x20c80f;};return _0x248a();}