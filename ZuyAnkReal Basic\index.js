const { Client, GatewayIntentBits, Collection, ActivityType } = require('discord.js');
const { token, defaultPrefix, activity } = require('./config.json');
const fs = require('fs');
const logger = require('./utils/logger');

const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers
    ]
});

client.commands = new Collection();

// **✅ Load Prefixes JSON**
function loadPrefixes() {
    try {
        if (fs.existsSync("prefixes.json")) {
            return JSON.parse(fs.readFileSync("prefixes.json", "utf8"));
        } else {
            return {};
        }
    } catch (error) {
        console.error("❌ Error reading prefixes.json:", error);
        return {};
    }
}

// **✅ Save Prefixes JSON**
function savePrefixes(prefixes) {
    try {
        fs.writeFileSync("prefixes.json", JSON.stringify(prefixes, null, 2));
    } catch (error) {
        console.error("❌ Error saving prefixes.json:", error);
    }
}

let prefixes = loadPrefixes(); // Load prefix data

// **✅ Get Prefix Function**
function getPrefix(guildId) {
    return prefixes[guildId] || defaultPrefix;
}

// **✅ Load Commands**
const commandFolders = fs.readdirSync('./commands');
let commandCount = 0;

for (const folder of commandFolders) {
    const commandFiles = fs.readdirSync(`./commands/${folder}`).filter(file => file.endsWith('.js'));
    for (const file of commandFiles) {
        const command = require(`./commands/${folder}/${file}`);
        client.commands.set(command.name, command);
        commandCount++;
    }
}

// **✅ Load Events**
const eventFiles = fs.readdirSync('./events').filter(file => file.endsWith('.js'));
let eventCount = 0;

for (const file of eventFiles) {
    const event = require(`./events/${file}`);
    client.on(event.name, (...args) => event.execute(...args, client));
    eventCount++;
}

// Manually bind interactionCreate if it's a separate custom handler
client.on("interactionCreate", async (interaction) => {
    require("./events/interactionCreate")(client, interaction);
});

if (!eventFiles.includes("interactionCreate.js")) {
    eventCount++;
}

// **✅ Message Event for Commands (With Aliases Support)**
client.on("messageCreate", async (message) => {
    if (message.author.bot || !message.guild) return;

    const prefix = getPrefix(message.guild.id);
    if (!message.content.startsWith(prefix)) return;

    const args = message.content.slice(prefix.length).trim().split(/ +/);
    const commandName = args.shift().toLowerCase();

    console.log(`⚡ Command Triggered: ${commandName}`);

    const command = client.commands.get(commandName) ||
        client.commands.find(cmd => cmd.aliases && cmd.aliases.includes(commandName));

    if (!command) return;

    try {
        command.execute(message, args, client);
    } catch (error) {
        logger.error("❌ Error executing command:", error);
        message.reply("There was an error executing this command.");
    }
});

// **✅ Bot Ready Event**
client.once('ready', async () => {
    logger.success(`✅ Logged in as ${client.user.tag}`);
    logger.info(`📝 Default prefix set to: ${defaultPrefix}`);
    logger.debug(`Loaded ${commandCount} commands successfully.`);
    logger.debug(`Loaded ${eventCount} events successfully.`);

    client.user.setPresence({
        status: activity.status,
        activities: [{
            name: activity.name,
            type: ActivityType[activity.type]
        }]
    });

    // **✅ Giveaway Handler Load**
    try {
        require('./events/giveawayHandler')(client);
        logger.info('🎉 Giveaway handler started successfully.');
    } catch (err) {
        logger.error('❌ Error starting giveaway handler:', err);
    }
});

// **✅ Bot Login**
client.login(token);