const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle default reaction emoji changes for forum channels
            if (oldChannel.defaultReactionEmoji?.id === newChannel.defaultReactionEmoji?.id && 
                oldChannel.defaultReactionEmoji?.name === newChannel.defaultReactionEmoji?.name) return;
            
            console.log(`😀 Channel default reaction emoji updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_DEFAULT_REACTION_EMOJI_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_DEFAULT_REACTION_EMOJI_UPDATE',
                user: 'System',
                userId: null,
                action: 'Emoji reaction mặc định của kênh được cập nhật',
                details: `Emoji reaction mặc định của kênh **${newChannel.name}** đã được cập nhật`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the default reaction emoji from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Emoji reaction mặc định được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for channel default reaction emoji update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '😀 Emoji reaction mặc định cập nhật',
                `Vừa có emoji reaction mặc định của kênh được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get channel type names
            const channelTypes = {
                0: 'Kênh văn bản',
                2: 'Kênh thoại',
                4: 'Danh mục',
                5: 'Kênh tin tức',
                10: 'Diễn đàn tin tức',
                11: 'Diễn đàn công khai',
                12: 'Diễn đàn riêng tư',
                13: 'Kênh sân khấu',
                15: 'Forum',
                16: 'Media'
            };
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Loại kênh',
                    value: `- ${channelTypes[newChannel.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Format emoji display
            const formatEmoji = (emoji) => {
                if (!emoji) return 'Không có emoji';
                if (emoji.id) {
                    return `<:${emoji.name}:${emoji.id}> (:${emoji.name}:)`;
                }
                return `${emoji.name}`;
            };
            
            embed.addFields([
                {
                    name: '> Emoji cũ',
                    value: `- ${formatEmoji(oldChannel.defaultReactionEmoji)}`,
                    inline: true
                },
                {
                    name: '> Emoji mới',
                    value: `- ${formatEmoji(newChannel.defaultReactionEmoji)}`,
                    inline: true
                }
            ]);
            
            // Add parent category info if available
            if (newChannel.parent) {
                embed.addFields({
                    name: '> Danh mục',
                    value: `- ${newChannel.parent.name}`,
                    inline: true
                });
            }
            
            // Determine change type and set appropriate color
            if (newChannel.defaultReactionEmoji && oldChannel.defaultReactionEmoji) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Thay đổi emoji reaction mặc định`,
                    inline: true
                });
                embed.setColor(0x3498db); // Blue for change
            } else if (newChannel.defaultReactionEmoji && !oldChannel.defaultReactionEmoji) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Thiết lập emoji reaction mặc định mới`,
                    inline: true
                });
                embed.setColor(0x2ecc71); // Green for new emoji
            } else if (!newChannel.defaultReactionEmoji && oldChannel.defaultReactionEmoji) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Xóa emoji reaction mặc định`,
                    inline: true
                });
                embed.setColor(0xe74c3c); // Red for removed emoji
            }
            
            // Add impact explanation
            if (newChannel.defaultReactionEmoji && !oldChannel.defaultReactionEmoji) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Posts mới sẽ tự động có emoji reaction này',
                        '• Tăng tương tác và engagement',
                        '• Tạo thống nhất trong forum',
                        '• Khuyến khích thành viên react'
                    ].join('\n'),
                    inline: false
                });
            } else if (!newChannel.defaultReactionEmoji && oldChannel.defaultReactionEmoji) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Posts mới sẽ không có emoji reaction mặc định',
                        '• Giảm tương tác tự động',
                        '• Thành viên phải tự thêm reactions',
                        '• Giao diện đơn giản hơn'
                    ].join('\n'),
                    inline: false
                });
            } else if (newChannel.defaultReactionEmoji && oldChannel.defaultReactionEmoji) {
                embed.addFields({
                    name: '> 🔄 Tác động',
                    value: [
                        '• Posts mới sẽ sử dụng emoji reaction mới',
                        '• Posts cũ giữ nguyên emoji cũ',
                        '• Thay đổi cách thể hiện cảm xúc',
                        '• Có thể cần thời gian để thành viên quen'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add forum-specific information
            if (newChannel.type === 15) { // Forum channel
                embed.addFields({
                    name: '> 💡 Đặc biệt cho Forum Channel',
                    value: [
                        '• Emoji sẽ tự động được thêm vào posts mới',
                        '• Giúp thành viên express cảm xúc nhanh chóng',
                        '• Tạo consistency trong forum',
                        '• Có thể dùng để vote hoặc like posts'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add emoji guidelines
            embed.addFields({
                name: '> 📝 Lưu ý về emoji reaction mặc định',
                value: [
                    '• Chỉ áp dụng cho posts mới',
                    '• Có thể sử dụng emoji Unicode hoặc custom emoji',
                    '• Nên chọn emoji phù hợp với chủ đề forum',
                    '• Thành viên vẫn có thể thêm reactions khác'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Default Reaction Emoji Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel default reaction emoji update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelDefaultReactionEmojiUpdate audit log:', error);
        }
    }
};
