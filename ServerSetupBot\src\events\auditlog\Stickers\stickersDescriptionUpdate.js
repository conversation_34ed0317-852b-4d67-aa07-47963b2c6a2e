const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildStickerUpdate,
    async execute(oldSticker, newSticker, client) {
        try {
            // Only handle description changes
            if (oldSticker.description === newSticker.description) return;
            
            console.log(`📝 Sticker description updated: ${newSticker.name} in ${newSticker.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newSticker.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'STICKERS_DESCRIPTION_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'STICKERS_DESCRIPTION_UPDATE',
                user: 'System',
                userId: null,
                action: 'Mô tả sticker được cập nhật',
                details: `<PERSON>ô tả của sticker **${newSticker.name}** đã được cập nhật`,
                target: newSticker.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the sticker description from audit logs
            try {
                const auditLogs = await newSticker.guild.fetchAuditLogs({
                    type: 91, // STICKER_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newSticker.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Mô tả sticker được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for sticker description update');
            }
            
            // Add to database
            await client.db.addAuditLog(newSticker.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📝 Mô tả sticker được cập nhật',
                `Vừa có mô tả sticker được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Tên sticker',
                    value: `- ${newSticker.name}`,
                    inline: true
                },
                {
                    name: '> ID sticker',
                    value: `- ${newSticker.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add description comparison
            const oldDesc = oldSticker.description || 'Không có mô tả';
            const newDesc = newSticker.description || 'Không có mô tả';
            
            embed.addFields([
                {
                    name: '> Mô tả cũ',
                    value: `- ${oldDesc}`,
                    inline: false
                },
                {
                    name: '> Mô tả mới',
                    value: `- ${newDesc}`,
                    inline: false
                }
            ]);
            
            // Add sticker format
            const formatTypes = {
                1: 'PNG',
                2: 'APNG',
                3: 'Lottie',
                4: 'GIF'
            };
            
            embed.addFields({
                name: '> Định dạng',
                value: `- ${formatTypes[newSticker.format] || 'Unknown'}`,
                inline: true
            });
            
            // Add sticker type
            const stickerTypes = {
                1: 'Standard (Discord)',
                2: 'Guild (Server custom)'
            };
            
            embed.addFields({
                name: '> Loại sticker',
                value: `- ${stickerTypes[newSticker.type] || 'Unknown'}`,
                inline: true
            });
            
            // Add sticker tags if available
            if (newSticker.tags) {
                embed.addFields({
                    name: '> Tags',
                    value: `- ${newSticker.tags}`,
                    inline: true
                });
            }
            
            // Add sticker availability
            embed.addFields({
                name: '> Có thể sử dụng',
                value: `- ${newSticker.available ? 'Có' : 'Không'}`,
                inline: true
            });
            
            // Add sticker URL
            if (newSticker.url) {
                embed.addFields({
                    name: '> URL sticker',
                    value: `- [Xem sticker](${newSticker.url})`,
                    inline: true
                });
            }
            
            // Add creation info if available
            if (newSticker.createdTimestamp) {
                embed.addFields({
                    name: '> Được tạo lúc',
                    value: `- <t:${Math.floor(newSticker.createdTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add impact explanation
            if (newSticker.description && !oldSticker.description) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Sticker giờ đây có mô tả rõ ràng',
                        '• Dễ hiểu mục đích sử dụng',
                        '• Tăng tính chuyên nghiệp',
                        '• Giúp người dùng biết khi nào sử dụng'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for added description
            } else if (!newSticker.description && oldSticker.description) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Sticker không còn mô tả',
                        '• Có thể gây nhầm lẫn về mục đích',
                        '• Giảm tính rõ ràng',
                        '• Người dùng phải đoán cách sử dụng'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0xe74c3c); // Red for removed description
            } else if (newSticker.description && oldSticker.description) {
                embed.addFields({
                    name: '> 🔄 Tác động',
                    value: [
                        '• Mô tả sticker đã được cập nhật',
                        '• Thông tin mới về cách sử dụng',
                        '• Có thể thay đổi ngữ cảnh sử dụng',
                        '• Cải thiện sự hiểu biết của người dùng'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add description guidelines
            embed.addFields({
                name: '> 💡 Lưu ý về mô tả sticker',
                value: [
                    '• Mô tả giúp người dùng hiểu cách sử dụng',
                    '• Nên ngắn gọn và rõ ràng',
                    '• Có thể mô tả cảm xúc hoặc tình huống',
                    '• Tối đa 100 ký tự'
                ].join('\n'),
                inline: false
            });
            
            // Add server sticker info
            const currentStickerCount = newSticker.guild.stickers.cache.size;
            const maxStickers = newSticker.guild.premiumTier >= 2 ? 60 : 0;
            
            embed.addFields([
                {
                    name: '> Số sticker trong server',
                    value: `- ${currentStickerCount}/${maxStickers} stickers`,
                    inline: true
                },
                {
                    name: '> Server Boost Level',
                    value: `- Level ${newSticker.guild.premiumTier}`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do thay đổi',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set sticker as thumbnail if possible
            if (newSticker.url) {
                embed.setThumbnail(newSticker.url);
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newSticker.guild.name} • Sticker Description Update`,
                iconURL: newSticker.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Sticker description update logged: ${newSticker.name}`);
            
        } catch (error) {
            console.error('Error in stickersDescriptionUpdate audit log:', error);
        }
    }
};
