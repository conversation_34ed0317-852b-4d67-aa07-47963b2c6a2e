(function(_0xc6e7eb,_0x89a68b){function _0x87beef(_0x335ba2,_0x2ab0fb,_0x57823d,_0x2c20bf,_0x58d53c){return _0xe334(_0x2c20bf-0x31a,_0x57823d);}function _0xfde640(_0x6dcb16,_0xd685bf,_0x183089,_0x81a56e,_0x12d7c3){return _0xe334(_0x12d7c3-0x19d,_0x81a56e);}const _0x1936dd=_0xc6e7eb();while(!![]){try{const _0x13e43a=parseInt(_0xfde640(0x286,0x2a9,0x2a0,0x2b4,0x2ab))/0x1+parseInt(_0x87beef(0x3ee,0x3d1,0x405,0x3ed,0x417))/0x2+parseInt(_0xfde640(0x2e0,0x2ea,0x299,0x2b6,0x2bd))/0x3+-parseInt(_0xfde640(0x282,0x28d,0x2cb,0x2c5,0x2aa))/0x4+-parseInt(_0xfde640(0x272,0x28d,0x28f,0x28c,0x29c))/0x5*(parseInt(_0x87beef(0x421,0x3f4,0x407,0x40c,0x3ea))/0x6)+-parseInt(_0xfde640(0x2ca,0x2a4,0x2ef,0x2ee,0x2ca))/0x7+-parseInt(_0xfde640(0x28d,0x28f,0x2a5,0x26e,0x279))/0x8;if(_0x13e43a===_0x89a68b)break;else _0x1936dd['push'](_0x1936dd['shift']());}catch(_0x4ea0fb){_0x1936dd['push'](_0x1936dd['shift']());}}}(_0x4d22,0xe48f8));const _0xbd0ac9=(function(){let _0x4b909a=!![];return function(_0x1a8700,_0x2c65ed){const _0x277fa5=_0x4b909a?function(){if(_0x2c65ed){const _0x12cad7=_0x2c65ed['\x61\x70\x70\x6c\x79'](_0x1a8700,arguments);return _0x2c65ed=null,_0x12cad7;}}:function(){};return _0x4b909a=![],_0x277fa5;};}()),_0x3646e5=_0xbd0ac9(this,function(){function _0x5358b5(_0x3fff5c,_0xf22040,_0x557099,_0x487de0,_0x49a00f){return _0xe334(_0x3fff5c- -0x1d1,_0x49a00f);}function _0x263707(_0x5095ca,_0x319849,_0x450bae,_0x4eca19,_0x1ddee0){return _0xe334(_0x5095ca- -0x2bc,_0x450bae);}return _0x3646e5[_0x5358b5(-0xfc,-0x124,-0x110,-0xd6,-0x127)]()['\x73\x65\x61\x72\x63\x68'](_0x5358b5(-0xe1,-0xc9,-0xe8,-0xf7,-0xe7)+'\x2b\x24')['\x74\x6f\x53\x74\x72\x69\x6e\x67']()[_0x5358b5(-0xbc,-0x8f,-0xaa,-0xdd,-0x91)+'\x72'](_0x3646e5)[_0x263707(-0x1d3,-0x1ab,-0x1c8,-0x1f1,-0x202)](_0x5358b5(-0xe1,-0xd0,-0xf6,-0x102,-0xbc)+'\x2b\x24');});function _0x4d22(){const _0x205b60=['\x63\x6f\x6e\x74\x65\x6e\x74','\x65\x7a\x6f\x6e\x65','\x5c\x2b\x5c\x2b\x20\x2a\x28\x3f\x3a\x5b','\x43\x6f\x6c\x6f\x72','\x38\x33\x33\x36\x38\x39\x36\x55\x57\x59\x7a\x45\x49','\x75\x73\x65\x72\x6e\x61\x6d\x65','\x66\x6f\x72\x6d\x61\x74','\x73\x6c\x69\x63\x65','\x6e\x63\x74\x69\x6f\x6e\x28\x29\x20','\x63\x61\x6c\x6c','\x46\x6f\x6f\x74\x65\x72','\x67\x65\x74','\x63\x74\x6f\x72\x28\x22\x72\x65\x74\x75','\x72\x65\x67\x65\x78\x3a','\x61\x63\x74\x69\x6f\x6e','\x73\x65\x6e\x64','\x73\x74\x72\x69\x6e\x67','\x73\x65\x61\x72\x63\x68','\x4b\x52\x53\x67\x4f','\x62\x6f\x74','\x6e\x61\x6d\x65','\x30\x2d\x39\x61\x2d\x7a\x41\x2d\x5a\x5f','\x65\x6d\x62\x65\x64\x73','\x49\x63\x6f\x6e','\x28\x28\x28\x2e\x2b\x29\x2b\x29\x2b\x29','\x54\x65\x78\x74','\x31\x36\x33\x38\x37\x38\x6a\x52\x64\x6e\x78\x4e','\x74\x65\x73\x74','\x2e\x2f\x6c\x61\x6e\x67\x2e\x79\x6d\x6c','\x6a\x73\x2d\x79\x61\x6d\x6c','\x6c\x6f\x61\x64','\x4c\x6f\x67\x73\x43\x68\x61\x6e\x6e\x65','\x6f\x48\x79\x55\x6c','\x45\x6e\x61\x62\x6c\x65\x64','\x72\x65\x74\x75\x72\x6e\x20\x28\x66\x75','\x75\x74\x66\x38','\x73\x65\x74\x49\x6e\x74\x65\x72\x76\x61','\x24\x5d\x2a\x29','\x63\x68\x61\x6e\x6e\x65\x6c','\x31\x37\x30\x68\x74\x64\x75\x71\x4a','\x54\x69\x6d\x65\x7a\x6f\x6e\x65','\x49\x6d\x61\x67\x65','\x77\x68\x69\x6c\x65\x20\x28\x74\x72\x75','\x73\x74\x61\x72\x74\x73\x57\x69\x74\x68','\x45\x4c\x55\x50\x79','\x73\x74\x61\x74\x65\x4f\x62\x6a\x65\x63','\x77\x6e\x6d\x68\x75','\x23\x46\x46\x39\x38\x30\x30','\x69\x63\x6f\x6e\x55\x52\x4c','\x6a\x6f\x69\x6e','\x67\x75\x69\x6c\x64','\x73\x65\x74\x46\x6f\x6f\x74\x65\x72','\x4d\x65\x73\x73\x61\x67\x65\x55\x70\x64','\x32\x31\x39\x35\x34\x31\x32\x76\x61\x78\x75\x59\x57','\x31\x32\x34\x32\x37\x37\x39\x51\x42\x7a\x62\x4c\x6e','\x76\x67\x65\x77\x41','\x61\x74\x65\x4c\x6f\x67\x73','\x64\x69\x73\x70\x6c\x61\x79\x41\x76\x61','\x63\x6f\x75\x6e\x74\x65\x72','\x63\x61\x63\x68\x65','\x5c\x28\x20\x2a\x5c\x29','\x63\x6f\x6e\x73\x74\x72\x75\x63\x74\x6f','\x69\x6e\x69\x74','\x73\x65\x74\x44\x65\x73\x63\x72\x69\x70','\x65\x29\x20\x7b\x7d','\x72\x65\x70\x6c\x61\x63\x65','\x61\x75\x74\x68\x6f\x72','\x74\x69\x6f\x6e','\x4d\x4d\x4d\x4d\x20\x44\x6f\x20\x59\x59','\x6c\x49\x44','\x63\x68\x61\x6e\x6e\x65\x6c\x73','\x74\x61\x67','\x33\x39\x38\x38\x38\x32\x31\x77\x71\x4c\x4d\x51\x48','\x73\x65\x74\x54\x68\x75\x6d\x62\x6e\x61','\x64\x69\x73\x63\x6f\x72\x64\x2e\x6a\x73','\x61\x2d\x7a\x41\x2d\x5a\x5f\x24\x5d\x5b','\x61\x70\x70\x6c\x79','\x54\x69\x74\x6c\x65','\x72\x65\x61\x64\x46\x69\x6c\x65\x53\x79','\x66\x75\x6e\x63\x74\x69\x6f\x6e\x20\x2a','\x4e\x6f\x6e\x65','\x70\x6e\x67','\x41\x75\x74\x68\x6f\x72','\x72\x6e\x20\x74\x68\x69\x73\x22\x29\x28','\x64\x65\x62\x75','\x31\x31\x32\x38\x39\x36\x30\x4e\x6b\x4b\x53\x74\x6e','\x74\x65\x78\x74','\x7b\x7d\x2e\x63\x6f\x6e\x73\x74\x72\x75','\x74\x61\x72\x55\x52\x4c','\x32\x30\x38\x39\x33\x37\x30\x56\x79\x4a\x68\x64\x54','\x6c\x65\x6e\x67\x74\x68','\x74\x6f\x53\x74\x72\x69\x6e\x67','\x54\x68\x75\x6d\x62\x6e\x61\x69\x6c','\x65\x78\x70\x6f\x72\x74\x73'];_0x4d22=function(){return _0x205b60;};return _0x4d22();}function _0x1f6039(_0x2e0c14,_0xdedd53,_0x32b782,_0x4a6364,_0x1dad89){return _0xe334(_0xdedd53- -0x323,_0x2e0c14);}_0x3646e5();const _0x2af661=(function(){let _0x58c1db=!![];return function(_0x638be,_0x4638d9){const _0x451e94=_0x58c1db?function(){function _0x4ffb34(_0x5bff01,_0x1ca0f5,_0x5740cc,_0x542d39,_0x194d3b){return _0xe334(_0x542d39-0x2ce,_0x1ca0f5);}if(_0x4638d9){const _0x4a6811=_0x4638d9[_0x4ffb34(0x40d,0x3d6,0x40b,0x3f2,0x41b)](_0x638be,arguments);return _0x4638d9=null,_0x4a6811;}}:function(){};return _0x58c1db=![],_0x451e94;};}());(function(){_0x2af661(this,function(){function _0x3c0c6c(_0x1c6cb1,_0x18eb08,_0x44dcc8,_0x8c10f4,_0x101d61){return _0xe334(_0x101d61- -0x132,_0x18eb08);}const _0x2cacff=new RegExp(_0x3c0c6c(-0x25,-0xd,-0x27,0x9,-0xb)+_0x145552(0x27c,0x2a4,0x261,0x294,0x2ab)),_0x5557d5=new RegExp(_0x145552(0x242,0x21b,0x24d,0x25c,0x222)+_0x3c0c6c(-0x24,-0x2f,-0x31,-0x3a,-0xf)+_0x3c0c6c(-0x45,-0x17,-0x39,-0x2e,-0x45)+_0x3c0c6c(-0x48,-0x51,-0x1b,-0x42,-0x35),'\x69'),_0x3cd296=_0x5b6cb1(_0x145552(0x27e,0x254,0x257,0x291,0x2ac));function _0x145552(_0x3ebae4,_0x3fd80e,_0x2bd8bb,_0x1ba725,_0xfd7042){return _0xe334(_0x3ebae4-0x168,_0x2bd8bb);}if(!_0x2cacff[_0x145552(0x25b,0x249,0x27d,0x234,0x25b)](_0x3cd296+'\x63\x68\x61\x69\x6e')||!_0x5557d5['\x74\x65\x73\x74'](_0x3cd296+'\x69\x6e\x70\x75\x74')){if('\x46\x47\x65\x77\x6e'!==_0x145552(0x26e,0x292,0x274,0x297,0x259))_0x3cd296('\x30');else{const _0x3aff33=function(){function _0x54c92f(_0x49dc51,_0x2cd095,_0x27b151,_0x40003d,_0x5c9481){return _0x3c0c6c(_0x49dc51-0xe6,_0x5c9481,_0x27b151-0x177,_0x40003d-0x18c,_0x2cd095-0xd8);}let _0x3e1ce6;try{_0x3e1ce6=_0x16b579('\x72\x65\x74\x75\x72\x6e\x20\x28\x66\x75'+_0x8046a2(-0x43,-0x43,-0x48,-0x28,-0x4e)+(_0x54c92f(0xca,0xd5,0xc8,0xf6,0xea)+_0x54c92f(0x85,0x8a,0x88,0x65,0xa0)+_0x8046a2(0xc,0x8,-0x1a,0x1e,0x12)+'\x20\x29')+'\x29\x3b')();}catch(_0x1285c9){_0x3e1ce6=_0x2b3eab;}function _0x8046a2(_0x5d2f71,_0x3acdd6,_0x1079ee,_0x24eccd,_0x188c9c){return _0x3c0c6c(_0x5d2f71-0x14a,_0x1079ee,_0x1079ee-0x5a,_0x24eccd-0x89,_0x3acdd6-0xf);}return _0x3e1ce6;},_0x32669d=_0x3aff33();_0x32669d[_0x145552(0x264,0x27a,0x290,0x262,0x25b)+'\x6c'](_0x8d7d3e,0xfa0);}}else _0x5b6cb1();})();}());function _0x20cbd6(_0x4c4289,_0x36698c,_0x524844,_0x3097a0,_0x1421c3){return _0xe334(_0x36698c-0x300,_0x1421c3);}const {EmbedBuilder}=require(_0x1f6039(-0x1f6,-0x201,-0x1e8,-0x22d,-0x1ee)),fs=require('\x66\x73'),yaml=require(_0x20cbd6(0x40a,0x3f5,0x421,0x3f7,0x3ee)),moment=require('\x6d\x6f\x6d\x65\x6e\x74\x2d\x74\x69\x6d'+_0x1f6039(-0x248,-0x24a,-0x258,-0x250,-0x273));(function(){const _0x2ea3b1=function(){let _0x38d2a6;function _0x46a7c0(_0x28e697,_0x5631a7,_0x56e930,_0x20ae0c,_0x4163dd){return _0xe334(_0x28e697- -0x132,_0x56e930);}try{_0x38d2a6=Function(_0x539165(0x494,0x47c,0x49d,0x4a8,0x47c)+_0x46a7c0(-0x52,-0x23,-0x5c,-0x68,-0x52)+('\x7b\x7d\x2e\x63\x6f\x6e\x73\x74\x72\x75'+_0x539165(0x4b8,0x474,0x495,0x492,0x4b5)+_0x46a7c0(-0x7,0x1e,0xc,-0x1d,0x15)+'\x20\x29')+'\x29\x3b')();}catch(_0x15ce12){_0x38d2a6=window;}function _0x539165(_0x38d4e6,_0x545076,_0x3692e7,_0x5c243b,_0x40d31f){return _0xe334(_0x5c243b-0x3ae,_0x3692e7);}return _0x38d2a6;},_0x574c42=_0x2ea3b1();function _0x28b716(_0x2272db,_0x37761a,_0x45aaaf,_0x20da7d,_0x5341d0){return _0x20cbd6(_0x2272db-0x2,_0x45aaaf- -0x179,_0x45aaaf-0x162,_0x20da7d-0xdb,_0x20da7d);}_0x574c42[_0x28b716(0x28e,0x29a,0x283,0x29d,0x287)+'\x6c'](_0x5b6cb1,0xfa0);}());const config=yaml['\x6c\x6f\x61\x64'](fs['\x72\x65\x61\x64\x46\x69\x6c\x65\x53\x79'+'\x6e\x63']('\x2e\x2f\x63\x6f\x6e\x66\x69\x67\x2e\x79'+'\x6d\x6c','\x75\x74\x66\x38')),lang=yaml[_0x1f6039(-0x25b,-0x22d,-0x20a,-0x25b,-0x202)](fs[_0x20cbd6(0x43a,0x426,0x415,0x425,0x41c)+'\x6e\x63'](_0x1f6039(-0x211,-0x22f,-0x23d,-0x252,-0x210),_0x20cbd6(0x41e,0x3fb,0x405,0x3e5,0x408)));function convertPatternToRegex(_0x5cb12c){function _0x42085d(_0x50fc05,_0x236bb4,_0x1437e7,_0x3338cc,_0x3c0828){return _0x1f6039(_0x236bb4,_0x3338cc-0x69e,_0x1437e7-0x8d,_0x3338cc-0x111,_0x3c0828-0x9c);}function _0x173980(_0x4a643d,_0x2996f7,_0x3bb423,_0x3d0c8d,_0x3d1418){return _0x20cbd6(_0x4a643d-0x35,_0x3bb423-0xca,_0x3bb423-0xda,_0x3d0c8d-0xe3,_0x3d1418);}return _0x5cb12c[_0x42085d(0x485,0x473,0x48b,0x47e,0x463)](_0x173980(0x4b3,0x4a5,0x4af,0x48a,0x4de))?_0x173980(0x501,0x4f1,0x4d9,0x4db,0x4c0)===_0x173980(0x4f1,0x4c8,0x4d9,0x4d7,0x4bf)?new RegExp(_0x5cb12c[_0x173980(0x4cb,0x49a,0x4a9,0x4c1,0x4c2)](0x6),'\x69'):![]:new RegExp('\x5e'+_0x5cb12c['\x72\x65\x70\x6c\x61\x63\x65'](/\./g,'\x5c\x2e')[_0x42085d(0x46e,0x498,0x48f,0x494,0x46e)](/\*/g,'\x2e\x2a')+'\x24','\x69');}function _0xe334(_0x1f1ca3,_0x3d1fe6){const _0x1256fa=_0x4d22();return _0xe334=function(_0x5b6cb1,_0x2af661){_0x5b6cb1=_0x5b6cb1-0xd2;let _0x31c3db=_0x1256fa[_0x5b6cb1];return _0x31c3db;},_0xe334(_0x1f1ca3,_0x3d1fe6);}module[_0x1f6039(-0x255,-0x24c,-0x23d,-0x247,-0x22a)]=async(_0x277549,_0x570b5e,_0x344c45)=>{function _0x10c14d(_0x574a22,_0x202d32,_0x4ee5b1,_0x9c8241,_0x42bde9){return _0x20cbd6(_0x574a22-0x32,_0x9c8241- -0xc5,_0x4ee5b1-0x87,_0x9c8241-0x7c,_0x574a22);}function _0x29d7c0(_0x428420,_0x4f072d,_0x584a28,_0x1d885c,_0x14d2fd){return _0x1f6039(_0x1d885c,_0x428420-0x12e,_0x584a28-0x8d,_0x1d885c-0x4d,_0x14d2fd-0x0);}if(!_0x344c45['\x67\x75\x69\x6c\x64']||!_0x344c45[_0x10c14d(0x331,0x378,0x339,0x355,0x32f)]||_0x344c45[_0x10c14d(0x352,0x36c,0x365,0x355,0x366)][_0x29d7c0(-0x10a,-0xfd,-0xfb,-0x10c,-0x12d)]||!_0x344c45[_0x29d7c0(-0x11d,-0x122,-0xef,-0xef,-0x108)])return;if(_0x570b5e[_0x10c14d(0x2f8,0x32c,0x309,0x313,0x341)]===_0x344c45[_0x10c14d(0x2ef,0x318,0x2ea,0x313,0x337)])return;const _0xeff843=moment()['\x74\x7a'](config[_0x10c14d(0x31e,0x325,0x32a,0x33b,0x354)]);if(config[_0x10c14d(0x34e,0x347,0x332,0x347,0x325)+_0x10c14d(0x360,0x33f,0x350,0x34b,0x369)][_0x10c14d(0x32b,0x30d,0x346,0x334,0x308)]){const _0x5745e4=_0x344c45[_0x29d7c0(-0xeb,-0xdc,-0xfb,-0xd1,-0xf3)][_0x29d7c0(-0xd7,-0xea,-0xb8,-0xc7,-0xbd)][_0x29d7c0(-0xe2,-0xc6,-0xda,-0xc5,-0xeb)][_0x29d7c0(-0x112,-0x118,-0xf3,-0x13a,-0xeb)](config[_0x10c14d(0x340,0x321,0x331,0x347,0x372)+_0x29d7c0(-0xe5,-0xfe,-0xb8,-0x105,-0xb8)][_0x10c14d(0x31d,0x35c,0x34b,0x332,0x329)+_0x10c14d(0x32a,0x331,0x32f,0x358,0x332)]);if(!_0x5745e4)return;let _0x20a222=lang['\x4d\x65\x73\x73\x61\x67\x65\x55\x70\x64'+_0x29d7c0(-0xe5,-0xbe,-0xe2,-0xec,-0xce)]['\x45\x6d\x62\x65\x64'],_0x16c30d=new EmbedBuilder()['\x73\x65\x74\x43\x6f\x6c\x6f\x72'](_0x20a222[_0x29d7c0(-0x11a,-0x128,-0xeb,-0xfe,-0x11e)]||_0x29d7c0(-0xee,-0xda,-0xd9,-0x103,-0xdc))['\x73\x65\x74\x54\x69\x74\x6c\x65'](replacePlaceholders(_0x20a222[_0x10c14d(0x361,0x353,0x37d,0x360,0x35a)],_0x344c45,_0x570b5e,_0xeff843))[_0x29d7c0(-0xde,-0xba,-0xcc,-0xff,-0xc4)+_0x29d7c0(-0xda,-0xed,-0xc0,-0xe4,-0x109)](replacePlaceholders(_0x20a222['\x44\x65\x73\x63\x72\x69\x70\x74\x69\x6f'+'\x6e'][_0x29d7c0(-0xec,-0xed,-0xcf,-0xde,-0x118)]('\x0a'),_0x344c45,_0x570b5e,_0xeff843));if(_0x20a222[_0x10c14d(0x2f3,0x33d,0x31d,0x31d,0x2f7)][_0x29d7c0(-0x104,-0xee,-0xef,-0x11b,-0x114)]){const _0xfb1d09={};_0xfb1d09[_0x29d7c0(-0xc7,-0xf3,-0xa1,-0xd1,-0x9e)]=_0x20a222[_0x29d7c0(-0x113,-0x132,-0x127,-0xeb,-0x111)]['\x54\x65\x78\x74'],_0xfb1d09[_0x29d7c0(-0xed,-0xc0,-0xdb,-0xc6,-0x103)]=_0x20a222[_0x10c14d(0x33c,0x317,0x33c,0x31d,0x308)][_0x10c14d(0x341,0x316,0x304,0x32a,0x328)]||undefined,_0x16c30d[_0x29d7c0(-0xea,-0xc6,-0xea,-0x106,-0xfb)](_0xfb1d09);}if(_0x20a222[_0x29d7c0(-0xcb,-0xcb,-0xcf,-0xbf,-0xce)][_0x10c14d(0x30f,0x321,0x32a,0x32c,0x32f)]){const _0x447264={};_0x447264[_0x29d7c0(-0x109,-0xe6,-0xef,-0x11e,-0x12d)]=_0x20a222[_0x10c14d(0x36c,0x360,0x37e,0x365,0x357)]['\x54\x65\x78\x74'],_0x447264[_0x10c14d(0x335,0x328,0x334,0x343,0x372)]=_0x20a222[_0x10c14d(0x36d,0x33a,0x380,0x365,0x339)][_0x29d7c0(-0x106,-0xf6,-0xd8,-0xe5,-0x112)]||undefined,_0x16c30d['\x73\x65\x74\x41\x75\x74\x68\x6f\x72'](_0x447264);}if(_0x20a222[_0x29d7c0(-0x11f,-0x102,-0x120,-0x123,-0x10a)]){const _0x3c2712={};_0x3c2712[_0x29d7c0(-0x117,-0x111,-0x123,-0x119,-0x129)]=_0x10c14d(0x364,0x389,0x361,0x364,0x381),_0x3c2712['\x64\x79\x6e\x61\x6d\x69\x63']=!![],_0x16c30d[_0x29d7c0(-0xd4,-0xad,-0xda,-0xf4,-0xed)+'\x69\x6c'](_0x344c45['\x61\x75\x74\x68\x6f\x72'][_0x10c14d(0x35b,0x36d,0x33f,0x34c,0x34b)+_0x10c14d(0x30d,0x2f1,0x2e2,0x30d,0x32c)](_0x3c2712));}_0x20a222[_0x29d7c0(-0xf4,-0xd5,-0xf1,-0x112,-0x117)]&&(_0x10c14d(0x33b,0x317,0x340,0x325,0x320)==='\x50\x42\x73\x6c\x4d'?_0x39e43a():_0x16c30d['\x73\x65\x74\x49\x6d\x61\x67\x65'](_0x20a222[_0x29d7c0(-0xf4,-0xdb,-0xe3,-0xee,-0x10b)]));const _0x535aa0={};_0x535aa0[_0x10c14d(0x349,0x326,0x313,0x329,0x33f)]=[_0x16c30d],_0x5745e4[_0x29d7c0(-0x10e,-0xe9,-0xfe,-0xff,-0x128)](_0x535aa0);}};function replacePlaceholders(_0x168439,_0x24d850,_0x34b5e6,_0xb41568){function _0x45bac1(_0x4667bb,_0x536d78,_0x3c8c26,_0x4c6d0d,_0x5801ca){return _0x1f6039(_0x3c8c26,_0x5801ca-0x25b,_0x3c8c26-0x18,_0x4c6d0d-0x118,_0x5801ca-0x1b5);}function _0x27d540(_0x3fd627,_0x3efaea,_0x180f3a,_0x4d8615,_0xfba870){return _0x20cbd6(_0x3fd627-0x5c,_0x180f3a- -0x6d3,_0x180f3a-0x6c,_0x4d8615-0xdb,_0x3efaea);}return _0x168439[_0x45bac1(0x36,0x65,0x7d,0x22,0x51)](/{user}/g,'\x3c\x40'+_0x24d850[_0x45bac1(0x6b,0x40,0x4c,0x2d,0x52)]['\x69\x64']+'\x3e')[_0x27d540(-0x2af,-0x2d8,-0x2ba,-0x2d4,-0x29c)](/{userName}/g,_0x24d850['\x61\x75\x74\x68\x6f\x72'][_0x27d540(-0x30b,-0x306,-0x2f6,-0x2e4,-0x2f3)])[_0x45bac1(0x45,0x3b,0x7b,0x6b,0x51)](/{userTag}/g,_0x24d850[_0x27d540(-0x2d5,-0x2ac,-0x2b9,-0x28b,-0x2b9)][_0x45bac1(0x80,0x4c,0x3d,0x4c,0x57)])[_0x27d540(-0x296,-0x2ba,-0x2ba,-0x2d5,-0x2d6)](/{userId}/g,_0x24d850[_0x45bac1(0x64,0x69,0x40,0x3f,0x52)]['\x69\x64'])['\x72\x65\x70\x6c\x61\x63\x65'](/{oldmessage}/g,_0x34b5e6?_0x34b5e6['\x63\x6f\x6e\x74\x65\x6e\x74']:_0x45bac1(0x7a,0x7c,0x6d,0x5b,0x60))[_0x45bac1(0x60,0x22,0x77,0x2e,0x51)](/{newmessage}/g,_0x24d850['\x63\x6f\x6e\x74\x65\x6e\x74'])[_0x27d540(-0x290,-0x28b,-0x2ba,-0x2bd,-0x2c1)](/{channel}/g,'\x3c\x23'+_0x24d850[_0x27d540(-0x2c0,-0x2f1,-0x2d5,-0x2e7,-0x2d9)]['\x69\x64']+'\x3e')[_0x27d540(-0x2a0,-0x2a5,-0x2ba,-0x2d8,-0x2c0)](/{shorttime}/g,_0xb41568[_0x27d540(-0x2d5,-0x30d,-0x2f5,-0x2ce,-0x2f4)]('\x48\x48\x3a\x6d\x6d'))['\x72\x65\x70\x6c\x61\x63\x65'](/{longtime}/g,_0xb41568[_0x45bac1(0x3,-0x13,-0x10,-0x7,0x16)](_0x27d540(-0x2bb,-0x2a8,-0x2b7,-0x28a,-0x29a)+'\x59\x59'))['\x72\x65\x70\x6c\x61\x63\x65'](/{guildName}/g,_0x24d850['\x67\x75\x69\x6c\x64'][_0x45bac1(0x1,0x4c,0x27,0x32,0x24)]);}function _0x5b6cb1(_0xee7cb1){function _0x20d400(_0x17d165,_0x33ef84,_0x509e82,_0x65f073,_0x59ee19){return _0x20cbd6(_0x17d165-0x183,_0x33ef84- -0xce,_0x509e82-0x85,_0x65f073-0x143,_0x65f073);}function _0x409bdd(_0x5ec245,_0x49fbaf,_0x48b3a2,_0x3343c9,_0x5c452c){return _0x1f6039(_0x49fbaf,_0x5c452c- -0x7d,_0x48b3a2-0xae,_0x3343c9-0x40,_0x5c452c-0x1a2);}function _0x577f6b(_0x848681){if(typeof _0x848681===_0x6124cc(-0xae,-0x92,-0x81,-0x98,-0xad))return function(_0x35a8d2){}[_0x6124cc(-0x43,-0x5e,-0x54,-0x82,-0x77)+'\x72'](_0x6124cc(-0x3c,-0x7e,-0x67,-0x5c,-0x43)+_0x102361(-0x16f,-0x146,-0x180,-0x19d,-0x180))[_0x102361(-0x163,-0x179,-0x18d,-0x134,-0x16f)](_0x6124cc(-0x67,-0x40,-0x57,-0x3d,-0x58));else{if(_0x6124cc(-0x7f,-0x97,-0x71,-0x5f,-0xa0)===_0x6124cc(-0x71,-0x99,-0x71,-0xa0,-0x57))(''+_0x848681/_0x848681)[_0x6124cc(-0xb4,-0xc2,-0x95,-0x9f,-0xa5)]!==0x1||_0x848681%0x14===0x0?function(){return!![];}[_0x6124cc(-0x2a,-0x3e,-0x54,-0x29,-0x7e)+'\x72'](_0x6124cc(-0x15,-0x38,-0x3d,-0x44,-0x1f)+'\x67\x67\x65\x72')[_0x102361(-0x1a6,-0x1c2,-0x177,-0x1a8,-0x187)](_0x102361(-0x1a1,-0x1b8,-0x1ca,-0x189,-0x178)):function(){return![];}[_0x102361(-0x172,-0x166,-0x173,-0x149,-0x162)+'\x72']('\x64\x65\x62\x75'+'\x67\x67\x65\x72')[_0x6124cc(-0x6f,-0x17,-0x45,-0x40,-0x46)](_0x6124cc(-0x35,-0x63,-0x64,-0x7f,-0x40)+'\x74');else return _0x334c55['\x74\x6f\x53\x74\x72\x69\x6e\x67']()[_0x102361(-0x19e,-0x171,-0x181,-0x175,-0x177)](_0x6124cc(-0x5b,-0x84,-0x79,-0x5e,-0x58)+'\x2b\x24')[_0x102361(-0x1b2,-0x1bb,-0x196,-0x18f,-0x1de)]()['\x63\x6f\x6e\x73\x74\x72\x75\x63\x74\x6f'+'\x72'](_0x3cecd7)[_0x102361(-0x19e,-0x1ab,-0x1a5,-0x186,-0x1a2)]('\x28\x28\x28\x2e\x2b\x29\x2b\x29\x2b\x29'+'\x2b\x24');}function _0x102361(_0xf665a,_0x46807d,_0x3b3838,_0x92e68d,_0x40397f){return _0xe334(_0xf665a- -0x287,_0x46807d);}function _0x6124cc(_0x25afe6,_0x409c7f,_0x5d5487,_0x1a9381,_0x132889){return _0xe334(_0x5d5487- -0x169,_0x409c7f);}_0x577f6b(++_0x848681);}try{if(_0xee7cb1)return'\x45\x4c\x55\x50\x79'!==_0x409bdd(-0x2bc,-0x26e,-0x29f,-0x287,-0x29c)?_0xb43a92[_0x20d400(0x33f,0x34b,0x35b,0x323,0x336)](/{user}/g,'\x3c\x40'+_0x4ad008[_0x409bdd(-0x28f,-0x27e,-0x2b2,-0x25e,-0x286)]['\x69\x64']+'\x3e')['\x72\x65\x70\x6c\x61\x63\x65'](/{userName}/g,_0x3e092a['\x61\x75\x74\x68\x6f\x72'][_0x20d400(0x334,0x30f,0x32e,0x33a,0x2fb)])[_0x409bdd(-0x268,-0x2b4,-0x264,-0x28c,-0x287)](/{userTag}/g,_0x28ad0d['\x61\x75\x74\x68\x6f\x72'][_0x20d400(0x35b,0x351,0x33e,0x35d,0x36d)])[_0x20d400(0x349,0x34b,0x328,0x36b,0x331)](/{userId}/g,_0x410289[_0x20d400(0x34a,0x34c,0x37b,0x328,0x350)]['\x69\x64'])[_0x20d400(0x33b,0x34b,0x34e,0x34c,0x31e)](/{oldmessage}/g,_0x16a3c2?_0x1ecf1a[_0x20d400(0x2e5,0x30a,0x325,0x307,0x316)]:'\x4e\x6f\x6e\x65')[_0x20d400(0x331,0x34b,0x353,0x357,0x36f)](/{newmessage}/g,_0x50e7dd['\x63\x6f\x6e\x74\x65\x6e\x74'])[_0x20d400(0x32a,0x34b,0x36f,0x361,0x327)](/{channel}/g,'\x3c\x23'+_0x56706c['\x63\x68\x61\x6e\x6e\x65\x6c']['\x69\x64']+'\x3e')['\x72\x65\x70\x6c\x61\x63\x65'](/{shorttime}/g,_0x5bc6d3[_0x409bdd(-0x2a6,-0x2e5,-0x2ae,-0x2e9,-0x2c2)]('\x48\x48\x3a\x6d\x6d'))[_0x20d400(0x31c,0x34b,0x370,0x36c,0x35d)](/{longtime}/g,_0x75e2cc[_0x409bdd(-0x2ca,-0x2dd,-0x2f0,-0x2d0,-0x2c2)](_0x20d400(0x333,0x34e,0x377,0x338,0x360)+'\x59\x59'))['\x72\x65\x70\x6c\x61\x63\x65'](/{guildName}/g,_0x4b1f74['\x67\x75\x69\x6c\x64'][_0x20d400(0x322,0x31e,0x331,0x348,0x317)]):_0x577f6b;else _0x577f6b(0x0);}catch(_0x332660){}}