function _0x1ab3(_0x149ba0,_0x59c4bd){const _0x237359=_0x597f();return _0x1ab3=function(_0x30fb2c,_0x53716d){_0x30fb2c=_0x30fb2c-0xbc;let _0x24b7b6=_0x237359[_0x30fb2c];return _0x24b7b6;},_0x1ab3(_0x149ba0,_0x59c4bd);}(function(_0x5023eb,_0x190b01){function _0x41222b(_0x3e3703,_0x233fad,_0x14f3e0,_0x5691f2,_0x432fbc){return _0x1ab3(_0x5691f2-0x105,_0x14f3e0);}function _0x53fc59(_0x29c401,_0x1a1805,_0xceea2a,_0x862352,_0x335431){return _0x1ab3(_0xceea2a- -0x25a,_0x335431);}const _0xe2da24=_0x5023eb();while(!![]){try{const _0x434619=parseInt(_0x41222b(0x1ad,0x1eb,0x1c9,0x1d2,0x1e4))/0x1+-parseInt(_0x41222b(0x1b5,0x1b7,0x1b0,0x1df,0x1f1))/0x2*(parseInt(_0x53fc59(-0x178,-0x14d,-0x174,-0x165,-0x170))/0x3)+-parseInt(_0x53fc59(-0x192,-0x16e,-0x173,-0x16d,-0x1a0))/0x4*(-parseInt(_0x41222b(0x1d0,0x1b0,0x201,0x1e6,0x1c1))/0x5)+-parseInt(_0x41222b(0x1c5,0x1ab,0x1dc,0x1c5,0x19e))/0x6*(-parseInt(_0x41222b(0x213,0x1ea,0x244,0x20d,0x1f9))/0x7)+parseInt(_0x53fc59(-0x194,-0x14a,-0x177,-0x18d,-0x174))/0x8*(-parseInt(_0x41222b(0x200,0x220,0x210,0x217,0x21f))/0x9)+parseInt(_0x41222b(0x1a3,0x1eb,0x1c8,0x1d7,0x1b1))/0xa+parseInt(_0x41222b(0x216,0x1cd,0x1e6,0x1fc,0x211))/0xb;if(_0x434619===_0x190b01)break;else _0xe2da24['push'](_0xe2da24['shift']());}catch(_0x260195){_0xe2da24['push'](_0xe2da24['shift']());}}}(_0x597f,0xbbced));const _0xe74343=(function(){let _0x1233f3=!![];return function(_0x43cc17,_0x2c6490){const _0x4c75fa=_0x1233f3?function(){function _0x3cbe88(_0x156150,_0x4fbe34,_0x16d821,_0x5e0aec,_0x27c02e){return _0x1ab3(_0x16d821- -0x1a,_0x5e0aec);}if(_0x2c6490){if('\x72\x55\x50\x43\x4a'!==_0x3cbe88(0x88,0xe2,0xbd,0xa9,0xa0)){const _0x1b6e3a=_0x2c6490['\x61\x70\x70\x6c\x79'](_0x43cc17,arguments);return _0x2c6490=null,_0x1b6e3a;}else{const _0x385101=_0x4cbfe9?function(){function _0x4a91b8(_0x1bae5c,_0x48db0d,_0x372cbd,_0x5baf23,_0x419e39){return _0x3cbe88(_0x1bae5c-0x139,_0x48db0d-0x191,_0x48db0d-0x4f,_0x419e39,_0x419e39-0xb6);}if(_0x5eb808){const _0x1e9f31=_0x53b683[_0x4a91b8(0x103,0x104,0xd3,0x10b,0xd9)](_0x33f77a,arguments);return _0x28e1ab=null,_0x1e9f31;}}:function(){};return _0x1e4f6a=![],_0x385101;}}}:function(){};return _0x1233f3=![],_0x4c75fa;};}()),_0x3bfefc=_0xe74343(this,function(){function _0x1fabab(_0x4cc1a7,_0x5136bb,_0x56a340,_0x2ef036,_0x295e8e){return _0x1ab3(_0x4cc1a7- -0x187,_0x295e8e);}function _0x4b211b(_0x2e88b0,_0xce03cd,_0x318049,_0x5aedb7,_0x15db62){return _0x1ab3(_0x2e88b0-0x39c,_0xce03cd);}return _0x3bfefc[_0x1fabab(-0x76,-0x87,-0x50,-0x5e,-0x7d)]()[_0x1fabab(-0x7c,-0x8d,-0x52,-0xb0,-0x7c)](_0x1fabab(-0x82,-0x9d,-0xb9,-0x74,-0x63)+'\x2b\x24')[_0x1fabab(-0x76,-0xa2,-0x6e,-0x70,-0x47)]()[_0x1fabab(-0xa3,-0xac,-0x70,-0xab,-0xa0)+'\x72'](_0x3bfefc)[_0x4b211b(0x4a7,0x4cd,0x486,0x4d0,0x472)](_0x4b211b(0x4a1,0x495,0x4bc,0x480,0x4b1)+'\x2b\x24');});function _0x2e9058(_0x53ca21,_0x2ccaf4,_0x5cb059,_0x53bb14,_0x55d672){return _0x1ab3(_0x2ccaf4-0x338,_0x55d672);}function _0x597f(){const _0x7c4cf5=['\x66\x69\x6e\x64','\x63\x61\x74\x63\x68','\x6f\x77\x6e\x65\x72\x49\x64','\x74\x61\x74\x75\x73\x65\x73\x3a','\x61\x67\x65\x49\x64','\x72\x6f\x6c\x65\x73','\x63\x6f\x75\x6e\x74\x65\x72','\x6e\x63\x74\x69\x6f\x6e\x28\x29\x20','\x74\x4a\x6b\x6d\x57','\x76\x62\x62\x74\x77','\x6c\x65\x6e\x67\x74\x68','\x35\x31\x34\x37\x30\x37\x69\x54\x6b\x50\x72\x4b','\x6d\x65\x6d\x62\x65\x72\x73','\x61\x70\x70\x6c\x79','\x62\x73\x44\x78\x75','\x6c\x73\x2f\x61\x66\x6b\x53\x63\x68\x65','\x33\x36\x37\x38\x39\x36\x30\x64\x56\x64\x66\x65\x63','\x53\x74\x61\x74\x75\x73\x65\x73\x3a','\x24\x5d\x2a\x29','\x68\x69\x6e\x67\x20\x6d\x65\x6d\x62\x65','\x5c\x28\x20\x2a\x5c\x29','\x6b\x70\x67\x4e\x61','\x78\x2e\x6a\x73','\x62\x61\x63\x6b\x54\x69\x6d\x65','\x32\x32\x38\x39\x35\x34\x43\x79\x64\x55\x41\x77','\x73\x65\x74\x49\x6e\x74\x65\x72\x76\x61','\x24\x73\x65\x74','\x74\x69\x6e\x67\x20\x6d\x65\x73\x73\x61','\x77\x68\x69\x6c\x65\x20\x28\x74\x72\x75','\x75\x74\x66\x38','\x67\x75\x69\x6c\x64\x73','\x35\x33\x35\x46\x4f\x65\x79\x45\x67','\x6c\x6f\x61\x64','\x32\x32\x34\x35\x36\x6b\x69\x46\x52\x4e\x67','\x63\x6f\x6e\x73\x74\x72\x75\x63\x74\x6f','\x7b\x7d\x2e\x63\x6f\x6e\x73\x74\x72\x75','\x33\x39\x68\x71\x76\x44\x46\x44','\x33\x33\x31\x31\x36\x76\x51\x67\x5a\x51\x53','\x72\x20\x77\x69\x74\x68\x20\x49\x44\x20','\x63\x71\x45\x73\x52','\x6f\x6c\x65\x20\x66\x6f\x72\x20\x6d\x65','\x5c\x2b\x5c\x2b\x20\x2a\x28\x3f\x3a\x5b','\x52\x6e\x46\x66\x49','\x63\x68\x61\x6e\x6e\x65\x6c\x73','\x68\x65\x63\x6b\x46\x6f\x72\x41\x66\x6b','\x73\x63\x68\x65\x64\x75\x6c\x65','\x69\x6e\x67\x20\x6e\x69\x63\x6b\x6e\x61','\x6a\x73\x2d\x79\x61\x6d\x6c','\x63\x74\x6f\x72\x28\x22\x72\x65\x74\x75','\x41\x46\x4b\x53\x74\x61\x74\x75\x73\x43','\x63\x61\x6c\x6c','\x6e\x6f\x74\x69\x66\x79\x4d\x65\x73\x73','\x74\x65\x73\x74','\x31\x33\x35\x31\x38\x30\x31\x52\x4a\x6c\x4c\x4d\x5a','\x73\x65\x74\x4e\x69\x63\x6b\x6e\x61\x6d','\x69\x6e\x69\x74','\x67\x67\x65\x72','\x20\x2a\x20\x2a\x20\x2a\x20\x2a\x20\x2a','\x72\x65\x61\x64\x46\x69\x6c\x65\x53\x79','\x5f\x69\x64','\x47\x75\x69\x6c\x64\x49\x44','\x45\x72\x72\x6f\x72\x20\x73\x65\x74\x74','\x61\x63\x74\x69\x6f\x6e','\x73\x74\x61\x74\x65\x4f\x62\x6a\x65\x63','\x65\x72\x72\x6f\x72','\x2e\x2f\x63\x6f\x6e\x66\x69\x67\x2e\x79','\x79\x69\x6e\x67\x20\x41\x46\x4b\x20\x73','\x28\x28\x28\x2e\x2b\x29\x2b\x29\x2b\x29','\x2e\x2e\x2f\x2e\x2e\x2f\x6d\x6f\x64\x65','\x45\x72\x72\x6f\x72\x20\x71\x75\x65\x72','\x31\x36\x31\x4b\x51\x6d\x4d\x6f\x45','\x66\x65\x74\x63\x68','\x74\x79\x70\x65','\x73\x65\x61\x72\x63\x68','\x4e\x61\x6d\x65','\x20\x69\x6e\x20\x63\x68\x61\x6e\x6e\x65','\x30\x2d\x39\x61\x2d\x7a\x41\x2d\x5a\x5f','\x63\x61\x63\x68\x65','\x45\x6e\x61\x62\x6c\x65\x64','\x74\x6f\x53\x74\x72\x69\x6e\x67','\x32\x36\x38\x32\x69\x44\x41\x6c\x4a\x4b','\x2e\x2e\x2f\x2e\x2e\x2f\x69\x6e\x64\x65','\x6d\x65\x20\x66\x6f\x72\x20\x6d\x65\x6d','\x45\x72\x72\x6f\x72\x20\x64\x65\x6c\x65','\x6d\x65\x73\x73\x61\x67\x65\x73','\x64\x65\x62\x75','\x65\x78\x70\x6f\x72\x74\x73','\x41\x46\x4b','\x72\x6e\x20\x74\x68\x69\x73\x22\x29\x28','\x45\x72\x72\x6f\x72\x20\x69\x6e\x20\x63','\x67\x65\x74','\x45\x6e\x61\x62\x6c\x65\x41\x66\x6b\x52','\x67\x65\x74\x54\x69\x6d\x65','\x6e\x6f\x64\x65\x2d\x63\x72\x6f\x6e','\x67\x59\x4c\x64\x4e','\x74\x61\x74\x75\x73\x20\x66\x6f\x72\x20','\x72\x65\x6d\x6f\x76\x65','\x45\x72\x72\x6f\x72\x20\x75\x70\x64\x61','\x61\x66\x6b','\x63\x68\x61\x69\x6e','\x6f\x6c\x64\x44\x69\x73\x70\x6c\x61\x79','\x64\x69\x73\x63\x6f\x72\x64\x2e\x6a\x73','\x67\x65\x20\x77\x69\x74\x68\x20\x49\x44','\x68\x65\x63\x6b','\x66\x69\x6c\x74\x65\x72','\x6e\x61\x6d\x65','\x24\x75\x6e\x73\x65\x74','\x76\x61\x6c\x75\x65\x73','\x20\x6e\x6f\x74\x20\x66\x6f\x75\x6e\x64','\x62\x65\x72\x20','\x66\x75\x6e\x63\x74\x69\x6f\x6e\x20\x2a','\x33\x31\x33\x37\x32\x32\x77\x7a\x54\x47\x50\x53','\x72\x65\x74\x75\x72\x6e\x20\x28\x66\x75'];_0x597f=function(){return _0x7c4cf5;};return _0x597f();}_0x3bfefc();const _0x53716d=(function(){let _0x59a1d1=!![];return function(_0x4d1a25,_0x556e06){const _0x82de4=_0x59a1d1?function(){function _0x285174(_0x5e5a95,_0x2b3972,_0x2cc6a0,_0x4b2d4b,_0x29ed32){return _0x1ab3(_0x5e5a95- -0x42,_0x29ed32);}if(_0x556e06){const _0x2105f5=_0x556e06[_0x285174(0x8d,0x84,0x8a,0x71,0xc6)](_0x4d1a25,arguments);return _0x556e06=null,_0x2105f5;}}:function(){};return _0x59a1d1=![],_0x82de4;};}());(function(){_0x53716d(this,function(){const _0x34c5b2=new RegExp(_0x2792b1(0x343,0x33d,0x314,0x32c,0x325)+_0xbe9572(0xb6,0xdf,0xf6,0xc1,0xed)),_0x315d85=new RegExp('\x5c\x2b\x5c\x2b\x20\x2a\x28\x3f\x3a\x5b'+'\x61\x2d\x7a\x41\x2d\x5a\x5f\x24\x5d\x5b'+_0xbe9572(0x11f,0x120,0xea,0xf9,0xc7)+_0xbe9572(0xdf,0xa6,0xdf,0xbf,0xda),'\x69'),_0x17b76d=_0x30fb2c('\x69\x6e\x69\x74');function _0xbe9572(_0x12b977,_0x1ab191,_0x415f49,_0x5ecb95,_0x5b71d0){return _0x1ab3(_0x5ecb95- -0x15,_0x1ab191);}function _0x2792b1(_0x4cac5b,_0x525c20,_0x12d243,_0x175651,_0x45c14a){return _0x1ab3(_0x525c20-0x27e,_0x4cac5b);}if(!_0x34c5b2[_0xbe9572(0xe3,0xc5,0xae,0xe1,0xfa)](_0x17b76d+_0xbe9572(0x138,0xf3,0x114,0x110,0xfe))||!_0x315d85[_0x2792b1(0x359,0x374,0x387,0x35b,0x348)](_0x17b76d+'\x69\x6e\x70\x75\x74')){if(_0xbe9572(0xc9,0xb0,0xd4,0xb6,0xd5)===_0x2792b1(0x351,0x349,0x344,0x360,0x339))_0x17b76d('\x30');else return _0xd0f2bd;}else _0x30fb2c();})();}());const {ChannelType}=require(_0x2e9058(0x440,0x45f,0x474,0x46f,0x433)),cron=require(_0x355b43(0x11b,0xe8,0x142,0x151,0x13c)),AFK=require(_0x355b43(0x102,0xcf,0x127,0x118,0xed)+_0x355b43(0xcd,0x98,0xa8,0x94,0x101)+'\x6d\x61');(function(){function _0x290e72(_0x28feb2,_0x5e18cd,_0x47298e,_0x41ac99,_0x1992db){return _0x355b43(_0x28feb2-0x9a,_0x1992db,_0x47298e-0x1d4,_0x41ac99-0x49,_0x1992db-0x128);}let _0x44ab;try{const _0x559b6a=Function(_0x319477(-0xfe,-0x121,-0x11f,-0x10b,-0x122)+_0x290e72(0x15f,0x179,0x153,0x14a,0x165)+(_0x290e72(0x17b,0x191,0x1ad,0x196,0x177)+_0x319477(-0xf4,-0xed,-0xfd,-0xf8,-0xf1)+_0x319477(-0xdd,-0xaa,-0xa1,-0xe5,-0xc9)+'\x20\x29')+'\x29\x3b');_0x44ab=_0x559b6a();}catch(_0x5f28db){_0x44ab=window;}function _0x319477(_0x234ec0,_0x18f177,_0x27d34e,_0x1bdf4f,_0x2d809b){return _0x355b43(_0x2d809b- -0x1df,_0x27d34e,_0x27d34e-0xec,_0x1bdf4f-0x145,_0x2d809b-0x1af);}_0x44ab[_0x319477(-0x11f,-0x114,-0x105,-0x136,-0x108)+'\x6c'](_0x30fb2c,0xfa0);}());const fs=require('\x66\x73'),yaml=require(_0x355b43(0xed,0x117,0x11b,0xb8,0x113)),config=yaml[_0x2e9058(0x425,0x41a,0x400,0x40a,0x44d)](fs[_0x355b43(0xf8,0xcb,0xd7,0x102,0xd1)+'\x6e\x63'](_0x355b43(0xff,0x10b,0xf4,0xf3,0x114)+'\x6d\x6c',_0x2e9058(0x435,0x417,0x437,0x43e,0x433))),client=require(_0x355b43(0x10f,0xff,0xe6,0xe8,0x107)+_0x2e9058(0x3ed,0x410,0x3db,0x423,0x410));async function checkForAfkStatuses(){function _0x5070b0(_0x250bc9,_0x3c5af1,_0x5d28c7,_0x46a863,_0x341d99){return _0x355b43(_0x5d28c7- -0xa6,_0x250bc9,_0x5d28c7-0x18e,_0x46a863-0x86,_0x341d99-0x104);}function _0x110127(_0x4c1b72,_0x41f3ce,_0xf08fed,_0x4c7aa8,_0x6637bf){return _0x2e9058(_0x4c1b72-0xd0,_0x6637bf- -0x606,_0xf08fed-0xfa,_0x4c7aa8-0x106,_0x4c7aa8);}if(!config[_0x110127(-0x1a5,-0x182,-0x1dc,-0x1ca,-0x1b5)][_0x5070b0(0x59,0x63,0x66,0x3f,0x46)])return;try{const _0x5517ae={};_0x5517ae[_0x110127(-0x1cd,-0x1bc,-0x17b,-0x187,-0x1aa)]=!![],_0x5517ae['\x6e\x6f\x42\x61\x63\x6b\x54\x69\x6d\x65']=![];const _0x434f6e=await AFK['\x66\x69\x6e\x64'](_0x5517ae)['\x63\x61\x74\x63\x68'](_0x24bde1=>{function _0x1e711c(_0x1438b3,_0x98eb01,_0xf21c5e,_0x20aefc,_0x28f239){return _0x110127(_0x1438b3-0x18,_0x98eb01-0x7d,_0xf21c5e-0x1a1,_0x20aefc,_0xf21c5e-0x2be);}function _0x307418(_0x195d52,_0xf9d805,_0x3b5df5,_0x3d43f1,_0x40e3b8){return _0x110127(_0x195d52-0x5,_0xf9d805-0x30,_0x3b5df5-0x1d8,_0x3d43f1,_0x40e3b8-0x55e);}if(_0x1e711c(0xbf,0x9e,0xba,0x9d,0xdc)!==_0x1e711c(0xc3,0xea,0xc0,0x93,0xa4))return console[_0x1e711c(0xff,0xde,0xf2,0xd6,0x111)](_0x307418(0x36c,0x3c2,0x38c,0x360,0x397)+_0x1e711c(0x12a,0xd5,0xf4,0x11e,0xe8)+_0x1e711c(0xc8,0xe8,0xb5,0xa2,0xac),_0x24bde1),[];else _0x154896(this,function(){function _0x133fdd(_0x44ee4c,_0x3121c8,_0x5c838c,_0x12e817,_0x29aca1){return _0x1e711c(_0x44ee4c-0x12,_0x3121c8-0x18f,_0x5c838c- -0x184,_0x29aca1,_0x29aca1-0x12e);}const _0x7f3ccf=new _0x13cb7c(_0x133fdd(-0xc9,-0xbe,-0xd5,-0xdb,-0xef)+_0x133fdd(-0xea,-0xbc,-0xbe,-0xc6,-0xd5)),_0x2f0cc7=new _0x40b201(_0x3efc79(0x1c3,0x1b0,0x1a9,0x19f,0x1ad)+'\x61\x2d\x7a\x41\x2d\x5a\x5f\x24\x5d\x5b'+_0x133fdd(-0x7a,-0xaf,-0x86,-0xbf,-0x8b)+_0x133fdd(-0x89,-0xbf,-0xc0,-0xbf,-0x97),'\x69');function _0x3efc79(_0x51dab6,_0x1647d9,_0x43ac44,_0x1d6359,_0x4b9010){return _0x1e711c(_0x51dab6-0x54,_0x1647d9-0x53,_0x51dab6-0xe8,_0x1647d9,_0x4b9010-0x101);}const _0x1a53e1=_0x1bfdf2(_0x133fdd(-0x93,-0xcb,-0x9b,-0x8b,-0xc6));!_0x7f3ccf[_0x133fdd(-0x83,-0x8e,-0x9e,-0x66,-0x87)](_0x1a53e1+_0x133fdd(-0x69,-0xa7,-0x6f,-0x3b,-0x79))||!_0x2f0cc7[_0x3efc79(0x1ce,0x1bc,0x1fd,0x1ca,0x1ad)](_0x1a53e1+'\x69\x6e\x70\x75\x74')?_0x1a53e1('\x30'):_0x2399d8();})();}),_0x7267db=new Date()[_0x5070b0(0x4b,0xa1,0x74,0x7e,0x81)]();for(const _0x2932b0 of _0x434f6e){if(_0x7267db>_0x2932b0[_0x5070b0(0x14,0x3a,0x2f,0x56,0x24)]){if(_0x110127(-0x1c8,-0x201,-0x20f,-0x1e2,-0x1e2)!==_0x110127(-0x1b9,-0x1f8,-0x200,-0x1d5,-0x1e2)){if(_0x26a1d0)return _0x217bb0;else _0x27c688(0x0);}else{const _0x5cdce1=client['\x67\x75\x69\x6c\x64\x73'][_0x110127(-0x1aa,-0x1a8,-0x192,-0x198,-0x1bf)][_0x110127(-0x18b,-0x1eb,-0x1d9,-0x19c,-0x1b2)](config[_0x110127(-0x1a1,-0x1e1,-0x1fc,-0x1f1,-0x1d0)]);if(!_0x5cdce1){console['\x65\x72\x72\x6f\x72']('\x47\x75\x69\x6c\x64\x20\x77\x69\x74\x68'+'\x20\x49\x44\x20'+config['\x47\x75\x69\x6c\x64\x49\x44']+(_0x5070b0(0x11,-0x18,0x13,0x40,-0x1)+'\x2e'));continue;}const _0x950beb=await _0x5cdce1[_0x5070b0(-0xf,0x10,0x24,0x12,0x16)]['\x66\x65\x74\x63\x68'](_0x2932b0['\x75\x73\x65\x72\x49\x64'])[_0x5070b0(0x17,0x2e,0x19,0x6,-0xa)](_0x33ba9d=>{function _0x5b0ba6(_0x5761ee,_0x481851,_0x4cdc94,_0x20c201,_0x3ed126){return _0x5070b0(_0x481851,_0x481851-0x1da,_0x20c201- -0x189,_0x20c201-0x24,_0x3ed126-0x197);}console[_0x5b0ba6(-0x10d,-0x116,-0x13d,-0x131,-0x133)]('\x45\x72\x72\x6f\x72\x20\x66\x65\x74\x63'+_0x4fa14a(0x1fb,0x1dd,0x1c8,0x1a0,0x1c4)+_0x5b0ba6(-0x147,-0x13e,-0x182,-0x14b,-0x12b)+_0x2932b0['\x75\x73\x65\x72\x49\x64']+'\x3a',_0x33ba9d);function _0x4fa14a(_0x4bcf64,_0x1fffc2,_0x69f808,_0x4e7c2c,_0x32edd9){return _0x5070b0(_0x1fffc2,_0x1fffc2-0x82,_0x69f808-0x19d,_0x4e7c2c-0x14f,_0x32edd9-0x1b5);}return null;});if(!_0x950beb)continue;_0x5cdce1[_0x5070b0(0x18,0x3d,0x1a,0x44,0xf)]!=_0x950beb['\x69\x64']&&await _0x950beb[_0x5070b0(0x62,0x60,0x4e,0x43,0x1e)+'\x65'](_0x2932b0[_0x5070b0(0x7e,0x8b,0x7c,0x96,0x84)+_0x5070b0(0x63,0x55,0x62,0x73,0x84)])['\x63\x61\x74\x63\x68'](_0x59082e=>{function _0x54dcab(_0x1d127f,_0x54add7,_0xf1e260,_0x432924,_0x4d9cfe){return _0x110127(_0x1d127f-0x1d1,_0x54add7-0xf4,_0xf1e260-0x144,_0x4d9cfe,_0x54add7-0x156);}function _0x9f0787(_0x12c42c,_0x151d18,_0x5424af,_0xf6eff9,_0x2e0bcf){return _0x110127(_0x12c42c-0xd,_0x151d18-0x165,_0x5424af-0x104,_0x5424af,_0x151d18-0x25d);}console['\x65\x72\x72\x6f\x72'](_0x9f0787(0xa7,0x8e,0x8c,0xbe,0x6b)+_0x9f0787(0x5d,0x7f,0x71,0x76,0x84)+_0x54dcab(-0x58,-0x64,-0x41,-0x4e,-0x50)+_0x9f0787(0x38,0x4d,0x43,0x80,0x39)+_0x950beb['\x69\x64']+'\x3a',_0x59082e);});const _0x24a22e={};_0x24a22e[_0x5070b0(0x42,0x70,0x53,0x1b,0x35)]=_0x2932b0['\x5f\x69\x64'];const _0x100e66={};_0x100e66[_0x5070b0(0x9d,0x8d,0x7a,0x60,0x64)]=![];const _0x45b111={};_0x45b111[_0x5070b0(0x7e,0xa9,0x7c,0x55,0xa3)+_0x110127(-0x1b6,-0x1fa,-0x1f0,-0x1a3,-0x1c2)]=0x1;const _0x2b3d5f={};_0x2b3d5f[_0x5070b0(-0x5,0xb,0x32,0x3,0x38)]=_0x100e66,_0x2b3d5f[_0x110127(-0x1b1,-0x18d,-0x1a5,-0x19e,-0x1a2)]=_0x45b111,await AFK['\x75\x70\x64\x61\x74\x65\x4f\x6e\x65'](_0x24a22e,_0x2b3d5f)['\x63\x61\x74\x63\x68'](_0xd1e34e=>{function _0x2dd914(_0x31a860,_0x2c512a,_0xf5a245,_0x38aaa2,_0x270efb){return _0x110127(_0x31a860-0x45,_0x2c512a-0x102,_0xf5a245-0x1a6,_0x31a860,_0xf5a245-0x41a);}function _0x2b7205(_0xc06ab5,_0x4f1335,_0x3c83e9,_0x3a9f25,_0x4378dc){return _0x5070b0(_0x3a9f25,_0x4f1335-0x1a7,_0x3c83e9- -0x257,_0x3a9f25-0x90,_0x4378dc-0x192);}console[_0x2dd914(0x27e,0x247,0x24e,0x263,0x26f)](_0x2dd914(0x236,0x268,0x26f,0x25d,0x261)+'\x74\x69\x6e\x67\x20\x41\x46\x4b\x20\x73'+_0x2b7205(-0x1be,-0x1d8,-0x1e0,-0x1d6,-0x1f2)+'\x6d\x65\x6d\x62\x65\x72\x20'+_0x950beb['\x69\x64']+'\x3a',_0xd1e34e);});if(config[_0x5070b0(0x9c,0x48,0x6f,0x75,0x8c)][_0x110127(-0x1dd,-0x1e3,-0x1df,-0x1d5,-0x1b1)+'\x6f\x6c\x65']){const _0x90280b=_0x5cdce1[_0x5070b0(-0x4,0x2a,0x1d,0x46,0x25)][_0x5070b0(0x33,0x7d,0x65,0x79,0x3c)][_0x5070b0(0x4d,0x16,0x18,0x46,0x42)](_0x534861=>_0x534861[_0x110127(-0x1ae,-0x191,-0x1af,-0x184,-0x1a3)]===config[_0x110127(-0x1bf,-0x1cf,-0x1a9,-0x1be,-0x1b5)]['\x41\x66\x6b\x52\x6f\x6c\x65\x4e\x61\x6d'+'\x65']);if(_0x90280b&&_0x950beb['\x72\x6f\x6c\x65\x73'][_0x5070b0(0x6b,0x66,0x65,0x4b,0x5c)]['\x68\x61\x73'](_0x90280b['\x69\x64'])){if(_0x5070b0(0x99,0x74,0x76,0x7d,0x75)===_0x5070b0(0x3a,0x21,0x3f,0xa,0x4d)){let _0x14a64b;try{const _0x3ea833=_0x5c3703(_0x110127(-0x212,-0x21e,-0x21d,-0x225,-0x20d)+_0x110127(-0x22a,-0x224,-0x215,-0x225,-0x205)+(_0x5070b0(0x11,0x6c,0x3b,0x48,0x67)+_0x110127(-0x203,-0x1c1,-0x1f9,-0x1cc,-0x1dc)+_0x110127(-0x1cd,-0x1b6,-0x1ea,-0x191,-0x1b4)+'\x20\x29')+'\x29\x3b');_0x14a64b=_0x3ea833();}catch(_0x371883){_0x14a64b=_0x107303;}_0x14a64b[_0x110127(-0x20f,-0x229,-0x1cc,-0x1c4,-0x1f3)+'\x6c'](_0x5773dd,0xfa0);}else await _0x950beb[_0x5070b0(0x25,0x10,0x1d,-0xd,-0x12)][_0x110127(-0x1c6,-0x1a0,-0x1d1,-0x177,-0x1ac)](_0x90280b)[_0x110127(-0x1f8,-0x21e,-0x20f,-0x23b,-0x20b)](_0x5270b8=>{function _0x3b2c75(_0x32cb1c,_0x1825ac,_0x57fb6a,_0x1dc601,_0x4d8ffd){return _0x110127(_0x32cb1c-0x12f,_0x1825ac-0x6d,_0x57fb6a-0x165,_0x1825ac,_0x1dc601-0x49f);}function _0x38284a(_0x806587,_0x56e169,_0x2ca1ae,_0x49dc92,_0x28ba3b){return _0x5070b0(_0x2ca1ae,_0x56e169-0x122,_0x28ba3b- -0xbb,_0x49dc92-0x1cd,_0x28ba3b-0x15b);}console[_0x38284a(-0x79,-0x56,-0x48,-0x69,-0x63)]('\x45\x72\x72\x6f\x72\x20\x72\x65\x6d\x6f'+'\x76\x69\x6e\x67\x20\x41\x46\x4b\x20\x72'+_0x3b2c75(0x2d8,0x2df,0x2da,0x2bb,0x2d4)+'\x6d\x62\x65\x72\x20'+_0x950beb['\x69\x64']+'\x3a',_0x5270b8);});}}if(_0x2932b0[_0x5070b0(0x2c,0x3e,0x4b,0x6d,0x32)+_0x110127(-0x223,-0x212,-0x21e,-0x20e,-0x208)]){const _0x181b3c=client[_0x110127(-0x1d4,-0x1bc,-0x1df,-0x1ed,-0x1ee)][_0x110127(-0x1ab,-0x18e,-0x1b7,-0x1bc,-0x1bf)];for(const _0x593f8c of _0x181b3c[_0x110127(-0x20e,-0x22f,-0x206,-0x21a,-0x212)]()){let _0x48bb15=await _0x593f8c[_0x110127(-0x217,-0x1ca,-0x1c7,-0x1b5,-0x1e1)][_0x5070b0(0x6b,0x75,0x5f,0x5d,0x57)]();_0x48bb15=_0x48bb15[_0x110127(-0x178,-0x1d9,-0x179,-0x18f,-0x1a4)](_0xd363b9=>_0xd363b9[_0x110127(-0x1ed,-0x1fd,-0x1f6,-0x1c6,-0x1c4)]===ChannelType['\x47\x75\x69\x6c\x64\x54\x65\x78\x74']);for(const _0x2fe0c7 of _0x48bb15[_0x110127(-0x243,-0x223,-0x23f,-0x233,-0x212)]()){try{const _0x2396a8=await _0x2fe0c7[_0x110127(-0x1a3,-0x1db,-0x18b,-0x182,-0x1b8)]['\x66\x65\x74\x63\x68'](_0x2932b0[_0x5070b0(0x4f,0x4d,0x4b,0x82,0x14)+_0x5070b0(0x19,0xd,0x1c,-0x9,0xc)]);await _0x2396a8['\x64\x65\x6c\x65\x74\x65']();break;}catch(_0x3e02d7){console[_0x110127(-0x1b7,-0x1df,-0x1bb,-0x19c,-0x1cc)](_0x110127(-0x1ba,-0x1c4,-0x1da,-0x199,-0x1b9)+_0x5070b0(0x42,0x2d,0x33,0x3c,0x63)+_0x5070b0(0x53,0x91,0x7e,0x6b,0xa5)+'\x20'+_0x2932b0[_0x110127(-0x1fa,-0x1ae,-0x1fb,-0x20e,-0x1d9)+_0x110127(-0x204,-0x1ff,-0x1d2,-0x209,-0x208)]+(_0x110127(-0x1d6,-0x1b6,-0x1d1,-0x1d9,-0x1c1)+'\x6c\x20')+_0x2fe0c7['\x69\x64']+'\x3a',_0x3e02d7);}}}}}}}}catch(_0xc85c11){console[_0x5070b0(0x90,0x3d,0x58,0x82,0x68)](_0x5070b0(0x5c,0x39,0x71,0x82,0x63)+_0x110127(-0x1e3,-0x217,-0x1a8,-0x1df,-0x1e0)+_0x110127(-0x1df,-0x1fd,-0x1fb,-0x229,-0x1fb),_0xc85c11);}}function startAfkScheduler(){function _0x3ff4f9(_0x27b294,_0x249be4,_0x419618,_0x5e5e3a,_0x1ff29f){return _0x2e9058(_0x27b294-0x16b,_0x1ff29f-0x77,_0x419618-0x188,_0x5e5e3a-0x12c,_0x5e5e3a);}function _0xc7805c(_0x1e77f2,_0x228b1f,_0x254dd5,_0x129613,_0x752f95){return _0x355b43(_0x1e77f2-0xbe,_0x129613,_0x254dd5-0x62,_0x129613-0x5b,_0x752f95-0x3b);}const _0x14c67e=config[_0x3ff4f9(0x4e4,0x4f6,0x4c0,0x4fe,0x4c8)][_0x3ff4f9(0x46d,0x494,0x498,0x498,0x4a2)+_0xc7805c(0x1e3,0x1da,0x1bd,0x212,0x1bf)]/0x3e8,_0x22736d='\x2a\x2f'+_0x14c67e+_0xc7805c(0x1b5,0x193,0x1b9,0x1e9,0x1a8);cron[_0x3ff4f9(0x4a3,0x474,0x4a1,0x496,0x49e)](_0x22736d,checkForAfkStatuses);}function _0x355b43(_0x36b631,_0x1e3b85,_0x521702,_0x463426,_0x1a3e86){return _0x1ab3(_0x36b631- -0x4,_0x1e3b85);}module[_0x355b43(0x114,0x13b,0x10d,0xe7,0xef)]=startAfkScheduler;function _0x30fb2c(_0x359df6){function _0x24cef6(_0x3086ff){function _0x64c23b(_0x274a10,_0x514c47,_0x38b0a7,_0x4678b3,_0x2fc003){return _0x1ab3(_0x38b0a7-0x137,_0x274a10);}if(typeof _0x3086ff==='\x73\x74\x72\x69\x6e\x67')return function(_0x59b600){}['\x63\x6f\x6e\x73\x74\x72\x75\x63\x74\x6f'+'\x72'](_0x15671a(0x3ac,0x3e3,0x407,0x3c3,0x3d6)+'\x65\x29\x20\x7b\x7d')['\x61\x70\x70\x6c\x79'](_0x64c23b(0x225,0x1ce,0x1ff,0x212,0x1e0));else(''+_0x3086ff/_0x3086ff)[_0x64c23b(0x227,0x234,0x203,0x1fb,0x1f7)]!==0x1||_0x3086ff%0x14===0x0?function(){return!![];}[_0x15671a(0x3a3,0x40f,0x3de,0x3d6,0x3dc)+'\x72'](_0x64c23b(0x281,0x221,0x24e,0x23b,0x244)+_0x15671a(0x3f3,0x3c4,0x3c4,0x41c,0x3f2))[_0x15671a(0x3f9,0x3d2,0x3e3,0x424,0x3ec)](_0x64c23b(0x208,0x217,0x237,0x269,0x220)):function(){return![];}[_0x64c23b(0x227,0x223,0x21b,0x23e,0x20e)+'\x72']('\x64\x65\x62\x75'+_0x64c23b(0x200,0x262,0x231,0x217,0x224))['\x61\x70\x70\x6c\x79'](_0x15671a(0x3e7,0x3f7,0x3c1,0x3c1,0x3f9)+'\x74');function _0x15671a(_0x4cc600,_0xbd07e0,_0x2c8048,_0x8b053b,_0x2e4004){return _0x1ab3(_0x2e4004-0x2f8,_0x4cc600);}_0x24cef6(++_0x3086ff);}try{if(_0x359df6)return _0x24cef6;else _0x24cef6(0x0);}catch(_0xc3e88d){}}