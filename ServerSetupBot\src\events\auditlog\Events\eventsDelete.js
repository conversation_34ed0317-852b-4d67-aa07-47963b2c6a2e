const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildScheduledEventDelete,
    async execute(guildScheduledEvent, client) {
        try {
            console.log(`🗑️ Scheduled event deleted: ${guildScheduledEvent.name} in ${guildScheduledEvent.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(guildScheduledEvent.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EVENTS_DELETE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EVENTS_DELETE',
                user: 'System',
                userId: null,
                action: 'Sự kiện được xóa',
                details: `Sự kiện **${guildScheduledEvent.name}** đ<PERSON> được xóa`,
                target: guildScheduledEvent.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the event from audit logs
            try {
                const auditLogs = await guildScheduledEvent.guild.fetchAuditLogs({
                    type: 102, // GUILD_SCHEDULED_EVENT_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === guildScheduledEvent.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Sự kiện được xóa bởi ${auditEntry.executor.tag}`;
                }
            } catch (error) {
                console.log('Could not fetch audit logs for event delete');
            }
            
            // Add to database
            await client.db.addAuditLog(guildScheduledEvent.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Sự kiện được xóa',
                `Vừa có một sự kiện được xóa khỏi server`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            // Get entity type names
            const entityTypes = {
                1: 'Stage Instance',
                2: 'Voice Channel',
                3: 'External Location'
            };
            
            // Get status names
            const statusTypes = {
                1: 'Scheduled',
                2: 'Active',
                3: 'Completed',
                4: 'Cancelled'
            };
            
            embed.addFields([
                {
                    name: '> Tên sự kiện',
                    value: `- ${guildScheduledEvent.name}`,
                    inline: true
                },
                {
                    name: '> ID sự kiện',
                    value: `- ${guildScheduledEvent.id}`,
                    inline: true
                },
                {
                    name: '> Loại sự kiện',
                    value: `- ${entityTypes[guildScheduledEvent.entityType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Trạng thái trước khi xóa',
                    value: `- ${statusTypes[guildScheduledEvent.status] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian xóa',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add event timing that was scheduled
            if (guildScheduledEvent.scheduledStartTimestamp) {
                embed.addFields([
                    {
                        name: '> Đã lên lịch bắt đầu',
                        value: `- <t:${Math.floor(guildScheduledEvent.scheduledStartTimestamp / 1000)}:F>`,
                        inline: true
                    }
                ]);
            }
            
            if (guildScheduledEvent.scheduledEndTimestamp) {
                embed.addFields({
                    name: '> Đã lên lịch kết thúc',
                    value: `- <t:${Math.floor(guildScheduledEvent.scheduledEndTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add location/channel info that was set
            if (guildScheduledEvent.channel) {
                embed.addFields({
                    name: '> Kênh đã thiết lập',
                    value: `- ${guildScheduledEvent.channel.name}`,
                    inline: true
                });
            } else if (guildScheduledEvent.entityMetadata?.location) {
                embed.addFields({
                    name: '> Địa điểm đã thiết lập',
                    value: `- ${guildScheduledEvent.entityMetadata.location}`,
                    inline: true
                });
            }
            
            // Add user count if available
            if (guildScheduledEvent.userCount !== undefined) {
                embed.addFields({
                    name: '> Số người quan tâm',
                    value: `- ${guildScheduledEvent.userCount} người`,
                    inline: true
                });
            }
            
            // Add description if it was set
            if (guildScheduledEvent.description) {
                const description = guildScheduledEvent.description.length > 300 
                    ? guildScheduledEvent.description.substring(0, 300) + '...'
                    : guildScheduledEvent.description;
                
                embed.addFields({
                    name: '> Mô tả đã có',
                    value: `\`\`\`${description}\`\`\``,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${guildScheduledEvent.guild.name} • Event Delete`,
                iconURL: guildScheduledEvent.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Event delete logged: ${guildScheduledEvent.name}`);
            
        } catch (error) {
            console.error('Error in eventsDelete audit log:', error);
        }
    }
};
