const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildMemberUpdate,
    async execute(oldMember, newMember, client) {
        try {
            // Only handle role removals
            const oldRoles = oldMember.roles.cache;
            const newRoles = newMember.roles.cache;
            const removedRoles = oldRoles.filter(role => !newRoles.has(role.id));
            
            if (removedRoles.size === 0) return;
            
            console.log(`➖ User roles removed: ${newMember.user.tag} in ${newMember.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newMember.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'USER_ROLES_REMOVE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'USER_ROLES_REMOVE',
                user: 'System',
                userId: null,
                action: 'Roles được xóa khỏi thành viên',
                details: `**${removedRoles.size}** roles đã được xóa khỏi **${newMember.user.tag}**`,
                target: newMember.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who removed the roles from audit logs
            try {
                const auditLogs = await newMember.guild.fetchAuditLogs({
                    type: 25, // MEMBER_ROLE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newMember.user.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Roles được xóa bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for role remove');
            }
            
            // Add to database
            await client.db.addAuditLog(newMember.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '➖ Roles được xóa',
                `Vừa có roles được xóa khỏi thành viên trong server`
            );
            
            embed.setColor(0xe74c3c); // Red for remove
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${newMember.user.tag} (${newMember.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newMember.user.id}`,
                    inline: true
                },
                {
                    name: '> Người thực hiện',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add roles that were removed
            const removedRolesList = removedRoles.map(role => `~~${role.name}~~`).join(', ');
            embed.addFields({
                name: '> ❌ Roles được xóa',
                value: removedRolesList.length > 1000 ? removedRolesList.substring(0, 1000) + '...' : removedRolesList,
                inline: false
            });
            
            embed.addFields([
                {
                    name: '> 📊 Số roles được xóa',
                    value: `- ${removedRoles.size} roles`,
                    inline: true
                },
                {
                    name: '> 📊 Tổng roles còn lại',
                    value: `- ${newRoles.size - 1} roles`, // -1 for @everyone
                    inline: true
                }
            ]);
            
            // Check for important roles that were removed
            const importantPerms = ['Administrator', 'ManageGuild', 'ManageRoles', 'ManageChannels', 'BanMembers', 'KickMembers', 'ModerateMembers'];
            const importantRoles = removedRoles.filter(role => 
                role.permissions.toArray().some(perm => importantPerms.includes(perm))
            );
            
            if (importantRoles.size > 0) {
                embed.addFields({
                    name: '⚠️ Lưu ý',
                    value: `- Đã xóa roles quan trọng: ${importantRoles.map(r => r.name).join(', ')}`,
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set user avatar as thumbnail
            if (newMember.user.displayAvatarURL()) {
                embed.setThumbnail(newMember.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newMember.guild.name} • User Roles Remove`,
                iconURL: newMember.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ User roles remove logged: ${newMember.user.tag}`);
            
        } catch (error) {
            console.error('Error in userRolesRemove audit log:', error);
        }
    }
};
