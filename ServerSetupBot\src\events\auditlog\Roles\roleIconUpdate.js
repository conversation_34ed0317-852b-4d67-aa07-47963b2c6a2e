const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildRoleUpdate,
    async execute(oldRole, newRole, client) {
        try {
            // Only handle icon changes
            if (oldRole.icon === newRole.icon) return;
            
            console.log(`🖼️ Role icon updated: ${newRole.name} in ${newRole.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newRole.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'ROLE_ICON_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'ROLE_ICON_UPDATE',
                user: 'System',
                userId: null,
                action: 'Icon role được cập nhật',
                details: `Icon của role **${newRole.name}** đ<PERSON> được cập nhật`,
                target: newRole.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the role icon from audit logs
            try {
                const auditLogs = await newRole.guild.fetchAuditLogs({
                    type: 31, // ROLE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newRole.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Icon role được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for role icon update');
            }
            
            // Add to database
            await client.db.addAuditLog(newRole.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🖼️ Icon role được cập nhật',
                `Vừa có icon role được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Role',
                    value: `- ${newRole} (${newRole.name})`,
                    inline: false
                },
                {
                    name: '> ID role',
                    value: `- ${newRole.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add icon information
            const oldIconURL = oldRole.iconURL({ dynamic: true, size: 256 });
            const newIconURL = newRole.iconURL({ dynamic: true, size: 256 });
            
            embed.addFields([
                {
                    name: '> Icon cũ',
                    value: oldIconURL ? `- [Xem icon cũ](${oldIconURL})` : '- Không có icon',
                    inline: true
                },
                {
                    name: '> Icon mới',
                    value: newIconURL ? `- [Xem icon mới](${newIconURL})` : '- Không có icon',
                    inline: true
                }
            ]);
            
            // Determine change type
            if (newRole.icon && oldRole.icon) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Thay đổi icon role`,
                    inline: true
                });
                embed.setColor(0x3498db); // Blue for change
            } else if (newRole.icon && !oldRole.icon) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Thiết lập icon role mới`,
                    inline: true
                });
                embed.setColor(0x2ecc71); // Green for new icon
            } else if (!newRole.icon && oldRole.icon) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Xóa icon role`,
                    inline: true
                });
                embed.setColor(0xe74c3c); // Red for removed icon
            }
            
            // Add role information
            embed.addFields([
                {
                    name: '> Số thành viên có role',
                    value: `- ${newRole.members.size} thành viên`,
                    inline: true
                },
                {
                    name: '> Màu role',
                    value: `- ${newRole.hexColor}`,
                    inline: true
                },
                {
                    name: '> Vị trí role',
                    value: `- ${newRole.position}`,
                    inline: true
                },
                {
                    name: '> Hiển thị riêng',
                    value: `- ${newRole.hoist ? 'Có' : 'Không'}`,
                    inline: true
                },
                {
                    name: '> Có thể mention',
                    value: `- ${newRole.mentionable ? 'Có' : 'Không'}`,
                    inline: true
                }
            ]);
            
            // Add impact information
            if (newRole.icon && !oldRole.icon) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Role giờ đây có icon riêng',
                        '• Dễ nhận biết trong danh sách thành viên',
                        '• Tăng tính chuyên nghiệp cho server',
                        '• Icon hiển thị bên cạnh tên role'
                    ].join('\n'),
                    inline: false
                });
            } else if (!newRole.icon && oldRole.icon) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Role không còn icon riêng',
                        '• Hiển thị như role thông thường',
                        '• Khó phân biệt với các role khác',
                        '• Chỉ dựa vào màu và tên để nhận biết'
                    ].join('\n'),
                    inline: false
                });
            } else if (newRole.icon && oldRole.icon) {
                embed.addFields({
                    name: '> 🔄 Tác động',
                    value: [
                        '• Icon role đã được thay đổi',
                        '• Giao diện mới cho role',
                        '• Có thể cần thời gian để thành viên quen',
                        '• Cập nhật hình ảnh thương hiệu server'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add requirements note
            embed.addFields({
                name: '> 📝 Lưu ý',
                value: [
                    '• Icon role chỉ hiển thị với Discord Nitro Server Boost Level 2+',
                    '• Kích thước tối đa: 256KB',
                    '• Định dạng hỗ trợ: PNG, JPG, GIF'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set new icon as thumbnail if available
            if (newIconURL) {
                embed.setThumbnail(newIconURL);
            } else if (oldIconURL) {
                embed.setThumbnail(oldIconURL);
            }
            
            // Set role color as embed color if it has one
            if (newRole.color !== 0) {
                embed.setColor(newRole.color);
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newRole.guild.name} • Role Icon Update`,
                iconURL: newRole.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Role icon update logged: ${newRole.name}`);
            
        } catch (error) {
            console.error('Error in roleIconUpdate audit log:', error);
        }
    }
};
