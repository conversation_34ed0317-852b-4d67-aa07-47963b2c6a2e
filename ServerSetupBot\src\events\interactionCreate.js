const { Events, PermissionFlagsBits } = require("discord.js");
const { createErrorEmbed } = require("../utils/embedBuilder.js");

module.exports = {
  name: Events.InteractionCreate,
  async execute(interaction, client) {
    // X<PERSON> lý slash commands
    if (interaction.isChatInputCommand()) {
      const command = client.commands.get(interaction.commandName);

      if (!command) {
        console.error(`❌ Không tìm thấy lệnh ${interaction.commandName}.`);
        return;
      }

      try {
        // Kiểm tra quyền admin nếu lệnh yêu cầu
        if (
          command.adminOnly &&
          !interaction.member.permissions.has(PermissionFlagsBits.Administrator)
        ) {
          const errorEmbed = createErrorEmbed(
            "Không có quyền!",
            "Bạn cần có quyền **Quản trị viên** để sử dụng lệnh này!"
          );
          return await interaction.reply({
            embeds: [errorEmbed],
            ephemeral: true,
          });
        }

        // <PERSON><PERSON><PERSON> tra quyền manage server nếu lệnh yêu cầu
        if (
          command.manageServer &&
          !interaction.member.permissions.has(PermissionFlagsBits.ManageGuild)
        ) {
          const errorEmbed = createErrorEmbed(
            "Không có quyền!",
            "Bạn cần có quyền **Quản lý máy chủ** để sử dụng lệnh này!"
          );
          return await interaction.reply({
            embeds: [errorEmbed],
            ephemeral: true,
          });
        }

        // Thực thi lệnh
        await command.execute(interaction, client);
      } catch (error) {
        console.error(
          `❌ Lỗi khi thực thi lệnh ${interaction.commandName}:`,
          error
        );

        const errorEmbed = createErrorEmbed(
          "Lỗi hệ thống!",
          "Đã xảy ra lỗi khi thực hiện lệnh. Vui lòng thử lại sau!"
        );

        if (interaction.replied || interaction.deferred) {
          await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
        } else {
          await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
      }
    }

    // Xử lý button interactions
    else if (interaction.isButton()) {
      try {
        console.log(`🔘 Button clicked: ${interaction.customId}`);
        const [action, ...params] = interaction.customId.split("_");
        console.log(`🎯 Action: ${action}, Params:`, params);

        // Import handler tương ứng
        const handlerPath = `../handlers/buttons/${action}Handler.js`;
        try {
          const handler = require(handlerPath);
          console.log(`✅ Handler found for: ${action}`);
          await handler.execute(interaction, client, params);
        } catch (err) {
          console.log(
            `⚠️ Không tìm thấy handler cho button: ${action}`,
            err.message
          );
        }
      } catch (error) {
        console.error("❌ Lỗi khi xử lý button interaction:", error);

        const errorEmbed = createErrorEmbed(
          "Lỗi hệ thống!",
          "Đã xảy ra lỗi khi xử lý tương tác. Vui lòng thử lại!"
        );

        if (interaction.replied || interaction.deferred) {
          await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
        } else {
          await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
      }
    }

    // Xử lý select menu interactions
    else if (interaction.isStringSelectMenu()) {
      try {
        const [action, ...params] = interaction.customId.split("_");

        // Import handler tương ứng
        const handlerPath = `../handlers/selectMenus/${action}Handler.js`;
        try {
          const handler = require(handlerPath);
          await handler.execute(interaction, client, params);
        } catch (err) {
          console.log(`⚠️ Không tìm thấy handler cho select menu: ${action}`);
        }
      } catch (error) {
        console.error("❌ Lỗi khi xử lý select menu interaction:", error);

        const errorEmbed = createErrorEmbed(
          "Lỗi hệ thống!",
          "Đã xảy ra lỗi khi xử lý tương tác. Vui lòng thử lại!"
        );

        if (interaction.replied || interaction.deferred) {
          await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
        } else {
          await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
      }
    }

    // Xử lý modal interactions
    else if (interaction.isModalSubmit()) {
      try {
        const [action, ...params] = interaction.customId.split("_");

        // Import handler tương ứng
        const handlerPath = `../handlers/modals/${action}Handler.js`;
        try {
          const handler = require(handlerPath);
          await handler.execute(interaction, client, params);
        } catch (err) {
          console.log(`⚠️ Không tìm thấy handler cho modal: ${action}`);
        }
      } catch (error) {
        console.error("❌ Lỗi khi xử lý modal interaction:", error);

        const errorEmbed = createErrorEmbed(
          "Lỗi hệ thống!",
          "Đã xảy ra lỗi khi xử lý form. Vui lòng thử lại!"
        );

        if (interaction.replied || interaction.deferred) {
          await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
        } else {
          await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
      }
    }
  },
};
