const Command = require("../../structures/Command");
const Discord = require("discord.js");
const fs = require("fs");

module.exports = class Templates extends Command {
  constructor(client) {
    super(client, {
      name: "templates",
      description: client.cmdConfig.templates.description,
      usage: client.cmdConfig.templates.usage,
      permissions: client.cmdConfig.templates.permissions,
      aliases: client.cmdConfig.templates.aliases,
      category: "setups",
      listed: client.cmdConfig.templates.enabled,
      slash: true,
    });
  }

  async run(message, args) {
    let content = "";
    let dirFiles = fs.readdirSync("./configs/templates").filter((f) => f.endsWith(".yml"));
    
    for(const file of dirFiles) {
      let reqFile = this.client.template(file.replace(".yml", ""));
      
      content += this.client.config.setup.list_format.replace("<name>", file.slice(0, -4))
        .replace("<channels>", reqFile.channels.length)
        .replace("<role>", reqFile.roles.length)
        .replace("<emojis>", reqFile.emojis.length);
    }
    
    message.channel.send({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, content, this.client.embeds.general_color)] });
  }
  async slashRun(interaction, args) {
    let content = "";
    let dirFiles = fs.readdirSync("./configs/templates").filter((f) => f.endsWith(".yml"));
    
    for(const file of dirFiles) {
      let reqFile = this.client.template(file.replace(".yml", ""));
      
      content += this.client.config.setup.list_format.replace("<name>", file.slice(0, -4))
        .replace("<channels>", reqFile.channels.length)
        .replace("<role>", reqFile.roles.length)
        .replace("<emojis>", reqFile.emojis.length);
    }
    
    interaction.reply({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, content, this.client.embeds.general_color)], ephemeral: this.client.cmdConfig.templates.ephemeral });
  }
};
