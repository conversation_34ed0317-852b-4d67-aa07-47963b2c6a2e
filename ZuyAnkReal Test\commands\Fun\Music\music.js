(function(_0x27559f,_0x4c7b2e){const _0x107e38=_0x27559f();function _0x586477(_0x2c10e2,_0x4b882a){return _0x5b9f(_0x2c10e2-0xb,_0x4b882a);}while(!![]){try{const _0x36c45d=parseInt(_0x586477(0x224,0x27c))/0x1*(parseInt(_0x586477(0x329,0x2ce))/0x2)+-parseInt(_0x586477(0x2a5,0x324))/0x3*(-parseInt(_0x586477(0x23d,0x2cf))/0x4)+-parseInt(_0x586477(0x2ea,0x293))/0x5+-parseInt(_0x586477(0x323,0x37c))/0x6+-parseInt(_0x586477(0x25b,0x1f2))/0x7*(parseInt(_0x586477(0x2bf,0x2ca))/0x8)+parseInt(_0x586477(0x258,0x2ed))/0x9+-parseInt(_0x586477(0x20c,0x27a))/0xa;if(_0x36c45d===_0x4c7b2e)break;else _0x107e38['push'](_0x107e38['shift']());}catch(_0x1660ab){_0x107e38['push'](_0x107e38['shift']());}}}(_0x3bd7,0x8d2f3));const {SlashCommandBuilder}=require(_0x162012(-0x98,-0x106)),{EmbedBuilder,ActionRowBuilder,ButtonBuilder,ButtonStyle,AttachmentBuilder}=require(_0x162012(-0x14a,-0x1af)),fs=require('fs'),yaml=require(_0x162012(-0x200,-0x173)),config=yaml['load'](fs['readFileSync']('././config.yml','utf8')),lang=yaml[_0x162012(-0x11a,-0x179)](fs[_0x162012(-0x25e,-0x1c4)](_0x162012(-0x161,-0x13b),_0x162012(-0x170,-0x152))),sharp=require(_0x162012(-0xf1,-0xcd)),axios=require(_0x162012(-0xd6,-0xca)),{useMainPlayer,QueryType,useHistory,QueueRepeatMode}=require(_0x162012(-0x171,-0x160)),{createCanvas,loadImage}=require(_0x162012(-0x56,-0xa2)),getColors=require(_0x162012(-0x120,-0xf4)),moment=require('moment');async function autocompleteRun(_0x1d4b74){function _0x42ca84(_0x55e35e,_0x2857e7){return _0x162012(_0x55e35e,_0x2857e7-0x19d);}try{const _0xe2ec8b=useMainPlayer(),_0x4ab6e2=_0x1d4b74[_0x42ca84(0xc4,0x81)][_0x42ca84(0x26,0x6)](_0x42ca84(0xfd,0x74),!![]);let _0x405b6b=[];const _0x53442e=await _0xe2ec8b[_0x42ca84(0x74,0xe6)](_0x4ab6e2,{'requestedBy':_0x1d4b74[_0x42ca84(0x16,0x4)]});if(_0x53442e[_0x42ca84(0xb1,0xc3)]&&_0x53442e[_0x42ca84(0x7b,0xc3)][_0x42ca84(0x45,-0x22)]){let _0x2e6853='';const _0x4d4cfc=_0x4ab6e2[_0x42ca84(0x51,0x31)](/artist:([^\s]+)/);_0x4d4cfc&&_0x4d4cfc[0x1]&&(_0x2e6853=_0x4d4cfc[0x1]['toLowerCase']());_0x53442e['_data'][_0x42ca84(-0x18,-0x22)]['forEach'](_0x59c458=>{const _0xdd2763=_0x59c458['title'][_0xd1e9d(0x59f,0x59d)](),_0x1a74d1=_0x59c458[_0xd1e9d(0x5d1,0x5dc)]?_0x59c458[_0xd1e9d(0x5d1,0x5fa)]['toLowerCase']():'';function _0xd1e9d(_0x4fd5b9,_0x5a5bdd){return _0x42ca84(_0x5a5bdd,_0x4fd5b9-0x571);}if(_0xdd2763['includes'](_0x4ab6e2[_0xd1e9d(0x59f,0x632)]())&&(!_0x2e6853||_0x1a74d1[_0xd1e9d(0x65a,0x6c1)](_0x2e6853))){const _0xa4529c=_0x59c458['title']+_0xd1e9d(0x5f4,0x668)+_0x59c458[_0xd1e9d(0x5ea,0x582)];_0xa4529c[_0xd1e9d(0x564,0x586)]<=0x64&&_0x405b6b['push'](_0xa4529c);}});const _0x3a3da0=_0x405b6b[_0x42ca84(0x7f,0xdb)](0x0,0x5);await _0x1d4b74[_0x42ca84(-0x2a,-0x1b)](_0x3a3da0[_0x42ca84(0xb7,0x5e)](_0x1810b4=>({'name':_0x1810b4,'value':_0x1810b4})));}else{}}catch(_0x546ea9){console[_0x42ca84(0x10e,0x7a)](_0x546ea9);}}module[_0x162012(-0xdf,-0x166)]={'data':new SlashCommandBuilder()[_0x162012(-0xa7,-0x12b)](_0x162012(-0xe4,-0xf6))[_0x162012(-0x1b1,-0x19c)]('Music\x20related\x20commands')[_0x162012(-0x156,-0x153)](_0xe9eda4=>_0xe9eda4['setName'](_0x162012(-0x78,-0xce))[_0x162012(-0x12e,-0x19c)](_0x162012(-0x168,-0x18a))[_0x162012(-0x154,-0x16d)](_0x39ff60=>_0x39ff60[_0x162012(-0x96,-0x12b)]('query')['setDescription'](_0x162012(-0xf5,-0xdb))[_0x162012(-0x144,-0x10d)](!![])['setAutocomplete'](!![])))['addSubcommand'](_0x3d2c96=>_0x3d2c96[_0x162012(-0x123,-0x12b)](_0x162012(-0x213,-0x198))[_0x162012(-0x1ae,-0x19c)](_0x162012(-0x198,-0x16a)))[_0x162012(-0x148,-0x153)](_0x17bc5c=>_0x17bc5c[_0x162012(-0xc5,-0x12b)]('resume')[_0x162012(-0x1be,-0x19c)](_0x162012(-0x1bb,-0x19e)))[_0x162012(-0x1d0,-0x153)](_0x92b62b=>_0x92b62b[_0x162012(-0x1a8,-0x12b)]('skip')['setDescription']('Skip\x20the\x20current\x20song'))[_0x162012(-0x11c,-0x153)](_0x181dd4=>_0x181dd4['setName'](_0x162012(-0x57,-0xab))['setDescription'](_0x162012(-0x17a,-0x113)))[_0x162012(-0x152,-0x153)](_0x132181=>_0x132181[_0x162012(-0x1b4,-0x12b)]('loop')['setDescription'](_0x162012(-0x100,-0x168))[_0x162012(-0x15d,-0x16d)](_0x531ace=>_0x531ace[_0x162012(-0xf8,-0x12b)](_0x162012(-0x146,-0xd7))['setDescription']('Select\x20loop\x20mode')['setRequired'](!![])[_0x162012(-0xba,-0xd2)]({'name':'Off','value':_0x162012(-0x119,-0x14a)},{'name':_0x162012(-0x52,-0xbb),'value':_0x162012(-0x1be,-0x175)},{'name':_0x162012(-0x135,-0x142),'value':_0x162012(-0x66,-0xb6)},{'name':'Autoplay','value':_0x162012(-0x135,-0xe3)})))[_0x162012(-0x104,-0x153)](_0x4e86d=>_0x4e86d[_0x162012(-0x131,-0x12b)]('queue')['setDescription'](_0x162012(-0x114,-0x18c)))[_0x162012(-0x158,-0x153)](_0x48332f=>_0x48332f[_0x162012(-0x11f,-0x12b)](_0x162012(-0x1be,-0x1b5))[_0x162012(-0x1fc,-0x19c)]('Information\x20about\x20the\x20current\x20song'))[_0x162012(-0x122,-0x153)](_0x9dc920=>_0x9dc920[_0x162012(-0x119,-0x12b)]('clear')[_0x162012(-0x134,-0x19c)]('Clear\x20the\x20queue'))[_0x162012(-0x12d,-0x153)](_0x282b0f=>_0x282b0f[_0x162012(-0x9e,-0x12b)](_0x162012(-0x78,-0xc4))[_0x162012(-0x1db,-0x19c)](_0x162012(-0x112,-0x190)))[_0x162012(-0xbf,-0x153)](_0x5006ca=>_0x5006ca[_0x162012(-0xf1,-0x12b)]('seek')[_0x162012(-0x147,-0x19c)]('Skip\x20to\x20a\x20certain\x20part\x20of\x20the\x20current\x20song')[_0x162012(-0x154,-0x16d)](_0x3f3c78=>_0x3f3c78['setName'](_0x162012(-0x1c6,-0x193))[_0x162012(-0x19a,-0x19c)](_0x162012(-0xef,-0xe7))[_0x162012(-0x13b,-0x10d)](!![])))[_0x162012(-0x1e1,-0x153)](_0x1b8301=>_0x1b8301[_0x162012(-0xd9,-0x12b)]('volume')[_0x162012(-0x150,-0x19c)](_0x162012(-0x171,-0xdd))['addIntegerOption'](_0x12298e=>_0x12298e[_0x162012(-0xe1,-0x12b)](_0x162012(-0x8b,-0xf5))[_0x162012(-0x220,-0x19c)]('Volume\x20level\x20(0-100)')[_0x162012(-0x165,-0x10d)](!![])[_0x162012(-0x12d,-0x14d)](0x0)[_0x162012(-0x15a,-0x1ce)](0x64)))[_0x162012(-0x1d4,-0x153)](_0x17fa63=>_0x17fa63[_0x162012(-0x1b3,-0x12b)](_0x162012(-0xae,-0x10a))[_0x162012(-0x20e,-0x19c)]('Move\x20a\x20song\x20to\x20a\x20specific\x20postion\x20in\x20queue')[_0x162012(-0x1fe,-0x1c0)](_0xdf7907=>_0xdf7907['setName'](_0x162012(-0x107,-0x1a4))[_0x162012(-0x1fb,-0x19c)](_0x162012(-0xd4,-0x102))[_0x162012(-0xb6,-0x10d)](!![]))[_0x162012(-0x240,-0x1c0)](_0x51772d=>_0x51772d[_0x162012(-0x116,-0x12b)](_0x162012(-0x1d5,-0x13c))[_0x162012(-0x18d,-0x19c)]('The\x20new\x20position\x20of\x20the\x20song\x20to\x20move')[_0x162012(-0x187,-0x10d)](!![])))[_0x162012(-0xfb,-0x153)](_0x54c778=>_0x54c778[_0x162012(-0x121,-0x12b)](_0x162012(-0x13a,-0x10b))['setDescription'](_0x162012(-0x9d,-0xfc))),'category':'Fun',async 'execute'(_0x17570e,_0x576790){const _0xa49ce9=useMainPlayer();function _0x5c57d3(_0x2695b6,_0x307143){return _0x162012(_0x307143,_0x2695b6-0x58e);}const _0x4bd5d6=config[_0x5c57d3(0x48f,0x45b)][_0x5c57d3(0x3d7,0x3db)],_0xf4f2a=config[_0x5c57d3(0x48f,0x4b7)][_0x5c57d3(0x3cc,0x334)],_0x48b19f=_0x17570e['member'][_0x5c57d3(0x43e,0x462)][_0x5c57d3(0x4ce,0x4a7)]['map'](_0x4c7102=>_0x4c7102['id']);if(!_0x17570e[_0x5c57d3(0x3f5,0x3d6)][_0x5c57d3(0x414,0x37e)]['channel']){await _0x17570e[_0x5c57d3(0x471,0x4c5)]({'content':lang[_0x5c57d3(0x418,0x396)][_0x5c57d3(0x482,0x4dd)],'ephemeral':!![]});return;}if(config[_0x5c57d3(0x48f,0x506)]['EnableWhitelist']){const _0x3770ca=_0x48b19f[_0x5c57d3(0x3f3,0x367)](_0x361d87=>_0xf4f2a[_0x5c57d3(0x4da,0x477)](_0x361d87));if(_0x3770ca){await _0x17570e[_0x5c57d3(0x471,0x3db)]({'content':lang['Music'][_0x5c57d3(0x42d,0x407)],'ephemeral':!![]});return;}const _0x282cee=_0x48b19f[_0x5c57d3(0x3f3,0x3f6)](_0xf3ea75=>_0x4bd5d6[_0x5c57d3(0x4da,0x45d)](_0xf3ea75));if(!_0x282cee){await _0x17570e[_0x5c57d3(0x471,0x3f8)]({'content':lang['Music'][_0x5c57d3(0x42d,0x4c9)],'ephemeral':!![]});return;}}if(_0x17570e[_0x5c57d3(0x4e9,0x517)]['members']['me'][_0x5c57d3(0x414,0x45a)]['channel']&&_0x17570e['member'][_0x5c57d3(0x414,0x3bf)][_0x5c57d3(0x455,0x436)]['id']!==_0x17570e[_0x5c57d3(0x4e9,0x52d)]['members']['me'][_0x5c57d3(0x414,0x3fe)][_0x5c57d3(0x455,0x425)]['id']){await _0x17570e[_0x5c57d3(0x442,0x403)]({'content':lang[_0x5c57d3(0x418,0x3b4)][_0x5c57d3(0x4f3,0x4e5)],'ephemeral':!![]});return;}const _0x20cffd=_0x17570e[_0x5c57d3(0x3f5,0x45f)][_0x5c57d3(0x414,0x3ff)][_0x5c57d3(0x455,0x3fd)];if(!_0x20cffd){await _0x17570e[_0x5c57d3(0x442,0x3ee)]({'content':lang['Music']['NotInVoiceChannel'],'ephemeral':!![]});return;}const _0x37e7b5=_0x17570e[_0x5c57d3(0x472,0x4b1)]['getSubcommand']();switch(_0x37e7b5){case _0x5c57d3(0x4e3,0x480):await back(_0x17570e,_0xa49ce9);break;case _0x5c57d3(0x4c0,0x4a6):await play(_0x17570e,_0xa49ce9,_0x20cffd);break;case'pause':await pause(_0x17570e,_0xa49ce9,_0x20cffd);break;case _0x5c57d3(0x3bf,0x3c5):await resume(_0x17570e,_0xa49ce9,_0x20cffd);break;case _0x5c57d3(0x443,0x404):await skip(_0x17570e,_0xa49ce9);break;case _0x5c57d3(0x3be,0x417):await loop(_0x17570e,_0xa49ce9,_0x576790);break;case'playingnow':await nowplaying(_0x17570e,_0xa49ce9,_0x576790);break;case'seek':await seek(_0x17570e,_0xa49ce9,_0x576790);break;case _0x5c57d3(0x497,0x44a):await clear(_0x17570e,_0xa49ce9,_0x20cffd);break;case'queue':await queue(_0x17570e,_0xa49ce9);break;case'move':await move(_0x17570e,_0xa49ce9);break;case'filters':await filters(_0x17570e,_0xa49ce9);break;case _0x5c57d3(0x483,0x506):await stop(_0x17570e,_0xa49ce9);break;case _0x5c57d3(0x3e5,0x44e):await setVolume(_0x17570e,_0xa49ce9);break;default:await _0x17570e[_0x5c57d3(0x442,0x3c0)]({'content':_0x5c57d3(0x45f,0x4bf),'ephemeral':!![]});}},'autocompleteRun':autocompleteRun};function _0x3bd7(){const _0x2a8024=['YouTube','Resume\x20the\x20current\x20song','{newPosition}','setDescription','some','reduce','member','pause','getString','QueryNotFound','treble','shadowBlur','time','2113880AXXLlf','queue_next_','Shows\x20the\x20filters\x20control\x20embed','{volume}','value','Style','List\x20the\x20songs\x20next\x20in\x20queue','arcTo','Play\x20a\x20song','ffmpeg','Filter','Emojis','left','addColorStop','```ansi\x0a','fillRect','thumbnail','Error\x20generating\x20Now\x20Playing\x20image:','Buttons','setCustomId','ProgressBar','setFooter','min','Color','voice','load','Unknown\x20Platform','8303652ACwuPu','Music','TRACK','217455OUgwLM','js-yaml','catch','{state}','NowPlaying','toLowerCase','total','addStringOption','match','#000000','Pause\x20the\x20current\x20song','fillStyle','Toggle\x20loop\x20for\x20the\x20current\x20song\x20or\x20queue','No\x20track\x20is\x20currently\x20playing.','exports','LeaveOnEmptyTimer','Fields','Next','Now\x20Playing','NoPermission','discord-player','Height','delete','Paused','reverse','getFiltersDisabled','setEmoji','NothingToSkip','textAlign','NoMusicPlaying','{track}','push','durationFormatted','addSubcommand','utf8','setImage','roles','Time','\x1b[2;31m','setMinValue','editReply','skip','OFF','http:','nowplaying.png','SoundCloud','{title}','...','get','soundcloud','Queue','drawImage','clip','map','shadowColor','author','moveposition','././lang.yml','arraybuffer','channel','ytsearch','earrape','save','playlist','estimatedDuration','connect','nightcore','applemusic','GradientStart','Invalid\x20command','uppercase','ThumbnailPlaceholder','Embed','setName','3NvqggL','query','isEmpty','nodes','Header','duration','source','log','setColor','AlreadyResumed','deferReply','Accent','closePath','reply','options','currentTrack','\x20-\x20','from','Author','{time}','filter','replace','Text','Go\x20back\x20a\x20song','Icon','previous','8QdvzSl','SPOTIFY_PLAYLIST','isPlaying','setRequired','NotInVoiceChannel','stop','move','spotify.com','NoMusicInQueue','Title','@discordjs/builders','Start','charAt','lofi','The\x20position\x20of\x20the\x20song\x20to\x20move','getTimestamp','AppleMusic','MusicCommand','APPLE_MUSIC','EmbedError','Stop\x20the\x20current\x20song\x20and\x20clear\x20the\x20queue','AlreadyPaused','WentBackATrack','Image','Error','clear','music','level','get-image-colors','`This\x20command\x20is\x20Experimental!`','GradientEnd','setThumbnail','music.apple.com','Width','Canvas','seek','current','isPaused','pow','setVolume','queue_last_page','Format:\x20HH:MM:SS','png','1733595iEAynC','YOUTUBE','AUTOPLAY','BackgroundColor','setRepeatMode','Error\x20with\x20music\x20queue\x20command:','Disabled','```\x0a','Adjust\x20the\x20volume\x20of\x20the\x20music','Thumbnail','The\x20song\x20to\x20play','_data','Looping','Back','mode','Volume','\x20by\x20','resize','queue_first_page','addChoices','Move','Resumed','Stopped','play','sharp','fill','toggle_filter_','axios','protocol','setLabel','measureText','arc','setStyle','filters','setDisabled','slice','LeaveOnEmpty','cache','spotify','error','strokeStyle','vaporwave','Track','getInteger','replied','amsearch','search','QUEUE','overlay','includes','beginPath','Footer','spsearch','```','toBuffer','restore','Added\x20playlist:\x20','2864604iTIAwx','back','Font','Platform','max','Off','892902NXRAkp','guild','data','user','canvas','moveTo','title','SOUNDCLOUD','setTitle','font','width','NotInSameVoiceChannel','Description','forEach','createLinearGradient','Success','End','loop','resume','setMaxValue','protocols','height','node','BackgroundGradient','addTrack','addFields','BorderRadius','right','QueueCleared','readFileSync','4640400RyVxAd','BlacklistRoles','lineWidth','addIntegerOption','tracks','borderRadius','Emoji','Spotify','Number','soundcloud.com','globalCompositeOperation','respond','WhitelistRoles','create','playingnow','\x1b[0m','scsearch','toUpperCase','splice','\x20with\x20','discord.js','queue_previous_','InvalidPosition','binary','1OCjQAA','length','volume','AddingTrack','\x1b[2;34m','message','Apple\x20Music','songtomove','label','Autoplay','Filters','\x20tracks\x20to\x20the\x20queue.'];_0x3bd7=function(){return _0x2a8024;};return _0x3bd7();}async function setVolume(_0x1150b2,_0x3f1654){function _0x4c30cd(_0x508923,_0x589224){return _0x162012(_0x508923,_0x589224-0x1b6);}await _0x1150b2['deferReply']();try{const _0x58b6d8=_0x3f1654[_0x4c30cd(0xe7,0x8f)][_0x4c30cd(0xba,0x72)](_0x1150b2['guild']['id']);if(!_0x58b6d8||!_0x58b6d8[_0x4c30cd(0xa5,0x9b)]){await _0x1150b2['editReply']({'content':lang['Music'][_0x4c30cd(0xa9,0x5f)],'ephemeral':!![]});return;}const _0x1baa81=_0x1150b2['options'][_0x4c30cd(0x147,0xfc)]('level'),_0x14282e=_0x58b6d8[_0x4c30cd(0x2f,-0x15)][_0x4c30cd(0x125,0xcd)](_0x1baa81);_0x14282e?await _0x1150b2['editReply']({'content':lang[_0x4c30cd(0x97,0x40)][_0x4c30cd(0xf2,0xe0)][_0x4c30cd(0x110,0x11f)][_0x4c30cd(0xa4,0xa1)](_0x4c30cd(-0x51,0x27),_0x1baa81),'ephemeral':![]}):await _0x1150b2['editReply']({'content':lang[_0x4c30cd(0x60,0x40)][_0x4c30cd(0xb4,0xe0)]['Error'],'ephemeral':!![]});}catch(_0x3530df){console[_0x4c30cd(0x17a,0xf8)](_0x3530df),await _0x1150b2[_0x4c30cd(0x77,0x6a)]({'content':lang[_0x4c30cd(0x40,0x40)][_0x4c30cd(0x111,0xbe)],'ephemeral':!![]});}}async function play(_0x2cc8d0,_0x2d4904,_0x4a35b5){await _0x2cc8d0['deferReply']();const _0x588e35=_0x2cc8d0[_0x731088(0x513,0x59d)]['getString'](_0x731088(0x5ac,0x590),!![]),_0x5dab44=_0x588e35[_0x731088(0x60d,0x605)](_0x731088(0x5af,0x5c9)),_0xb910cb=_0x588e35[_0x731088(0x5f3,0x605)](_0x731088(0x585,0x5b0));function _0x731088(_0x472346,_0x35ed37){return _0x162012(_0x472346,_0x35ed37-0x6b9);}const _0x599b3a=_0x588e35[_0x731088(0x67f,0x605)](_0x731088(0x580,0x4ff));try{await _0x2cc8d0['editReply']({'content':lang['Music'][_0x731088(0x5ac,0x511)]});let _0x3c1139;if(_0x5dab44)_0x3c1139=QueryType[_0x731088(0x5d8,0x5bb)];else{if(_0xb910cb)_0x3c1139=QueryType[_0x731088(0x56c,0x5aa)];else _0x599b3a?_0x3c1139=QueryType[_0x731088(0x583,0x61a)]:_0x3c1139=QueryType[_0x731088(0x660,0x5d5)];}const _0x208b80=await _0x2d4904[_0x731088(0x60e,0x602)](_0x588e35,{'requestedBy':_0x2cc8d0[_0x731088(0x6a8,0x616)],'searchType':_0x3c1139});if(!_0x208b80||!_0x208b80[_0x731088(0x4d2,0x4fa)][_0x731088(0x4a7,0x50f)])return _0x2cc8d0[_0x731088(0x5b2,0x56d)]({'content':lang[_0x731088(0x547,0x543)][_0x731088(0x4da,0x523)],'ephemeral':!![]});let _0x48bffd=_0x2d4904[_0x731088(0x5a8,0x592)][_0x731088(0x5a0,0x575)](_0x2cc8d0[_0x731088(0x67c,0x614)]['id']);if(!_0x48bffd){_0x48bffd=_0x2d4904[_0x731088(0x5d6,0x592)][_0x731088(0x59c,0x503)](_0x2cc8d0[_0x731088(0x69d,0x614)]['id'],{'metadata':{'channel':_0x2cc8d0[_0x731088(0x530,0x580)],'requestedByUser':_0x2cc8d0[_0x731088(0x692,0x616)]},'leaveOnEnd':![],'leaveOnEmpty':config[_0x731088(0x580,0x5ba)][_0x731088(0x584,0x5f8)],'leaveOnEmptyCooldown':config[_0x731088(0x5f5,0x5ba)][_0x731088(0x500,0x554)]});try{await _0x48bffd[_0x731088(0x5d7,0x586)](_0x4a35b5);}catch(_0x38f0f0){return _0x2d4904[_0x731088(0x54a,0x592)][_0x731088(0x54c,0x55b)](_0x2cc8d0[_0x731088(0x5de,0x614)]['id']),_0x2cc8d0[_0x731088(0x4eb,0x56d)]({'content':lang[_0x731088(0x587,0x543)]['ErrorJoinChannel'],'ephemeral':!![]});}}if(_0x208b80[_0x731088(0x510,0x584)]){const _0x31b6b5=_0x208b80[_0x731088(0x4e5,0x4fa)][_0x731088(0x5d6,0x57a)](_0x50713b=>{function _0x46f6c5(_0xc13a25,_0x16bd1a){return _0x731088(_0xc13a25,_0x16bd1a- -0x490);}return _0x48bffd[_0x46f6c5(0xa4,0x60)](_0x50713b);});await Promise['all'](_0x31b6b5),await _0x2cc8d0[_0x731088(0x4f3,0x56d)]({'content':_0x731088(0x67a,0x60c)+_0x208b80['playlist']['title']+_0x731088(0x549,0x509)+_0x208b80[_0x731088(0x4a3,0x4fa)][_0x731088(0x51c,0x50f)]+_0x731088(0x53c,0x519),'ephemeral':![]}),!_0x48bffd['node'][_0x731088(0x533,0x5ab)]()&&await _0x48bffd['node'][_0x731088(0x584,0x5eb)]();}else{const _0x1b8a75=_0x208b80['tracks'][0x0];_0x48bffd['addTrack'](_0x1b8a75),await _0x2cc8d0['editReply']({'content':'Playing:\x20'+_0x1b8a75[_0x731088(0x599,0x619)]+_0x731088(0x584,0x5e4)+_0x1b8a75[_0x731088(0x5e1,0x57c)],'ephemeral':![]}),!_0x48bffd['node'][_0x731088(0x556,0x5ab)]()&&await _0x48bffd[_0x731088(0x47a,0x4ee)][_0x731088(0x5b8,0x5eb)]();}}catch(_0x26d460){if(_0x26d460['message']&&_0x26d460[_0x731088(0x4fa,0x513)][_0x731088(0x65f,0x605)]('ERR_NO_RESULT'))return _0x2cc8d0[_0x731088(0x54a,0x56d)]({'content':lang['Music'][_0x731088(0x4b6,0x523)],'ephemeral':!![]});return _0x2cc8d0[_0x731088(0x5e9,0x56d)]({'content':lang['Music'][_0x731088(0x639,0x5c1)],'ephemeral':!![]});}}async function pause(_0x1025d1,_0x195938){function _0x4942f2(_0x29762e,_0x11f47d){return _0x162012(_0x29762e,_0x11f47d-0x35b);}await _0x1025d1[_0x4942f2(0x224,0x23b)]();try{const _0x2e82d8=_0x195938[_0x4942f2(0x298,0x234)]['get'](_0x1025d1['guild']['id']);if(!_0x2e82d8||!_0x2e82d8[_0x4942f2(0x1af,0x19c)]||_0x2e82d8[_0x4942f2(0x234,0x19c)][_0x4942f2(0x266,0x2b7)][_0x4942f2(0x168,0x1b1)]===0x0){await _0x1025d1['editReply']({'content':lang['Music'][_0x4942f2(0x2e3,0x253)]});return;}if(!_0x2e82d8[_0x4942f2(0x2ac,0x240)]){await _0x1025d1[_0x4942f2(0x1f8,0x20f)]({'content':_0x4942f2(0x212,0x1f4)});return;}if(!_0x2e82d8[_0x4942f2(0x112,0x190)][_0x4942f2(0x1df,0x270)]()){_0x2e82d8[_0x4942f2(0x131,0x190)]['pause'](),await _0x1025d1[_0x4942f2(0x249,0x20f)]({'content':lang['Music'][_0x4942f2(0x287,0x1fe)][_0x4942f2(0x27f,0x246)](_0x4942f2(0x1e3,0x215),_0x2e82d8[_0x4942f2(0x29e,0x240)][_0x4942f2(0x32c,0x2bb)])});return;}else await _0x1025d1[_0x4942f2(0x29b,0x20f)]({'content':lang[_0x4942f2(0x1d1,0x1e5)][_0x4942f2(0x273,0x260)][_0x4942f2(0x221,0x246)](_0x4942f2(0x266,0x215),_0x2e82d8[_0x4942f2(0x285,0x240)][_0x4942f2(0x27e,0x2bb)])});}catch(_0x503249){console[_0x4942f2(0x2ce,0x238)](_0x503249),!_0x1025d1[_0x4942f2(0x222,0x2a2)]&&await _0x1025d1[_0x4942f2(0x27f,0x20f)]({'content':lang[_0x4942f2(0x170,0x1e5)]['Error']});}}async function resume(_0x2ed123,_0x53b304){await _0x2ed123['deferReply']();function _0x56f4ab(_0x4d2513,_0x1c7dab){return _0x162012(_0x1c7dab,_0x4d2513-0x797);}try{const _0x4ba55d=_0x53b304['nodes'][_0x56f4ab(0x653,0x698)](_0x2ed123[_0x56f4ab(0x6f2,0x781)]['id']);if(!_0x4ba55d||!_0x4ba55d[_0x56f4ab(0x5d8,0x5c3)]||_0x4ba55d[_0x56f4ab(0x5d8,0x5d6)][_0x56f4ab(0x6f3,0x787)]['length']===0x0){await _0x2ed123[_0x56f4ab(0x64b,0x617)]({'content':lang['Music']['NoMusicInQueue'],'ephemeral':!![]});return;}if(!_0x4ba55d[_0x56f4ab(0x67c,0x6e8)]){await _0x2ed123['editReply']({'content':_0x56f4ab(0x630,0x6bb)});return;}if(!_0x4ba55d[_0x56f4ab(0x5cc,0x5da)][_0x56f4ab(0x689,0x638)]()){_0x4ba55d['node'][_0x56f4ab(0x5c8,0x538)](),await _0x2ed123[_0x56f4ab(0x64b,0x6b1)]({'content':lang[_0x56f4ab(0x621,0x678)][_0x56f4ab(0x6c7,0x66e)][_0x56f4ab(0x682,0x68b)](_0x56f4ab(0x651,0x6eb),_0x4ba55d[_0x56f4ab(0x67c,0x636)][_0x56f4ab(0x6f7,0x69e)])});return;}else await _0x2ed123[_0x56f4ab(0x64b,0x5ce)]({'content':lang[_0x56f4ab(0x621,0x5c7)][_0x56f4ab(0x676,0x62c)][_0x56f4ab(0x682,0x6a5)](_0x56f4ab(0x651,0x669),_0x4ba55d[_0x56f4ab(0x67c,0x6ca)]['title'])});}catch(_0x26d141){console['log'](_0x26d141),!_0x2ed123[_0x56f4ab(0x6de,0x75b)]&&await _0x2ed123[_0x56f4ab(0x64b,0x692)]({'content':lang[_0x56f4ab(0x621,0x665)]['Error'],'ephemeral':!![]});}}async function clear(_0x15fcb7,_0x1f431a){function _0x2a186c(_0x60ad10,_0x4c01b6){return _0x162012(_0x60ad10,_0x4c01b6-0x661);}await _0x15fcb7[_0x2a186c(0x505,0x541)]();try{const _0x2585eb=_0x1f431a['nodes']['get'](_0x15fcb7['guild']['id']);!_0x2585eb&&await _0x15fcb7['editReply']({'content':lang[_0x2a186c(0x57a,0x4eb)][_0x2a186c(0x4ec,0x559)],'ephemeral':!![]}),_0x2585eb[_0x2a186c(0x5d1,0x56a)](),await _0x15fcb7[_0x2a186c(0x493,0x515)]({'content':lang[_0x2a186c(0x563,0x4eb)][_0x2a186c(0x436,0x49c)],'ephemeral':!![]});}catch(_0x2fa634){console[_0x2a186c(0x4bf,0x53e)](_0x2fa634),!_0x15fcb7[_0x2a186c(0x596,0x5a8)]&&await _0x15fcb7['editReply']({'content':lang[_0x2a186c(0x495,0x4eb)][_0x2a186c(0x5d8,0x569)],'ephemeral':!![]});}}async function skip(_0x553815,_0x268a0a){function _0x4721c2(_0x49e46f,_0x32ce0a){return _0x162012(_0x32ce0a,_0x49e46f-0x5d5);}await _0x553815[_0x4721c2(0x4b5,0x529)]();try{const _0x27cac4=_0x268a0a[_0x4721c2(0x4ae,0x4d7)][_0x4721c2(0x491,0x446)](_0x553815[_0x4721c2(0x530,0x4f2)]['id']);if(!_0x27cac4){await _0x553815[_0x4721c2(0x489,0x51d)]({'content':lang['Music'][_0x4721c2(0x4cd,0x4ed)],'ephemeral':!![]});return;}if(!_0x27cac4['currentTrack']){await _0x553815['editReply']({'content':_0x4721c2(0x46e,0x423)});return;}if(!_0x27cac4[_0x4721c2(0x40a,0x3aa)]['isIdle']()){_0x27cac4[_0x4721c2(0x40a,0x4a2)][_0x4721c2(0x48a,0x50b)](),await _0x553815[_0x4721c2(0x489,0x40d)]({'content':lang['Music']['Skipped'][_0x4721c2(0x4c0,0x52c)]('{title}',_0x27cac4[_0x4721c2(0x4ba,0x529)][_0x4721c2(0x535,0x4ca)]),'ephemeral':!![]});return;}else await _0x553815[_0x4721c2(0x489,0x51d)]({'content':lang['Music'][_0x4721c2(0x47c,0x51a)],'ephemeral':!![]});}catch(_0x3a4483){!_0x553815[_0x4721c2(0x51c,0x4f6)]&&await _0x553815[_0x4721c2(0x489,0x418)]({'content':lang['Music'][_0x4721c2(0x4dd,0x4e5)],'ephemeral':!![]});}}async function back(_0x4ea2fa,_0x3f1dc6){await _0x4ea2fa[_0x1d5d3c(0x43,0x8d)]();function _0x1d5d3c(_0x5d3526,_0x2e7e09){return _0x162012(_0x5d3526,_0x2e7e09-0x1ad);}try{const _0x541a34=_0x3f1dc6[_0x1d5d3c(0xd1,0x86)][_0x1d5d3c(0x10,0x69)](_0x4ea2fa[_0x1d5d3c(0x12a,0x108)]['id']);if(!_0x541a34||!_0x541a34[_0x1d5d3c(-0x3d,-0x12)]||_0x541a34[_0x1d5d3c(0x6b,-0x12)][_0x1d5d3c(0xd9,0x109)]['length']===0x0){await _0x4ea2fa[_0x1d5d3c(0x88,0x61)]({'content':lang[_0x1d5d3c(-0x66,0x37)][_0x1d5d3c(0x62,0xa5)],'ephemeral':!![]});return;}const _0x4f5f1a=useHistory(_0x4ea2fa['guild']['id']);if(!_0x4f5f1a||_0x4f5f1a[_0x1d5d3c(-0x10,0x85)]()){await _0x4ea2fa[_0x1d5d3c(0xc1,0x61)]({'content':lang[_0x1d5d3c(0x99,0x37)]['NoPreviousMusic'],'ephemeral':!![]});return;}await _0x4f5f1a[_0x1d5d3c(0xf4,0x9c)](),await _0x4ea2fa[_0x1d5d3c(-0x3b,0x61)]({'content':lang[_0x1d5d3c(0x32,0x37)][_0x1d5d3c(0x6d,0xb3)],'ephemeral':!![]});}catch(_0x1177ed){!_0x4ea2fa[_0x1d5d3c(0xe7,0xf4)]&&await _0x4ea2fa['editReply']({'content':lang[_0x1d5d3c(0x1a,0x37)][_0x1d5d3c(0xc9,0xb5)],'ephemeral':!![]});}}function _0x162012(_0x165f65,_0x4549a0){return _0x5b9f(_0x4549a0- -0x3c4,_0x165f65);}async function loop(_0x1c505b,_0x2fa3d2,_0x28e061){function _0x1a33ce(_0x5f5cdb,_0x5ba07a){return _0x162012(_0x5f5cdb,_0x5ba07a-0x4d0);}await _0x1c505b[_0x1a33ce(0x39d,0x3b0)]();try{const _0x4f1654=_0x1c505b[_0x1a33ce(0x462,0x42b)]['id'],_0xc7e3dc=_0x2fa3d2[_0x1a33ce(0x41d,0x3a9)]['get'](_0x4f1654);if(!_0xc7e3dc){await _0x1c505b['editReply']({'content':lang[_0x1a33ce(0x3d8,0x35a)][_0x1a33ce(0x416,0x3c8)],'ephemeral':!![]});return;}const _0x5b0f9a=_0x1c505b[_0x1a33ce(0x35d,0x3b4)]['getString'](_0x1a33ce(0x360,0x3f9));let _0x45bb7f;switch(_0x5b0f9a[_0x1a33ce(0x36b,0x31e)]()){case _0x1a33ce(0x365,0x386):_0x45bb7f=QueueRepeatMode[_0x1a33ce(0x336,0x386)];break;case _0x1a33ce(0x354,0x35b):_0x45bb7f=QueueRepeatMode[_0x1a33ce(0x382,0x35b)];break;case'QUEUE':_0x45bb7f=QueueRepeatMode[_0x1a33ce(0x48a,0x41a)];break;case _0x1a33ce(0x436,0x3ed):_0x45bb7f=QueueRepeatMode['AUTOPLAY'];break;default:_0x45bb7f=QueueRepeatMode[_0x1a33ce(0x372,0x386)];}_0xc7e3dc[_0x1a33ce(0x3dd,0x3ef)](_0x45bb7f);let _0x28a47c,_0x7123e0;switch(_0x45bb7f){case 0x0:_0x28a47c=lang[_0x1a33ce(0x3d0,0x35a)][_0x1a33ce(0x404,0x3f7)][_0x1a33ce(0x480,0x429)],_0x7123e0=_0x1a33ce(0x496,0x429);break;case 0x1:_0x28a47c=lang['Music'][_0x1a33ce(0x3e9,0x3f7)][_0x1a33ce(0x48e,0x415)],_0x7123e0=_0x1a33ce(0x42a,0x415);break;case 0x2:_0x28a47c=lang[_0x1a33ce(0x32f,0x35a)][_0x1a33ce(0x3d9,0x3f7)][_0x1a33ce(0x3b0,0x38e)],_0x7123e0=_0x1a33ce(0x425,0x38e);break;case 0x3:_0x28a47c=lang[_0x1a33ce(0x3e3,0x35a)][_0x1a33ce(0x43b,0x3f7)][_0x1a33ce(0x2dd,0x32e)],_0x7123e0='Autoplay';break;}await _0x1c505b[_0x1a33ce(0x3e2,0x384)]({'content':_0x28a47c[_0x1a33ce(0x408,0x3bb)](_0x1a33ce(0x2d3,0x35f),_0x7123e0),'ephemeral':!![]});}catch(_0x48bb13){console[_0x1a33ce(0x3f2,0x3ad)](_0x48bb13),await _0x1c505b[_0x1a33ce(0x3ea,0x384)]({'content':lang[_0x1a33ce(0x34d,0x35a)][_0x1a33ce(0x3b8,0x3d8)],'ephemeral':!![]});}}async function queue(_0x58b426,_0x15079e){function _0x4dbc8b(_0x420480,_0x14614c){return _0x162012(_0x14614c,_0x420480-0x48e);}await _0x58b426[_0x4dbc8b(0x36e,0x3be)]();try{const _0x541c99=_0x15079e[_0x4dbc8b(0x367,0x3a8)][_0x4dbc8b(0x34a,0x2f3)](_0x58b426[_0x4dbc8b(0x3e9,0x3e1)]['id']);if(!_0x541c99||_0x541c99[_0x4dbc8b(0x2cf,0x31f)][_0x4dbc8b(0x3ea,0x413)][_0x4dbc8b(0x2e4,0x260)]===0x0){await _0x58b426[_0x4dbc8b(0x342,0x360)]({'content':lang[_0x4dbc8b(0x318,0x350)][_0x4dbc8b(0x386,0x304)],'ephemeral':!![]});return;}const _0x1dcd59={'title':_0x541c99[_0x4dbc8b(0x373,0x399)]?.['title'],'author':_0x541c99[_0x4dbc8b(0x373,0x37f)]?.[_0x4dbc8b(0x351,0x3b7)],'duration':_0x541c99['currentTrack']?.[_0x4dbc8b(0x35a,0x364)],'queueDuration':_0x541c99[_0x4dbc8b(0x33a,0x2a0)]},{Embed:_0x50028d,SongsPerPage:_0x343d99}=config[_0x4dbc8b(0x38f,0x328)][_0x4dbc8b(0x34c,0x2e3)]||{},_0x46345b=Math['ceil'](_0x541c99['tracks'][_0x4dbc8b(0x3ea,0x42f)][_0x4dbc8b(0x2e4,0x2dd)]/_0x343d99);let _0x40b57d=0x0;const _0x27fd91=_0x32243f=>{const _0x3b4826=_0x32243f*_0x343d99,_0x414f97=Math[_0x50021d(-0xc8,-0x38)](_0x3b4826+_0x343d99,_0x541c99[_0x50021d(0x9,-0x7b)][_0x50021d(0x25,0xa0)][_0x50021d(-0xd7,-0x66)]),_0x48b80c=_0x541c99[_0x50021d(-0x28,-0x7b)][_0x50021d(0x13d,0xa0)]['slice'](_0x3b4826,_0x414f97);let _0xe8b14b=replacePlaceholders(_0x50028d[_0x50021d(0xd,0xaa)][0x0],_0x1dcd59);_0xe8b14b+=_0x50028d[_0x50021d(0x111,0xaa)][0x1],_0x48b80c[_0x50021d(0x61,0xab)]((_0x4b7daa,_0x2f990a)=>{function _0x358197(_0x30c46d,_0x4f267d){return _0x50021d(_0x4f267d,_0x30c46d-0x2dd);}const _0x719410={'numberInQueue':_0x3b4826+_0x2f990a+0x1,'title':_0x4b7daa['title'],'author':_0x4b7daa['author'],'duration':_0x4b7daa[_0x358197(0x2fc,0x371)]};_0xe8b14b+=replacePlaceholders(_0x50028d['Description'][0x2],_0x719410)+'\x0a';});function _0x50021d(_0x525be1,_0x30f400){return _0x4dbc8b(_0x30f400- -0x34a,_0x525be1);}const _0x90dc6d={'currentPage':_0x32243f+0x1,'totalPages':_0x46345b};return _0xe8b14b+=replacePlaceholders(_0x50028d['Description'][0x3],_0x90dc6d),_0xe8b14b;},_0x27824e=_0x5d90a2=>{const _0x388abf=new EmbedBuilder();_0x388abf[_0x532eb4(-0x3c,-0x8d)](_0x27fd91(_0x5d90a2)),_0x388abf[_0x532eb4(0x3e,-0x2)](_0x50028d?.[_0x532eb4(-0x1b,-0x4d)]||_0x532eb4(-0xb,0x90));function _0x532eb4(_0x187cbf,_0x19029f){return _0x4dbc8b(_0x187cbf- -0x32e,_0x19029f);}if(_0x50028d?.[_0x532eb4(0x59,-0x19)])_0x388abf['setTitle'](replacePlaceholders(_0x50028d[_0x532eb4(0x59,0xb6)],_0x1dcd59));if(_0x50028d?.['Footer']?.[_0x532eb4(0x4c,0xac)])_0x388abf[_0x532eb4(-0x1d,-0x1f)]({'text':_0x50028d[_0x532eb4(0xae,0xb2)][_0x532eb4(0x4c,-0x9)],'iconURL':isValidHttpUrl(_0x50028d[_0x532eb4(0xae,0xd4)][_0x532eb4(0x4e,0xd6)])?_0x50028d[_0x532eb4(0xae,0xdc)][_0x532eb4(0x4e,-0x43)]:undefined});if(_0x50028d?.[_0x532eb4(0x67,-0x33)])_0x388abf[_0x532eb4(0xf,0x69)](isValidHttpUrl(_0x50028d[_0x532eb4(0x67,-0x34)])?_0x50028d['Image']:undefined);if(_0x50028d?.[_0x532eb4(0x84,0x65)])_0x388abf[_0x532eb4(0x6f,0xe5)](isValidHttpUrl(_0x50028d[_0x532eb4(0x84,0x31)])?_0x50028d[_0x532eb4(0x84,0x80)]:undefined);return _0x388abf;},_0x451c2f=(_0xadf443,_0xfa2d87,_0x5c7bc4)=>{const _0x408092=new ButtonBuilder()['setCustomId'](_0xadf443)[_0x18b161(0x1ce,0x1ed)](_0x5c7bc4);if(_0xfa2d87?.[_0x18b161(0xd7,0x123)])_0x408092[_0x18b161(0x288,0x1eb)](_0xfa2d87[_0x18b161(0x18f,0x123)]);if(_0xfa2d87?.['Text'])_0x408092[_0x18b161(0x1b7,0x1e8)](_0xfa2d87['Text']||'');function _0x18b161(_0x157f05,_0x3c000b){return _0x4dbc8b(_0x3c000b- -0x1de,_0x157f05);}if(_0xfa2d87?.[_0x18b161(0x130,0xf3)])_0x408092[_0x18b161(0x1e4,0x156)](_0xfa2d87[_0x18b161(0xd7,0xf3)]);return _0x408092;},_0x5514a4=new ActionRowBuilder()['addComponents'](_0x451c2f(_0x4dbc8b(0x3bb,0x411),_0x50028d[_0x4dbc8b(0x30e,0x2dd)][_0x4dbc8b(0x389,0x2f5)],_0x40b57d===0x0),_0x451c2f(_0x4dbc8b(0x2e0,0x2ab)+_0x40b57d,_0x50028d[_0x4dbc8b(0x30e,0x36c)][_0x4dbc8b(0x3b6,0x436)],_0x40b57d===0x0),_0x451c2f(_0x4dbc8b(0x2fd,0x306)+_0x40b57d,_0x50028d[_0x4dbc8b(0x30e,0x349)][_0x4dbc8b(0x32b,0x398)],_0x40b57d>=_0x46345b-0x1),_0x451c2f(_0x4dbc8b(0x3a6,0x389),_0x50028d[_0x4dbc8b(0x30e,0x2ad)][_0x4dbc8b(0x3f8,0x45a)],_0x40b57d>=_0x46345b-0x1));await _0x58b426[_0x4dbc8b(0x342,0x2d0)]({'embeds':[_0x27824e(_0x40b57d)],'components':[_0x5514a4],'ephemeral':!![]});}catch(_0x1e9604){await _0x58b426[_0x4dbc8b(0x342,0x319)]({'content':lang[_0x4dbc8b(0x318,0x2d7)][_0x4dbc8b(0x396,0x3c7)],'ephemeral':!![]}),console['log'](_0x4dbc8b(0x3ae,0x3bb)+_0x1e9604);}}async function move(_0x8b3db1,_0x56197e){await _0x8b3db1['deferReply']();let _0x26d719;function _0x434cae(_0xf9eeb3,_0x1fe0fc){return _0x162012(_0xf9eeb3,_0x1fe0fc-0x37c);}try{const _0x4a4611=_0x56197e[_0x434cae(0x283,0x255)][_0x434cae(0x25d,0x238)](_0x8b3db1[_0x434cae(0x2ae,0x2d7)]['id']);if(!_0x4a4611||!_0x4a4611[_0x434cae(0x155,0x1bd)]||_0x4a4611['tracks']['data'][_0x434cae(0x208,0x1d2)]===0x0){await _0x8b3db1[_0x434cae(0x237,0x230)]({'content':lang[_0x434cae(0x213,0x206)][_0x434cae(0x293,0x274)],'ephemeral':!![]});return;}const _0x4e5964=_0x8b3db1[_0x434cae(0x29b,0x260)][_0x434cae(0x273,0x2c2)](_0x434cae(0x1ec,0x1d8),!![])-0x1,_0x58bcd3=_0x8b3db1['options'][_0x434cae(0x329,0x2c2)](_0x434cae(0x1f5,0x240),!![])-0x1;if(_0x4e5964<0x0||_0x4e5964>=_0x4a4611['tracks']['data']['length']||_0x58bcd3<0x0||_0x58bcd3>=_0x4a4611[_0x434cae(0x206,0x1bd)]['data'][_0x434cae(0x21a,0x1d2)]){await _0x8b3db1[_0x434cae(0x1b3,0x230)]({'content':lang[_0x434cae(0x25d,0x206)][_0x434cae(0x248,0x2ab)][_0x434cae(0x22b,0x1cf)],'ephemeral':!![]});return;}[_0x26d719]=_0x4a4611[_0x434cae(0x127,0x1bd)]['data'][_0x434cae(0x250,0x1cb)](_0x4e5964,0x1),_0x4a4611['swapTracks'](_0x26d719,_0x58bcd3),await _0x8b3db1[_0x434cae(0x20d,0x230)]({'content':lang[_0x434cae(0x1c5,0x206)][_0x434cae(0x2c1,0x2ab)][_0x434cae(0x274,0x2e5)][_0x434cae(0x2de,0x267)](_0x434cae(0x265,0x226),_0x26d719[_0x434cae(0x264,0x2dc)])[_0x434cae(0x230,0x267)](_0x434cae(0x230,0x1df),_0x58bcd3+0x1)});}catch(_0x5ac3eb){console[_0x434cae(0x2d5,0x2be)](_0x5ac3eb),await _0x8b3db1[_0x434cae(0x298,0x230)]({'content':lang[_0x434cae(0x208,0x206)][_0x434cae(0x31c,0x2ab)][_0x434cae(0x215,0x284)][_0x434cae(0x21e,0x267)](_0x434cae(0x188,0x226),_0x26d719[_0x434cae(0x2be,0x2dc)]),'ephemeral':!![]});}}async function filters(_0x30f94e,_0x4a18b4){await _0x30f94e[_0x347f26(-0x65,-0xea)]();function _0x347f26(_0x463b34,_0x175141){return _0x162012(_0x463b34,_0x175141-0x36);}try{const _0x50e8c8=_0x4a18b4['nodes'][_0x347f26(-0x136,-0x10e)](_0x30f94e['guild']['id']);if(!_0x50e8c8||!_0x50e8c8[_0x347f26(-0xd4,-0xe5)]){await _0x30f94e[_0x347f26(-0x187,-0x116)]({'content':lang[_0x347f26(-0x17f,-0x140)]['NoMusicPlaying'],'ephemeral':!![]});return;}const _0x58ba31=['bassboost','8D',_0x347f26(-0xaa,-0x86),_0x347f26(-0xc8,-0xfc),_0x347f26(-0x9d,-0xcd),_0x347f26(-0x143,-0x126),_0x347f26(-0x141,-0x15f),'karaoke',_0x347f26(-0xb9,-0x101)],_0x42587c=_0x50e8c8[_0x347f26(-0x70,-0x8e)][_0x347f26(-0x19b,-0x153)]['getFiltersDisabled'](),_0x2b587b=_0x42587c['filter'](_0x5790a5=>_0x58ba31[_0x347f26(-0x75,-0x7e)](_0x5790a5));let _0x32abd6=_0x347f26(-0x13d,-0xa8),_0x55a3a0=_0x347f26(-0x12f,-0xa8),_0x3232ca=_0x347f26(-0x161,-0x14e);_0x58ba31[_0x347f26(-0x24,-0x63)]((_0x54cc6c,_0xbb430a)=>{const _0x302692=_0x2b587b[_0x494bdf(0x555,0x4e9)](_0x54cc6c)?_0x494bdf(0x4b5,0x44f)+lang[_0x494bdf(0x39f,0x427)][_0x494bdf(0x454,0x3fc)]['Fields'][_0x494bdf(0x46c,0x4be)]+'\x1b[0m':_0x494bdf(0x487,0x3f6)+lang['Music']['Filters']['Fields']['Enabled']+_0x494bdf(0x35c,0x3e9);function _0x494bdf(_0x57715b,_0x1d14f4){return _0x347f26(_0x57715b,_0x1d14f4-0x567);}_0x32abd6+=_0xbb430a+0x1+'\x0a',_0x55a3a0+=_0x54cc6c[_0x494bdf(0x490,0x499)](0x0)['toUpperCase']()+_0x54cc6c[_0x494bdf(0x56f,0x4db)](0x1)+'\x0a',_0x3232ca+=_0x302692+'\x0a';}),_0x32abd6+=_0x347f26(-0xc0,-0x7a),_0x55a3a0+=_0x347f26(-0x6f,-0x7a),_0x3232ca+='```';const _0x240490=config['MusicCommand'][_0x347f26(-0x1ab,-0x16b)][_0x347f26(-0xad,-0xf6)],_0x356225=new EmbedBuilder();_0x356225[_0x347f26(-0x19b,-0x192)]({'name':_0x240490[_0x347f26(-0xb5,-0x12e)][_0x347f26(-0x1d6,-0x185)],'value':_0x32abd6,'inline':!![]},{'name':_0x240490[_0x347f26(-0x156,-0x12e)][_0x347f26(-0xc4,-0x152)],'value':_0x55a3a0,'inline':!![]},{'name':_0x240490['Fields']['Enabled'],'value':_0x3232ca,'inline':!![]}),_0x356225[_0x347f26(0x2,-0x68)](_0x347f26(-0xec,-0xbd));_0x240490[_0x347f26(-0x13c,-0xd1)]&&_0x356225[_0x347f26(-0x1eb,-0x166)](_0x240490['Title']);_0x240490['Thumbnail']&&isValidHttpUrl(_0x240490['Thumbnail'])&&_0x356225['setThumbnail'](_0x240490['Thumbnail']);if(_0x240490[_0x347f26(-0xe2,-0x7c)]&&_0x240490[_0x347f26(-0xdc,-0x7c)][_0x347f26(-0xd2,-0xde)]){const _0x392b25=_0x240490[_0x347f26(-0xba,-0x7c)][_0x347f26(-0x117,-0xdc)];_0x356225[_0x347f26(-0xd5,-0x147)]({'text':_0x240490[_0x347f26(-0x5,-0x7c)][_0x347f26(-0xf9,-0xde)],'iconURL':isValidHttpUrl(_0x392b25)?_0x392b25:undefined});}const _0x4f0384=[],_0x44af13=_0x50e8c8[_0x347f26(-0x113,-0x8e)]['ffmpeg'][_0x347f26(-0x1c1,-0x125)](),_0x20ebf6=_0x44af13[_0x347f26(-0xbc,-0xe0)](_0x5fba95=>_0x58ba31['includes'](_0x5fba95));for(let _0x51315d=0x0;_0x51315d<_0x58ba31[_0x347f26(-0x1d8,-0x174)];_0x51315d+=0x3){const _0x120bf8=new ActionRowBuilder();_0x58ba31[_0x347f26(-0x114,-0x8c)](_0x51315d,_0x51315d+0x3)['forEach'](_0x501a23=>{const _0xd93a87=_0x20ebf6[_0x12d7fa(0x12b,0x185)](_0x501a23),_0x12ff66=_0xd93a87?_0x240490[_0x12d7fa(0x7b,0x6c)][_0x12d7fa(0x5f,0xa6)][_0x12d7fa(0x100,0x64)][_0x12d7fa(0x52,0x1c)]:_0x240490[_0x12d7fa(0x7b,-0xd)][_0x12d7fa(0x5f,0x5d)]['Enabled'][_0x12d7fa(0x52,0x8d)],_0x6d9032=new ButtonBuilder()[_0x12d7fa(0x60,-0xa)](_0x12d7fa(0x114,0x188)+_0x501a23)['setLabel'](_0x501a23[_0x12d7fa(0xdb,0xa8)](0x0)[_0x12d7fa(0x2d,-0x31)]()+_0x501a23[_0x12d7fa(0x11d,0x10c)](0x1))[_0x12d7fa(0x11a,0xd7)](_0x12ff66);function _0x12d7fa(_0x171b70,_0x26dfc1){return _0x347f26(_0x26dfc1,_0x171b70-0x1a9);}_0x120bf8['addComponents'](_0x6d9032);}),_0x4f0384[_0x347f26(-0xf7,-0x11f)](_0x120bf8);}await _0x30f94e[_0x347f26(-0xea,-0x116)]({'embeds':[_0x356225],'components':_0x4f0384,'ephemeral':!![]});}catch(_0x2d3c10){console['log'](_0x2d3c10),await _0x30f94e[_0x347f26(-0x8c,-0x116)]({'content':lang[_0x347f26(-0x133,-0x140)]['Filters'][_0x347f26(-0x55,-0xc7)],'ephemeral':!![]});}}async function nowplaying(_0x3ff687,_0x8a73a8,_0x52a940){await _0x3ff687[_0x4ecd1d(-0x156,-0xdc)]();function _0x4ecd1d(_0x4c51bb,_0x573c49){return _0x162012(_0x4c51bb,_0x573c49-0x44);}try{const _0x1e9eee=_0x8a73a8[_0x4ecd1d(-0x17c,-0xe3)][_0x4ecd1d(-0xd3,-0x100)](_0x3ff687[_0x4ecd1d(-0xaf,-0x61)]['id']);if(!_0x1e9eee||!_0x1e9eee['currentTrack'])return _0x3ff687[_0x4ecd1d(-0xd5,-0x108)]({'content':_0x52a940[_0x4ecd1d(-0xa4,-0x132)]['NoMusicPlaying'],'ephemeral':!![]});const _0x441932=_0x1e9eee[_0x4ecd1d(-0x10a,-0xd7)],_0x23d859=_0x1e9eee[_0x4ecd1d(-0x184,-0x187)][_0x4ecd1d(-0x21,-0xbd)](),_0x162946=_0x23d859['current']['value']/_0x23d859[_0x4ecd1d(-0x199,-0x12a)][_0x4ecd1d(-0x166,-0x14a)]*0x64,_0x544d00=createCanvas(config[_0x4ecd1d(-0x90,-0x12c)]['Canvas']['Width'],config[_0x4ecd1d(-0xf1,-0x12c)][_0x4ecd1d(-0xba,-0xaa)][_0x4ecd1d(-0x145,-0x11b)]),_0x2c3685=_0x544d00['getContext']('2d'),_0x37c5f6=_0x2c3685[_0x4ecd1d(-0xb8,-0x54)](0x0,0x0,_0x544d00[_0x4ecd1d(-0x19,-0x58)],_0x544d00[_0x4ecd1d(-0x186,-0x188)]);_0x37c5f6[_0x4ecd1d(-0x17d,-0x141)](0x0,config[_0x4ecd1d(-0xe2,-0x12c)][_0x4ecd1d(-0x12c,-0x186)][_0x4ecd1d(-0xdb,-0xc1)]),_0x37c5f6[_0x4ecd1d(-0xa8,-0x141)](0x1,config['NowPlaying']['BackgroundGradient']['End']),_0x2c3685['fillStyle']=_0x37c5f6,roundRect(_0x2c3685,0x0,0x0,_0x544d00[_0x4ecd1d(-0x60,-0x58)],_0x544d00['height'],0x28),_0x2c3685[_0x4ecd1d(-0xef,-0x88)](),addPatternOverlay(_0x2c3685);try{const _0x4b6587=await axios[_0x4ecd1d(-0x170,-0x100)](_0x441932[_0x4ecd1d(-0xb6,-0x13e)],{'responseType':_0x4ecd1d(-0xfb,-0xf6)}),_0x5e1da9=Buffer[_0x4ecd1d(-0x168,-0xd5)](_0x4b6587[_0x4ecd1d(-0x49,-0x60)],_0x4ecd1d(-0x19b,-0x168)),_0x174033=await sharp(_0x5e1da9)[_0x4ecd1d(-0xa7,-0x90)](0x12c,0x12c)[_0x4ecd1d(-0xf0,-0xa2)]()[_0x4ecd1d(0x9,-0x6b)](),_0x2f940c=await loadImage(_0x174033);_0x2c3685['save'](),_0x2c3685['beginPath'](),_0x2c3685[_0x4ecd1d(-0x11,-0x82)](0xc8,0xc8,0x96,0x0,Math['PI']*0x2,!![]),_0x2c3685[_0x4ecd1d(-0xc9,-0xda)](),_0x2c3685[_0x4ecd1d(-0x84,-0xfc)](),_0x2c3685[_0x4ecd1d(-0x170,-0xfd)](_0x2f940c,0x32,0x32,0x12c,0x12c),_0x2c3685[_0x4ecd1d(-0xc5,-0x6a)](),_0x2c3685[_0x4ecd1d(-0x178,-0xfa)]=config['NowPlaying'][_0x4ecd1d(-0x120,-0xdb)][_0x4ecd1d(-0x120,-0x137)],_0x2c3685[_0x4ecd1d(-0xc8,-0x150)]=0x1e,_0x2c3685[_0x4ecd1d(-0xf0,-0x6f)](),_0x2c3685[_0x4ecd1d(-0x6c,-0x82)](0xc8,0xc8,0x97,0x0,Math['PI']*0x2,!![]),_0x2c3685[_0x4ecd1d(-0xb5,-0x79)]=config[_0x4ecd1d(-0xf7,-0x12c)][_0x4ecd1d(-0x6a,-0xdb)][_0x4ecd1d(-0xa7,-0x137)],_0x2c3685[_0x4ecd1d(-0x148,-0x17d)]=0x6,_0x2c3685['stroke'](),_0x2c3685[_0x4ecd1d(-0x16c,-0x150)]=0x0;}catch(_0x236a77){console[_0x4ecd1d(-0x45,-0x7a)]('Error\x20loading\x20thumbnail:',_0x236a77),_0x2c3685[_0x4ecd1d(-0x165,-0x125)]=config[_0x4ecd1d(-0x8f,-0x12c)][_0x4ecd1d(-0x6b,-0xe9)][_0x4ecd1d(-0xb8,-0x137)],_0x2c3685[_0x4ecd1d(-0xd,-0x6f)](),_0x2c3685['arc'](0xc8,0xc8,0x96,0x0,Math['PI']*0x2,!![]),_0x2c3685[_0x4ecd1d(-0x15,-0x88)]();}drawProgressBar(_0x2c3685,_0x162946);const _0x4de59e=config[_0x4ecd1d(-0x146,-0x12c)][_0x4ecd1d(0x15,-0x66)];drawText(_0x2c3685,_0x4ecd1d(-0x189,-0x11e),0x190,0x50,0x302,{..._0x4de59e[_0x4ecd1d(-0x14e,-0xe2)],'transform':_0x4ecd1d(-0x155,-0xea)}),drawText(_0x2c3685,_0x441932['title'],0x190,0x8c,0x302,{..._0x4de59e[_0x4ecd1d(-0xd1,-0xc3)]}),drawText(_0x2c3685,_0x441932[_0x4ecd1d(-0xd5,-0xf9)],0x190,0xc8,0x302,{..._0x4de59e[_0x4ecd1d(-0x110,-0xd4)]}),drawText(_0x2c3685,_0x23d859[_0x4ecd1d(-0x4d,-0xa8)][_0x4ecd1d(-0xe1,-0x15f)],0x190,0x154,0x64,{..._0x4de59e['Time']}),drawText(_0x2c3685,_0x23d859[_0x4ecd1d(-0x159,-0x12a)][_0x4ecd1d(-0x1f1,-0x15f)],0x492,0x154,0x64,{..._0x4de59e[_0x4ecd1d(-0xcf,-0x10b)],'textAlign':_0x4ecd1d(-0x105,-0x182)});const _0x86adb2=_0x544d00[_0x4ecd1d(-0xc0,-0x6b)](),_0x25bcef=new AttachmentBuilder(_0x86adb2,{'name':_0x4ecd1d(-0x12f,-0x104)});await _0x3ff687[_0x4ecd1d(-0x8c,-0x108)]({'files':[_0x25bcef],'ephemeral':![]});}catch(_0x3e9176){console[_0x4ecd1d(-0xe2,-0x7a)](_0x4ecd1d(-0xa8,-0x13d),_0x3e9176),await _0x3ff687[_0x4ecd1d(-0x15f,-0x108)]({'content':_0x52a940[_0x4ecd1d(-0x15d,-0x132)][_0x4ecd1d(-0xd3,-0x12c)]['GeneratingError'],'ephemeral':!![]});}}function drawProgressBar(_0x2ca586,_0x39124c){const _0x161cc2={'x':0x190,'y':0x118,'width':0x302,'height':config[_0x45a269(0x187,0x1dd)][_0x45a269(0x179,0xfb)]['Height'],'borderRadius':config[_0x45a269(0x187,0x123)][_0x45a269(0x179,0xea)][_0x45a269(0x130,0xd1)]};_0x2ca586[_0x45a269(0x18e,0x10d)]=config[_0x45a269(0x187,0x133)]['ProgressBar'][_0x45a269(0x215,0x234)];function _0x45a269(_0x56995b,_0x9e9f44){return _0x162012(_0x9e9f44,_0x56995b-0x2f7);}roundRect(_0x2ca586,_0x161cc2['x'],_0x161cc2['y'],_0x161cc2[_0x45a269(0x25b,0x1e6)],_0x161cc2['height'],_0x161cc2[_0x45a269(0x139,0x1be)]),_0x2ca586['fill']();const _0x5b6c8a=Math[_0x45a269(0x24f,0x269)](_0x161cc2['width']*(_0x39124c/0x64),0x14),_0x57ec08=_0x2ca586['createLinearGradient'](_0x161cc2['x'],0x0,_0x161cc2['x']+_0x161cc2[_0x45a269(0x25b,0x1d9)],0x0);_0x57ec08[_0x45a269(0x172,0x15a)](0x0,config[_0x45a269(0x187,0x168)][_0x45a269(0x179,0x19f)][_0x45a269(0x1c7,0x214)]),_0x57ec08['addColorStop'](0x1,config[_0x45a269(0x187,0xff)][_0x45a269(0x179,0x18a)][_0x45a269(0x205,0x215)]),_0x2ca586[_0x45a269(0x18e,0x15e)]=_0x57ec08,roundRect(_0x2ca586,_0x161cc2['x'],_0x161cc2['y'],_0x5b6c8a,_0x161cc2[_0x45a269(0x12b,0x118)],_0x161cc2[_0x45a269(0x139,0x155)]),_0x2ca586[_0x45a269(0x22b,0x228)](),_0x2ca586[_0x45a269(0x244,0x214)](),_0x2ca586[_0x45a269(0x231,0x2a2)](_0x161cc2['x']+_0x5b6c8a,_0x161cc2['y']+_0x161cc2[_0x45a269(0x12b,0xfa)]/0x2,_0x161cc2[_0x45a269(0x12b,0x152)]/0x2+0x5,0x0,Math['PI']*0x2),_0x2ca586[_0x45a269(0x18e,0x1b4)]=config[_0x45a269(0x187,0x156)][_0x45a269(0x1d8,0x1ec)]['Color'],_0x2ca586[_0x45a269(0x22b,0x23d)]();}function drawText(_0x314f8d,_0x3d3aa1,_0x498d6e,_0x4ee4fa,_0x3a5ae5,{size:_0x7c638c,weight:_0x582505,family:_0x5ce9c8,textAlign:_0x22a0a4,fillStyle:_0x21e023,transform:_0x53333b}){_0x314f8d[_0xaa1da(-0x73,-0xb3)]=_0x582505+'\x20'+_0x7c638c+'\x20'+_0x5ce9c8,_0x314f8d[_0xaa1da(-0x148,-0x16e)]=_0x22a0a4||_0xaa1da(-0x227,-0x19c);function _0xaa1da(_0x33a154,_0xd7ce47){return _0x162012(_0x33a154,_0xd7ce47- -0x16);}_0x314f8d[_0xaa1da(-0x1fc,-0x17f)]=_0x21e023;_0x53333b===_0xaa1da(-0x1c7,-0x144)&&(_0x3d3aa1=_0x3d3aa1['toUpperCase']());if(_0x314f8d[_0xaa1da(-0x5f,-0xdd)](_0x3d3aa1)[_0xaa1da(-0x53,-0xb2)]>_0x3a5ae5){let _0x33ea7b=_0x3d3aa1;while(_0x314f8d[_0xaa1da(-0xf5,-0xdd)](_0x33ea7b+'...')[_0xaa1da(-0x13e,-0xb2)]>_0x3a5ae5){_0x33ea7b=_0x33ea7b[_0xaa1da(-0x110,-0xd8)](0x0,-0x1);}_0x3d3aa1=_0x33ea7b+_0xaa1da(-0x1a7,-0x15b);}_0x314f8d['fillText'](_0x3d3aa1,_0x498d6e,_0x4ee4fa);}function roundRect(_0x1f373e,_0x3bee18,_0x364913,_0x5dfa03,_0x1669fa,_0xc7c200){function _0x55e433(_0x15124e,_0x4953f6){return _0x162012(_0x4953f6,_0x15124e-0x462);}_0x1f373e[_0x55e433(0x3af,0x335)](),_0x1f373e[_0x55e433(0x3c1,0x39b)](_0x3bee18+_0xc7c200,_0x364913),_0x1f373e[_0x55e433(0x2d7,0x294)](_0x3bee18+_0x5dfa03,_0x364913,_0x3bee18+_0x5dfa03,_0x364913+_0x1669fa,_0xc7c200),_0x1f373e[_0x55e433(0x2d7,0x2e2)](_0x3bee18+_0x5dfa03,_0x364913+_0x1669fa,_0x3bee18,_0x364913+_0x1669fa,_0xc7c200),_0x1f373e[_0x55e433(0x2d7,0x34f)](_0x3bee18,_0x364913+_0x1669fa,_0x3bee18,_0x364913,_0xc7c200),_0x1f373e['arcTo'](_0x3bee18,_0x364913,_0x3bee18+_0x5dfa03,_0x364913,_0xc7c200),_0x1f373e['closePath']();}function _0x5b9f(_0x80634,_0x5ae68f){const _0x3bd74d=_0x3bd7();return _0x5b9f=function(_0x5b9f48,_0x4c41e9){_0x5b9f48=_0x5b9f48-0x1f4;let _0x230a85=_0x3bd74d[_0x5b9f48];return _0x230a85;},_0x5b9f(_0x80634,_0x5ae68f);}function addPatternOverlay(_0x5aacfa){_0x5aacfa[_0x1a3ac4(0x2ff,0x2fc)](),_0x5aacfa[_0x1a3ac4(0x27c,0x307)]=_0x1a3ac4(0x380,0x3e6),_0x5aacfa[_0x1a3ac4(0x2cc,0x261)]=config[_0x1a3ac4(0x2c5,0x348)]['Overlay'][_0x1a3ac4(0x2ba,0x281)];for(let _0x599fa7=0x0;_0x599fa7<config[_0x1a3ac4(0x2c5,0x28a)][_0x1a3ac4(0x347,0x372)][_0x1a3ac4(0x346,0x3c9)];_0x599fa7+=0x4){for(let _0x305d72=0x0;_0x305d72<config['NowPlaying']['Canvas']['Height'];_0x305d72+=0x4){Math['random']()>0.5&&_0x5aacfa[_0x1a3ac4(0x2b2,0x322)](_0x599fa7,_0x305d72,0x2,0x2);}}function _0x1a3ac4(_0x2da910,_0x327517){return _0x162012(_0x327517,_0x2da910-0x435);}_0x5aacfa[_0x1a3ac4(0x387,0x387)]();}async function seek(_0x2e7645,_0x207280){function _0x4512a9(_0x5b7ff4,_0x17b18b){return _0x162012(_0x5b7ff4,_0x17b18b-0x355);}await _0x2e7645[_0x4512a9(0x2c2,0x235)]();try{const _0x2a17cf=_0x207280[_0x4512a9(0x290,0x22e)][_0x4512a9(0x28e,0x211)](_0x2e7645[_0x4512a9(0x23e,0x2b0)]['id']);if(!_0x2a17cf||!_0x2a17cf[_0x4512a9(0x232,0x23a)]){await _0x2e7645[_0x4512a9(0x214,0x209)]({'content':lang[_0x4512a9(0x238,0x1df)][_0x4512a9(0x261,0x1fe)],'ephemeral':!![]});return;}const _0x32c717=_0x2e7645[_0x4512a9(0x2d2,0x239)][_0x4512a9(0x14c,0x1be)](_0x4512a9(0x20c,0x1c2),!![]),_0x466983=_0x32c717['split'](':')[_0x4512a9(0x1bc,0x1f9)](),_0x4caeba=_0x466983[_0x4512a9(0x16c,0x1bb)]((_0x4e146e,_0x5042cb,_0x1a656d)=>{function _0x116856(_0x10281a,_0x374b77){return _0x4512a9(_0x374b77,_0x10281a-0xb1);}return _0x4e146e+parseInt(_0x5042cb,0xa)*Math[_0x116856(0x31c,0x329)](0x3c,_0x1a656d);},0x0),_0x551bfc=_0x2a17cf[_0x4512a9(0x186,0x18a)][_0x4512a9(0x2e6,0x268)](_0x4caeba*0x3e8);_0x551bfc?await _0x2e7645['editReply']({'content':lang[_0x4512a9(0x16b,0x1df)]['Seeked'][_0x4512a9(0x242,0x2be)][_0x4512a9(0x27c,0x240)]('{time}',_0x32c717),'ephemeral':![]}):await _0x2e7645[_0x4512a9(0x296,0x209)]({'content':lang['Music']['Seeked'][_0x4512a9(0x216,0x25d)][_0x4512a9(0x2db,0x240)](_0x4512a9(0x243,0x23e),_0x32c717),'ephemeral':!![]});}catch(_0x44a29f){!_0x2e7645['replied']&&await _0x2e7645[_0x4512a9(0x18c,0x209)]({'content':lang['Music']['Error'],'ephemeral':!![]})[_0x4512a9(0x17b,0x1e3)](console[_0x4512a9(0x321,0x297)]);}}function getPlatformName(_0x5c7910){let _0x436b05=_0x5b13d1(-0x135,-0x1c1);for(const _0xab0edd of _0x5c7910[_0x5b13d1(-0x18a,-0x110)]){switch(_0xab0edd){case _0x5b13d1(-0xf5,-0x167):case'youtube':_0x436b05=_0x5b13d1(-0x15c,-0xc8);break;case _0x5b13d1(-0x6e,-0xcc):case _0x5b13d1(-0x7c,-0xee):_0x436b05=_0x5b13d1(-0x179,-0x1f1);break;case _0x5b13d1(-0x170,-0x1cb):case _0x5b13d1(-0x100,-0x77):_0x436b05=_0x5b13d1(-0x104,-0xf3);break;case _0x5b13d1(-0x75,-0xc9):case _0x5b13d1(-0xee,-0x11d):_0x436b05=_0x5b13d1(-0x162,-0x11e);break;default:continue;}if(_0x436b05!=='Unknown\x20Platform')break;}function _0x5b13d1(_0x42e20b,_0x406875){return _0x162012(_0x406875,_0x42e20b-0x43);}return _0x436b05;}function getPlatformEmoji(_0x491c89){let _0x385ed9='';function _0x1a438b(_0x3729a1,_0x397160){return _0x162012(_0x397160,_0x3729a1-0x3cd);}switch(_0x491c89){case _0x1a438b(0x22e,0x1b1):_0x385ed9=config[_0x1a438b(0x2ce,0x24f)][_0x1a438b(0x246,0x2c4)][_0x1a438b(0x324,0x3b3)][_0x1a438b(0x22e,0x208)];break;case _0x1a438b(0x211,0x1d3):_0x385ed9=config[_0x1a438b(0x2ce,0x2e9)][_0x1a438b(0x246,0x276)][_0x1a438b(0x324,0x341)][_0x1a438b(0x211,0x252)];break;case _0x1a438b(0x286,0x1ea):_0x385ed9=config[_0x1a438b(0x2ce,0x27d)][_0x1a438b(0x246,0x2bc)][_0x1a438b(0x324,0x36a)][_0x1a438b(0x286,0x230)];break;case'Apple\x20Music':_0x385ed9=config['MusicCommand'][_0x1a438b(0x246,0x267)]['Platform'][_0x1a438b(0x2cd,0x2fe)];break;default:_0x385ed9=config[_0x1a438b(0x2ce,0x263)][_0x1a438b(0x246,0x28e)][_0x1a438b(0x257,0x286)];break;}return _0x385ed9;}function replacePlaceholders(_0x3dbf8f,_0x23b764={}){if(!_0x3dbf8f)return'​';function _0x145179(_0xa61c09,_0x3c564e){return _0x162012(_0x3c564e,_0xa61c09-0x7a2);}return _0x3dbf8f[_0x145179(0x68d,0x64b)](/{([^}]+)}/g,(_0x46c82d,_0x3bb062)=>{return _0x3bb062 in _0x23b764?_0x23b764[_0x3bb062]||'':_0x46c82d;});}function isValidHttpUrl(_0x5c54f9){let _0x26e7da;try{_0x26e7da=new URL(_0x5c54f9);}catch(_0x50e28b){return![];}function _0xe56333(_0x2dae5a,_0x24535d){return _0x162012(_0x24535d,_0x2dae5a-0x1a4);}return _0x26e7da[_0xe56333(0xdb,0xfb)]===_0xe56333(0x5b,0x81)||_0x26e7da[_0xe56333(0xdb,0xf0)]==='https:';}async function stop(_0xa7dd1c,_0x178571){await _0xa7dd1c[_0x262f37(0x48e,0x4a3)]();function _0x262f37(_0x3edec2,_0x2740c3){return _0x162012(_0x3edec2,_0x2740c3-0x5c3);}try{const _0xfcca8d=_0x178571[_0x262f37(0x4f3,0x49c)]['get'](_0xa7dd1c[_0x262f37(0x484,0x51e)]['id']);if(!_0xfcca8d||!_0xfcca8d[_0x262f37(0x41f,0x4a8)]){await _0xa7dd1c[_0x262f37(0x411,0x477)]({'content':lang['Music'][_0x262f37(0x49f,0x46c)],'ephemeral':!![]});return;}_0xfcca8d['clear'](),_0xfcca8d['node'][_0x262f37(0x4b8,0x4b8)](),await _0xa7dd1c['editReply']({'content':lang[_0x262f37(0x411,0x44d)][_0x262f37(0x57b,0x4f4)],'ephemeral':!![]});}catch(_0x3c6ce2){console[_0x262f37(0x4d8,0x4a0)](_0x3c6ce2),!_0xa7dd1c['replied']&&await _0xa7dd1c[_0x262f37(0x463,0x477)]({'content':lang[_0x262f37(0x46a,0x44d)][_0x262f37(0x51a,0x4cb)],'ephemeral':!![]});}}