const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildScheduledEventUpdate,
    async execute(oldEvent, newEvent, client) {
        try {
            // Only handle end time changes
            if (oldEvent.scheduledEndTimestamp === newEvent.scheduledEndTimestamp) return;
            
            console.log(`⏰ Event end time updated: ${newEvent.name} in ${newEvent.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newEvent.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EVENTS_END_TIME_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EVENTS_END_TIME_UPDATE',
                user: 'System',
                userId: null,
                action: 'Th<PERSON><PERSON> gian kết thúc sự kiện được cập nhật',
                details: `Thời gian kết thúc của sự kiện **${newEvent.name}** đã được cập nhật`,
                target: newEvent.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the event end time from audit logs
            try {
                const auditLogs = await newEvent.guild.fetchAuditLogs({
                    type: 102, // GUILD_SCHEDULED_EVENT_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newEvent.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Thời gian kết thúc sự kiện được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for event end time update');
            }
            
            // Add to database
            await client.db.addAuditLog(newEvent.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '⏰ Thời gian kết thúc sự kiện được cập nhật',
                `Vừa có thời gian kết thúc sự kiện được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Tên sự kiện',
                    value: `- ${newEvent.name}`,
                    inline: true
                },
                {
                    name: '> ID sự kiện',
                    value: `- ${newEvent.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian thay đổi',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add end time comparison
            const oldEndTime = oldEvent.scheduledEndTimestamp 
                ? `<t:${Math.floor(oldEvent.scheduledEndTimestamp / 1000)}:F>`
                : 'Không thiết lập';
            const newEndTime = newEvent.scheduledEndTimestamp 
                ? `<t:${Math.floor(newEvent.scheduledEndTimestamp / 1000)}:F>`
                : 'Không thiết lập';
            
            embed.addFields([
                {
                    name: '> Thời gian kết thúc cũ',
                    value: `- ${oldEndTime}`,
                    inline: true
                },
                {
                    name: '> Thời gian kết thúc mới',
                    value: `- ${newEndTime}`,
                    inline: true
                }
            ]);
            
            // Add start time for context
            if (newEvent.scheduledStartTimestamp) {
                embed.addFields({
                    name: '> Thời gian bắt đầu',
                    value: `- <t:${Math.floor(newEvent.scheduledStartTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Calculate duration changes
            if (newEvent.scheduledStartTimestamp) {
                const oldDuration = oldEvent.scheduledEndTimestamp 
                    ? oldEvent.scheduledEndTimestamp - newEvent.scheduledStartTimestamp
                    : null;
                const newDuration = newEvent.scheduledEndTimestamp 
                    ? newEvent.scheduledEndTimestamp - newEvent.scheduledStartTimestamp
                    : null;
                
                if (oldDuration && newDuration) {
                    const durationDiff = newDuration - oldDuration;
                    const diffHours = Math.floor(Math.abs(durationDiff) / (1000 * 60 * 60));
                    const diffMinutes = Math.floor((Math.abs(durationDiff) % (1000 * 60 * 60)) / (1000 * 60));
                    
                    const oldDurationHours = Math.floor(oldDuration / (1000 * 60 * 60));
                    const oldDurationMinutes = Math.floor((oldDuration % (1000 * 60 * 60)) / (1000 * 60));
                    const newDurationHours = Math.floor(newDuration / (1000 * 60 * 60));
                    const newDurationMinutes = Math.floor((newDuration % (1000 * 60 * 60)) / (1000 * 60));
                    
                    embed.addFields([
                        {
                            name: '> Thời lượng cũ',
                            value: `- ${oldDurationHours}h ${oldDurationMinutes}m`,
                            inline: true
                        },
                        {
                            name: '> Thời lượng mới',
                            value: `- ${newDurationHours}h ${newDurationMinutes}m`,
                            inline: true
                        },
                        {
                            name: '> Thay đổi thời lượng',
                            value: `- ${durationDiff > 0 ? '+' : '-'}${diffHours}h ${diffMinutes}m`,
                            inline: true
                        }
                    ]);
                    
                    // Set color based on duration change
                    if (durationDiff > 0) {
                        embed.setColor(0x2ecc71); // Green for longer
                    } else if (durationDiff < 0) {
                        embed.setColor(0xe74c3c); // Red for shorter
                    }
                }
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newEvent.guild.name} • Event End Time Update`,
                iconURL: newEvent.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Event end time update logged: ${newEvent.name}`);
            
        } catch (error) {
            console.error('Error in eventsEndTimeUpdate audit log:', error);
        }
    }
};
