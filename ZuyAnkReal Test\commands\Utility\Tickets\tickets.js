function _0x5b6e(_0x2e234a,_0x109a0b){const _0x24f74c=_0x24f7();return _0x5b6e=function(_0x5b6ecf,_0x2ed5d2){_0x5b6ecf=_0x5b6ecf-0x1b4;let _0x6864e3=_0x24f74c[_0x5b6ecf];return _0x6864e3;},_0x5b6e(_0x2e234a,_0x109a0b);}(function(_0x4c23a2,_0x454d81){function _0x4b0f95(_0x4d69de,_0x44c044){return _0x5b6e(_0x4d69de- -0x334,_0x44c044);}const _0x33e039=_0x4c23a2();while(!![]){try{const _0x9288d7=parseInt(_0x4b0f95(-0x116,-0x143))/0x1*(-parseInt(_0x4b0f95(-0x43,-0xaf))/0x2)+parseInt(_0x4b0f95(-0x15b,-0x11d))/0x3+-parseInt(_0x4b0f95(-0x14c,-0x120))/0x4*(-parseInt(_0x4b0f95(-0xc9,-0xb6))/0x5)+parseInt(_0x4b0f95(-0x133,-0x165))/0x6*(parseInt(_0x4b0f95(-0xcb,-0x12c))/0x7)+-parseInt(_0x4b0f95(-0x10d,-0xad))/0x8*(parseInt(_0x4b0f95(-0x126,-0x1b4))/0x9)+parseInt(_0x4b0f95(-0xf7,-0x89))/0xa+-parseInt(_0x4b0f95(-0xc0,-0xf7))/0xb*(parseInt(_0x4b0f95(-0x97,-0x105))/0xc);if(_0x9288d7===_0x454d81)break;else _0x33e039['push'](_0x33e039['shift']());}catch(_0x549321){_0x33e039['push'](_0x33e039['shift']());}}}(_0x24f7,0x3ce6e));const {SlashCommandBuilder,EmbedBuilder,ActionRowBuilder,ButtonBuilder,StringSelectMenuBuilder,PermissionFlagsBits,ButtonStyle}=require(_0xfcbd19(0x2a0,0x33e)),fs=require('fs'),yaml=require(_0xfcbd19(0x2eb,0x352)),path=require('path'),moment=require(_0xfcbd19(0x1ff,0x227));function _0xfcbd19(_0x45349e,_0x3f58ae){return _0x5b6e(_0x45349e-0xe,_0x3f58ae);}const Ticket=require(_0xfcbd19(0x2e6,0x336)),Blacklist=require(_0xfcbd19(0x228,0x1e1)),{handleTicketClose}=require('../../../events/interactionCreate'),configPath=path[_0xfcbd19(0x295,0x24a)](__dirname,'../../../config.yml'),langPath=path[_0xfcbd19(0x295,0x2d1)](__dirname,_0xfcbd19(0x20e,0x28a)),config=yaml[_0xfcbd19(0x2ad,0x2c9)](fs['readFileSync'](configPath,_0xfcbd19(0x1fe,0x252))),lang=yaml[_0xfcbd19(0x2ad,0x272)](fs[_0xfcbd19(0x1f5,0x202)](langPath,_0xfcbd19(0x1fe,0x279)));function parseDuration(_0xab6581){const _0x1f9b6a={'s':0x3e8,'m':0x3e8*0x3c,'h':0x3e8*0x3c*0x3c,'d':0x3e8*0x3c*0x3c*0x18,'w':0x3e8*0x3c*0x3c*0x18*0x7},_0x4ee77c=_0xab6581['match'](/(\d+\s*[smhdw])/g);if(!_0x4ee77c)return 0x0;return _0x4ee77c['reduce']((_0x52e7e1,_0x2d7c0e)=>{const _0x1d47ca=parseInt(_0x2d7c0e[_0x38b7ce(0xa8,0x109)](/\d+/)[0x0]),_0x26fd0a=_0x2d7c0e[_0x38b7ce(0xfd,0x109)](/[smhdw]/)[0x0];function _0x38b7ce(_0x4380a3,_0x240e52){return _0x5b6e(_0x240e52- -0x11c,_0x4380a3);}return _0x52e7e1+_0x1d47ca*_0x1f9b6a[_0x26fd0a];},0x0);}function isValidHttpUrl(_0x311dbd){function _0x358e68(_0x14a4c8,_0x4f9792){return _0xfcbd19(_0x14a4c8- -0xf7,_0x4f9792);}try{const _0x4528e8=new URL(_0x311dbd);return _0x4528e8['protocol']===_0x358e68(0x1ca,0x14e)||_0x4528e8['protocol']==='https:';}catch(_0x7a313f){return![];}}function hasSupportRole(_0x14ca9e,_0x18fbe9){function _0x5dbdf8(_0x1fc9e1,_0x135441){return _0xfcbd19(_0x1fc9e1- -0x24c,_0x135441);}return _0x14ca9e[_0x5dbdf8(0x7e,0x86)][_0x5dbdf8(0x86,0xeb)][_0x5dbdf8(-0x8a,-0xce)](_0x10ebfb=>_0x18fbe9[_0x5dbdf8(0x1,0x21)](_0x10ebfb['id']));}function formatDuration(_0x5497a8){const _0x15524c=moment[_0x404554(0x4cb,0x44f)](_0x5497a8),_0x19511b=_0x15524c[_0x404554(0x51e,0x4af)](),_0x30601b=_0x15524c[_0x404554(0x4af,0x4ab)]();function _0x404554(_0x50f3ee,_0xbe3eba){return _0xfcbd19(_0xbe3eba-0x286,_0x50f3ee);}const _0x449545=_0x15524c[_0x404554(0x518,0x504)](),_0x245f8c=_0x15524c[_0x404554(0x551,0x56a)]();if(_0x19511b>0x0)return _0x19511b+'d\x20'+_0x30601b+'h\x20'+_0x449545+'m';else{if(_0x30601b>0x0)return _0x30601b+'h\x20'+_0x449545+'m';else return _0x449545>0x0?_0x449545+'m\x20'+_0x245f8c+'s':_0x245f8c+'s';}}async function getUserPriority(_0x352420){const _0x368b46=config[_0x3446da(-0xbf,-0xcb)];function _0x3446da(_0x5df37e,_0x3c8bb9){return _0xfcbd19(_0x5df37e- -0x34a,_0x3c8bb9);}if(!_0x368b46['Enabled'])return _0x368b46[_0x3446da(-0x157,-0x1c7)];for(const _0x2e0666 of Object[_0x3446da(-0xf8,-0xf5)](_0x368b46[_0x3446da(-0x16d,-0xf5)])){const _0x3b7034=_0x368b46['Levels'][_0x2e0666];for(const _0xb3876b of _0x3b7034[_0x3446da(-0x102,-0x179)]){if(_0x352420['roles'][_0x3446da(-0x78,-0xeb)]['has'](_0xb3876b))return _0x2e0666;}}return _0x368b46[_0x3446da(-0x157,-0xe2)];}const replacePlaceholders=(_0x183715,_0x5405dd)=>{if(!_0x183715)return'';function _0x5805e0(_0x2c1a6f,_0x2d4f6a){return _0xfcbd19(_0x2d4f6a- -0x34b,_0x2c1a6f);}return _0x183715[_0x5805e0(-0x1c6,-0x16d)](_0x5805e0(-0x103,-0xf3),_0x5405dd[_0x5805e0(-0x10b,-0x10b)])[_0x5805e0(-0xe5,-0x16d)]('{workinghours_end}',_0x5405dd[_0x5805e0(-0x2c,-0xa7)])['replace'](_0x5805e0(-0x15,-0x9f),_0x5405dd[_0x5805e0(-0x9c,-0x66)])[_0x5805e0(-0xd1,-0x16d)]('{workinghours_end_monday}',_0x5405dd[_0x5805e0(-0x8e,-0xec)])[_0x5805e0(-0xe4,-0x16d)](_0x5805e0(-0x4f,-0xd1),_0x5405dd[_0x5805e0(-0x102,-0x13b)])[_0x5805e0(-0xfd,-0x16d)]('{workinghours_end_tuesday}',_0x5405dd[_0x5805e0(-0x12d,-0xea)])[_0x5805e0(-0x1b7,-0x16d)](_0x5805e0(-0x106,-0xb1),_0x5405dd[_0x5805e0(-0x158,-0xfa)])[_0x5805e0(-0x197,-0x16d)](_0x5805e0(-0x93,-0x53),_0x5405dd[_0x5805e0(-0x189,-0x178)])[_0x5805e0(-0x104,-0x16d)](_0x5805e0(-0x18d,-0x15d),_0x5405dd[_0x5805e0(-0x39,-0x9a)])[_0x5805e0(-0x158,-0x16d)](_0x5805e0(-0x17f,-0x187),_0x5405dd[_0x5805e0(-0x137,-0x16c)])[_0x5805e0(-0x1c8,-0x16d)](_0x5805e0(-0xe4,-0x7c),_0x5405dd[_0x5805e0(-0x197,-0x104)])[_0x5805e0(-0x121,-0x16d)](_0x5805e0(-0x12d,-0x16b),_0x5405dd[_0x5805e0(-0x25,-0x6f)])[_0x5805e0(-0x1a8,-0x16d)](_0x5805e0(-0x1c4,-0x149),_0x5405dd[_0x5805e0(-0xbe,-0x50)])[_0x5805e0(-0x13f,-0x16d)](_0x5805e0(-0x145,-0xf4),_0x5405dd[_0x5805e0(-0x119,-0x147)])[_0x5805e0(-0x104,-0x16d)]('{workinghours_start_sunday}',_0x5405dd[_0x5805e0(-0x14c,-0x134)])[_0x5805e0(-0x1ae,-0x16d)]('{workinghours_end_sunday}',_0x5405dd[_0x5805e0(-0xbd,-0x5f)]);};async function updateChannelPermissions(_0x2f4a96,_0x5dddac,_0xa013d0){function _0xcf973b(_0x5f4656,_0x580f64){return _0xfcbd19(_0x580f64-0x288,_0x5f4656);}try{const _0x47441e=[{'id':_0x2f4a96[_0xcf973b(0x511,0x572)]['roles'][_0xcf973b(0x53c,0x4b9)]['id'],'deny':[_0xcf973b(0x46d,0x461),_0xcf973b(0x45a,0x4fa)]},{'id':_0xa013d0,'allow':[_0xcf973b(0x44a,0x461),_0xcf973b(0x4f3,0x4fa),_0xcf973b(0x57e,0x530),_0xcf973b(0x59e,0x57c),'ReadMessageHistory']}];_0x5dddac[_0xcf973b(0x51e,0x4e8)][_0xcf973b(0x401,0x457)](_0x125db5=>{const _0x3c98b0=_0x2f4a96['guild'][_0x543ad9(-0xb7,-0x151)][_0x543ad9(-0xaf,-0xa7)][_0x543ad9(-0xab,-0xc0)](_0x125db5);function _0x543ad9(_0x3d0373,_0x42b195){return _0xcf973b(_0x42b195,_0x3d0373- -0x609);}_0x3c98b0&&_0x47441e['push']({'id':_0x3c98b0['id'],'allow':['SendMessages',_0x543ad9(-0x10f,-0xcb),_0x543ad9(-0xd9,-0xb3),_0x543ad9(-0x8d,-0x123),'ReadMessageHistory']});}),await _0x2f4a96['permissionOverwrites']['set'](_0x47441e);}catch(_0x26877c){console[_0xcf973b(0x3ff,0x470)]('Error\x20updating\x20channel\x20permissions:',_0x26877c);throw new Error(_0xcf973b(0x5d1,0x537));}}async function moveChannel(_0x33dc14,_0x26b2b2){function _0x514815(_0xf2f3b0,_0x4fdd92){return _0xfcbd19(_0x4fdd92-0x3d1,_0xf2f3b0);}try{await _0x33dc14['setParent'](_0x26b2b2,{'lockPermissions':![]});}catch(_0x42a953){console['error']('Error\x20moving\x20channel:',_0x42a953);throw new Error(_0x514815(0x67f,0x5fc));}}async function renameChannel(_0x2709ca,_0x58f852){function _0x29b5ba(_0x2b86b4,_0x545592){return _0xfcbd19(_0x545592- -0xde,_0x2b86b4);}try{await _0x2709ca[_0x29b5ba(0x1de,0x1a2)](_0x58f852);}catch(_0x11097f){console['error'](_0x29b5ba(0x1b0,0x134),_0x11097f);throw new Error(_0x29b5ba(0x291,0x204));}}module[_0xfcbd19(0x304,0x366)]={'data':new SlashCommandBuilder()['setName'](_0xfcbd19(0x2f6,0x29d))['setDescription']('Various\x20ticket\x20commands\x20for\x20managing\x20tickets')[_0xfcbd19(0x297,0x287)](_0x5374db=>_0x5374db[_0xfcbd19(0x280,0x253)]('add')[_0xfcbd19(0x27b,0x204)](_0xfcbd19(0x209,0x21c))['addUserOption'](_0x2a725d=>_0x2a725d[_0xfcbd19(0x280,0x25c)](_0xfcbd19(0x250,0x285))[_0xfcbd19(0x27b,0x304)](_0xfcbd19(0x2c0,0x2ee))[_0xfcbd19(0x25e,0x23f)](!![])))[_0xfcbd19(0x297,0x245)](_0x16a2f9=>_0x16a2f9['setName'](_0xfcbd19(0x294,0x30d))[_0xfcbd19(0x27b,0x241)](_0xfcbd19(0x291,0x25b)))[_0xfcbd19(0x2b3,0x263)](_0x2152ab=>_0x2152ab[_0xfcbd19(0x280,0x306)](_0xfcbd19(0x1ce,0x1c6))['setDescription']('Manage\x20the\x20blacklist')[_0xfcbd19(0x297,0x2f6)](_0x38fd66=>_0x38fd66['setName']('add')[_0xfcbd19(0x27b,0x313)](_0xfcbd19(0x2e0,0x30f))[_0xfcbd19(0x1dc,0x13e)](_0x2ecad2=>_0x2ecad2['setName'](_0xfcbd19(0x250,0x21f))[_0xfcbd19(0x27b,0x2a4)]('The\x20user\x20to\x20blacklist')[_0xfcbd19(0x25e,0x22e)](!![]))[_0xfcbd19(0x284,0x260)](_0x510e69=>_0x510e69['setName'](_0xfcbd19(0x267,0x25d))[_0xfcbd19(0x27b,0x28a)]('Reason\x20for\x20blacklisting')['setRequired'](![])))[_0xfcbd19(0x297,0x2a4)](_0x46e544=>_0x46e544['setName'](_0xfcbd19(0x2b0,0x344))[_0xfcbd19(0x27b,0x288)]('View\x20the\x20blacklist\x20reason\x20for\x20a\x20user')['addUserOption'](_0xcd8192=>_0xcd8192['setName']('user')['setDescription'](_0xfcbd19(0x24f,0x1cf))[_0xfcbd19(0x25e,0x273)](!![])))[_0xfcbd19(0x297,0x292)](_0x1da686=>_0x1da686[_0xfcbd19(0x280,0x23b)](_0xfcbd19(0x232,0x237))[_0xfcbd19(0x27b,0x223)](_0xfcbd19(0x20a,0x1bd))[_0xfcbd19(0x1dc,0x25e)](_0x4a7022=>_0x4a7022[_0xfcbd19(0x280,0x271)](_0xfcbd19(0x250,0x207))[_0xfcbd19(0x27b,0x259)](_0xfcbd19(0x2b8,0x303))[_0xfcbd19(0x25e,0x2fc)](!![]))))[_0xfcbd19(0x297,0x26b)](_0x22fed9=>_0x22fed9[_0xfcbd19(0x280,0x28b)](_0xfcbd19(0x2ed,0x262))[_0xfcbd19(0x27b,0x244)](_0xfcbd19(0x301,0x394)))[_0xfcbd19(0x297,0x236)](_0x12f90f=>_0x12f90f[_0xfcbd19(0x280,0x302)](_0xfcbd19(0x29d,0x32e))['setDescription'](_0xfcbd19(0x2c4,0x314))[_0xfcbd19(0x284,0x26b)](_0x227d0f=>{_0x227d0f[_0x14bca5(0x373,0x2e6)](_0x14bca5(0x390,0x337))[_0x14bca5(0x36e,0x301)](_0x14bca5(0x2ec,0x335))[_0x14bca5(0x351,0x361)](!![]),Object['keys'](config[_0x14bca5(0x361,0x2ec)])[_0x14bca5(0x2c2,0x2ba)](_0x47c330=>{function _0xa97b94(_0x5d4937,_0x26390d){return _0x14bca5(_0x26390d- -0x146,_0x5d4937);}_0x227d0f[_0xa97b94(0x199,0x1b5)]({'name':_0x47c330,'value':_0x47c330});});function _0x14bca5(_0x5678e9,_0xce6325){return _0xfcbd19(_0x5678e9-0xf3,_0xce6325);}return _0x227d0f;}))[_0xfcbd19(0x297,0x232)](_0xbf86e3=>_0xbf86e3[_0xfcbd19(0x280,0x296)](_0xfcbd19(0x232,0x2c4))[_0xfcbd19(0x27b,0x249)](_0xfcbd19(0x278,0x299))['addUserOption'](_0x8847e=>_0x8847e['setName'](_0xfcbd19(0x250,0x2b5))[_0xfcbd19(0x27b,0x2d9)](_0xfcbd19(0x2dd,0x2b1))['setRequired'](!![])))[_0xfcbd19(0x297,0x200)](_0x4abe02=>_0x4abe02[_0xfcbd19(0x280,0x2d6)](_0xfcbd19(0x23a,0x233))[_0xfcbd19(0x27b,0x2aa)](_0xfcbd19(0x2f7,0x2bd))[_0xfcbd19(0x284,0x254)](_0x4d824d=>_0x4d824d[_0xfcbd19(0x280,0x2a5)](_0xfcbd19(0x23b,0x2b7))[_0xfcbd19(0x27b,0x1e0)](_0xfcbd19(0x2a2,0x333))[_0xfcbd19(0x25e,0x296)](!![])))['addSubcommand'](_0x32d832=>_0x32d832[_0xfcbd19(0x280,0x25b)](_0xfcbd19(0x227,0x1b8))[_0xfcbd19(0x27b,0x25f)](_0xfcbd19(0x268,0x1f3)))[_0xfcbd19(0x297,0x205)](_0x1edb85=>_0x1edb85[_0xfcbd19(0x280,0x2fe)](_0xfcbd19(0x2d1,0x291))[_0xfcbd19(0x27b,0x23d)](_0xfcbd19(0x2fe,0x335))[_0xfcbd19(0x284,0x2e2)](_0x4a61aa=>_0x4a61aa[_0xfcbd19(0x280,0x314)](_0xfcbd19(0x2fa,0x275))[_0xfcbd19(0x27b,0x2ee)](_0xfcbd19(0x28e,0x225))[_0xfcbd19(0x25e,0x244)](!![])['addChoices'](Object[_0xfcbd19(0x252,0x2bb)](config['TicketTypes'])['map'](_0x973e2c=>({'name':config[_0xfcbd19(0x2f3,0x370)][_0x973e2c][_0xfcbd19(0x2fc,0x29f)],'value':_0x973e2c}))))),'category':_0xfcbd19(0x254,0x2f3),async 'execute'(_0x15721b){const _0x27b3b4=_0x15721b[_0x5fad3c(0x46,-0x5a)][_0x5fad3c(0x53,-0xf)]();function _0x5fad3c(_0x38073c,_0x49929e){return _0xfcbd19(_0x38073c- -0x26c,_0x49929e);}const _0x5f48d4=_0x15721b['options'][_0x5fad3c(0x5,-0x6c)](![]);if(_0x27b3b4===_0x5fad3c(-0x89,-0x128)&&!_0x5f48d4)try{const _0x23ac9f=_0x15721b[_0x5fad3c(0x46,-0x4)][_0x5fad3c(-0x53,0x34)](_0x5fad3c(-0x1c,0x56)),_0x36015c=await Ticket[_0x5fad3c(-0x11,0x48)]({'channelId':_0x15721b[_0x5fad3c(0x7,-0x34)]['id']});if(!_0x36015c)return _0x15721b['reply']({'content':_0x5fad3c(0x3b,0xd),'ephemeral':!![]});const _0x466f67=config[_0x5fad3c(0x87,0x74)][_0x36015c[_0x5fad3c(-0x19,0x34)]][_0x5fad3c(-0xc,0x62)],_0x55c3e6=_0x15721b[_0x5fad3c(-0x86,-0x22)][_0x5fad3c(0x5e,0xaa)]['cache']['some'](_0x2e3c59=>_0x466f67['includes'](_0x2e3c59['id']));if(!_0x55c3e6)return _0x15721b[_0x5fad3c(0x27,0xa3)]({'content':_0x5fad3c(-0xa2,-0x117),'ephemeral':!![]});const _0x4b8687=_0x15721b[_0x5fad3c(0x7,0x51)],_0x58a369=_0x4b8687[_0x5fad3c(-0x42,0x35)](_0x23ac9f);if(_0x58a369&&_0x58a369['has'](PermissionFlagsBits[_0x5fad3c(0x6,0x5a)],![]))return _0x15721b[_0x5fad3c(0x27,0x61)]({'content':_0x5fad3c(-0xa5,-0x143),'ephemeral':!![]});await _0x4b8687['permissionOverwrites'][_0x5fad3c(-0xa9,-0x9c)](_0x23ac9f,{'ViewChannel':!![],'SendMessages':!![],'ReadMessageHistory':!![],'AttachFiles':!![],'EmbedLinks':!![]});const _0x185620=new EmbedBuilder()['setColor'](_0x5fad3c(-0x67,-0x94))[_0x5fad3c(0xf,0x66)](_0x5fad3c(-0x7a,0x8)+_0x23ac9f['tag']+_0x5fad3c(0x5a,0x2c));await _0x15721b[_0x5fad3c(0x27,-0xf)]({'embeds':[_0x185620],'ephemeral':!![]});const _0x55dc04=new EmbedBuilder()['setColor']('#00FF00')['setDescription']('<@'+_0x23ac9f['id']+_0x5fad3c(-0x5f,-0xf7)+_0x15721b[_0x5fad3c(-0x1c,0x6d)]['id']+'>.');await _0x4b8687['send']({'embeds':[_0x55dc04]});}catch(_0x578a67){console[_0x5fad3c(-0x84,-0xdb)](_0x5fad3c(-0x4d,0x4a),_0x578a67),await _0x15721b[_0x5fad3c(0x27,0x14)]({'content':_0x5fad3c(0x5f,0x7),'ephemeral':!![]});}else{if(_0x27b3b4==='alert'&&!_0x5f48d4){const _0x5b566f=config[_0x5fad3c(0x87,0x114)]['TicketType1'][_0x5fad3c(-0xc,-0x29)];if(!_0x15721b[_0x5fad3c(-0x86,-0x68)][_0x5fad3c(0x5e,-0x18)][_0x5fad3c(0x66,0x6d)][_0x5fad3c(-0xaa,-0x7a)](_0x4cb91f=>_0x5b566f['includes'](_0x4cb91f['id'])))return _0x15721b['reply']({'content':_0x5fad3c(-0x34,-0x66),'ephemeral':!![]});if(!config[_0x5fad3c(0x82,0x97)]['Enabled'])return _0x15721b['reply']({'content':'Alert\x20feature\x20is\x20disabled.','ephemeral':!![]});const _0x4e8172=await Ticket[_0x5fad3c(-0x11,-0x5c)]({'channelId':_0x15721b[_0x5fad3c(0x7,0x71)]['id']});if(!_0x4e8172)return _0x15721b[_0x5fad3c(0x27,-0x34)]({'content':_0x5fad3c(-0x7f,-0xf0),'ephemeral':!![]});const _0x42e148=config[_0x5fad3c(0x82,-0xa)][_0x5fad3c(0x26,0x48)],_0xf2e3e1=parseDuration(_0x42e148),_0x50b3d5=new Date(Date[_0x5fad3c(-0x4e,0x12)]()+_0xf2e3e1),_0x28a4b6=_0x5fad3c(-0xa4,-0x136)+Math['floor'](_0x50b3d5[_0x5fad3c(0x15,-0x7e)]()/0x3e8)+':R>';_0x4e8172['alertTime']=_0x50b3d5,await _0x4e8172[_0x5fad3c(-0x8b,-0x10b)]();const _0x4bb6f5=config[_0x5fad3c(0x82,0xad)][_0x5fad3c(0x52,-0x28)],_0x262b21=new EmbedBuilder()[_0x5fad3c(0xf,0x8d)](_0x4bb6f5['Description'][_0x5fad3c(0x29,-0x5c)]('\x0a')[_0x5fad3c(-0x8e,-0x107)](_0x5fad3c(-0x95,-0x12d),'<@'+_0x4e8172[_0x5fad3c(0x94,0x6f)]+'>')[_0x5fad3c(-0x8e,-0x4e)](_0x5fad3c(-0x16,-0xad),_0x28a4b6));if(_0x4bb6f5[_0x5fad3c(-0x78,-0x40)])_0x262b21[_0x5fad3c(0x13,0x23)](_0x4bb6f5[_0x5fad3c(-0x78,-0x36)]);if(_0x4bb6f5[_0x5fad3c(-0x12,-0x26)])_0x262b21[_0x5fad3c(-0x1,0x34)](_0x4bb6f5['Color']);_0x4bb6f5['Footer']&&_0x4bb6f5[_0x5fad3c(-0x20,-0x5d)][_0x5fad3c(-0x35,-0x5)]&&_0x262b21[_0x5fad3c(-0x6,-0x42)]({'text':_0x4bb6f5['Footer'][_0x5fad3c(-0x35,-0xa)],'iconURL':_0x4bb6f5[_0x5fad3c(-0x20,0x44)]['Icon']||null});if(_0x4bb6f5['Image'])_0x262b21['setImage'](_0x4bb6f5[_0x5fad3c(-0x83,-0xb5)]);if(_0x4bb6f5['Thumbnail'])_0x262b21[_0x5fad3c(0x4d,0xe3)](_0x4bb6f5['Thumbnail']);const _0xc724e8=new ButtonBuilder()[_0x5fad3c(-0x2b,-0x61)](_0x5fad3c(0x42,0x46)+_0x4e8172[_0x5fad3c(-0x70,-0x1b)])['setLabel'](lang[_0x5fad3c(-0x6f,-0x7b)][_0x5fad3c(-0x56,-0xeb)])[_0x5fad3c(0x83,0x6d)](ButtonStyle[_0x5fad3c(-0x60,-0x6e)])[_0x5fad3c(-0x28,-0x8f)]('🔒'),_0x194e5f=new ActionRowBuilder()[_0x5fad3c(-0x88,-0x2c)](_0xc724e8),_0x4a7871=await _0x15721b['channel'][_0x5fad3c(0x30,0xc0)]('<@'+_0x4e8172['userId']+'>'),_0x433af4=await _0x15721b['channel'][_0x5fad3c(0x30,-0x10)]({'embeds':[_0x262b21],'components':[_0x194e5f]});await _0x15721b['reply']({'content':_0x5fad3c(-0x3d,-0x28),'ephemeral':!![]}),setTimeout(()=>_0x4a7871[_0x5fad3c(-0x82,-0x79)](),0x1f4),_0x4e8172['alertMessageId']=_0x433af4['id'],await _0x4e8172[_0x5fad3c(-0x8b,-0x122)]();}else{if(_0x5f48d4===_0x5fad3c(-0x9e,-0x32)){if(_0x27b3b4===_0x5fad3c(-0x89,0xa))try{const _0x5c635f=_0x15721b[_0x5fad3c(0x46,-0x42)][_0x5fad3c(-0x53,-0x73)](_0x5fad3c(-0x1c,0x71)),_0x399631=_0x15721b[_0x5fad3c(0x46,0x68)]['getString'](_0x5fad3c(-0x5,-0xa1))||_0x5fad3c(0x24,0x75),_0x54ffad=config['ModerationRoles'][_0x5fad3c(-0x9e,-0x72)],_0x461397=_0x15721b[_0x5fad3c(-0x86,-0xb7)][_0x5fad3c(0x5e,0x93)][_0x5fad3c(0x66,0x39)][_0x5fad3c(-0xaa,-0x148)](_0x38d022=>_0x54ffad[_0x5fad3c(-0x1f,0x55)](_0x38d022['id']))||_0x15721b['member'][_0x5fad3c(0x3d,0xa9)]['has'](PermissionFlagsBits[_0x5fad3c(-0x58,0x48)]);if(!_0x461397)return _0x15721b[_0x5fad3c(0x27,0x2d)]({'content':_0x5fad3c(-0xa2,-0xba),'ephemeral':!![]});const _0x107dd6=await Blacklist['findOne']({'userId':_0x5c635f['id']});if(_0x107dd6)return _0x15721b['reply']({'content':_0x5c635f[_0x5fad3c(-0x8,-0x90)]+_0x5fad3c(0x73,0xc6),'ephemeral':!![]});const _0x31e47e=new Blacklist({'userId':_0x5c635f['id'],'addedBy':_0x15721b['user']['id'],'addedAt':new Date(),'reason':_0x399631});await _0x31e47e[_0x5fad3c(-0x8b,-0x56)]();const _0x4359cf=new EmbedBuilder()['setColor'](_0x5fad3c(-0x6b,-0xe8))['setTitle'](_0x5fad3c(-0x2f,-0x72))[_0x5fad3c(0xf,0x92)](_0x5fad3c(0x7c,0xef)+_0x5c635f['id']+_0x5fad3c(-0x3,-0x30))[_0x5fad3c(0x0,0x37)]({'name':'Reason','value':_0x399631,'inline':![]},{'name':_0x5fad3c(-0x13,-0x68),'value':'<@'+_0x15721b[_0x5fad3c(-0x1c,-0xbd)]['id']+'>','inline':!![]},{'name':_0x5fad3c(-0xf,-0x17),'value':_0x5fad3c(-0xa4,-0x21)+Math[_0x5fad3c(-0x96,-0x78)](new Date(_0x31e47e[_0x5fad3c(-0x9b,-0xff)])[_0x5fad3c(0x15,-0x55)]()/0x3e8)+_0x5fad3c(0x11,-0x37),'inline':!![]})[_0x5fad3c(0x4d,0xb6)](_0x5c635f[_0x5fad3c(0x2a,0xc1)]({'dynamic':!![]}))[_0x5fad3c(-0x6,0x0)]({'text':'Contact\x20an\x20admin\x20if\x20you\x20believe\x20this\x20is\x20a\x20mistake.'});await _0x15721b[_0x5fad3c(0x27,0xb9)]({'embeds':[_0x4359cf],'ephemeral':!![]});}catch(_0x2979ba){console[_0x5fad3c(-0x84,-0x68)](_0x5fad3c(-0x9a,-0xe),_0x2979ba),await _0x15721b[_0x5fad3c(0x27,0xac)]({'content':'An\x20error\x20occurred\x20while\x20blacklisting\x20the\x20user.\x20Please\x20try\x20again\x20later.','ephemeral':!![]});}else{if(_0x27b3b4===_0x5fad3c(0x44,0xcd))try{const _0x35969f=_0x15721b[_0x5fad3c(0x46,0x34)][_0x5fad3c(-0x53,-0x1a)](_0x5fad3c(-0x1c,-0x35)),_0x1850c2=config['ModerationRoles'][_0x5fad3c(-0x9e,-0xcb)],_0x474a6b=_0x15721b[_0x5fad3c(-0x86,-0xc6)][_0x5fad3c(0x5e,0xa4)][_0x5fad3c(0x66,0xcc)][_0x5fad3c(-0xaa,-0x6a)](_0x4bf3ca=>_0x1850c2[_0x5fad3c(-0x1f,0x68)](_0x4bf3ca['id']))||_0x15721b[_0x5fad3c(-0x86,0x6)]['permissions'][_0x5fad3c(0x6d,0x2d)](PermissionFlagsBits['Administrator']);if(!_0x474a6b)return _0x15721b[_0x5fad3c(0x27,-0x61)]({'content':'You\x20do\x20not\x20have\x20permissions\x20to\x20use\x20this\x20command.','ephemeral':!![]});const _0x44cb7b=await Blacklist[_0x5fad3c(-0x11,0x42)]({'userId':_0x35969f['id']});if(!_0x44cb7b)return _0x15721b['reply']({'content':_0x35969f[_0x5fad3c(-0x8,0x46)]+_0x5fad3c(-0x51,-0x51),'ephemeral':!![]});const _0x545023=new EmbedBuilder()[_0x5fad3c(-0x1,-0x30)](_0x5fad3c(-0x72,-0xd4))[_0x5fad3c(0x13,0x3f)](_0x5fad3c(0x2f,-0x53))['addFields']({'name':_0x5fad3c(-0x23,-0x8a),'value':'<@'+_0x44cb7b[_0x5fad3c(0x94,0x18)]+'>','inline':!![]},{'name':_0x5fad3c(-0x13,0x79),'value':'<@'+_0x44cb7b[_0x5fad3c(-0x9f,-0x7d)]+'>','inline':!![]},{'name':_0x5fad3c(0x37,0x8e),'value':_0x44cb7b[_0x5fad3c(-0x5,0x34)],'inline':![]},{'name':'Date','value':_0x5fad3c(-0xa4,-0xea)+Math[_0x5fad3c(-0x96,-0xfe)](new Date(_0x44cb7b['addedAt'])[_0x5fad3c(0x15,0x6a)]()/0x3e8)+_0x5fad3c(0x11,-0x70),'inline':!![]})[_0x5fad3c(0x4d,0x28)](_0x35969f[_0x5fad3c(0x2a,0x2c)]({'dynamic':!![]}))[_0x5fad3c(-0x6,-0x6b)]({'text':'Contact\x20an\x20admin\x20if\x20you\x20need\x20more\x20information.'});await _0x15721b[_0x5fad3c(0x27,0x70)]({'embeds':[_0x545023],'ephemeral':!![]});}catch(_0x37768a){console['error'](_0x5fad3c(-0xa1,-0x112),_0x37768a),await _0x15721b['reply']({'content':_0x5fad3c(-0x54,-0xdd),'ephemeral':!![]});}else{if(_0x27b3b4===_0x5fad3c(-0x3a,0x6))try{const _0x105bb2=_0x15721b[_0x5fad3c(0x46,-0x32)][_0x5fad3c(-0x53,-0x66)]('user'),_0x314982=config['ModerationRoles'][_0x5fad3c(-0x9e,-0x114)],_0x284f86=_0x15721b['member']['roles'][_0x5fad3c(0x66,0x78)][_0x5fad3c(-0xaa,-0xce)](_0x679a8=>_0x314982[_0x5fad3c(-0x1f,0x8)](_0x679a8['id']))||_0x15721b[_0x5fad3c(-0x86,-0xc6)][_0x5fad3c(0x3d,0xdc)][_0x5fad3c(0x6d,0xf9)](PermissionFlagsBits['Administrator']);if(!_0x284f86)return _0x15721b[_0x5fad3c(0x27,-0x22)]({'content':'You\x20do\x20not\x20have\x20permissions\x20to\x20use\x20this\x20command.','ephemeral':!![]});const _0x55ac91=await Blacklist[_0x5fad3c(0x4a,0x77)]({'userId':_0x105bb2['id']});if(!_0x55ac91)return _0x15721b[_0x5fad3c(0x27,0xb)]({'content':_0x105bb2['tag']+_0x5fad3c(-0x51,-0x9b),'ephemeral':!![]});const _0x249575=new EmbedBuilder()['setColor'](_0x5fad3c(-0x67,-0xa9))[_0x5fad3c(0x13,-0x82)](_0x5fad3c(-0x6c,-0xd2))[_0x5fad3c(0xf,-0x39)](_0x5fad3c(0x7c,0x106)+_0x105bb2['id']+_0x5fad3c(-0x1e,0x4b))[_0x5fad3c(0x0,-0x92)]({'name':'Removed\x20By','value':'<@'+_0x15721b[_0x5fad3c(-0x1c,-0x9)]['id']+'>','inline':!![]},{'name':_0x5fad3c(-0xf,0x27),'value':'<t:'+Math[_0x5fad3c(-0x96,-0x52)](new Date()[_0x5fad3c(0x15,-0x4a)]()/0x3e8)+_0x5fad3c(0x11,0x88),'inline':!![]})[_0x5fad3c(0x4d,-0x5)](_0x105bb2[_0x5fad3c(0x2a,-0x41)]({'dynamic':!![]}))[_0x5fad3c(-0x6,0x79)]({'text':_0x5fad3c(0x3a,0x6c)});await _0x15721b[_0x5fad3c(0x27,-0x36)]({'embeds':[_0x249575],'ephemeral':!![]});}catch(_0x45bacb){console[_0x5fad3c(-0x84,-0xc)](_0x5fad3c(-0x7b,-0x14),_0x45bacb),await _0x15721b[_0x5fad3c(0x27,0xbd)]({'content':_0x5fad3c(0x2c,0x45),'ephemeral':!![]});}}}}else{if(_0x27b3b4===_0x5fad3c(0x81,0x64)&&!_0x5f48d4)try{const _0x5dd7b7=await Ticket[_0x5fad3c(-0x11,-0x3c)]({'channelId':_0x15721b[_0x5fad3c(0x7,-0x4f)]['id']});if(!_0x5dd7b7)return _0x15721b['reply']({'content':_0x5fad3c(0x61,0x92),'ephemeral':!![]});await handleTicketClose(_0x15721b['client'],_0x15721b,_0x5dd7b7[_0x5fad3c(-0x70,0x23)]),await _0x15721b[_0x5fad3c(-0x33,0x6a)]({'content':_0x5fad3c(-0x7d,-0xff),'ephemeral':!![]});}catch(_0x1511c4){console[_0x5fad3c(-0x84,-0xff)]('Error\x20executing\x20/close\x20command:',_0x1511c4),_0x15721b[_0x5fad3c(-0x87,0x1b)]||_0x15721b[_0x5fad3c(-0x4b,-0x63)]?await _0x15721b[_0x5fad3c(-0x61,0x39)]({'content':_0x5fad3c(-0x30,0x28),'ephemeral':!![]}):await _0x15721b['reply']({'content':'An\x20error\x20occurred\x20while\x20closing\x20the\x20ticket.\x20Please\x20try\x20again\x20later.','ephemeral':!![]});}else{if(_0x27b3b4===_0x5fad3c(0x31,-0x46)&&!_0x5f48d4)try{if(!config[_0x5fad3c(0x9,-0x38)]?.['Enabled'])return _0x15721b[_0x5fad3c(0x27,-0x5e)]({'content':_0x5fad3c(-0x46,-0xdb),'ephemeral':!![]});if(!_0x15721b[_0x5fad3c(-0x86,-0x4b)]['permissions']['has'](PermissionFlagsBits['Administrator']))return _0x15721b[_0x5fad3c(0x27,0xc8)]({'content':_0x5fad3c(-0xa2,-0xc9),'ephemeral':!![]});const _0x5f2bff=_0x15721b[_0x5fad3c(0x46,0x67)][_0x5fad3c(0x6e,0x68)](_0x5fad3c(0x31,0xae)),_0x11ecee=config[_0x5fad3c(0x2,0x6e)][_0x5f2bff];if(!_0x11ecee)return _0x15721b[_0x5fad3c(0x27,0x86)]({'content':_0x5fad3c(0x7b,0x74),'ephemeral':!![]});const _0x278432=_0x11ecee[_0x5fad3c(0x52,0x27)],_0x4debda=moment()['tz'](config[_0x5fad3c(0x3,0x4e)][_0x5fad3c(0x39,0x0)]),_0x250ebb=_0x4debda['format'](_0x5fad3c(-0x27,-0xbf))['toLowerCase'](),_0x705989=config['WorkingHours'][_0x5fad3c(0x67,0x38)][_0x250ebb[_0x5fad3c(-0x57,0x36)](0x0)[_0x5fad3c(-0x10,0x33)]()+_0x250ebb[_0x5fad3c(0x51,0x5b)](0x1)],_0x2e4b08={};if(_0x705989){const [_0x45e00c,_0x5aea97]=_0x705989[_0x5fad3c(-0x98,-0x33)]('-');_0x2e4b08[_0x5fad3c(-0x2c,-0x27)]=_0x5fad3c(-0xa4,-0x44)+moment['tz'](_0x45e00c,'HH:mm',config[_0x5fad3c(0x3,0x1d)][_0x5fad3c(0x39,0x85)])[_0x5fad3c(0x96,0xb)]()+_0x5fad3c(0x10,-0x62),_0x2e4b08['end']=_0x5fad3c(-0xa4,-0xab)+moment['tz'](_0x5aea97,_0x5fad3c(-0x49,-0x3d),config[_0x5fad3c(0x3,-0x20)]['Timezone'])[_0x5fad3c(0x96,0xb1)]()+_0x5fad3c(0x10,-0x12),Object[_0x5fad3c(-0x1a,-0x35)](config[_0x5fad3c(0x3,-0x16)][_0x5fad3c(0x67,0xeb)])[_0x5fad3c(-0x9d,-0xb2)](_0x54a30b=>{const [_0x37ad0b,_0x1c9a6e]=config[_0x490b43(0x1de,0x1c2)][_0x490b43(0x1cb,0x226)][_0x54a30b][_0x490b43(0x10b,0x127)]('-');_0x2e4b08[_0x54a30b[_0x490b43(0x1da,0x14e)]()+_0x490b43(0x22f,0x1da)]=_0x490b43(0xab,0x11b)+moment['tz'](_0x37ad0b,_0x490b43(0x191,0x176),config['WorkingHours']['Timezone'])[_0x490b43(0x268,0x255)]()+':t>';function _0x490b43(_0x5260be,_0x4eb528){return _0x5fad3c(_0x4eb528-0x1bf,_0x5260be);}_0x2e4b08[_0x54a30b[_0x490b43(0xae,0x14e)]()+_0x490b43(0x18c,0x12e)]=_0x490b43(0xb8,0x11b)+moment['tz'](_0x1c9a6e,_0x490b43(0x107,0x176),config[_0x490b43(0x1b8,0x1c2)][_0x490b43(0x248,0x1f8)])[_0x490b43(0x1c6,0x255)]()+_0x490b43(0x23b,0x1cf);});}const _0x5cb964=new EmbedBuilder()[_0x5fad3c(-0x1,0x3f)](_0x278432['Color']||_0x5fad3c(0x86,0xcf))[_0x5fad3c(0xf,-0x81)](replacePlaceholders((_0x278432[_0x5fad3c(-0x3f,-0xc5)]||[])[_0x5fad3c(0x29,0x58)]('\x0a'),_0x2e4b08));_0x278432[_0x5fad3c(-0x78,-0x9c)]&&_0x5cb964[_0x5fad3c(0x13,0x2f)](replacePlaceholders(_0x278432[_0x5fad3c(-0x78,-0xe4)],_0x2e4b08));(_0x278432[_0x5fad3c(-0x20,-0x26)]?.['Text']||_0x278432[_0x5fad3c(-0x20,0x19)]?.[_0x5fad3c(0x77,0x3c)])&&_0x5cb964['setFooter']({'text':replacePlaceholders(_0x278432[_0x5fad3c(-0x20,0x22)][_0x5fad3c(-0x35,0x20)]||'',_0x2e4b08),'iconURL':_0x278432[_0x5fad3c(-0x20,-0x36)]['Icon']||null});(_0x278432[_0x5fad3c(0x84,0xc7)]?.[_0x5fad3c(-0x35,-0xba)]||_0x278432[_0x5fad3c(0x84,0x8c)]?.[_0x5fad3c(0x77,0xec)])&&_0x5cb964['setAuthor']({'name':replacePlaceholders(_0x278432['Author'][_0x5fad3c(-0x35,0x39)]||'',_0x2e4b08),'iconURL':_0x278432[_0x5fad3c(0x84,-0x5)]['Icon']||null});if(isValidHttpUrl(_0x278432[_0x5fad3c(-0x83,-0xfa)]))_0x5cb964['setImage'](_0x278432['Image']);if(isValidHttpUrl(_0x278432[_0x5fad3c(0x89,0x4b)]))_0x5cb964[_0x5fad3c(0x4d,0xb0)](_0x278432[_0x5fad3c(0x89,0xc)]);_0x278432['embedFields']&&Array[_0x5fad3c(-0x75,-0x106)](_0x278432[_0x5fad3c(0x68,0x4)])&&_0x278432['embedFields'][_0x5fad3c(-0x9d,-0xb)](_0x45c5cd=>{const _0x5b0ea3=replacePlaceholders(_0x45c5cd[_0x3aeaaa(0x460,0x3e8)],_0x2e4b08),_0x14af1e=replacePlaceholders(_0x45c5cd[_0x3aeaaa(0x4f3,0x588)],_0x2e4b08);function _0x3aeaaa(_0x550d99,_0x54f777){return _0x5fad3c(_0x550d99-0x491,_0x54f777);}_0x5b0ea3&&_0x14af1e&&_0x5cb964[_0x3aeaaa(0x491,0x49a)]({'name':_0x5b0ea3,'value':_0x14af1e,'inline':_0x45c5cd[_0x3aeaaa(0x522,0x54a)]||![]});});const {useSelectMenu:_0x2f0031}=config['TicketSettings'],_0x3e6368=[];let _0x27b42c=new ActionRowBuilder();if(_0x2f0031){const _0x5656b9=new StringSelectMenuBuilder()[_0x5fad3c(-0x2b,-0x4f)](_0x5fad3c(0x32,0xcf))['setPlaceholder']('Select\x20a\x20ticket\x20type');Object[_0x5fad3c(-0x1a,-0x8c)](config['TicketTypes'])['forEach'](_0x4b17fd=>{function _0x3c3b5c(_0x22cc3b,_0x3fc0be){return _0x5fad3c(_0x3fc0be- -0x21,_0x22cc3b);}const _0x2cf992=config[_0x3c3b5c(0xce,0x66)][_0x4b17fd];_0x2cf992['Enabled']&&_0x2cf992[_0x3c3b5c(-0x1b,-0x1d)]===_0x5f2bff&&_0x5656b9[_0x3c3b5c(-0x5d,-0x87)]({'label':replacePlaceholders(_0x2cf992[_0x3c3b5c(0x7,-0x7)]['Name']||_0x3c3b5c(0x6e,0x2),_0x2e4b08),'emoji':_0x2cf992[_0x3c3b5c(0x39,-0x7)][_0x3c3b5c(-0x71,-0x47)]||'','value':_0x4b17fd,'description':replacePlaceholders(_0x2cf992[_0x3c3b5c(0xb,-0x7)][_0x3c3b5c(-0x10,-0x60)]||'',_0x2e4b08)});});if(_0x5656b9['options'][_0x5fad3c(-0x7,0x2d)]>0x0)_0x27b42c['addComponents'](_0x5656b9),_0x3e6368[_0x5fad3c(-0x80,-0xfe)](_0x27b42c);else throw new Error('No\x20ticket\x20types\x20are\x20enabled\x20for\x20this\x20panel.');}else{Object['keys'](config[_0x5fad3c(0x87,0x101)])[_0x5fad3c(-0x9d,-0x45)](_0x40f12b=>{const _0x10aba9=config[_0x101a5d(0x333,0x374)][_0x40f12b];function _0x101a5d(_0x115d70,_0x2ce724){return _0x5fad3c(_0x2ce724-0x2ed,_0x115d70);}if(_0x10aba9['Enabled']&&_0x10aba9['Panel']===_0x5f2bff){const _0xfa4420={'Primary':ButtonStyle[_0x101a5d(0x321,0x344)],'Secondary':ButtonStyle[_0x101a5d(0x3bf,0x34a)],'Success':ButtonStyle['Success'],'Danger':ButtonStyle[_0x101a5d(0x204,0x28d)]},_0x2d4c9d=_0xfa4420[_0x10aba9[_0x101a5d(0x2ed,0x307)]['Style']]||ButtonStyle[_0x101a5d(0x2ba,0x344)];_0x27b42c['addComponents'](new ButtonBuilder()['setCustomId']('ticketcreate-'+_0x40f12b)[_0x101a5d(0x2e3,0x320)](replacePlaceholders(_0x10aba9[_0x101a5d(0x30f,0x307)]['Name']||_0x101a5d(0x274,0x310),_0x2e4b08))['setEmoji'](_0x10aba9[_0x101a5d(0x331,0x307)][_0x101a5d(0x2a6,0x2c7)]||'')[_0x101a5d(0x3c0,0x370)](_0x2d4c9d)),_0x27b42c[_0x101a5d(0x31e,0x356)][_0x101a5d(0x25b,0x2e6)]===0x5&&(_0x3e6368[_0x101a5d(0x215,0x26d)](_0x27b42c),_0x27b42c=new ActionRowBuilder());}});_0x27b42c[_0x5fad3c(0x69,-0x6)][_0x5fad3c(-0x7,0x7b)]>0x0&&_0x3e6368[_0x5fad3c(-0x80,-0x19)](_0x27b42c);if(_0x3e6368[_0x5fad3c(-0x7,0x8a)]===0x0)throw new Error(_0x5fad3c(0x49,0xb));}await _0x15721b[_0x5fad3c(0x27,-0x5e)]({'content':_0x5fad3c(0x75,0xd0),'ephemeral':!![]}),await _0x15721b[_0x5fad3c(0x7,0x47)]['send']({'embeds':[_0x5cb964],'components':_0x3e6368});}catch(_0x12acf3){console[_0x5fad3c(-0x84,-0x14)]('Error\x20sending\x20ticket\x20panel:',_0x12acf3),!_0x15721b[_0x5fad3c(-0x87,-0xfd)]&&!_0x15721b[_0x5fad3c(-0x4b,-0x89)]?await _0x15721b[_0x5fad3c(0x27,0x30)]({'content':_0x5fad3c(-0x4a,-0x64),'ephemeral':!![]})[_0x5fad3c(-0x9,0x8a)](_0x354364=>console[_0x5fad3c(-0x84,-0x19)](_0x5fad3c(-0xa6,-0xa0),_0x354364)):await _0x15721b[_0x5fad3c(-0x33,-0x11)]({'content':_0x5fad3c(-0x4a,0x44),'ephemeral':!![]})[_0x5fad3c(-0x9,0x67)](_0x3e503e=>console[_0x5fad3c(-0x84,-0xb5)](_0x5fad3c(0x17,0x63),_0x3e503e));}else{if(_0x27b3b4==='remove'&&!_0x5f48d4)try{const _0xbe807c=_0x15721b['options'][_0x5fad3c(-0x53,-0xce)](_0x5fad3c(-0x1c,-0x9c)),_0x7e6b1e=await Ticket['findOne']({'channelId':_0x15721b['channel']['id']});if(!_0x7e6b1e)return _0x15721b[_0x5fad3c(0x27,-0x39)]({'content':_0x5fad3c(0x3b,-0x57),'ephemeral':!![]});const _0x16f085=config['TicketTypes'][_0x7e6b1e[_0x5fad3c(-0x19,-0x28)]][_0x5fad3c(-0xc,-0x3a)],_0x2950a0=_0x15721b[_0x5fad3c(-0x86,-0x4)][_0x5fad3c(0x5e,0xb8)]['cache'][_0x5fad3c(-0xaa,-0xd8)](_0x3723ac=>_0x16f085['includes'](_0x3723ac['id']));if(!_0x2950a0)return _0x15721b[_0x5fad3c(0x27,0x24)]({'content':_0x5fad3c(-0xa2,-0x114),'ephemeral':!![]});const _0x3aedc5=_0x15721b[_0x5fad3c(0x7,-0x96)],_0x51ad76=_0x3aedc5[_0x5fad3c(-0x42,-0x29)](_0xbe807c);if(!_0x51ad76||!_0x51ad76[_0x5fad3c(0x6d,0xfb)](PermissionFlagsBits[_0x5fad3c(0x6,0x2f)]))return _0x15721b[_0x5fad3c(0x27,-0x19)]({'content':_0x5fad3c(0xa,0x9e),'ephemeral':!![]});await _0x3aedc5[_0x5fad3c(-0x92,-0x8b)][_0x5fad3c(-0xa9,-0xf2)](_0xbe807c,{'ViewChannel':![],'SendMessages':![],'ReadMessageHistory':![],'AttachFiles':![],'EmbedLinks':![]});const _0x4df5b9=new EmbedBuilder()['setColor']('#FF0000')['setDescription'](_0x5fad3c(-0xa0,-0x13f)+_0xbe807c['tag']+'\x20from\x20the\x20ticket.');await _0x15721b[_0x5fad3c(0x27,0x54)]({'embeds':[_0x4df5b9],'ephemeral':!![]});const _0x36b6bd=new EmbedBuilder()['setColor']('#FF0000')[_0x5fad3c(0xf,0xa)]('<@'+_0xbe807c['id']+_0x5fad3c(0x2d,0x6e)+_0x15721b['user']['id']+'>.');await _0x3aedc5[_0x5fad3c(0x30,-0x17)]({'embeds':[_0x36b6bd]});}catch(_0x1b5772){console[_0x5fad3c(-0x84,-0x2a)](_0x5fad3c(-0x2d,-0x64),_0x1b5772),await _0x15721b[_0x5fad3c(0x27,-0x28)]({'content':_0x5fad3c(0x8d,0x23),'ephemeral':!![]});}else{if(_0x27b3b4==='rename'&&!_0x5f48d4)try{const _0x3e2676=_0x15721b['options'][_0x5fad3c(0x6e,-0x1)](_0x5fad3c(-0x31,-0x64)),_0x5847f5=await Ticket[_0x5fad3c(-0x11,0x36)]({'channelId':_0x15721b[_0x5fad3c(0x7,-0x99)]['id']});if(!_0x5847f5)return _0x15721b[_0x5fad3c(0x27,-0x5e)]({'content':_0x5fad3c(0x3b,0x57),'ephemeral':!![]});const _0x4b7706=config['TicketTypes'][_0x5847f5['ticketType']]['SupportRole'],_0x3117db=_0x15721b[_0x5fad3c(-0x86,-0xa7)][_0x5fad3c(0x5e,0xf)][_0x5fad3c(0x66,0x54)]['some'](_0x21377f=>_0x4b7706['includes'](_0x21377f['id']));if(!_0x3117db)return _0x15721b[_0x5fad3c(0x27,-0x11)]({'content':_0x5fad3c(-0xa2,-0x28),'ephemeral':!![]});const _0x383b5d=_0x15721b['channel'];await _0x383b5d[_0x5fad3c(0x14,-0x67)](_0x3e2676);const _0x572199=new EmbedBuilder()[_0x5fad3c(-0x1,0x2e)](_0x5fad3c(-0x67,-0x106))[_0x5fad3c(0xf,0x2d)](_0x5fad3c(-0x36,-0x39)+_0x3e2676+'.');await _0x15721b[_0x5fad3c(0x27,-0x2c)]({'embeds':[_0x572199],'ephemeral':!![]});const _0x462c24=new EmbedBuilder()[_0x5fad3c(-0x1,-0x4e)](_0x5fad3c(-0x67,-0xc5))['setDescription'](_0x5fad3c(0x1e,-0x19)+_0x3e2676+_0x5fad3c(0x5b,0x40)+_0x15721b[_0x5fad3c(-0x1c,-0xb3)]['id']+'>.');await _0x383b5d['send']({'embeds':[_0x462c24]});}catch(_0x4dbe6e){console['error'](_0x5fad3c(-0x4f,0x42),_0x4dbe6e),await _0x15721b[_0x5fad3c(0x27,0x61)]({'content':_0x5fad3c(-0x97,-0x2b),'ephemeral':!![]});}else{if(_0x27b3b4===_0x5fad3c(-0x45,-0x23)&&!_0x5f48d4){if(!hasSupportRole(_0x15721b['member'],Object[_0x5fad3c(0x19,0x1)](config[_0x5fad3c(0x87,0x11a)])[_0x5fad3c(0x20,0x29)](_0x844344=>_0x844344[_0x5fad3c(-0xc,-0x77)]))){await _0x15721b['reply']({'content':_0x5fad3c(0x50,0x6a),'ephemeral':!![]});return;}try{const _0x3b3f4f=await Ticket['find']({},_0x5fad3c(-0x22,0x5a))[_0x5fad3c(0x6c,0x72)](),_0x49594b=_0x3b3f4f[_0x5fad3c(-0x7,-0xb)],_0x5d0089=_0x3b3f4f[_0x5fad3c(0x5c,0xd4)](_0xa0b4c3=>_0xa0b4c3[_0x5fad3c(0x1,0x65)]==='open')[_0x5fad3c(-0x7,0x8b)],_0x2ed509=_0x3b3f4f[_0x5fad3c(0x5c,-0x2d)](_0x579418=>_0x579418[_0x5fad3c(0x1,0x59)]===_0x5fad3c(-0xa,0x75))['length'],_0x4107ff=_0x3b3f4f[_0x5fad3c(0x5c,0xae)](_0x53bcb3=>_0x53bcb3[_0x5fad3c(0x1,0x71)]===_0x5fad3c(0x6b,0x13))[_0x5fad3c(-0x7,-0x92)],_0x1180c1=_0x3b3f4f[_0x5fad3c(-0x29,-0x7d)]((_0x33b730,_0x3771e3)=>_0x33b730+(_0x3771e3[_0x5fad3c(-0x3c,-0x83)]||0x0),0x0),_0x37bb15=_0x3b3f4f['filter'](_0x467858=>{const _0x503ace=_0x467858[_0x387401(0xf,0x38)]['match'](/\((\d)\/5\)$/);if(_0x503ace)return _0x467858[_0x387401(0x2b,-0x1c)]=parseInt(_0x503ace[0x1]),!![];function _0x387401(_0x247fa2,_0x2b041b){return _0x5fad3c(_0x2b041b-0x80,_0x247fa2);}return![];}),_0x2396a5=_0x37bb15['length'],_0x409933=_0x2396a5>0x0?(_0x37bb15['reduce']((_0x26c44c,_0x3243c1)=>_0x26c44c+_0x3243c1['numericRating'],0x0)/_0x2396a5)['toFixed'](0x1):_0x5fad3c(0x8,0x14),_0x1a139f=_0x2396a5>0x0?'⭐'[_0x5fad3c(-0x52,0x8)](Math[_0x5fad3c(-0x96,-0x2d)](_0x409933))+'\x20`'+_0x409933+_0x5fad3c(0x4e,-0x6):_0x5fad3c(0x4b,0x7d),_0x4d4e50=_0x3b3f4f[_0x5fad3c(0x5c,0x86)](_0x5570bc=>_0x5570bc['status']===_0x5fad3c(0x6b,0xf2)&&_0x5570bc[_0x5fad3c(-0x3e,0x60)]&&_0x5570bc[_0x5fad3c(-0x65,0x9)]),_0x3736e5=_0x4d4e50[_0x5fad3c(-0x29,-0x5b)]((_0x291a01,_0x20a5e1)=>{function _0x4a0adb(_0x521d0d,_0xc3ab27){return _0x5fad3c(_0x521d0d-0x5a9,_0xc3ab27);}const _0x2cb98e=moment(_0x20a5e1[_0x4a0adb(0x544,0x4ca)]),_0x300377=moment(_0x20a5e1[_0x4a0adb(0x56b,0x4db)]);return _0x291a01+_0x300377[_0x4a0adb(0x626,0x646)](_0x2cb98e);},0x0),_0x79ace3=_0x4d4e50[_0x5fad3c(-0x7,-0x2e)]>0x0?_0x3736e5/_0x4d4e50[_0x5fad3c(-0x7,0x2c)]:0x0,_0x201e35=formatDuration(_0x79ace3),_0x2c496b=new EmbedBuilder()['setTitle'](_0x5fad3c(0x35,-0x53))[_0x5fad3c(-0x1,0x93)](_0x5fad3c(-0x67,-0xf0))[_0x5fad3c(0xf,-0x35)](_0x5fad3c(0x6f,-0x3)+_0x49594b+_0x5fad3c(-0x4c,0x56)+_0x1180c1+'`\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20>\x20Open\x20Tickets:\x20`'+_0x5d0089+'`\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20>\x20Closed\x20Tickets:\x20`'+_0x2ed509+_0x5fad3c(-0x2e,-0xd0)+_0x4107ff+_0x5fad3c(-0x8a,-0x90)+_0x201e35+_0x5fad3c(-0x5b,-0xd7)+_0x2396a5+_0x5fad3c(0x4f,0xb9)+_0x1a139f+_0x5fad3c(0x3e,0x7))[_0x5fad3c(0x72,-0x1b)]();await _0x15721b[_0x5fad3c(0x27,0x95)]({'embeds':[_0x2c496b],'ephemeral':!![]});}catch(_0x1e6178){console[_0x5fad3c(-0x84,-0xe6)]('Error\x20fetching\x20ticket\x20stats:',_0x1e6178),await _0x15721b[_0x5fad3c(0x27,0x47)]({'content':_0x5fad3c(0x56,0xe1),'ephemeral':!![]});}}else{if(_0x27b3b4===_0x5fad3c(0x65,0x9d)&&!_0x5f48d4){await _0x15721b['deferReply']({'ephemeral':!![]});try{const _0x3ff9dd=_0x15721b['options'][_0x5fad3c(0x6e,0x79)]('type'),_0x4fba6c=await Ticket[_0x5fad3c(-0x11,0x6f)]({'channelId':_0x15721b[_0x5fad3c(0x7,-0x74)]['id']});if(!_0x4fba6c)return _0x15721b[_0x5fad3c(-0x61,0x21)]({'content':_0x5fad3c(0x3b,-0x14)});const _0x1c36df=config[_0x5fad3c(0x87,0xf3)][_0x4fba6c['ticketType']],_0x41d2c8=config['TicketTypes'][_0x3ff9dd];if(!_0x41d2c8)return _0x15721b[_0x5fad3c(-0x61,-0xd3)]({'content':_0x5fad3c(0x21,0xa)});if(_0x4fba6c['ticketType']===_0x3ff9dd)return _0x15721b[_0x5fad3c(-0x61,-0xe4)]({'content':_0x5fad3c(0x64,0xd5)});const _0x265c69=_0x15721b[_0x5fad3c(-0x86,-0xd4)][_0x5fad3c(0x5e,-0x3f)][_0x5fad3c(0x66,0xac)]['some'](_0x2c3771=>_0x1c36df['SupportRole'][_0x5fad3c(-0x1f,-0x6f)](_0x2c3771['id']));if(!_0x265c69)return _0x15721b[_0x5fad3c(-0x61,-0xd3)]({'content':_0x5fad3c(-0x34,-0x97)});const _0x5ee725=await _0x15721b['guild'][_0x5fad3c(-0x7c,-0x8c)][_0x5fad3c(-0x2,0x9d)](_0x4fba6c['channelId']);if(!_0x5ee725)return _0x15721b[_0x5fad3c(-0x61,-0xed)]({'content':_0x5fad3c(0x59,0x52)});const _0x5b8a8e=await _0x15721b[_0x5fad3c(0x7e,0x11f)][_0x5fad3c(-0x94,-0x59)]['fetch'](_0x4fba6c[_0x5fad3c(0x94,0x10a)]),_0x2849fd=await getUserPriority(_0x5b8a8e),_0x424f09=_0x41d2c8[_0x5fad3c(0x48,0x72)]&&_0x41d2c8[_0x5fad3c(0x48,-0x30)]!==''?_0x41d2c8[_0x5fad3c(0x48,0x1d)]:null;try{await moveChannel(_0x5ee725,_0x424f09),await updateChannelPermissions(_0x5ee725,_0x41d2c8,_0x4fba6c[_0x5fad3c(0x94,0x37)]);}catch(_0x56f5ac){return console[_0x5fad3c(-0x84,-0xc)]('Error\x20during\x20channel\x20operations:',_0x56f5ac),_0x15721b[_0x5fad3c(-0x61,-0x2b)]({'content':'An\x20error\x20occurred\x20while\x20transferring\x20the\x20ticket.\x20Please\x20try\x20again\x20later.'});}_0x4fba6c['questions']=_0x4fba6c[_0x5fad3c(-0x81,-0x11)][_0x5fad3c(-0x38,0x4b)](_0x4bfedb=>({'question':_0x4bfedb[_0x5fad3c(-0x74,-0xb6)]||_0x5fad3c(-0x2a,-0x67),'answer':_0x4bfedb[_0x5fad3c(0x60,-0x24)]||_0x5fad3c(-0x69,-0x2b)})),_0x4fba6c[_0x5fad3c(-0x19,-0x2e)]=_0x3ff9dd,await _0x4fba6c[_0x5fad3c(-0x8b,-0x90)]();const _0x1e6dcb=_0x41d2c8[_0x5fad3c(0x85,0x7b)]['replace'](_0x5fad3c(-0xa7,-0x114),_0x4fba6c[_0x5fad3c(-0x70,-0x110)])['replace'](_0x5fad3c(-0x95,-0x7),_0x15721b[_0x5fad3c(-0x1c,0x4c)]['username'])['replace'](_0x5fad3c(-0x17,-0xd),_0x2849fd);try{await renameChannel(_0x5ee725,_0x1e6dcb);}catch(_0x4d52f0){return console['error'](_0x5fad3c(-0x59,-0x53),_0x4d52f0),_0x15721b['editReply']({'content':_0x5fad3c(-0x97,-0x4)});}const _0x5e581d=new EmbedBuilder()[_0x5fad3c(-0x1,-0x2c)](_0x5fad3c(-0x67,-0x2c))[_0x5fad3c(0xf,-0x81)](_0x5fad3c(0x1d,0x4c)+_0x41d2c8[_0x5fad3c(0x90,0x89)]+'.');await _0x15721b['editReply']({'embeds':[_0x5e581d]});const _0x1cb2f2=new EmbedBuilder()[_0x5fad3c(-0x1,0x18)]('#00FF00')[_0x5fad3c(0xf,0x25)](_0x5fad3c(0x1c,0x7c)+_0x41d2c8[_0x5fad3c(0x90,0xb7)]+_0x5fad3c(0x5b,0x42)+_0x15721b[_0x5fad3c(-0x1c,0x1a)]['id']+'>.');await _0x5ee725[_0x5fad3c(0x30,0x4f)]({'embeds':[_0x1cb2f2]});}catch(_0x55865b){console['error']('Error\x20transferring\x20ticket:',_0x55865b),await _0x15721b['editReply']({'content':_0x5fad3c(0x97,0xfe)});}}}}}}}}}}}};function _0x24f7(){const _0x42efc4=['Image','delete','questions','push','This\x20command\x20can\x20only\x20be\x20used\x20in\x20ticket\x20channels.','{workinghours_start_thursday}','Ticket\x20closed.','channels','Error\x20removing\x20user\x20from\x20blacklist:','Successfully\x20added\x20','DefaultPriority','Title','readFileSync','4vrZcCj','isArray','question','The\x20panel\x20to\x20display','#FFA500','toLowerCase','ticketId','Tickets','utf8','moment-timezone','✅\x20User\x20Removed\x20from\x20Blacklist','#FF0000','{workinghours_start_saturday}','No\x20Answer\x20Provided','saturdayEnd','#00FF00','addOptions','createdAt','addChoices','Add\x20a\x20user\x20to\x20the\x20ticket','Remove\x20a\x20user\x20from\x20the\x20blacklist','editReply','Danger','>\x20has\x20been\x20added\x20to\x20the\x20ticket\x20by\x20<@','../../../lang.yml','888JWDWsP','tuesdayStart','`\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20**Review\x20Stats**\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20>\x20Total\x20Reviews:\x20`','Error\x20renaming\x20channel:','Error\x20during\x20channel\x20rename:','Administrator','charAt','CloseTicketButton','sundayStart','An\x20error\x20occurred\x20while\x20fetching\x20the\x20blacklist\x20information.\x20Please\x20try\x20again\x20later.','getUser','repeat','\x20is\x20not\x20blacklisted.','7893NYAkEG','Error\x20renaming\x20ticket:','now','Error\x20adding\x20user\x20to\x20ticket:','`\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20>\x20Total\x20Messages:\x20`','deferred','An\x20error\x20occurred\x20while\x20sending\x20the\x20ticket\x20panel.','HH:mm','rating','hours','This\x20command\x20has\x20been\x20disabled\x20in\x20the\x20config!','stats','../../../models/blacklist','days','permissionsFor','Failed\x20to\x20move\x20channel','1EnINmW','Description','closedAt','Alert\x20has\x20been\x20sent.','messageCount','everyone','remove','match','map','1096iyfWSr','Successfully\x20renamed\x20the\x20ticket\x20to\x20','Text','You\x20do\x20not\x20have\x20permission\x20to\x20use\x20this\x20command.','followUp','rename','name','An\x20error\x20occurred\x20while\x20closing\x20the\x20ticket.\x20Please\x20try\x20again\x20later.','🚫\x20User\x20Blacklisted','`\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20>\x20Deleted\x20Tickets:\x20`','Error\x20removing\x20user\x20from\x20ticket:','start','setCustomId','No\x20Question\x20Provided','reduce','setEmoji','dddd','Emoji','fridayStart','Roles','User','status\x20rating\x20createdAt\x20closedAt\x20messageCount','1601160RZqwSk','Footer','includes','>**\x20has\x20been\x20removed\x20from\x20the\x20blacklist.','The\x20user\x20to\x20check','user','wednesdayStart','keys','ticketType','Utility','{priority}','{time}','{workinghours_end_saturday}','{workinghours_start}','Blacklisted\x20By','Color','findOne','toUpperCase','Date','setRequired','mondayEnd','SupportRole','tuesdayEnd','closed','catch','tag','length','setFooter','reason','Show\x20general\x20ticket\x20stats','>**\x20has\x20been\x20blacklisted\x20from\x20opening\x20tickets.','fetch','setColor','addFields','status','TicketPanelSettings','WorkingHours','Panel','getSubcommandGroup','ViewChannel','channel','N/A','TicketSettings','User\x20is\x20not\x20part\x20of\x20this\x20ticket.','15197tvtxle','Remove\x20a\x20user\x20from\x20the\x20ticket','389045apBmUk','{workinghours_start_tuesday}','setDescription',':t>',':F>','minutes','setTitle','setName','getTime','120769vxwedH','Error\x20sending\x20follow-up:','addStringOption','values','Button','Start','Ticket\x20has\x20been\x20transferred\x20to\x20','Successfully\x20transferred\x20the\x20ticket\x20to\x20','The\x20ticket\x20has\x20been\x20renamed\x20to\x20','Priority','flatMap','New\x20ticket\x20type\x20not\x20found.','The\x20new\x20ticket\x20type','Unnamed\x20Ticket','No\x20reason\x20provided','Send\x20an\x20alert\x20about\x20pending\x20ticket\x20closure.','Time','reply','alert','join','displayAvatarURL','addSubcommand','An\x20error\x20occurred\x20while\x20removing\x20the\x20user\x20from\x20the\x20blacklist.\x20Please\x20try\x20again\x20later.','>\x20has\x20been\x20removed\x20from\x20the\x20ticket\x20by\x20<@','{workinghours_start_wednesday}','📋\x20Blacklist\x20Information','send','panel','ticketcreate','setLabel','discord.js','Ticket\x20Stats','The\x20new\x20name\x20for\x20the\x20ticket\x20channel','Reason','end','Timezone','The\x20user\x20can\x20now\x20open\x20tickets\x20again.','This\x20command\x20can\x20only\x20be\x20used\x20within\x20a\x20ticket\x20channel.','AttachFiles','permissions','\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20','264JOVxyp','{workinghours_start_monday}','load','ticketclose-','Failed\x20to\x20update\x20channel\x20permissions','view','thursdayStart','options','addSubcommandGroup','CategoryID','No\x20buttons\x20are\x20enabled\x20for\x20this\x20panel.','findOneAndDelete','No\x20Ratings','The\x20user\x20to\x20remove\x20from\x20the\x20blacklist','setThumbnail','/5`','`\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20>\x20Average\x20Rating:\x20','You\x20do\x20not\x20have\x20the\x20required\x20roles\x20to\x20use\x20this\x20command.','slice','Embed','getSubcommand','The\x20user\x20to\x20add\x20to\x20the\x20ticket','http:','An\x20error\x20occurred\x20while\x20fetching\x20ticket\x20stats.\x20Please\x20try\x20again\x20later.','Primary','Send\x20the\x20ticket\x20panel','Channel\x20not\x20found.','\x20to\x20the\x20ticket.','\x20by\x20<@','filter','Secondary','roles','An\x20error\x20occurred\x20while\x20adding\x20the\x20user\x20to\x20the\x20ticket.\x20Please\x20try\x20again\x20later.','answer','Ticket\x20not\x20found\x20or\x20already\x20closed.','value','{workinghours_start_friday}','This\x20ticket\x20is\x20already\x20of\x20the\x20specified\x20type.','transfer','cache','Schedule','embedFields','components','get','deleted','lean','has','getString','\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20**Ticket\x20Stats**\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20>\x20Total\x20Tickets:\x20`','fridayEnd','The\x20user\x20to\x20remove\x20from\x20the\x20ticket','setTimestamp','\x20is\x20already\x20blacklisted.','Blacklist\x20a\x20user\x20from\x20opening\x20tickets','Ticket\x20panel\x20sent!','Failed\x20to\x20rename\x20channel','Icon','seconds','mondayStart','../../../models/tickets','Invalid\x20panel\x20selected!','**<@','diff','guild','js-yaml','sundayEnd','close','Alert','setStyle','Author','ChannelName','#0099ff','TicketTypes','EmbedLinks','Thumbnail','tickets','Rename\x20the\x20ticket\x20channel','{workinghours_end_wednesday}','An\x20error\x20occurred\x20while\x20removing\x20the\x20user\x20from\x20the\x20ticket.\x20Please\x20try\x20again\x20later.','type','saturdayStart','Name','inline','Transfer\x20a\x20ticket\x20to\x20a\x20new\x20type','70572LYMjMx','userId','Close\x20the\x20current\x20ticket','unix','An\x20error\x20occurred\x20while\x20transferring\x20the\x20ticket.\x20Please\x20try\x20again\x20later.','exports','some','create','{workinghours_end_thursday}','{ticket-id}','Error\x20sending\x20reply:','User\x20is\x20already\x20added\x20to\x20this\x20ticket.','<t:','duration','You\x20do\x20not\x20have\x20permissions\x20to\x20use\x20this\x20command.','Error\x20fetching\x20blacklist\x20information:','Successfully\x20removed\x20','addedBy','blacklist','forEach','numericRating','addedAt','Error\x20blacklisting\x20user:','wednesdayEnd','split','An\x20error\x20occurred\x20while\x20renaming\x20the\x20ticket.\x20Please\x20try\x20again\x20later.','floor','{user}','members','SendMessages','permissionOverwrites','End','addUserOption','Levels','replace','thursdayEnd','{workinghours_end_friday}','save','`\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20>\x20Average\x20Closure\x20Time:\x20`','add','addComponents','replied','member','261582gJgzjy','error'];_0x24f7=function(){return _0x42efc4;};return _0x24f7();}