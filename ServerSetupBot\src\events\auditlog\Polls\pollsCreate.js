const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.MessageCreate,
    async execute(message, client) {
        try {
            // Only handle messages with polls
            if (!message.poll || message.author.bot) return;
            
            console.log(`📊 Poll created: ${message.poll.question?.text || 'Unknown'} in ${message.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(message.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'POLLS_CREATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'POLLS_CREATE',
                user: message.author.tag,
                userId: message.author.id,
                action: 'Poll được tạo',
                details: `**${message.author.tag}** đã tạo một poll mới`,
                target: message.poll.question?.text || 'Unknown',
                channel: message.channel.name,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(message.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📊 Poll được tạo',
                `Vừa có một poll mới được tạo trong server`
            );
            
            embed.setColor(0x2ecc71); // Green for create
            
            embed.addFields([
                {
                    name: '> Người tạo',
                    value: `- ${message.author.tag} (${message.author})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${message.author.id}`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${message.channel}`,
                    inline: true
                },
                {
                    name: '> Thời gian tạo',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add poll question
            if (message.poll.question?.text) {
                embed.addFields({
                    name: '> Câu hỏi poll',
                    value: `- ${message.poll.question.text}`,
                    inline: false
                });
            }
            
            // Add poll answers
            if (message.poll.answers && message.poll.answers.length > 0) {
                const answersList = message.poll.answers.map((answer, index) => {
                    const emoji = answer.emoji ? `${answer.emoji} ` : `${index + 1}. `;
                    return `${emoji}${answer.text || 'No text'}`;
                }).join('\n');
                
                embed.addFields([
                    {
                        name: '> Số lựa chọn',
                        value: `- ${message.poll.answers.length} lựa chọn`,
                        inline: true
                    },
                    {
                        name: '> Danh sách lựa chọn',
                        value: answersList.length > 1000 ? answersList.substring(0, 1000) + '...' : answersList,
                        inline: false
                    }
                ]);
            }
            
            // Add poll settings
            if (message.poll.allowMultiselect !== undefined) {
                embed.addFields({
                    name: '> Cho phép chọn nhiều',
                    value: `- ${message.poll.allowMultiselect ? 'Có' : 'Không'}`,
                    inline: true
                });
            }
            
            // Add poll duration if available
            if (message.poll.expiresTimestamp) {
                embed.addFields([
                    {
                        name: '> Thời gian kết thúc',
                        value: `- <t:${Math.floor(message.poll.expiresTimestamp / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Thời gian còn lại',
                        value: `- <t:${Math.floor(message.poll.expiresTimestamp / 1000)}:R>`,
                        inline: true
                    }
                ]);
            }
            
            // Add message link
            embed.addFields({
                name: '> Link poll',
                value: `- [Nhấn để xem poll](${message.url})`,
                inline: true
            });
            
            // Add poll ID
            embed.addFields({
                name: '> ID tin nhắn',
                value: `- ${message.id}`,
                inline: true
            });
            
            // Add usage instructions
            embed.addFields({
                name: '> 💡 Cách tham gia',
                value: [
                    '• Nhấn vào lựa chọn để vote',
                    '• Có thể thay đổi vote trước khi poll kết thúc',
                    '• Kết quả sẽ hiển thị real-time',
                    '• Poll tự động kết thúc khi hết thời gian'
                ].join('\n'),
                inline: false
            });
            
            // Set user avatar as thumbnail
            if (message.author.displayAvatarURL()) {
                embed.setThumbnail(message.author.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${message.guild.name} • Poll Create`,
                iconURL: message.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Poll create logged: ${message.poll.question?.text || 'Unknown'}`);
            
        } catch (error) {
            console.error('Error in pollsCreate audit log:', error);
        }
    }
};
