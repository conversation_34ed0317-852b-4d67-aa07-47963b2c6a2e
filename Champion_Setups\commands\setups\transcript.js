const Command = require("../../structures/Command");
const Discord = require("discord.js");

module.exports = class Transcript extends Command {
  constructor(client) {
    super(client, {
      name: "transcript",
      description: client.cmdConfig.transcript.description,
      usage: client.cmdConfig.transcript.usage,
      permissions: client.cmdConfig.transcript.permissions,
      aliases: client.cmdConfig.transcript.aliases,
      category: "setups",
      listed: client.cmdConfig.transcript.enabled,
      slash: true,
      options: [{
        name: "channel",
        type: Discord.ApplicationCommandOptionType.Channel,
        description: "Channel from which to export last 100 Messages",
        channelType: [Discord.ChannelType.GuildText],
        required: true
      }]
    });
  }

  async run(message, args) {
    let channel = message.mentions.channels.first() || message.guild.channels.cache.get(args[0]);
    if(!channel || channel.type != Discord.ChannelType.GuildText) return message.channel.send({ embeds: [this.client.utils.usage(this.client, this.client.cmdConfig.transcript.usage)], ephemeral: this.client.cmdConfig.transcript.ephemeral });

    message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
      this.client.embeds.title, this.client.language.general.transcript_start.replace("<channel>", channel.name), this.client.embeds.general_color)]});

    let messageCollection = new Discord.Collection();
    let channelMessages = await channel.messages.fetch({ limit: 100 });
    messageCollection = messageCollection.concat(channelMessages);

    while(channelMessages.size == 100) {
      let lastMessageId = channelMessages.lastKey();
      channelMessages = await channel.messages.fetch({ limit: 100, before: lastMessageId });
      if(channelMessages) messageCollection = messageCollection.concat(channelMessages);
    }
    
    const msgs = [...messageCollection.values()].sort((a, b) => a.createdTimestamp - b.createdTimestamp);
    
    await this.client.utils.generateTranscript(channel, msgs)
    let path = `./data/transcripts/channel-${channel.id}.html`;
    
    message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
      this.client.embeds.title, this.client.language.general.transcript_end.replace("<channel>", channel.name), this.client.embeds.general_color)], files: [path] });
  }
  async slashRun(interaction, args) {
    let channel = interaction.options.getChannel("channel");

    interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
      this.client.embeds.title, this.client.language.general.transcript_start.replace("<channel>", channel.name), this.client.embeds.general_color)], ephemeral: this.client.cmdConfig.transcript.ephemeral });

    let messageCollection = new Discord.Collection();
    let channelMessages = await channel.messages.fetch({ limit: 100 });
    messageCollection = messageCollection.concat(channelMessages);

    while(channelMessages.size == 100) {
      let lastMessageId = channelMessages.lastKey();
      channelMessages = await channel.messages.fetch({ limit: 100, before: lastMessageId });
      if(channelMessages) messageCollection = messageCollection.concat(channelMessages);
    }
    
    const msgs = [...messageCollection.values()].sort((a, b) => a.createdTimestamp - b.createdTimestamp);
    
    await this.client.utils.generateTranscript(channel, msgs);
    let path = `./data/transcripts/channel-${channel.id}.html`;
    
    interaction.followUp({ embeds: [this.client.embedBuilder(this.client, 
      this.client.embeds.title, this.client.language.general.transcript_end.replace("<channel>", channel.name), this.client.embeds.general_color)], files: [path], ephemeral: this.client.cmdConfig.transcript.ephemeral });
  }
};
