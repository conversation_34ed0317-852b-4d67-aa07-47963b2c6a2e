const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle available tags changes for forum channels
            if (!oldChannel.availableTags || !newChannel.availableTags) return;
            
            // Check if tags actually changed
            const oldTagsString = JSON.stringify(oldChannel.availableTags.map(tag => ({ id: tag.id, name: tag.name })));
            const newTagsString = JSON.stringify(newChannel.availableTags.map(tag => ({ id: tag.id, name: tag.name })));
            
            if (oldTagsString === newTagsString) return;
            
            console.log(`🏷️ Channel available tags updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_AVAILABLE_TAGS_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_AVAILABLE_TAGS_UPDATE',
                user: 'System',
                userId: null,
                action: 'Tags có sẵn của kênh được cập nhật',
                details: `Tags có sẵn của kênh **${newChannel.name}** đã được cập nhật`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the available tags from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Tags có sẵn được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for channel available tags update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🏷️ Tags có sẵn của kênh cập nhật',
                `Vừa có tags có sẵn của kênh được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get channel type names
            const channelTypes = {
                0: 'Kênh văn bản',
                2: 'Kênh thoại',
                4: 'Danh mục',
                5: 'Kênh tin tức',
                10: 'Diễn đàn tin tức',
                11: 'Diễn đàn công khai',
                12: 'Diễn đàn riêng tư',
                13: 'Kênh sân khấu',
                15: 'Forum',
                16: 'Media'
            };
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Loại kênh',
                    value: `- ${channelTypes[newChannel.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add parent category info if available
            if (newChannel.parent) {
                embed.addFields({
                    name: '> Danh mục',
                    value: `- ${newChannel.parent.name}`,
                    inline: true
                });
            }
            
            // Analyze tag changes
            const oldTags = oldChannel.availableTags || [];
            const newTags = newChannel.availableTags || [];
            
            const addedTags = newTags.filter(newTag => !oldTags.find(oldTag => oldTag.id === newTag.id));
            const removedTags = oldTags.filter(oldTag => !newTags.find(newTag => newTag.id === oldTag.id));
            const modifiedTags = newTags.filter(newTag => {
                const oldTag = oldTags.find(oldTag => oldTag.id === newTag.id);
                return oldTag && (oldTag.name !== newTag.name || oldTag.emoji?.name !== newTag.emoji?.name);
            });
            
            // Add tag counts
            embed.addFields([
                {
                    name: '> Số tags cũ',
                    value: `- ${oldTags.length} tags`,
                    inline: true
                },
                {
                    name: '> Số tags mới',
                    value: `- ${newTags.length} tags`,
                    inline: true
                },
                {
                    name: '> Thay đổi',
                    value: `- ${newTags.length - oldTags.length >= 0 ? '+' : ''}${newTags.length - oldTags.length} tags`,
                    inline: true
                }
            ]);
            
            // Show added tags
            if (addedTags.length > 0) {
                const addedTagsList = addedTags.map(tag => {
                    const emoji = tag.emoji ? (tag.emoji.id ? `<:${tag.emoji.name}:${tag.emoji.id}>` : tag.emoji.name) : '';
                    return `${emoji} ${tag.name}`;
                }).join('\n');
                
                embed.addFields({
                    name: '> ✅ Tags được thêm',
                    value: addedTagsList.length > 1000 ? addedTagsList.substring(0, 1000) + '...' : addedTagsList,
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for additions
            }
            
            // Show removed tags
            if (removedTags.length > 0) {
                const removedTagsList = removedTags.map(tag => {
                    const emoji = tag.emoji ? (tag.emoji.id ? `<:${tag.emoji.name}:${tag.emoji.id}>` : tag.emoji.name) : '';
                    return `${emoji} ${tag.name}`;
                }).join('\n');
                
                embed.addFields({
                    name: '> ❌ Tags được xóa',
                    value: removedTagsList.length > 1000 ? removedTagsList.substring(0, 1000) + '...' : removedTagsList,
                    inline: false
                });
                if (addedTags.length === 0) {
                    embed.setColor(0xe74c3c); // Red for removals only
                }
            }
            
            // Show modified tags
            if (modifiedTags.length > 0) {
                const modifiedTagsList = modifiedTags.map(tag => {
                    const oldTag = oldTags.find(oldTag => oldTag.id === tag.id);
                    const emoji = tag.emoji ? (tag.emoji.id ? `<:${tag.emoji.name}:${tag.emoji.id}>` : tag.emoji.name) : '';
                    return `${emoji} ${oldTag.name} → ${tag.name}`;
                }).join('\n');
                
                embed.addFields({
                    name: '> 🔄 Tags được sửa đổi',
                    value: modifiedTagsList.length > 1000 ? modifiedTagsList.substring(0, 1000) + '...' : modifiedTagsList,
                    inline: false
                });
            }
            
            // Add current tags list (if not too many)
            if (newTags.length <= 10) {
                const currentTagsList = newTags.map(tag => {
                    const emoji = tag.emoji ? (tag.emoji.id ? `<:${tag.emoji.name}:${tag.emoji.id}>` : tag.emoji.name) : '';
                    return `${emoji} ${tag.name}`;
                }).join('\n');
                
                embed.addFields({
                    name: '> 📋 Tags hiện tại',
                    value: currentTagsList || 'Không có tags',
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> 📋 Tags hiện tại',
                    value: `- Tổng cộng ${newTags.length} tags (quá nhiều để hiển thị)`,
                    inline: false
                });
            }
            
            // Add impact explanation
            embed.addFields({
                name: '> 📝 Tác động',
                value: [
                    '• Thành viên có thể sử dụng tags mới khi tạo posts',
                    '• Tags bị xóa không thể sử dụng cho posts mới',
                    '• Posts cũ vẫn giữ nguyên tags đã có',
                    '• Giúp phân loại và tìm kiếm posts tốt hơn'
                ].join('\n'),
                inline: false
            });
            
            // Add forum-specific information
            if (newChannel.type === 15) { // Forum channel
                embed.addFields({
                    name: '> 💡 Đặc biệt cho Forum Channel',
                    value: [
                        '• Tags giúp phân loại posts trong forum',
                        '• Thành viên có thể filter posts theo tags',
                        '• Tối đa 20 tags cho mỗi forum',
                        '• Mỗi post có thể có tối đa 5 tags'
                    ].join('\n'),
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Available Tags Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel available tags update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelAvailableTagsUpdate audit log:', error);
        }
    }
};
