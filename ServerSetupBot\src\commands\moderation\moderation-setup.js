const { SlashCommandBuilder, ChannelType } = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
} = require("../../utils/embedBuilder.js");
const {
  canSendMessages,
  canManageRole,
} = require("../../utils/permissions.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("moderation-setup")
    .setDescription("Thiết lập hệ thống kiểm duyệt và automod")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("enable")
        .setDescription("Bật hệ thống kiểm duyệt")
        .addChannelOption((option) =>
          option
            .setName("log_channel")
            .setDescription("Kênh ghi log kiểm duyệt")
            .setRequired(true)
            .addChannelTypes(ChannelType.GuildText)
        )
        .addRoleOption((option) =>
          option
            .setName("mute_role")
            .setDescription("Role mute cho thành viên vi phạm")
            .setRequired(true)
        )
        .addBooleanOption((option) =>
          option
            .setName("automod")
            .setDescription("Bật automod tự động (mặc định: true)")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("spam_protection")
            .setDescription("Chống spam tin nhắn (mặc định: true)")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("link_protection")
            .setDescription("Chống link độc hại (mặc định: true)")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand.setName("disable").setDescription("Tắt hệ thống kiểm duyệt")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("config")
        .setDescription("Xem cấu hình hệ thống kiểm duyệt")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("warnings")
        .setDescription("Thiết lập hệ thống cảnh báo")
        .addIntegerOption((option) =>
          option
            .setName("warning_threshold")
            .setDescription("Số cảnh báo trước khi kick (mặc định: 3)")
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(10)
        )
        .addIntegerOption((option) =>
          option
            .setName("ban_threshold")
            .setDescription("Số cảnh báo trước khi ban (mặc định: 5)")
            .setRequired(false)
            .setMinValue(2)
            .setMaxValue(15)
        )
    ),
  category: "moderation",
  adminOnly: true,
  manageServer: false,

  async execute(interaction, client) {
    const subcommand = interaction.options.getSubcommand();
    const guildId = interaction.guild.id;

    switch (subcommand) {
      case "enable":
        await handleEnable(interaction, client, guildId);
        break;
      case "disable":
        await handleDisable(interaction, client, guildId);
        break;
      case "config":
        await handleConfig(interaction, client, guildId);
        break;
      case "warnings":
        await handleWarnings(interaction, client, guildId);
        break;
    }
  },
};

async function handleEnable(interaction, client, guildId) {
  const logChannel = interaction.options.getChannel("log_channel");
  const muteRole = interaction.options.getRole("mute_role");
  const automod = interaction.options.getBoolean("automod") ?? true;
  const spamProtection =
    interaction.options.getBoolean("spam_protection") ?? true;
  const linkProtection =
    interaction.options.getBoolean("link_protection") ?? true;

  // Kiểm tra quyền bot trong log channel
  const canSend = canSendMessages(logChannel);
  if (!canSend.canSend) {
    const errorEmbed = createErrorEmbed(
      "Lỗi quyền hạn!",
      `${canSend.reason}\n\nVui lòng cấp quyền cho bot hoặc chọn kênh khác.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Kiểm tra quyền quản lý mute role
  const canManage = canManageRole(interaction.guild, muteRole);
  if (!canManage.canManage) {
    const errorEmbed = createErrorEmbed(
      "Bot không thể quản lý role!",
      `${canManage.reason}\n\nRole: ${muteRole}`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    // Lưu cấu hình vào database
    await client.db.updateModerationConfig(guildId, {
      automod_enabled: automod ? 1 : 0,
      spam_protection: spamProtection ? 1 : 0,
      link_protection: linkProtection ? 1 : 0,
      caps_protection: 1, // Mặc định bật
      mute_role_id: muteRole.id,
      log_channel_id: logChannel.id,
    });

    const successEmbed = createSuccessEmbed(
      "Hệ thống kiểm duyệt đã được bật!",
      `**Kênh log:** ${logChannel}\n` +
        `**Role mute:** ${muteRole}\n` +
        `**AutoMod:** ${automod ? "✅ Bật" : "❌ Tắt"}\n` +
        `**Chống spam:** ${spamProtection ? "✅ Bật" : "❌ Tắt"}\n` +
        `**Chống link:** ${linkProtection ? "✅ Bật" : "❌ Tắt"}\n` +
        `**Chống viết hoa:** ✅ Bật`
    );

    successEmbed.addFields({
      name: "📝 Bước tiếp theo",
      value:
        "• Sử dụng `/moderation-setup warnings` để thiết lập hệ thống cảnh báo\n" +
        "• Kiểm tra quyền của mute role trong các channel\n" +
        "• Test hệ thống với một số tin nhắn vi phạm",
      inline: false,
    });

    await interaction.reply({ embeds: [successEmbed] });

    // Gửi tin nhắn test vào log channel
    const logEmbed = createInfoEmbed(
      "🛡️ Hệ thống kiểm duyệt đã được bật",
      `Được thiết lập bởi ${interaction.user}\n\n` +
        `**Cấu hình:**\n` +
        `• AutoMod: ${automod ? "Bật" : "Tắt"}\n` +
        `• Chống spam: ${spamProtection ? "Bật" : "Tắt"}\n` +
        `• Chống link: ${linkProtection ? "Bật" : "Tắt"}\n` +
        `• Role mute: ${muteRole}`
    );

    await logChannel.send({ embeds: [logEmbed] });
  } catch (error) {
    console.error("Lỗi khi cập nhật moderation config:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể lưu cấu hình kiểm duyệt. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleDisable(interaction, client, guildId) {
  try {
    await client.db.updateModerationConfig(guildId, {
      automod_enabled: 0,
      spam_protection: 0,
      link_protection: 0,
      caps_protection: 0,
    });

    const successEmbed = createSuccessEmbed(
      "Hệ thống kiểm duyệt đã được tắt!",
      "Tất cả tính năng automod đã được vô hiệu hóa.\n\nCấu hình mute role và log channel vẫn được giữ lại."
    );

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi tắt moderation:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể tắt hệ thống kiểm duyệt. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleConfig(interaction, client, guildId) {
  try {
    const config = await client.db.getModerationConfig(guildId);

    if (!config) {
      const infoEmbed = createInfoEmbed(
        "Chưa có cấu hình",
        "Hệ thống kiểm duyệt chưa được thiết lập.\n\nSử dụng `/moderation-setup enable` để bắt đầu!"
      );
      return await interaction.reply({ embeds: [infoEmbed] });
    }

    const logChannel = interaction.guild.channels.cache.get(
      config.log_channel_id
    );
    const muteRole = interaction.guild.roles.cache.get(config.mute_role_id);

    const embed = createInfoEmbed(
      "Cấu hình hệ thống kiểm duyệt",
      `**AutoMod:** ${config.automod_enabled ? "✅ Bật" : "❌ Tắt"}\n` +
        `**Chống spam:** ${config.spam_protection ? "✅ Bật" : "❌ Tắt"}\n` +
        `**Chống link:** ${config.link_protection ? "✅ Bật" : "❌ Tắt"}\n` +
        `**Chống viết hoa:** ${
          config.caps_protection ? "✅ Bật" : "❌ Tắt"
        }\n` +
        `**Kênh log:** ${logChannel || "❌ Đã bị xóa"}\n` +
        `**Role mute:** ${muteRole || "❌ Đã bị xóa"}`
    );

    embed.addFields({
      name: "⚠️ Hệ thống cảnh báo",
      value:
        `• Ngưỡng kick: ${config.warning_threshold || 3} cảnh báo\n` +
        `• Ngưỡng ban: ${config.auto_ban_threshold || 5} cảnh báo`,
      inline: false,
    });

    embed.addFields({
      name: "🔧 Quản lý",
      value:
        "• `/moderation-setup enable` - Bật/cập nhật cấu hình\n" +
        "• `/moderation-setup disable` - Tắt hệ thống\n" +
        "• `/moderation-setup warnings` - Thiết lập cảnh báo",
      inline: false,
    });

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error("Lỗi khi lấy moderation config:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể lấy cấu hình kiểm duyệt. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleWarnings(interaction, client, guildId) {
  const warningThreshold =
    interaction.options.getInteger("warning_threshold") || 3;
  const banThreshold = interaction.options.getInteger("ban_threshold") || 5;

  if (banThreshold <= warningThreshold) {
    const errorEmbed = createErrorEmbed(
      "Cấu hình không hợp lệ!",
      "Ngưỡng ban phải lớn hơn ngưỡng kick!"
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    await client.db.updateModerationConfig(guildId, {
      warning_threshold: warningThreshold,
      auto_ban_threshold: banThreshold,
    });

    const successEmbed = createSuccessEmbed(
      "Đã cập nhật hệ thống cảnh báo!",
      `**Ngưỡng kick:** ${warningThreshold} cảnh báo\n` +
        `**Ngưỡng ban:** ${banThreshold} cảnh báo\n\n` +
        `Khi thành viên đạt số cảnh báo này, hệ thống sẽ tự động thực hiện hành động tương ứng.`
    );

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi cập nhật warning config:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể cập nhật cấu hình cảnh báo. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}
