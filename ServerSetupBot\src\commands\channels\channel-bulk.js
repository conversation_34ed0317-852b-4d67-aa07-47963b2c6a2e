const { SlashCommandBuilder, ChannelType } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createLoadingEmbed } = require('../../utils/embedBuilder.js');
const { canManageChannel } = require('../../utils/permissions.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('channel-bulk')
        .setDescription('Tạo nhiều kênh cùng lúc theo template có sẵn')
        .addSubcommand(subcommand =>
            subcommand
                .setName('gaming')
                .setDescription('Tạo template kênh cho server gaming')
                .addStringOption(option =>
                    option.setName('prefix')
                        .setDescription('Tiền tố cho tên kênh (ví dụ: "game-")')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('community')
                .setDescription('Tạo template kênh cho cộng đồng')
                .addStringOption(option =>
                    option.setName('prefix')
                        .setDescription('Tiền tố cho tên kênh')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('study')
                .setDescription('Tạo template kênh cho học tập')
                .addStringOption(option =>
                    option.setName('prefix')
                        .setDescription('Tiền tố cho tên kênh')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('business')
                .setDescription('Tạo template kênh cho doanh nghiệp')
                .addStringOption(option =>
                    option.setName('prefix')
                        .setDescription('Tiền tố cho tên kênh')
                        .setRequired(false)
                )
        ),
    category: 'channels',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();
        const prefix = interaction.options.getString('prefix') || '';
        
        // Kiểm tra quyền bot
        const canManage = canManageChannel(interaction.guild);
        if (!canManage.canManage) {
            const errorEmbed = createErrorEmbed(
                'Bot không có quyền!',
                canManage.reason
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        // Hiển thị loading message
        const loadingEmbed = createLoadingEmbed(
            'Đang tạo kênh...',
            'Vui lòng chờ trong khi bot tạo các kênh theo template.'
        );
        await interaction.reply({ embeds: [loadingEmbed] });
        
        try {
            let template;
            let templateName;
            
            switch (subcommand) {
                case 'gaming':
                    template = getGamingTemplate(prefix);
                    templateName = 'Gaming Server';
                    break;
                case 'community':
                    template = getCommunityTemplate(prefix);
                    templateName = 'Community Server';
                    break;
                case 'study':
                    template = getStudyTemplate(prefix);
                    templateName = 'Study Server';
                    break;
                case 'business':
                    template = getBusinessTemplate(prefix);
                    templateName = 'Business Server';
                    break;
            }
            
            const result = await createChannelsFromTemplate(interaction.guild, template);
            
            const successEmbed = createSuccessEmbed(
                `Template ${templateName} đã được tạo!`,
                `**Đã tạo thành công:**\n` +
                `• ${result.categories} danh mục\n` +
                `• ${result.textChannels} kênh text\n` +
                `• ${result.voiceChannels} kênh voice\n\n` +
                `**Tổng cộng:** ${result.total} kênh`
            );
            
            if (result.errors.length > 0) {
                successEmbed.addFields({
                    name: '⚠️ Một số kênh không thể tạo',
                    value: result.errors.slice(0, 5).join('\n') + (result.errors.length > 5 ? '\n...' : ''),
                    inline: false
                });
            }
            
            await interaction.editReply({ embeds: [successEmbed] });
            
        } catch (error) {
            console.error('Lỗi khi tạo bulk channels:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi tạo kênh!',
                'Đã xảy ra lỗi khi tạo kênh. Vui lòng thử lại sau!'
            );
            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },
};

function getGamingTemplate(prefix) {
    return [
        // Thông tin chung
        { type: 'category', name: `${prefix}📋 THÔNG TIN` },
        { type: 'text', name: `${prefix}📢-thông-báo`, parent: `${prefix}📋 THÔNG TIN`, topic: 'Thông báo quan trọng từ admin' },
        { type: 'text', name: `${prefix}📜-quy-tắc`, parent: `${prefix}📋 THÔNG TIN`, topic: 'Quy tắc và điều khoản server' },
        { type: 'text', name: `${prefix}🎭-vai-trò`, parent: `${prefix}📋 THÔNG TIN`, topic: 'Lấy vai trò và quyền hạn' },
        
        // Chat chung
        { type: 'category', name: `${prefix}💬 CHAT CHUNG` },
        { type: 'text', name: `${prefix}👋-chào-hỏi`, parent: `${prefix}💬 CHAT CHUNG`, topic: 'Chào hỏi và làm quen' },
        { type: 'text', name: `${prefix}💭-tán-gẫu`, parent: `${prefix}💬 CHAT CHUNG`, topic: 'Trò chuyện tự do' },
        { type: 'text', name: `${prefix}🎮-game-chat`, parent: `${prefix}💬 CHAT CHUNG`, topic: 'Thảo luận về game' },
        { type: 'text', name: `${prefix}🖼️-chia-sẻ-media`, parent: `${prefix}💬 CHAT CHUNG`, topic: 'Chia sẻ hình ảnh, video' },
        
        // Gaming
        { type: 'category', name: `${prefix}🎮 GAMING` },
        { type: 'text', name: `${prefix}🎯-tìm-đội`, parent: `${prefix}🎮 GAMING`, topic: 'Tìm đồng đội chơi game' },
        { type: 'text', name: `${prefix}🏆-thành-tích`, parent: `${prefix}🎮 GAMING`, topic: 'Khoe thành tích game' },
        { type: 'voice', name: `${prefix}🎮 Gaming Room 1`, parent: `${prefix}🎮 GAMING`, userLimit: 10 },
        { type: 'voice', name: `${prefix}🎮 Gaming Room 2`, parent: `${prefix}🎮 GAMING`, userLimit: 10 },
        { type: 'voice', name: `${prefix}🎮 Gaming Room 3`, parent: `${prefix}🎮 GAMING`, userLimit: 10 },
        
        // Voice chung
        { type: 'category', name: `${prefix}🔊 VOICE CHAT` },
        { type: 'voice', name: `${prefix}🗣️ Tán gẫu`, parent: `${prefix}🔊 VOICE CHAT`, userLimit: 0 },
        { type: 'voice', name: `${prefix}🎵 Nghe nhạc`, parent: `${prefix}🔊 VOICE CHAT`, userLimit: 0 },
        { type: 'voice', name: `${prefix}📚 Học tập`, parent: `${prefix}🔊 VOICE CHAT`, userLimit: 8 }
    ];
}

function getCommunityTemplate(prefix) {
    return [
        // Thông tin
        { type: 'category', name: `${prefix}📋 THÔNG TIN` },
        { type: 'text', name: `${prefix}📢-thông-báo`, parent: `${prefix}📋 THÔNG TIN` },
        { type: 'text', name: `${prefix}📜-quy-tắc`, parent: `${prefix}📋 THÔNG TIN` },
        { type: 'text', name: `${prefix}❓-hỏi-đáp`, parent: `${prefix}📋 THÔNG TIN` },
        
        // Cộng đồng
        { type: 'category', name: `${prefix}👥 CỘNG ĐỒNG` },
        { type: 'text', name: `${prefix}👋-giới-thiệu`, parent: `${prefix}👥 CỘNG ĐỒNG` },
        { type: 'text', name: `${prefix}💬-tán-gẫu`, parent: `${prefix}👥 CỘNG ĐỒNG` },
        { type: 'text', name: `${prefix}🎉-sự-kiện`, parent: `${prefix}👥 CỘNG ĐỒNG` },
        { type: 'text', name: `${prefix}💡-ý-tưởng`, parent: `${prefix}👥 CỘNG ĐỒNG` },
        
        // Chia sẻ
        { type: 'category', name: `${prefix}📤 CHIA SẺ` },
        { type: 'text', name: `${prefix}🖼️-hình-ảnh`, parent: `${prefix}📤 CHIA SẺ` },
        { type: 'text', name: `${prefix}🎵-âm-nhạc`, parent: `${prefix}📤 CHIA SẺ` },
        { type: 'text', name: `${prefix}📺-video`, parent: `${prefix}📤 CHIA SẺ` },
        
        // Voice
        { type: 'category', name: `${prefix}🔊 VOICE` },
        { type: 'voice', name: `${prefix}🗣️ Tán gẫu chung`, parent: `${prefix}🔊 VOICE` },
        { type: 'voice', name: `${prefix}🎵 Nghe nhạc cùng nhau`, parent: `${prefix}🔊 VOICE` }
    ];
}

function getStudyTemplate(prefix) {
    return [
        // Thông tin
        { type: 'category', name: `${prefix}📚 THÔNG TIN HỌC TẬP` },
        { type: 'text', name: `${prefix}📢-thông-báo-học-tập`, parent: `${prefix}📚 THÔNG TIN HỌC TẬP` },
        { type: 'text', name: `${prefix}📅-lịch-học`, parent: `${prefix}📚 THÔNG TIN HỌC TẬP` },
        { type: 'text', name: `${prefix}📋-bài-tập`, parent: `${prefix}📚 THÔNG TIN HỌC TẬP` },
        
        // Môn học
        { type: 'category', name: `${prefix}📖 MÔN HỌC` },
        { type: 'text', name: `${prefix}🔢-toán-học`, parent: `${prefix}📖 MÔN HỌC` },
        { type: 'text', name: `${prefix}🔬-khoa-học`, parent: `${prefix}📖 MÔN HỌC` },
        { type: 'text', name: `${prefix}🌍-địa-lý`, parent: `${prefix}📖 MÔN HỌC` },
        { type: 'text', name: `${prefix}📜-lịch-sử`, parent: `${prefix}📖 MÔN HỌC` },
        { type: 'text', name: `${prefix}🗣️-ngôn-ngữ`, parent: `${prefix}📖 MÔN HỌC` },
        
        // Hỗ trợ
        { type: 'category', name: `${prefix}🤝 HỖ TRỢ HỌC TẬP` },
        { type: 'text', name: `${prefix}❓-hỏi-bài`, parent: `${prefix}🤝 HỖ TRỢ HỌC TẬP` },
        { type: 'text', name: `${prefix}📚-tài-liệu`, parent: `${prefix}🤝 HỖ TRỢ HỌC TẬP` },
        { type: 'text', name: `${prefix}👥-nhóm-học`, parent: `${prefix}🤝 HỖ TRỢ HỌC TẬP` },
        
        // Voice học tập
        { type: 'category', name: `${prefix}🔊 PHÒNG HỌC` },
        { type: 'voice', name: `${prefix}📚 Phòng học chung`, parent: `${prefix}🔊 PHÒNG HỌC`, userLimit: 10 },
        { type: 'voice', name: `${prefix}👥 Học nhóm 1`, parent: `${prefix}🔊 PHÒNG HỌC`, userLimit: 6 },
        { type: 'voice', name: `${prefix}👥 Học nhóm 2`, parent: `${prefix}🔊 PHÒNG HỌC`, userLimit: 6 },
        { type: 'voice', name: `${prefix}🤫 Tự học`, parent: `${prefix}🔊 PHÒNG HỌC`, userLimit: 4 }
    ];
}

function getBusinessTemplate(prefix) {
    return [
        // Thông tin công ty
        { type: 'category', name: `${prefix}🏢 THÔNG TIN CÔNG TY` },
        { type: 'text', name: `${prefix}📢-thông-báo`, parent: `${prefix}🏢 THÔNG TIN CÔNG TY` },
        { type: 'text', name: `${prefix}📋-quy-định`, parent: `${prefix}🏢 THÔNG TIN CÔNG TY` },
        { type: 'text', name: `${prefix}👥-nhân-sự`, parent: `${prefix}🏢 THÔNG TIN CÔNG TY` },
        
        // Làm việc
        { type: 'category', name: `${prefix}💼 LÀM VIỆC` },
        { type: 'text', name: `${prefix}💬-thảo-luận-chung`, parent: `${prefix}💼 LÀM VIỆC` },
        { type: 'text', name: `${prefix}📊-dự-án`, parent: `${prefix}💼 LÀM VIỆC` },
        { type: 'text', name: `${prefix}📅-lịch-họp`, parent: `${prefix}💼 LÀM VIỆC` },
        { type: 'text', name: `${prefix}📈-báo-cáo`, parent: `${prefix}💼 LÀM VIỆC` },
        
        // Phòng ban
        { type: 'category', name: `${prefix}🏬 PHÒNG BAN` },
        { type: 'text', name: `${prefix}💰-kế-toán`, parent: `${prefix}🏬 PHÒNG BAN` },
        { type: 'text', name: `${prefix}📢-marketing`, parent: `${prefix}🏬 PHÒNG BAN` },
        { type: 'text', name: `${prefix}💻-it-support`, parent: `${prefix}🏬 PHÒNG BAN` },
        { type: 'text', name: `${prefix}👥-nhân-sự`, parent: `${prefix}🏬 PHÒNG BAN` },
        
        // Họp
        { type: 'category', name: `${prefix}🔊 PHÒNG HỌP` },
        { type: 'voice', name: `${prefix}🏢 Phòng họp chính`, parent: `${prefix}🔊 PHÒNG HỌP`, userLimit: 20 },
        { type: 'voice', name: `${prefix}👥 Phòng họp nhỏ 1`, parent: `${prefix}🔊 PHÒNG HỌP`, userLimit: 8 },
        { type: 'voice', name: `${prefix}👥 Phòng họp nhỏ 2`, parent: `${prefix}🔊 PHÒNG HỌP`, userLimit: 8 }
    ];
}

async function createChannelsFromTemplate(guild, template) {
    const result = {
        categories: 0,
        textChannels: 0,
        voiceChannels: 0,
        total: 0,
        errors: []
    };
    
    const createdCategories = new Map();
    
    // Tạo categories trước
    for (const item of template) {
        if (item.type === 'category') {
            try {
                // Kiểm tra category đã tồn tại chưa
                const existing = guild.channels.cache.find(ch => 
                    ch.type === ChannelType.GuildCategory && ch.name === item.name
                );
                
                if (!existing) {
                    const category = await guild.channels.create({
                        name: item.name,
                        type: ChannelType.GuildCategory
                    });
                    createdCategories.set(item.name, category);
                    result.categories++;
                    result.total++;
                } else {
                    createdCategories.set(item.name, existing);
                }
                
                // Delay để tránh rate limit
                await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
                result.errors.push(`Lỗi tạo danh mục ${item.name}: ${error.message}`);
            }
        }
    }
    
    // Tạo channels
    for (const item of template) {
        if (item.type !== 'category') {
            try {
                // Kiểm tra channel đã tồn tại chưa
                const existing = guild.channels.cache.find(ch => ch.name === item.name);
                if (existing) {
                    result.errors.push(`Kênh ${item.name} đã tồn tại`);
                    continue;
                }
                
                const parentCategory = item.parent ? createdCategories.get(item.parent) : null;
                
                const channelOptions = {
                    name: item.name,
                    type: item.type === 'text' ? ChannelType.GuildText : ChannelType.GuildVoice,
                    parent: parentCategory?.id || null
                };
                
                if (item.topic) channelOptions.topic = item.topic;
                if (item.userLimit !== undefined) channelOptions.userLimit = item.userLimit;
                if (item.bitrate) channelOptions.bitrate = item.bitrate * 1000;
                
                await guild.channels.create(channelOptions);
                
                if (item.type === 'text') {
                    result.textChannels++;
                } else {
                    result.voiceChannels++;
                }
                result.total++;
                
                // Delay để tránh rate limit
                await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
                result.errors.push(`Lỗi tạo kênh ${item.name}: ${error.message}`);
            }
        }
    }
    
    return result;
}
