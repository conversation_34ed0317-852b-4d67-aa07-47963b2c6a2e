(function(_0x4930fd,_0xdb056a){function _0xf88522(_0xef179e,_0x566d75){return _0xaa2d(_0xef179e-0x1bb,_0x566d75);}const _0x563f7b=_0x4930fd();while(!![]){try{const _0x2c613d=parseInt(_0xf88522(0x3cb,0x3df))/0x1*(parseInt(_0xf88522(0x3e4,0x3ef))/0x2)+-parseInt(_0xf88522(0x3c5,0x3b4))/0x3+-parseInt(_0xf88522(0x3b4,0x3ab))/0x4+-parseInt(_0xf88522(0x3c8,0x3be))/0x5+parseInt(_0xf88522(0x3ef,0x3cc))/0x6+-parseInt(_0xf88522(0x3b5,0x394))/0x7*(parseInt(_0xf88522(0x3ce,0x3bb))/0x8)+parseInt(_0xf88522(0x3c1,0x3d9))/0x9;if(_0x2c613d===_0xdb056a)break;else _0x563f7b['push'](_0x563f7b['shift']());}catch(_0x4dcd33){_0x563f7b['push'](_0x563f7b['shift']());}}}(_0x3974,0x97169));const {SlashCommandBuilder,EmbedBuilder}=require('discord.js'),fs=require('fs'),yaml=require(_0x3f8a4d(0x512,0x51f)),ms=require('parse-duration'),AFK=require(_0x3f8a4d(0x4bf,0x4e4)),config=yaml[_0x3f8a4d(0x50e,0x52c)](fs[_0x3f8a4d(0x4f6,0x504)](_0x3f8a4d(0x4e4,0x4e1),'utf8')),lang=yaml[_0x3f8a4d(0x52b,0x52c)](fs[_0x3f8a4d(0x4d9,0x504)]('./lang.yml',_0x3f8a4d(0x4fc,0x500))),replacePlaceholders=(_0x118a31,_0x58f171={})=>{function _0x37080e(_0x5f17f6,_0x10b7fc){return _0x3f8a4d(_0x10b7fc,_0x5f17f6- -0x21e);}if(!_0x118a31)return'​';return Object[_0x37080e(0x2cf,0x2af)](_0x58f171)[_0x37080e(0x2d8,0x2cc)]((_0x30c33c,_0x1eaf3e)=>{const _0x2257b1=new RegExp('{'+_0x1eaf3e+'}','gi');return _0x30c33c['replace'](_0x2257b1,_0x58f171[_0x1eaf3e]||'');},_0x118a31);};function _0x3974(){const _0x66d0f=['RoleCreate','setDescription','member','You\x20are\x20now\x20marked\x20as\x20AFK.','load','findOne','AFK','Error\x20in\x20/afk\x20command:','add','setName','editReply','channel','ErrorMessges','fetch','addStringOption','name','./config.yml','send','pop','../../models/afkSchema','now','create','2145408BoUsBJ','761915uZfhhr','displayName','EmbedImage',':R>','guild','keys','afk','setTitle','options','TimeParseError','How\x20long\x20you\x20will\x20be\x20AFK:\x201m\x20(minutes),\x201h\x20(hours),\x201d\x20(days)','An\x20error\x20occurred:\x20','17267814QpkTlx','<t:','reduce','find','2278563HCbJhv','setThumbnail','Set\x20an\x20AFK\x20status\x20with\x20a\x20message\x20and\x20duration','3446955iusBin','message','NotifyChannel','151DzamMA','findOneAndUpdate','utf8','32EWvFMT','You\x20do\x20not\x20have\x20permission\x20to\x20use\x20this\x20command.','cache','readFileSync','Needed\x20an\x20AFK\x20role','Title','roles','Description','EnableAfkRole','Your\x20AFK\x20message','AllowRoles','floor','setRequired','getString','members','AfkTag','AllowAnyone','Failed\x20to\x20create\x20AFK\x20role.','Failed\x20to\x20parse\x20time.','map','Missing\x20permissions\x20to\x20change\x20nickname.','Success','8218kQEAzC','deferReply','error','NotifyMessage','Invalid\x20time\x20format.\x20Please\x20use\x20s,\x20m,\x20h,\x20or\x20d.','AfkRoleName','NotifyEmbed','UseNotifyEmbed','js-yaml','You\x20are\x20already\x20marked\x20as\x20AFK.','time','3002376EPowrl','user','some','join','AfktagInDisplayName','exports'];_0x3974=function(){return _0x66d0f;};return _0x3974();}function _0xaa2d(_0x2c65b3,_0x2f7ef3){const _0x3974a2=_0x3974();return _0xaa2d=function(_0xaa2d9d,_0x158ca0){_0xaa2d9d=_0xaa2d9d-0x1f2;let _0x3b54bc=_0x3974a2[_0xaa2d9d];return _0x3b54bc;},_0xaa2d(_0x2c65b3,_0x2f7ef3);}function _0x3f8a4d(_0x4a2e90,_0x52d115){return _0xaa2d(_0x52d115-0x2ee,_0x4a2e90);}const handleRolePermissionCheck=_0x2c92ce=>{function _0x3eca28(_0x537c14,_0x17021d){return _0x3f8a4d(_0x17021d,_0x537c14- -0x1fa);}if(!config[_0x3eca28(0x334,0x328)][_0x3eca28(0x317,0x320)]){const _0x1438b6=config[_0x3eca28(0x334,0x34e)][_0x3eca28(0x311,0x325)],_0x15394d=_0x2c92ce[_0x3eca28(0x330,0x31f)][_0x3eca28(0x30d,0x2ff)]['cache'][_0x3eca28(0x31a,0x336)](_0x2a0585=>_0x2a0585['id']),_0xceba76=_0x15394d[_0x3eca28(0x32a,0x323)](_0x87d1b=>_0x1438b6['includes'](_0x87d1b));if(!_0xceba76)throw new Error(lang[_0x3eca28(0x334,0x33b)]['ErrorMessges']['NoPermission']||_0x3eca28(0x308,0x31d));}},handleTimeFormatCheck=_0x4c07a2=>{const _0x284c4e=/^(\d+)(s|m|h|d)$/;function _0x2df275(_0x4f7545,_0x39be22){return _0x3f8a4d(_0x39be22,_0x4f7545- -0x5c3);}if(!_0x284c4e['test'](_0x4c07a2))throw new Error(lang[_0x2df275(-0x95,-0x95)][_0x2df275(-0x8f,-0xa6)]['TimeFormat']||_0x2df275(-0xa8,-0x8a));},handleNicknameUpdate=async(_0x26e416,_0x4a088a,_0x40a5e3)=>{const _0x6151c2=await _0x26e416[_0xfb1f9d(0x28f,0x27c)][_0xfb1f9d(0x2b2,0x28e)][_0xfb1f9d(0x2d8,0x2b9)](_0x26e416[_0xfb1f9d(0x2c6,0x2ef)]['id']);function _0xfb1f9d(_0x4944e1,_0x4decfa){return _0x3f8a4d(_0x4decfa,_0x4944e1- -0x25d);}try{await _0x6151c2['setNickname'](_0x40a5e3);}catch(_0x7b2b02){throw new Error(lang[_0xfb1f9d(0x2d1,0x2c9)]['ErrorMessges']['NickNameMissingPermissions']||_0xfb1f9d(0x2b8,0x291));}},handleAFKRoleAssignment=async _0x4cca8d=>{let _0x267a54=_0x4cca8d['guild'][_0x5d4c6b(-0x1c8,-0x1b7)][_0x5d4c6b(-0x1ab,-0x1bb)][_0x5d4c6b(-0x1d5,-0x1c7)](_0x344b68=>_0x344b68[_0x5d4c6b(-0x1c6,-0x1de)]===config[_0x5d4c6b(-0x193,-0x190)]['AfkRoleName']);function _0x5d4c6b(_0x5edea9,_0x57b47e){return _0x3f8a4d(_0x5edea9,_0x57b47e- -0x6be);}if(!_0x267a54)try{_0x267a54=await _0x4cca8d['guild'][_0x5d4c6b(-0x192,-0x1b7)][_0x5d4c6b(-0x1ef,-0x1d8)]({'name':config[_0x5d4c6b(-0x183,-0x190)][_0x5d4c6b(-0x1ba,-0x1a2)],'color':config[_0x5d4c6b(-0x1bc,-0x190)]['AfkRoleColor'],'reason':_0x5d4c6b(-0x1ad,-0x1b9)});}catch(_0x42c2e3){throw new Error(lang['AFK'][_0x5d4c6b(-0x18f,-0x18a)][_0x5d4c6b(-0x189,-0x196)]||_0x5d4c6b(-0x1d0,-0x1ac));}const _0x12e7ab=await _0x4cca8d[_0x5d4c6b(-0x1ee,-0x1d2)][_0x5d4c6b(-0x1a2,-0x1af)][_0x5d4c6b(-0x19f,-0x189)](_0x4cca8d[_0x5d4c6b(-0x170,-0x19b)]['id']);await _0x12e7ab['roles'][_0x5d4c6b(-0x18b,-0x18e)](_0x267a54);},createNotifyMessage=async(_0x5a61ea,_0x499922,_0x12ff54)=>{function _0x4e24c7(_0x65696d,_0x5b6b5f){return _0x3f8a4d(_0x5b6b5f,_0x65696d- -0x9c);}if(config[_0x4e24c7(0x492,0x49a)][_0x4e24c7(0x482,0x499)]){const _0x203d21=new EmbedBuilder()['setColor'](config[_0x4e24c7(0x492,0x49b)][_0x4e24c7(0x481,0x46b)]['EmbedColor']);config['AFK'][_0x4e24c7(0x481,0x488)]['Title']&&_0x203d21[_0x4e24c7(0x453,0x44e)](replacePlaceholders(config[_0x4e24c7(0x492,0x470)][_0x4e24c7(0x481,0x495)][_0x4e24c7(0x46a,0x462)],_0x499922));config[_0x4e24c7(0x492,0x4ac)]['NotifyEmbed'][_0x4e24c7(0x44e,0x444)]&&_0x203d21['setImage'](config[_0x4e24c7(0x492,0x4aa)][_0x4e24c7(0x481,0x49f)][_0x4e24c7(0x44e,0x45f)]);config[_0x4e24c7(0x492,0x498)][_0x4e24c7(0x481,0x4a0)]['EmbedThumbnail']&&_0x203d21[_0x4e24c7(0x45d,0x43e)](config[_0x4e24c7(0x492,0x49c)]['NotifyEmbed']['EmbedThumbnail']);let _0x4b017c=config[_0x4e24c7(0x492,0x47c)][_0x4e24c7(0x481,0x456)][_0x4e24c7(0x46c,0x447)]['map'](_0x71e9de=>replacePlaceholders(_0x71e9de,_0x499922));return _0x12ff54&&_0x4b017c[_0x4e24c7(0x447,0x441)](),_0x203d21[_0x4e24c7(0x48d,0x479)](_0x4b017c[_0x4e24c7(0x489,0x4b4)]('\x0a')),await _0x5a61ea[_0x4e24c7(0x497,0x47f)]['send']({'embeds':[_0x203d21]});}else{const _0x380da9=replacePlaceholders(config[_0x4e24c7(0x492,0x4ae)][_0x4e24c7(0x47e,0x49d)],_0x499922);return await _0x5a61ea[_0x4e24c7(0x497,0x4af)][_0x4e24c7(0x446,0x442)](_0x380da9);}};module[_0x3f8a4d(0x503,0x527)]={'data':new SlashCommandBuilder()[_0x3f8a4d(0x525,0x531)](_0x3f8a4d(0x4f9,0x4ee))[_0x3f8a4d(0x53d,0x529)](_0x3f8a4d(0x511,0x4fa))[_0x3f8a4d(0x51b,0x536)](_0x2e536b=>_0x2e536b[_0x3f8a4d(0x526,0x531)]('message')['setDescription'](_0x3f8a4d(0x511,0x50a))[_0x3f8a4d(0x4e5,0x50d)](!![]))[_0x3f8a4d(0x553,0x536)](_0x341d1c=>_0x341d1c[_0x3f8a4d(0x547,0x531)](_0x3f8a4d(0x50c,0x521))[_0x3f8a4d(0x551,0x529)](_0x3f8a4d(0x4d2,0x4f2))),'category':'Utility',async 'execute'(_0x1bb90c){function _0x45fd52(_0x292a6a,_0x1f9b19){return _0x3f8a4d(_0x1f9b19,_0x292a6a- -0x52b);}await _0x1bb90c[_0x45fd52(-0x13,0x19)]({'ephemeral':!![]});try{handleRolePermissionCheck(_0x1bb90c);const _0x585237=_0x1bb90c[_0x45fd52(-0x3b,-0x3c)]['getString'](_0x45fd52(-0xa,0x1e));let _0x354b6c=!![],_0x36d424=0x0;if(_0x585237){if(!handleTimeFormatCheck(_0x585237)){await _0x1bb90c['editReply']({'content':lang[_0x45fd52(0x3,-0x4)][_0x45fd52(0x9,0x33)]['TimeFormat']||_0x45fd52(-0x10,-0x35)});return;}_0x354b6c=![],_0x36d424=ms(_0x585237);if(!_0x36d424){await _0x1bb90c['editReply']({'content':lang[_0x45fd52(0x3,0x15)][_0x45fd52(0x9,0xf)][_0x45fd52(-0x3a,-0x4c)]||_0x45fd52(-0x18,-0x12)});return;}}const _0x506758=await AFK[_0x45fd52(0x2,-0x8)]({'userId':_0x1bb90c['user']['id'],'afk':!![]});if(_0x506758){await _0x1bb90c[_0x45fd52(0x7,0x26)]({'content':lang[_0x45fd52(0x3,0x16)][_0x45fd52(0x9,0x3)]['AlreadyMarkedAFK']||_0x45fd52(-0xb,0x1)});return;}const _0x560409=_0x1bb90c['options'][_0x45fd52(-0x1d,-0x23)](_0x45fd52(-0x2f,-0x3f)),_0x3a5851=Date[_0x45fd52(-0x46,-0x60)]()+_0x36d424,_0x2e9ea2=_0x1bb90c[_0x45fd52(0x8,-0xf)];await AFK[_0x45fd52(-0x2c,-0x41)]({'userId':_0x1bb90c[_0x45fd52(-0x8,0x11)]['id']},{'userId':_0x1bb90c[_0x45fd52(-0x8,0x19)]['id'],'afkMessage':_0x560409,'backTime':_0x3a5851,'noBackTime':_0x354b6c,'afk':!![],'notifyMessageId':_0x2e9ea2['id']},{'upsert':!![],'new':!![]});const _0x908c9e={'user':_0x1bb90c[_0x45fd52(-0x8,0x1a)]['username'],'username':_0x1bb90c['user'][_0x45fd52(-0x42,-0x22)],'reason':_0x560409,'backTime':_0x45fd52(-0x36,-0xc)+Math[_0x45fd52(-0x1f,-0x3b)](_0x3a5851/0x3e8)+_0x45fd52(-0x40,-0x3c)};if(config[_0x45fd52(0x3,-0xd)][_0x45fd52(-0x5,-0x1b)]){const _0x15707f=await _0x1bb90c[_0x45fd52(-0x3f,-0x43)][_0x45fd52(-0x1c,0x2)][_0x45fd52(0xa,-0xb)](_0x1bb90c[_0x45fd52(-0x8,0x1)]['id']),_0x12ac7d=_0x15707f[_0x45fd52(-0x42,-0x27)]?_0x15707f[_0x45fd52(-0x42,-0x62)]:_0x15707f[_0x45fd52(-0x8,-0x21)]['username'],_0x5233c7=_0x12ac7d+'\x20'+config[_0x45fd52(0x3,-0x7)][_0x45fd52(-0x1b,-0x1b)];await handleNicknameUpdate(_0x1bb90c,_0x12ac7d,_0x5233c7),await AFK['findOneAndUpdate']({'userId':_0x1bb90c[_0x45fd52(-0x8,-0x33)]['id']},{'userId':_0x1bb90c[_0x45fd52(-0x8,-0x9)]['id'],'afkMessage':_0x560409,'backTime':_0x3a5851,'noBackTime':_0x354b6c,'afk':!![],'oldDisplayName':_0x12ac7d},{'upsert':!![],'new':!![]});}config[_0x45fd52(0x3,-0xc)][_0x45fd52(-0x22,-0x4d)]&&await handleAFKRoleAssignment(_0x1bb90c);if(config[_0x45fd52(0x3,0x1)][_0x45fd52(-0x2e,-0x56)]){const _0x1dfd1e=await createNotifyMessage(_0x1bb90c,_0x908c9e,_0x354b6c);await AFK[_0x45fd52(-0x2c,-0x5)]({'userId':_0x1bb90c[_0x45fd52(-0x8,-0x6)]['id']},{'notifyMessageId':_0x1dfd1e['id'],'notifyChannelId':_0x1bb90c['channel']['id']},{'new':!![]});}await _0x1bb90c[_0x45fd52(0x7,0x9)]({'content':replacePlaceholders(lang['AFK'][_0x45fd52(-0x15,-0x37)]||_0x45fd52(0x0,0x1d),_0x908c9e)});}catch(_0x58016d){console[_0x45fd52(-0x12,-0x34)](_0x45fd52(0x4,-0x20),_0x58016d),await _0x1bb90c['editReply']({'content':_0x45fd52(-0x38,-0x3d)+_0x58016d[_0x45fd52(-0x2f,-0x15)]});}}};