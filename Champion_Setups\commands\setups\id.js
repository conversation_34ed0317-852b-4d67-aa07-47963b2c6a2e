const Command = require("../../structures/Command");
const { ApplicationCommandOptionType } = require("discord.js");

module.exports = class ID extends Command {
  constructor(client) {
    super(client, {
      name: "id",
      description: client.cmdConfig.id.description,
      usage: client.cmdConfig.id.usage,
      permissions: client.cmdConfig.id.permissions,
      aliases: client.cmdConfig.id.aliases,
      category: "setups",
      listed: client.cmdConfig.id.enabled,
      slash: true,
      options: [{
        name: "channel",
        type: ApplicationCommandOptionType.Subcommand,
        description: "Channel which ID to obtain",
        options: [{
          name: "channelmention",
          type: ApplicationCommandOptionType.Channel,
          description: "Channel which ID to obtain",
          required: true,
        }]
      },{
        name: "role",
        type: ApplicationCommandOptionType.Subcommand,
        description: "Role which ID to obtain",
        options: [{
          name: "rolemention",
          type: ApplicationCommandOptionType.Role,
          description: "Role which ID to obtain",
          required: true,
        }]
      },{
        name: "user",
        type: ApplicationCommandOptionType.Subcommand,
        description: "User which ID to obtain",
        options: [{
          name: "usermention",
          type: ApplicationCommandOptionType.User,
          description: "User which ID to obtain",
          required: true,
        }]
      }]
    });
  }

  async run(message, args) {
    if(this.client.cmdConfig.id.enabled == false) return message.channel.send({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.general.cmd_disabled, this.client.embeds.error_color)] });
    let mention = message.mentions.channels.first() || message.mentions.roles.first() || message.mentions.users.first();
    if(!mention) return message.channel.send({ embeds: [this.client.utils.usage(this.client, message, this.client.cmdConfig.id.usage)]});

    message.channel.send({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.general.getid.replace("<type>", mention).replace("<id>", mention.id), this.client.embeds.success_color)] });
  }
  async slashRun(interaction, args) {
    if(this.client.cmdConfig.id.enabled == false) return interaction.reply({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.general.cmd_disabled, this.client.embeds.error_color)] });
    let option = args[0];
    let value = args[1];

    if(option == "channel") {
      interaction.reply({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.general.getid.replace("<type>", `<#${value}>`).replace("<id>", value), this.client.embeds.success_color)], ephemeral: this.client.cmdConfig.templates.ephemeral });
    } else if(option == "role") {
      interaction.reply({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.general.getid.replace("<type>", `<@&${value}>`).replace("<id>", value), this.client.embeds.success_color)], ephemeral: this.client.cmdConfig.templates.ephemeral });
    } else if(option == "user") {
      interaction.reply({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.general.getid.replace("<type>", `<@${value}>`).replace("<id>", value), this.client.embeds.success_color)], ephemeral: this.client.cmdConfig.templates.ephemeral });
    }
  }
};
