const { EmbedBuilder, PermissionsBitField } = require('discord.js');

module.exports = {
    name: 'ban',
    aliases: ['b'],
    description: 'Ban a member from the server.',
    async execute(message, args) {
        // Permission Check
        if (!message.member.permissions.has(PermissionsBitField.Flags.BanMembers)) {
            return message.channel.send({
                embeds: [
                    new EmbedBuilder()
                        .setColor('#FF0000')
                        .setDescription('<:cross:1354835259107180687> | You need the `Ban Members` permission to use this command.')
                ]
            });
        }

        // User Fetching
        const user = message.mentions.members.first() || message.guild.members.cache.get(args[0]);
        if (!user) {
            return message.channel.send({
                embeds: [
                    new EmbedBuilder()
                        .setColor('#FF0000')
                        .setDescription('<:cross:1354835259107180687> | Please mention a valid member to ban or provide a valid user ID.')
                ]
            });
        }

        // Ban Check
        if (!user.bannable) {
            return message.channel.send({
                embeds: [
                    new EmbedBuilder()
                        .setColor('#FF0000')
                        .setDescription('<:cross:1354835259107180687> | I cannot ban this member due to role hierarchy or missing permissions.')
                ]
            });
        }

        // Reason
        const reason = args.slice(1).join(' ') || 'No reason provided';
        await user.ban({ reason });

        message.channel.send({
            embeds: [
                new EmbedBuilder()
                    .setColor('#00FF00')
                    .setDescription(`<:tick:1354835257223807036> | Successfully banned **${user.user.tag}**.\n**Reason:** ${reason}`)
            ]
        });
    }
};