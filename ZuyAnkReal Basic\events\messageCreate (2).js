const fs = require('fs');
const path = require('path');
const ignorePath = path.join(__dirname, '../data/ignoredChannels.json');
const bypassPath = path.join(__dirname, '../data/bypassRoles.json');

module.exports = {
    name: 'messageCreate',
    async execute(message, client) {
        if (message.author.bot || !message.guild) return;

        const botMention = `<@${client.user.id}>`;
        let args;
        let commandName;

        if (message.content.startsWith(botMention)) {
            args = message.content.slice(botMention.length).trim().split(/ +/);
        } else {
            args = message.content.trim().split(/ +/);
        }

        commandName = args.shift()?.toLowerCase();
        if (!commandName) return;

        const command = client.commands.get(commandName) ||
            client.commands.find(cmd => cmd.aliases && cmd.aliases.includes(commandName));

        if (!command) return;

        // Ensure ignore & bypass files exist
        if (!fs.existsSync(ignorePath)) fs.writeFileSync(ignorePath, JSON.stringify([]));
        if (!fs.existsSync(bypassPath)) fs.writeFileSync(bypassPath, JSON.stringify([]));

        const ignored = JSON.parse(fs.readFileSync(ignorePath, 'utf8'));
        const bypass = JSON.parse(fs.readFileSync(bypassPath, 'utf8'));

        // Ignore logic with bypass
        if (ignored.includes(message.channel.id)) {
            const hasBypass = message.member.roles.cache.some(role => bypass.includes(role.id));
            if (!hasBypass) {
                const reply = await message.channel.send("This channel is ignored.");
                setTimeout(() => reply.delete().catch(() => {}), 500);
                return;
            }
        }

        try {
            await (command.execute ? command.execute(message, args, client) : command.run(message, args, client));
        } catch (error) {
            console.error('❌ Command Error:', error);
            message.reply('There was an error executing that command!');
        }
    }
};