const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.MessageDelete,
    async execute(message, client) {
        try {
            // Only handle moderation case deletions (this would be custom implementation)
            // This is a placeholder for when you implement a case system
            if (!message.guild) return;
            
            // Check if this is a moderation case message (you'd implement this logic)
            const isModerationCase = await client.db.isModerationCaseMessage(message.id);
            if (!isModerationCase) return;
            
            console.log(`🗑️ Moderation case deleted in ${message.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(message.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CASE_DELETE')) return;
            
            // Get case info from database
            const caseInfo = await client.db.getModerationCase(message.id);
            
            // Create event data
            const eventData = {
                eventType: 'CASE_DELETE',
                user: 'System',
                userId: null,
                action: 'Case moderation bị xóa',
                details: `Case moderation đã bị xóa`,
                target: caseInfo?.caseId || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the case from audit logs
            try {
                const auditLogs = await message.guild.fetchAuditLogs({
                    type: 72, // MESSAGE_DELETE
                    limit: 5
                });
                
                const auditEntry = auditLogs.entries.find(entry => 
                    entry.target?.id === message.author?.id && 
                    entry.extra?.channel?.id === message.channel?.id
                );
                
                if (auditEntry) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Case moderation bị xóa bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for case delete');
            }
            
            // Add to database
            await client.db.addAuditLog(message.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Case moderation bị xóa',
                `Vừa có case moderation bị xóa`
            );
            
            embed.setColor(0xe74c3c); // Red for deletion
            
            embed.addFields([
                {
                    name: '> Case ID',
                    value: `- ${caseInfo?.caseId || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian xóa',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add case info if available
            if (caseInfo) {
                embed.addFields([
                    {
                        name: '> Loại case',
                        value: `- ${caseInfo.type || 'Unknown'}`,
                        inline: true
                    },
                    {
                        name: '> Đối tượng',
                        value: `- ${caseInfo.targetUser || 'Unknown'}`,
                        inline: true
                    },
                    {
                        name: '> Moderator gốc',
                        value: `- ${caseInfo.moderator || 'Unknown'}`,
                        inline: true
                    }
                ]);
                
                if (caseInfo.reason) {
                    embed.addFields({
                        name: '> Lý do case gốc',
                        value: `- ${caseInfo.reason}`,
                        inline: false
                    });
                }
                
                if (caseInfo.createdAt) {
                    embed.addFields({
                        name: '> Thời gian tạo case',
                        value: `- <t:${Math.floor(new Date(caseInfo.createdAt).getTime() / 1000)}:F>`,
                        inline: true
                    });
                }
            }
            
            // Add deletion impact
            embed.addFields({
                name: '> 📝 Tác động của việc xóa case',
                value: [
                    '• Mất thông tin moderation quan trọng',
                    '• Không thể tham chiếu case này nữa',
                    '• Ảnh hưởng đến lịch sử moderation',
                    '• Có thể cần tạo case mới nếu cần'
                ].join('\n'),
                inline: false
            });
            
            // Add guidelines
            embed.addFields({
                name: '> ⚠️ Lưu ý quan trọng',
                value: [
                    '• Tránh xóa cases trừ khi thực sự cần thiết',
                    '• Nên edit thay vì xóa nếu có thể',
                    '• Backup thông tin quan trọng trước khi xóa',
                    '• Thông báo cho team moderation'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do xóa',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${message.guild.name} • Moderation Case Delete`,
                iconURL: message.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Case delete logged`);
            
        } catch (error) {
            console.error('Error in caseDelete audit log:', error);
        }
    }
};
