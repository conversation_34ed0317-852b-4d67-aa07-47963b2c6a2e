let createCanvas, loadImage, registerFont;
try {
  const canvas = require("canvas");
  createCanvas = canvas.createCanvas;
  loadImage = canvas.loadImage;
  registerFont = canvas.registerFont;
} catch (error) {
  console.log(
    "⚠️ Canvas package not installed. Canvas features will be disabled."
  );
  console.log('💡 Run "npm install canvas" to enable beautiful profile cards.');
}

const path = require("path");
const fs = require("fs");

class CanvasBuilder {
  constructor() {
    this.canvas = null;
    this.ctx = null;
    this.width = 800;
    this.height = 400;
  }

  // Tạo canvas mới
  createCanvas(width = 800, height = 400) {
    this.width = width;
    this.height = height;
    this.canvas = createCanvas(width, height);
    this.ctx = this.canvas.getContext("2d");
    return this;
  }

  // Vẽ background gradient
  drawBackground(color1 = "#1a1a2e", color2 = "#16213e") {
    const gradient = this.ctx.createLinearGradient(
      0,
      0,
      this.width,
      this.height
    );
    gradient.addColorStop(0, color1);
    gradient.addColorStop(1, color2);

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.width, this.height);
    return this;
  }

  // Vẽ border bo tròn
  drawRoundedRect(
    x,
    y,
    width,
    height,
    radius,
    fillColor,
    strokeColor = null,
    strokeWidth = 0
  ) {
    this.ctx.beginPath();
    this.ctx.moveTo(x + radius, y);
    this.ctx.lineTo(x + width - radius, y);
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    this.ctx.lineTo(x + width, y + height - radius);
    this.ctx.quadraticCurveTo(
      x + width,
      y + height,
      x + width - radius,
      y + height
    );
    this.ctx.lineTo(x + radius, y + height);
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    this.ctx.lineTo(x, y + radius);
    this.ctx.quadraticCurveTo(x, y, x + radius, y);
    this.ctx.closePath();

    if (fillColor) {
      this.ctx.fillStyle = fillColor;
      this.ctx.fill();
    }

    if (strokeColor && strokeWidth > 0) {
      this.ctx.strokeStyle = strokeColor;
      this.ctx.lineWidth = strokeWidth;
      this.ctx.stroke();
    }

    return this;
  }

  // Vẽ avatar tròn
  async drawCircularImage(
    imagePath,
    x,
    y,
    radius,
    borderColor = "#ffffff",
    borderWidth = 4
  ) {
    try {
      const image = await loadImage(imagePath);

      // Vẽ border
      if (borderWidth > 0) {
        this.ctx.beginPath();
        this.ctx.arc(
          x + radius,
          y + radius,
          radius + borderWidth,
          0,
          Math.PI * 2
        );
        this.ctx.fillStyle = borderColor;
        this.ctx.fill();
      }

      // Clip hình tròn
      this.ctx.save();
      this.ctx.beginPath();
      this.ctx.arc(x + radius, y + radius, radius, 0, Math.PI * 2);
      this.ctx.clip();

      // Vẽ hình
      this.ctx.drawImage(image, x, y, radius * 2, radius * 2);
      this.ctx.restore();
    } catch (error) {
      // Nếu không load được ảnh, vẽ placeholder
      this.ctx.beginPath();
      this.ctx.arc(x + radius, y + radius, radius, 0, Math.PI * 2);
      this.ctx.fillStyle = "#7289da";
      this.ctx.fill();

      // Vẽ icon user
      this.ctx.fillStyle = "#ffffff";
      this.ctx.font = `${radius}px Arial`;
      this.ctx.textAlign = "center";
      this.ctx.fillText("👤", x + radius, y + radius + radius / 3);
    }

    return this;
  }

  // Vẽ text với shadow
  drawText(
    text,
    x,
    y,
    font = "24px Arial",
    color = "#ffffff",
    shadowColor = "#000000",
    shadowBlur = 4
  ) {
    this.ctx.font = font;
    this.ctx.fillStyle = color;
    this.ctx.textAlign = "left";

    // Shadow
    if (shadowColor && shadowBlur > 0) {
      this.ctx.shadowColor = shadowColor;
      this.ctx.shadowBlur = shadowBlur;
      this.ctx.shadowOffsetX = 2;
      this.ctx.shadowOffsetY = 2;
    }

    this.ctx.fillText(text, x, y);

    // Reset shadow
    this.ctx.shadowColor = "transparent";
    this.ctx.shadowBlur = 0;
    this.ctx.shadowOffsetX = 0;
    this.ctx.shadowOffsetY = 0;

    return this;
  }

  // Vẽ progress bar
  drawProgressBar(
    x,
    y,
    width,
    height,
    progress,
    bgColor = "#2c2f33",
    fillColor = "#7289da",
    borderRadius = 10
  ) {
    // Background
    this.drawRoundedRect(x, y, width, height, borderRadius, bgColor);

    // Progress fill
    const fillWidth = (width * progress) / 100;
    if (fillWidth > 0) {
      this.drawRoundedRect(x, y, fillWidth, height, borderRadius, fillColor);
    }

    return this;
  }

  // Vẽ icon emoji
  drawEmoji(emoji, x, y, size = 24) {
    this.ctx.font = `${size}px Arial`;
    this.ctx.textAlign = "center";
    this.ctx.fillText(emoji, x, y);
    return this;
  }

  // Vẽ số tiền với format
  drawCurrency(amount, x, y, font = "20px Arial", color = "#ffd700") {
    const formatted = amount.toLocaleString("vi-VN") + " 💰";
    this.drawText(formatted, x, y, font, color);
    return this;
  }

  // Vẽ level badge
  drawLevelBadge(level, x, y, size = 60) {
    // Background circle
    this.ctx.beginPath();
    this.ctx.arc(x + size / 2, y + size / 2, size / 2, 0, Math.PI * 2);
    this.ctx.fillStyle = "#7289da";
    this.ctx.fill();

    // Border
    this.ctx.strokeStyle = "#ffffff";
    this.ctx.lineWidth = 3;
    this.ctx.stroke();

    // Level text
    this.ctx.fillStyle = "#ffffff";
    this.ctx.font = `bold ${size / 3}px Arial`;
    this.ctx.textAlign = "center";
    this.ctx.fillText(level.toString(), x + size / 2, y + size / 2 + size / 8);

    return this;
  }

  // Vẽ decorative elements
  drawDecorations() {
    // Vẽ các hình trang trí
    const colors = ["#7289da", "#43b581", "#faa61a", "#f04747"];

    for (let i = 0; i < 20; i++) {
      const x = Math.random() * this.width;
      const y = Math.random() * this.height;
      const radius = Math.random() * 3 + 1;
      const color = colors[Math.floor(Math.random() * colors.length)];

      this.ctx.beginPath();
      this.ctx.arc(x, y, radius, 0, Math.PI * 2);
      this.ctx.fillStyle = color + "30"; // 30% opacity
      this.ctx.fill();
    }

    return this;
  }

  // Lấy buffer của canvas
  getBuffer() {
    return this.canvas.toBuffer("image/png");
  }

  // Lưu file
  saveToFile(filePath) {
    const buffer = this.getBuffer();
    fs.writeFileSync(filePath, buffer);
    return this;
  }
}

// Hàm tạo profile card
async function createProfileCard(user, userData) {
  if (!createCanvas) {
    throw new Error(
      'Canvas package not installed. Please run "npm install canvas" to enable this feature.'
    );
  }

  const canvas = new CanvasBuilder();

  // Tạo canvas
  canvas.createCanvas(800, 400);

  // Background gradient
  canvas.drawBackground("#1a1a2e", "#16213e");

  // Decorations
  canvas.drawDecorations();

  // Main card background
  canvas.drawRoundedRect(
    20,
    20,
    760,
    360,
    20,
    "rgba(44, 47, 51, 0.9)",
    "#7289da",
    2
  );

  // Avatar
  const avatarUrl = user.displayAvatarURL({ extension: "png", size: 128 });
  await canvas.drawCircularImage(avatarUrl, 50, 50, 60, "#7289da", 4);

  // Username
  canvas.drawText(user.username, 180, 90, "bold 32px Arial", "#ffffff");

  // Level badge
  canvas.drawLevelBadge(userData.level, 680, 50);

  // Stats section
  const statsY = 150;

  // Balance
  canvas.drawEmoji("💰", 60, statsY);
  canvas.drawText("Số dư:", 80, statsY, "18px Arial", "#b9bbbe");
  canvas.drawCurrency(userData.balance, 160, statsY, "18px Arial", "#ffd700");

  // Bank
  canvas.drawEmoji("🏦", 60, statsY + 30);
  canvas.drawText("Ngân hàng:", 80, statsY + 30, "18px Arial", "#b9bbbe");
  canvas.drawCurrency(userData.bank, 180, statsY + 30, "18px Arial", "#43b581");

  // Total wealth
  canvas.drawEmoji("💎", 60, statsY + 60);
  canvas.drawText("Tổng tài sản:", 80, statsY + 60, "18px Arial", "#b9bbbe");
  canvas.drawCurrency(
    userData.balance + userData.bank,
    200,
    statsY + 60,
    "18px Arial",
    "#f04747"
  );

  // Level progress
  const levelInfo = userData.getLevelInfo();
  canvas.drawText(
    `Level ${userData.level}`,
    60,
    statsY + 110,
    "bold 20px Arial",
    "#7289da"
  );
  canvas.drawText(
    `${levelInfo.progressExp}/${levelInfo.neededExp} EXP`,
    60,
    statsY + 135,
    "14px Arial",
    "#b9bbbe"
  );

  // Progress bar
  canvas.drawProgressBar(
    60,
    statsY + 145,
    300,
    20,
    levelInfo.progress,
    "#2c2f33",
    "#7289da"
  );
  canvas.drawText(
    `${levelInfo.progress}%`,
    370,
    statsY + 160,
    "14px Arial",
    "#ffffff"
  );

  // Right side stats
  const rightX = 450;

  // Prestige
  canvas.drawEmoji("⭐", rightX, statsY);
  canvas.drawText(
    `Prestige: ${userData.prestige}`,
    rightX + 20,
    statsY,
    "16px Arial",
    "#ffd700"
  );

  // Daily streak
  canvas.drawEmoji("🔥", rightX, statsY + 30);
  canvas.drawText(
    `Daily Streak: ${userData.streakDaily} ngày`,
    rightX + 20,
    statsY + 30,
    "16px Arial",
    "#f04747"
  );

  // Total earned
  canvas.drawEmoji("📈", rightX, statsY + 60);
  canvas.drawText(
    "Tổng kiếm được:",
    rightX + 20,
    statsY + 60,
    "14px Arial",
    "#b9bbbe"
  );
  canvas.drawCurrency(
    userData.totalEarned,
    rightX + 20,
    statsY + 80,
    "14px Arial",
    "#43b581"
  );

  // Join date
  const joinDate = new Date(userData.createdAt).toLocaleDateString("vi-VN");
  canvas.drawEmoji("📅", rightX, statsY + 110);
  canvas.drawText(
    `Tham gia: ${joinDate}`,
    rightX + 20,
    statsY + 110,
    "14px Arial",
    "#b9bbbe"
  );

  return canvas.getBuffer();
}

module.exports = {
  CanvasBuilder,
  createProfileCard,
};
