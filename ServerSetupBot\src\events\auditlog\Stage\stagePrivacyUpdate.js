const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.StageInstanceUpdate,
    async execute(oldStageInstance, newStageInstance, client) {
        try {
            // Only handle privacy level changes
            if (oldStageInstance.privacyLevel === newStageInstance.privacyLevel) return;
            
            console.log(`🔒 Stage privacy updated: ${newStageInstance.topic} in ${newStageInstance.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newStageInstance.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'STAGE_PRIVACY_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'STAGE_PRIVACY_UPDATE',
                user: 'System',
                userId: null,
                action: '<PERSON><PERSON><PERSON><PERSON> riê<PERSON> tư stage được cập nhật',
                details: `<PERSON>uy<PERSON>n riêng tư của stage **${newStageInstance.topic || 'No topic'}** đã được cập nhật`,
                target: newStageInstance.topic || 'No topic',
                channel: newStageInstance.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the stage privacy from audit logs
            try {
                const auditLogs = await newStageInstance.guild.fetchAuditLogs({
                    type: 84, // STAGE_INSTANCE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newStageInstance.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Quyền riêng tư stage được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for stage privacy update');
            }
            
            // Add to database
            await client.db.addAuditLog(newStageInstance.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔒 Quyền riêng tư stage được cập nhật',
                `Vừa có quyền riêng tư stage được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Privacy level mapping
            const privacyLevels = {
                1: '🌍 Public (Mọi người có thể tham gia)',
                2: '🏠 Guild Only (Chỉ thành viên server)'
            };
            
            embed.addFields([
                {
                    name: '> Chủ đề stage',
                    value: `- ${newStageInstance.topic || 'Không có chủ đề'}`,
                    inline: false
                },
                {
                    name: '> ID stage',
                    value: `- ${newStageInstance.id}`,
                    inline: true
                },
                {
                    name: '> Kênh stage',
                    value: `- ${newStageInstance.channel || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Quyền riêng tư cũ',
                    value: `- ${privacyLevels[oldStageInstance.privacyLevel] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Quyền riêng tư mới',
                    value: `- ${privacyLevels[newStageInstance.privacyLevel] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Thời gian thay đổi',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add stage duration so far
            if (newStageInstance.createdTimestamp) {
                const duration = Date.now() - newStageInstance.createdTimestamp;
                const hours = Math.floor(duration / (1000 * 60 * 60));
                const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
                
                embed.addFields([
                    {
                        name: '> Stage bắt đầu lúc',
                        value: `- <t:${Math.floor(newStageInstance.createdTimestamp / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Đã diễn ra được',
                        value: `- ${hours} giờ, ${minutes} phút`,
                        inline: true
                    }
                ]);
            }
            
            // Add channel info
            if (newStageInstance.channel) {
                embed.addFields([
                    {
                        name: '> ID kênh',
                        value: `- ${newStageInstance.channel.id}`,
                        inline: true
                    },
                    {
                        name: '> Số người trong stage',
                        value: `- ${newStageInstance.channel.members?.size || 0} thành viên`,
                        inline: true
                    }
                ]);
            }
            
            // Add impact explanation based on privacy change
            if (newStageInstance.privacyLevel === 1 && oldStageInstance.privacyLevel === 2) {
                embed.addFields({
                    name: '> 📈 Tác động (Chuyển sang Public)',
                    value: [
                        '• Mọi người có thể tìm thấy stage này',
                        '• Không cần là thành viên server để tham gia',
                        '• Stage có thể xuất hiện trong Discovery',
                        '• Tăng khả năng có người lạ tham gia',
                        '• Cần cẩn thận hơn về nội dung thảo luận'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0xe67e22); // Orange-red for more open
            } else if (newStageInstance.privacyLevel === 2 && oldStageInstance.privacyLevel === 1) {
                embed.addFields({
                    name: '> 📉 Tác động (Chuyển sang Guild Only)',
                    value: [
                        '• Chỉ thành viên server có thể tham gia',
                        '• Tăng tính riêng tư và bảo mật',
                        '• Không xuất hiện trong Discovery công khai',
                        '• Phù hợp cho thảo luận nội bộ',
                        '• Kiểm soát tốt hơn về người tham gia'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for more secure
            }
            
            // Add privacy guidelines
            embed.addFields({
                name: '> 💡 Hướng dẫn về quyền riêng tư stage',
                value: [
                    '**Public Stage:**',
                    '• Phù hợp cho sự kiện công khai',
                    '• Nội dung phải phù hợp với mọi đối tượng',
                    '• Có thể thu hút nhiều người tham gia',
                    '',
                    '**Guild Only Stage:**',
                    '• Phù hợp cho thảo luận nội bộ',
                    '• Kiểm soát được người tham gia',
                    '• An toàn hơn cho nội dung nhạy cảm'
                ].join('\n'),
                inline: false
            });
            
            // Add stage link
            if (newStageInstance.channel) {
                embed.addFields({
                    name: '> Link tham gia stage',
                    value: `- [Nhấn để tham gia](https://discord.com/channels/${newStageInstance.guild.id}/${newStageInstance.channel.id})`,
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do thay đổi',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newStageInstance.guild.name} • Stage Privacy Update`,
                iconURL: newStageInstance.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Stage privacy update logged: ${newStageInstance.topic}`);
            
        } catch (error) {
            console.error('Error in stagePrivacyUpdate audit log:', error);
        }
    }
};
