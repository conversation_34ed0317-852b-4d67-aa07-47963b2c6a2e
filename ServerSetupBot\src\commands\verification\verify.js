const {
  SlashCommandBuilder,
  ChannelType,
  ButtonBuilder,
  ButtonStyle,
  ActionRowBuilder,
  PermissionFlagsBits,
} = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
} = require("../../utils/embedBuilder.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("verify")
    .setDescription("Hệ thống xác minh thành viên nâng cao")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("setup")
        .setDescription("Thiết lập hệ thống xác minh")
        .addChannelOption((option) =>
          option
            .setName("channel")
            .setDescription("Kênh xác minh")
            .setRequired(true)
            .addChannelTypes(ChannelType.GuildText)
        )
        .addRoleOption((option) =>
          option
            .setName("verified_role")
            .setDescription("Role sẽ được g<PERSON>u khi xác minh")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("type")
            .setDescription("Loại xác minh")
            .setRequired(false)
            .addChoices(
              { name: "🔘 Reaction - React emoji", value: "reaction" },
              { name: "🔲 Button - Nhấn nút", value: "button" },
              { name: "🧩 Captcha - Giải captcha", value: "captcha" },
              { name: "📝 Manual - Xác minh thủ công", value: "manual" },
              { name: "📧 Email - Xác minh email", value: "email" },
              { name: "📱 Phone - Xác minh số điện thoại", value: "phone" }
            )
        )
        .addRoleOption((option) =>
          option
            .setName("unverified_role")
            .setDescription("Role cho thành viên chưa xác minh (tùy chọn)")
            .setRequired(false)
        )
        .addStringOption((option) =>
          option
            .setName("emoji")
            .setDescription("Emoji cho reaction verify (mặc định: ✅)")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("auto_kick")
            .setDescription("Tự động kick thành viên không xác minh sau 24h")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("dm_welcome")
            .setDescription("Gửi tin nhắn chào mừng qua DM sau khi xác minh")
            .setRequired(false)
        )
        .addIntegerOption((option) =>
          option
            .setName("timeout_hours")
            .setDescription("Thời gian timeout cho xác minh (giờ, 1-168)")
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(168)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand.setName("disable").setDescription("Tắt hệ thống xác minh")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("config")
        .setDescription("Xem cấu hình hệ thống xác minh")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("message")
        .setDescription("Gửi tin nhắn xác minh vào channel")
        .addStringOption((option) =>
          option
            .setName("title")
            .setDescription("Tiêu đề tin nhắn xác minh (tùy chọn)")
            .setRequired(false)
        )
        .addStringOption((option) =>
          option
            .setName("description")
            .setDescription("Mô tả tin nhắn xác minh (tùy chọn)")
            .setRequired(false)
        )
        .addStringOption((option) =>
          option
            .setName("color")
            .setDescription("Màu embed (hex code, ví dụ: #00ff00)")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("force")
        .setDescription("Xác minh thủ công cho thành viên")
        .addUserOption((option) =>
          option
            .setName("user")
            .setDescription("Thành viên cần xác minh")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do xác minh thủ công")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("unverify")
        .setDescription("Hủy xác minh cho thành viên")
        .addUserOption((option) =>
          option
            .setName("user")
            .setDescription("Thành viên cần hủy xác minh")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do hủy xác minh")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("stats")
        .setDescription("Xem thống kê hệ thống xác minh")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("pending")
        .setDescription("Xem danh sách thành viên chờ xác minh")
        .addIntegerOption((option) =>
          option
            .setName("page")
            .setDescription("Trang cần xem (mặc định: 1)")
            .setRequired(false)
            .setMinValue(1)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("cleanup")
        .setDescription("Dọn dẹp thành viên không xác minh")
        .addBooleanOption((option) =>
          option
            .setName("confirm")
            .setDescription("Xác nhận thực hiện cleanup (bắt buộc)")
            .setRequired(true)
        )
        .addIntegerOption((option) =>
          option
            .setName("days")
            .setDescription("Kick thành viên không xác minh sau X ngày (1-30)")
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(30)
        )
    ),
  category: "verification",
  adminOnly: true,
  manageServer: false,

  async execute(interaction, client) {
    // Kiểm tra quyền bot
    if (
      !interaction.guild.members.me.permissions.has([
        PermissionFlagsBits.ManageRoles,
        PermissionFlagsBits.ManageChannels,
        PermissionFlagsBits.KickMembers,
      ])
    ) {
      const errorEmbed = createErrorEmbed(
        "Bot thiếu quyền!",
        "Bot cần các quyền sau:\n• Manage Roles\n• Manage Channels\n• Kick Members"
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    const subcommand = interaction.options.getSubcommand();
    const guildId = interaction.guild.id;

    switch (subcommand) {
      case "setup":
        await handleSetup(interaction, client, guildId);
        break;
      case "disable":
        await handleDisable(interaction, client, guildId);
        break;
      case "config":
        await handleConfig(interaction, client, guildId);
        break;
      case "message":
        await handleMessage(interaction, client, guildId);
        break;
      case "force":
        await handleForceVerify(interaction, client, guildId);
        break;
      case "unverify":
        await handleUnverify(interaction, client, guildId);
        break;
      case "stats":
        await handleStats(interaction, client, guildId);
        break;
      case "pending":
        await handlePending(interaction, client, guildId);
        break;
      case "cleanup":
        await handleCleanup(interaction, client, guildId);
        break;
    }
  },
};

async function handleSetup(interaction, client, guildId) {
  const channel = interaction.options.getChannel("channel");
  const verifiedRole = interaction.options.getRole("verified_role");
  const verificationType = interaction.options.getString("type") || "button";
  const unverifiedRole = interaction.options.getRole("unverified_role");
  const emoji = interaction.options.getString("emoji") || "✅";
  const autoKick = interaction.options.getBoolean("auto_kick") || false;
  const dmWelcome = interaction.options.getBoolean("dm_welcome") || false;
  const timeoutHours = interaction.options.getInteger("timeout_hours") || 24;

  // Validate emoji for reaction type
  if (verificationType === "reaction") {
    const emojiRegex = /^(\p{Emoji}|<a?:\w+:\d+>)$/u;
    if (!emojiRegex.test(emoji)) {
      const errorEmbed = createErrorEmbed(
        "Emoji không hợp lệ!",
        "Vui lòng cung cấp emoji hợp lệ cho loại xác minh reaction."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  }

  // Check role hierarchy
  if (
    verifiedRole.position >= interaction.guild.members.me.roles.highest.position
  ) {
    const errorEmbed = createErrorEmbed(
      "Role quá cao!",
      "Role verified phải thấp hơn role cao nhất của bot."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    // Lưu cấu hình vào database
    await client.db.updateVerificationConfig(guildId, {
      enabled: 1,
      verification_type: verificationType,
      verification_channel_id: channel.id,
      verified_role_id: verifiedRole.id,
      verification_role_id: unverifiedRole?.id || null,
      verification_emoji: emoji,
      auto_kick: autoKick ? 1 : 0,
      dm_welcome: dmWelcome ? 1 : 0,
      timeout_hours: timeoutHours,
      verification_message: null, // Will be set when message is sent
    });

    const successEmbed = createSuccessEmbed(
      "Hệ thống xác minh đã được thiết lập!",
      `**Kênh xác minh:** ${channel}\n` +
        `**Loại xác minh:** ${getVerificationTypeName(verificationType)}\n` +
        `**Role sau xác minh:** ${verifiedRole}\n` +
        `**Role chưa xác minh:** ${unverifiedRole || "Không có"}\n` +
        `**Emoji:** ${
          verificationType === "reaction" ? emoji : "Không áp dụng"
        }\n` +
        `**Auto kick:** ${autoKick ? "Bật" : "Tắt"}\n` +
        `**DM welcome:** ${dmWelcome ? "Bật" : "Tắt"}\n` +
        `**Timeout:** ${timeoutHours} giờ`
    );

    successEmbed.addFields({
      name: "📝 Bước tiếp theo",
      value: "Sử dụng `/verify message` để gửi tin nhắn xác minh vào channel.",
      inline: false,
    });

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi thiết lập verification:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể thiết lập hệ thống xác minh. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleDisable(interaction, client, guildId) {
  try {
    await client.db.updateVerificationConfig(guildId, {
      enabled: 0,
    });

    const successEmbed = createSuccessEmbed(
      "Hệ thống xác minh đã được tắt!",
      "Thành viên mới sẽ không cần xác minh nữa."
    );

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi tắt verification:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể tắt hệ thống xác minh. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleConfig(interaction, client, guildId) {
  try {
    const config = await client.db.getVerificationConfig(guildId);

    if (!config) {
      const infoEmbed = createInfoEmbed(
        "Hệ thống chưa được thiết lập",
        "Sử dụng `/verify setup` để thiết lập hệ thống xác minh."
      );
      return await interaction.reply({ embeds: [infoEmbed] });
    }

    const channel = interaction.guild.channels.cache.get(
      config.verification_channel_id
    );
    const verifiedRole = interaction.guild.roles.cache.get(
      config.verified_role_id
    );
    const unverifiedRole = config.verification_role_id
      ? interaction.guild.roles.cache.get(config.verification_role_id)
      : null;

    const embed = createInfoEmbed(
      "⚙️ Cấu hình hệ thống xác minh",
      `**Trạng thái:** ${config.enabled ? "✅ Đã bật" : "❌ Đã tắt"}\n` +
        `**Loại xác minh:** ${getVerificationTypeName(
          config.verification_type
        )}\n` +
        `**Kênh xác minh:** ${channel || "❌ Đã bị xóa"}\n` +
        `**Role sau xác minh:** ${verifiedRole || "❌ Đã bị xóa"}\n` +
        `**Role chưa xác minh:** ${unverifiedRole || "Không có"}\n` +
        `**Emoji:** ${config.verification_emoji || "✅"}\n` +
        `**Auto kick:** ${config.auto_kick ? "Bật" : "Tắt"}\n` +
        `**DM welcome:** ${config.dm_welcome ? "Bật" : "Tắt"}\n` +
        `**Timeout:** ${config.timeout_hours || 24} giờ`
    );

    // Thống kê
    const stats = await getVerificationStats(client, guildId);
    embed.addFields({
      name: "📊 Thống kê",
      value: `**Đã xác minh:** ${stats.verified}\n**Chờ xác minh:** ${stats.pending}\n**Tổng thành viên:** ${stats.total}`,
      inline: true,
    });

    embed.addFields({
      name: "🔧 Quản lý",
      value:
        "• `/verify setup` - Cập nhật cấu hình\n" +
        "• `/verify disable` - Tắt hệ thống\n" +
        "• `/verify message` - Gửi tin nhắn xác minh\n" +
        "• `/verify stats` - Xem thống kê chi tiết",
      inline: false,
    });

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error("Lỗi khi xem config verification:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể lấy cấu hình xác minh. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleMessage(interaction, client, guildId) {
  const customTitle = interaction.options.getString("title");
  const customDescription = interaction.options.getString("description");
  const customColor = interaction.options.getString("color");

  try {
    const config = await client.db.getVerificationConfig(guildId);

    if (!config || !config.enabled) {
      const errorEmbed = createErrorEmbed(
        "Hệ thống chưa được bật!",
        "Vui lòng sử dụng `/verify setup` để thiết lập hệ thống xác minh trước."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    const channel = interaction.guild.channels.cache.get(
      config.verification_channel_id
    );
    if (!channel) {
      const errorEmbed = createErrorEmbed(
        "Kênh xác minh không tồn tại!",
        "Kênh xác minh đã bị xóa. Vui lòng thiết lập lại."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    // Parse color
    let embedColor = 0x00ff00; // Default green
    if (customColor) {
      const colorMatch = customColor.match(/^#?([0-9a-f]{6})$/i);
      if (colorMatch) {
        embedColor = parseInt(colorMatch[1], 16);
      }
    }

    // Tạo embed xác minh
    const verificationEmbed = createInfoEmbed(
      customTitle || "🔐 Xác minh thành viên",
      customDescription ||
        `Chào mừng bạn đến với **${interaction.guild.name}**!\n\n` +
          `Để truy cập đầy đủ vào server, bạn cần xác minh tài khoản của mình.\n\n` +
          `**Cách xác minh:**\n${getVerificationInstructions(
            config.verification_type,
            config.verification_emoji
          )}`
    );

    verificationEmbed.setColor(embedColor);
    verificationEmbed.setThumbnail(
      interaction.guild.iconURL({ dynamic: true })
    );
    verificationEmbed.setFooter({
      text: "Hệ thống xác minh tự động • Server Setup Bot",
      iconURL: client.user.displayAvatarURL(),
    });

    let messageOptions = { embeds: [verificationEmbed] };

    // Thêm button hoặc reaction tùy theo loại
    if (config.verification_type === "button") {
      const verifyButton = new ButtonBuilder()
        .setCustomId("verify_member")
        .setLabel("Xác minh tài khoản")
        .setStyle(ButtonStyle.Success)
        .setEmoji("✅");

      const row = new ActionRowBuilder().addComponents(verifyButton);
      messageOptions.components = [row];
    }

    const message = await channel.send(messageOptions);

    // Thêm reaction nếu cần
    if (config.verification_type === "reaction") {
      await message.react(config.verification_emoji);
    }

    // Lưu message ID
    await client.db.updateVerificationConfig(guildId, {
      verification_message: message.id,
    });

    const successEmbed = createSuccessEmbed(
      "Tin nhắn xác minh đã được gửi!",
      `Tin nhắn xác minh đã được gửi tới ${channel}\n\n` +
        `Thành viên mới có thể xác minh bằng cách ${getVerificationAction(
          config.verification_type
        )}.`
    );

    await interaction.reply({ embeds: [successEmbed], ephemeral: true });
  } catch (error) {
    console.error("Lỗi khi gửi verification message:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi gửi tin nhắn!",
      "Không thể gửi tin nhắn xác minh. Vui lòng kiểm tra quyền bot!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

function getVerificationTypeName(type) {
  const types = {
    reaction: "🔘 Reaction",
    button: "🔲 Button",
    captcha: "🧩 Captcha",
    manual: "📝 Manual",
    email: "📧 Email",
    phone: "📱 Phone",
  };
  return types[type] || type;
}

function getVerificationInstructions(type, emoji) {
  switch (type) {
    case "reaction":
      return `React với ${emoji} vào tin nhắn này`;
    case "button":
      return `Nhấn nút **Xác minh tài khoản** bên dưới`;
    case "captcha":
      return `Giải captcha sẽ được gửi qua DM`;
    case "manual":
      return `Chờ admin xác minh thủ công`;
    case "email":
      return `Xác minh qua email đã đăng ký`;
    case "phone":
      return `Xác minh qua số điện thoại`;
    default:
      return `Làm theo hướng dẫn để xác minh`;
  }
}

function getVerificationAction(type) {
  switch (type) {
    case "reaction":
      return "react emoji";
    case "button":
      return "nhấn nút";
    case "captcha":
      return "giải captcha";
    case "manual":
      return "chờ admin xác minh";
    case "email":
      return "xác minh email";
    case "phone":
      return "xác minh số điện thoại";
    default:
      return "làm theo hướng dẫn";
  }
}

async function getVerificationStats(client, guildId) {
  const config = await client.db.getVerificationConfig(guildId);
  if (!config) return { verified: 0, pending: 0, total: 0 };

  const guild = client.guilds.cache.get(guildId);
  if (!guild) return { verified: 0, pending: 0, total: 0 };

  await guild.members.fetch();

  const verifiedRole = guild.roles.cache.get(config.verified_role_id);
  const unverifiedRole = config.verification_role_id
    ? guild.roles.cache.get(config.verification_role_id)
    : null;

  const totalMembers = guild.members.cache.filter((m) => !m.user.bot).size;
  const verifiedMembers = verifiedRole
    ? verifiedRole.members.filter((m) => !m.user.bot).size
    : 0;
  const pendingMembers = unverifiedRole
    ? unverifiedRole.members.filter((m) => !m.user.bot).size
    : totalMembers - verifiedMembers;

  return {
    verified: verifiedMembers,
    pending: pendingMembers,
    total: totalMembers,
  };
}
