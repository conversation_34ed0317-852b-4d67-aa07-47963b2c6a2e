const { SlashCommandBuilder, PermissionFlagsBits } = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createWarningEmbed,
} = require("../../utils/embedBuilder.js");
const { sendModerationLog } = require("../../utils/moderationUtils.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("massban")
    .setDescription("Ban nhiều thành viên cùng lúc")
    .addStringOption((option) =>
      option
        .setName("user_ids")
        .setDescription("Danh sách ID thành viên (cách nhau bởi dấu phẩy)")
        .setRequired(true)
    )
    .addBooleanOption((option) =>
      option
        .setName("confirm")
        .setDescription("Xác nhận thực hiện mass ban (bắt buộc)")
        .setRequired(true)
    )
    .addStringOption((option) =>
      option.setName("reason").setDescription("Lý do ban").setRequired(false)
    )
    .addIntegerOption((option) =>
      option
        .setName("delete_days")
        .setDescription("Xóa tin nhắn trong X ngày (0-7)")
        .setRequired(false)
        .setMinValue(0)
        .setMaxValue(7)
    ),
  category: "moderation",
  adminOnly: false,
  manageServer: false,

  async execute(interaction, client) {
    // Kiểm tra quyền
    if (!interaction.member.permissions.has(PermissionFlagsBits.BanMembers)) {
      const errorEmbed = createErrorEmbed(
        "Bạn không có quyền!",
        "Bạn cần quyền `Ban Members` để sử dụng lệnh này."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    if (
      !interaction.guild.members.me.permissions.has(
        PermissionFlagsBits.BanMembers
      )
    ) {
      const errorEmbed = createErrorEmbed(
        "Bot thiếu quyền!",
        "Bot cần quyền `Ban Members` để thực hiện lệnh này."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    const userIdsStr = interaction.options.getString("user_ids");
    const reason = interaction.options.getString("reason") || "Mass ban";
    const deleteDays = interaction.options.getInteger("delete_days") || 0;
    const confirm = interaction.options.getBoolean("confirm");
    const guildId = interaction.guild.id;

    if (!confirm) {
      const errorEmbed = createErrorEmbed(
        "Xác nhận bắt buộc!",
        "Bạn phải xác nhận để thực hiện mass ban."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    // Parse user IDs
    const userIds = userIdsStr
      .split(",")
      .map((id) => id.trim())
      .filter((id) => /^\d{17,19}$/.test(id));

    if (userIds.length === 0) {
      const errorEmbed = createErrorEmbed(
        "Danh sách ID không hợp lệ!",
        "Vui lòng cung cấp danh sách ID Discord hợp lệ, cách nhau bởi dấu phẩy."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    if (userIds.length > 20) {
      const errorEmbed = createErrorEmbed(
        "Quá nhiều user!",
        "Chỉ có thể ban tối đa 20 thành viên cùng lúc."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    await interaction.deferReply();

    try {
      const results = {
        success: [],
        failed: [],
        alreadyBanned: [],
        hierarchyError: [],
        notFound: [],
      };

      // Thông báo bắt đầu
      const startEmbed = createWarningEmbed(
        "⚠️ Đang thực hiện Mass Ban",
        `Đang xử lý ${userIds.length} thành viên...\nVui lòng chờ...`
      );
      await interaction.editReply({ embeds: [startEmbed] });

      for (const userId of userIds) {
        try {
          // Kiểm tra xem đã bị ban chưa
          const existingBan = await interaction.guild.bans
            .fetch(userId)
            .catch(() => null);
          if (existingBan) {
            results.alreadyBanned.push({
              id: userId,
              tag: existingBan.user.tag,
            });
            continue;
          }

          // Fetch user info
          const user = await client.users.fetch(userId).catch(() => null);
          if (!user) {
            results.notFound.push(userId);
            continue;
          }

          // Kiểm tra member nếu có trong server
          const member = await interaction.guild.members
            .fetch(userId)
            .catch(() => null);
          if (member) {
            // Kiểm tra hierarchy
            if (
              member.roles.highest.position >=
              interaction.member.roles.highest.position
            ) {
              results.hierarchyError.push({
                id: userId,
                tag: user.tag,
                reason: "User hierarchy",
              });
              continue;
            }

            if (
              member.roles.highest.position >=
              interaction.guild.members.me.roles.highest.position
            ) {
              results.hierarchyError.push({
                id: userId,
                tag: user.tag,
                reason: "Bot hierarchy",
              });
              continue;
            }

            if (!member.bannable) {
              results.hierarchyError.push({
                id: userId,
                tag: user.tag,
                reason: "Not bannable",
              });
              continue;
            }
          }

          // Thực hiện ban
          await interaction.guild.bans.create(userId, {
            reason: `${reason} | Mass ban bởi: ${interaction.user.tag}`,
            deleteMessageDays: deleteDays,
          });

          results.success.push({ id: userId, tag: user.tag });

          // Log vào database
          await client.db.addModerationLog(guildId, {
            type: "ban",
            userId: userId,
            moderatorId: interaction.user.id,
            reason: `${reason} (Mass ban)`,
            duration: null,
            deleteDays: deleteDays,
          });

          // Delay nhỏ để tránh rate limit
          await new Promise((resolve) => setTimeout(resolve, 500));
        } catch (error) {
          console.error(`Lỗi khi ban user ${userId}:`, error);
          results.failed.push({ id: userId, error: error.message });
        }
      }

      // Tạo embed kết quả
      const resultEmbed = createSuccessEmbed(
        "🔨 Mass Ban hoàn thành!",
        `**Tổng số xử lý:** ${userIds.length}\n` +
          `**Thành công:** ${results.success.length}\n` +
          `**Thất bại:** ${
            results.failed.length +
            results.hierarchyError.length +
            results.notFound.length
          }\n` +
          `**Đã bị ban:** ${results.alreadyBanned.length}`
      );

      if (results.success.length > 0) {
        const successList = results.success
          .slice(0, 10)
          .map((u) => `• ${u.tag} (${u.id})`)
          .join("\n");
        resultEmbed.addFields({
          name: `✅ Thành công (${results.success.length})`,
          value:
            successList +
            (results.success.length > 10
              ? `\n*... và ${results.success.length - 10} user khác*`
              : ""),
          inline: false,
        });
      }

      if (results.failed.length > 0) {
        const failedList = results.failed
          .slice(0, 5)
          .map((u) => `• ${u.id}: ${u.error}`)
          .join("\n");
        resultEmbed.addFields({
          name: `❌ Thất bại (${results.failed.length})`,
          value:
            failedList +
            (results.failed.length > 5
              ? `\n*... và ${results.failed.length - 5} lỗi khác*`
              : ""),
          inline: false,
        });
      }

      if (results.hierarchyError.length > 0) {
        const hierarchyList = results.hierarchyError
          .slice(0, 5)
          .map((u) => `• ${u.tag} (${u.reason})`)
          .join("\n");
        resultEmbed.addFields({
          name: `⚠️ Lỗi quyền hạn (${results.hierarchyError.length})`,
          value:
            hierarchyList +
            (results.hierarchyError.length > 5
              ? `\n*... và ${results.hierarchyError.length - 5} user khác*`
              : ""),
          inline: false,
        });
      }

      if (results.alreadyBanned.length > 0) {
        const bannedList = results.alreadyBanned
          .slice(0, 5)
          .map((u) => `• ${u.tag}`)
          .join("\n");
        resultEmbed.addFields({
          name: `🔨 Đã bị ban (${results.alreadyBanned.length})`,
          value:
            bannedList +
            (results.alreadyBanned.length > 5
              ? `\n*... và ${results.alreadyBanned.length - 5} user khác*`
              : ""),
          inline: false,
        });
      }

      if (results.notFound.length > 0) {
        const notFoundList = results.notFound.slice(0, 5).join(", ");
        resultEmbed.addFields({
          name: `❓ Không tìm thấy (${results.notFound.length})`,
          value:
            notFoundList +
            (results.notFound.length > 5
              ? `, *... và ${results.notFound.length - 5} ID khác*`
              : ""),
          inline: false,
        });
      }

      resultEmbed.addFields({
        name: "📝 Chi tiết",
        value: `**Lý do:** ${reason}\n**Xóa tin nhắn:** ${deleteDays} ngày\n**Bởi:** ${interaction.user.tag}`,
        inline: false,
      });

      await interaction.editReply({ embeds: [resultEmbed] });

      // Gửi log vào channel nếu có ban thành công
      if (results.success.length > 0) {
        await sendModerationLog(client, guildId, {
          type: "massban",
          user: { tag: "Multiple Users", id: "multiple" },
          moderator: interaction.user,
          reason: reason,
          count: results.success.length,
          total: userIds.length,
        });
      }
    } catch (error) {
      console.error("Lỗi khi mass ban:", error);
      const errorEmbed = createErrorEmbed(
        "Lỗi mass ban!",
        "Đã xảy ra lỗi khi thực hiện mass ban. Vui lòng thử lại sau!"
      );
      await interaction.editReply({ embeds: [errorEmbed] });
    }
  },
};
