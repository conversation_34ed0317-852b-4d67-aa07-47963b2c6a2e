const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');
const auditLogDB = require('../../../utils/auditLogDatabase.js');

module.exports = {
    name: Events.GuildMemberAdd,
    async execute(member, client) {
        try {
            console.log(`👋 Member joined: ${member.user.tag} in ${member.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(member.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'USER_JOIN')) return;
            
            // Create event data
            const eventData = {
                eventType: 'USER_JOIN',
                user: member.user.tag,
                userId: member.user.id,
                action: 'Thành viên tham gia server',
                details: `**${member.user.tag}** đã tham gia server`,
                target: member.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(member.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Calculate account age
            const accountAge = Date.now() - member.user.createdTimestamp;
            const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
            
            // Create embed
            const embed = createInfoEmbed(
                '👋 Thành viên tham gia',
                `Vừa có một thành viên mới tham gia server`
            );
            
            embed.setColor(0x2ecc71); // Green for join
            
            embed.addFields([
                {
                    name: '> Thành viên mới',
                    value: `- ${member.user.tag} (${member.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${member.user.id}`,
                    inline: true
                },
                {
                    name: '> Tài khoản tạo',
                    value: `- <t:${Math.floor(member.user.createdTimestamp / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                },
                {
                    name: '> Tham gia lúc',
                    value: `- <t:${Math.floor(member.joinedTimestamp / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Tổng thành viên',
                    value: `- ${member.guild.memberCount} thành viên`,
                    inline: true
                },
                {
                    name: '> Thứ tự tham gia',
                    value: `- Thành viên thứ ${member.guild.memberCount}`,
                    inline: true
                }
            ]);
            
            // Add warning for new accounts
            if (accountAgeDays < 7) {
                embed.addFields({
                    name: '⚠️ Cảnh báo',
                    value: `- Tài khoản mới tạo (${accountAgeDays} ngày)`,
                    inline: false
                });
                embed.setColor(0xf39c12); // Orange for warning
            }
            
            // Set user avatar as thumbnail
            if (member.user.displayAvatarURL()) {
                embed.setThumbnail(member.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${member.guild.name} • Member Join`,
                iconURL: member.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Member join logged: ${member.user.tag}`);
            
        } catch (error) {
            console.error('Error in userJoin audit log:', error);
        }
    }
};
