const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle default thread slow mode changes for forum/text channels
            if (oldChannel.defaultThreadRateLimitPerUser === newChannel.defaultThreadRateLimitPerUser) return;
            
            console.log(`⏱️ Channel default thread slow mode updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_DEFAULT_THREAD_SLOW_MODE_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_DEFAULT_THREAD_SLOW_MODE_UPDATE',
                user: 'System',
                userId: null,
                action: 'Slow mode mặc định cho thread được cập nhật',
                details: `Slow mode mặc định cho thread của kênh **${newChannel.name}** đã được thay đổi từ ${oldChannel.defaultThreadRateLimitPerUser || 0}s thành ${newChannel.defaultThreadRateLimitPerUser || 0}s`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the default thread slow mode from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Slow mode mặc định cho thread được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for channel default thread slow mode update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '⏱️ Slow mode mặc định cho thread cập nhật',
                `Vừa có slow mode mặc định cho thread của kênh được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Format time display
            const formatTime = (seconds) => {
                if (seconds === 0) return 'Tắt';
                if (seconds < 60) return `${seconds} giây`;
                if (seconds < 3600) return `${Math.floor(seconds / 60)} phút ${seconds % 60} giây`;
                return `${Math.floor(seconds / 3600)} giờ ${Math.floor((seconds % 3600) / 60)} phút`;
            };
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Slow mode cũ',
                    value: `- ${formatTime(oldChannel.defaultThreadRateLimitPerUser || 0)}`,
                    inline: true
                },
                {
                    name: '> Slow mode mới',
                    value: `- ${formatTime(newChannel.defaultThreadRateLimitPerUser || 0)}`,
                    inline: true
                }
            ]);
            
            // Add impact explanation
            if ((newChannel.defaultThreadRateLimitPerUser || 0) > (oldChannel.defaultThreadRateLimitPerUser || 0)) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Threads mới sẽ có slow mode cao hơn',
                        '• Thành viên phải chờ lâu hơn giữa các tin nhắn',
                        '• Giảm spam trong threads',
                        '• Tăng chất lượng thảo luận'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0xe74c3c); // Red for restriction
            } else if ((newChannel.defaultThreadRateLimitPerUser || 0) < (oldChannel.defaultThreadRateLimitPerUser || 0)) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Threads mới sẽ có slow mode thấp hơn',
                        '• Thành viên có thể gửi tin nhắn nhanh hơn',
                        '• Tăng tốc độ thảo luận',
                        '• Có thể tăng nguy cơ spam'
                    ].join('\n'),
                    inline: false
                });
                if ((newChannel.defaultThreadRateLimitPerUser || 0) === 0) {
                    embed.setColor(0x2ecc71); // Green for no restriction
                }
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Default Thread Slow Mode Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel default thread slow mode update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelDefaultThreadSlowModeUpdate audit log:', error);
        }
    }
};
