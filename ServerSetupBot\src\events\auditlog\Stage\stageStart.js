const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.StageInstanceCreate,
    async execute(stageInstance, client) {
        try {
            console.log(`🎭 Stage started: ${stageInstance.topic} in ${stageInstance.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(stageInstance.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'STAGE_START')) return;
            
            // Create event data
            const eventData = {
                eventType: 'STAGE_START',
                user: 'System',
                userId: null,
                action: 'Stage được bắt đầu',
                details: `Stage **${stageInstance.topic || 'No topic'}** đã đượ<PERSON> bắt đầu`,
                target: stageInstance.topic || 'No topic',
                channel: stageInstance.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who started the stage from audit logs
            try {
                const auditLogs = await stageInstance.guild.fetchAuditLogs({
                    type: 83, // STAGE_INSTANCE_CREATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === stageInstance.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Stage được bắt đầu bởi ${auditEntry.executor.tag}`;
                }
            } catch (error) {
                console.log('Could not fetch audit logs for stage start');
            }
            
            // Add to database
            await client.db.addAuditLog(stageInstance.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🎭 Stage được bắt đầu',
                `Vừa có một stage được bắt đầu trong server`
            );
            
            embed.setColor(0x2ecc71); // Green for start
            
            embed.addFields([
                {
                    name: '> Chủ đề stage',
                    value: `- ${stageInstance.topic || 'Không có chủ đề'}`,
                    inline: false
                },
                {
                    name: '> ID stage',
                    value: `- ${stageInstance.id}`,
                    inline: true
                },
                {
                    name: '> Kênh stage',
                    value: `- ${stageInstance.channel || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người bắt đầu',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian bắt đầu',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add privacy level
            const privacyLevels = {
                1: '🌍 Public (Mọi người có thể tham gia)',
                2: '🏠 Guild Only (Chỉ thành viên server)'
            };
            
            embed.addFields({
                name: '> Mức độ riêng tư',
                value: `- ${privacyLevels[stageInstance.privacyLevel] || 'Unknown'}`,
                inline: true
            });
            
            // Add channel info
            if (stageInstance.channel) {
                embed.addFields([
                    {
                        name: '> ID kênh',
                        value: `- ${stageInstance.channel.id}`,
                        inline: true
                    },
                    {
                        name: '> Số người trong kênh',
                        value: `- ${stageInstance.channel.members?.size || 0} thành viên`,
                        inline: true
                    }
                ]);
                
                // Add channel permissions info
                if (stageInstance.channel.userLimit) {
                    embed.addFields({
                        name: '> Giới hạn người dùng',
                        value: `- ${stageInstance.channel.userLimit} người`,
                        inline: true
                    });
                }
            }
            
            // Add stage guidelines
            embed.addFields({
                name: '> 🎤 Cách tham gia stage',
                value: [
                    '• Vào kênh stage để nghe',
                    '• Nhấn "Request to Speak" để xin phát biểu',
                    '• Moderator sẽ cho phép bạn speak',
                    '• Tuân thủ chủ đề và quy tắc server'
                ].join('\n'),
                inline: false
            });
            
            // Add moderator guidelines
            embed.addFields({
                name: '> 👑 Quyền moderator stage',
                value: [
                    '• Mute/unmute speakers',
                    '• Move speakers to audience',
                    '• Invite audience to speak',
                    '• End stage khi cần thiết'
                ].join('\n'),
                inline: false
            });
            
            // Add stage link if possible
            if (stageInstance.channel) {
                embed.addFields({
                    name: '> Link tham gia',
                    value: `- [Nhấn để tham gia stage](https://discord.com/channels/${stageInstance.guild.id}/${stageInstance.channel.id})`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${stageInstance.guild.name} • Stage Start`,
                iconURL: stageInstance.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Stage start logged: ${stageInstance.topic}`);
            
        } catch (error) {
            console.error('Error in stageStart audit log:', error);
        }
    }
};
