# Hướng dẫn cài đặt Discord Economy Bot

## 📋 Yêu cầu trước khi cài đặt

### Ph<PERSON><PERSON> mềm cần thiết:
- **Node.js** (phiên bản 16.0.0 trở lên)
  - T<PERSON><PERSON> từ: https://nodejs.org/
  - Kiểm tra: `node --version`
- **NPM** (thường đi kèm với Node.js)
  - Ki<PERSON><PERSON> tra: `npm --version`
- **Git** (tùy chọn, để clone repository)
  - T<PERSON><PERSON> từ: https://git-scm.com/

### Tài khoản cần thiết:
- **Discord Account** để tạo bot
- **Discord Server** để test bot (hoặc quyền admin trong server)

## 🤖 Tạo Discord Bot

### Bước 1: Tạo Discord Application
1. Truy cập [Discord Developer Portal](https://discord.com/developers/applications)
2. <PERSON><PERSON><PERSON> nhập với tài khoản Discord
3. Nhấn **"New Application"**
4. Đặt tên cho application (ví dụ: "Economy Bot")
5. <PERSON><PERSON>ấn **"Create"**

### Bước 2: Tạo Bot
1. Trong application vừa tạo, chọn tab **"Bot"** ở sidebar
2. Nhấn **"Add Bot"**
3. Xác nhận bằng cách nhấn **"Yes, do it!"**

### Bước 3: Lấy Bot Token
1. Trong tab Bot, tìm phần **"Token"**
2. Nhấn **"Copy"** để sao chép token
3. **⚠️ LƯU Ý**: Giữ token này bí mật, không chia sẻ với ai!

### Bước 4: Lấy Application ID
1. Chuyển sang tab **"General Information"**
2. Sao chép **"Application ID"**

### Bước 5: Cấu hình Bot Settings
1. Quay lại tab **"Bot"**
2. Bật các Privileged Gateway Intents (nếu cần):
   - ✅ **Presence Intent**
   - ✅ **Server Members Intent**
   - ✅ **Message Content Intent**

## 💻 Cài đặt Bot Code

### Phương pháp 1: Download ZIP
1. Tải file ZIP của project
2. Giải nén vào thư mục mong muốn
3. Mở terminal/command prompt trong thư mục đó

### Phương pháp 2: Git Clone
```bash
git clone <repository-url>
cd EconomyBot
```

### Cài đặt Dependencies
```bash
npm install
```

## ⚙️ Cấu hình Bot

### Bước 1: Tạo file .env
```bash
# Windows
copy .env.example .env

# macOS/Linux
cp .env.example .env
```

### Bước 2: Chỉnh sửa file .env
Mở file `.env` bằng text editor và điền thông tin:

```env
# Discord Bot Configuration
DISCORD_TOKEN=YOUR_BOT_TOKEN_HERE
CLIENT_ID=YOUR_APPLICATION_ID_HERE

# Database Configuration
DATABASE_PATH=./data/economy.db

# Economy Configuration
DEFAULT_CURRENCY=💰
CURRENCY_NAME=Xu
CURRENCY_SYMBOL=💰

# Daily Rewards
DAILY_REWARD_MIN=100
DAILY_REWARD_MAX=500

# Starting Balance
STARTING_BALANCE=1000

# Shop Configuration
SHOP_TAX_RATE=0.05

# Bot Configuration
PREFIX=!
TIMEZONE=Asia/Ho_Chi_Minh
```

**Thay thế:**
- `YOUR_BOT_TOKEN_HERE` → Token bot từ bước 3
- `YOUR_APPLICATION_ID_HERE` → Application ID từ bước 4

## 🔗 Mời Bot vào Server

### Bước 1: Tạo Invite Link
1. Trong Discord Developer Portal, chọn tab **"OAuth2"** > **"URL Generator"**
2. Chọn **Scopes**:
   - ✅ `bot`
   - ✅ `applications.commands`
3. Chọn **Bot Permissions**:
   - ✅ `Send Messages`
   - ✅ `Use Slash Commands`
   - ✅ `Embed Links`
   - ✅ `Read Message History`
   - ✅ `Add Reactions`

### Bước 2: Mời Bot
1. Sao chép URL được tạo ở cuối trang
2. Mở URL trong trình duyệt
3. Chọn server muốn thêm bot
4. Nhấn **"Authorize"**

## 🚀 Khởi động Bot

### Chế độ Production
```bash
npm start
```

### Chế độ Development (Auto-restart)
```bash
npm run dev
```

### Kiểm tra Bot hoạt động
1. Bot sẽ hiển thị status "Online" trong Discord
2. Thử lệnh `/help` trong server
3. Kiểm tra console có thông báo thành công

## ✅ Kiểm tra cài đặt

### Test các lệnh cơ bản:
```
/help - Hiển thị hướng dẫn
/balance - Kiểm tra số dư (sẽ tạo tài khoản mới)
/daily - Nhận phần thưởng hàng ngày
/shop list - Xem cửa hàng
```

### Kiểm tra database:
- File `data/economy.db` được tạo tự động
- Thư mục `data/` xuất hiện sau lần chạy đầu tiên

## 🔧 Troubleshooting

### Bot không online
- ✅ Kiểm tra token đúng trong file .env
- ✅ Kiểm tra internet connection
- ✅ Xem console có lỗi gì không

### Slash commands không hoạt động
- ✅ Đảm bảo bot có quyền `Use Slash Commands`
- ✅ Kiểm tra CLIENT_ID đúng
- ✅ Restart bot để deploy commands
- ✅ Đợi vài phút để Discord sync commands

### Lỗi database
- ✅ Kiểm tra quyền ghi file trong thư mục project
- ✅ Tạo thư mục `data/` thủ công nếu cần
- ✅ Xóa file `data/economy.db` để reset database

### Bot không phản hồi
- ✅ Kiểm tra bot có quyền `Send Messages` và `Embed Links`
- ✅ Kiểm tra role của bot trong server hierarchy
- ✅ Thử lệnh trong channel khác

### Lỗi permissions
```
Missing Permissions: Send Messages
```
→ Cấp quyền `Send Messages` cho bot trong server/channel

```
Missing Permissions: Use Slash Commands
```
→ Cấp quyền `Use Slash Commands` cho bot

## 📝 Cấu hình nâng cao

### Thay đổi múi giờ
Sửa `TIMEZONE` trong file .env:
```env
TIMEZONE=Asia/Ho_Chi_Minh  # Việt Nam
TIMEZONE=America/New_York  # New York
TIMEZONE=Europe/London     # London
```

### Tùy chỉnh tiền tệ
```env
CURRENCY_NAME=Đồng
CURRENCY_SYMBOL=₫
DEFAULT_CURRENCY=₫
```

### Tùy chỉnh phần thưởng
```env
STARTING_BALANCE=5000
DAILY_REWARD_MIN=500
DAILY_REWARD_MAX=1000
```

## 🔄 Cập nhật Bot

### Cập nhật code:
```bash
git pull origin main
npm install
```

### Backup database:
```bash
# Sao lưu trước khi cập nhật
cp data/economy.db data/economy.db.backup
```

## 📞 Hỗ trợ

### Nếu gặp vấn đề:
1. Kiểm tra phần Troubleshooting ở trên
2. Xem log trong console để tìm lỗi cụ thể
3. Tạo issue trên GitHub với thông tin chi tiết
4. Liên hệ developer

### Thông tin cần cung cấp khi báo lỗi:
- Hệ điều hành (Windows/macOS/Linux)
- Phiên bản Node.js (`node --version`)
- Log lỗi từ console
- Các bước đã thực hiện

---

**Chúc bạn cài đặt thành công! 🎉**
