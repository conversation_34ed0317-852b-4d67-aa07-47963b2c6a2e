const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const Item = require('../models/Item');
const User = require('../models/User');
const Inventory = require('../models/Inventory');
const { createSuccessEmbed, createErrorEmbed, formatCurrency } = require('../utils/embedBuilder');
const config = require('../config/config');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('shop')
        .setNameLocalizations({
            'vi': 'cuahang'
        })
        .setDescription('Quản lý cửa hàng')
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setNameLocalizations({
                    'vi': 'danhsach'
                })
                .setDescription('Xem danh sách vật phẩm trong cửa hàng')
                .addStringOption(option =>
                    option.setName('category')
                        .setNameLocalizations({
                            'vi': 'danhmuc'
                        })
                        .setDescription('Danh mục vật phẩm')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('buy')
                .setNameLocalizations({
                    'vi': 'mua'
                })
                .setDescription('Mua vật phẩm từ cửa hàng')
                .addStringOption(option =>
                    option.setName('item')
                        .setNameLocalizations({
                            'vi': 'vatpham'
                        })
                        .setDescription('Tên vật phẩm cần mua')
                        .setRequired(true)
                )
                .addIntegerOption(option =>
                    option.setName('quantity')
                        .setNameLocalizations({
                            'vi': 'soluong'
                        })
                        .setDescription('Số lượng cần mua')
                        .setRequired(false)
                        .setMinValue(1)
                        .setMaxValue(100)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('sell')
                .setNameLocalizations({
                    'vi': 'ban'
                })
                .setDescription('Bán vật phẩm từ kho đồ')
                .addStringOption(option =>
                    option.setName('item')
                        .setNameLocalizations({
                            'vi': 'vatpham'
                        })
                        .setDescription('Tên vật phẩm cần bán')
                        .setRequired(true)
                )
                .addIntegerOption(option =>
                    option.setName('quantity')
                        .setNameLocalizations({
                            'vi': 'soluong'
                        })
                        .setDescription('Số lượng cần bán')
                        .setRequired(false)
                        .setMinValue(1)
                        .setMaxValue(100)
                )
        ),
    
    cooldown: 5,

    async execute(interaction) {
        try {
            const subcommand = interaction.options.getSubcommand();

            switch (subcommand) {
                case 'list':
                    await handleShopList(interaction);
                    break;
                case 'buy':
                    await handleBuyItem(interaction);
                    break;
                case 'sell':
                    await handleSellItem(interaction);
                    break;
            }
            
        } catch (error) {
            console.error('Lỗi lệnh shop:', error);
            
            const errorEmbed = createErrorEmbed(
                'Lỗi cửa hàng',
                'Không thể thực hiện giao dịch. Vui lòng thử lại sau.'
            );
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};

async function handleShopList(interaction) {
    const category = interaction.options.getString('category');
    
    try {
        let items;
        if (category) {
            items = await Item.getByCategory(category);
        } else {
            items = await Item.getAll();
        }

        if (items.length === 0) {
            const errorEmbed = createErrorEmbed(
                'Cửa hàng trống',
                category ? `Không có vật phẩm nào trong danh mục "${category}"` : 'Cửa hàng hiện tại không có vật phẩm nào'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Nhóm items theo category
        const itemsByCategory = {};
        items.forEach(item => {
            if (!itemsByCategory[item.category]) {
                itemsByCategory[item.category] = [];
            }
            itemsByCategory[item.category].push(item);
        });

        const embed = new EmbedBuilder()
            .setColor(config.colors.economy)
            .setTitle(`${config.emojis.shop} Cửa hàng`)
            .setDescription(category ? `Danh mục: **${category}**` : 'Tất cả vật phẩm có sẵn')
            .setTimestamp()
            .setFooter({ text: 'Sử dụng /shop buy <tên vật phẩm> để mua' });

        // Thêm fields cho mỗi category
        for (const [cat, categoryItems] of Object.entries(itemsByCategory)) {
            const itemList = categoryItems.map(item => 
                `${item.emoji} **${item.name}** - ${formatCurrency(item.price)}`
            ).join('\n');

            embed.addFields({
                name: `📂 ${cat.charAt(0).toUpperCase() + cat.slice(1)}`,
                value: itemList.length > 1024 ? itemList.substring(0, 1021) + '...' : itemList,
                inline: false
            });
        }

        await interaction.reply({ embeds: [embed] });
        
    } catch (error) {
        console.error('Lỗi hiển thị shop:', error);
        throw error;
    }
}

async function handleBuyItem(interaction) {
    const itemName = interaction.options.getString('item');
    const quantity = interaction.options.getInteger('quantity') || 1;
    
    try {
        // Tìm item
        const item = await Item.findByName(itemName);
        if (!item) {
            const errorEmbed = createErrorEmbed(
                'Không tìm thấy vật phẩm',
                `Không có vật phẩm nào tên "${itemName}" trong cửa hàng`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        if (!item.buyable) {
            const errorEmbed = createErrorEmbed(
                'Không thể mua',
                'Vật phẩm này không thể mua'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Tính tổng giá
        const totalPrice = item.price * quantity;
        
        // Lấy thông tin user
        const userData = await User.findOrCreate(interaction.user);
        
        if (userData.balance < totalPrice) {
            const errorEmbed = createErrorEmbed(
                'Số dư không đủ',
                `Bạn cần **${formatCurrency(totalPrice)}** nhưng chỉ có **${formatCurrency(userData.balance)}**`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Thực hiện giao dịch
        await userData.updateBalance(-totalPrice, 'shop_buy');
        await Inventory.addItem(interaction.user.id, item.id, quantity);

        const successEmbed = createSuccessEmbed(
            'Mua hàng thành công!',
            `Bạn đã mua **${quantity}x ${item.emoji} ${item.name}** với giá **${formatCurrency(totalPrice)}**\n\n` +
            `${config.emojis.money} **Số dư còn lại:** ${formatCurrency(userData.balance - totalPrice)}`
        );

        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi mua item:', error);
        throw error;
    }
}

async function handleSellItem(interaction) {
    const itemName = interaction.options.getString('item');
    const quantity = interaction.options.getInteger('quantity') || 1;
    
    try {
        // Tìm item
        const item = await Item.findByName(itemName);
        if (!item) {
            const errorEmbed = createErrorEmbed(
                'Không tìm thấy vật phẩm',
                `Không có vật phẩm nào tên "${itemName}"`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        if (!item.sellable) {
            const errorEmbed = createErrorEmbed(
                'Không thể bán',
                'Vật phẩm này không thể bán'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Kiểm tra inventory
        const hasQuantity = await Inventory.hasItem(interaction.user.id, item.id);
        if (hasQuantity < quantity) {
            const errorEmbed = createErrorEmbed(
                'Không đủ vật phẩm',
                `Bạn chỉ có **${hasQuantity}x ${item.name}** nhưng muốn bán **${quantity}x**`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Tính tổng giá bán
        const totalSellPrice = item.sellPrice * quantity;
        
        // Lấy thông tin user
        const userData = await User.findOrCreate(interaction.user);

        // Thực hiện giao dịch
        await userData.updateBalance(totalSellPrice, 'shop_sell');
        await Inventory.removeItem(interaction.user.id, item.id, quantity);

        const successEmbed = createSuccessEmbed(
            'Bán hàng thành công!',
            `Bạn đã bán **${quantity}x ${item.emoji} ${item.name}** và nhận được **${formatCurrency(totalSellPrice)}**\n\n` +
            `${config.emojis.money} **Số dư hiện tại:** ${formatCurrency(userData.balance + totalSellPrice)}`
        );

        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi bán item:', error);
        throw error;
    }
}
