const { Permissions<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    name: 'nuke',
    description: 'Deletes and recreates the current channel to clear messages.',

    run: async (message, args, client) => {
        // ✅ **Check Permissions**
        if (!message.member.permissions.has(PermissionsBitField.Flags.ManageChannels)) {
            return message.reply('❌ You need `Manage Channels` permission to use this command.');
        }
        if (!message.guild.members.me.permissions.has(PermissionsBitField.Flags.ManageChannels)) {
            return message.reply('❌ I need `Manage Channels` permission to nuke a channel.');
        }

        let channel = message.channel;

        // ⚠️ **Warning Embed**
        const embed = new EmbedBuilder()
            .setColor('#FF0000')
            .setTitle('⚠️ Nuke Channel')
            .setDescription(`Are you sure you want to nuke **${channel.name}**? This will **delete all messages** in the channel.`)
            .setFooter({ text: 'Press the Nuke button to confirm.' });

        // 🔥 **Nuke Button**
        const row = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId('confirm_nuke')
                .setLabel('Nuke')
                .setStyle(ButtonStyle.Danger)
                .setEmoji('💥') // 💥 Explosion emoji
        );

        const msg = await message.channel.send({ embeds: [embed], components: [row] });

        // 🕐 **Collector for Button Response**
        const filter = (interaction) => interaction.user.id === message.author.id;
        const collector = msg.createMessageComponentCollector({ filter, time: 15000 });

        collector.on('collect', async (interaction) => {
            if (interaction.customId === 'confirm_nuke') {
                await interaction.deferUpdate();

                // ✅ **Nuking Message**
                embed.setDescription(`💥 **${channel.name}** is being nuked...`);
                await msg.edit({ embeds: [embed], components: [] });

                // 📌 **Channel Data**
                let newChannel = await channel.clone();
                await channel.delete().catch(() => {
                    message.channel.send('❌ Failed to delete the channel.');
                });

                // ✅ **Final Nuke Message**
                await newChannel.send({
                    embeds: [
                        new EmbedBuilder()
                            .setColor('#00FF00')
                            .setTitle('🔥 Channel Nuked!')
                            .setDescription(`This channel has been nuked by **${message.author.tag}**.`)
                            .setFooter({ text: 'All messages have been cleared.' })
                    ]
                });
            }
        });

        collector.on('end', () => {
            msg.edit({ components: [] }).catch(() => {});
        });
    }
};