const Command = require("../../structures/Command");
const Discord = require("discord.js");
const fetch = require("node-fetch");

module.exports = class InviteBot extends Command {
  constructor(client) {
    super(client, {
      name: "invitebot",
      description: client.cmdConfig.invitebot.description,
      usage: client.cmdConfig.invitebot.usage,
      permissions: client.cmdConfig.invitebot.permissions,
      aliases: client.cmdConfig.invitebot.aliases,
      category: "setups",
      listed: client.cmdConfig.invitebot.enabled,
      slash: true,
      options: [{
        name: "name", 
        type: Discord.ApplicationCommandOptionType.String, 
        description: "Name of <PERSON><PERSON>", 
        required: true
      }]
    });
  }

  async run(message, args) {
    let name = args.join(" ");
    if(!args[0]) return message.channel.send({ embeds: [this.client.utils.usage(this.client, this.client.cmdConfig.invitebot.usage)]});

    let botInfo = {
      username: "",
      botInvite: "",
      avatarURL: "",
      shortDescription: "",
      guildCount: "N/A",
      exist: true
    };

    let regex = / /g;

    fetch(`https://discord.bots.gg/api/v1/bots?q=${name.replaceAll(regex, "%20")}`).then(async (res) => {
      let data = await res.json();

      if (data.bots.length < 1) {
        let findBot = findExtra(this.client, name);
        if (findBot) {
          botInfo.username = findBot.username;
          botInfo[username] = findBot.username;
          botInfo.botInvite = findBot.botInvite;
          botInfo.avatarURL = findBot.avatarURL;
          botInfo.shortDescription = findBot.shortDescription;
          botInfo.guildCount = findBot.guildCount > 0 ? findBot.guildCount : "N/A";
        } else {
          botInfo.exist = false;
        };
      } else {
        let findBot = data.bots.find((x) => `${x.username}`.toLowerCase().includes(name.toLowerCase()));

        if (!findBot) {
          let findData = findExtra(this.client, name);
          if (findData) {
            botInfo.username = findData.username;
            botInfo.botInvite = findData.botInvite;
            botInfo.avatarURL = findData.avatarURL;
            botInfo.shortDescription = findData.shortDescription;
            botInfo.guildCount = findData.guildCount > 0 ? findData.guildCount : "N/A";
          } else {
            botInfo.exist = false;
          };
        } else {
          botInfo.username = findBot.username;
          botInfo.botInvite = findBot.botInvite;
          botInfo.avatarURL = findBot.avatarURL;
          botInfo.shortDescription = findBot.shortDescription;
          botInfo.guildCount = findBot.guildCount > 0 ? findBot.guildCount : "N/A";
        }
      }

      let embed = new Discord.EmbedBuilder()
        .setColor(this.client.embeds.setup.inviteBot.color);
      
      if(this.client.embeds.setup.inviteBot.title) embed.setTitle(this.client.embeds.setup.inviteBot.title);
      
      if(this.client.embeds.setup.inviteBot.description) embed.setDescription(this.client.embeds.setup.inviteBot.description.replace("<name>", botInfo.username)
        .replace("<description>", botInfo.shortDescription)
        .replace("<guilds>", botInfo.guildCount));
      
      let field = this.client.embeds.setup.inviteBot.fields;
      for(let i = 0; i < this.client.embeds.setup.inviteBot.fields.length; i++) {
        embed.addFields([{ name: field[i].title, value: field[i].description.replace("<name>", botInfo.username)
          .replace("<description>", botInfo.shortDescription)
          .replace("<guilds>", botInfo.guildCount) }]);
      }
  
      if(this.client.embeds.setup.inviteBot.footer == true) embed.setFooter({ text: message.author.username, iconURL: message.author.displayAvatarURL() }).setTimestamp();
      if(this.client.embeds.setup.inviteBot.thumbnail == true) embed.setThumbnail(message.guild.iconURL());
  
      if (botInfo.exist == false) return message.channel.send({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.general.invalid_bot, this.client.embeds.error_color)]});
      
      const row = new Discord.ActionRowBuilder()
        .addComponents(
          new Discord.ButtonBuilder()
          .setURL(botInfo.botInvite)
          .setLabel(this.client.embeds.setup.inviteBot.button)
          .setStyle(Discord.ButtonStyle.Link),
        );
  
      message.channel.send({ embeds: [embed], components: [row] })
    });
  }
  async slashRun(interaction, args) {
    let name = interaction.options.getString("name");
    let botInfo = {
      username: "",
      botInvite: "",
      avatarURL: "",
      shortDescription: "",
      guildCount: "N/A",
      exist: true
    };

    let regex = / /g;

    fetch(`https://discord.bots.gg/api/v1/bots?q=${name.replaceAll(regex, "%20")}`).then(async (res) => {
      let data = await res.json();

      if (data.bots.length < 1) {
        let findBot = findExtra(this.client, name);
        if (findBot) {
          botInfo.username = findBot.username;
          botInfo[username] = findBot.username;
          botInfo.botInvite = findBot.botInvite;
          botInfo.avatarURL = findBot.avatarURL;
          botInfo.shortDescription = findBot.shortDescription;
          botInfo.guildCount = findBot.guildCount > 0 ? findBot.guildCount : "N/A";
        } else {
          botInfo.exist = false;
        };
      } else {
        let findBot = data.bots.find((x) => `${x.username}`.toLowerCase().includes(name.toLowerCase()));

        if (!findBot) {
          let findData = findExtra(this.client, name);
          if (findData) {
            botInfo.username = findData.username;
            botInfo.botInvite = findData.botInvite;
            botInfo.avatarURL = findData.avatarURL;
            botInfo.shortDescription = findData.shortDescription;
            botInfo.guildCount = findData.guildCount > 0 ? findData.guildCount : "N/A";
          } else {
            botInfo.exist = false;
          };
        } else {
          botInfo.username = findBot.username;
          botInfo.botInvite = findBot.botInvite;
          botInfo.avatarURL = findBot.avatarURL;
          botInfo.shortDescription = findBot.shortDescription;
          botInfo.guildCount = findBot.guildCount > 0 ? findBot.guildCount : "N/A";
        }
      }

      let embed = new Discord.EmbedBuilder()
        .setColor(this.client.embeds.setup.inviteBot.color);

      if (this.client.embeds.setup.inviteBot.title) embed.setTitle(this.client.embeds.setup.inviteBot.title);

      if (this.client.embeds.setup.inviteBot.description) embed.setDescription(this.client.embeds.setup.inviteBot.description.replace("<name>", botInfo.username)
        .replace("<description>", botInfo.shortDescription)
        .replace("<guilds>", botInfo.guildCount));

      let field = this.client.embeds.setup.inviteBot.fields;
      for (let i = 0; i < this.client.embeds.setup.inviteBot.fields.length; i++) {
        embed.addFields([{ name: field[i].title, value: field[i].description.replace("<name>", botInfo.username)
          .replace("<description>", botInfo.shortDescription)
          .replace("<guilds>", botInfo.guildCount) }]);
      }

      if (this.client.embeds.setup.inviteBot.footer == true) embed.setFooter({ text: interaction.user.username, iconURL: interaction.user.displayAvatarURL() }).setTimestamp();
      if (this.client.embeds.setup.inviteBot.thumbnail == true) embed.setThumbnail(interaction.guild.iconURL());

      if (botInfo.exist == false) return interaction.reply({ embeds: [this.client.embedBuilder(this.client, this.client.embeds.title, this.client.language.general.invalid_bot, this.client.embeds.error_color)], ephemeral: this.client.cmdConfig.templates.ephemeral });

      const row = new Discord.ActionRowBuilder()
        .addComponents(
          new Discord.ButtonBuilder()
          .setURL(botInfo.botInvite)
          .setLabel(this.client.embeds.setup.inviteBot.button)
          .setStyle(Discord.ButtonStyle.Link),
        );

      interaction.reply({ embeds: [embed], components: [row], ephemeral: this.client.cmdConfig.templates.ephemeral })
    });
  }
};

const findExtra = (client, name) => {
  return client.bots.find((x) => `${x.username}`.toLowerCase().includes(name.toLowerCase()));
}
