const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.MessageBulkDelete,
    async execute(messages, channel, client) {
        try {
            console.log(`🗑️ Bulk messages deleted: ${messages.size} messages in ${channel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(channel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'MESSAGE_BULK_DELETE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'MESSAGE_BULK_DELETE',
                user: 'System',
                userId: null,
                action: 'Xóa hàng loạt tin nhắn',
                details: `**${messages.size}** tin nhắn đã được xóa hàng loạt trong ${channel}`,
                target: `${messages.size} messages`,
                channel: channel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the messages from audit logs
            try {
                const auditLogs = await channel.guild.fetchAuditLogs({
                    type: 73, // MESSAGE_BULK_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.extra?.channel?.id === channel.id) {
                    const timeDiff = Date.now() - auditEntry.createdTimestamp;
                    if (timeDiff < 5000) { // Within 5 seconds
                        eventData.user = auditEntry.executor.tag;
                        eventData.userId = auditEntry.executor.id;
                        eventData.action = `Xóa hàng loạt tin nhắn bởi ${auditEntry.executor.tag}`;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for bulk delete');
            }
            
            // Add to database
            await client.db.addAuditLog(channel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Xóa hàng loạt tin nhắn',
                `Vừa có tin nhắn được xóa hàng loạt trong server`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            embed.addFields([
                {
                    name: '> Số lượng tin nhắn',
                    value: `- ${messages.size} tin nhắn`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${channel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${channel.id}`,
                    inline: true
                },
                {
                    name: '> Người thực hiện',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Analyze deleted messages
            const messageArray = Array.from(messages.values());
            const authors = new Set();
            const oldestMessage = messageArray.reduce((oldest, msg) => 
                msg.createdTimestamp < oldest.createdTimestamp ? msg : oldest
            );
            const newestMessage = messageArray.reduce((newest, msg) => 
                msg.createdTimestamp > newest.createdTimestamp ? msg : newest
            );
            
            messageArray.forEach(msg => {
                if (msg.author) authors.add(msg.author.tag);
            });
            
            embed.addFields([
                {
                    name: '> Số tác giả khác nhau',
                    value: `- ${authors.size} người`,
                    inline: true
                },
                {
                    name: '> Tin nhắn cũ nhất',
                    value: `- <t:${Math.floor(oldestMessage.createdTimestamp / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Tin nhắn mới nhất',
                    value: `- <t:${Math.floor(newestMessage.createdTimestamp / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add sample of deleted content (first few messages)
            const sampleMessages = messageArray.slice(0, 3).map(msg => {
                const content = msg.content || '*Không có nội dung văn bản*';
                const truncated = content.length > 100 ? content.substring(0, 100) + '...' : content;
                return `**${msg.author?.tag || 'Unknown'}**: ${truncated}`;
            }).join('\n');
            
            if (sampleMessages) {
                embed.addFields({
                    name: '> Mẫu nội dung đã xóa',
                    value: `\`\`\`${sampleMessages}\`\`\``,
                    inline: false
                });
            }
            
            // Warning for suspicious bulk delete
            if (messages.size > 50) {
                embed.addFields({
                    name: '⚠️ Cảnh báo',
                    value: `- Xóa hàng loạt ${messages.size} tin nhắn có thể là hành vi bất thường`,
                    inline: false
                });
                embed.setColor(0xf39c12); // Orange for warning
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${channel.guild.name} • Message Bulk Delete`,
                iconURL: channel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Message bulk delete logged: ${messages.size} messages`);
            
            // Smart alert for bulk delete
            if (config.smartAlerts && messages.size > (config.alertThresholds?.messageBulkDelete || 20)) {
                try {
                    const owner = await channel.guild.fetchOwner();
                    const alertEmbed = createInfoEmbed(
                        '🚨 Smart Alert: Bulk Delete',
                        `Phát hiện xóa hàng loạt ${messages.size} tin nhắn trong **${channel.guild.name}**`
                    );
                    
                    alertEmbed.setColor(0xe74c3c);
                    alertEmbed.addFields([
                        { name: '🎯 Loại', value: 'MESSAGE_BULK_DELETE', inline: true },
                        { name: '📊 Mức độ', value: messages.size > 100 ? 'HIGH' : 'MEDIUM', inline: true },
                        { name: '👤 Thực hiện bởi', value: eventData.user, inline: true },
                        { name: '📍 Kênh', value: channel.name, inline: true },
                        { name: '📈 Số lượng', value: `${messages.size} tin nhắn`, inline: true }
                    ]);
                    
                    await owner.send({ embeds: [alertEmbed] });
                } catch (dmError) {
                    console.log('Could not send bulk delete alert to owner');
                }
            }
            
        } catch (error) {
            console.error('Error in messagesBulkDelete audit log:', error);
        }
    }
};
