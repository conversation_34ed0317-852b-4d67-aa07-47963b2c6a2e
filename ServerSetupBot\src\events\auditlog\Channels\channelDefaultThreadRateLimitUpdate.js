const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle default thread rate limit changes for forum/text channels
            if (oldChannel.defaultThreadRateLimitPerUser === newChannel.defaultThreadRateLimitPerUser) return;
            
            console.log(`⏱️ Channel default thread rate limit updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_DEFAULT_THREAD_RATE_LIMIT_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_DEFAULT_THREAD_RATE_LIMIT_UPDATE',
                user: 'System',
                userId: null,
                action: 'Rate limit thread mặc định của kênh được cập nhật',
                details: `Rate limit thread mặc định của kênh **${newChannel.name}** đã được thay đổi từ ${oldChannel.defaultThreadRateLimitPerUser}s thành ${newChannel.defaultThreadRateLimitPerUser}s`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the default thread rate limit from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Rate limit thread mặc định được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for channel default thread rate limit update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '⏱️ Rate limit thread mặc định cập nhật',
                `Vừa có rate limit thread mặc định của kênh được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Format time display
            const formatTime = (seconds) => {
                if (seconds === 0) return 'Tắt';
                if (seconds < 60) return `${seconds} giây`;
                if (seconds < 3600) return `${Math.floor(seconds / 60)} phút ${seconds % 60} giây`;
                return `${Math.floor(seconds / 3600)} giờ ${Math.floor((seconds % 3600) / 60)} phút`;
            };
            
            // Get channel type names
            const channelTypes = {
                0: 'Kênh văn bản',
                2: 'Kênh thoại',
                4: 'Danh mục',
                5: 'Kênh tin tức',
                10: 'Diễn đàn tin tức',
                11: 'Diễn đàn công khai',
                12: 'Diễn đàn riêng tư',
                13: 'Kênh sân khấu',
                15: 'Forum',
                16: 'Media'
            };
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Loại kênh',
                    value: `- ${channelTypes[newChannel.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Rate limit cũ',
                    value: `- ${formatTime(oldChannel.defaultThreadRateLimitPerUser || 0)}`,
                    inline: true
                },
                {
                    name: '> Rate limit mới',
                    value: `- ${formatTime(newChannel.defaultThreadRateLimitPerUser || 0)}`,
                    inline: true
                }
            ]);
            
            // Add parent category info if available
            if (newChannel.parent) {
                embed.addFields({
                    name: '> Danh mục',
                    value: `- ${newChannel.parent.name}`,
                    inline: true
                });
            }
            
            // Add impact explanation
            if ((newChannel.defaultThreadRateLimitPerUser || 0) > (oldChannel.defaultThreadRateLimitPerUser || 0)) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Threads mới sẽ có rate limit cao hơn',
                        '• Thành viên phải chờ lâu hơn giữa các tin nhắn',
                        '• Giảm spam trong threads',
                        '• Tăng chất lượng thảo luận'
                    ].join('\n'),
                    inline: false
                });
                if ((newChannel.defaultThreadRateLimitPerUser || 0) >= 60) {
                    embed.setColor(0xe74c3c); // Red for heavy restriction
                }
            } else if ((newChannel.defaultThreadRateLimitPerUser || 0) < (oldChannel.defaultThreadRateLimitPerUser || 0)) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Threads mới sẽ có rate limit thấp hơn',
                        '• Thành viên có thể gửi tin nhắn nhanh hơn',
                        '• Tăng tốc độ thảo luận',
                        '• Có thể tăng nguy cơ spam'
                    ].join('\n'),
                    inline: false
                });
                if ((newChannel.defaultThreadRateLimitPerUser || 0) === 0) {
                    embed.setColor(0x2ecc71); // Green for no restriction
                }
            }
            
            // Add rate limit recommendations
            embed.addFields({
                name: '> 💡 Khuyến nghị rate limit cho threads',
                value: [
                    '• 0s: Không giới hạn - cho thảo luận tự do',
                    '• 3-5s: Nhẹ - giảm spam nhẹ',
                    '• 10-30s: Trung bình - cho thảo luận nghiêm túc',
                    '• 1-5 phút: Nặng - cho Q&A hoặc support',
                    '• 6 giờ: Rất nặng - chỉ cho announcements'
                ].join('\n'),
                inline: false
            });
            
            // Add note about existing threads
            embed.addFields({
                name: '> 📝 Lưu ý quan trọng',
                value: [
                    '• Chỉ áp dụng cho threads mới được tạo',
                    '• Threads hiện tại giữ nguyên rate limit',
                    '• Có thể thay đổi riêng cho từng thread',
                    '• Áp dụng cho cả public và private threads'
                ].join('\n'),
                inline: false
            });
            
            // Add channel specific info
            if (newChannel.type === 15) { // Forum channel
                embed.addFields({
                    name: '> Đặc biệt cho Forum',
                    value: `- Tất cả posts mới trong forum sẽ có rate limit ${formatTime(newChannel.defaultThreadRateLimitPerUser || 0)}`,
                    inline: false
                });
            } else if (newChannel.type === 0) { // Text channel
                embed.addFields({
                    name: '> Đặc biệt cho Text Channel',
                    value: `- Tất cả threads mới trong kênh sẽ có rate limit ${formatTime(newChannel.defaultThreadRateLimitPerUser || 0)}`,
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Default Thread Rate Limit Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel default thread rate limit update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelDefaultThreadRateLimitUpdate audit log:', error);
        }
    }
};
