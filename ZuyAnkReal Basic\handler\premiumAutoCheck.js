const fs = require("fs");
const path = require("path");

const premiumFile = path.join(__dirname, "../../data/premium.json");

// Read premium data
function readPremiumData() {
    return JSON.parse(fs.readFileSync(premiumFile));
}

// Write premium data
function writePremiumData(data) {
    fs.writeFileSync(premiumFile, JSON.stringify(data, null, 2));
}

// Auto-remove expired premium users
function checkPremiumExpiry() {
    let data = readPremiumData();
    let now = new Date();

    for (let userId in data.users) {
        if (new Date(data.users[userId].expiry) < now) {
            delete data.users[userId];
        }
    }

    writePremiumData(data);
}

// Run every 12 hours
setInterval(checkPremiumExpiry, 12 * 60 * 60 * 1000);