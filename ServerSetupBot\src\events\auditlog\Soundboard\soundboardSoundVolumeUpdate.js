const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'soundboardSoundUpdate', // Custom event name
    async execute(oldSound, newSound, client) {
        try {
            // Only handle volume changes
            if (oldSound.volume === newSound.volume) return;
            
            console.log(`🔊 Soundboard sound volume updated: ${newSound.name} in ${newSound.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newSound.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'SOUNDBOARD_SOUND_VOLUME_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'SOUNDBOARD_SOUND_VOLUME_UPDATE',
                user: 'System',
                userId: null,
                action: 'Âm lượng soundboard được cập nhật',
                details: `Âm lượng của âm thanh soundboard **${newSound.name}** đã được thay đổi từ ${Math.round(oldSound.volume * 100)}% thành ${Math.round(newSound.volume * 100)}%`,
                target: newSound.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the sound volume from audit logs
            try {
                const auditLogs = await newSound.guild.fetchAuditLogs({
                    type: 131, // SOUNDBOARD_SOUND_UPDATE (hypothetical)
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newSound.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Âm lượng soundboard được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for soundboard volume update');
            }
            
            // Add to database
            await client.db.addAuditLog(newSound.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔊 Âm lượng soundboard được cập nhật',
                `Vừa có âm lượng soundboard được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Determine volume level and color
            const oldVolumePercent = Math.round(oldSound.volume * 100);
            const newVolumePercent = Math.round(newSound.volume * 100);
            
            const getVolumeLevel = (volume) => {
                if (volume >= 80) return '🔊 Rất cao';
                if (volume >= 60) return '🔉 Cao';
                if (volume >= 40) return '🔉 Trung bình';
                if (volume >= 20) return '🔈 Thấp';
                return '🔇 Rất thấp';
            };
            
            embed.addFields([
                {
                    name: '> Âm thanh',
                    value: `- ${newSound.name}`,
                    inline: true
                },
                {
                    name: '> ID âm thanh',
                    value: `- ${newSound.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Âm lượng cũ',
                    value: `- ${oldVolumePercent}% (${getVolumeLevel(oldVolumePercent)})`,
                    inline: true
                },
                {
                    name: '> Âm lượng mới',
                    value: `- ${newVolumePercent}% (${getVolumeLevel(newVolumePercent)})`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add sound properties
            if (newSound.emoji) {
                embed.addFields({
                    name: '> Emoji',
                    value: `- ${newSound.emoji}`,
                    inline: true
                });
            }
            
            // Add creator info if available
            if (newSound.user) {
                embed.addFields({
                    name: '> Người tạo',
                    value: `- ${newSound.user.tag}`,
                    inline: true
                });
            }
            
            // Add impact information
            if (newSound.volume > oldSound.volume) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Âm thanh sẽ to hơn khi phát',
                        '• Có thể gây giật mình cho người nghe',
                        '• Cần cân nhắc về âm lượng phù hợp',
                        '• Tăng sự chú ý khi sử dụng'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0xe67e22); // Orange-red for louder
            } else if (newSound.volume < oldSound.volume) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Âm thanh sẽ nhỏ hơn khi phát',
                        '• Dễ chịu hơn cho người nghe',
                        '• Có thể khó nghe trong môi trường ồn',
                        '• Phù hợp cho không gian yên tĩnh'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for quieter
            }
            
            // Add volume recommendations
            embed.addFields({
                name: '> 💡 Khuyến nghị âm lượng',
                value: [
                    '• 20-40%: Phù hợp cho môi trường yên tĩnh',
                    '• 40-60%: Cân bằng tốt cho hầu hết trường hợp',
                    '• 60-80%: Phù hợp cho không gian ồn ào',
                    '• 80-100%: Chỉ dùng khi cần thiết'
                ].join('\n'),
                inline: false
            });
            
            // Add usage note
            embed.addFields({
                name: '> 📝 Lưu ý',
                value: `- Âm lượng mới sẽ áp dụng cho tất cả lần sử dụng tiếp theo`,
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newSound.guild.name} • Soundboard Volume Update`,
                iconURL: newSound.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Soundboard volume update logged: ${newSound.name}`);
            
        } catch (error) {
            console.error('Error in soundboardSoundVolumeUpdate audit log:', error);
        }
    }
};
