// Mock database functions for audit log system
// In production, these would connect to a real database

const auditLogConfigs = new Map();
const auditLogs = new Map();

class AuditLogDatabase {
    // Config management
    async setAuditLogConfig(guildId, config) {
        auditLogConfigs.set(guildId, {
            ...config,
            updatedAt: new Date().toISOString()
        });
        return true;
    }

    async getAuditLogConfig(guildId) {
        return auditLogConfigs.get(guildId) || null;
    }

    async removeAuditLogConfig(guildId) {
        auditLogConfigs.delete(guildId);
        return true;
    }

    async updateAuditLogConfig(guildId, updates) {
        const existing = auditLogConfigs.get(guildId);
        if (!existing) return null;

        const updated = {
            ...existing,
            ...updates,
            updatedAt: new Date().toISOString()
        };

        auditLogConfigs.set(guildId, updated);
        return updated;
    }

    // Log storage
    async addAuditLog(guildId, logData) {
        if (!auditLogs.has(guildId)) {
            auditLogs.set(guildId, []);
        }

        const log = {
            id: Date.now() + Math.random(),
            guildId,
            timestamp: new Date().toISOString(),
            ...logData
        };

        auditLogs.get(guildId).push(log);
        
        // Keep only last 10000 logs per guild
        const logs = auditLogs.get(guildId);
        if (logs.length > 10000) {
            logs.splice(0, logs.length - 10000);
        }

        return log;
    }

    async getAuditLogs(guildId, startDate, endDate, limit = 1000) {
        const logs = auditLogs.get(guildId) || [];
        
        return logs
            .filter(log => {
                const logDate = new Date(log.timestamp);
                return logDate >= startDate && logDate <= endDate;
            })
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
            .slice(0, limit);
    }

    async getAuditLogStats(guildId) {
        const logs = auditLogs.get(guildId) || [];
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        
        const recentLogs = logs.filter(log => new Date(log.timestamp) >= thirtyDaysAgo);
        
        // Count events by type
        const eventCounts = {};
        recentLogs.forEach(log => {
            eventCounts[log.eventType] = (eventCounts[log.eventType] || 0) + 1;
        });

        const topEvent = Object.keys(eventCounts).reduce((a, b) => 
            eventCounts[a] > eventCounts[b] ? a : b, 'N/A'
        );

        return {
            totalLogs: recentLogs.length,
            alerts: Math.floor(recentLogs.length * 0.05), // 5% of logs trigger alerts
            topEvent: topEvent,
            eventCounts
        };
    }

    // Alert management
    async addAlert(guildId, alertData) {
        const alert = {
            id: Date.now() + Math.random(),
            guildId,
            timestamp: new Date().toISOString(),
            ...alertData
        };

        // In production, this would store alerts and potentially send notifications
        console.log(`🚨 ALERT for guild ${guildId}:`, alert);
        
        return alert;
    }

    async getRecentAlerts(guildId, hours = 24) {
        // Mock implementation - in production would query real alerts
        const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
        
        return [
            {
                id: 1,
                type: 'MASS_MESSAGE_DELETE',
                severity: 'HIGH',
                message: '15 tin nhắn bị xóa trong 3 phút',
                timestamp: new Date().toISOString(),
                user: '123456789',
                channel: '987654321'
            }
        ].filter(alert => new Date(alert.timestamp) >= cutoff);
    }

    // Event type definitions
    getEventTypes() {
        return {
            // Applications
            APP_ADD: 'App add',
            APP_REMOVE: 'App remove',
            APP_COMMAND_PERMISSION_UPDATE: 'App command permission update',

            // Channels
            CHANNEL_CREATE: 'Channel create',
            CHANNEL_DELETE: 'Channel delete',
            CHANNEL_PINS_UPDATE: 'Channel pins update',
            CHANNEL_NAME_UPDATE: 'Channel name update',
            CHANNEL_TOPIC_UPDATE: 'Channel topic update',
            CHANNEL_NSFW_UPDATE: 'Channel nsfw update',
            CHANNEL_PARENT_UPDATE: 'Channel parent update',
            CHANNEL_PERMISSION_UPDATE: 'Channel permission update',
            CHANNEL_TYPE_UPDATE: 'Channel type update',
            CHANNEL_BITRATE_UPDATE: 'Channel bitrate update',
            CHANNEL_USER_LIMIT_UPDATE: 'Channel user limit update',
            CHANNEL_SLOW_MODE_UPDATE: 'Channel slow mode update',
            CHANNEL_RTC_REGION_UPDATE: 'Channel rtc region update',
            CHANNEL_VIDEO_QUALITY_UPDATE: 'Channel video quality update',
            CHANNEL_DEFAULT_ARCHIVE_DURATION_UPDATE: 'Channel default archive duration update',
            CHANNEL_DEFAULT_THREAD_SLOW_MODE_UPDATE: 'Channel default thread slow mode update',
            CHANNEL_DEFAULT_REACTION_EMOJI_UPDATE: 'Channel default reaction emoji update',
            CHANNEL_DEFAULT_SORT_ORDER_UPDATE: 'Channel default sort order update',
            CHANNEL_FORUM_TAGS_UPDATE: 'Channel forum tags update',
            CHANNEL_FORUM_LAYOUT_UPDATE: 'Channel forum layout update',
            CHANNEL_VOICE_STATUS_UPDATE: 'Channel voice status update',

            // Discord AutoMod
            AUTOMOD_RULE_CREATE: 'Discord automod rule create',
            AUTOMOD_RULE_DELETE: 'Discord automod rule delete',
            AUTOMOD_RULE_TOGGLE: 'Discord automod rule toggle',
            AUTOMOD_RULE_NAME_UPDATE: 'Discord automod rule name update',
            AUTOMOD_RULE_ACTIONS_UPDATE: 'Discord automod rule actions update',
            AUTOMOD_RULE_CONTENT_UPDATE: 'Discord automod rule content update',
            AUTOMOD_RULE_ROLES_UPDATE: 'Discord automod rule roles update',
            AUTOMOD_RULE_CHANNELS_UPDATE: 'Discord automod rule channels update',
            AUTOMOD_RULE_WHITELIST_UPDATE: 'Discord automod rule whitelist update',

            // Emoji
            EMOJI_CREATE: 'Emoji create',
            EMOJI_DELETE: 'Emoji delete',
            EMOJI_NAME_UPDATE: 'Emoji name update',
            EMOJI_ROLES_UPDATE: 'Emoji roles update',

            // Events
            EVENT_CREATE: 'Event create',
            EVENT_DELETE: 'Event delete',
            EVENT_LOCATION_UPDATE: 'Event location update',
            EVENT_DESCRIPTION_UPDATE: 'Event description update',
            EVENT_NAME_UPDATE: 'Event name update',
            EVENT_PRIVACY_LEVEL_UPDATE: 'Event privacy level update',
            EVENT_START_TIME_UPDATE: 'Event start time update',
            EVENT_END_TIME_UPDATE: 'Event end time update',
            EVENT_STATUS_UPDATE: 'Event status update',
            EVENT_IMAGE_UPDATE: 'Event image update',
            EVENT_USER_SUBSCRIBE: 'Event user subscribe',
            EVENT_USER_UNSUBSCRIBE: 'Event user unsubscribe',

            // Invites
            INVITE_CREATE: 'Invite create',
            INVITE_DELETE: 'Invite delete',
            INVITE_POST: 'Invite post',

            // Messages
            MESSAGE_DELETE: 'Message delete',
            MESSAGE_BULK_DELETE: 'Message bulk delete',
            MESSAGE_EDIT_PUBLISH: 'Message edit publish',
            MESSAGE_SENT_COMMAND: 'Message sent using command',

            // Polls
            POLL_CREATE: 'Poll create',
            POLL_DELETE: 'Poll delete',
            POLL_FINALIZE: 'Poll finalize',
            POLL_VOTE_ADD: 'Poll vote add',
            POLL_VOTE_REMOVE: 'Poll vote remove',

            // Roles
            ROLE_CREATE: 'Role create',
            ROLE_DELETE: 'Role delete',
            ROLE_COLOR_UPDATE: 'Role color update',
            ROLE_HOIST_UPDATE: 'Role hoist update',
            ROLE_MENTIONABLE_UPDATE: 'Role mentionable update',
            ROLE_NAME_UPDATE: 'Role name update',
            ROLE_PERMISSIONS_UPDATE: 'Role permissions update',
            ROLE_ICON_UPDATE: 'Role icon update',

            // Stage
            STAGE_START: 'Stage start',
            STAGE_END: 'Stage end',
            STAGE_TOPIC_UPDATE: 'Stage topic update',
            STAGE_PRIVACY_UPDATE: 'Stage privacy update',

            // Server
            BAN_ADD: 'Ban add',
            BAN_REMOVE: 'Ban remove',
            USER_JOIN: 'User join',
            USER_LEAVE: 'User leave',
            USER_KICK: 'User kick',
            MEMBER_PRUNE: 'Member prune',
            AFK_CHANNEL_UPDATE: 'AFK channel update',
            AFK_TIMEOUT_UPDATE: 'AFK timeout update',
            SERVER_BANNER_UPDATE: 'Server banner update',
            MESSAGE_NOTIFICATIONS_UPDATE: 'Message notifications update',
            SERVER_DISCOVERY_SPLASH_UPDATE: 'Server discovery splash update',
            SERVER_CONTENT_FILTER_LEVEL_UPDATE: 'Server content filter level update',
            SERVER_FEATURES_UPDATE: 'Server features update',
            SERVER_ICON_UPDATE: 'Server icon update',
            MFA_LEVEL_UPDATE: 'MFA level update',
            SERVER_NAME_UPDATE: 'Server name update',
            SERVER_DESCRIPTION_UPDATE: 'Server description update',
            SERVER_OWNER_UPDATE: 'Server owner update',
            PARTNERED_UPDATE: 'Partnered update',
            SERVER_BOOST_LEVEL_UPDATE: 'Server boost level update',
            BOOST_PROGRESS_BAR_TOGGLE: 'Boost progress bar toggle',
            PUBLIC_UPDATES_CHANNEL_UPDATE: 'Public updates channel update',
            SERVER_RULES_CHANNEL_UPDATE: 'Server rules channel update',
            SERVER_SPLASH_UPDATE: 'Server splash update',
            SYSTEM_CHANNEL_UPDATE: 'System channel update',
            SERVER_VANITY_UPDATE: 'Server vanity update',
            VERIFICATION_LEVEL_UPDATE: 'Verification level update',
            VERIFIED_UPDATE: 'Verified update',
            SERVER_WIDGET_UPDATE: 'Server widget update',
            SERVER_PREFERRED_LOCALE_UPDATE: 'Server preferred locale update',
            ONBOARDING_TOGGLE: 'Onboarding toggle',
            ONBOARDING_CHANNELS_UPDATE: 'Onboarding channels update',
            ONBOARDING_QUESTION_ADD: 'Onboarding question add',
            ONBOARDING_QUESTION_REMOVE: 'Onboarding question remove',
            ONBOARDING_QUESTION_UPDATE: 'Onboarding question update',

            // Stickers
            STICKER_CREATE: 'Sticker create',
            STICKER_DELETE: 'Sticker delete',
            STICKER_NAME_UPDATE: 'Sticker name update',
            STICKER_DESCRIPTION_UPDATE: 'Sticker description update',
            STICKER_RELATED_EMOJI_UPDATE: 'Sticker related emoji update',

            // Soundboard
            SOUNDBOARD_SOUND_UPLOAD: 'Soundboard sound upload',
            SOUNDBOARD_SOUND_NAME_UPDATE: 'Soundboard sound name update',
            SOUNDBOARD_SOUND_VOLUME_UPDATE: 'Soundboard sound volume update',
            SOUNDBOARD_SOUND_EMOJI_UPDATE: 'Soundboard sound emoji update',
            SOUNDBOARD_SOUND_DELETE: 'Soundboard sound delete',

            // Threads
            THREAD_CREATE: 'Thread create',
            THREAD_DELETE: 'Thread delete',
            THREAD_NAME_UPDATE: 'Thread name update',
            THREAD_SLOW_MODE_UPDATE: 'Thread slow mode update',
            THREAD_ARCHIVE_DURATION_UPDATE: 'Thread archive duration update',
            THREAD_ARCHIVE: 'Thread archive',
            THREAD_UNARCHIVE: 'Thread unarchive',
            THREAD_LOCK: 'Thread lock',
            THREAD_UNLOCK: 'Thread unlock',

            // Users
            USER_NAME_UPDATE: 'User name update',
            USER_ROLES_UPDATE: 'User roles update',
            USER_ROLES_ADD: 'User roles add',
            USER_ROLES_REMOVE: 'User roles remove',
            USER_AVATAR_UPDATE: 'User avatar update',
            USER_TIMED_OUT: 'User timed out',
            USER_TIMEOUT_REMOVED: 'User timeout removed',

            // Voice
            VOICE_CHANNEL_FULL: 'Voice channel full',
            VOICE_USER_JOIN: 'Voice user join',
            VOICE_USER_SWITCH: 'Voice user switch',
            VOICE_USER_LEAVE: 'Voice user leave',
            VOICE_USER_MOVE: 'Voice user move',
            VOICE_USER_KICK: 'Voice user kick',

            // Webhooks
            WEBHOOK_CREATE: 'Webhook create',
            WEBHOOK_AVATAR_UPDATE: 'Webhook avatar update',
            WEBHOOK_NAME_UPDATE: 'Webhook name update',
            WEBHOOK_CHANNEL_UPDATE: 'Webhook channel update',
            WEBHOOK_DELETE: 'Webhook delete',

            // Moderation
            AUTO_MODERATION: 'Auto moderation',
            CASE_DELETE: 'Case delete',
            CASE_UPDATE: 'Case update',
            KICK_ADD: 'Kick add',
            KICK_REMOVE: 'Kick remove',
            MUTE_ADD: 'Mute add',
            MUTE_REMOVE: 'Mute remove',
            WARN_ADD: 'Warn add',
            WARN_REMOVE: 'Warn remove',
            REPORT_CREATE: 'Report create',
            REPORT_IGNORE: 'Report ignore',
            REPORT_ACCEPT: 'Report accept',
            USER_NOTE_ADD: 'User note add',
            USER_NOTE_REMOVE: 'User note remove'
        };
    }

    // Check if event should be logged based on config
    shouldLogEvent(config, eventType) {
        if (!config || !config.enabled) return false;

        const eventCategories = {
            all: Object.keys(this.getEventTypes()),
            moderation: [
                'AUTO_MODERATION', 'BAN_ADD', 'BAN_REMOVE', 'KICK_ADD', 'KICK_REMOVE',
                'MUTE_ADD', 'MUTE_REMOVE', 'WARN_ADD', 'WARN_REMOVE', 'CASE_DELETE',
                'CASE_UPDATE', 'REPORT_CREATE', 'REPORT_IGNORE', 'REPORT_ACCEPT',
                'USER_NOTE_ADD', 'USER_NOTE_REMOVE'
            ],
            server: [
                'USER_JOIN', 'USER_LEAVE', 'SERVER_NAME_UPDATE', 'SERVER_ICON_UPDATE',
                'SERVER_BANNER_UPDATE', 'SERVER_BOOST_LEVEL_UPDATE', 'VERIFICATION_LEVEL_UPDATE',
                'MFA_LEVEL_UPDATE', 'SERVER_FEATURES_UPDATE', 'MEMBER_PRUNE'
            ],
            channels: [
                'CHANNEL_CREATE', 'CHANNEL_DELETE', 'CHANNEL_NAME_UPDATE', 'CHANNEL_TOPIC_UPDATE',
                'CHANNEL_PERMISSION_UPDATE', 'CHANNEL_SLOW_MODE_UPDATE', 'CHANNEL_NSFW_UPDATE'
            ],
            users: [
                'USER_NAME_UPDATE', 'USER_AVATAR_UPDATE', 'USER_ROLES_UPDATE', 'USER_ROLES_ADD',
                'USER_ROLES_REMOVE', 'USER_TIMED_OUT', 'USER_TIMEOUT_REMOVED'
            ]
        };

        const allowedEvents = eventCategories[config.events] || eventCategories.all;
        return allowedEvents.includes(eventType);
    }
}

module.exports = new AuditLogDatabase();
