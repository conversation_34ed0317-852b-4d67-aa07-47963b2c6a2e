(function(_0x31e828,_0x151a9b){function _0x192a4b(_0x488fbe,_0x295b3d){return _0xf4f8(_0x488fbe- -0x188,_0x295b3d);}const _0x38312b=_0x31e828();while(!![]){try{const _0x1d659d=-parseInt(_0x192a4b(0xf,-0x6))/0x1+parseInt(_0x192a4b(-0x5,0x20))/0x2*(-parseInt(_0x192a4b(-0x7d,-0x77))/0x3)+parseInt(_0x192a4b(-0x3d,-0x65))/0x4+-parseInt(_0x192a4b(-0x5c,-0x1b))/0x5+-parseInt(_0x192a4b(-0x5f,-0x25))/0x6+parseInt(_0x192a4b(-0x53,-0x6b))/0x7+parseInt(_0x192a4b(-0x24,-0x43))/0x8*(parseInt(_0x192a4b(-0x12,-0x48))/0x9);if(_0x1d659d===_0x151a9b)break;else _0x38312b['push'](_0x38312b['shift']());}catch(_0x3f4d67){_0x38312b['push'](_0x38312b['shift']());}}}(_0x23f0,0xe1af3));const {SlashCommandBuilder,ActionRowBuilder,ButtonBuilder,ButtonStyle,EmbedBuilder,AttachmentBuilder}=require(_0x227eb8(-0x190,-0x1dd)),{createCanvas}=require(_0x227eb8(-0x19a,-0x1a1)),fs=require('fs'),yaml=require(_0x227eb8(-0x1f5,-0x1f6));let config,lang;try{config=yaml[_0x227eb8(-0x18f,-0x1d5)](fs['readFileSync'](_0x227eb8(-0x1e1,-0x190),_0x227eb8(-0x1bf,-0x1b7))),lang=yaml[_0x227eb8(-0x18f,-0x148)](fs[_0x227eb8(-0x1b8,-0x1b7)](_0x227eb8(-0x1c6,-0x180),_0x227eb8(-0x1bf,-0x1c9)));}catch(_0x528ae1){console['error'](_0x227eb8(-0x1b4,-0x190),_0x528ae1);}module[_0x227eb8(-0x15f,-0x198)]={'data':new SlashCommandBuilder()[_0x227eb8(-0x172,-0x122)](_0x227eb8(-0x1bd,-0x1c3))[_0x227eb8(-0x168,-0x125)](_0x227eb8(-0x150,-0x176))['addStringOption'](_0x13f99d=>_0x13f99d[_0x227eb8(-0x172,-0x128)](_0x227eb8(-0x1a3,-0x1d3))[_0x227eb8(-0x168,-0x13f)](_0x227eb8(-0x157,-0x12d))[_0x227eb8(-0x1d2,-0x188)](!![])[_0x227eb8(-0x1d3,-0x192)]({'name':_0x227eb8(-0x19c,-0x1ec),'value':'bot'},{'name':_0x227eb8(-0x15e,-0x1b1),'value':'player'}))['addStringOption'](_0x1639f4=>_0x1639f4[_0x227eb8(-0x172,-0x12f)](_0x227eb8(-0x158,-0x10f))[_0x227eb8(-0x168,-0x13b)](_0x227eb8(-0x153,-0x126))[_0x227eb8(-0x1d3,-0x184)]({'name':_0x227eb8(-0x15a,-0x112),'value':_0x227eb8(-0x19d,-0x169)},{'name':_0x227eb8(-0x193,-0x1c7),'value':_0x227eb8(-0x16a,-0x16d)},{'name':_0x227eb8(-0x16d,-0x141),'value':'hard'}))[_0x227eb8(-0x1c3,-0x1cd)](_0x12388e=>_0x12388e[_0x227eb8(-0x172,-0x145)](_0x227eb8(-0x17f,-0x1cb))[_0x227eb8(-0x168,-0x121)](_0x227eb8(-0x1f6,-0x1e4)))[_0x227eb8(-0x1ec,-0x1a8)](![]),'category':_0x227eb8(-0x1a8,-0x198),async 'execute'(_0x501c2d){function _0x23bfed(_0x2c481c,_0x3b6de0){return _0x227eb8(_0x2c481c-0x6da,_0x3b6de0);}try{const _0x114082=_0x501c2d[_0x23bfed(0x515,0x4c5)][_0x23bfed(0x577,0x5af)](_0x23bfed(0x537,0x583)),_0x2d8085=_0x501c2d[_0x23bfed(0x515,0x53e)][_0x23bfed(0x55d,0x589)](_0x23bfed(0x55b,0x598)),_0x4cddb0=_0x501c2d['options'][_0x23bfed(0x577,0x589)](_0x23bfed(0x582,0x5ba))||_0x23bfed(0x570,0x572);if(_0x114082==='player'&&!_0x2d8085)return _0x501c2d[_0x23bfed(0x502,0x4bf)]({'content':_0x23bfed(0x4f6,0x53e),'ephemeral':!![]});_0x114082===_0x23bfed(0x541,0x569)?await requestOpponentConfirmation(_0x501c2d,_0x2d8085,_0x4cddb0):await startGame(_0x501c2d,_0x114082,_0x2d8085,_0x4cddb0);}catch(_0x52de26){console[_0x23bfed(0x538,0x528)]('Error\x20executing\x20command:',_0x52de26),await _0x501c2d[_0x23bfed(0x502,0x4b4)]({'content':'An\x20error\x20occurred\x20while\x20starting\x20the\x20game.\x20Please\x20try\x20again\x20later.','ephemeral':!![]});}}};async function requestOpponentConfirmation(_0x41d551,_0x26f569,_0x11c6ad){const _0x2b6f62=new EmbedBuilder()[_0x222772(0x2f1,0x346)](_0x222772(0x2cc,0x292))[_0x222772(0x2ad,0x2e7)](_0x222772(0x30d,0x2da))[_0x222772(0x311,0x2c0)](_0x41d551[_0x222772(0x2de,0x2f5)]+_0x222772(0x2a4,0x2f5))[_0x222772(0x289,0x26e)]({'text':_0x222772(0x2f5,0x2db)}),_0x19554c=new ButtonBuilder()['setCustomId'](_0x222772(0x2f2,0x316))[_0x222772(0x314,0x2e4)](_0x222772(0x306,0x304))['setStyle'](ButtonStyle[_0x222772(0x2b2,0x2c5)]),_0x27c869=new ButtonBuilder()[_0x222772(0x28c,0x2da)]('decline_game')[_0x222772(0x314,0x2f5)](_0x222772(0x2bf,0x2e8))[_0x222772(0x32d,0x309)](ButtonStyle[_0x222772(0x2ee,0x335)]);function _0x222772(_0x3600c7,_0x440e6f){return _0x227eb8(_0x3600c7-0x479,_0x440e6f);}const _0x5c605b=new ActionRowBuilder()[_0x222772(0x2c2,0x2d4)](_0x19554c,_0x27c869),_0x19cf21=await _0x41d551[_0x222772(0x2a1,0x2e6)]({'content':_0x26f569+_0x222772(0x2ae,0x2ba),'embeds':[_0x2b6f62],'components':[_0x5c605b],'fetchReply':!![]}),_0x30177f=_0x19cf21['createMessageComponentCollector']({'time':0xea60});_0x30177f['on'](_0x222772(0x2c9,0x2b6),async _0x47791a=>{if(_0x47791a['user']['id']!==_0x26f569['id']){await _0x47791a[_0x59e0e9(-0x4,0x39)]({'content':_0x59e0e9(0x5c,0x5c),'ephemeral':!![]});return;}if(_0x47791a['customId']===_0x59e0e9(0x3f,0x8a))await _0x47791a[_0x59e0e9(0x6e,0x58)]({'content':'Game\x20accepted!\x20Starting\x20now...','components':[]}),await startGame(_0x41d551,_0x59e0e9(0x94,0x78),_0x26f569,_0x11c6ad);else _0x47791a[_0x59e0e9(0xe3,0xbc)]==='decline_game'&&(await _0x47791a[_0x59e0e9(0x20,0x58)]({'content':_0x59e0e9(0x7d,0xb5),'components':[]}),await _0x41d551[_0x59e0e9(0x3f,0x66)]({'content':_0x26f569+'\x20has\x20declined\x20the\x20game.'}));function _0x59e0e9(_0x25cfaa,_0x54d5bc){return _0x222772(_0x54d5bc- -0x268,_0x25cfaa);}_0x30177f[_0x59e0e9(-0x2e,0x1f)]();}),_0x30177f['on'](_0x222772(0x308,0x2c3),async(_0x49a2f6,_0x3a65b4)=>{function _0x4d44e3(_0x5d4bb6,_0x214092){return _0x222772(_0x5d4bb6-0x218,_0x214092);}_0x3a65b4===_0x4d44e3(0x507,0x540)&&_0x49a2f6['size']===0x0&&await _0x41d551[_0x4d44e3(0x4ac,0x4cf)]({'content':_0x4d44e3(0x4d0,0x514),'components':[]});});}async function startGame(_0x47c63d,_0x5349ae,_0x39d02d,_0x5c485b){function _0xdc67f3(_0x482722,_0x4ee401){return _0x227eb8(_0x482722-0x502,_0x4ee401);}try{!_0x47c63d[_0xdc67f3(0x36c,0x3a1)]&&!_0x47c63d[_0xdc67f3(0x36e,0x330)]&&await _0x47c63d[_0xdc67f3(0x347,0x31b)]();const _0x57a922=lang[_0xdc67f3(0x3a7,0x37d)][_0xdc67f3(0x3ae,0x3b5)][_0xdc67f3(0x3b5,0x3b5)][_0xdc67f3(0x344,0x2f2)],_0x2cf9d6=lang[_0xdc67f3(0x3a7,0x39e)][_0xdc67f3(0x3ae,0x3d0)][_0xdc67f3(0x3b5,0x3e6)]['Player'],_0x11aa84=lang[_0xdc67f3(0x3a7,0x391)]['Board']['Emojis'][_0xdc67f3(0x366,0x326)],_0x24c863=Array(0x6)[_0xdc67f3(0x35e,0x344)]()[_0xdc67f3(0x358,0x304)](()=>Array(0x7)[_0xdc67f3(0x35e,0x389)](_0x57a922)),_0x3f5b4b=_0xdc67f3(0x37c,0x3b5)+Date[_0xdc67f3(0x3b0,0x3e0)]()+'-'+_0x47c63d[_0xdc67f3(0x367,0x34c)]['id'];let _0xa983c3=_0x47c63d['user']['id'],_0x2e4962={'row':null,'col':null};const _0x244008=await createGameBoardCanvas(_0x24c863,_0x2cf9d6,_0x11aa84,_0x57a922,_0x2e4962,null);await _0x47c63d['editReply']({'content':createGameMessage(_0x47c63d[_0xdc67f3(0x367,0x31e)][_0xdc67f3(0x328,0x361)],_0x24c863,_0xa983c3,_0x47c63d[_0xdc67f3(0x367,0x382)],_0x39d02d),'files':[_0x244008],'components':createBoardComponents(_0x3f5b4b,![])});const _0x4fb698=await _0x47c63d[_0xdc67f3(0x35c,0x379)](),_0x507bac=_0x4fb698[_0xdc67f3(0x342,0x33b)]({'time':0x927c0,'filter':_0x5e8c88=>_0x5e8c88[_0xdc67f3(0x3ad,0x36e)][_0xdc67f3(0x339,0x364)](_0x3f5b4b)&&(_0x5e8c88[_0xdc67f3(0x367,0x365)]['id']===_0x47c63d[_0xdc67f3(0x367,0x3a5)]['id']||_0x39d02d&&_0x5e8c88[_0xdc67f3(0x367,0x35b)]['id']===_0x39d02d['id'])});let _0x5d56b3=_0x5349ae==='bot'?_0xdc67f3(0x319,0x347):_0x39d02d['id'];_0x507bac['on'](_0xdc67f3(0x352,0x324),async _0x305aa4=>{function _0x20ab3e(_0x11f8bd,_0xaff0de){return _0xdc67f3(_0xaff0de- -0x576,_0x11f8bd);}try{await _0x305aa4[_0x20ab3e(-0x235,-0x24b)]();if(_0x305aa4[_0x20ab3e(-0x252,-0x20f)]['id']!==_0xa983c3){await _0x305aa4[_0x20ab3e(-0x1dc,-0x21f)]({'content':_0x20ab3e(-0x28f,-0x25a),'ephemeral':!![]});return;}const _0x427021=_0x305aa4['customId']['split']('_'),_0x202d2d=parseInt(_0x427021[0x2]);_0x2e4962={'row':null,'col':_0x202d2d};if(!makeMove(_0x24c863,_0x202d2d,_0xa983c3===_0x47c63d[_0x20ab3e(-0x203,-0x20f)]['id']?_0x2cf9d6:_0x11aa84,_0x2e4962)){await _0x305aa4[_0x20ab3e(-0x1f9,-0x21f)]({'content':'This\x20column\x20is\x20full!','ephemeral':!![]});return;}let _0x3ba5f5=checkWin(_0x24c863,_0xa983c3===_0x47c63d[_0x20ab3e(-0x237,-0x20f)]['id']?_0x2cf9d6:_0x11aa84);if(_0x3ba5f5){await endGame(_0x47c63d,_0x305aa4['message'],_0x305aa4[_0x20ab3e(-0x228,-0x20f)][_0x20ab3e(-0x28f,-0x24e)]+_0x20ab3e(-0x25b,-0x25f),_0x24c863,_0x2cf9d6,_0x11aa84,_0x57a922,_0x2e4962,_0x3ba5f5),_0x507bac[_0x20ab3e(-0x2ae,-0x266)]();return;}else{if(isBoardFull(_0x24c863)){await endGame(_0x47c63d,_0x305aa4['message'],'It\x27s\x20a\x20draw!',_0x24c863,_0x2cf9d6,_0x11aa84,_0x57a922,_0x2e4962,null),_0x507bac['stop']();return;}}if(_0x5349ae===_0x20ab3e(-0x2b3,-0x25d)&&_0xa983c3!==_0x20ab3e(-0x247,-0x25d)){await botMove(_0x24c863,_0x11aa84,_0x2cf9d6,_0x5c485b,_0x2e4962),_0x3ba5f5=checkWin(_0x24c863,_0x11aa84);if(_0x3ba5f5){await endGame(_0x47c63d,_0x305aa4[_0x20ab3e(-0x227,-0x1f4)],_0x20ab3e(-0x1e6,-0x1d8),_0x24c863,_0x2cf9d6,_0x11aa84,_0x57a922,_0x2e4962,_0x3ba5f5),_0x507bac['stop']();return;}else{if(isBoardFull(_0x24c863)){await endGame(_0x47c63d,_0x305aa4[_0x20ab3e(-0x1a6,-0x1f4)],_0x20ab3e(-0x1f2,-0x1ec),_0x24c863,_0x2cf9d6,_0x11aa84,_0x57a922,_0x2e4962,null),_0x507bac['stop']();return;}}_0xa983c3=_0x47c63d[_0x20ab3e(-0x221,-0x20f)]['id'];}else[_0xa983c3,_0x5d56b3]=[_0x5d56b3,_0xa983c3];const _0x483255=await createGameBoardCanvas(_0x24c863,_0x2cf9d6,_0x11aa84,_0x57a922,_0x2e4962,null);await _0x305aa4[_0x20ab3e(-0x2a5,-0x259)]({'content':createGameMessage(_0x47c63d[_0x20ab3e(-0x1be,-0x20f)][_0x20ab3e(-0x27b,-0x24e)],_0x24c863,_0xa983c3,_0x47c63d[_0x20ab3e(-0x1e9,-0x20f)],_0x39d02d),'files':[_0x483255],'components':createBoardComponents(_0x3f5b4b,![])});}catch(_0x14c055){console[_0x20ab3e(-0x1d4,-0x216)](_0x20ab3e(-0x224,-0x243),_0x14c055),!_0x47c63d[_0x20ab3e(-0x21d,-0x208)]&&!_0x47c63d[_0x20ab3e(-0x21a,-0x20a)]?await _0x47c63d['reply']({'content':_0x20ab3e(-0x24a,-0x219),'ephemeral':!![]}):await _0x47c63d[_0x20ab3e(-0x1d8,-0x21f)]({'content':'An\x20error\x20occurred\x20while\x20processing\x20your\x20move.\x20Please\x20try\x20again.','ephemeral':!![]});}});}catch(_0x38c397){console[_0xdc67f3(0x360,0x311)]('Error\x20starting\x20the\x20game:',_0x38c397),!_0x47c63d[_0xdc67f3(0x36e,0x36b)]&&!_0x47c63d[_0xdc67f3(0x36c,0x35d)]?await _0x47c63d[_0xdc67f3(0x32a,0x327)]({'content':'An\x20error\x20occurred\x20while\x20setting\x20up\x20the\x20game.\x20Please\x20try\x20again\x20later.','ephemeral':!![]}):await _0x47c63d['followUp']({'content':_0xdc67f3(0x380,0x3c4),'ephemeral':!![]});}}async function createGameBoardCanvas(_0x5a084a,_0x209a7f,_0x2e8b73,_0x5a6eb2,_0x31b028,_0x3d8b27){function _0x5640bd(_0x134580,_0x4228a1){return _0x227eb8(_0x134580-0x30e,_0x4228a1);}try{const _0x4ab769=createCanvas(0x2bc,0x258),_0x176c21=_0x4ab769[_0x5640bd(0x177,0x1a3)]('2d'),_0x1f0654=0x64,_0x57128f=0xa,_0x7108eb=(_0x1f0654-_0x57128f)/0x2,_0xb50e6e=_0x176c21[_0x5640bd(0x1a3,0x156)](0x0,0x0,_0x4ab769[_0x5640bd(0x131,0x150)],_0x4ab769['height']);_0xb50e6e[_0x5640bd(0x11d,0xc8)](0x0,'#f8b500'),_0xb50e6e[_0x5640bd(0x11d,0x11c)](0x1,_0x5640bd(0x135,0x175)),_0x176c21[_0x5640bd(0x199,0x1e6)]=_0xb50e6e,_0x176c21[_0x5640bd(0x19f,0x1aa)](0x0,0x0,_0x4ab769[_0x5640bd(0x131,0x164)],_0x4ab769['height']),_0x176c21[_0x5640bd(0x199,0x156)]='#FFFFFF';for(let _0x467e57=0x0;_0x467e57<_0x5a084a[_0x5640bd(0x1bf,0x20c)];_0x467e57++){for(let _0x4f59ed=0x0;_0x4f59ed<_0x5a084a[0x0][_0x5640bd(0x1bf,0x1df)];_0x4f59ed++){const _0x4dbb46=_0x4f59ed*_0x1f0654+_0x1f0654/0x2,_0x4cb366=_0x467e57*_0x1f0654+_0x1f0654/0x2;_0x176c21[_0x5640bd(0x185,0x1a9)](),_0x176c21[_0x5640bd(0x146,0x184)]='rgba(0,\x200,\x200,\x200.3)',_0x176c21['shadowBlur']=0x5,_0x176c21[_0x5640bd(0x16d,0x150)]=0x3,_0x176c21[_0x5640bd(0x1b8,0x209)]=0x3,_0x176c21[_0x5640bd(0x199,0x177)]=_0x5640bd(0x193,0x14c),_0x176c21[_0x5640bd(0x176,0x14c)](),_0x176c21['arc'](_0x4dbb46,_0x4cb366,_0x7108eb,0x0,0x2*Math['PI']),_0x176c21[_0x5640bd(0x16a,0x1a1)](),_0x176c21['restore']();if(_0x5a084a[_0x467e57][_0x4f59ed]===_0x209a7f){const _0x53a4fb=_0x176c21[_0x5640bd(0x182,0x197)](_0x4dbb46,_0x4cb366,_0x7108eb-_0x57128f,_0x4dbb46,_0x4cb366,0x0);_0x53a4fb['addColorStop'](0x0,_0x5640bd(0x1ae,0x1b7)),_0x53a4fb[_0x5640bd(0x11d,0xe8)](0x1,_0x5640bd(0x1b5,0x1cf)),_0x176c21[_0x5640bd(0x199,0x17c)]=_0x53a4fb;}else{if(_0x5a084a[_0x467e57][_0x4f59ed]===_0x2e8b73){const _0x57bac7=_0x176c21[_0x5640bd(0x182,0x13b)](_0x4dbb46,_0x4cb366,_0x7108eb-_0x57128f,_0x4dbb46,_0x4cb366,0x0);_0x57bac7[_0x5640bd(0x11d,0xf9)](0x0,'#FF0000'),_0x57bac7[_0x5640bd(0x11d,0xd1)](0x1,_0x5640bd(0x195,0x17e)),_0x176c21[_0x5640bd(0x199,0x180)]=_0x57bac7;}else continue;}_0x176c21['save'](),_0x176c21[_0x5640bd(0x146,0x11c)]=_0x5640bd(0x14c,0x153),_0x176c21[_0x5640bd(0x194,0x17a)]=0x5,_0x176c21['shadowOffsetX']=0x3,_0x176c21[_0x5640bd(0x1b8,0x1de)]=0x3,_0x176c21[_0x5640bd(0x176,0x146)](),_0x176c21[_0x5640bd(0x165,0x14c)](_0x4dbb46,_0x4cb366,_0x7108eb-_0x57128f,0x0,0x2*Math['PI']),_0x176c21[_0x5640bd(0x16a,0x130)](),_0x176c21['restore']();}}if(_0x3d8b27)for(const {row:_0x33d8c3,col:_0x144cc1}of _0x3d8b27){const _0x5aca2a=_0x144cc1*_0x1f0654+_0x1f0654/0x2,_0x538670=_0x33d8c3*_0x1f0654+_0x1f0654/0x2;_0x176c21[_0x5640bd(0x185,0x1b3)](),_0x176c21['strokeStyle']=_0x5640bd(0x1a7,0x18b),_0x176c21['lineWidth']=0xa,_0x176c21['beginPath'](),_0x176c21[_0x5640bd(0x165,0x1a1)](_0x5aca2a,_0x538670,_0x7108eb,0x0,0x2*Math['PI']),_0x176c21[_0x5640bd(0x133,0x131)](),_0x176c21[_0x5640bd(0x167,0x129)]();}else{if(_0x31b028&&_0x31b028[_0x5640bd(0x140,0x170)]!==null&&_0x31b028[_0x5640bd(0x144,0x170)]!==null){const _0x29dba4=_0x31b028[_0x5640bd(0x144,0x125)]*_0x1f0654+_0x1f0654/0x2,_0x466cc4=_0x31b028['row']*_0x1f0654+_0x1f0654/0x2;_0x176c21[_0x5640bd(0x185,0x157)](),_0x176c21[_0x5640bd(0x124,0x121)]='black',_0x176c21[_0x5640bd(0x11a,0xe9)]=0xa,_0x176c21[_0x5640bd(0x176,0x14a)](),_0x176c21[_0x5640bd(0x165,0x170)](_0x29dba4,_0x466cc4,_0x7108eb,0x0,0x2*Math['PI']),_0x176c21[_0x5640bd(0x133,0x138)](),_0x176c21[_0x5640bd(0x167,0x1bd)]();}}const _0x193655=_0x4ab769[_0x5640bd(0x11b,0x113)](_0x5640bd(0x1bd,0x1b8));return new AttachmentBuilder(_0x193655,{'name':_0x5640bd(0x12f,0x114)});}catch(_0xa7b983){console['error']('Error\x20creating\x20game\x20board\x20canvas:',_0xa7b983);throw new Error(_0x5640bd(0x197,0x16c));}}function createBoardComponents(_0x73bb4,_0x3f6e79){function _0x24361a(_0x26514f,_0xe5ee57){return _0x227eb8(_0xe5ee57-0x57c,_0x26514f);}try{const _0x140782=[];for(let _0x30f6af=0x0;_0x30f6af<0x7;_0x30f6af+=0x5){const _0x17f60b=new ActionRowBuilder();for(let _0x354ab4=_0x30f6af;_0x354ab4<_0x30f6af+0x5&&_0x354ab4<0x7;_0x354ab4++){_0x17f60b[_0x24361a(0x3bd,0x3c5)](new ButtonBuilder()[_0x24361a(0x3a0,0x38f)](_0x73bb4+_0x24361a(0x37c,0x39a)+_0x354ab4)[_0x24361a(0x41e,0x430)](ButtonStyle[_0x24361a(0x3d2,0x3c0)])[_0x24361a(0x446,0x417)]((_0x354ab4+0x1)[_0x24361a(0x40e,0x416)]())[_0x24361a(0x3ff,0x3dc)](_0x3f6e79));}_0x140782[_0x24361a(0x3fc,0x3ea)](_0x17f60b);}return _0x140782;}catch(_0x41f962){console[_0x24361a(0x3d6,0x3da)](_0x24361a(0x3c5,0x399),_0x41f962);throw new Error(_0x24361a(0x398,0x3cb));}}function makeMove(_0x48042d,_0x21a6db,_0x3744f1,_0x1d9742){function _0x377305(_0xeb9c0a,_0x1d112a){return _0x227eb8(_0x1d112a-0x5d9,_0xeb9c0a);}try{for(let _0x7caf32=_0x48042d[_0x377305(0x445,0x48a)]-0x1;_0x7caf32>=0x0;_0x7caf32--){if(_0x48042d[_0x7caf32][_0x21a6db]===lang[_0x377305(0x484,0x47e)][_0x377305(0x454,0x485)][_0x377305(0x451,0x48c)][_0x377305(0x46a,0x41b)])return _0x48042d[_0x7caf32][_0x21a6db]=_0x3744f1,_0x1d9742[_0x377305(0x43f,0x40b)]=_0x7caf32,!![];}return![];}catch(_0x194329){return console[_0x377305(0x437,0x437)](_0x377305(0x487,0x44b),_0x194329),![];}}async function botMove(_0x5bf7ce,_0x2c1035,_0x571f21,_0x431e3a){function _0x21f69c(_0x13fd2e,_0x4e752e){return _0x227eb8(_0x13fd2e-0x379,_0x4e752e);}try{const _0x5db4f9=findBestMove(_0x5bf7ce,_0x2c1035,_0x571f21,_0x431e3a);_0x5db4f9!==null&&makeMove(_0x5bf7ce,_0x5db4f9,_0x2c1035,{});}catch(_0x2fa21e){console[_0x21f69c(0x1d7,0x1a2)]('Error\x20in\x20bot\x20move:',_0x2fa21e);}}function findBestMove(_0x19b353,_0x3d1357,_0x3daf9b,_0x266dd0){function _0x5e2a9c(_0x89245f,_0x496bad){return _0x227eb8(_0x496bad-0x474,_0x89245f);}try{const _0x470127=[];for(let _0x2b7a7d=0x0;_0x2b7a7d<0x7;_0x2b7a7d++){_0x19b353[0x0][_0x2b7a7d]===lang['Connectfour']['Board']['Emojis']['Blank']&&_0x470127[_0x5e2a9c(0x32d,0x2e2)](_0x2b7a7d);}if(_0x470127['length']===0x0)return null;if(_0x266dd0===_0x5e2a9c(0x2c0,0x2d7))return _0x470127[Math[_0x5e2a9c(0x281,0x2c5)](Math[_0x5e2a9c(0x245,0x285)]()*_0x470127[_0x5e2a9c(0x2da,0x325)])];if(_0x266dd0===_0x5e2a9c(0x31b,0x30a)){for(const _0x112b03 of _0x470127){const _0x4dad9a=_0x19b353[_0x5e2a9c(0x318,0x2ca)](_0x2a5cf5=>_0x2a5cf5[_0x5e2a9c(0x332,0x2e7)]());makeMove(_0x4dad9a,_0x112b03,_0x3d1357,{});if(checkWin(_0x4dad9a,_0x3d1357))return _0x112b03;}for(const _0x3a9a27 of _0x470127){const _0x228901=_0x19b353[_0x5e2a9c(0x2fe,0x2ca)](_0x4061dd=>_0x4061dd['slice']());makeMove(_0x228901,_0x3a9a27,_0x3daf9b,{});if(checkWin(_0x228901,_0x3daf9b))return _0x3a9a27;}return _0x470127[Math['floor'](Math[_0x5e2a9c(0x248,0x285)]()*_0x470127[_0x5e2a9c(0x368,0x325)])];}if(_0x266dd0===_0x5e2a9c(0x2bb,0x2f8)){let _0xef9173=-Infinity,_0x3284f7=null;for(const _0xeb41d3 of _0x470127){const _0x1a9ba1=_0x19b353[_0x5e2a9c(0x312,0x2ca)](_0x1abf89=>_0x1abf89[_0x5e2a9c(0x31e,0x2e7)]());makeMove(_0x1a9ba1,_0xeb41d3,_0x3d1357,{});const _0xf3f76c=minimax(_0x1a9ba1,0x3,![],_0x3d1357,_0x3daf9b);_0xf3f76c>_0xef9173&&(_0xef9173=_0xf3f76c,_0x3284f7=_0xeb41d3);}return _0x3284f7;}return _0x470127[Math[_0x5e2a9c(0x2d7,0x2c5)](Math[_0x5e2a9c(0x2ab,0x285)]()*_0x470127['length'])];}catch(_0xb0a127){return console[_0x5e2a9c(0x2dc,0x2d2)](_0x5e2a9c(0x2da,0x2f6),_0xb0a127),null;}}function minimax(_0x2c707f,_0x524511,_0x143501,_0x42bce3,_0x549f65){function _0x5726a3(_0x3c858d,_0x4e7e2a){return _0x227eb8(_0x4e7e2a-0x30e,_0x3c858d);}try{if(_0x524511===0x0||checkWin(_0x2c707f,_0x42bce3)||checkWin(_0x2c707f,_0x549f65))return scorePosition(_0x2c707f,_0x42bce3);const _0x507a98=[];for(let _0x249b3e=0x0;_0x249b3e<0x7;_0x249b3e++){_0x2c707f[0x0][_0x249b3e]===lang[_0x5726a3(0x17f,0x1b3)][_0x5726a3(0x1dd,0x1ba)][_0x5726a3(0x20e,0x1c1)][_0x5726a3(0x189,0x150)]&&_0x507a98['push'](_0x249b3e);}if(_0x143501){let _0x38a9ae=-Infinity;for(const _0xe8794e of _0x507a98){const _0x32c4bf=_0x2c707f['map'](_0x57327a=>_0x57327a[_0x5726a3(0x1c4,0x181)]());makeMove(_0x32c4bf,_0xe8794e,_0x42bce3,{});const _0x1ab1db=minimax(_0x32c4bf,_0x524511-0x1,![],_0x42bce3,_0x549f65);_0x38a9ae=Math['max'](_0x1ab1db,_0x38a9ae);}return _0x38a9ae;}else{let _0x5b2be0=Infinity;for(const _0x1d7708 of _0x507a98){const _0x192ba0=_0x2c707f[_0x5726a3(0x16c,0x164)](_0x132915=>_0x132915[_0x5726a3(0x1d7,0x181)]());makeMove(_0x192ba0,_0x1d7708,_0x549f65,{});const _0x78cf37=minimax(_0x192ba0,_0x524511-0x1,!![],_0x42bce3,_0x549f65);_0x5b2be0=Math[_0x5726a3(0x175,0x19e)](_0x78cf37,_0x5b2be0);}return _0x5b2be0;}}catch(_0x115734){return console[_0x5726a3(0x176,0x16c)](_0x5726a3(0x105,0x13a),_0x115734),0x0;}}function _0xf4f8(_0x18e7e6,_0xde0ca9){const _0x23f0cc=_0x23f0();return _0xf4f8=function(_0xf4f815,_0x40e8c6){_0xf4f815=_0xf4f815-0x103;let _0x4beb8a=_0x23f0cc[_0xf4f815];return _0x4beb8a;},_0xf4f8(_0x18e7e6,_0xde0ca9);}function scorePosition(_0x4724c4,_0x1df6e1){function _0x1aa0d2(_0x4483b6,_0x2f4277){return _0x227eb8(_0x4483b6-0x563,_0x2f4277);}try{let _0x4b8e99=0x0;const _0x41f7e0=_0x1df6e1===lang[_0x1aa0d2(0x408,0x3e4)][_0x1aa0d2(0x40f,0x3f9)]['Emojis']['Bot']?lang[_0x1aa0d2(0x408,0x430)][_0x1aa0d2(0x40f,0x41c)][_0x1aa0d2(0x416,0x3ed)][_0x1aa0d2(0x405,0x43a)]:lang[_0x1aa0d2(0x408,0x3c4)][_0x1aa0d2(0x40f,0x445)][_0x1aa0d2(0x416,0x410)][_0x1aa0d2(0x3c7,0x40e)],_0x423be7=_0x4724c4[_0x1aa0d2(0x3b9,0x3e4)](_0x34011d=>_0x34011d[0x3]),_0x1f7b45=_0x423be7[_0x1aa0d2(0x3c5,0x408)](_0x510d65=>_0x510d65===_0x1df6e1)[_0x1aa0d2(0x414,0x40d)];return _0x4b8e99+=_0x1f7b45*0x3,_0x4b8e99+=evaluateLines(_0x4724c4,_0x1df6e1),_0x4b8e99-=evaluateLines(_0x4724c4,_0x41f7e0),_0x4b8e99;}catch(_0x48db12){return console['error'](_0x1aa0d2(0x3b7,0x40c),_0x48db12),0x0;}}function evaluateLines(_0x29c9a5,_0x4de992){function _0x4c7e65(_0x1a0c83,_0xf45197){return _0x227eb8(_0xf45197-0x3c8,_0x1a0c83);}try{let _0x1bcda1=0x0;const _0x2aae11=[{'x':0x0,'y':0x1},{'x':0x1,'y':0x0},{'x':0x1,'y':0x1},{'x':0x1,'y':-0x1}];for(let _0xf25936=0x0;_0xf25936<_0x29c9a5[_0x4c7e65(0x27b,0x279)];_0xf25936++){for(let _0x40af68=0x0;_0x40af68<_0x29c9a5[0x0]['length'];_0x40af68++){if(_0x29c9a5[_0xf25936][_0x40af68]===_0x4de992)for(const {x:_0x385206,y:_0x5e3a45}of _0x2aae11){let _0x6b755c=0x0;for(let _0x57481b=0x0;_0x57481b<0x4;_0x57481b++){const _0x44e1f6=_0xf25936+_0x57481b*_0x385206,_0x535fd1=_0x40af68+_0x57481b*_0x5e3a45;if(_0x29c9a5[_0x44e1f6]&&_0x29c9a5[_0x44e1f6][_0x535fd1]===_0x4de992)_0x6b755c++;else break;}if(_0x6b755c===0x4)_0x1bcda1+=0x64;else{if(_0x6b755c===0x3)_0x1bcda1+=0xa;else{if(_0x6b755c===0x2)_0x1bcda1+=0x1;}}}}}return _0x1bcda1;}catch(_0x3482c2){return console[_0x4c7e65(0x205,0x226)](_0x4c7e65(0x230,0x1e1),_0x3482c2),0x0;}}function _0x227eb8(_0x4d0bd6,_0x4e32db){return _0xf4f8(_0x4d0bd6- -0x2f9,_0x4e32db);}function checkWin(_0x4ba763,_0x234530){function _0x90d245(_0x11146b,_0x3e972f){return _0x227eb8(_0x3e972f- -0x2e,_0x11146b);}try{const _0x3b1386=[{'x':0x0,'y':0x1},{'x':0x1,'y':0x0},{'x':0x1,'y':0x1},{'x':0x1,'y':-0x1}];for(let _0x2b90e3=0x0;_0x2b90e3<_0x4ba763[_0x90d245(-0x17d,-0x17d)];_0x2b90e3++){for(let _0x226426=0x0;_0x226426<_0x4ba763[0x0]['length'];_0x226426++){if(_0x4ba763[_0x2b90e3][_0x226426]===_0x234530)for(const {x:_0x46399d,y:_0x65b688}of _0x3b1386){const _0x24a097=[];for(let _0x5e2ede=0x0;_0x5e2ede<0x4;_0x5e2ede++){const _0x14368d=_0x2b90e3+_0x5e2ede*_0x46399d,_0x4991af=_0x226426+_0x5e2ede*_0x65b688;if(_0x4ba763[_0x14368d]&&_0x4ba763[_0x14368d][_0x4991af]===_0x234530)_0x24a097[_0x90d245(-0x1a6,-0x1c0)]({'row':_0x14368d,'col':_0x4991af});else break;}if(_0x24a097[_0x90d245(-0x18b,-0x17d)]===0x4)return _0x24a097;}}}return null;}catch(_0x14fbc7){return console[_0x90d245(-0x17f,-0x1d0)](_0x90d245(-0x1c8,-0x1cd),_0x14fbc7),null;}}function isBoardFull(_0x44a59e){function _0x2cc2e8(_0x189d59,_0x5002c5){return _0x227eb8(_0x5002c5-0x46b,_0x189d59);}try{return _0x44a59e[_0x2cc2e8(0x2bc,0x28b)](_0x20990c=>_0x20990c[_0x2cc2e8(0x283,0x28b)](_0x84a9a8=>_0x84a9a8!==lang[_0x2cc2e8(0x30f,0x310)]['Board'][_0x2cc2e8(0x337,0x31e)]['Blank']));}catch(_0x1540d5){return console[_0x2cc2e8(0x278,0x2c9)](_0x2cc2e8(0x2e2,0x28d),_0x1540d5),![];}}async function endGame(_0x58e578,_0x256556,_0x29758f,_0x5da71e,_0xd41643,_0x317e83,_0x510730,_0x172140,_0x399d80){function _0x2e0155(_0x4308cf,_0x2353b3){return _0x227eb8(_0x4308cf-0x2cb,_0x2353b3);}try{const _0x4c9b68=await createGameBoardCanvas(_0x5da71e,_0xd41643,_0x317e83,_0x510730,_0x172140,_0x399d80),_0x2a7c67=_0x29758f[_0x2e0155(0xfa,0xe8)](_0x2e0155(0x17d,0x1c7))?_0x29758f[_0x2e0155(0x16e,0x18e)]('\x20')[0x0]:_0x2e0155(0x118,0xc2),_0x275613=new EmbedBuilder()[_0x2e0155(0x143,0x177)](_0x29758f[_0x2e0155(0xfa,0x12d)](_0x2e0155(0x17d,0x133))?lang[_0x2e0155(0x170,0x13f)][_0x2e0155(0x180,0x171)][_0x2e0155(0x162,0x15a)]:lang[_0x2e0155(0x170,0x196)]['Colors'][_0x2e0155(0xf5,0xae)])[_0x2e0155(0xff,0xf9)](lang['Connectfour'][_0x2e0155(0x115,0xd7)]['Title'])[_0x2e0155(0x163,0x14b)](lang[_0x2e0155(0x170,0x172)][_0x2e0155(0x115,0xd0)][_0x2e0155(0x13a,0xe9)][_0x2e0155(0x14a,0x14c)](_0x2e0155(0xe3,0x9c),_0x2a7c67))['setFooter']({'text':lang[_0x2e0155(0x170,0x14d)]['Embed'][_0x2e0155(0xef,0xe0)]});await _0x256556[_0x2e0155(0x146,0x16d)]({'files':[_0x4c9b68],'embeds':[_0x275613],'components':[]});}catch(_0x2794fb){console[_0x2e0155(0x129,0x17c)](_0x2e0155(0x157,0x173),_0x2794fb),await _0x58e578[_0x2e0155(0x120,0x159)]({'content':'An\x20error\x20occurred\x20while\x20ending\x20the\x20game.\x20Please\x20try\x20again.','ephemeral':!![]});}}function _0x23f0(){const _0x14fe7e=['createLinearGradient','medium','Win','setDescription','lime','toString','setLabel','Bot\x20has\x20won!','getString','1120793xifNBl','Error\x20creating\x20game\x20message:','#0000FF','exports','Player','split','Game\x20declined.','Connectfour','Easy','#00008B','difficulty','Choose\x20game\x20type','shadowOffsetY','customId','Board','Choose\x20difficulty\x20level\x20(only\x20for\x20bot)','now','image/png','Play\x20Connect\x20Four\x20against\x20a\x20bot\x20or\x20another\x20player!','length','won','Emojis','setStyle','Colors','Choose\x20an\x20opponent\x20(required\x20if\x20playing\x20against\x20another\x20player)','js-yaml','lineWidth','toBuffer','stop','addColorStop','setFooter','random','129JzJLlW','setCustomId','setDMPermission','\x20has\x20won!','strokeStyle','bot','{user}','Error\x20evaluating\x20lines:','It\x20is\x20not\x20your\x20turn!','editReply','You\x20must\x20specify\x20an\x20opponent\x20when\x20playing\x20against\x20another\x20player.','Error\x20creating\x20board\x20components:','_column_','./config.yml','every','connect-four.png','Error\x20checking\x20if\x20board\x20is\x20full:','width','Footer','stroke','username','#ffcc00','reply','deferUpdate','Tie','\x20has\x20challenged\x20you\x20to\x20a\x20game\x20of\x20Connect\x20Four.\x20Do\x20you\x20accept?','Error\x20in\x20minimax\x20function:','addChoices','setRequired','includes','7873824LUZKzz','Error\x20handling\x20interaction:','row','1008165KjEgQS','setTitle',',\x20you\x27ve\x20been\x20challenged\x20to\x20a\x20game\x20of\x20Connect\x20Four!','col','startsWith','shadowColor','Success','././lang.yml','options','10720157IMQrWL','addUserOption','rgba(0,\x200,\x200,\x200.3)','The\x20game\x20request\x20has\x20expired.','createMessageComponentCollector','utf8','Blank','connectfour','Primary','deferReply','Decline','update','readFileSync','addComponents','Embed','This\x20confirmation\x20is\x20not\x20for\x20you.','Error\x20loading\x20configuration\x20files:','No\x20one','Title','Could\x20not\x20create\x20board\x20components.','collect','floor','2228268pwRBfI','#0099ff','Error\x20scoring\x20position:','followUp','map','arc','Fun','restore','fetchReply','An\x20error\x20occurred\x20while\x20processing\x20your\x20move.\x20Please\x20try\x20again.','fill','type','error','shadowOffsetX','setDisabled','Error\x20checking\x20win:','filter','easy','Bot','user','canvas','player','beginPath','getContext','deferred','824HjXkCJ','replied','Medium','push','Description','discord.js','load','Error\x20making\x20move:','slice','createRadialGradient','Danger','time','save','setColor','accept_game','connectfour-','edit','This\x20request\x20will\x20expire\x20in\x2060\x20seconds.','153351thMzSs','An\x20error\x20occurred\x20while\x20setting\x20up\x20the\x20game.\x20Please\x20try\x20again\x20later.','replace','message','opponent','Error\x20finding\x20best\x20move:','getUser','hard','#FFFFFF','shadowBlur','#8B0000','It\x27s\x20a\x20draw!','Could\x20not\x20create\x20game\x20board\x20canvas.','13228tUShsO','fillStyle','Error\x20ending\x20game:','Accept','setName','end','min','fillRect','Error\x20creating\x20game\x20message.','Hard','Connect\x20Four\x20Game\x20Request'];_0x23f0=function(){return _0x14fe7e;};return _0x23f0();}function createGameMessage(_0x3f3996,_0x30179a,_0x47048d,_0x54ec06,_0x49c38b){function _0x363eea(_0x1598e4,_0x19dd47){return _0x227eb8(_0x19dd47-0x3c5,_0x1598e4);}try{const _0x2505d6=_0x47048d===_0x54ec06['id']?_0x54ec06[_0x363eea(0x22e,0x1eb)]:_0x49c38b?_0x49c38b[_0x363eea(0x1ee,0x1eb)]:_0x363eea(0x21c,0x229);return lang[_0x363eea(0x299,0x26a)][_0x363eea(0x1f9,0x213)][_0x363eea(0x205,0x244)]('{user}',_0x2505d6);}catch(_0x235928){return console['error'](_0x363eea(0x230,0x264),_0x235928),_0x363eea(0x227,0x257);}}