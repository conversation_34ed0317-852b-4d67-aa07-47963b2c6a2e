const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.VoiceStateUpdate,
    async execute(oldState, newState, client) {
        try {
            // Only handle server mute changes (not self mute)
            if (oldState.serverMute === newState.serverMute) return;
            
            // Skip if user left voice channel
            if (!newState.channel) return;
            
            const isMuted = newState.serverMute;
            const action = isMuted ? 'muted' : 'unmuted';
            
            console.log(`🔇 User ${action} in voice: ${newState.member?.user.tag} in ${newState.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newState.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, isMuted ? 'VOICE_USER_MUTE' : 'VOICE_USER_UNMUTE')) return;
            
            // Create event data
            const eventData = {
                eventType: isMuted ? 'VOICE_USER_MUTE' : 'VOICE_USER_UNMUTE',
                user: 'System',
                userId: null,
                action: isMuted ? 'Thành viên bị mute voice' : 'Thành viên được unmute voice',
                details: `**${newState.member?.user.tag || 'Unknown'}** đã bị ${isMuted ? 'mute' : 'unmute'} trong voice`,
                target: newState.member?.user.tag || 'Unknown',
                channel: newState.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who muted/unmuted the user from audit logs
            try {
                const auditLogs = await newState.guild.fetchAuditLogs({
                    type: 24, // MEMBER_UPDATE
                    limit: 5
                });
                
                const auditEntry = auditLogs.entries.find(entry => 
                    entry.target?.id === newState.member?.user.id && 
                    entry.createdTimestamp > Date.now() - 10000 // Within last 10 seconds
                );
                
                if (auditEntry) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Thành viên bị ${isMuted ? 'mute' : 'unmute'} voice bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for voice mute/unmute');
            }
            
            // Add to database
            await client.db.addAuditLog(newState.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                `🔇 Thành viên ${isMuted ? 'bị mute' : 'được unmute'} voice`,
                `Vừa có thành viên ${isMuted ? 'bị mute' : 'được unmute'} trong voice channel`
            );
            
            embed.setColor(isMuted ? 0xe74c3c : 0x2ecc71); // Red for mute, green for unmute
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${newState.member?.user.tag || 'Unknown'} (${newState.member?.user || 'Unknown'})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newState.member?.user.id || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh voice',
                    value: `- ${newState.channel}`,
                    inline: true
                },
                {
                    name: `> Người ${isMuted ? 'mute' : 'unmute'}`,
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add voice channel info
            if (newState.channel) {
                embed.addFields([
                    {
                        name: '> ID kênh',
                        value: `- ${newState.channel.id}`,
                        inline: true
                    },
                    {
                        name: '> Số người trong kênh',
                        value: `- ${newState.channel.members.size} thành viên`,
                        inline: true
                    }
                ]);
                
                // Add channel type
                const channelTypes = {
                    2: 'Voice Channel',
                    13: 'Stage Channel'
                };
                
                embed.addFields({
                    name: '> Loại kênh',
                    value: `- ${channelTypes[newState.channel.type] || 'Unknown'}`,
                    inline: true
                });
            }
            
            // Add current voice state
            embed.addFields([
                {
                    name: '> Trạng thái voice hiện tại',
                    value: [
                        `• Server Mute: ${newState.serverMute ? '🔇 Có' : '🔊 Không'}`,
                        `• Self Mute: ${newState.selfMute ? '🔇 Có' : '🔊 Không'}`,
                        `• Server Deafen: ${newState.serverDeaf ? '🔇 Có' : '🔊 Không'}`,
                        `• Self Deafen: ${newState.selfDeaf ? '🔇 Có' : '🔊 Không'}`
                    ].join('\n'),
                    inline: false
                }
            ]);
            
            // Add impact explanation
            if (isMuted) {
                embed.addFields({
                    name: '> 📝 Tác động của mute',
                    value: [
                        '• Thành viên không thể nói trong voice channel',
                        '• Vẫn có thể nghe người khác',
                        '• Chỉ moderator có thể unmute',
                        '• Áp dụng cho tất cả voice channels trong server'
                    ].join('\n'),
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> 📝 Tác động của unmute',
                    value: [
                        '• Thành viên có thể nói trong voice channel',
                        '• Khôi phục quyền voice bình thường',
                        '• Có thể tự mute/unmute',
                        '• Áp dụng cho tất cả voice channels trong server'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add member info
            if (newState.member) {
                const accountAge = Date.now() - newState.member.user.createdTimestamp;
                const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
                
                embed.addFields([
                    {
                        name: '> Tuổi tài khoản',
                        value: `- ${accountAgeDays} ngày`,
                        inline: true
                    },
                    {
                        name: '> Tham gia server lúc',
                        value: `- <t:${Math.floor(newState.member.joinedTimestamp / 1000)}:F>`,
                        inline: true
                    }
                ]);
                
                // Add roles info
                const roleCount = newState.member.roles.cache.size - 1; // -1 for @everyone
                if (roleCount > 0) {
                    embed.addFields({
                        name: '> Số roles',
                        value: `- ${roleCount} roles`,
                        inline: true
                    });
                }
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set user avatar as thumbnail
            if (newState.member?.user.displayAvatarURL()) {
                embed.setThumbnail(newState.member.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newState.guild.name} • Voice User ${isMuted ? 'Mute' : 'Unmute'}`,
                iconURL: newState.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Voice user ${action} logged: ${newState.member?.user.tag}`);
            
        } catch (error) {
            console.error('Error in voiceUserMute audit log:', error);
        }
    }
};
