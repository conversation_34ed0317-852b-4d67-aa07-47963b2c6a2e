const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ThreadUpdate,
    async execute(oldThread, newThread, client) {
        try {
            // Only handle archiving (was not archived, now is)
            if (oldThread.archived || !newThread.archived) return;
            
            console.log(`📁 Thread archived: ${newThread.name} in ${newThread.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newThread.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'THREAD_ARCHIVE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'THREAD_ARCHIVE',
                user: 'System',
                userId: null,
                action: 'Thread được archive',
                details: `Thread **${newThread.name}** đã được archive`,
                target: newThread.name,
                channel: newThread.parent?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who archived the thread from audit logs
            try {
                const auditLogs = await newThread.guild.fetchAuditLogs({
                    type: 111, // THREAD_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newThread.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Thread được archive bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for thread archive');
            }
            
            // Add to database
            await client.db.addAuditLog(newThread.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📁 Thread được archive',
                `Vừa có một thread được archive trong server`
            );
            
            embed.setColor(0x95a5a6); // Gray for archive
            
            // Get thread type names
            const threadTypes = {
                10: 'Announcement Thread',
                11: 'Public Thread',
                12: 'Private Thread'
            };
            
            // Calculate thread lifetime
            const threadLifetime = Date.now() - newThread.createdTimestamp;
            const lifetimeDays = Math.floor(threadLifetime / (1000 * 60 * 60 * 24));
            const lifetimeHours = Math.floor((threadLifetime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            
            embed.addFields([
                {
                    name: '> Tên thread',
                    value: `- ${newThread.name}`,
                    inline: true
                },
                {
                    name: '> ID thread',
                    value: `- ${newThread.id}`,
                    inline: true
                },
                {
                    name: '> Loại thread',
                    value: `- ${threadTypes[newThread.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh cha',
                    value: `- ${newThread.parent || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người archive',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian archive',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Thời gian tồn tại',
                    value: `- ${lifetimeDays} ngày, ${lifetimeHours} giờ`,
                    inline: true
                },
                {
                    name: '> Số tin nhắn',
                    value: `- ${newThread.messageCount || 0} tin nhắn`,
                    inline: true
                },
                {
                    name: '> Số thành viên',
                    value: `- ${newThread.memberCount || 0} thành viên`,
                    inline: true
                }
            ]);
            
            // Add thread owner info if available
            if (newThread.ownerId) {
                try {
                    const owner = await newThread.guild.members.fetch(newThread.ownerId);
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- ${owner.user.tag}`,
                        inline: true
                    });
                } catch (error) {
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- Unknown (ID: ${newThread.ownerId})`,
                        inline: true
                    });
                }
            }
            
            // Add archive reason
            const archiveReasons = [];
            if (newThread.autoArchiveDuration) {
                archiveReasons.push(`Auto-archive sau ${newThread.autoArchiveDuration} phút không hoạt động`);
            }
            if (eventData.user !== 'System') {
                archiveReasons.push('Archive thủ công');
            }
            
            if (archiveReasons.length > 0) {
                embed.addFields({
                    name: '> Lý do archive',
                    value: `- ${archiveReasons.join(', ')}`,
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Ghi chú',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Add note about unarchiving
            embed.addFields({
                name: '> Lưu ý',
                value: '- Thread có thể được unarchive bằng cách gửi tin nhắn mới',
                inline: false
            });
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newThread.guild.name} • Thread Archive`,
                iconURL: newThread.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Thread archive logged: ${newThread.name}`);
            
        } catch (error) {
            console.error('Error in threadArchive audit log:', error);
        }
    }
};
