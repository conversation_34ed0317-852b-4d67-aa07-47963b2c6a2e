const { createSuccessEmbed, createErrorEmbed } = require('../../utils/embedBuilder.js');

module.exports = {
    async execute(interaction, client, params) {
        // params[0] = 'permission', params[1] = channelId, params[2] = roleId
        if (params[0] !== 'permission') return;
        
        const channelId = params[1];
        const roleId = params[2];
        const selectedValue = interaction.values[0];
        
        const channel = interaction.guild.channels.cache.get(channelId);
        const role = interaction.guild.roles.cache.get(roleId);
        
        if (!channel || !role) {
            const errorEmbed = createErrorEmbed(
                'Không tìm thấy!',
                'Không thể tìm thấy kênh hoặc role được chỉ định.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        try {
            let permissions = {};
            
            switch (selectedValue) {
                case 'allow_all':
                    permissions = {
                        ViewChannel: true,
                        SendMessages: true,
                        ReadMessageHistory: true,
                        AddReactions: true,
                        UseExternalEmojis: true,
                        EmbedLinks: true,
                        AttachFiles: true,
                        Connect: true,
                        Speak: true,
                        UseVAD: true
                    };
                    break;
                    
                case 'deny_all':
                    permissions = {
                        ViewChannel: false,
                        SendMessages: false,
                        ReadMessageHistory: false,
                        AddReactions: false,
                        UseExternalEmojis: false,
                        EmbedLinks: false,
                        AttachFiles: false,
                        Connect: false,
                        Speak: false,
                        UseVAD: false
                    };
                    break;
                    
                case 'view_only':
                    permissions = {
                        ViewChannel: true,
                        SendMessages: false,
                        ReadMessageHistory: true,
                        AddReactions: false,
                        Connect: true,
                        Speak: false
                    };
                    break;
                    
                case 'view_chat':
                    permissions = {
                        ViewChannel: true,
                        SendMessages: true,
                        ReadMessageHistory: true,
                        AddReactions: true,
                        UseExternalEmojis: true,
                        EmbedLinks: true,
                        AttachFiles: true
                    };
                    break;
                    
                case 'voice_basic':
                    permissions = {
                        ViewChannel: true,
                        Connect: true,
                        Speak: false,
                        UseVAD: false
                    };
                    break;
                    
                case 'voice_full':
                    permissions = {
                        ViewChannel: true,
                        Connect: true,
                        Speak: true,
                        UseVAD: true
                    };
                    break;
            }
            
            // Apply permissions
            await channel.permissionOverwrites.edit(role, permissions, {
                reason: `Quyền được thiết lập bởi ${interaction.user.tag}`
            });
            
            const permissionNames = {
                'allow_all': 'Cho phép tất cả',
                'deny_all': 'Từ chối tất cả',
                'view_only': 'Chỉ xem',
                'view_chat': 'Xem và chat',
                'voice_basic': 'Voice cơ bản',
                'voice_full': 'Voice đầy đủ'
            };
            
            const successEmbed = createSuccessEmbed(
                'Đã thiết lập quyền thành công!',
                `**Kênh:** ${channel}\n` +
                `**Role:** ${role}\n` +
                `**Quyền:** ${permissionNames[selectedValue]}\n\n` +
                `Quyền đã được áp dụng cho role trong kênh này.`
            );
            
            // Add details about what permissions were set
            const allowedPerms = Object.entries(permissions).filter(([_, value]) => value === true);
            const deniedPerms = Object.entries(permissions).filter(([_, value]) => value === false);
            
            if (allowedPerms.length > 0) {
                successEmbed.addFields({
                    name: '✅ Quyền được cho phép',
                    value: allowedPerms.map(([perm]) => `• ${getPermissionName(perm)}`).join('\n'),
                    inline: true
                });
            }
            
            if (deniedPerms.length > 0) {
                successEmbed.addFields({
                    name: '❌ Quyền bị từ chối',
                    value: deniedPerms.map(([perm]) => `• ${getPermissionName(perm)}`).join('\n'),
                    inline: true
                });
            }
            
            await interaction.update({ embeds: [successEmbed], components: [] });
            
        } catch (error) {
            console.error('Lỗi khi thiết lập quyền:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi thiết lập quyền!',
                'Không thể thiết lập quyền. Vui lòng kiểm tra quyền bot và thử lại!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    }
};

function getPermissionName(permission) {
    const names = {
        'ViewChannel': 'Xem kênh',
        'SendMessages': 'Gửi tin nhắn',
        'ReadMessageHistory': 'Đọc lịch sử tin nhắn',
        'AddReactions': 'Thêm reaction',
        'UseExternalEmojis': 'Sử dụng emoji ngoài',
        'EmbedLinks': 'Nhúng link',
        'AttachFiles': 'Đính kèm file',
        'Connect': 'Kết nối voice',
        'Speak': 'Nói trong voice',
        'UseVAD': 'Sử dụng Voice Activity'
    };
    return names[permission] || permission;
}
