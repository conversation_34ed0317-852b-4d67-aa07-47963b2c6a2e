const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'inviteUpdate', // Custom event name
    async execute(oldInvite, newInvite, client) {
        try {
            console.log(`✏️ Invite updated: ${newInvite.code} in ${newInvite.guild?.name || 'Unknown'}`);
            
            // Skip if no guild
            if (!newInvite.guild) return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newInvite.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'INVITE_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'INVITE_UPDATE',
                user: 'System',
                userId: null,
                action: 'Invite được cập nhật',
                details: `Invite **${newInvite.code}** đã được cập nhật`,
                target: newInvite.code,
                channel: newInvite.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the invite from audit logs
            try {
                const auditLogs = await newInvite.guild.fetchAuditLogs({
                    type: 41, // INVITE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.code === newInvite.code) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Invite được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for invite update');
            }
            
            // Add to database
            await client.db.addAuditLog(newInvite.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Invite được cập nhật',
                `Vừa có invite được cập nhật trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Mã invite',
                    value: `- ${newInvite.code}`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${newInvite.channel || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người cập nhật',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian cập nhật',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add invite creator info
            if (newInvite.inviter) {
                embed.addFields({
                    name: '> Người tạo invite',
                    value: `- ${newInvite.inviter.tag}`,
                    inline: true
                });
            }
            
            // Compare and show changes
            const changes = [];
            
            // Check max age changes
            if (oldInvite.maxAge !== newInvite.maxAge) {
                const formatAge = (age) => {
                    if (age === 0) return 'Không giới hạn';
                    if (age < 3600) return `${Math.floor(age / 60)} phút`;
                    if (age < 86400) return `${Math.floor(age / 3600)} giờ`;
                    return `${Math.floor(age / 86400)} ngày`;
                };
                
                changes.push({
                    name: '> Thời gian hết hạn',
                    old: formatAge(oldInvite.maxAge),
                    new: formatAge(newInvite.maxAge)
                });
            }
            
            // Check max uses changes
            if (oldInvite.maxUses !== newInvite.maxUses) {
                const formatUses = (uses) => uses === 0 ? 'Không giới hạn' : `${uses} lần`;
                
                changes.push({
                    name: '> Số lần sử dụng tối đa',
                    old: formatUses(oldInvite.maxUses),
                    new: formatUses(newInvite.maxUses)
                });
            }
            
            // Check temporary changes
            if (oldInvite.temporary !== newInvite.temporary) {
                changes.push({
                    name: '> Thành viên tạm thời',
                    old: oldInvite.temporary ? 'Có' : 'Không',
                    new: newInvite.temporary ? 'Có' : 'Không'
                });
            }
            
            // Add changes to embed
            if (changes.length > 0) {
                changes.forEach(change => {
                    embed.addFields([
                        {
                            name: `${change.name} (Cũ)`,
                            value: `- ${change.old}`,
                            inline: true
                        },
                        {
                            name: `${change.name} (Mới)`,
                            value: `- ${change.new}`,
                            inline: true
                        },
                        {
                            name: '\u200b', // Empty field for spacing
                            value: '\u200b',
                            inline: true
                        }
                    ]);
                });
            }
            
            // Add current invite stats
            embed.addFields([
                {
                    name: '> Số lần đã sử dụng',
                    value: `- ${newInvite.uses || 0} lần`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${newInvite.temporary ? 'Tạm thời' : 'Vĩnh viễn'}`,
                    inline: true
                }
            ]);
            
            // Add expiration info
            if (newInvite.expiresTimestamp) {
                embed.addFields({
                    name: '> Hết hạn lúc',
                    value: `- <t:${Math.floor(newInvite.expiresTimestamp / 1000)}:F>`,
                    inline: true
                });
            } else {
                embed.addFields({
                    name: '> Hết hạn',
                    value: `- Không bao giờ`,
                    inline: true
                });
            }
            
            // Add invite URL
            embed.addFields({
                name: '> Link invite',
                value: `- https://discord.gg/${newInvite.code}`,
                inline: false
            });
            
            // Add impact note
            if (changes.length > 0) {
                embed.addFields({
                    name: '> 📝 Tác động',
                    value: `- Các thay đổi sẽ áp dụng ngay lập tức cho invite này`,
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do cập nhật',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newInvite.guild.name} • Invite Update`,
                iconURL: newInvite.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Invite update logged: ${newInvite.code}`);
            
        } catch (error) {
            console.error('Error in inviteUpdate audit log:', error);
        }
    }
};
