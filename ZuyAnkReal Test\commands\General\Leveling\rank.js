(function(_0x2c0289,_0x35c30e){const _0x127d51=_0x2c0289();function _0xf0e87e(_0x578524,_0x38df8e){return _0x4a5e(_0x578524-0x2bd,_0x38df8e);}while(!![]){try{const _0xa62d82=-parseInt(_0xf0e87e(0x475,0x45c))/0x1+parseInt(_0xf0e87e(0x479,0x49d))/0x2+parseInt(_0xf0e87e(0x476,0x483))/0x3*(parseInt(_0xf0e87e(0x494,0x47b))/0x4)+-parseInt(_0xf0e87e(0x460,0x47a))/0x5*(-parseInt(_0xf0e87e(0x4a2,0x4b6))/0x6)+parseInt(_0xf0e87e(0x49d,0x490))/0x7*(parseInt(_0xf0e87e(0x489,0x4a2))/0x8)+-parseInt(_0xf0e87e(0x468,0x44c))/0x9+-parseInt(_0xf0e87e(0x46d,0x48e))/0xa;if(_0xa62d82===_0x35c30e)break;else _0x127d51['push'](_0x127d51['shift']());}catch(_0x35a717){_0x127d51['push'](_0x127d51['shift']());}}}(_0x46f5,0x220c9));function _0x46f5(){const _0x59ae2b=['40WAVCkK','UsernameFontWeight','LevelTextColor','setLevel','set','exports','OverlayOpacity','members','376965cIlZLg','ProgressBarStyles','followUp','RankValueColor','findIndex','3219180mbpiLe','load','RankCardBuilder','rank-card.png','resize','XPTextStyles','LevelTextSize','It\x20looks\x20like\x20you\x20don\x27t\x20have\x20any\x20level\x20data.','257566GJRJrS','3ySebdk','join','LevelTextStyles','555404bRIsyf','setRank','XPTextSize','setAvatar','setRequiredXP','UsernameStyles','UsernameColor','setOverlay','UsernameSize','setCurrentXP','ProgressBarColor','XPValueColor','General','includes','bg-[','RankTextSize','305112rRNLtf','png','backgrounds','node-fetch','presence','guild','XPNeeded','Check\x20your\x20level\x20and\x20xp','rank','toUpperCase','../../../config.yml','280988UWiwwg','text-[','has','RankTextColor','map','filter','setBackground','findOne','RankCard','49cpnqBH','displayAvatarURL','offline','XPTextColor','canvacord','109434VbvYmA','toFormat','userId','default','LevelingSystem','setDisplayName','setStyles',']\x20text-','level','user','status','RankTextStyles','setName','discord.js','setStatus','fetch','utf8'];_0x46f5=function(){return _0x59ae2b;};return _0x46f5();}const {SlashCommandBuilder,AttachmentBuilder}=require(_0x43b0bb(-0x12,-0x18)),{Font}=require(_0x43b0bb(-0x20,0x9)),canvacord=require(_0x43b0bb(-0x20,0x6)),path=require('path'),yaml=require('js-yaml'),fs=require('fs'),UserData=require('../../../models/UserData'),config=yaml[_0x43b0bb(-0x53,-0x71)](fs['readFileSync'](path['join'](__dirname,_0x43b0bb(-0x2e,-0xe)),_0x43b0bb(-0x62,-0x4a)));function _0x4a5e(_0xf9ba3a,_0x1611f5){const _0x46f583=_0x46f5();return _0x4a5e=function(_0x4a5e30,_0x3a5c67){_0x4a5e30=_0x4a5e30-0x1a2;let _0xb146f7=_0x46f583[_0x4a5e30];return _0xb146f7;},_0x4a5e(_0xf9ba3a,_0x1611f5);}function _0x43b0bb(_0x14bb91,_0x4b1521){return _0x4a5e(_0x14bb91- -0x204,_0x4b1521);}const sharp=require('sharp'),rankCardConfig=config[_0x43b0bb(-0x25,-0xc)],avatarCache=new Map();async function fetchAndConvertAvatar(_0x13b881){if(avatarCache[_0x167d83(0x520,0x511)](_0x13b881))return avatarCache['get'](_0x13b881);function _0x167d83(_0x35d27d,_0x2abc18){return _0x43b0bb(_0x35d27d-0x54b,_0x2abc18);}const _0x550885=(await import(_0x167d83(0x516,0x4fc)))[_0x167d83(0x52f,0x53e)],_0x47d391=await _0x550885(_0x13b881),_0x256479=await _0x47d391['buffer'](),_0x20702c=await sharp(_0x256479)[_0x167d83(0x4fb,0x4ee)](0x80,0x80)[_0x167d83(0x52d,0x52c)]('png')['toBuffer']();return avatarCache[_0x167d83(0x4ee,0x514)](_0x13b881,_0x20702c),_0x20702c;}async function generateRankCard(_0x29d79e,_0x5ccffc){const _0x1a1eb0=await _0x29d79e[_0x4e55c2(-0x5e,-0x72)][_0x4e55c2(-0x85,-0xa4)][_0x4e55c2(-0x3b,-0x37)](_0x29d79e[_0x4e55c2(-0x41,-0x19)]['id']),_0x41e0a9=_0x1a1eb0[_0x4e55c2(-0x5f,-0x70)]?_0x1a1eb0[_0x4e55c2(-0x5f,-0x5c)][_0x4e55c2(-0x40,-0x31)]:_0x4e55c2(-0x4d,-0x48);function _0x4e55c2(_0x399d1d,_0x697b2e){return _0x43b0bb(_0x399d1d- -0x2b,_0x697b2e);}const _0x3cdf58=await UserData['find']({'guildId':_0x29d79e['guild']['id']})['sort']({'level':-0x1,'xp':-0x1}),_0x2aa4e3=await _0x29d79e[_0x4e55c2(-0x5e,-0x62)]['members']['fetch'](),_0x50c3cf=_0x2aa4e3[_0x4e55c2(-0x54,-0x2e)](_0xae48ea=>_0xae48ea['id']),_0x39b1f2=_0x3cdf58[_0x4e55c2(-0x53,-0x57)](_0x104090=>_0x50c3cf[_0x4e55c2(-0x66,-0x4c)](_0x104090['userId'])),_0x66be27=_0x39b1f2[_0x4e55c2(-0x80,-0xa6)](_0x464763=>_0x464763['userId']===_0x5ccffc[_0x4e55c2(-0x48,-0x38)])+0x1,_0x5a23d0=_0x29d79e[_0x4e55c2(-0x41,-0x46)][_0x4e55c2(-0x4e,-0x62)]({'format':'gif','dynamic':!![],'size':0x400}),_0x5c33d5=await fetchAndConvertAvatar(_0x5a23d0),_0x57d9ef=(_0x5ccffc[_0x4e55c2(-0x42,-0x59)]+0x1)*config[_0x4e55c2(-0x46,-0x2d)][_0x4e55c2(-0x5d,-0x5a)],_0x556eb0=path[_0x4e55c2(-0x75,-0x60)](__dirname,_0x4e55c2(-0x61,-0x4f),rankCardConfig['Background']),_0xc76dc6=new canvacord[(_0x4e55c2(-0x7d,-0x67))]()[_0x4e55c2(-0x52,-0x36)](_0x556eb0)[_0x4e55c2(-0x70,-0x8a)](_0x5c33d5)[_0x4e55c2(-0x45,-0x4a)](_0x29d79e[_0x4e55c2(-0x41,-0x30)]['username'][_0x4e55c2(-0x5a,-0x4a)]())[_0x4e55c2(-0x6a,-0x6e)](_0x5ccffc['xp'])[_0x4e55c2(-0x6f,-0x65)](_0x57d9ef)[_0x4e55c2(-0x89,-0x69)](_0x5ccffc['level'])[_0x4e55c2(-0x72,-0x74)](_0x66be27)[_0x4e55c2(-0x3c,-0x54)](_0x41e0a9)[_0x4e55c2(-0x6c,-0x8e)](rankCardConfig[_0x4e55c2(-0x86,-0x6c)])[_0x4e55c2(-0x44,-0x1a)]({'username':{'name':{'className':_0x4e55c2(-0x57,-0x59)+rankCardConfig[_0x4e55c2(-0x6e,-0x5a)][_0x4e55c2(-0x6d,-0x65)]+_0x4e55c2(-0x43,-0x67)+rankCardConfig[_0x4e55c2(-0x6e,-0x4a)][_0x4e55c2(-0x6b,-0x54)]+'\x20'+rankCardConfig['UsernameStyles'][_0x4e55c2(-0x8b,-0x83)]}},'statistics':{'level':{'text':{'className':_0x4e55c2(-0x57,-0x5a)+rankCardConfig['LevelTextStyles'][_0x4e55c2(-0x8a,-0x8b)]+']\x20text-'+rankCardConfig[_0x4e55c2(-0x74,-0x8b)][_0x4e55c2(-0x79,-0x79)]},'value':{'className':_0x4e55c2(-0x57,-0x7a)+rankCardConfig[_0x4e55c2(-0x74,-0x82)]['LevelValueColor']+']\x20text-'+rankCardConfig[_0x4e55c2(-0x74,-0x57)][_0x4e55c2(-0x79,-0xa3)]}},'rank':{'text':{'className':_0x4e55c2(-0x57,-0x4d)+rankCardConfig[_0x4e55c2(-0x3f,-0x1f)][_0x4e55c2(-0x55,-0x73)]+_0x4e55c2(-0x43,-0x1d)+rankCardConfig[_0x4e55c2(-0x3f,-0x1b)]['RankTextSize']},'value':{'className':_0x4e55c2(-0x57,-0x72)+rankCardConfig['RankTextStyles'][_0x4e55c2(-0x81,-0x80)]+_0x4e55c2(-0x43,-0x61)+rankCardConfig[_0x4e55c2(-0x3f,-0x49)][_0x4e55c2(-0x64,-0x44)]}},'xp':{'text':{'className':_0x4e55c2(-0x57,-0x78)+rankCardConfig['XPTextStyles'][_0x4e55c2(-0x4c,-0x3f)]+_0x4e55c2(-0x43,-0x53)+rankCardConfig[_0x4e55c2(-0x7a,-0x84)][_0x4e55c2(-0x71,-0x99)]},'value':{'className':_0x4e55c2(-0x57,-0x60)+rankCardConfig[_0x4e55c2(-0x7a,-0x66)][_0x4e55c2(-0x68,-0x8a)]+_0x4e55c2(-0x43,-0x2e)+rankCardConfig[_0x4e55c2(-0x7a,-0xa2)]['XPTextSize']}}},'progressbar':{'track':{'className':_0x4e55c2(-0x65,-0x53)+rankCardConfig[_0x4e55c2(-0x83,-0x77)][_0x4e55c2(-0x69,-0x8a)]+']'}}}),_0x22023e=await _0xc76dc6['build']({'format':_0x4e55c2(-0x62,-0x41)});return _0x22023e;}module[_0x43b0bb(-0x5c,-0x71)]={'data':new SlashCommandBuilder()[_0x43b0bb(-0x13,-0x18)](_0x43b0bb(-0x30,-0x58))['setDescription'](_0x43b0bb(-0x31,-0x25)),'category':_0x43b0bb(-0x3c,-0x47),async 'execute'(_0x2a97ba){function _0x5e259b(_0x3c67ca,_0x78dfef){return _0x43b0bb(_0x78dfef-0x5e6,_0x3c67ca);}await _0x2a97ba['deferReply']();const _0x188afd=await UserData[_0x5e259b(0x5bb,0x5c0)]({'userId':_0x2a97ba[_0x5e259b(0x5c0,0x5d0)]['id'],'guildId':_0x2a97ba[_0x5e259b(0x5d6,0x5b3)]['id']});if(!_0x188afd)return _0x2a97ba[_0x5e259b(0x56c,0x58f)]({'content':_0x5e259b(0x590,0x599),'ephemeral':!![]});const _0x3930f1=await generateRankCard(_0x2a97ba,_0x188afd),_0x28d59d=new AttachmentBuilder(_0x3930f1,{'name':_0x5e259b(0x57c,0x595)});await _0x2a97ba['followUp']({'files':[_0x28d59d]});}};