const { EmbedBuilder } = require('discord.js');

let restartData = {
    channelId: null,
    messageId: null,
    time: null
};

module.exports = {
    name: 'restartHand<PERSON>',
    setRestartData: (channelId, messageId) => {
        restartData = {
            channelId,
            messageId,
            time: Date.now()
        };
    },
    execute: async (client) => {
        try {
            // Check karo ki 5 minute ke andar restart hua hai
            if (restartData.channelId && restartData.time && (Date.now() - restartData.time) < 300000) {
                const channel = await client.channels.fetch(restartData.channelId);
                
                if (channel) {
                    try {
                        // Purani message ko edit karo
                        const originalMessage = await channel.messages.fetch(restartData.messageId);
                        const embed = new EmbedBuilder()
                            .setColor('#00FF00')
                            .setDescription('✅ **Bot phir se chal gaya!**');
                        
                        await originalMessage.edit({ embeds: [embed] });
                        return;
                    } catch (error) {
                        console.log('Purani message edit nahi hui, naya bhej raha hun');
                    }
                    
                    // Naya message bhejo
                    const embed = new EmbedBuilder()
                        .setColor('#00FF00')
                        .setDescription('✅ **Bot phir se chal gaya!**');
                    
                    await channel.send({ embeds: [embed] });
                }
            }
        } catch (error) {
            console.error('Restart handler error:', error);
        }
    }
};