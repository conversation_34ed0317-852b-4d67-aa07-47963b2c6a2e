const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'userinfo',
    aliases: ['ui', 'whois'], // Added aliases
    description: 'Get detailed information about a user',
    usage: '!userinfo [@user|userID]',
    async execute(message, args) {
        // Get the target user
        let targetUser;
        
        if (args.length > 0) {
            // Try to find mentioned user
            const mention = args[0].match(/^<@!?(\d+)>$/);
            const userId = mention ? mention[1] : args[0];
            
            try {
                targetUser = await message.client.users.fetch(userId);
            } catch (error) {
                return message.reply('Could not find that user.');
            }
        } else {
            targetUser = message.author;
        }

        try {
            // Fetch the user with all data
            const user = await message.client.users.fetch(targetUser.id, { force: true });
            const member = message.guild?.members.cache.get(user.id);
            
            // Get banner URL if available
            const bannerURL = user.bannerURL({ size: 4096, dynamic: true });
            
            // Format dates
            const formatDate = (date) => {
                return {
                    date: date.toLocaleDateString('en-US', { 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                    }),
                    time: date.toLocaleTimeString('en-US', { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                    })
                };
            };

            // Create embed
            const embed = new EmbedBuilder()
                .setColor(member?.displayHexColor || '#0099ff')
                .setTitle(`${user.username}'s Information`)
                .setThumbnail(user.displayAvatarURL({ size: 4096, dynamic: true }))
                .addFields(
                    { name: '👤 Username', value: `${user.tag} ${user.bot ? '🤖' : ''}`, inline: true },
                    { name: '🆔 User ID', value: user.id, inline: true },
                    { name: '📅 Account Created', 
                      value: `${formatDate(user.createdAt).date}\n${formatDate(user.createdAt).time}`, 
                      inline: true }
                );

            // Add banner if available
            if (bannerURL) {
                embed.setImage(bannerURL)
                     .addFields({ name: '🖼️ Banner', value: `[View Full Banner](${bannerURL})`, inline: false });
            }

            // Add server-specific information if available
            if (member) {
                const roles = member.roles.cache
                    .filter(role => role.id !== message.guild.id)
                    .sort((a, b) => b.position - a.position)
                    .map(role => role.toString())
                    .slice(0, 5)
                    .join(', ');

                embed.addFields(
                    { name: '🏷️ Server Nickname', value: member.nickname || 'None', inline: true },
                    { name: '📥 Joined Server', 
                      value: `${formatDate(member.joinedAt).date}\n${formatDate(member.joinedAt).time}`, 
                      inline: true },
                    { name: `🎭 Roles (${member.roles.cache.size - 1})`, 
                      value: roles || 'None', 
                      inline: false },
                    { name: '🔝 Highest Role', value: member.roles.highest.toString(), inline: true }
                );
            }

            // Add status information
            if (member?.presence) {
                const status = member.presence.status;
                const activities = member.presence.activities;
                
                let statusText = '';
                switch(status) {
                    case 'online': statusText = '🟢 Online'; break;
                    case 'idle': statusText = '🟡 Idle'; break;
                    case 'dnd': statusText = '🔴 Do Not Disturb'; break;
                    case 'offline': statusText = '⚫ Offline'; break;
                }

                if (activities.length > 0) {
                    const activity = activities[0];
                    statusText += `\n🎮 ${activity.type === 'CUSTOM_STATUS' ? 'Custom Status' : activity.type} - ${activity.name}`;
                    if (activity.details) statusText += `\n📝 ${activity.details}`;
                }

                embed.addFields({ name: '📶 Status', value: statusText, inline: false });
            }

            // Add footer with requested by info
            embed.setFooter({ 
                text: `Requested by ${message.author.username}`, 
                iconURL: message.author.displayAvatarURL() 
            })
            .setTimestamp();

            message.channel.send({ embeds: [embed] });
            
        } catch (error) {
            console.error(error);
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setDescription('There was an error fetching user information.');
            message.channel.send({ embeds: [errorEmbed] });
        }
    },
};