# 🔍 <PERSON><PERSON> thống Audit Log - Hướng dẫn sử dụng

## 📋 Tổng quan

Hệ thống Audit Log của Server Setup Bot là một giải pháp toàn diện để theo dõi và ghi lại mọi hoạt động trong Discord server của bạn. Với hơn **150 loại events** được hỗ trợ và tính năng **Smart Alerts** thông minh, bạn sẽ luôn nắm bắt được mọi thay đổi quan trọng.

## 🚀 Bắt đầu nhanh

### 1. Thiết lập cơ bản
```
/auditlog setup channel:#logs events:all
```

### 2. Xem cài đặt hiện tại
```
/auditlog settings
```

### 3. Xuất dữ liệu logs
```
/auditlog export format:json time_range:7days
```

## 📝 Các lệnh chi tiết

### `/auditlog setup`
Thiết lập hệ thống audit log cho server

**Tham số:**
- `channel` (bắt buộc): Kênh để gửi audit logs
- `events` (tùy chọn): Loại events cần theo dõi
  - `all` - Tất cả events (mặc định)
  - `moderation` - Chỉ moderation events
  - `server` - Chỉ server events  
  - `channels` - Chỉ channel events
  - `users` - Chỉ user events
  - `custom` - Tùy chỉnh

**Ví dụ:**
```
/auditlog setup channel:#audit-logs events:all
/auditlog setup channel:#mod-logs events:moderation
```

### `/auditlog remove`
Xóa audit log khỏi kênh

**Tham số:**
- `channel` (bắt buộc): Kênh cần xóa audit log

**Ví dụ:**
```
/auditlog remove channel:#old-logs
```

### `/auditlog settings`
Xem và chỉnh sửa cài đặt audit log

**Tính năng:**
- 📍 Thay đổi kênh log
- 📋 Chỉnh sửa events
- 🔔 Cài đặt Smart Alerts
- 🚫 Bỏ qua roles/users

### `/auditlog export`
Xuất audit logs ra file

**Tham số:**
- `format` (tùy chọn): Định dạng file
  - `json` - JSON format (mặc định)
  - `csv` - CSV format
  - `txt` - Text format
- `time_range` (tùy chọn): Khoảng thời gian
  - `1day` - 24 giờ qua
  - `7days` - 7 ngày qua (mặc định)
  - `30days` - 30 ngày qua
  - `all` - Tất cả thời gian

**Ví dụ:**
```
/auditlog export format:csv time_range:30days
/auditlog export format:json time_range:all
```

## 🎯 Các loại Events được hỗ trợ

### 📱 Applications (3 events)
- App add/remove
- App command permission update

### 📝 Channels (25 events)
- Channel create/delete/update
- Permission changes
- Topic/Name updates
- Slowmode, NSFW, Parent changes
- Forum tags, layout updates
- Voice status updates

### 🤖 Discord AutoMod (9 events)
- Rule create/delete/toggle
- Rule name/actions/content updates
- Roles/channels/whitelist updates

### 😀 Emoji (4 events)
- Emoji create/delete
- Name/roles updates

### 📅 Events (12 events)
- Event create/delete
- Location/description/name updates
- Privacy/time/status/image updates
- User subscribe/unsubscribe

### 🔗 Invites (3 events)
- Invite create/delete/post

### 💬 Messages (4 events)
- Message delete/bulk delete
- Message edit/publish
- Command usage

### 📊 Polls (5 events)
- Poll create/delete/finalize
- Vote add/remove

### 🎭 Roles (7 events)
- Role create/delete
- Color/hoist/mentionable updates
- Name/permissions/icon updates

### 🎤 Stage (4 events)
- Stage start/end
- Topic/privacy updates

### 🏰 Server (25 events)
- Ban/unban, User join/leave/kick
- Member prune, AFK settings
- Server settings (name, icon, banner)
- Boost level, verification level
- Features, widget, vanity URL
- Onboarding settings

### 🎨 Stickers (5 events)
- Sticker create/delete
- Name/description/emoji updates

### 🔊 Soundboard (5 events)
- Sound upload/delete
- Name/volume/emoji updates

### 🧵 Threads (9 events)
- Thread create/delete
- Name/slowmode updates
- Archive/unarchive/lock/unlock

### 👥 Users (7 events)
- Username/avatar updates
- Role changes (add/remove)
- Timeouts

### 🎵 Voice (6 events)
- Channel full
- User join/switch/leave/move/kick

### 🔗 Webhooks (5 events)
- Webhook create/delete
- Avatar/name/channel updates

### 🔨 Moderation (15 events)
- Auto moderation
- Ban/unban, Kick, Mute/unmute
- Warn/unwarn, Cases
- Reports, User notes

## 🔔 Smart Alerts

### Tính năng thông minh
- **Phát hiện hành vi bất thường** dựa trên ngưỡng cài đặt
- **Gửi cảnh báo DM** cho owner server
- **Ghi log chi tiết** về incidents
- **Đề xuất hành động** khắc phục

### Các loại cảnh báo
- **Mass Message Delete**: 10+ tin nhắn xóa trong 5 phút
- **Mass Role Grant**: 5+ roles cấp trong 10 phút  
- **Mass Channel Create**: 3+ kênh tạo trong 5 phút
- **Mass Member Kick**: 5+ kicks trong 10 phút
- **Mass Member Ban**: 3+ bans trong 10 phút

### Mức độ cảnh báo
- 🟢 **LOW**: Thông tin
- 🟡 **MEDIUM**: Cần chú ý
- 🟠 **HIGH**: Nguy hiểm
- 🔴 **CRITICAL**: Khẩn cấp

## ⚙️ Cài đặt nâng cao

### Bỏ qua Roles/Users
- Loại trừ hoạt động của roles/users cụ thể
- Hữu ích cho bots hoặc admin
- Owner server luôn được ghi log

### Tùy chỉnh ngưỡng cảnh báo
- Điều chỉnh sensitivity của Smart Alerts
- Phù hợp với quy mô server
- Tránh false positives

### Xuất dữ liệu
- **JSON**: Dữ liệu có cấu trúc, dễ phân tích
- **CSV**: Import vào Excel/Google Sheets
- **TXT**: Đọc trực tiếp, dễ hiểu

## 🛡️ Bảo mật & Quyền riêng tư

### Quyền hạn cần thiết
- **Manage Server**: Để sử dụng lệnh audit log
- **Send Messages**: Bot gửi logs vào kênh
- **Embed Links**: Hiển thị embeds đẹp mắt
- **Read Message History**: Đọc audit logs Discord

### Lưu trữ dữ liệu
- Logs được lưu local trên server bot
- Không chia sẻ với bên thứ ba
- Tự động xóa logs cũ (>10,000 entries/server)
- Có thể xuất và backup bất cứ lúc nào

## 🔧 Troubleshooting

### Bot không ghi logs
1. Kiểm tra quyền bot trong kênh audit log
2. Đảm bảo events được bật đúng loại
3. Kiểm tra user/role có bị ignore không

### Smart Alerts không hoạt động
1. Kiểm tra Smart Alerts đã được bật
2. Xem ngưỡng cảnh báo có phù hợp không
3. Đảm bảo bot có thể DM owner

### Không xuất được file
1. Kiểm tra có logs trong khoảng thời gian không
2. Thử format khác (JSON thay vì CSV)
3. Giảm time range nếu quá nhiều logs

## 📞 Hỗ trợ

Nếu gặp vấn đề hoặc cần hỗ trợ:
1. Sử dụng `/help` để xem hướng dẫn
2. Kiểm tra logs console của bot
3. Liên hệ admin server hoặc developer

---

**Server Setup Bot** - Hệ thống audit log toàn diện cho Discord server của bạn! 🚀
