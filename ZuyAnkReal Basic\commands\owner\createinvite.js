const { PermissionsBitField, ChannelType } = require('discord.js');
const config = require('../../config.json');

module.exports = {
    name: 'createinvite',
    description: 'Creates a SERVER invite link (Owner Only) - Text channels only',
    usage: 'createinvite <guildID> [maxUses] [expireTime]',
    async execute(message, args) {
        if (message.author.id !== config.ownerID) {
            return message.reply('❌ Owner only command.');
        }

        if (!args[0]) return message.reply('❌ Please provide a server ID.');

        const guildId = args[0];
        const maxUses = args[1] ? parseInt(args[1]) : 0;
        const expireTime = args[2] ? parseInt(args[2]) : 0;

        try {
            const guild = message.client.guilds.cache.get(guildId);
            if (!guild) return message.reply('❌ Bot is not in that server.');

            // Find only TEXT channels (no voice channels)
            const textChannels = guild.channels.cache.filter(channel => {
                return channel.type === ChannelType.GuildText && 
                       channel.permissionsFor(guild.members.me).has([
                           PermissionsBitField.Flags.ViewChannel,
                           PermissionsBitField.Flags.CreateInstantInvite
                       ]);
            });

            if (textChannels.size === 0) {
                return message.reply(`
❌ No suitable TEXT channels found in ${guild.name}.

**Requirements:**
- Must be a text channel (no voice channels)
- \`View Channel\` permission
- \`Create Instant Invite\` permission

**Troubleshooting:**
1. Check bot permissions in text channels
2. Try a different text channel
3. Make sure bot role is high enough
                `);
            }

            // Select the default channel or first available text channel
            const channel = guild.rulesChannel || guild.publicUpdatesChannel || textChannels.first();
            
            const invite = await channel.createInvite({
                maxUses: maxUses || undefined,
                maxAge: expireTime || undefined,
                unique: true,
                reason: `Server invite generated by owner ${message.author.tag}`
            });

            await message.author.send(`
🎟️ **SERVER INVITE LINK** 🎟️
🔗 https://discord.gg/${invite.code}
🏠 Server: ${guild.name}
📊 Max uses: ${maxUses || 'Unlimited'}
⏳ Expires: ${expireTime ? formatTime(expireTime) : 'Never'}

⚠️ This is a SERVER invite, not VC-specific
            `);

            if (message.deletable) await message.delete();
            return message.channel.send('✅ Server invite link sent to your DMs!')
                .then(msg => setTimeout(() => msg.delete(), 5000));

        } catch (error) {
            console.error('Server Invite Error:', error);
            return message.reply(`
❌ Failed to create SERVER invite: ${error.message}

Make sure:
1. Bot has proper permissions
2. Channel exists and is accessible
3. Bot role is high enough in hierarchy
            `);
        }
    }
};

function formatTime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    
    return [
        days > 0 ? `${days} day${days !== 1 ? 's' : ''}` : null,
        hours > 0 ? `${hours} hour${hours !== 1 ? 's' : ''}` : null,
        mins > 0 ? `${mins} minute${mins !== 1 ? 's' : ''}` : null
    ].filter(Boolean).join(' ') || 'less than 1 minute';
}