const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ThreadUpdate,
    async execute(oldThread, newThread, client) {
        try {
            // Only handle auto archive duration changes
            if (oldThread.autoArchiveDuration === newThread.autoArchiveDuration) return;
            
            console.log(`⏰ Thread archive duration updated: ${newThread.name} in ${newThread.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newThread.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'THREAD_ARCHIVE_DURATION_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'THREAD_ARCHIVE_DURATION_UPDATE',
                user: 'System',
                userId: null,
                action: 'Thời gian archive thread đư<PERSON><PERSON> cập nhật',
                details: `Thời gian archive của thread **${newThread.name}** đã được thay đổi từ ${oldThread.autoArchiveDuration} phút thành ${newThread.autoArchiveDuration} phút`,
                target: newThread.name,
                channel: newThread.parent?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the archive duration from audit logs
            try {
                const auditLogs = await newThread.guild.fetchAuditLogs({
                    type: 111, // THREAD_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newThread.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Thời gian archive thread được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for thread archive duration update');
            }
            
            // Add to database
            await client.db.addAuditLog(newThread.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '⏰ Thời gian archive thread cập nhật',
                `Vừa có thời gian archive thread được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Format duration display
            const formatDuration = (minutes) => {
                if (minutes < 60) return `${minutes} phút`;
                if (minutes < 1440) return `${Math.floor(minutes / 60)} giờ`;
                if (minutes < 10080) return `${Math.floor(minutes / 1440)} ngày`;
                return `${Math.floor(minutes / 10080)} tuần`;
            };
            
            // Get thread type names
            const threadTypes = {
                10: 'Announcement Thread',
                11: 'Public Thread',
                12: 'Private Thread'
            };
            
            embed.addFields([
                {
                    name: '> Tên thread',
                    value: `- ${newThread.name}`,
                    inline: true
                },
                {
                    name: '> ID thread',
                    value: `- ${newThread.id}`,
                    inline: true
                },
                {
                    name: '> Loại thread',
                    value: `- ${threadTypes[newThread.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh cha',
                    value: `- ${newThread.parent || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Thời gian archive cũ',
                    value: `- ${formatDuration(oldThread.autoArchiveDuration)}`,
                    inline: true
                },
                {
                    name: '> Thời gian archive mới',
                    value: `- ${formatDuration(newThread.autoArchiveDuration)}`,
                    inline: true
                },
                {
                    name: '> Số thành viên',
                    value: `- ${newThread.memberCount || 0} thành viên`,
                    inline: true
                }
            ]);
            
            // Add thread owner info if available
            if (newThread.ownerId) {
                try {
                    const owner = await newThread.guild.members.fetch(newThread.ownerId);
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- ${owner.user.tag}`,
                        inline: true
                    });
                } catch (error) {
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- Unknown (ID: ${newThread.ownerId})`,
                        inline: true
                    });
                }
            }
            
            // Add current thread status
            embed.addFields([
                {
                    name: '> Trạng thái hiện tại',
                    value: `- ${newThread.archived ? '📁 Archived' : '🔓 Active'}`,
                    inline: true
                },
                {
                    name: '> Bị khóa',
                    value: `- ${newThread.locked ? '🔒 Có' : '🔓 Không'}`,
                    inline: true
                }
            ]);
            
            // Add impact explanation
            if (newThread.autoArchiveDuration > oldThread.autoArchiveDuration) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Thread sẽ tồn tại lâu hơn khi không hoạt động',
                        '• Giảm nguy cơ bị archive sớm',
                        '• Phù hợp cho thảo luận dài hạn',
                        '• Tăng thời gian để thành viên phản hồi'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for longer duration
            } else if (newThread.autoArchiveDuration < oldThread.autoArchiveDuration) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Thread sẽ bị archive nhanh hơn khi không hoạt động',
                        '• Giúp giữ server gọn gàng',
                        '• Phù hợp cho thảo luận ngắn hạn',
                        '• Cần hoạt động thường xuyên để duy trì'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0xe67e22); // Orange-red for shorter duration
            }
            
            // Add archive duration options explanation
            const durationOptions = {
                60: '1 giờ - Cho thảo luận nhanh',
                1440: '1 ngày - Cho thảo luận thông thường',
                4320: '3 ngày - Cho thảo luận quan trọng',
                10080: '1 tuần - Cho dự án dài hạn'
            };
            
            embed.addFields({
                name: '> 💡 Các tùy chọn thời gian archive',
                value: Object.entries(durationOptions).map(([minutes, desc]) => `• ${desc}`).join('\n'),
                inline: false
            });
            
            // Add thread link
            embed.addFields({
                name: '> Link thread',
                value: `- [Nhấn để xem](https://discord.com/channels/${newThread.guild.id}/${newThread.id})`,
                inline: true
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newThread.guild.name} • Thread Archive Duration Update`,
                iconURL: newThread.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Thread archive duration update logged: ${newThread.name}`);
            
        } catch (error) {
            console.error('Error in threadArchiveDurationUpdate audit log:', error);
        }
    }
};
