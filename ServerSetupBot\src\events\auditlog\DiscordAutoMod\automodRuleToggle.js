const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.AutoModerationRuleUpdate,
    async execute(oldRule, newRule, client) {
        try {
            // Only handle enable/disable changes
            if (oldRule.enabled === newRule.enabled) return;
            
            console.log(`🔄 AutoMod rule toggled: ${newRule.name} in ${newRule.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newRule.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'AUTOMOD_RULE_TOGGLE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'AUTOMOD_RULE_TOGGLE',
                user: 'System',
                userId: null,
                action: `Quy tắc AutoMod được ${newRule.enabled ? 'bật' : 'tắt'}`,
                details: `Quy tắc AutoMod **${newRule.name}** đã được ${newRule.enabled ? 'bật' : 'tắt'}`,
                target: newRule.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who toggled the rule from audit logs
            try {
                const auditLogs = await newRule.guild.fetchAuditLogs({
                    type: 141, // AUTO_MODERATION_RULE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newRule.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Quy tắc AutoMod được ${newRule.enabled ? 'bật' : 'tắt'} bởi ${auditEntry.executor.tag}`;
                }
            } catch (error) {
                console.log('Could not fetch audit logs for automod rule toggle');
            }
            
            // Add to database
            await client.db.addAuditLog(newRule.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                `🔄 Quy tắc AutoMod được ${newRule.enabled ? 'bật' : 'tắt'}`,
                `Vừa có quy tắc AutoMod được ${newRule.enabled ? 'bật' : 'tắt'} trong server`
            );
            
            embed.setColor(newRule.enabled ? 0x2ecc71 : 0xe74c3c); // Green if enabled, red if disabled
            
            // Get trigger type names
            const triggerTypes = {
                1: 'Keyword',
                2: 'Harmful Link',
                3: 'Spam',
                4: 'Keyword Preset',
                5: 'Mention Spam'
            };
            
            embed.addFields([
                {
                    name: '> Tên quy tắc',
                    value: `- ${newRule.name}`,
                    inline: true
                },
                {
                    name: '> ID quy tắc',
                    value: `- ${newRule.id}`,
                    inline: true
                },
                {
                    name: '> Loại trigger',
                    value: `- ${triggerTypes[newRule.triggerType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Trạng thái cũ',
                    value: `- ${oldRule.enabled ? '✅ Bật' : '❌ Tắt'}`,
                    inline: true
                },
                {
                    name: '> Trạng thái mới',
                    value: `- ${newRule.enabled ? '✅ Bật' : '❌ Tắt'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add actions info
            if (newRule.actions && newRule.actions.length > 0) {
                const actionTypes = {
                    1: 'Block Message',
                    2: 'Send Alert Message',
                    3: 'Timeout User'
                };
                
                const actionsList = newRule.actions.map(action => 
                    actionTypes[action.type] || 'Unknown Action'
                ).join(', ');
                
                embed.addFields({
                    name: '> Hành động',
                    value: `- ${actionsList}`,
                    inline: false
                });
            }
            
            // Add impact message
            if (newRule.enabled) {
                embed.addFields({
                    name: '✅ Tác động',
                    value: '- Quy tắc này hiện đang bảo vệ server',
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '⚠️ Cảnh báo',
                    value: '- Quy tắc này đã bị tắt và không còn bảo vệ server',
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newRule.guild.name} • AutoMod Rule Toggle`,
                iconURL: newRule.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ AutoMod rule toggle logged: ${newRule.name}`);
            
        } catch (error) {
            console.error('Error in automodRuleToggle audit log:', error);
        }
    }
};
