# 🔧 Troubleshooting Guide

## ❌ Slash Commands không hiển thị

### Nguyên nhân thường gặp:
1. **Commands chưa được deploy**
2. **Bot thiếu quyền applications.commands**
3. **Token hoặc Client ID sai**
4. **Cache Discord chưa cập nhật**

### Giải pháp:

#### 1. Kiểm tra file .env
```bash
# Đảm bảo file .env có đầy đủ thông tin
DISCORD_TOKEN=your_bot_token_here
CLIENT_ID=your_bot_client_id_here
```

#### 2. Deploy commands thủ công
```bash
npm run deploy
```

#### 3. Kiểm tra quyền bot
- Bot cần quyền `applications.commands`
- Mời lại bot với link đúng:
```
https://discord.com/api/oauth2/authorize?client_id=YOUR_CLIENT_ID&permissions=8&scope=bot%20applications.commands
```

#### 4. X<PERSON>a cache Discord
- Restart Discord client
- Hoặc Ctrl+Shift+R để reload

---

## 🔑 Lỗi Token/Authentication

### Lỗi: "Invalid token"
```
❌ Lỗi: Token bot không hợp lệ
```

**Giải pháp:**
1. Kiểm tra token trong Discord Developer Portal
2. Regenerate token nếu cần
3. Cập nhật file .env với token mới

### Lỗi: "Missing Access"
```
❌ Lỗi: Bot không có quyền truy cập
```

**Giải pháp:**
1. Kiểm tra bot có trong server không
2. Kiểm tra quyền bot trong server
3. Mời lại bot với quyền đầy đủ

---

## 💾 Lỗi Database

### Lỗi: "Database locked"
```
❌ SQLITE_BUSY: database is locked
```

**Giải pháp:**
1. Đóng tất cả instance bot đang chạy
2. Xóa file lock nếu có: `rm data/serversetup.db-wal`
3. Khởi động lại bot

### Lỗi: "No such table"
```
❌ SQLITE_ERROR: no such table: server_configs
```

**Giải pháp:**
1. Xóa database cũ: `rm data/serversetup.db`
2. Khởi động lại bot để tạo database mới

---

## 🚀 Lỗi khởi động

### Lỗi: "Cannot find module"
```
❌ Error: Cannot find module 'discord.js'
```

**Giải pháp:**
```bash
npm install
```

### Lỗi: "Port already in use"
```
❌ Error: listen EADDRINUSE :::3000
```

**Giải pháp:**
1. Tìm process đang dùng port: `netstat -ano | findstr :3000`
2. Kill process: `taskkill /PID <PID> /F`
3. Hoặc đổi port trong config

---

## 🔧 Lỗi Commands

### Commands không hoạt động
1. **Kiểm tra quyền user:**
   - User có quyền sử dụng command không?
   - Command có yêu cầu admin không?

2. **Kiểm tra quyền bot:**
   - Bot có quyền thực hiện hành động không?
   - Ví dụ: Manage Roles để gán role

3. **Kiểm tra logs:**
   ```bash
   npm start
   # Xem console để tìm lỗi
   ```

### Lỗi: "Missing Permissions"
```
❌ Bot thiếu quyền!
```

**Giải pháp:**
1. Cấp quyền cần thiết cho bot
2. Hoặc mời lại với quyền Administrator

---

## 📱 Lỗi Slash Commands

### Commands hiển thị nhưng không chạy
1. **Restart Discord client**
2. **Kiểm tra bot online:**
   ```bash
   npm start
   ```
3. **Xem logs lỗi trong console**

### Commands bị duplicate
1. **Clear commands cũ:**
   ```bash
   # Sửa deployCommands.js để clear commands
   npm run deploy
   ```

---

## 🛠️ Debug Mode

### Bật debug để xem chi tiết lỗi:

1. **Sửa file .env:**
   ```env
   DEBUG=true
   NODE_ENV=development
   ```

2. **Chạy với nodemon:**
   ```bash
   npm run dev
   ```

3. **Xem logs chi tiết trong console**

---

## 📞 Hỗ trợ thêm

### Nếu vẫn gặp lỗi:

1. **Kiểm tra logs đầy đủ**
2. **Tạo issue trên GitHub với:**
   - Mô tả lỗi chi tiết
   - Console logs
   - Bước tái hiện lỗi
   - Thông tin môi trường (Node.js version, OS)

3. **Thông tin cần thiết:**
   ```bash
   node --version
   npm --version
   # Copy toàn bộ error message
   ```

---

## 🔄 Reset hoàn toàn

### Nếu muốn bắt đầu lại từ đầu:

```bash
# 1. Xóa database
rm -rf data/

# 2. Xóa node_modules
rm -rf node_modules/

# 3. Xóa .env
rm .env

# 4. Cài đặt lại
npm install

# 5. Thiết lập lại
npm run setup
```

---

**💡 Tip:** Luôn backup database trước khi thực hiện thay đổi lớn!
