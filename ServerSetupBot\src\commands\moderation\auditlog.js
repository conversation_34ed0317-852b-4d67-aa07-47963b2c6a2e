const {
  SlashCommandBuilder,
  PermissionFlagsBits,
  ChannelType,
} = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
} = require("../../utils/embedBuilder.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("auditlog")
    .setDescription("Quản lý hệ thống audit log cho server")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("setup")
        .setDescription("Thiết lập audit log cho server")
        .addChannelOption((option) =>
          option
            .setName("channel")
            .setDescription("Kênh để gửi audit logs")
            .setRequired(true)
            .addChannelTypes(ChannelType.GuildText)
        )
        .addStringOption((option) =>
          option
            .setName("events")
            .setDescription(
              "Loại events cần log (all, moderation, server, channels, users)"
            )
            .setRequired(false)
            .addChoices(
              { name: "Tất cả events", value: "all" },
              { name: "Chỉ moderation", value: "moderation" },
              { name: "Chỉ server events", value: "server" },
              { name: "Chỉ channel events", value: "channels" },
              { name: "Chỉ user events", value: "users" },
              { name: "Tùy chỉnh", value: "custom" }
            )
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("remove")
        .setDescription("Xóa audit log khỏi kênh")
        .addChannelOption((option) =>
          option
            .setName("channel")
            .setDescription("Kênh cần xóa audit log")
            .setRequired(true)
            .addChannelTypes(ChannelType.GuildText)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("settings")
        .setDescription("Xem và chỉnh sửa cài đặt audit log")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("export")
        .setDescription("Xuất audit logs ra file")
        .addStringOption((option) =>
          option
            .setName("format")
            .setDescription("Định dạng file xuất")
            .setRequired(false)
            .addChoices(
              { name: "JSON", value: "json" },
              { name: "CSV", value: "csv" },
              { name: "TXT", value: "txt" }
            )
        )
        .addStringOption((option) =>
          option
            .setName("time_range")
            .setDescription("Khoảng thời gian xuất logs")
            .setRequired(false)
            .addChoices(
              { name: "24 giờ qua", value: "1day" },
              { name: "7 ngày qua", value: "7days" },
              { name: "30 ngày qua", value: "30days" },
              { name: "Tất cả", value: "all" }
            )
        )
    ),
  category: "moderation",
  adminOnly: false,
  manageServer: true,

  async execute(interaction, client) {
    const subcommand = interaction.options.getSubcommand();
    const guildId = interaction.guild.id;

    try {
      switch (subcommand) {
        case "setup":
          await handleSetup(interaction, client, guildId);
          break;
        case "remove":
          await handleRemove(interaction, client, guildId);
          break;
        case "settings":
          await handleSettings(interaction, client, guildId);
          break;
        case "export":
          await handleExport(interaction, client, guildId);
          break;
      }
    } catch (error) {
      console.error("Lỗi trong auditlog command:", error);
      const errorEmbed = createErrorEmbed(
        "Lỗi hệ thống!",
        "Đã xảy ra lỗi khi xử lý audit log. Vui lòng thử lại sau!"
      );

      if (interaction.replied || interaction.deferred) {
        await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
      } else {
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
      }
    }
  },
};

async function handleSetup(interaction, client, guildId) {
  const channel = interaction.options.getChannel("channel");
  const events = interaction.options.getString("events") || "all";

  // Kiểm tra quyền bot trong kênh
  const botPermissions = channel.permissionsFor(interaction.guild.members.me);
  if (
    !botPermissions.has([
      PermissionFlagsBits.SendMessages,
      PermissionFlagsBits.EmbedLinks,
    ])
  ) {
    const errorEmbed = createErrorEmbed(
      "Thiếu quyền!",
      `Bot cần quyền **Send Messages** và **Embed Links** trong kênh ${channel}.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  await interaction.deferReply();

  try {
    // Lưu cấu hình audit log
    const config = {
      channelId: channel.id,
      events: events,
      enabled: true,
      ignoredRoles: [],
      ignoredUsers: [],
      smartAlerts: true,
      alertThresholds: {
        messageDelete: 10, // 10 tin nhắn xóa trong 5 phút
        roleGrant: 5, // 5 role được cấp trong 10 phút
        channelCreate: 3, // 3 kênh tạo trong 5 phút
        memberKick: 5, // 5 thành viên bị kick trong 10 phút
        memberBan: 3, // 3 thành viên bị ban trong 10 phút
      },
      createdAt: new Date().toISOString(),
      createdBy: interaction.user.id,
    };

    await client.db.setAuditLogConfig(guildId, config);

    // Tạo embed thông báo thành công
    const embed = createSuccessEmbed(
      "✅ Audit Log đã được thiết lập!",
      `Hệ thống audit log đã được kích hoạt cho server **${interaction.guild.name}**`
    );

    embed.addFields(
      { name: "📍 Kênh Log", value: `${channel}`, inline: true },
      { name: "📋 Events", value: getEventsDisplayName(events), inline: true },
      { name: "🔔 Smart Alerts", value: "Đã bật", inline: true },
      {
        name: "📊 Tổng Events",
        value: `${getEventCount(events)} loại`,
        inline: true,
      },
      {
        name: "⚙️ Cài đặt",
        value: "Sử dụng `/auditlog settings` để tùy chỉnh",
        inline: true,
      },
      {
        name: "📤 Xuất dữ liệu",
        value: "Sử dụng `/auditlog export` để xuất logs",
        inline: true,
      }
    );

    embed.addFields({
      name: "🎯 Các loại events được theo dõi",
      value: getEventsList(events),
      inline: false,
    });

    await interaction.editReply({ embeds: [embed] });

    // Gửi tin nhắn test vào kênh audit log
    const testEmbed = createInfoEmbed(
      "🔍 Audit Log System Activated",
      `Hệ thống audit log đã được kích hoạt bởi ${interaction.user.tag}\n\n` +
        `**Cấu hình:**\n` +
        `• Events: ${getEventsDisplayName(events)}\n` +
        `• Smart Alerts: Enabled\n` +
        `• Thời gian: <t:${Math.floor(Date.now() / 1000)}:F>`
    );

    testEmbed.setFooter({
      text: `Server Setup Bot • Audit Log System`,
      iconURL: client.user.displayAvatarURL(),
    });

    await channel.send({ embeds: [testEmbed] });
  } catch (error) {
    console.error("Lỗi khi setup audit log:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi thiết lập!",
      "Không thể thiết lập audit log. Vui lòng thử lại sau!"
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleRemove(interaction, client, guildId) {
  const channel = interaction.options.getChannel("channel");

  await interaction.deferReply();

  try {
    // Lấy cấu hình hiện tại
    const config = await client.db.getAuditLogConfig(guildId);

    if (!config || !config.enabled) {
      const errorEmbed = createErrorEmbed(
        "Không tìm thấy!",
        "Server này chưa có audit log được thiết lập."
      );
      return await interaction.editReply({ embeds: [errorEmbed] });
    }

    if (config.channelId !== channel.id) {
      const errorEmbed = createErrorEmbed(
        "Kênh không đúng!",
        `Audit log hiện tại được thiết lập ở <#${config.channelId}>, không phải ${channel}.`
      );
      return await interaction.editReply({ embeds: [errorEmbed] });
    }

    // Xóa cấu hình
    await client.db.removeAuditLogConfig(guildId);

    const embed = createSuccessEmbed(
      "✅ Đã xóa Audit Log!",
      `Hệ thống audit log đã được tắt cho kênh ${channel}`
    );

    embed.addFields(
      {
        name: "📊 Thống kê",
        value: "Dữ liệu logs đã được lưu trữ",
        inline: true,
      },
      {
        name: "🔄 Khôi phục",
        value: "Có thể thiết lập lại bất cứ lúc nào",
        inline: true,
      }
    );

    await interaction.editReply({ embeds: [embed] });

    // Gửi tin nhắn thông báo vào kênh
    try {
      const notifyEmbed = createInfoEmbed(
        "🔍 Audit Log System Deactivated",
        `Hệ thống audit log đã được tắt bởi ${interaction.user.tag}\n\n` +
          `Thời gian: <t:${Math.floor(Date.now() / 1000)}:F>`
      );

      await channel.send({ embeds: [notifyEmbed] });
    } catch (error) {
      console.log("Không thể gửi thông báo vào kênh audit log");
    }
  } catch (error) {
    console.error("Lỗi khi remove audit log:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi xóa!",
      "Không thể xóa audit log. Vui lòng thử lại sau!"
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

// Helper functions
function getEventsDisplayName(events) {
  const names = {
    all: "🌟 Tất cả events",
    moderation: "🔨 Moderation",
    server: "🏰 Server events",
    channels: "📝 Channel events",
    users: "👥 User events",
    custom: "⚙️ Tùy chỉnh",
  };
  return names[events] || events;
}

function getEventCount(events) {
  const counts = {
    all: 150,
    moderation: 25,
    server: 35,
    channels: 30,
    users: 15,
    custom: "Tùy chỉnh",
  };
  return counts[events] || "N/A";
}

function getEventsList(events) {
  const lists = {
    all: "• Applications, Channels, AutoMod, Emoji, Events\n• Invites, Messages, Polls, Roles, Stage\n• Server, Stickers, Soundboard, Threads\n• Users, Voice, Webhooks, Moderation",
    moderation:
      "• Auto moderation, Ban/Unban, Kick\n• Mute/Unmute, Warn/Unwarn\n• Reports, User notes, Case management",
    server:
      "• Server settings, Boost level, Features\n• Verification, MFA, Vanity URL\n• Member join/leave, Prune",
    channels:
      "• Channel create/delete/update\n• Permission changes, Topic/Name updates\n• Slowmode, NSFW, Parent changes",
    users:
      "• Username/Avatar updates\n• Role changes, Timeouts\n• Voice activity",
    custom:
      "• Các events được chọn tùy chỉnh\n• Có thể bật/tắt từng loại riêng biệt",
  };
  return lists[events] || "Danh sách events tùy chỉnh";
}

async function handleSettings(interaction, client, guildId) {
  await interaction.deferReply();

  try {
    const config = await client.db.getAuditLogConfig(guildId);

    if (!config || !config.enabled) {
      const errorEmbed = createErrorEmbed(
        "Chưa thiết lập!",
        "Server này chưa có audit log được thiết lập. Sử dụng `/auditlog setup` để bắt đầu."
      );
      return await interaction.editReply({ embeds: [errorEmbed] });
    }

    const embed = createInfoEmbed(
      "⚙️ Cài đặt Audit Log",
      `Cấu hình audit log hiện tại cho **${interaction.guild.name}**`
    );

    const channel = interaction.guild.channels.cache.get(config.channelId);
    const channelName = channel
      ? `${channel}`
      : `❌ Kênh đã bị xóa (${config.channelId})`;

    embed.addFields(
      { name: "📍 Kênh Log", value: channelName, inline: true },
      {
        name: "📋 Events",
        value: getEventsDisplayName(config.events),
        inline: true,
      },
      {
        name: "🔔 Smart Alerts",
        value: config.smartAlerts ? "✅ Đã bật" : "❌ Đã tắt",
        inline: true,
      },
      {
        name: "👥 Ignored Roles",
        value:
          config.ignoredRoles.length > 0
            ? `${config.ignoredRoles.length} roles`
            : "Không có",
        inline: true,
      },
      {
        name: "🚫 Ignored Users",
        value:
          config.ignoredUsers.length > 0
            ? `${config.ignoredUsers.length} users`
            : "Không có",
        inline: true,
      },
      {
        name: "📅 Thiết lập lúc",
        value: `<t:${Math.floor(
          new Date(config.createdAt).getTime() / 1000
        )}:F>`,
        inline: true,
      }
    );

    // Alert thresholds
    const thresholds = config.alertThresholds;
    embed.addFields({
      name: "⚠️ Ngưỡng cảnh báo",
      value:
        `• **Message Delete:** ${thresholds.messageDelete} tin nhắn/5 phút\n` +
        `• **Role Grant:** ${thresholds.roleGrant} roles/10 phút\n` +
        `• **Channel Create:** ${thresholds.channelCreate} kênh/5 phút\n` +
        `• **Member Kick:** ${thresholds.memberKick} kicks/10 phút\n` +
        `• **Member Ban:** ${thresholds.memberBan} bans/10 phút`,
      inline: false,
    });

    // Statistics
    const stats = await client.db.getAuditLogStats(guildId);
    embed.addFields({
      name: "📊 Thống kê (30 ngày qua)",
      value:
        `• **Tổng logs:** ${stats.totalLogs.toLocaleString()}\n` +
        `• **Cảnh báo:** ${stats.alerts}\n` +
        `• **Events phổ biến:** ${stats.topEvent}\n` +
        `• **Trung bình/ngày:** ${Math.round(
          stats.totalLogs / 30
        ).toLocaleString()}`,
      inline: false,
    });

    const components = createSettingsComponents();

    await interaction.editReply({ embeds: [embed], components });
  } catch (error) {
    console.error("Lỗi khi lấy settings audit log:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể lấy cài đặt audit log. Vui lòng thử lại sau!"
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleExport(interaction, client, guildId) {
  const format = interaction.options.getString("format") || "json";
  const timeRange = interaction.options.getString("time_range") || "7days";

  await interaction.deferReply();

  try {
    const config = await client.db.getAuditLogConfig(guildId);

    if (!config || !config.enabled) {
      const errorEmbed = createErrorEmbed(
        "Chưa thiết lập!",
        "Server này chưa có audit log được thiết lập."
      );
      return await interaction.editReply({ embeds: [errorEmbed] });
    }

    // Tính toán thời gian
    const now = new Date();
    let startDate;

    switch (timeRange) {
      case "1day":
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case "7days":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30days":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case "all":
        startDate = new Date(config.createdAt);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // Lấy dữ liệu logs
    const logs = await client.db.getAuditLogs(guildId, startDate, now);

    if (logs.length === 0) {
      const errorEmbed = createErrorEmbed(
        "Không có dữ liệu!",
        `Không tìm thấy audit logs nào trong khoảng thời gian ${getTimeRangeDisplayName(
          timeRange
        )}.`
      );
      return await interaction.editReply({ embeds: [errorEmbed] });
    }

    // Tạo file export
    const exportData = await generateExportFile(
      logs,
      format,
      interaction.guild
    );

    const embed = createSuccessEmbed(
      "📤 Xuất Audit Logs thành công!",
      `Đã xuất **${logs.length.toLocaleString()}** audit logs`
    );

    embed.addFields(
      {
        name: "📅 Khoảng thời gian",
        value: getTimeRangeDisplayName(timeRange),
        inline: true,
      },
      { name: "📄 Định dạng", value: format.toUpperCase(), inline: true },
      {
        name: "📊 Số lượng",
        value: `${logs.length.toLocaleString()} logs`,
        inline: true,
      },
      {
        name: "💾 Kích thước file",
        value: `${(exportData.length / 1024).toFixed(2)} KB`,
        inline: true,
      },
      {
        name: "⏰ Xuất lúc",
        value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
        inline: true,
      },
      { name: "👤 Xuất bởi", value: interaction.user.tag, inline: true }
    );

    const fileName = `auditlogs_${
      interaction.guild.id
    }_${timeRange}_${Date.now()}.${format}`;

    await interaction.editReply({
      embeds: [embed],
      files: [
        {
          attachment: Buffer.from(exportData),
          name: fileName,
        },
      ],
    });
  } catch (error) {
    console.error("Lỗi khi export audit log:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi xuất file!",
      "Không thể xuất audit logs. Vui lòng thử lại sau!"
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

function createSettingsComponents() {
  const {
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
  } = require("discord.js");

  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("auditlog_settings_channel")
      .setLabel("📍 Đổi kênh")
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId("auditlog_settings_events")
      .setLabel("📋 Chỉnh events")
      .setStyle(ButtonStyle.Primary),
    new ButtonBuilder()
      .setCustomId("auditlog_settings_alerts")
      .setLabel("🔔 Cảnh báo")
      .setStyle(ButtonStyle.Success),
    new ButtonBuilder()
      .setCustomId("auditlog_settings_ignore")
      .setLabel("🚫 Bỏ qua")
      .setStyle(ButtonStyle.Danger)
  );

  return [buttonRow];
}

function getTimeRangeDisplayName(timeRange) {
  const names = {
    "1day": "24 giờ qua",
    "7days": "7 ngày qua",
    "30days": "30 ngày qua",
    all: "Tất cả thời gian",
  };
  return names[timeRange] || timeRange;
}

async function generateExportFile(logs, format, guild) {
  switch (format) {
    case "json":
      return JSON.stringify(
        {
          guild: {
            id: guild.id,
            name: guild.name,
          },
          exportedAt: new Date().toISOString(),
          totalLogs: logs.length,
          logs: logs,
        },
        null,
        2
      );

    case "csv":
      const headers =
        "Timestamp,Event Type,User,Target,Action,Details,Channel\n";
      const rows = logs
        .map((log) => {
          const timestamp = new Date(log.timestamp).toISOString();
          const eventType = log.eventType || "";
          const user = log.user || "";
          const target = log.target || "";
          const action = log.action || "";
          const details = (log.details || "").replace(/"/g, '""');
          const channel = log.channel || "";

          return `"${timestamp}","${eventType}","${user}","${target}","${action}","${details}","${channel}"`;
        })
        .join("\n");

      return headers + rows;

    case "txt":
      return logs
        .map((log) => {
          const timestamp = new Date(log.timestamp).toLocaleString();
          return `[${timestamp}] ${log.eventType}: ${log.action} - ${log.details}`;
        })
        .join("\n");

    default:
      return JSON.stringify(logs, null, 2);
  }
}
