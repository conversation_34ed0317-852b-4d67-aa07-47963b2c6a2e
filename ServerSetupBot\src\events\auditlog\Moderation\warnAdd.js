const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction, client) {
        try {
            // This would be triggered by a custom warn command
            // Check if this is a warn command interaction
            if (!interaction.isCommand() || interaction.commandName !== 'warn') return;
            
            console.log(`⚠️ User warned in ${interaction.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(interaction.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'WARN_ADD')) return;
            
            // Get warn details from interaction
            const targetUser = interaction.options.getUser('user');
            const reason = interaction.options.getString('reason') || 'Không có lý do';
            const moderator = interaction.user;
            
            // Create event data
            const eventData = {
                eventType: 'WARN_ADD',
                user: targetUser.tag,
                userId: targetUser.id,
                moderator: moderator.tag,
                moderatorId: moderator.id,
                action: `Thành viên được cảnh báo bởi ${moderator.tag}`,
                details: `**${targetUser.tag}** đã được cảnh báo bởi **${moderator.tag}**`,
                target: targetUser.tag,
                reason: reason,
                timestamp: new Date().toISOString()
            };
            
            // Add to database and get warn count
            await client.db.addAuditLog(interaction.guild.id, eventData);
            const warnCount = await client.db.addWarning(interaction.guild.id, targetUser.id, moderator.id, reason);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '⚠️ Thành viên được cảnh báo',
                `Vừa có thành viên nhận cảnh báo`
            );
            
            embed.setColor(0xf39c12); // Orange for warning
            
            embed.addFields([
                {
                    name: '> Thành viên được cảnh báo',
                    value: `- ${targetUser.tag} (${targetUser})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${targetUser.id}`,
                    inline: true
                },
                {
                    name: '> Moderator',
                    value: `- ${moderator.tag}`,
                    inline: true
                },
                {
                    name: '> Thời gian cảnh báo',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Số cảnh báo hiện tại',
                    value: `- ${warnCount} cảnh báo`,
                    inline: true
                }
            ]);
            
            // Add warning reason
            embed.addFields({
                name: '> Lý do cảnh báo',
                value: `- ${reason}`,
                inline: false
            });
            
            // Add member info
            const member = interaction.guild.members.cache.get(targetUser.id);
            if (member) {
                const accountAge = Date.now() - targetUser.createdTimestamp;
                const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
                const memberAge = Date.now() - member.joinedTimestamp;
                const memberAgeDays = Math.floor(memberAge / (1000 * 60 * 60 * 24));
                
                embed.addFields([
                    {
                        name: '> Tuổi tài khoản',
                        value: `- ${accountAgeDays} ngày`,
                        inline: true
                    },
                    {
                        name: '> Thời gian trong server',
                        value: `- ${memberAgeDays} ngày`,
                        inline: true
                    }
                ]);
            }
            
            // Add warning level assessment
            let warningLevel = '';
            let warningColor = 0xf39c12;
            
            if (warnCount === 1) {
                warningLevel = '🟡 Cảnh báo đầu tiên';
                warningColor = 0xf1c40f;
            } else if (warnCount === 2) {
                warningLevel = '🟠 Cảnh báo lần hai';
                warningColor = 0xe67e22;
            } else if (warnCount >= 3) {
                warningLevel = '🔴 Nhiều cảnh báo';
                warningColor = 0xe74c3c;
            }
            
            embed.setColor(warningColor);
            embed.addFields({
                name: '> Mức độ cảnh báo',
                value: `- ${warningLevel}`,
                inline: true
            });
            
            // Add escalation recommendations
            if (warnCount >= 3) {
                embed.addFields({
                    name: '> 🚨 Khuyến nghị leo thang',
                    value: [
                        '• Cân nhắc mute tạm thời',
                        '• Có thể cần kick nếu tiếp tục',
                        '• Thảo luận với team moderation',
                        '• Xem xét ban nếu vi phạm nghiêm trọng'
                    ].join('\n'),
                    inline: false
                });
            } else if (warnCount === 2) {
                embed.addFields({
                    name: '> ⚠️ Lưu ý',
                    value: [
                        '• Theo dõi hành vi chặt chẽ hơn',
                        '• Cảnh báo cuối trước khi leo thang',
                        '• Có thể cần giải thích quy tắc rõ hơn',
                        '• Chuẩn bị biện pháp mạnh hơn'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add warning impact
            embed.addFields({
                name: '> 📝 Tác động của cảnh báo',
                value: [
                    '• Ghi nhận vi phạm vào hồ sơ',
                    '• Tăng mức độ theo dõi',
                    '• Có thể ảnh hưởng đến quyết định tương lai',
                    '• Nhắc nhở tuân thủ quy tắc'
                ].join('\n'),
                inline: false
            });
            
            // Set user avatar as thumbnail
            if (targetUser.displayAvatarURL()) {
                embed.setThumbnail(targetUser.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${interaction.guild.name} • Member Warning Add`,
                iconURL: interaction.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Warning add logged: ${targetUser.tag}`);
            
        } catch (error) {
            console.error('Error in warnAdd audit log:', error);
        }
    }
};
