const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const Inventory = require('../models/Inventory');
const User = require('../models/User');
const { createErrorEmbed, formatCurrency } = require('../utils/embedBuilder');
const config = require('../config/config');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('inventory')
        .setNameLocalizations({
            'vi': 'kho'
        })
        .setDescription('Xem kho đồ của bạn hoặc người khác')
        .addUserOption(option =>
            option.setName('user')
                .setNameLocalizations({
                    'vi': 'nguoidung'
                })
                .setDescription('Người dùng cần xem kho đồ')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('category')
                .setNameLocalizations({
                    'vi': 'danhmuc'
                })
                .setDescription('<PERSON>ọ<PERSON> theo danh mục')
                .setRequired(false)
        ),
    
    cooldown: 5,

    async execute(interaction) {
        try {
            const targetUser = interaction.options.getUser('user') || interaction.user;
            const category = interaction.options.getString('category');
            
            // Đảm bảo user tồn tại trong database
            await User.findOrCreate(targetUser);
            
            // Lấy inventory
            let inventory;
            if (category) {
                inventory = await Inventory.getItemsByCategory(targetUser.id, category);
            } else {
                inventory = await Inventory.getUserInventory(targetUser.id);
            }

            // Lấy thống kê inventory
            const stats = await Inventory.getInventoryStats(targetUser.id);

            if (inventory.length === 0) {
                const errorEmbed = createErrorEmbed(
                    'Kho đồ trống',
                    category ? 
                        `${targetUser.username} không có vật phẩm nào trong danh mục "${category}"` :
                        `${targetUser.username} chưa có vật phẩm nào trong kho`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Nhóm items theo category
            const itemsByCategory = {};
            inventory.forEach(invItem => {
                const cat = invItem.item.category;
                if (!itemsByCategory[cat]) {
                    itemsByCategory[cat] = [];
                }
                itemsByCategory[cat].push(invItem);
            });

            const embed = new EmbedBuilder()
                .setColor(config.colors.primary)
                .setTitle(`${config.emojis.shop} Kho đồ của ${targetUser.username}`)
                .setThumbnail(targetUser.displayAvatarURL({ dynamic: true }))
                .setDescription(
                    category ? 
                        `Danh mục: **${category}**` :
                        `**${stats.uniqueItems}** loại vật phẩm • **${stats.totalItems}** tổng số lượng`
                )
                .setTimestamp()
                .setFooter({ text: 'Economy Bot' });

            // Thêm thông tin tổng quan nếu không filter category
            if (!category) {
                embed.addFields({
                    name: `${config.emojis.gem} Thống kê`,
                    value: `**Tổng giá trị:** ${formatCurrency(stats.totalValue)}\n` +
                           `**Số loại vật phẩm:** ${stats.uniqueItems}\n` +
                           `**Tổng số lượng:** ${stats.totalItems}`,
                    inline: false
                });
            }

            // Thêm fields cho mỗi category
            for (const [cat, categoryItems] of Object.entries(itemsByCategory)) {
                const itemList = categoryItems.map(invItem => {
                    const sellValue = invItem.item.sellPrice * invItem.quantity;
                    return `${invItem.item.emoji} **${invItem.item.name}** x${invItem.quantity} (${formatCurrency(sellValue)})`;
                }).join('\n');

                embed.addFields({
                    name: `📂 ${cat.charAt(0).toUpperCase() + cat.slice(1)}`,
                    value: itemList.length > 1024 ? itemList.substring(0, 1021) + '...' : itemList,
                    inline: false
                });
            }

            // Thêm hướng dẫn
            if (!category) {
                embed.addFields({
                    name: `${config.emojis.info} Hướng dẫn`,
                    value: '• Sử dụng `/shop sell <tên vật phẩm>` để bán\n' +
                           '• Sử dụng `/inventory <category>` để lọc theo danh mục',
                    inline: false
                });
            }

            await interaction.reply({ embeds: [embed] });
            
        } catch (error) {
            console.error('Lỗi lệnh inventory:', error);
            
            const errorEmbed = createErrorEmbed(
                'Lỗi kho đồ',
                'Không thể hiển thị kho đồ. Vui lòng thử lại sau.'
            );
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
