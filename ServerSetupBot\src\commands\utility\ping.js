const {
  SlashCommandBuilder,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
} = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
} = require("../../utils/embedBuilder.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("ping")
    .setDescription("Kiểm tra độ trễ và trạng thái bot")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("simple")
        .setDescription("Ping đơn giản")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("detailed")
        .setDescription("Ping chi tiết với thông tin hệ thống")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("monitor")
        .setDescription("Theo dõi ping liên tục")
        .addIntegerOption((option) =>
          option
            .setName("duration")
            .setDescription("Thời gian theo dõi (giây)")
            .setRequired(false)
            .setMinValue(10)
            .setMaxValue(300)
        )
        .addIntegerOption((option) =>
          option
            .setName("interval")
            .setDescription("Khoảng cách giữa các lần ping (giây)")
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(30)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("compare")
        .setDescription("So sánh ping với các region khác")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("history")
        .setDescription("Xem lịch sử ping")
        .addStringOption((option) =>
          option
            .setName("period")
            .setDescription("Khoảng thời gian")
            .setRequired(false)
            .addChoices(
              { name: "1 giờ qua", value: "1h" },
              { name: "6 giờ qua", value: "6h" },
              { name: "24 giờ qua", value: "24h" },
              { name: "7 ngày qua", value: "7d" }
            )
        )
    ),
  category: "utility",
  adminOnly: false,
  manageServer: false,

  async execute(interaction, client) {
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case "simple":
        await handleSimplePing(interaction, client);
        break;
      case "detailed":
        await handleDetailedPing(interaction, client);
        break;
      case "monitor":
        await handleMonitorPing(interaction, client);
        break;
      case "compare":
        await handleComparePing(interaction, client);
        break;
      case "history":
        await handlePingHistory(interaction, client);
        break;
    }
  },
};

async function handleSimplePing(interaction, client) {
  const sent = await interaction.reply({
    content: "🏓 Đang ping...",
    fetchReply: true,
  });

  const roundtripLatency = sent.createdTimestamp - interaction.createdTimestamp;
  const wsLatency = client.ws.ping;

  const embed = createSuccessEmbed(
    "🏓 Pong!",
    `**Roundtrip:** ${roundtripLatency}ms\n` +
    `**WebSocket:** ${wsLatency}ms\n` +
    `**Status:** ${getLatencyStatus(roundtripLatency)}`
  );

  embed.setColor(getLatencyColor(roundtripLatency));

  await interaction.editReply({ content: null, embeds: [embed] });
}

async function handleDetailedPing(interaction, client) {
  const sent = await interaction.reply({
    content: "🔍 Đang kiểm tra chi tiết...",
    fetchReply: true,
  });

  const roundtripLatency = sent.createdTimestamp - interaction.createdTimestamp;
  const wsLatency = client.ws.ping;
  const dbLatency = await measureDatabaseLatency(client);
  const apiLatency = await measureAPILatency();

  const embed = createInfoEmbed(
    "📊 Ping Chi Tiết",
    `Thông tin độ trễ và hiệu suất hệ thống`
  );

  embed.addFields({
    name: "🏓 Độ trễ",
    value: `**Roundtrip:** ${roundtripLatency}ms ${getLatencyEmoji(roundtripLatency)}\n` +
           `**WebSocket:** ${wsLatency}ms ${getLatencyEmoji(wsLatency)}\n` +
           `**Database:** ${dbLatency}ms ${getLatencyEmoji(dbLatency)}\n` +
           `**Discord API:** ${apiLatency}ms ${getLatencyEmoji(apiLatency)}`,
    inline: true
  });

  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  
  embed.addFields({
    name: "⚙️ Hệ thống",
    value: `**Uptime:** ${formatUptime(uptime)}\n` +
           `**Memory:** ${formatBytes(memoryUsage.used)}/${formatBytes(memoryUsage.total)}\n` +
           `**CPU:** ${await getCPUUsage()}%\n` +
           `**Node.js:** ${process.version}`,
    inline: true
  });

  embed.addFields({
    name: "🌐 Kết nối",
    value: `**Guilds:** ${client.guilds.cache.size.toLocaleString()}\n` +
           `**Users:** ${client.users.cache.size.toLocaleString()}\n` +
           `**Channels:** ${client.channels.cache.size.toLocaleString()}\n` +
           `**Shard:** ${interaction.guild.shardId || 0}`,
    inline: true
  });

  const overallStatus = getOverallStatus(roundtripLatency, wsLatency, dbLatency);
  embed.addFields({
    name: "📈 Tổng quan",
    value: `**Trạng thái:** ${overallStatus.status}\n` +
           `**Điểm số:** ${overallStatus.score}/100\n` +
           `**Đánh giá:** ${overallStatus.rating}`,
    inline: false
  });

  embed.setColor(getLatencyColor(roundtripLatency));
  embed.setTimestamp();

  const components = createDetailedPingComponents();

  await interaction.editReply({ 
    content: null, 
    embeds: [embed], 
    components 
  });
}

async function handleMonitorPing(interaction, client) {
  const duration = interaction.options.getInteger("duration") || 60;
  const interval = interaction.options.getInteger("interval") || 5;

  await interaction.deferReply();

  const embed = createInfoEmbed(
    "📡 Ping Monitor",
    `Theo dõi ping trong **${duration}s** với khoảng cách **${interval}s**`
  );

  embed.addFields({
    name: "📊 Thống kê",
    value: "Đang khởi tạo...",
    inline: false
  });

  const message = await interaction.editReply({ embeds: [embed] });

  // Start monitoring
  const pingData = [];
  const startTime = Date.now();
  let iterations = 0;
  const maxIterations = Math.floor(duration / interval);

  const monitorInterval = setInterval(async () => {
    try {
      const pingStart = Date.now();
      const testMessage = await interaction.followUp({ 
        content: "ping test", 
        ephemeral: true 
      });
      await testMessage.delete();
      const pingTime = Date.now() - pingStart;

      pingData.push({
        time: Date.now(),
        roundtrip: pingTime,
        websocket: client.ws.ping,
        iteration: iterations + 1
      });

      iterations++;

      // Update embed
      const stats = calculatePingStats(pingData);
      const updatedEmbed = createInfoEmbed(
        "📡 Ping Monitor",
        `Theo dõi ping - **${iterations}/${maxIterations}** lần đo`
      );

      updatedEmbed.addFields({
        name: "📊 Thống kê hiện tại",
        value: `**Trung bình:** ${stats.average}ms\n` +
               `**Thấp nhất:** ${stats.min}ms\n` +
               `**Cao nhất:** ${stats.max}ms\n` +
               `**Độ lệch:** ${stats.deviation}ms`,
        inline: true
      });

      updatedEmbed.addFields({
        name: "📈 Dữ liệu gần đây",
        value: pingData.slice(-5).map((data, index) => 
          `**${data.iteration}.** ${data.roundtrip}ms (WS: ${data.websocket}ms)`
        ).join('\n'),
        inline: true
      });

      const progress = Math.round((iterations / maxIterations) * 100);
      updatedEmbed.addFields({
        name: "⏱️ Tiến độ",
        value: `${'█'.repeat(Math.floor(progress / 5))}${'░'.repeat(20 - Math.floor(progress / 5))} ${progress}%`,
        inline: false
      });

      await interaction.editReply({ embeds: [updatedEmbed] });

      if (iterations >= maxIterations) {
        clearInterval(monitorInterval);
        
        // Final summary
        const finalStats = calculatePingStats(pingData);
        const finalEmbed = createSuccessEmbed(
          "✅ Ping Monitor Hoàn Thành",
          `Đã hoàn thành **${iterations}** lần đo trong **${duration}s**`
        );

        finalEmbed.addFields({
          name: "📊 Thống kê cuối cùng",
          value: `**Trung bình:** ${finalStats.average}ms\n` +
                 `**Thấp nhất:** ${finalStats.min}ms\n` +
                 `**Cao nhất:** ${finalStats.max}ms\n` +
                 `**Độ lệch chuẩn:** ${finalStats.deviation}ms\n` +
                 `**Độ ổn định:** ${finalStats.stability}%`,
          inline: false
        });

        await interaction.editReply({ embeds: [finalEmbed] });
      }

    } catch (error) {
      console.error("Lỗi trong ping monitor:", error);
      clearInterval(monitorInterval);
    }
  }, interval * 1000);

  // Auto cleanup after duration
  setTimeout(() => {
    clearInterval(monitorInterval);
  }, duration * 1000 + 5000);
}

async function handleComparePing(interaction, client) {
  await interaction.deferReply();

  const regions = [
    { name: "US East", endpoint: "discord.com" },
    { name: "US West", endpoint: "discord.com" },
    { name: "Europe", endpoint: "discord.com" },
    { name: "Asia", endpoint: "discord.com" },
    { name: "Brazil", endpoint: "discord.com" },
    { name: "Australia", endpoint: "discord.com" }
  ];

  const embed = createInfoEmbed(
    "🌍 So sánh Ping theo Region",
    "Đang kiểm tra ping đến các region khác nhau..."
  );

  const message = await interaction.editReply({ embeds: [embed] });

  const results = [];
  for (const region of regions) {
    try {
      const ping = await measureRegionPing(region.endpoint);
      results.push({
        name: region.name,
        ping: ping,
        status: getLatencyStatus(ping)
      });
    } catch (error) {
      results.push({
        name: region.name,
        ping: -1,
        status: "❌ Lỗi"
      });
    }
  }

  // Sort by ping
  results.sort((a, b) => {
    if (a.ping === -1) return 1;
    if (b.ping === -1) return -1;
    return a.ping - b.ping;
  });

  const updatedEmbed = createInfoEmbed(
    "🌍 So sánh Ping theo Region",
    "Kết quả ping đến các region Discord"
  );

  updatedEmbed.addFields({
    name: "📊 Kết quả",
    value: results.map((result, index) => {
      const medal = index === 0 ? "🥇" : index === 1 ? "🥈" : index === 2 ? "🥉" : "📍";
      const pingText = result.ping === -1 ? "Timeout" : `${result.ping}ms`;
      return `${medal} **${result.name}:** ${pingText} ${result.status}`;
    }).join('\n'),
    inline: false
  });

  const bestRegion = results.find(r => r.ping !== -1);
  if (bestRegion) {
    updatedEmbed.addFields({
      name: "🏆 Region tốt nhất",
      value: `**${bestRegion.name}** với ping **${bestRegion.ping}ms**`,
      inline: false
    });
  }

  await interaction.editReply({ embeds: [updatedEmbed] });
}

async function handlePingHistory(interaction, client) {
  const period = interaction.options.getString("period") || "1h";

  await interaction.deferReply();

  try {
    const historyData = await getPingHistory(client, period);
    
    if (!historyData || historyData.length === 0) {
      const embed = createErrorEmbed(
        "Không có dữ liệu",
        "Chưa có dữ liệu ping cho khoảng thời gian này."
      );
      return await interaction.editReply({ embeds: [embed] });
    }

    const stats = calculateHistoryStats(historyData);
    
    const embed = createInfoEmbed(
      `📈 Lịch sử Ping - ${getPeriodDisplayName(period)}`,
      `Thống kê ping trong **${getPeriodDisplayName(period)}**`
    );

    embed.addFields({
      name: "📊 Thống kê tổng quan",
      value: `**Số lần đo:** ${historyData.length}\n` +
             `**Trung bình:** ${stats.average}ms\n` +
             `**Thấp nhất:** ${stats.min}ms\n` +
             `**Cao nhất:** ${stats.max}ms\n` +
             `**Độ ổn định:** ${stats.stability}%`,
      inline: true
    });

    embed.addFields({
      name: "📈 Xu hướng",
      value: `**Hiện tại:** ${stats.current}ms\n` +
             `**Xu hướng:** ${stats.trend}\n` +
             `**Thay đổi:** ${stats.change}\n` +
             `**Dự đoán:** ${stats.prediction}ms`,
      inline: true
    });

    // Create simple ASCII chart
    const chart = createASCIIChart(historyData.slice(-20));
    embed.addFields({
      name: "📊 Biểu đồ (20 điểm gần nhất)",
      value: `\`\`\`${chart}\`\`\``,
      inline: false
    });

    const components = createHistoryComponents(period);

    await interaction.editReply({ embeds: [embed], components });

  } catch (error) {
    console.error("Lỗi khi lấy lịch sử ping:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống",
      "Không thể lấy dữ liệu lịch sử ping."
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

// Helper functions
function getLatencyStatus(latency) {
  if (latency < 100) return "🟢 Tuyệt vời";
  if (latency < 200) return "🟡 Tốt";
  if (latency < 500) return "🟠 Trung bình";
  return "🔴 Chậm";
}

function getLatencyEmoji(latency) {
  if (latency < 100) return "🟢";
  if (latency < 200) return "🟡";
  if (latency < 500) return "🟠";
  return "🔴";
}

function getLatencyColor(latency) {
  if (latency < 100) return "#00ff00";
  if (latency < 200) return "#ffff00";
  if (latency < 500) return "#ff8000";
  return "#ff0000";
}

function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (days > 0) return `${days}d ${hours}h ${minutes}m`;
  if (hours > 0) return `${hours}h ${minutes}m`;
  return `${minutes}m`;
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function measureDatabaseLatency(client) {
  const start = Date.now();
  try {
    // Mock database query
    await new Promise(resolve => setTimeout(resolve, Math.random() * 50));
    return Date.now() - start;
  } catch (error) {
    return -1;
  }
}

async function measureAPILatency() {
  const start = Date.now();
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
    return Date.now() - start;
  } catch (error) {
    return -1;
  }
}

async function getCPUUsage() {
  // Mock CPU usage
  return Math.floor(Math.random() * 30) + 10;
}

function getOverallStatus(roundtrip, websocket, database) {
  const avgLatency = (roundtrip + websocket + database) / 3;
  let score = 100;
  let status = "🟢 Tuyệt vời";
  let rating = "Excellent";

  if (avgLatency > 100) {
    score -= 20;
    status = "🟡 Tốt";
    rating = "Good";
  }
  if (avgLatency > 200) {
    score -= 30;
    status = "🟠 Trung bình";
    rating = "Average";
  }
  if (avgLatency > 500) {
    score -= 40;
    status = "🔴 Chậm";
    rating = "Poor";
  }

  return { status, score: Math.max(score, 0), rating };
}

function createDetailedPingComponents() {
  const buttonRow = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId("ping_refresh")
        .setLabel("🔄 Làm mới")
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId("ping_monitor")
        .setLabel("📡 Monitor")
        .setStyle(ButtonStyle.Primary),
      new ButtonBuilder()
        .setCustomId("ping_history")
        .setLabel("📈 Lịch sử")
        .setStyle(ButtonStyle.Success)
    );

  return [buttonRow];
}

function calculatePingStats(pingData) {
  if (pingData.length === 0) return { average: 0, min: 0, max: 0, deviation: 0, stability: 0 };

  const pings = pingData.map(d => d.roundtrip);
  const average = Math.round(pings.reduce((a, b) => a + b, 0) / pings.length);
  const min = Math.min(...pings);
  const max = Math.max(...pings);
  
  const variance = pings.reduce((acc, ping) => acc + Math.pow(ping - average, 2), 0) / pings.length;
  const deviation = Math.round(Math.sqrt(variance));
  
  const stability = Math.max(0, 100 - (deviation / average * 100));

  return { average, min, max, deviation, stability: Math.round(stability) };
}

async function measureRegionPing(endpoint) {
  // Mock implementation
  return Math.floor(Math.random() * 200) + 50;
}

async function getPingHistory(client, period) {
  // Mock implementation - would fetch from database
  return [];
}

function calculateHistoryStats(historyData) {
  // Mock implementation
  return {
    average: 120,
    min: 80,
    max: 200,
    stability: 85,
    current: 115,
    trend: "📈 Tăng nhẹ",
    change: "+5ms",
    prediction: 125
  };
}

function getPeriodDisplayName(period) {
  const periods = {
    "1h": "1 giờ qua",
    "6h": "6 giờ qua", 
    "24h": "24 giờ qua",
    "7d": "7 ngày qua"
  };
  return periods[period] || period;
}

function createASCIIChart(data) {
  // Simple ASCII chart implementation
  return "Ping Chart (mock)";
}

function createHistoryComponents(period) {
  const buttonRow = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId("ping_history_refresh")
        .setLabel("🔄 Làm mới")
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId("ping_history_export")
        .setLabel("📊 Xuất dữ liệu")
        .setStyle(ButtonStyle.Primary)
    );

  return [buttonRow];
}
