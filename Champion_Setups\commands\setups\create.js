const Command = require("../../structures/Command");
const Discord = require("discord.js");

let callData = {};

module.exports = class Create extends Command {
  constructor(client) {
    super(client, {
      name: "create",
      description: client.cmdConfig.create.description,
      usage: client.cmdConfig.create.usage,
      permissions: client.cmdConfig.create.permissions,
      aliases: client.cmdConfig.create.aliases,
      category: "setups",
      listed: client.cmdConfig.create.enabled,
      slash: true,
      options: [{
        name: "name", 
        description: "Template Name",
        type: Discord.ApplicationCommandOptionType.String, 
        required: true
      }]
    });
  }

  async run(message, args) {
    let template = args[0];

    if(!template) return message.channel.send({ embeds: [this.client.utils.usage(this.client, this.client.cmdConfig.create.usage)]});
    if(!this.client.template(template)) return message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
      this.client.language.titles.error, this.client.language.setup.invalid_template, this.client.embeds.error_color)]});

    let embed = new Discord.EmbedBuilder()
      .setColor(this.client.embeds.setup.create.color);
    
    if(this.client.embeds.setup.create.title) embed.setTitle(this.client.embeds.setup.create.title);
    
    if(this.client.embeds.setup.create.description) embed.setDescription(this.client.embeds.setup.create.description.replace("<template>", template));
    
    let field = this.client.embeds.setup.create.fields;
    for(let i = 0; i < this.client.embeds.setup.create.fields.length; i++) {
      embed.addFields([{ name: field[i].title, value: field[i].description.replace("<template>", template) }]);
    }

    if(this.client.embeds.setup.create.footer == true) embed.setFooter({ text: message.author.username, iconURL: message.author.displayAvatarURL() }).setTimestamp();
    if(this.client.embeds.setup.create.thumbnail == true) embed.setThumbnail(message.guild.iconURL());

      const options = [{
        label: this.client.language.setup.buttons.create_all, 
        value: "create_all", 
        emoji: this.client.config.emojis.create_all
      }, {
        label: this.client.language.setup.buttons.create_channels, 
        value: "create_channels", 
        emoji: this.client.config.emojis.create_channels
      }, {
        label: this.client.language.setup.buttons.create_roles,
        value: "create_roles",
        emoji: this.client.config.emojis.create_roles
      }, {
        label: this.client.language.setup.buttons.create_emojis,
        value: "create_emojis",
        emoji: this.client.config.emojis.create_emojis
      }];
      
      let sMenu = new Discord.StringSelectMenuBuilder()
        .setCustomId("category_select")
        .setPlaceholder(this.client.config.setup.create_placeholder)
        .addOptions(options);

      let row = new Discord.ActionRowBuilder()
        .addComponents(sMenu);

      let m = await message.channel.send({ embeds: [embed], components: [row] });

      const filter = (i) => i.customId == "category_select" && i.user.id == message.author.id;
      let collector = m.createMessageComponentCollector({ filter, componentType: Discord.ComponentType.SelectMenu, time: 120000 });

      callData.embed = embed;
      callData.row = row;
      callData.m = m;
      callData.toggleComponents = toggleComponents;

      collector.on("collect", async(b) => {
        await b.deferUpdate();
        let value = b.values[0];
        await toggleComponents(m);
        if(value == "create_all") {
          message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.setup.creating_all, this.client.embeds.success_color)]});
          await this.client.utils.createRoles(this.client, message, template, callData, true);
          setTimeout(async() => {
            await this.client.utils.createEmojis(this.client, message, template, callData, true);
            await this.client.utils.createChannels(this.client, message, template, callData, true);
            await this.client.utils.createExtras(this.client, message, template, callData, true);
          }, 10000);
        } else if(value == "create_channels") {
          message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.setup.creating_channels, this.client.embeds.success_color)]});
          await this.client.utils.createChannels(this.client, message, template, callData);
        } else if(value == "create_roles") {
          message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.setup.creating_roles, this.client.embeds.success_color)]});
          await this.client.utils.createRoles(this.client, message, template, callData);
        } else if(value == "create_emojis") {
          message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.setup.creating_emojis, this.client.embeds.success_color)]});
          await this.client.utils.createEmojis(this.client, message, template, callData);
        }
      });

      collector.on("end", (collected, reason) => {
        if(reason != "time") return;
        row.components[0].setDisabled(true);
          
        m.edit({ embeds: [embed], components: [row] });
      });
  }
  async slashRun(interaction, args) {
    let template = interaction.options.getString("name");

    if(!template) return interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
      this.client.language.titles.error, this.client.language.setup.no_template, this.client.embeds.error_color)]});
    if(!this.client.template(template)) return interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
      this.client.language.titles.error, this.client.language.setup.invalid_template, this.client.embeds.error_color)]});

    let embed = new Discord.EmbedBuilder()
      .setColor(this.client.embeds.setup.create.color);
    
    if(this.client.embeds.setup.create.title) embed.setTitle(this.client.embeds.setup.create.title);
    
    if(this.client.embeds.setup.create.description) embed.setDescription(this.client.embeds.setup.create.description.replace("<template>", template));
    
    let field = this.client.embeds.setup.create.fields;
    for(let i = 0; i < this.client.embeds.setup.create.fields.length; i++) {
      embed.addFields([{ name: field[i].title, value: field[i].description.replace("<template>", template) }]);
    }

    if(this.client.embeds.setup.create.footer == true) embed.setFooter({ text: interaction.user.username, iconURL: interaction.user.displayAvatarURL() }).setTimestamp();
    if(this.client.embeds.setup.create.thumbnail == true) embed.setThumbnail(interaction.guild.iconURL());

      const options = [{
        label: this.client.language.setup.buttons.create_all, 
        value: "create_all", 
        emoji: this.client.config.emojis.create_all
      }, {
        label: this.client.language.setup.buttons.create_channels, 
        value: "create_channels", 
        emoji: this.client.config.emojis.create_channels
      }, {
        label: this.client.language.setup.buttons.create_roles,
        value: "create_roles",
        emoji: this.client.config.emojis.create_roles
      }, {
        label: this.client.language.setup.buttons.create_emojis,
        value: "create_emojis",
        emoji: this.client.config.emojis.create_emojis
      }];
      
      let sMenu = new Discord.StringSelectMenuBuilder()
        .setCustomId("category_select")
        .setPlaceholder(this.client.config.setup.create_placeholder)
        .addOptions(options);

      let row = new Discord.ActionRowBuilder()
        .addComponents(sMenu);

      let m = await interaction.reply({ embeds: [embed], components: [row] });

      const filter = (i) => i.customId == "category_select" && i.user.id == interaction.user.id;
      let collector = m.createMessageComponentCollector({ filter, componentType: Discord.ComponentType.SelectMenu, time: 120000 });

      callData.embed = embed;
      callData.row = row;
      callData.m = m;
      callData.toggleComponents = toggleComponents;

      collector.on("collect", async(b) => {
        await b.deferUpdate();
        let value = b.values[0];
        await toggleComponents(m);
        if(value == "create_all") {
          interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.setup.creating_all, this.client.embeds.success_color)]});
          await this.client.utils.createRoles(this.client, interaction, template, callData, true);
          setTimeout(async() => {
            await this.client.utils.createEmojis(this.client, interaction, template, callData, true);
            await this.client.utils.createChannels(this.client, interaction, template, callData, true);
            await this.client.utils.createExtras(this.client, interaction, template, callData, true);
          }, 10000);
        } else if(value == "create_channels") {
          interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.setup.creating_channels, this.client.embeds.success_color)]});
          await this.client.utils.createChannels(this.client, interaction, template, callData);
        } else if(value == "create_roles") {
          interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.setup.creating_roles, this.client.embeds.success_color)]});
          await this.client.utils.createRoles(this.client, interaction, template, callData);
        } else if(value == "create_emojis") {
          interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
            this.client.embeds.title, this.client.language.setup.creating_emojis, this.client.embeds.success_color)]});
          await this.client.utils.createEmojis(this.client, interaction, template, callData);
        }
      });

      collector.on("end", (collected, reason) => {
        if(reason != "time") return;
        row.components[0].setDisabled(true);
          
        m.edit({ embeds: [embed], components: [row] });
      });
  }
};

const toggleComponents = async(m) => {
  let disabledComponents = callData.row.components.map(c => c.setDisabled(!c.disabled));
  let newRow = new Discord.ActionRowBuilder()
    .addComponents(disabledComponents);

  await m.edit({ embeds: [callData.embed], components: [newRow] });
}
