const { SlashCommandBuilder } = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
} = require("../../utils/embedBuilder.js");
const {
  canManageRole,
  userCanManageRole,
} = require("../../utils/permissions.js");
const { isValidDelayTime } = require("../../utils/validators.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("autorole")
    .setDescription("Quản lý hệ thống auto-role cho thành viên và bot")
    .addSubcommandGroup((group) =>
      group
        .setName("human")
        .setDescription("Quản lý auto-role cho thành viên người")
        .addSubcommand((subcommand) =>
          subcommand
            .setName("add")
            .setDescription("Thêm role tự động cho thành viên người mới")
            .addRoleOption((option) =>
              option
                .setName("role")
                .setDescription("Role sẽ được gán tự động")
                .setRequired(true)
            )
            .addIntegerOption((option) =>
              option
                .setName("delay")
                .setDescription(
                  "Thời gian chờ trước khi gán role (giây, 0-3600)"
                )
                .setRequired(false)
                .setMinValue(0)
                .setMaxValue(3600)
            )
        )
        .addSubcommand((subcommand) =>
          subcommand
            .setName("remove")
            .setDescription(
              "Xóa role khỏi danh sách auto-role cho thành viên người"
            )
            .addRoleOption((option) =>
              option
                .setName("role")
                .setDescription("Role cần xóa khỏi auto-role")
                .setRequired(true)
            )
        )
        .addSubcommand((subcommand) =>
          subcommand
            .setName("list")
            .setDescription("Xem danh sách auto-role cho thành viên người")
        )
        .addSubcommand((subcommand) =>
          subcommand
            .setName("clear")
            .setDescription("Xóa tất cả auto-role cho thành viên người")
        )
    )
    .addSubcommandGroup((group) =>
      group
        .setName("bot")
        .setDescription("Quản lý auto-role cho bot")
        .addSubcommand((subcommand) =>
          subcommand
            .setName("add")
            .setDescription("Thêm role tự động cho bot mới")
            .addRoleOption((option) =>
              option
                .setName("role")
                .setDescription("Role sẽ được gán tự động cho bot")
                .setRequired(true)
            )
            .addIntegerOption((option) =>
              option
                .setName("delay")
                .setDescription(
                  "Thời gian chờ trước khi gán role (giây, 0-3600)"
                )
                .setRequired(false)
                .setMinValue(0)
                .setMaxValue(3600)
            )
        )
        .addSubcommand((subcommand) =>
          subcommand
            .setName("remove")
            .setDescription("Xóa role khỏi danh sách auto-role cho bot")
            .addRoleOption((option) =>
              option
                .setName("role")
                .setDescription("Role cần xóa khỏi auto-role")
                .setRequired(true)
            )
        )
        .addSubcommand((subcommand) =>
          subcommand
            .setName("list")
            .setDescription("Xem danh sách auto-role cho bot")
        )
        .addSubcommand((subcommand) =>
          subcommand
            .setName("clear")
            .setDescription("Xóa tất cả auto-role cho bot")
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("list")
        .setDescription("Xem tất cả auto-role (cả thành viên và bot)")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("clear")
        .setDescription("Xóa tất cả auto-role")
        .addStringOption((option) =>
          option
            .setName("type")
            .setDescription("Loại auto-role cần xóa")
            .setRequired(false)
            .addChoices(
              { name: "👤 Human - Chỉ thành viên người", value: "human" },
              { name: "🤖 Bot - Chỉ bot", value: "bot" },
              { name: "🗑️ All - Tất cả", value: "all" }
            )
        )
    ),
  category: "autoroles",
  adminOnly: true,
  manageServer: false,

  async execute(interaction, client) {
    const subcommandGroup = interaction.options.getSubcommandGroup();
    const subcommand = interaction.options.getSubcommand();
    const guildId = interaction.guild.id;

    if (subcommandGroup) {
      // Handle subcommand groups (human/bot)
      const targetType = subcommandGroup; // 'human' or 'bot'

      switch (subcommand) {
        case "add":
          await handleAdd(interaction, client, guildId, targetType);
          break;
        case "remove":
          await handleRemove(interaction, client, guildId, targetType);
          break;
        case "list":
          await handleList(interaction, client, guildId, targetType);
          break;
        case "clear":
          await handleClear(interaction, client, guildId, targetType);
          break;
      }
    } else {
      // Handle direct subcommands
      switch (subcommand) {
        case "list":
          await handleListAll(interaction, client, guildId);
          break;
        case "clear":
          await handleClearAll(interaction, client, guildId);
          break;
      }
    }
  },
};

async function handleAdd(interaction, client, guildId, targetType) {
  const role = interaction.options.getRole("role");
  const delay = interaction.options.getInteger("delay") || 0;

  // Kiểm tra quyền của user
  const userCanManage = userCanManageRole(interaction.member, role);
  if (!userCanManage.canManage) {
    const errorEmbed = createErrorEmbed(
      "Không có quyền!",
      userCanManage.reason
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Kiểm tra quyền của bot
  const botCanManage = canManageRole(interaction.guild, role);
  if (!botCanManage.canManage) {
    const errorEmbed = createErrorEmbed(
      "Bot không thể quản lý role!",
      botCanManage.reason
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Kiểm tra delay time
  if (!isValidDelayTime(delay)) {
    const errorEmbed = createErrorEmbed(
      "Thời gian chờ không hợp lệ!",
      "Thời gian chờ phải từ 0 đến 3600 giây (1 giờ)."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    // Kiểm tra role đã tồn tại chưa
    const existingRoles = await client.db.getAutoRoles(guildId, targetType);
    const existingRole = existingRoles.find((r) => r.role_id === role.id);

    if (existingRole) {
      const errorEmbed = createErrorEmbed(
        "Role đã tồn tại!",
        `Role ${role} đã có trong danh sách auto-role cho ${
          targetType === "human" ? "thành viên người" : "bot"
        }.\n\nSử dụng lệnh remove để xóa trước khi thêm lại.`
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    // Thêm auto role
    await client.db.addAutoRole(guildId, role.id, role.name, targetType, delay);

    const targetName = targetType === "human" ? "thành viên người" : "bot";
    const successEmbed = createSuccessEmbed(
      "Đã thêm auto-role!",
      `**Role:** ${role}\n` +
        `**Đối tượng:** ${targetName}\n` +
        `**Thời gian chờ:** ${
          delay > 0 ? `${delay} giây` : "Ngay lập tức"
        }\n\n` +
        `Từ giờ, ${targetName} mới sẽ tự động nhận role này khi tham gia server.`
    );

    if (delay > 0) {
      successEmbed.addFields({
        name: "⏰ Lưu ý về thời gian chờ",
        value: `Role sẽ được gán sau **${delay} giây** kể từ khi ${targetName} tham gia.\nĐiều này giúp tránh spam và cho phép ${
          targetType === "human"
            ? "thành viên đọc quy tắc trước"
            : "bot khởi động hoàn tất"
        }.`,
        inline: false,
      });
    }

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi thêm auto role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể thêm auto-role. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleRemove(interaction, client, guildId, targetType) {
  const role = interaction.options.getRole("role");

  try {
    // Kiểm tra role có trong danh sách không
    const existingRoles = await client.db.getAutoRoles(guildId, targetType);
    const existingRole = existingRoles.find((r) => r.role_id === role.id);

    if (!existingRole) {
      const targetName = targetType === "human" ? "thành viên người" : "bot";
      const errorEmbed = createErrorEmbed(
        "Role không tồn tại!",
        `Role ${role} không có trong danh sách auto-role cho ${targetName}.`
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    // Xóa auto role
    await client.db.removeAutoRole(guildId, role.id, targetType);

    const targetName = targetType === "human" ? "thành viên người" : "bot";
    const successEmbed = createSuccessEmbed(
      "Đã xóa auto-role!",
      `Role ${role} đã được xóa khỏi danh sách auto-role cho ${targetName}.\n\n${
        targetName.charAt(0).toUpperCase() + targetName.slice(1)
      } mới sẽ không còn nhận role này tự động.`
    );

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi xóa auto role:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể xóa auto-role. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleList(interaction, client, guildId, targetType) {
  try {
    const autoRoles = await client.db.getAutoRoles(guildId, targetType);

    const targetName = targetType === "human" ? "thành viên người" : "bot";
    const targetEmoji = targetType === "human" ? "👤" : "🤖";

    if (autoRoles.length === 0) {
      const infoEmbed = createInfoEmbed(
        `Chưa có auto-role cho ${targetName}`,
        `Server chưa thiết lập auto-role nào cho ${targetName}.\n\nSử dụng \`/autorole ${targetType} add\` để thêm auto-role!`
      );
      return await interaction.reply({ embeds: [infoEmbed] });
    }

    const embed = createInfoEmbed(
      `${targetEmoji} Danh sách Auto-Role cho ${
        targetName.charAt(0).toUpperCase() + targetName.slice(1)
      } (${autoRoles.length})`,
      `Dưới đây là các role sẽ được gán tự động cho ${targetName} mới:`
    );

    let roleList = "";
    let validRoles = 0;

    for (const autoRole of autoRoles) {
      const role = interaction.guild.roles.cache.get(autoRole.role_id);

      if (role) {
        const delay =
          autoRole.delay_seconds > 0
            ? ` (sau ${autoRole.delay_seconds}s)`
            : " (ngay lập tức)";
        roleList += `• ${role}${delay}\n`;
        validRoles++;
      } else {
        // Role đã bị xóa, xóa khỏi database
        await client.db.removeAutoRole(guildId, autoRole.role_id, targetType);
        roleList += `• ~~${autoRole.role_name}~~ *(đã bị xóa)*\n`;
      }
    }

    if (roleList) {
      embed.addFields({
        name: `🎭 Danh sách Role`,
        value: roleList,
        inline: false,
      });
    }

    if (validRoles !== autoRoles.length) {
      embed.addFields({
        name: "⚠️ Lưu ý",
        value:
          "Một số role đã bị xóa khỏi server và đã được tự động loại bỏ khỏi danh sách.",
        inline: false,
      });
    }

    embed.addFields({
      name: "🔧 Quản lý",
      value:
        `• \`/autorole ${targetType} add\` - Thêm auto-role\n` +
        `• \`/autorole ${targetType} remove\` - Xóa auto-role\n` +
        `• \`/autorole ${targetType} clear\` - Xóa tất cả`,
      inline: false,
    });

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error("Lỗi khi lấy danh sách auto roles:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể lấy danh sách auto-role. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleClear(interaction, client, guildId, targetType) {
  try {
    const autoRoles = await client.db.getAutoRoles(guildId, targetType);

    const targetName = targetType === "human" ? "thành viên người" : "bot";

    if (autoRoles.length === 0) {
      const infoEmbed = createInfoEmbed(
        "Không có gì để xóa",
        `Server chưa có auto-role nào cho ${targetName}.`
      );
      return await interaction.reply({ embeds: [infoEmbed], ephemeral: true });
    }

    // Xóa tất cả auto roles cho target type
    await client.db.clearAutoRoles(guildId, targetType);

    const successEmbed = createSuccessEmbed(
      "Đã xóa tất cả auto-role!",
      `Đã xóa ${autoRoles.length} auto-role cho ${targetName} khỏi server.\n\n${
        targetName.charAt(0).toUpperCase() + targetName.slice(1)
      } mới sẽ không còn nhận role tự động.`
    );

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi xóa tất cả auto roles:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể xóa auto-role. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleListAll(interaction, client, guildId) {
  try {
    const humanRoles = await client.db.getAutoRoles(guildId, "human");
    const botRoles = await client.db.getAutoRoles(guildId, "bot");

    if (humanRoles.length === 0 && botRoles.length === 0) {
      const infoEmbed = createInfoEmbed(
        "Chưa có auto-role nào",
        "Server chưa thiết lập auto-role nào.\n\nSử dụng `/autorole human add` hoặc `/autorole bot add` để thêm auto-role!"
      );
      return await interaction.reply({ embeds: [infoEmbed] });
    }

    const embed = createInfoEmbed(
      `🎭 Tất cả Auto-Role (${humanRoles.length + botRoles.length})`,
      "Dưới đây là tất cả auto-role được thiết lập trong server:"
    );

    // Human roles
    if (humanRoles.length > 0) {
      let humanList = "";
      for (const autoRole of humanRoles) {
        const role = interaction.guild.roles.cache.get(autoRole.role_id);
        if (role) {
          const delay =
            autoRole.delay_seconds > 0
              ? ` (sau ${autoRole.delay_seconds}s)`
              : " (ngay lập tức)";
          humanList += `• ${role}${delay}\n`;
        }
      }

      if (humanList) {
        embed.addFields({
          name: "👤 Auto-Role cho Thành viên Người",
          value: humanList,
          inline: false,
        });
      }
    }

    // Bot roles
    if (botRoles.length > 0) {
      let botList = "";
      for (const autoRole of botRoles) {
        const role = interaction.guild.roles.cache.get(autoRole.role_id);
        if (role) {
          const delay =
            autoRole.delay_seconds > 0
              ? ` (sau ${autoRole.delay_seconds}s)`
              : " (ngay lập tức)";
          botList += `• ${role}${delay}\n`;
        }
      }

      if (botList) {
        embed.addFields({
          name: "🤖 Auto-Role cho Bot",
          value: botList,
          inline: false,
        });
      }
    }

    embed.addFields({
      name: "🔧 Quản lý",
      value:
        "• `/autorole human` - Quản lý auto-role cho thành viên\n" +
        "• `/autorole bot` - Quản lý auto-role cho bot\n" +
        "• `/autorole clear` - Xóa tất cả auto-role",
      inline: false,
    });

    await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error("Lỗi khi lấy tất cả auto roles:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể lấy danh sách auto-role. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleClearAll(interaction, client, guildId) {
  const type = interaction.options.getString("type") || "all";

  try {
    let deletedCount = 0;
    let targetDescription = "";

    if (type === "all") {
      const humanRoles = await client.db.getAutoRoles(guildId, "human");
      const botRoles = await client.db.getAutoRoles(guildId, "bot");
      deletedCount = humanRoles.length + botRoles.length;

      if (deletedCount === 0) {
        const infoEmbed = createInfoEmbed(
          "Không có gì để xóa",
          "Server chưa có auto-role nào."
        );
        return await interaction.reply({
          embeds: [infoEmbed],
          ephemeral: true,
        });
      }

      await client.db.clearAutoRoles(guildId);
      targetDescription = "tất cả auto-role";
    } else {
      const roles = await client.db.getAutoRoles(guildId, type);
      deletedCount = roles.length;

      const targetName = type === "human" ? "thành viên người" : "bot";

      if (deletedCount === 0) {
        const infoEmbed = createInfoEmbed(
          "Không có gì để xóa",
          `Server chưa có auto-role nào cho ${targetName}.`
        );
        return await interaction.reply({
          embeds: [infoEmbed],
          ephemeral: true,
        });
      }

      await client.db.clearAutoRoles(guildId, type);
      targetDescription = `auto-role cho ${targetName}`;
    }

    const successEmbed = createSuccessEmbed(
      "Đã xóa auto-role!",
      `Đã xóa ${deletedCount} ${targetDescription} khỏi server.\n\nThành viên mới sẽ không còn nhận role tự động.`
    );

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi xóa tất cả auto roles:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi hệ thống!",
      "Không thể xóa auto-role. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}
