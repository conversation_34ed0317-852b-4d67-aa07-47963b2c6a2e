const { SlashCommandBuilder } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { canManageRole, userCanManageRole } = require('../../utils/permissions.js');
const { isValidDelayTime } = require('../../utils/validators.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('autorole-setup')
        .setDescription('Thiết lập hệ thống auto-role cho thành viên mới')
        .addSubcommand(subcommand =>
            subcommand
                .setName('add')
                .setDescription('Thêm role tự động cho thành viên mới')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role sẽ được gán tự động')
                        .setRequired(true)
                )
                .addIntegerOption(option =>
                    option.setName('delay')
                        .setDescription('Thời gian chờ trước khi gán role (giây, 0-3600)')
                        .setRequired(false)
                        .setMinValue(0)
                        .setMaxValue(3600)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('remove')
                .setDescription('Xóa role khỏi danh sách auto-role')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role cần xóa khỏi auto-role')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('Xem danh sách các auto-role hiện tại')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('clear')
                .setDescription('Xóa tất cả auto-role')
        ),
    category: 'autoroles',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();
        const guildId = interaction.guild.id;
        
        switch (subcommand) {
            case 'add':
                await handleAdd(interaction, client, guildId);
                break;
            case 'remove':
                await handleRemove(interaction, client, guildId);
                break;
            case 'list':
                await handleList(interaction, client, guildId);
                break;
            case 'clear':
                await handleClear(interaction, client, guildId);
                break;
        }
    },
};

async function handleAdd(interaction, client, guildId) {
    const role = interaction.options.getRole('role');
    const delay = interaction.options.getInteger('delay') || 0;
    
    // Kiểm tra quyền của user
    const userCanManage = userCanManageRole(interaction.member, role);
    if (!userCanManage.canManage) {
        const errorEmbed = createErrorEmbed(
            'Không có quyền!',
            userCanManage.reason
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    // Kiểm tra quyền của bot
    const botCanManage = canManageRole(interaction.guild, role);
    if (!botCanManage.canManage) {
        const errorEmbed = createErrorEmbed(
            'Bot không thể quản lý role!',
            botCanManage.reason
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    // Kiểm tra delay time
    if (!isValidDelayTime(delay)) {
        const errorEmbed = createErrorEmbed(
            'Thời gian chờ không hợp lệ!',
            'Thời gian chờ phải từ 0 đến 3600 giây (1 giờ).'
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    try {
        // Kiểm tra role đã tồn tại chưa
        const existingRoles = await client.db.getAutoRoles(guildId);
        const existingRole = existingRoles.find(r => r.role_id === role.id);
        
        if (existingRole) {
            const errorEmbed = createErrorEmbed(
                'Role đã tồn tại!',
                `Role ${role} đã có trong danh sách auto-role.\n\nSử dụng \`/autorole-setup remove\` để xóa trước khi thêm lại.`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        // Thêm auto role
        await client.db.addAutoRole(guildId, role.id, role.name, delay);
        
        const successEmbed = createSuccessEmbed(
            'Đã thêm auto-role!',
            `**Role:** ${role}\n` +
            `**Thời gian chờ:** ${delay > 0 ? `${delay} giây` : 'Ngay lập tức'}\n\n` +
            `Từ giờ, thành viên mới sẽ tự động nhận role này khi tham gia server.`
        );
        
        if (delay > 0) {
            successEmbed.addFields({
                name: '⏰ Lưu ý về thời gian chờ',
                value: `Role sẽ được gán sau **${delay} giây** kể từ khi thành viên tham gia.\nĐiều này giúp tránh spam và cho phép thành viên đọc quy tắc trước.`,
                inline: false
            });
        }
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi thêm auto role:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể thêm auto-role. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleRemove(interaction, client, guildId) {
    const role = interaction.options.getRole('role');
    
    try {
        // Kiểm tra role có trong danh sách không
        const existingRoles = await client.db.getAutoRoles(guildId);
        const existingRole = existingRoles.find(r => r.role_id === role.id);
        
        if (!existingRole) {
            const errorEmbed = createErrorEmbed(
                'Role không tồn tại!',
                `Role ${role} không có trong danh sách auto-role.`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        // Xóa auto role
        await client.db.removeAutoRole(guildId, role.id);
        
        const successEmbed = createSuccessEmbed(
            'Đã xóa auto-role!',
            `Role ${role} đã được xóa khỏi danh sách auto-role.\n\nThành viên mới sẽ không còn nhận role này tự động.`
        );
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi xóa auto role:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể xóa auto-role. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleList(interaction, client, guildId) {
    try {
        const autoRoles = await client.db.getAutoRoles(guildId);
        
        if (autoRoles.length === 0) {
            const infoEmbed = createInfoEmbed(
                'Chưa có auto-role',
                'Server chưa thiết lập auto-role nào.\n\nSử dụng `/autorole-setup add` để thêm auto-role!'
            );
            return await interaction.reply({ embeds: [infoEmbed] });
        }
        
        const embed = createInfoEmbed(
            `Danh sách Auto-Role (${autoRoles.length})`,
            'Dưới đây là các role sẽ được gán tự động cho thành viên mới:'
        );
        
        let roleList = '';
        let validRoles = 0;
        
        for (const autoRole of autoRoles) {
            const role = interaction.guild.roles.cache.get(autoRole.role_id);
            
            if (role) {
                const delay = autoRole.delay_seconds > 0 ? ` (sau ${autoRole.delay_seconds}s)` : ' (ngay lập tức)';
                roleList += `• ${role}${delay}\n`;
                validRoles++;
            } else {
                // Role đã bị xóa, xóa khỏi database
                await client.db.removeAutoRole(guildId, autoRole.role_id);
                roleList += `• ~~${autoRole.role_name}~~ *(đã bị xóa)*\n`;
            }
        }
        
        if (roleList) {
            embed.addFields({
                name: '🎭 Danh sách Role',
                value: roleList,
                inline: false
            });
        }
        
        if (validRoles !== autoRoles.length) {
            embed.addFields({
                name: '⚠️ Lưu ý',
                value: 'Một số role đã bị xóa khỏi server và đã được tự động loại bỏ khỏi danh sách.',
                inline: false
            });
        }
        
        embed.addFields({
            name: '🔧 Quản lý',
            value: '• `/autorole-setup add` - Thêm auto-role\n' +
                   '• `/autorole-setup remove` - Xóa auto-role\n' +
                   '• `/autorole-setup clear` - Xóa tất cả',
            inline: false
        });
        
        await interaction.reply({ embeds: [embed] });
        
    } catch (error) {
        console.error('Lỗi khi lấy danh sách auto roles:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể lấy danh sách auto-role. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleClear(interaction, client, guildId) {
    try {
        const autoRoles = await client.db.getAutoRoles(guildId);
        
        if (autoRoles.length === 0) {
            const infoEmbed = createInfoEmbed(
                'Không có gì để xóa',
                'Server chưa có auto-role nào.'
            );
            return await interaction.reply({ embeds: [infoEmbed], ephemeral: true });
        }
        
        // Xóa tất cả auto roles
        for (const autoRole of autoRoles) {
            await client.db.removeAutoRole(guildId, autoRole.role_id);
        }
        
        const successEmbed = createSuccessEmbed(
            'Đã xóa tất cả auto-role!',
            `Đã xóa ${autoRoles.length} auto-role khỏi server.\n\nThành viên mới sẽ không còn nhận role tự động.`
        );
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi xóa tất cả auto roles:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể xóa auto-role. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}
