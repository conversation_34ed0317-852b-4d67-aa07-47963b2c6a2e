(function(_0x5f482a,_0x6b13eb){function _0x1984c9(_0x5e41fa,_0x4a27d1){return _0x4b6d(_0x4a27d1- -0x25a,_0x5e41fa);}const _0x1edff6=_0x5f482a();while(!![]){try{const _0x239e8e=-parseInt(_0x1984c9(-0x1ad,-0x190))/0x1*(-parseInt(_0x1984c9(-0x193,-0x197))/0x2)+parseInt(_0x1984c9(-0x1ac,-0x18a))/0x3*(-parseInt(_0x1984c9(-0x16e,-0x174))/0x4)+-parseInt(_0x1984c9(-0x15d,-0x175))/0x5+-parseInt(_0x1984c9(-0x1a4,-0x1b7))/0x6*(-parseInt(_0x1984c9(-0x198,-0x1bd))/0x7)+-parseInt(_0x1984c9(-0x19e,-0x183))/0x8*(parseInt(_0x1984c9(-0x1c4,-0x1a8))/0x9)+parseInt(_0x1984c9(-0x194,-0x185))/0xa+-parseInt(_0x1984c9(-0x1a8,-0x1ac))/0xb*(parseInt(_0x1984c9(-0x1d5,-0x1b9))/0xc);if(_0x239e8e===_0x6b13eb)break;else _0x1edff6['push'](_0x1edff6['shift']());}catch(_0x5a17f2){_0x1edff6['push'](_0x1edff6['shift']());}}}(_0x16da,0x3fc79));function _0x16da(){const _0xee9aad=['transactionLogs','noPermission','reply','addStringOption','The\x20user\x20to\x20take\x20money\x20from','addUserOption','../../../models/UserData','Administrator','Messages','insufficientFunds','1003123ddydHn','#00FF00','balance','bank','2278791EmIOoI','both','load','Take\x20money\x20from\x20a\x20user','Specify\x20whether\x20to\x20give\x20to\x20balance,\x20bank,\x20or\x20both','setColor','exports','user','permissions','type','utf8','addSubcommand','./lang.yml','setRequired','The\x20amount\x20of\x20money\x20to\x20take','give','getString','2bFYHMf','Specify\x20whether\x20to\x20take\x20from\x20balance,\x20bank,\x20or\x20both','Give\x20money\x20to\x20a\x20user','guild','Economy','save','push','451499ZkjbBc','admin-give-bank','admin-take-balance','addChoices','readFileSync','amount','3CdGLPd','getSubcommand','admin-take-bank','getUser','roles','3783140aOyqnB','js-yaml','8zjFuEg','./config.yml','The\x20user\x20to\x20set\x20the\x20balance/bank\x20for','map','The\x20amount\x20of\x20money\x20to\x20give','getInteger','adminGive','#FF0000','setDescription','member','some','includes','./Utility/helpers','options','659375ElymAv','915388TJCvfL','addIntegerOption','adminSet','isArray','setName','administrator','7TANVYo','#0000FF','findOne','take','24rpapzk','set','1366410rfdAkr'];_0x16da=function(){return _0xee9aad;};return _0x16da();}function _0x4b6d(_0x2f6268,_0x26c9bb){const _0x16daff=_0x16da();return _0x4b6d=function(_0x4b6d94,_0x166c5d){_0x4b6d94=_0x4b6d94-0x9b;let _0x34ed9e=_0x16daff[_0x4b6d94];return _0x34ed9e;},_0x4b6d(_0x2f6268,_0x26c9bb);}const {SlashCommandBuilder,EmbedBuilder,PermissionFlagsBits}=require('discord.js'),User=require(_0x32a5b6(0x247,0x240)),fs=require('fs'),yaml=require(_0x32a5b6(0x273,0x298)),config=yaml[_0x32a5b6(0x251,0x23b)](fs[_0x32a5b6(0x26b,0x24d)](_0x32a5b6(0x275,0x272),_0x32a5b6(0x259,0x27d))),lang=yaml['load'](fs[_0x32a5b6(0x26b,0x25d)](_0x32a5b6(0x25b,0x26e),_0x32a5b6(0x259,0x23b)));function _0x32a5b6(_0x10c7bc,_0x548b58){return _0x4b6d(_0x10c7bc-0x19d,_0x548b58);}const {replacePlaceholders}=require(_0x32a5b6(0x280,0x2a4));module[_0x32a5b6(0x255,0x236)]={'data':new SlashCommandBuilder()[_0x32a5b6(0x238,0x23e)]('economy')[_0x32a5b6(0x27c,0x26f)]('Economy\x20administration\x20commands')[_0x32a5b6(0x25a,0x245)](_0x516a8b=>_0x516a8b['setName']('give')[_0x32a5b6(0x27c,0x28a)](_0x32a5b6(0x262,0x269))[_0x32a5b6(0x246,0x24c)](_0x277f1b=>_0x277f1b[_0x32a5b6(0x238,0x24a)]('user')[_0x32a5b6(0x27c,0x293)]('The\x20user\x20to\x20give\x20money\x20to')[_0x32a5b6(0x25c,0x27b)](!![]))[_0x32a5b6(0x284,0x269)](_0x1a820a=>_0x1a820a[_0x32a5b6(0x238,0x214)]('amount')[_0x32a5b6(0x27c,0x29d)](_0x32a5b6(0x278,0x29b))[_0x32a5b6(0x25c,0x280)](!![]))['addStringOption'](_0x31ac0a=>_0x31ac0a[_0x32a5b6(0x238,0x25e)](_0x32a5b6(0x258,0x256))[_0x32a5b6(0x27c,0x293)](_0x32a5b6(0x253,0x249))['setRequired'](!![])['addChoices']({'name':_0x32a5b6(0x24d,0x23c),'value':'balance'},{'name':_0x32a5b6(0x24e,0x256),'value':_0x32a5b6(0x24e,0x246)},{'name':_0x32a5b6(0x250,0x250),'value':_0x32a5b6(0x250,0x22b)})))[_0x32a5b6(0x25a,0x249)](_0x233294=>_0x233294[_0x32a5b6(0x238,0x252)]('take')[_0x32a5b6(0x27c,0x288)](_0x32a5b6(0x252,0x24a))[_0x32a5b6(0x246,0x24a)](_0x3ca784=>_0x3ca784[_0x32a5b6(0x238,0x241)](_0x32a5b6(0x256,0x279))[_0x32a5b6(0x27c,0x27e)](_0x32a5b6(0x245,0x21f))[_0x32a5b6(0x25c,0x278)](!![]))[_0x32a5b6(0x284,0x287)](_0x49727a=>_0x49727a['setName'](_0x32a5b6(0x26c,0x247))[_0x32a5b6(0x27c,0x25f)](_0x32a5b6(0x25d,0x25e))[_0x32a5b6(0x25c,0x251)](!![]))[_0x32a5b6(0x244,0x25f)](_0x32f5c9=>_0x32f5c9[_0x32a5b6(0x238,0x254)]('type')[_0x32a5b6(0x27c,0x2a1)](_0x32a5b6(0x261,0x23d))[_0x32a5b6(0x25c,0x25e)](!![])[_0x32a5b6(0x26a,0x263)]({'name':'balance','value':_0x32a5b6(0x24d,0x232)},{'name':_0x32a5b6(0x24e,0x25f),'value':_0x32a5b6(0x24e,0x275)},{'name':_0x32a5b6(0x250,0x266),'value':_0x32a5b6(0x250,0x241)})))['addSubcommand'](_0x25df58=>_0x25df58[_0x32a5b6(0x238,0x244)](_0x32a5b6(0x23f,0x229))[_0x32a5b6(0x27c,0x293)]('Set\x20a\x20user\x27s\x20balance\x20or\x20bank')[_0x32a5b6(0x246,0x249)](_0x2e9cf3=>_0x2e9cf3['setName'](_0x32a5b6(0x256,0x25d))[_0x32a5b6(0x27c,0x25c)](_0x32a5b6(0x276,0x262))[_0x32a5b6(0x25c,0x23a)](!![]))['addIntegerOption'](_0x46e110=>_0x46e110['setName'](_0x32a5b6(0x26c,0x27d))['setDescription']('The\x20new\x20balance/bank\x20amount')['setRequired'](!![]))['addStringOption'](_0x410dd2=>_0x410dd2[_0x32a5b6(0x238,0x253)]('type')[_0x32a5b6(0x27c,0x271)]('Specify\x20whether\x20to\x20set\x20balance,\x20bank,\x20or\x20both')[_0x32a5b6(0x25c,0x258)](!![])[_0x32a5b6(0x26a,0x27e)]({'name':'balance','value':_0x32a5b6(0x24d,0x269)},{'name':_0x32a5b6(0x24e,0x26e),'value':_0x32a5b6(0x24e,0x25f)},{'name':'both','value':_0x32a5b6(0x250,0x23e)}))),'category':_0x32a5b6(0x264,0x254),async 'execute'(_0x486ee0){const _0x4bc6c1=_0x486ee0['options'][_0x55eb29(0x327,0x317)](),_0x571e04=_0x486ee0[_0x55eb29(0x308,0x32a)][_0x55eb29(0x30e,0x319)](_0x55eb29(0x317,0x2ff)),_0x55d201=_0x486ee0[_0x55eb29(0x334,0x32a)][_0x55eb29(0x2fa,0x322)](_0x55eb29(0x31f,0x315)),_0x517b48=_0x486ee0[_0x55eb29(0x341,0x32a)][_0x55eb29(0x329,0x308)]('type'),_0x4e4ee7=config[_0x55eb29(0x2e7,0x30d)][_0x55eb29(0x2d4,0x2e2)],_0x231ca5=_0x486ee0[_0x55eb29(0x344,0x326)][_0x55eb29(0x30f,0x31a)]['cache'][_0x55eb29(0x303,0x320)](_0x40d671=>_0x40d671['id']);if(!_0x486ee0[_0x55eb29(0x34a,0x326)][_0x55eb29(0x2fd,0x300)]['has'](PermissionFlagsBits[_0x55eb29(0x2d3,0x2f1)])&&!_0x231ca5[_0x55eb29(0x315,0x327)](_0x5a1e91=>_0x4e4ee7[_0x55eb29(0x34c,0x328)](_0x5a1e91)))return _0x486ee0[_0x55eb29(0x2c4,0x2ec)]({'content':lang['Economy'][_0x55eb29(0x315,0x2f2)][_0x55eb29(0x2f9,0x2eb)],'ephemeral':!![]});let _0x3ad3da={};(_0x517b48===_0x55eb29(0x315,0x2f6)||_0x517b48===_0x55eb29(0x2e8,0x2f9))&&(_0x3ad3da[_0x55eb29(0x2fb,0x2f6)]=0x1);function _0x55eb29(_0x4e9910,_0xfdc4b1){return _0x32a5b6(_0xfdc4b1-0xa9,_0x4e9910);}(_0x517b48===_0x55eb29(0x30b,0x2f7)||_0x517b48===_0x55eb29(0x2ef,0x2f9))&&(_0x3ad3da[_0x55eb29(0x2f8,0x2f7)]=0x1);_0x3ad3da['transactionLogs']=0x1;let _0x36b1cf=await User[_0x55eb29(0x2ce,0x2e5)]({'userId':_0x571e04['id'],'guildId':_0x486ee0[_0x55eb29(0x30b,0x30c)]['id']},_0x3ad3da);if(!_0x36b1cf)_0x36b1cf=new User({'userId':_0x571e04['id'],'guildId':_0x486ee0[_0x55eb29(0x2f5,0x30c)]['id'],'balance':0x0,'bank':0x0,'commandData':{},'transactionLogs':[]});else!Array[_0x55eb29(0x326,0x32f)](_0x36b1cf[_0x55eb29(0x2f9,0x2ea)])&&(_0x36b1cf['transactionLogs']=[]);let _0x504ec1={'user':'<@'+_0x571e04['id']+'>','amount':_0x55d201,'balance':_0x36b1cf['balance']||0x0,'bank':_0x36b1cf[_0x55eb29(0x2dd,0x2f7)]||0x0,'type':_0x517b48};if(_0x4bc6c1===_0x55eb29(0x2eb,0x307)){(_0x517b48===_0x55eb29(0x2f2,0x2f6)||_0x517b48===_0x55eb29(0x2d4,0x2f9))&&(_0x36b1cf[_0x55eb29(0x318,0x2f6)]+=_0x55d201,_0x36b1cf[_0x55eb29(0x2cf,0x2ea)]['push']({'type':'admin-give-balance','amount':_0x55d201,'timestamp':new Date()}),_0x504ec1[_0x55eb29(0x2d8,0x2f6)]=_0x36b1cf[_0x55eb29(0x2e0,0x2f6)]);(_0x517b48===_0x55eb29(0x31c,0x2f7)||_0x517b48===_0x55eb29(0x31f,0x2f9))&&(_0x36b1cf[_0x55eb29(0x2fe,0x2f7)]+=_0x55d201,_0x36b1cf[_0x55eb29(0x2c4,0x2ea)][_0x55eb29(0x32c,0x30f)]({'type':_0x55eb29(0x2f2,0x311),'amount':_0x55d201,'timestamp':new Date()}),_0x504ec1['bank']=_0x36b1cf['bank']);const _0xca5d34=new EmbedBuilder()['setDescription'](replacePlaceholders(lang[_0x55eb29(0x2f6,0x30d)]['Messages'][_0x55eb29(0x32d,0x323)],_0x504ec1))['setColor'](_0x55eb29(0x308,0x2f5));return await _0x36b1cf['save'](),_0x486ee0['reply']({'embeds':[_0xca5d34]});}else{if(_0x4bc6c1===_0x55eb29(0x2de,0x2e6)){if(_0x517b48===_0x55eb29(0x303,0x2f6)||_0x517b48===_0x55eb29(0x314,0x2f9)){if(_0x36b1cf[_0x55eb29(0x2fe,0x2f6)]<_0x55d201)return _0x486ee0[_0x55eb29(0x2e0,0x2ec)]({'content':lang[_0x55eb29(0x31e,0x30d)][_0x55eb29(0x314,0x2f2)]['insufficientFunds'],'ephemeral':!![]});_0x36b1cf[_0x55eb29(0x306,0x2f6)]-=_0x55d201,_0x36b1cf[_0x55eb29(0x308,0x2ea)][_0x55eb29(0x2f1,0x30f)]({'type':_0x55eb29(0x2ef,0x312),'amount':-_0x55d201,'timestamp':new Date()}),_0x504ec1[_0x55eb29(0x307,0x2f6)]=_0x36b1cf[_0x55eb29(0x2d2,0x2f6)];}if(_0x517b48==='bank'||_0x517b48===_0x55eb29(0x2fb,0x2f9)){if(_0x36b1cf[_0x55eb29(0x30c,0x2f7)]<_0x55d201)return _0x486ee0[_0x55eb29(0x2c9,0x2ec)]({'content':lang[_0x55eb29(0x300,0x30d)][_0x55eb29(0x2e4,0x2f2)][_0x55eb29(0x2ef,0x2f3)],'ephemeral':!![]});_0x36b1cf[_0x55eb29(0x317,0x2f7)]-=_0x55d201,_0x36b1cf['transactionLogs'][_0x55eb29(0x328,0x30f)]({'type':_0x55eb29(0x318,0x318),'amount':-_0x55d201,'timestamp':new Date()}),_0x504ec1['bank']=_0x36b1cf[_0x55eb29(0x30c,0x2f7)];}const _0x4fb1bb=new EmbedBuilder()['setDescription'](replacePlaceholders(lang[_0x55eb29(0x315,0x30d)][_0x55eb29(0x2e0,0x2f2)]['adminTake'],_0x504ec1))[_0x55eb29(0x313,0x2fd)](_0x55eb29(0x324,0x324));return await _0x36b1cf['save'](),_0x486ee0[_0x55eb29(0x2cc,0x2ec)]({'embeds':[_0x4fb1bb]});}else{if(_0x4bc6c1==='set'){(_0x517b48===_0x55eb29(0x30f,0x2f6)||_0x517b48===_0x55eb29(0x300,0x2f9))&&(_0x36b1cf[_0x55eb29(0x2ee,0x2f6)]=_0x55d201,_0x36b1cf[_0x55eb29(0x301,0x2ea)]['push']({'type':'admin-set-balance','amount':_0x55d201,'timestamp':new Date()}),_0x504ec1[_0x55eb29(0x306,0x2f6)]=_0x36b1cf[_0x55eb29(0x2f3,0x2f6)]);(_0x517b48===_0x55eb29(0x2d8,0x2f7)||_0x517b48===_0x55eb29(0x2ef,0x2f9))&&(_0x36b1cf[_0x55eb29(0x2e8,0x2f7)]=_0x55d201,_0x36b1cf[_0x55eb29(0x305,0x2ea)]['push']({'type':'admin-set-bank','amount':_0x55d201,'timestamp':new Date()}),_0x504ec1[_0x55eb29(0x2e8,0x2f7)]=_0x36b1cf[_0x55eb29(0x319,0x2f7)]);const _0x4a7604=new EmbedBuilder()['setDescription'](replacePlaceholders(lang[_0x55eb29(0x304,0x30d)][_0x55eb29(0x306,0x2f2)][_0x55eb29(0x34b,0x32e)],_0x504ec1))[_0x55eb29(0x300,0x2fd)](_0x55eb29(0x2bf,0x2e4));return await _0x36b1cf[_0x55eb29(0x2ea,0x30e)](),_0x486ee0[_0x55eb29(0x2eb,0x2ec)]({'embeds':[_0x4a7604]});}}}}};