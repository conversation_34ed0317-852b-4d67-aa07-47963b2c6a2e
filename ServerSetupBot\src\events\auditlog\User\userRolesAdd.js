const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildMemberUpdate,
    async execute(oldMember, newMember, client) {
        try {
            // Only handle role additions
            const oldRoles = oldMember.roles.cache;
            const newRoles = newMember.roles.cache;
            const addedRoles = newRoles.filter(role => !oldRoles.has(role.id));
            
            if (addedRoles.size === 0) return;
            
            console.log(`➕ User roles added: ${newMember.user.tag} in ${newMember.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newMember.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'USER_ROLES_ADD')) return;
            
            // Create event data
            const eventData = {
                eventType: 'USER_ROLES_ADD',
                user: 'System',
                userId: null,
                action: 'Roles được thêm cho thành viên',
                details: `**${addedRoles.size}** roles đã được thêm cho **${newMember.user.tag}**`,
                target: newMember.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who added the roles from audit logs
            try {
                const auditLogs = await newMember.guild.fetchAuditLogs({
                    type: 25, // MEMBER_ROLE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newMember.user.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Roles được thêm bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for role add');
            }
            
            // Add to database
            await client.db.addAuditLog(newMember.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '➕ Roles được thêm',
                `Vừa có roles được thêm cho thành viên trong server`
            );
            
            embed.setColor(0x2ecc71); // Green for add
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${newMember.user.tag} (${newMember.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newMember.user.id}`,
                    inline: true
                },
                {
                    name: '> Người thực hiện',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add roles that were added
            const addedRolesList = addedRoles.map(role => `<@&${role.id}>`).join(', ');
            embed.addFields({
                name: '> ✅ Roles được thêm',
                value: addedRolesList.length > 1000 ? addedRolesList.substring(0, 1000) + '...' : addedRolesList,
                inline: false
            });
            
            embed.addFields([
                {
                    name: '> 📊 Số roles được thêm',
                    value: `- ${addedRoles.size} roles`,
                    inline: true
                },
                {
                    name: '> 📊 Tổng roles hiện tại',
                    value: `- ${newRoles.size - 1} roles`, // -1 for @everyone
                    inline: true
                }
            ]);
            
            // Check for dangerous roles
            const dangerousPerms = ['Administrator', 'ManageGuild', 'ManageRoles', 'ManageChannels', 'BanMembers', 'KickMembers'];
            const dangerousRoles = addedRoles.filter(role => 
                role.permissions.toArray().some(perm => dangerousPerms.includes(perm))
            );
            
            if (dangerousRoles.size > 0) {
                embed.addFields({
                    name: '⚠️ Cảnh báo',
                    value: `- Đã thêm roles có quyền nguy hiểm: ${dangerousRoles.map(r => r.name).join(', ')}`,
                    inline: false
                });
                embed.setColor(0xf39c12); // Orange for warning
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set user avatar as thumbnail
            if (newMember.user.displayAvatarURL()) {
                embed.setThumbnail(newMember.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newMember.guild.name} • User Roles Add`,
                iconURL: newMember.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ User roles add logged: ${newMember.user.tag}`);
            
            // Smart alert for dangerous role grants
            if (config.smartAlerts && dangerousRoles.size > 0) {
                try {
                    const owner = await newMember.guild.fetchOwner();
                    const alertEmbed = createInfoEmbed(
                        '🚨 Smart Alert: Dangerous Role Grant',
                        `Roles nguy hiểm đã được thêm cho **${newMember.user.tag}** trong **${newMember.guild.name}**`
                    );
                    
                    alertEmbed.setColor(0xf39c12);
                    alertEmbed.addFields([
                        { name: '🎯 Loại', value: 'DANGEROUS_ROLE_GRANT', inline: true },
                        { name: '📊 Mức độ', value: 'HIGH', inline: true },
                        { name: '👤 Thực hiện bởi', value: eventData.user, inline: true },
                        { name: '👥 Thành viên', value: newMember.user.tag, inline: true },
                        { name: '🎭 Roles nguy hiểm', value: dangerousRoles.map(r => r.name).join(', '), inline: true }
                    ]);
                    
                    await owner.send({ embeds: [alertEmbed] });
                } catch (dmError) {
                    console.log('Could not send dangerous role grant alert to owner');
                }
            }
            
        } catch (error) {
            console.error('Error in userRolesAdd audit log:', error);
        }
    }
};
