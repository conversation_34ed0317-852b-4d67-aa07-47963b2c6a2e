(function(_0x5eb5c7,_0x4f60b){function _0x27e9e2(_0x2ffa3b,_0x353318,_0x2dbad7,_0x485b4c,_0x24a24c){return _0x48f1(_0x485b4c- -0x3c5,_0x2ffa3b);}function _0x7f7d81(_0x2830a2,_0x172217,_0x1962b9,_0x50f411,_0x1dbafe){return _0x48f1(_0x2830a2- -0x1e1,_0x50f411);}const _0x340901=_0x5eb5c7();while(!![]){try{const _0x53c177=-parseInt(_0x7f7d81(-0xab,-0x98,-0x100,-0x66,-0x8e))/0x1*(parseInt(_0x27e9e2(-0x236,-0x232,-0x1d7,-0x1dd,-0x1c7))/0x2)+-parseInt(_0x7f7d81(-0x7d,-0x77,-0xb6,-0x5d,-0xce))/0x3+-parseInt(_0x27e9e2(-0x293,-0x202,-0x204,-0x23a,-0x27e))/0x4+parseInt(_0x7f7d81(-0x8e,-0x72,-0x4d,-0x38,-0xbd))/0x5+-parseInt(_0x27e9e2(-0x25a,-0x1eb,-0x1e7,-0x213,-0x24e))/0x6+parseInt(_0x7f7d81(-0x6f,-0x34,-0x88,-0x73,-0x8d))/0x7*(parseInt(_0x7f7d81(-0x98,-0x3c,-0xbb,-0x48,-0x62))/0x8)+-parseInt(_0x27e9e2(-0x1da,-0x1db,-0x22d,-0x22c,-0x249))/0x9*(-parseInt(_0x7f7d81(-0x5,0x3,0x3a,-0x64,-0x3d))/0xa);if(_0x53c177===_0x4f60b)break;else _0x340901['push'](_0x340901['shift']());}catch(_0x2e285e){_0x340901['push'](_0x340901['shift']());}}}(_0x24fb,0x62a15));function _0x24fb(){const _0x395c87=['\x6c\x6f\x61\x64','\x54\x65\x78\x74','\x66\x69\x6e\x64\x4f\x6e\x65\x41\x6e\x64','\x75\x6e\x76\x65\x72\x69\x66\x69\x65\x64','\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73','\x67\x75\x69\x6c\x64\x73','\x63\x6f\x75\x6e\x74\x65\x72','\x41\x6c\x74\x20\x61\x63\x63\x6f\x75\x6e','\x31\x30\x38\x35\x33\x36\x57\x43\x47\x48\x47\x67','\x73\x74\x72\x69\x6e\x67','\x74\x68\x65\x20\x6e\x65\x77\x20\x6d\x65','\x6a\x6f\x69\x6e','\x7b\x75\x73\x65\x72\x42\x61\x6e\x6e\x65','\x20\x72\x6f\x6c\x65\x20\x6e\x6f\x74\x20','\x6e\x6f\x77','\x73\x61\x76\x65','\x6e\x63\x74\x69\x6f\x6e\x28\x29\x20','\x63\x68\x61\x69\x6e','\x32\x35\x37\x32\x37\x33\x35\x78\x78\x73\x71\x77\x73','\x69\x73\x4d\x75\x74\x65\x64','\x63\x6f\x64\x65','\x49\x63\x6f\x6e','\x63\x61\x74\x63\x68','\x65\x29\x20\x7b\x7d','\x65\x7a\x6f\x6e\x65','\x73\x65\x6e\x64','\x55\x73\x65\x72\x44\x61\x74\x61','\x64\x61\x74\x61\x20\x6f\x6e\x20\x6a\x6f','\x65\x72\x72\x6f\x72','\x6a\x73\x2d\x79\x61\x6d\x6c','\x2e\x2e\x2f\x6d\x6f\x64\x65\x6c\x73\x2f','\x67\x75\x69\x6c\x64','\x75\x73\x65\x72\x49\x64','\x69\x6e\x3a\x20','\x6c\x65\x6e\x67\x74\x68','\x31\x33\x33\x32\x34\x37\x31\x72\x44\x68\x6c\x73\x4e','\x74\x61\x72\x55\x52\x4c','\x6b\x69\x6e\x67\x20\x75\x73\x65\x72\x20','\x64\x65\x62\x75','\x63\x6f\x6e\x73\x74\x72\x75\x63\x74\x6f','\x75\x74\x66\x38','\x61\x64\x64','\x44\x4d\x20\x74\x6f\x20','\x67\x75\x69\x6c\x64\x49\x64','\x28\x28\x28\x2e\x2b\x29\x2b\x29\x2b\x29','\x6d\x62\x65\x72\x2e','\x74\x69\x6d\x65\x6f\x75\x74\x52\x6f\x6c','\x73\x65\x74','\x69\x63\x6f\x6e\x55\x52\x4c','\x31\x36\x38\x55\x77\x78\x56\x75\x76','\x66\x6f\x72\x6d\x61\x74','\x65\x6d\x62\x65\x64\x73','\x69\x6c\x65\x64\x20\x74\x6f\x20\x73\x65','\x6a\x6f\x69\x6e\x65\x64\x41\x74','\x69\x6e\x76\x69\x74\x65\x72\x49\x44','\x54\x69\x6d\x65\x7a\x6f\x6e\x65','\x63\x74\x6f\x72\x28\x22\x72\x65\x74\x75','\x63\x68\x61\x6e\x6e\x65\x6c\x73','\x20\x6e\x6f\x74\x20\x66\x6f\x75\x6e\x64','\x6e\x6b\x6e\x6f\x77\x6e','\x54\x79\x70\x65','\x4c\x6f\x67','\x73\x65\x74\x46\x6f\x6f\x74\x65\x72','\x41\x75\x74\x68\x6f\x72','\x54\x68\x75\x6d\x62\x6e\x61\x69\x6c','\x62\x6c\x65\x20\x74\x6f\x20\x61\x73\x73','\x3a\x64\x3e','\x72\x6f\x6c\x65\x73','\x74\x65\x73\x74','\x20\x6d\x65\x73\x73\x61\x67\x65\x3a\x20','\x64\x69\x73\x63\x6f\x72\x64\x2e\x6a\x73','\x73\x3a\x20','\x73\x74\x61\x74\x65\x4f\x62\x6a\x65\x63','\x67\x65\x74','\x33\x30\x30\x31\x39\x36\x38\x6e\x51\x76\x6e\x73\x6a','\x73\x65\x61\x72\x63\x68','\x72\x65\x74\x75\x72\x6e\x20\x28\x66\x75','\x66\x6c\x6f\x6f\x72','\x65\x78\x70\x6f\x72\x74\x73','\x3a\x74\x3e','\x44\x65\x73\x63\x72\x69\x70\x74\x69\x6f','\x61\x64\x64\x46\x69\x65\x6c\x64\x73','\x5c\x2b\x5c\x2b\x20\x2a\x28\x3f\x3a\x5b','\x49\x6d\x61\x67\x65','\x73\x65\x74\x49\x6e\x74\x65\x72\x76\x61','\x4d\x4d\x4d\x4d\x20\x44\x6f\x20\x59\x59','\x68\x61\x73','\x63\x61\x63\x68\x65','\x38\x39\x34\x36\x62\x66\x59\x61\x44\x6b','\x69\x67\x6e\x20\x69\x74\x20\x74\x6f\x20','\x64\x69\x73\x70\x6c\x61\x79\x4e\x61\x6d','\x45\x6e\x61\x62\x6c\x65\x64','\x4b\x69\x63\x6b\x65\x64','\x72\x65\x76\x65\x6e\x74\x69\x6f\x6e\x20','\x63\x63\x6f\x75\x6e\x74\x20','\x4a\x77\x65\x6a\x58','\x55\x70\x64\x61\x74\x65','\x5b\x45\x52\x52\x4f\x52\x5d\x20\x46\x61','\x6d\x61\x70','\x73\x65\x74\x44\x65\x73\x63\x72\x69\x70','\x7b\x7d\x2e\x63\x6f\x6e\x73\x74\x72\x75','\x69\x6e\x76\x69\x74\x65\x43\x6f\x64\x65','\x65\x6d\x62\x65\x72\x3a','\x63\x61\x6c\x6c','\x69\x6c\x65\x64\x20\x74\x6f\x20\x66\x65','\x67\x75\x69\x6c\x64\x44\x61\x74\x61\x53','\x72\x65\x61\x64\x46\x69\x6c\x65\x53\x79','\x77\x53\x78\x72\x53','\x43\x6f\x6c\x6f\x72','\x73\x65\x74\x41\x75\x74\x68\x6f\x72','\x75\x73\x65\x72','\x7b\x75\x73\x65\x72\x2d\x61\x76\x61\x74','\x69\x6e\x70\x75\x74','\x35\x31\x30\x34\x36\x38\x54\x6d\x44\x47\x58\x59','\x67\x75\x69\x6c\x64\x49\x44','\x4d\x45\x53\x53\x41\x47\x45','\x4c\x6f\x67\x73\x43\x68\x61\x6e\x6e\x65','\x6a\x6f\x69\x6e\x65\x64\x55\x73\x65\x72','\x69\x6f\x6e','\x74\x61\x67','\x20\x6d\x75\x74\x65\x64\x20\x72\x6f\x6c','\x69\x6e\x76\x69\x74\x65\x73','\x73\x65\x74\x43\x6f\x6c\x6f\x72','\x73\x20\x66\x6f\x72\x20\x67\x75\x69\x6c','\x55\x6e\x76\x65\x72\x69\x66\x69\x65\x64','\x75\x70\x64\x61\x74\x65\x20\x73\x74\x6f','\x3a\x46\x3e','\x3c\x74\x3a','\x72\x65\x70\x6c\x61\x63\x65','\x6d\x65\x6d\x62\x65\x72\x43\x6f\x75\x6e','\x72\x65\x64\x75\x63\x65','\x45\x4d\x42\x45\x44','\x73\x65\x74\x54\x69\x74\x6c\x65','\x4d\x75\x74\x65\x64\x20\x72\x6f\x6c\x65','\x54\x69\x74\x6c\x65','\x74\x20\x64\x65\x74\x65\x63\x74\x65\x64','\x65\x20\x6f\x6e\x20\x72\x65\x6a\x6f\x69','\x6d\x6f\x6d\x65\x6e\x74\x2d\x74\x69\x6d','\x41\x6c\x74\x50\x72\x65\x76\x65\x6e\x74','\x6e\x61\x6d\x65','\x61\x72\x7d','\x63\x68\x65\x6d\x61','\x62\x6f\x74','\x74\x69\x6f\x6e','\x75\x6e\x69\x78','\x73\x65\x74\x49\x6d\x61\x67\x65','\x66\x69\x6e\x64\x4f\x6e\x65','\x52\x65\x61\x70\x70\x6c\x79\x69\x6e\x67','\x66\x69\x6e\x64','\x61\x70\x70\x6c\x79','\x70\x6e\x67','\x73\x65\x6e\x64\x20\x61\x6c\x74\x20\x70','\x42\x4f\x54\x48','\x77\x68\x69\x6c\x65\x20\x28\x74\x72\x75','\x74\x6f\x53\x74\x72\x69\x6e\x67','\x31\x34\x35\x31\x30\x50\x5a\x42\x4f\x6c\x56','\x73\x69\x7a\x65','\x75\x73\x65\x73','\x75\x70\x73\x65\x72\x74','\x72\x65\x64\x20\x6d\x65\x6d\x62\x65\x72','\x63\x72\x65\x61\x74\x65\x64\x41\x74','\x72\x6e\x20\x74\x68\x69\x73\x22\x29\x28','\x48\x48\x3a\x6d\x6d','\x73\x61\x67\x65','\x6a\x4f\x50\x67\x47','\x75\x73\x65\x72\x6e\x61\x6d\x65','\x6f\x6e\x53\x63\x68\x65\x6d\x61','\x32\x74\x79\x54\x7a\x75\x42','\x45\x72\x72\x6f\x72\x20\x63\x68\x65\x63','\x56\x65\x72\x69\x66\x69\x63\x61\x74\x69','\x69\x6e\x76\x69\x74\x65\x53\x63\x68\x65','\x63\x6c\x65\x61\x72','\x6f\x6e\x53\x65\x74\x74\x69\x6e\x67\x73','\x65\x49\x64','\x62\x61\x6e\x6e\x65\x72\x55\x52\x4c','\x4d\x4d\x2f\x44\x44\x2f\x59\x59\x59\x59','\x73\x65\x74\x54\x68\x75\x6d\x62\x6e\x61','\x6d\x65\x6d\x62\x65\x72\x73','\x67\x67\x65\x72','\x56\x61\x6e\x69\x74\x79\x20\x2f\x20\x55','\x6c\x49\x44','\x72\x69\x66\x69\x65\x64\x52\x6f\x6c\x65','\x64\x69\x73\x70\x6c\x61\x79\x41\x76\x61','\x64\x79\x6e\x61\x6d\x69\x63','\x66\x6f\x75\x6e\x64\x2c\x20\x75\x6e\x61','\x6c\x6f\x67\x20\x66\x6f\x72\x20','\x35\x39\x38\x37\x33\x33\x75\x4e\x74\x43\x70\x71','\x67\x6e\x69\x6e\x67\x20\x75\x6e\x76\x65','\x61\x63\x74\x69\x6f\x6e','\x66\x75\x6e\x63\x74\x69\x6f\x6e\x20\x2a','\x46\x61\x69\x6c\x65\x64\x20\x74\x6f\x20','\x24\x5d\x2a\x29','\x45\x6d\x62\x65\x64','\x6e\x64\x20\x77\x65\x6c\x63\x6f\x6d\x65','\x66\x65\x74\x63\x68','\x46\x6f\x6f\x74\x65\x72','\x69\x6e\x69\x74'];_0x24fb=function(){return _0x395c87;};return _0x24fb();}const _0x3ece2f=(function(){let _0x4fa6ee=!![];return function(_0x20e69e,_0x11641e){const _0x56c3ff=_0x4fa6ee?function(){function _0x5c8138(_0x5138ff,_0x281293,_0x161894,_0x4f32a7,_0x479dce){return _0x48f1(_0x161894-0x72,_0x4f32a7);}if(_0x11641e){const _0x34ce68=_0x11641e[_0x5c8138(0x29e,0x280,0x248,0x1e8,0x229)](_0x20e69e,arguments);return _0x11641e=null,_0x34ce68;}}:function(){};return _0x4fa6ee=![],_0x56c3ff;};}()),_0x5bff53=_0x3ece2f(this,function(){function _0x276c16(_0x4bc830,_0x3221d8,_0x389786,_0x593c68,_0x40fa98){return _0x48f1(_0x389786-0x1e2,_0x3221d8);}function _0x28c71b(_0x2f6b45,_0x2a2855,_0x84d1f8,_0x3391e7,_0x14677b){return _0x48f1(_0x2f6b45- -0x2aa,_0x3391e7);}return _0x5bff53[_0x276c16(0x3dc,0x3e5,0x3bd,0x368,0x3fa)]()[_0x28c71b(-0x11e,-0xf1,-0x123,-0xf9,-0x169)]('\x28\x28\x28\x2e\x2b\x29\x2b\x29\x2b\x29'+'\x2b\x24')[_0x28c71b(-0xcf,-0xf5,-0x7a,-0xef,-0x10c)]()['\x63\x6f\x6e\x73\x74\x72\x75\x63\x74\x6f'+'\x72'](_0x5bff53)['\x73\x65\x61\x72\x63\x68'](_0x28c71b(-0x13d,-0xf3,-0x152,-0x152,-0x110)+'\x2b\x24');});function _0x48f1(_0x1bc0d0,_0x4f4bbb){const _0x5204b7=_0x24fb();return _0x48f1=function(_0x539002,_0x38d29b){_0x539002=_0x539002-0x12e;let _0x2b1e29=_0x5204b7[_0x539002];return _0x2b1e29;},_0x48f1(_0x1bc0d0,_0x4f4bbb);}_0x5bff53();const _0x38d29b=(function(){let _0x4aef19=!![];return function(_0x5105c1,_0x1efd4c){function _0x198b8b(_0x340419,_0xcec9d4,_0x148dba,_0x2e7416,_0x4965ec){return _0x48f1(_0x4965ec-0x3a,_0x340419);}function _0xd1ecf4(_0x2093b4,_0x58ec6b,_0x1551ef,_0x50f65d,_0x301a8b){return _0x48f1(_0x50f65d- -0x3,_0x58ec6b);}if(_0x198b8b(0x18d,0x21f,0x19d,0x1a5,0x1e6)===_0x198b8b(0x1b5,0x1b5,0x18f,0x22e,0x1da)){let _0x4e99d2;try{const _0x59d2c8=_0x510844(_0x198b8b(0x176,0x212,0x199,0x1c0,0x1c7)+_0x198b8b(0x148,0x15d,0x166,0x19a,0x18b)+(_0xd1ecf4(0x15d,0x1b9,0x1d9,0x1a2,0x16a)+_0x198b8b(0x161,0x16a,0x1d1,0x1aa,0x1b3)+_0x198b8b(0x20a,0x1ce,0x1f2,0x25f,0x21c)+'\x20\x29')+'\x29\x3b');_0x4e99d2=_0x59d2c8();}catch(_0x241104){_0x4e99d2=_0x4f01fe;}_0x4e99d2[_0xd1ecf4(0x130,0x134,0x163,0x192,0x142)+'\x6c'](_0x4a3c0f,0xfa0);}else{const _0x1105d5=_0x4aef19?function(){if(_0x1efd4c){const _0x44f53c=_0x1efd4c['\x61\x70\x70\x6c\x79'](_0x5105c1,arguments);return _0x1efd4c=null,_0x44f53c;}}:function(){};return _0x4aef19=![],_0x1105d5;}};}());(function(){_0x38d29b(this,function(){function _0x401069(_0x10d3d0,_0x26de52,_0xa9f17c,_0x5c46eb,_0x49b413){return _0x48f1(_0x10d3d0- -0xc8,_0xa9f17c);}function _0xca0478(_0x2bfbc7,_0x2f3f82,_0xea898d,_0x18bb7c,_0x5cdb85){return _0x48f1(_0x2f3f82- -0x34d,_0x18bb7c);}const _0x3a4961=new RegExp(_0x401069(0x71,0x59,0xaf,0xd2,0x66)+'\x5c\x28\x20\x2a\x5c\x29'),_0x239d2f=new RegExp(_0xca0478(-0x204,-0x1ba,-0x206,-0x17b,-0x218)+'\x61\x2d\x7a\x41\x2d\x5a\x5f\x24\x5d\x5b'+'\x30\x2d\x39\x61\x2d\x7a\x41\x2d\x5a\x5f'+_0x401069(0x73,0x7f,0x7f,0xa7,0x30),'\x69'),_0x448709=_0x539002(_0x401069(0x78,0xd8,0x7b,0x58,0xc2));!_0x3a4961[_0xca0478(-0x1d7,-0x1c8,-0x1df,-0x1b6,-0x1fa)](_0x448709+_0xca0478(-0x1b8,-0x1fb,-0x1d3,-0x1e1,-0x1f7))||!_0x239d2f[_0xca0478(-0x20a,-0x1c8,-0x1a9,-0x1f2,-0x16f)](_0x448709+_0xca0478(-0x146,-0x19c,-0x15b,-0x181,-0x1fe))?_0x448709('\x30'):_0x539002();})();}());const {EmbedBuilder}=require(_0x247edf(0x445,0x3db,0x3bf,0x3e5,0x3c9)),fs=require('\x66\x73'),yaml=require(_0x247edf(0x3d9,0x3ba,0x37f,0x3bc,0x3d8)),moment=require(_0x3e98d8(0x43c,0x45d,0x3f9,0x40f,0x48c)+_0x247edf(0x396,0x36d,0x3d7,0x3b7,0x40f)),ms=require('\x6d\x73'),Verification=require(_0x247edf(0x35a,0x3d9,0x418,0x3bd,0x3c5)+'\x76\x65\x72\x69\x66\x69\x63\x61\x74\x69'+_0x247edf(0x43d,0x405,0x46a,0x445,0x478)),Invite=require('\x2e\x2e\x2f\x6d\x6f\x64\x65\x6c\x73\x2f'+_0x247edf(0x404,0x404,0x45c,0x449,0x471)+'\x6d\x61'),GuildData=require(_0x247edf(0x361,0x3a0,0x3c3,0x3bd,0x3dc)+_0x247edf(0x421,0x44a,0x3c1,0x408,0x3bc)+_0x247edf(0x42e,0x3ea,0x409,0x42c,0x46e)),UserData=require(_0x3e98d8(0x3d1,0x42b,0x388,0x3b1,0x3cc)+_0x3e98d8(0x3cd,0x397,0x3a2,0x415,0x418)),lang=yaml['\x6c\x6f\x61\x64'](fs[_0x3e98d8(0x41d,0x45b,0x3f6,0x465,0x44f)+'\x6e\x63']('\x2e\x2f\x6c\x61\x6e\x67\x2e\x79\x6d\x6c',_0x3e98d8(0x3db,0x410,0x3f1,0x3e2,0x412)));(function(){function _0x169683(_0xf75a86,_0x26c701,_0x15cec8,_0x3eaeec,_0x3b27a5){return _0x3e98d8(_0xf75a86- -0x2dc,_0x26c701-0x1d0,_0x15cec8-0xe4,_0x26c701,_0x3b27a5-0xb7);}let _0x5d8b92;try{const _0x15de4b=Function('\x72\x65\x74\x75\x72\x6e\x20\x28\x66\x75'+'\x6e\x63\x74\x69\x6f\x6e\x28\x29\x20'+(_0x169683(0x13b,0x19b,0x168,0x164,0x13c)+_0x4a71e3(0x2b7,0x2cb,0x31b,0x28d,0x2d2)+_0x4a71e3(0x366,0x31a,0x320,0x2f1,0x33b)+'\x20\x29')+'\x29\x3b');_0x5d8b92=_0x15de4b();}catch(_0x580e5b){_0x5d8b92=window;}function _0x4a71e3(_0x5c9e46,_0x2c3d5c,_0x2dce57,_0x333504,_0x2f77c2){return _0x3e98d8(_0x2f77c2- -0x119,_0x2c3d5c-0x41,_0x2dce57-0xc6,_0x2dce57,_0x2f77c2-0x4);}_0x5d8b92[_0x4a71e3(0x2c3,0x317,0x34f,0x2f2,0x2ee)+'\x6c'](_0x539002,0xfa0);}());const config=yaml[_0x247edf(0x383,0x373,0x347,0x39f,0x3be)](fs['\x72\x65\x61\x64\x46\x69\x6c\x65\x53\x79'+'\x6e\x63']('\x2e\x2f\x63\x6f\x6e\x66\x69\x67\x2e\x79'+'\x6d\x6c',_0x3e98d8(0x3db,0x3de,0x380,0x3ba,0x408))),sentWelcomeEmbeds=new Set(),WELCOME_EMBED_RESET_INTERVAL=ms('\x35\x6d');setInterval(()=>{function _0x155f02(_0x57f83b,_0x15cfd0,_0x196730,_0x3f020d,_0x13e336){return _0x3e98d8(_0x13e336- -0x2f3,_0x15cfd0-0x47,_0x196730-0x187,_0x196730,_0x13e336-0x54);}sentWelcomeEmbeds[_0x155f02(0x163,0x12a,0x18f,0x1a1,0x16b)]();},WELCOME_EMBED_RESET_INTERVAL),module[_0x3e98d8(0x401,0x431,0x434,0x3a4,0x3d7)]=async(_0x33b3a7,_0x5d92ad)=>{if(_0x5d92ad['\x69\x64']===_0x33b3a7['\x75\x73\x65\x72']['\x69\x64']||_0x5d92ad[_0x50cf89(0x1b2,0x152,0x1c3,0x188,0x162)][_0x50cf89(0x1dc,0x1ef,0x1d6,0x1a8,0x1ad)]){if(_0x5fd6e1(0x91,0x77,0x63,0xc4,0x7b)!==_0x5fd6e1(0xc9,0x6c,0xac,0xc4,0xcd))_0x44776a[_0x50cf89(0x112,0x17e,0xf5,0x136,0xdb)]('\x55\x6e\x76\x65\x72\x69\x66\x69\x65\x64'+'\x20\x72\x6f\x6c\x65\x20\x6e\x6f\x74\x20'+_0x50cf89(0x165,0x14f,0x155,0x10d,0x164)+_0x50cf89(0x157,0x181,0x174,0x15b,0x14a)+_0x5fd6e1(0x8a,0xa1,0x20,0x79,0x80)+_0x50cf89(0x140,0xf4,0x181,0x124,0xf7)+_0x50cf89(0x160,0x18b,0x166,0x147,0xef));else return;}if(config[_0x5fd6e1(0x9d,0xac,0xad,0xaa,0xe7)+_0x50cf89(0x144,0x1da,0x1f1,0x190,0x143)][_0x50cf89(0x1ba,0x158,0x155,0x175,0x13b)]){const _0x1a71b7=Date[_0x5fd6e1(0x5b,0x45,0x10,0x2e,-0x2f)]()-_0x5d92ad[_0x50cf89(0x175,0x1e0,0x151,0x188,0x17f)][_0x5fd6e1(0x102,0xec,0xb8,0xc0,0xfc)],_0x1390f8=ms(config['\x41\x6c\x74\x50\x72\x65\x76\x65\x6e\x74'+_0x5fd6e1(0x6a,0xb1,0x46,0x96,0xd3)]['\x54\x69\x6d\x65\x4c\x69\x6d\x69\x74']);if(_0x1a71b7<_0x1390f8){config[_0x5fd6e1(0xb4,0xba,0xba,0xaa,0x5c)+_0x5fd6e1(0xc3,0xf2,0xcc,0x96,0x60)]['\x44\x4d'][_0x50cf89(0x1c9,0x19b,0x1b8,0x175,0x119)]&&await sendAltPreventionDM(_0x5d92ad);if(config[_0x50cf89(0x19a,0x1b1,0x1f4,0x1a4,0x1e3)+_0x5fd6e1(0xd5,0x7c,0xf7,0x96,0xba)]['\x4b\x69\x63\x6b\x41\x6c\x74\x73'])try{await _0x5d92ad['\x6b\x69\x63\x6b'](_0x50cf89(0x108,0x14c,0x17d,0x121,0x153)+_0x5fd6e1(0x8b,0x54,0x96,0xa7,0x4b));}catch(_0x50bcfb){console['\x65\x72\x72\x6f\x72']('\x46\x61\x69\x6c\x65\x64\x20\x74\x6f\x20'+'\x6b\x69\x63\x6b\x20\x61\x6c\x74\x20\x61'+_0x5fd6e1(0x7e,0x4d,0x68,0x7e,0xc4)+_0x5d92ad[_0x50cf89(0x126,0x1c7,0x142,0x188,0x1a2)][_0x50cf89(0x1b2,0x18e,0x1c1,0x191,0x144)]+'\x3a',_0x50bcfb);}await sendAltPreventionLog(_0x33b3a7,_0x5d92ad,_0x1a71b7<_0x1390f8);return;}}if(config[_0x5fd6e1(0x128,0xe7,0x11d,0xc9,0x76)+_0x5fd6e1(0x91,0xa4,0xbf,0xcc,0x121)][_0x50cf89(0x1cf,0x120,0x1ba,0x175,0x1a6)]&&config[_0x5fd6e1(0x82,0xfc,0x82,0xc9,0x8e)+_0x50cf89(0x18c,0x20e,0x1c1,0x1c6,0x175)]['\x45\x6e\x61\x62\x6c\x65\x55\x6e\x76\x65'+_0x5fd6e1(0x3c,-0x29,0x22,0x10,-0x27)])try{const _0x3aa9cb={};_0x3aa9cb['\x67\x75\x69\x6c\x64\x49\x44']=_0x5d92ad[_0x5fd6e1(0x6b,0x41,-0x4,0x3f,0x71)]['\x69\x64'];let _0x26941d=await Verification[_0x5fd6e1(0x115,0xa4,0x9a,0xb2,0x4f)](_0x3aa9cb);if(!_0x26941d||!_0x26941d[_0x5fd6e1(0x14,0x12,0x34,0x23,0x11)+'\x52\x6f\x6c\x65\x49\x44']){await createUnverifiedRoleIfNeeded(_0x5d92ad[_0x5fd6e1(0x45,0x16,0x32,0x3f,0x1e)],_0x26941d);const _0x3adf13={};_0x3adf13[_0x5fd6e1(0xc2,0x9e,0x4b,0x92,0x49)]=_0x5d92ad['\x67\x75\x69\x6c\x64']['\x69\x64'],_0x26941d=await Verification[_0x5fd6e1(0x79,0x103,0x7c,0xb2,0x63)](_0x3adf13);}const _0x6ff8ff=_0x5d92ad['\x67\x75\x69\x6c\x64'][_0x5fd6e1(0x3d,0x9d,0x0,0x63,0x99)][_0x50cf89(0x1b7,0x1ac,0x1ba,0x171,0x118)][_0x5fd6e1(0x6d,0x53,0x3b,0x69,0x8a)](_0x26941d[_0x50cf89(0x133,0xdc,0x118,0x11d,0xe1)+'\x52\x6f\x6c\x65\x49\x44']);_0x6ff8ff?await _0x5d92ad['\x72\x6f\x6c\x65\x73']['\x61\x64\x64'](_0x6ff8ff)[_0x50cf89(0x14a,0x15b,0x142,0x130,0x16f)](console[_0x50cf89(0xf1,0x132,0x18d,0x136,0x18e)]):console[_0x50cf89(0xf8,0x156,0x123,0x136,0x111)](_0x5fd6e1(0xf0,0xad,0x86,0x9c,0x9f)+_0x50cf89(0x131,0xe5,0x133,0x127,0x14d)+'\x66\x6f\x75\x6e\x64\x2c\x20\x75\x6e\x61'+'\x62\x6c\x65\x20\x74\x6f\x20\x61\x73\x73'+'\x69\x67\x6e\x20\x69\x74\x20\x74\x6f\x20'+_0x5fd6e1(0x39,-0x31,0x8d,0x2a,-0x39)+'\x6d\x62\x65\x72\x2e');}catch(_0x10e5be){console['\x65\x72\x72\x6f\x72']('\x45\x72\x72\x6f\x72\x20\x61\x73\x73\x69'+_0x5fd6e1(0x2a,0xc,-0x21,0x16,-0x2e)+'\x72\x69\x66\x69\x65\x64\x20\x72\x6f\x6c'+'\x65\x20\x74\x6f\x20\x6e\x65\x77\x20\x6d'+_0x50cf89(0x124,0x1a1,0x165,0x180,0x1df),_0x10e5be);}let _0x887620=_0x5fd6e1(-0x3b,0x36,0x65,0xe,0x36)+_0x5fd6e1(0xb7,0x42,0x9c,0x5b,0xa0);function _0x5fd6e1(_0x21d152,_0x18a7f1,_0xf59755,_0x5708b4,_0x16a6fb){return _0x3e98d8(_0x5708b4- -0x393,_0x18a7f1-0xd4,_0xf59755-0xa7,_0xf59755,_0x16a6fb-0x149);}let _0x2333ed=0x0;try{!_0x33b3a7[_0x50cf89(0x19e,0x1dc,0x1b4,0x193,0x1a8)][_0x50cf89(0x187,0x15c,0x152,0x170,0x15a)](_0x5d92ad[_0x5fd6e1(-0x1e,-0x1f,0x7b,0x3f,-0x20)]['\x69\x64'])&&_0x33b3a7['\x69\x6e\x76\x69\x74\x65\x73'][_0x50cf89(0x14e,0x159,0x128,0x149,0x123)](_0x5d92ad[_0x50cf89(0x18e,0x163,0x16a,0x139,0x12d)]['\x69\x64'],new Map());const _0x31db13=_0x33b3a7[_0x5fd6e1(0x94,0x59,0xb5,0x99,0x47)][_0x50cf89(0x142,0x11f,0x10c,0x163,0x1a2)](_0x5d92ad['\x67\x75\x69\x6c\x64']['\x69\x64']),_0x9927f3=await _0x5d92ad[_0x5fd6e1(0x3f,0x77,-0xb,0x3f,0x6c)][_0x50cf89(0x182,0x189,0x19c,0x193,0x15c)][_0x5fd6e1(0x23,-0x15,0x6a,0x1d,-0xb)]();_0x33b3a7['\x69\x6e\x76\x69\x74\x65\x73'][_0x50cf89(0x11a,0x154,0xe6,0x149,0x17f)](_0x5d92ad['\x67\x75\x69\x6c\x64']['\x69\x64'],new Map(_0x9927f3[_0x5fd6e1(0x89,0x40,0x68,0x82,0x34)](_0x163b0e=>[_0x163b0e[_0x50cf89(0x14f,0x147,0x173,0x12e,0x11e)],_0x163b0e['\x75\x73\x65\x73']])));const _0x3d74f0=_0x9927f3[_0x50cf89(0x1bb,0x14d,0x1b0,0x1ae,0x172)](_0x5e9b06=>_0x31db13[_0x5fd6e1(0x6f,0xa6,0x36,0x69,0x4e)](_0x5e9b06[_0x5fd6e1(0x6a,0x2,-0x19,0x34,0x5)])<_0x5e9b06[_0x5fd6e1(0x9a,0xc4,0x7a,0xbd,0x62)]);if(_0x3d74f0){const _0x982cfa={};_0x982cfa[_0x50cf89(0x15f,0x1d9,0x131,0x18c,0x1bb)]=_0x5d92ad[_0x5fd6e1(-0x7,-0xf,-0x11,0x3f,0x23)]['\x69\x64'],_0x982cfa[_0x5fd6e1(0xe1,0x60,0x27,0x85,0xdc)]=_0x3d74f0['\x63\x6f\x64\x65'];const _0x2f1bb0={};_0x2f1bb0['\x75\x73\x65\x73']=0x1;const _0x25277f={};_0x25277f[_0x5fd6e1(0xff,0xdf,0xbc,0xbe,0x108)]=!![],_0x25277f['\x6e\x65\x77']=!![];const _0x1ef8c5=await Invite[_0x50cf89(0xfa,0x158,0x133,0x11c,0x14a)+_0x50cf89(0x177,0x183,0x1ac,0x17a,0x196)](_0x982cfa,{'\x24\x69\x6e\x63':_0x2f1bb0,'\x24\x61\x64\x64\x54\x6f\x53\x65\x74':{'\x6a\x6f\x69\x6e\x65\x64\x55\x73\x65\x72\x73':{'\x75\x73\x65\x72\x49\x44':_0x5d92ad['\x69\x64'],'\x6a\x6f\x69\x6e\x65\x64\x41\x74':new Date()}}},_0x25277f);if(!_0x1ef8c5[_0x5fd6e1(0x17,0x7a,0x3c,0x56,0x65)]){const _0xadbee7=await _0x5d92ad[_0x5fd6e1(0x8c,0x10,0x13,0x3f,-0x1c)][_0x50cf89(0x188,0x224,0x179,0x1cb,0x1f6)][_0x5fd6e1(0x62,-0xb,0x77,0x1d,0x7c)](_0x3d74f0['\x69\x6e\x76\x69\x74\x65\x72']['\x69\x64']);_0x1ef8c5['\x69\x6e\x76\x69\x74\x65\x72\x49\x44']=_0xadbee7['\x69\x64'],await _0x1ef8c5['\x73\x61\x76\x65']();}const _0xa56190=await _0x5d92ad[_0x50cf89(0x17e,0x170,0x171,0x139,0xe0)]['\x6d\x65\x6d\x62\x65\x72\x73'][_0x5fd6e1(0x67,0x4d,0x4c,0x1d,0x20)](_0x1ef8c5[_0x5fd6e1(0x4b,0x34,0x8e,0x56,0x59)]);_0x887620=_0xa56190['\x75\x73\x65\x72'][_0x50cf89(0x1d0,0x173,0x131,0x191,0x1dc)];const _0x1618f3={};_0x1618f3[_0x50cf89(0x1b3,0x172,0x19c,0x18c,0x1c9)]=_0x5d92ad[_0x50cf89(0x17f,0x13e,0xd8,0x139,0x131)]['\x69\x64'],_0x1618f3[_0x5fd6e1(0x86,0x41,0x70,0x56,0x61)]=_0x1ef8c5[_0x50cf89(0x115,0x141,0x12f,0x150,0x158)];const _0x42e635=await Invite[_0x5fd6e1(0x52,0x69,0xa3,0xb4,0xed)](_0x1618f3);_0x2333ed=_0x42e635[_0x50cf89(0x1a0,0x1e1,0x174,0x19c,0x160)]((_0x3092ae,_0xadcb8c)=>_0x3092ae+_0xadcb8c[_0x50cf89(0x168,0x178,0x171,0x18f,0x1e4)+'\x73'][_0x50cf89(0xe0,0x19b,0xe1,0x13c,0x187)],0x0);}}catch(_0x20863e){console[_0x5fd6e1(0x15,0x1f,0xe,0x3c,0x8c)](_0x5fd6e1(0xdf,0x32,0x6c,0x81,0x58)+_0x50cf89(0x165,0x183,0x148,0x182,0x193)+'\x74\x63\x68\x20\x69\x6e\x76\x69\x74\x65'+_0x50cf89(0x1a2,0x18b,0x106,0x161,0x167)+_0x20863e);}config['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+_0x50cf89(0x189,0x1ed,0x1bb,0x1bd,0x19a)]['\x45\x6e\x61\x62\x6c\x65\x64']&&sendWelcomeMessage(_0x33b3a7,_0x5d92ad,_0x887620,_0x2333ed);await updateStoredMembers(_0x33b3a7,_0x5d92ad[_0x5fd6e1(0x3e,0x78,0x2a,0x3f,0x3b)]['\x69\x64']);function _0x50cf89(_0x4ce650,_0x7871a3,_0x3f30e6,_0x1d1ac1,_0x250555){return _0x247edf(_0x4ce650-0x1b,_0x7871a3-0x15a,_0x250555,_0x1d1ac1- -0x285,_0x250555-0x18e);}try{const _0x4d4b96={};_0x4d4b96[_0x50cf89(0x18b,0xe6,0x116,0x13a,0x108)]=_0x5d92ad['\x69\x64'],_0x4d4b96[_0x50cf89(0x140,0x1a4,0x13d,0x145,0x19e)]=_0x5d92ad[_0x5fd6e1(0x6,0x93,0x18,0x3f,0x8a)]['\x69\x64'];const _0x3f8223=await UserData['\x66\x69\x6e\x64\x4f\x6e\x65'](_0x4d4b96);if(_0x3f8223&&_0x3f8223[_0x50cf89(0x17c,0xdd,0x11e,0x12d,0x15b)]){const _0xc43a60={};_0xc43a60['\x67\x75\x69\x6c\x64\x49\x44']=_0x5d92ad[_0x5fd6e1(0xa,0x21,0x9,0x3f,0x82)]['\x69\x64'];const _0x8ba8de=await GuildData['\x66\x69\x6e\x64\x4f\x6e\x65'](_0xc43a60),_0x3b5b51=_0x8ba8de&&_0x8ba8de[_0x5fd6e1(0xf,0x97,0x9,0x4e,-0x10)+_0x50cf89(0x16e,0x1ba,0x19b,0x1c7,0x210)]?_0x5d92ad[_0x5fd6e1(0x91,-0x24,-0x12,0x3f,0x2b)]['\x72\x6f\x6c\x65\x73'][_0x50cf89(0x125,0x14e,0x19c,0x171,0x183)][_0x50cf89(0x124,0x132,0x19b,0x163,0x12d)](_0x8ba8de[_0x50cf89(0x175,0x10e,0x101,0x148,0x148)+_0x50cf89(0x192,0x1e5,0x1e4,0x1c7,0x1aa)]):null;_0x3b5b51?await _0x5d92ad[_0x5fd6e1(0x65,0x50,0xb,0x63,0x60)][_0x5fd6e1(-0x2,0x7,0x5d,0x49,-0x5)](_0x3b5b51,_0x50cf89(0x18b,0x182,0x1e2,0x1ad,0x20a)+_0x50cf89(0x16b,0x1c8,0x13b,0x192,0x1f2)+_0x50cf89(0x1ca,0x193,0x143,0x1a2,0x1cb)+'\x6e'):console['\x65\x72\x72\x6f\x72'](_0x5fd6e1(0x9f,0x69,0xf2,0xa5,0xcc)+_0x5fd6e1(0x8,0x7e,0x6d,0x5a,0x82)+'\x20\x66\x6f\x72\x20\x67\x75\x69\x6c\x64'+'\x3a\x20'+_0x5d92ad[_0x50cf89(0x18c,0x197,0x13d,0x139,0x174)]['\x69\x64']);}}catch(_0x18cb40){console[_0x5fd6e1(0x64,0x42,0x29,0x3c,-0x20)](_0x5fd6e1(0xce,0xd5,0xe8,0xc8,0xb4)+_0x5fd6e1(-0xe,0x87,0x3,0x45,0x4)+_0x5fd6e1(0x32,0x4f,0x97,0x3b,0x2d)+_0x5fd6e1(0x3e,0x37,-0x22,0x41,0x81)+_0x18cb40);}};async function sendAltPreventionDM(_0x2a23b6){const _0x526b02=createEmbed(config['\x41\x6c\x74\x50\x72\x65\x76\x65\x6e\x74'+'\x69\x6f\x6e']['\x44\x4d'][_0x17f9fb(0x172,0x136,0x17c,0x18e,0x1c4)],_0x2a23b6);function _0x37bea0(_0x104ab8,_0x1e940e,_0x5789fa,_0x5d039d,_0x5f2acf){return _0x3e98d8(_0x5789fa- -0x3b0,_0x1e940e-0x19a,_0x5789fa-0xdf,_0x5d039d,_0x5f2acf-0x3a);}function _0x17f9fb(_0x2d69d6,_0x30ac49,_0x100f69,_0x450753,_0xc5781d){return _0x3e98d8(_0x100f69- -0x232,_0x30ac49-0xfe,_0x100f69-0x1cc,_0x2d69d6,_0xc5781d-0x8e);}try{const _0x1e99c8={};_0x1e99c8[_0x37bea0(-0x13,0x97,0x36,-0x11,0x34)]=[_0x526b02],await _0x2a23b6[_0x37bea0(0x74,0x5d,0x1c,0x5f,0x7)](_0x1e99c8);}catch(_0x503991){console['\x65\x72\x72\x6f\x72']('\x46\x61\x69\x6c\x65\x64\x20\x74\x6f\x20'+_0x17f9fb(0x1ed,0x25d,0x218,0x261,0x23c)+_0x17f9fb(0x207,0x187,0x1de,0x186,0x1b9)+_0x37bea0(0x1f,-0x17,0x2d,0x6a,0x6e)+_0x2a23b6[_0x37bea0(0x6d,0x3b,0x71,0xbc,0x46)][_0x37bea0(0xc5,0x2b,0x7a,0x2d,0xa2)]+'\x3a',_0x503991);}}async function sendAltPreventionLog(_0x6ac380,_0x14a453,_0x4d8a02){const _0x40ccf5=_0x14a453[_0x3aac52(0xaa,0x9a,0xae,0xe5,0xdb)][_0x3aac52(0x78,0x115,0xc8,0xe2,0x95)][_0x3aac52(0xad,0xab,0xe6,0xfd,0xe8)][_0x5328d9(0x381,0x368,0x3ae,0x381,0x3c2)](config[_0x5328d9(0x435,0x401,0x3c9,0x45e,0x403)+_0x3aac52(0xb4,0x149,0x105,0xa9,0xe6)][_0x3aac52(0xc7,0x11f,0xcc,0xc4,0x113)][_0x3aac52(0x142,0x15d,0x103,0x133,0x132)+_0x3aac52(0x76,0x75,0x7e,0x77,0xa9)]);if(!_0x40ccf5)return;const _0x15e4a5=createEmbed(config['\x41\x6c\x74\x50\x72\x65\x76\x65\x6e\x74'+_0x5328d9(0x426,0x3da,0x449,0x42c,0x3ef)][_0x5328d9(0x3a9,0x3c3,0x410,0x399,0x3b6)],_0x14a453);function _0x3aac52(_0x58b849,_0x50ac6a,_0xe606b9,_0x970afc,_0x1ddd75){return _0x3e98d8(_0xe606b9- -0x324,_0x50ac6a-0x60,_0xe606b9-0x169,_0x58b849,_0x1ddd75-0x10d);}const _0x43b952={};_0x43b952[_0x3aac52(0x13e,0xf4,0x11a,0x16c,0x13f)]=_0x5328d9(0x3be,0x3e4,0x421,0x415,0x3d5),_0x43b952['\x76\x61\x6c\x75\x65']=_0x4d8a02?'\x59\x65\x73':'\x4e\x6f';function _0x5328d9(_0xadeb78,_0x292c16,_0x256c06,_0x1b1e76,_0x1af5dc){return _0x247edf(_0xadeb78-0x15a,_0x292c16-0x173,_0x292c16,_0x1af5dc- -0x26,_0x1af5dc-0x15b);}_0x15e4a5[_0x5328d9(0x3d2,0x393,0x3a1,0x3ab,0x3ca)](_0x43b952);try{const _0x1107ed={};_0x1107ed[_0x5328d9(0x3d8,0x358,0x36d,0x39c,0x3ac)]=[_0x15e4a5],await _0x40ccf5[_0x3aac52(0x69,0xb8,0xa8,0xb5,0x104)](_0x1107ed);}catch(_0x3f6aca){console['\x65\x72\x72\x6f\x72'](_0x5328d9(0x315,0x3c2,0x3cc,0x3a6,0x372)+_0x3aac52(0x12b,0xdc,0x126,0x155,0x169)+_0x5328d9(0x374,0x437,0x3c8,0x42c,0x3d6)+_0x3aac52(0xbb,0xd2,0x83,0x4f,0x9e)+_0x14a453[_0x5328d9(0x423,0x395,0x3d7,0x39a,0x3e7)][_0x3aac52(0xe4,0x10f,0x106,0xe1,0x110)]+'\x3a',_0x3f6aca);}}function _0x247edf(_0x1a3523,_0x8ec4e9,_0x22a4d7,_0x46014e,_0x2e3b7a){return _0x48f1(_0x46014e-0x25e,_0x22a4d7);}function createEmbed(_0x13aeae,_0x1ba430){const _0x272c81=new EmbedBuilder()[_0x1abd17(-0x116,-0x157,-0x158,-0x1a7,-0xfe)](_0x13aeae['\x43\x6f\x6c\x6f\x72']),_0x2ccd92={};_0x2ccd92['\x66\x6f\x72\x6d\x61\x74']=_0x1abd17(-0x18b,-0x135,-0x13c,-0x105,-0x152),_0x2ccd92[_0xe8435(0x286,0x2f7,0x2aa,0x308,0x2ca)]=!![],_0x2ccd92[_0x1abd17(-0x152,-0x121,-0x136,-0x168,-0x107)]=0x1000;function _0x1abd17(_0x4d0618,_0x3ddcd0,_0x5c8a7b,_0x13c55a,_0x10b56c){return _0x3e98d8(_0x5c8a7b- -0x585,_0x3ddcd0-0x117,_0x5c8a7b-0xbd,_0x10b56c,_0x10b56c-0x1d4);}const _0x1b569b=_0x1ba430[_0xe8435(0x2ec,0x2fb,0x326,0x30c,0x339)][_0x1abd17(-0x240,-0x1d5,-0x1e1,-0x211,-0x18b)+_0x1abd17(-0x1ca,-0x1a1,-0x1ae,-0x1ff,-0x172)](_0x2ccd92),_0x117e2c={};_0x117e2c['\x66\x6f\x72\x6d\x61\x74']='\x70\x6e\x67';function _0xe8435(_0x23cbb6,_0x437a80,_0x43b16b,_0x2e3ba6,_0x40dcd6){return _0x3e98d8(_0x43b16b- -0xfb,_0x437a80-0x1c1,_0x43b16b-0x9d,_0x40dcd6,_0x40dcd6-0x9f);}_0x117e2c[_0xe8435(0x2c5,0x261,0x2aa,0x281,0x280)]=!![],_0x117e2c['\x73\x69\x7a\x65']=0x1000;const _0x151ac9=_0x1ba430[_0xe8435(0x30a,0x2ac,0x2d7,0x280,0x276)][_0x1abd17(-0x18d,-0x1cf,-0x1a2,-0x1dd,-0x196)](_0x117e2c);if(_0x13aeae['\x54\x69\x74\x6c\x65'])_0x272c81[_0x1abd17(-0x19e,-0x166,-0x14e,-0x126,-0x16b)](replacePlaceholders(_0x13aeae['\x54\x69\x74\x6c\x65'],_0x1ba430,'',0x0,_0x1b569b,'',![]));if(_0x13aeae[_0xe8435(0x2d4,0x2ac,0x308,0x36a,0x33c)+'\x6e']){const _0x2ce303=_0x13aeae[_0x1abd17(-0x1ca,-0x162,-0x182,-0x16e,-0x1ae)+'\x6e'][_0xe8435(0x290,0x288,0x2c3,0x2f1,0x2af)]('\x0a');_0x272c81[_0x1abd17(-0x15c,-0x160,-0x16f,-0x151,-0x181)+_0xe8435(0x38a,0x2f5,0x347,0x31b,0x33b)](replacePlaceholders(_0x2ce303,_0x1ba430,'',0x0,_0x1b569b,'',!![]));}_0x13aeae[_0x1abd17(-0x1e5,-0x1db,-0x193,-0x1a2,-0x144)]['\x54\x65\x78\x74']&&_0x272c81[_0x1abd17(-0x1c0,-0x12e,-0x165,-0x15b,-0x10a)]({'\x6e\x61\x6d\x65':replacePlaceholders(_0x13aeae[_0x1abd17(-0x1d2,-0x1cd,-0x193,-0x159,-0x1b4)][_0xe8435(0x27b,0x2a4,0x2b9,0x2ef,0x293)],_0x1ba430,'',0x0,_0x1b569b,'',![]),'\x69\x63\x6f\x6e\x55\x52\x4c':_0x13aeae[_0x1abd17(-0x17d,-0x1b6,-0x193,-0x134,-0x1b7)][_0x1abd17(-0x1df,-0x1ca,-0x1bd,-0x177,-0x1a4)]?replacePlaceholders(_0x13aeae[_0xe8435(0x2b7,0x2f3,0x2f7,0x2dd,0x34d)][_0x1abd17(-0x198,-0x1ca,-0x1bd,-0x206,-0x20b)],_0x1ba430,'',0x0,_0x1b569b,_0x151ac9,![]):undefined});_0x13aeae[_0x1abd17(-0x1ca,-0x20c,-0x1d4,-0x208,-0x1e3)][_0x1abd17(-0x175,-0x172,-0x1d1,-0x207,-0x1ce)]&&_0x272c81[_0x1abd17(-0x199,-0x171,-0x194,-0x142,-0x1e1)]({'\x74\x65\x78\x74':replacePlaceholders(_0x13aeae[_0x1abd17(-0x1c5,-0x218,-0x1d4,-0x203,-0x203)][_0xe8435(0x289,0x2c8,0x2b9,0x304,0x265)],_0x1ba430,'',0x0,_0x1b569b,'',![]),'\x69\x63\x6f\x6e\x55\x52\x4c':_0x13aeae[_0xe8435(0x296,0x27d,0x2b6,0x2a4,0x2e1)][_0x1abd17(-0x184,-0x18f,-0x1bd,-0x1c5,-0x1e7)]?replacePlaceholders(_0x13aeae[_0x1abd17(-0x1cb,-0x207,-0x1d4,-0x22f,-0x195)][_0x1abd17(-0x1e1,-0x1b3,-0x1bd,-0x18d,-0x175)],_0x1ba430,'',0x0,_0x1b569b,_0x151ac9,![]):undefined});if(_0x13aeae[_0xe8435(0x2fe,0x302,0x2f8,0x33e,0x303)]){const _0x2688bc=replacePlaceholders(_0x13aeae[_0xe8435(0x31e,0x2b8,0x2f8,0x2cd,0x34f)],_0x1ba430,'',0x0,_0x1b569b,_0x151ac9,![]);if(_0x2688bc)_0x272c81[_0x1abd17(-0x132,-0xdd,-0x122,-0x147,-0xdf)+'\x69\x6c'](_0x2688bc);}if(_0x13aeae[_0xe8435(0x2e6,0x32f,0x30b,0x344,0x2f9)]){const _0x56782b=replacePlaceholders(_0x13aeae['\x49\x6d\x61\x67\x65'],_0x1ba430,'',0x0,_0x1b569b,_0x151ac9,![]);if(_0x56782b)_0x272c81[_0xe8435(0x31b,0x33a,0x349,0x35f,0x374)](_0x56782b);}return _0x272c81;}async function sendWelcomeMessage(_0x17d536,_0x248062,_0x5a9876,_0x2fd20f){function _0x6713e4(_0x29305d,_0x4d0baf,_0x2c979d,_0x13c9a3,_0x10ce58){return _0x3e98d8(_0x29305d- -0x85,_0x4d0baf-0x1ad,_0x2c979d-0xea,_0x4d0baf,_0x10ce58-0xb6);}function _0xa55467(_0xab3e0e,_0x352cd6,_0x17970a,_0x38da74,_0xdaa658){return _0x3e98d8(_0x17970a- -0x4ec,_0x352cd6-0x14e,_0x17970a-0x116,_0xdaa658,_0xdaa658-0x65);}let _0x1ad6ac=_0x248062['\x67\x75\x69\x6c\x64']['\x63\x68\x61\x6e\x6e\x65\x6c\x73'][_0x6713e4(0x385,0x35e,0x3ca,0x38b,0x353)]['\x67\x65\x74'](config['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+_0x6713e4(0x3d1,0x40d,0x37f,0x405,0x3fd)]['\x43\x68\x61\x6e\x6e\x65\x6c\x49\x44']);if(_0x1ad6ac&&!sentWelcomeEmbeds['\x68\x61\x73'](_0x248062['\x69\x64'])){const _0x4cecaf={};_0x4cecaf[_0x6713e4(0x360,0x32f,0x383,0x390,0x374)]='\x70\x6e\x67',_0x4cecaf[_0x6713e4(0x320,0x321,0x312,0x2e6,0x2e7)]=!![],_0x4cecaf['\x73\x69\x7a\x65']=0x1000;const _0x9187e2=_0x248062[_0xa55467(-0xc4,-0x108,-0xcb,-0x103,-0xbe)][_0x6713e4(0x31f,0x350,0x346,0x2cd,0x32d)+_0x6713e4(0x352,0x32c,0x36c,0x366,0x3a0)](_0x4cecaf),_0x44ff23=await getUserBannerURL(_0x248062);let _0xca5cbc='';(config[_0x6713e4(0x332,0x361,0x36b,0x360,0x355)+_0xa55467(-0x7f,-0x91,-0x96,-0x85,-0x79)][_0xa55467(-0x143,-0xbb,-0xfd,-0x141,-0x12a)]===_0x6713e4(0x3a1,0x3cf,0x34f,0x35a,0x3df)||config[_0xa55467(-0xe5,-0xed,-0x135,-0x177,-0x181)+_0xa55467(-0x36,-0x85,-0x96,-0xc9,-0xca)][_0xa55467(-0xee,-0x14c,-0xfd,-0x9c,-0x138)]===_0xa55467(-0x7a,-0x3e,-0xa1,-0xdf,-0xed))&&(_0xca5cbc=replacePlaceholders(config['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+_0x6713e4(0x3d1,0x3f3,0x3e5,0x374,0x386)][_0x6713e4(0x32f,0x388,0x37a,0x2fe,0x390)],_0x248062,_0x5a9876,_0x2fd20f,_0x9187e2,_0x44ff23,![]));let _0x15f6a8=null;if(config[_0xa55467(-0x121,-0x106,-0x135,-0x14b,-0x170)+'\x73\x61\x67\x65'][_0x6713e4(0x36a,0x346,0x347,0x36e,0x357)]===_0xa55467(-0xc7,-0xca,-0xb6,-0xe1,-0xcf)||config[_0x6713e4(0x332,0x2ec,0x325,0x364,0x2cf)+_0xa55467(-0x43,-0xea,-0x96,-0xa4,-0xc1)][_0x6713e4(0x36a,0x313,0x3a4,0x361,0x374)]===_0x6713e4(0x3c6,0x3c9,0x3a3,0x420,0x3e3)){_0x15f6a8=new EmbedBuilder()[_0xa55467(-0x90,-0xba,-0xbf,-0xac,-0xef)](config['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+'\x73\x61\x67\x65'][_0xa55467(-0x198,-0xec,-0x13e,-0x104,-0x115)][_0x6713e4(0x39a,0x3b6,0x3b2,0x3f9,0x369)]);let _0x5cd40c=replacePlaceholders(config['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+_0xa55467(-0x58,-0x6a,-0x96,-0xf6,-0x47)][_0xa55467(-0x114,-0x13e,-0x13e,-0x135,-0x14f)][_0xa55467(-0xa5,-0xdc,-0xb3,-0x73,-0x108)],_0x248062,_0x5a9876,_0x2fd20f,_0x9187e2,_0x44ff23,![]);_0x5cd40c&&_0x5cd40c!==''&&_0x15f6a8[_0x6713e4(0x3b2,0x3c1,0x35c,0x3fe,0x3fe)](_0x5cd40c);let _0x1d0905=replacePlaceholders(config[_0x6713e4(0x332,0x33b,0x364,0x373,0x2f3)+'\x73\x61\x67\x65'][_0xa55467(-0xef,-0xf8,-0x13e,-0x14d,-0x101)][_0x6713e4(0x37e,0x338,0x335,0x34b,0x3b7)+'\x6e'][_0xa55467(-0x146,-0x15f,-0x12e,-0x14f,-0x157)]('\x0a'),_0x248062,_0x5a9876,_0x2fd20f,_0x9187e2,_0x44ff23,!![]);_0x1d0905&&_0x1d0905!==''&&_0x15f6a8[_0xa55467(-0xdc,-0xc2,-0xd6,-0x7b,-0x112)+_0xa55467(-0xf5,-0x9e,-0xaa,-0x97,-0xbd)](_0x1d0905);let _0xf6db9b=replacePlaceholders(config[_0xa55467(-0x17d,-0x17f,-0x135,-0x107,-0x11a)+_0xa55467(-0x4c,-0x83,-0x96,-0x73,-0xed)][_0x6713e4(0x329,0x35d,0x388,0x384,0x346)][_0x6713e4(0x36d,0x316,0x336,0x3a4,0x396)][_0xa55467(-0xe3,-0x163,-0x138,-0x127,-0x10f)],_0x248062,_0x5a9876,_0x2fd20f,_0x9187e2,_0x44ff23,![]),_0x383e71=replacePlaceholders(config[_0xa55467(-0x16c,-0x117,-0x135,-0x18c,-0x152)+_0x6713e4(0x3d1,0x3ad,0x3a6,0x41f,0x40c)][_0x6713e4(0x329,0x34e,0x320,0x35a,0x2e3)][_0x6713e4(0x36d,0x336,0x3af,0x3d0,0x328)][_0x6713e4(0x343,0x2ff,0x35f,0x2e4,0x2e2)],_0x248062,_0x5a9876,_0x2fd20f,_0x9187e2,_0x44ff23,![]);if(_0xf6db9b&&_0xf6db9b!==''){const _0x30d86f={};_0x30d86f[_0xa55467(-0xb4,-0xa7,-0xae,-0x5c,-0x100)]=_0xf6db9b,_0x30d86f['\x69\x63\x6f\x6e\x55\x52\x4c']=_0x383e71||undefined,_0x15f6a8[_0x6713e4(0x39b,0x354,0x3d1,0x3ba,0x3ec)](_0x30d86f);}let _0x36b7a9=replacePlaceholders(config[_0xa55467(-0xe3,-0x141,-0x135,-0xef,-0x15c)+_0xa55467(-0xe2,-0x4e,-0x96,-0xa2,-0x68)]['\x45\x6d\x62\x65\x64'][_0xa55467(-0x13a,-0x166,-0x13b,-0x138,-0x156)][_0x6713e4(0x32f,0x377,0x31e,0x340,0x2e1)],_0x248062,_0x5a9876,_0x2fd20f,_0x9187e2,_0x44ff23,![]),_0x39acad=replacePlaceholders(config['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+'\x73\x61\x67\x65'][_0xa55467(-0x116,-0x122,-0x13e,-0x12d,-0x10e)][_0xa55467(-0xeb,-0x19c,-0x13b,-0xde,-0x18b)][_0x6713e4(0x343,0x2ea,0x342,0x310,0x323)],_0x248062,_0x5a9876,_0x2fd20f,_0x9187e2,_0x44ff23,![]);if(_0x36b7a9&&_0x36b7a9!==''){const _0x46b7f8={};_0x46b7f8['\x74\x65\x78\x74']=_0x36b7a9,_0x46b7f8[_0x6713e4(0x35e,0x34a,0x32d,0x3c0,0x3b4)]=_0x39acad||undefined,_0x15f6a8['\x73\x65\x74\x46\x6f\x6f\x74\x65\x72'](_0x46b7f8);}if(config[_0xa55467(-0xfe,-0xe4,-0x135,-0xef,-0x15e)+_0x6713e4(0x3d1,0x42b,0x412,0x42b,0x398)][_0x6713e4(0x329,0x37e,0x371,0x346,0x30e)][_0xa55467(-0x119,-0x10b,-0xf9,-0x103,-0x104)]&&config[_0xa55467(-0x173,-0xd4,-0x135,-0x11a,-0x167)+_0x6713e4(0x3d1,0x42b,0x3e9,0x3ea,0x393)][_0x6713e4(0x329,0x30d,0x313,0x344,0x300)]['\x54\x68\x75\x6d\x62\x6e\x61\x69\x6c']!==''){let _0x56cc90=config[_0xa55467(-0x18f,-0x129,-0x135,-0x11a,-0x179)+_0xa55467(-0xa7,-0xdd,-0x96,-0x3c,-0xea)]['\x45\x6d\x62\x65\x64'][_0xa55467(-0x121,-0xad,-0xf9,-0xc5,-0xd0)]===_0x6713e4(0x39d,0x3bc,0x393,0x396,0x397)+'\x61\x72\x7d'?_0x9187e2:config[_0xa55467(-0x152,-0x175,-0x135,-0x115,-0x10b)+_0xa55467(-0x88,-0x3d,-0x96,-0xd1,-0x56)][_0xa55467(-0x118,-0x15f,-0x13e,-0x152,-0x16e)][_0xa55467(-0xe6,-0x12a,-0xf9,-0xd1,-0x142)];_0x56cc90&&_0x15f6a8[_0xa55467(-0x99,-0x6c,-0x89,-0xbe,-0xa5)+'\x69\x6c'](_0x56cc90);}if(config['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+'\x73\x61\x67\x65'][_0xa55467(-0x173,-0x143,-0x13e,-0x148,-0x182)][_0xa55467(-0x92,-0xe9,-0xe6,-0x126,-0xf2)]&&config[_0x6713e4(0x332,0x389,0x302,0x352,0x30d)+_0xa55467(-0x98,-0xa2,-0x96,-0xa3,-0xed)][_0x6713e4(0x329,0x321,0x2f7,0x2e6,0x350)]['\x49\x6d\x61\x67\x65']!==''){let _0x192903=config[_0x6713e4(0x332,0x2fd,0x2e1,0x2f9,0x358)+_0x6713e4(0x3d1,0x3f7,0x3e9,0x3e5,0x3d1)][_0x6713e4(0x329,0x349,0x363,0x2f3,0x34b)][_0xa55467(-0xbb,-0xc7,-0xe6,-0xb8,-0x8d)]==='\x7b\x75\x73\x65\x72\x42\x61\x6e\x6e\x65'+'\x72\x7d'?_0x44ff23:config[_0xa55467(-0x13b,-0x13d,-0x135,-0xec,-0x197)+_0x6713e4(0x3d1,0x40e,0x3a7,0x3c7,0x3c0)]['\x45\x6d\x62\x65\x64'][_0x6713e4(0x381,0x370,0x3be,0x332,0x3d2)];_0x192903&&_0x15f6a8['\x73\x65\x74\x49\x6d\x61\x67\x65'](_0x192903);}}try{if(config[_0x6713e4(0x332,0x31d,0x2f2,0x36d,0x31c)+_0xa55467(-0x53,-0xde,-0x96,-0x57,-0x59)][_0x6713e4(0x36a,0x328,0x3b5,0x380,0x338)]===_0xa55467(-0x41,-0x85,-0xa1,-0x9c,-0x103)){const _0xd67866={};_0xd67866['\x63\x6f\x6e\x74\x65\x6e\x74']=_0xca5cbc,_0xd67866[_0xa55467(-0x160,-0x11e,-0x106,-0x139,-0xc0)]=[_0x15f6a8],await _0x1ad6ac[_0x6713e4(0x347,0x384,0x3a4,0x33a,0x341)](_0xd67866);}else{if(config['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+_0x6713e4(0x3d1,0x3b8,0x3ef,0x408,0x371)][_0xa55467(-0x10e,-0x13d,-0xfd,-0x9b,-0xf8)]===_0x6713e4(0x3a1,0x399,0x3ce,0x388,0x3ef))await _0x1ad6ac[_0xa55467(-0x166,-0x13c,-0x120,-0x12b,-0x152)](_0xca5cbc);else{if(config[_0xa55467(-0xe2,-0x158,-0x135,-0xd7,-0x177)+_0xa55467(-0xc6,-0xb8,-0x96,-0x49,-0x8e)][_0x6713e4(0x36a,0x352,0x33a,0x342,0x327)]==='\x45\x4d\x42\x45\x44'){const _0x1b0848={};_0x1b0848['\x65\x6d\x62\x65\x64\x73']=[_0x15f6a8],await _0x1ad6ac[_0x6713e4(0x347,0x2eb,0x30e,0x2e9,0x334)](_0x1b0848);}}}}catch(_0x4d7098){console['\x65\x72\x72\x6f\x72'](_0xa55467(-0xc7,-0xbc,-0xd8,-0xc0,-0xe8)+_0x6713e4(0x362,0x33d,0x36d,0x3b3,0x3be)+_0xa55467(-0x19a,-0x195,-0x13d,-0x189,-0x158)+_0x6713e4(0x373,0x331,0x346,0x335,0x351)+_0x4d7098);}sentWelcomeEmbeds[_0x6713e4(0x357,0x34c,0x395,0x3b0,0x340)](_0x248062['\x69\x64']),config['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+_0xa55467(-0x8c,-0xa3,-0x96,-0xe3,-0x7c)]['\x44\x4d'][_0x6713e4(0x389,0x370,0x3ab,0x340,0x360)]&&sendWelcomeDM(_0x248062,_0x5a9876,_0x2fd20f,_0x9187e2,_0x44ff23);}}async function sendWelcomeDM(_0x2e3209,_0x3f5320,_0xb1575a,_0xf4142a,_0x359b30){let _0xd84e28=new EmbedBuilder()[_0x485c2c(-0xc3,-0xf0,-0x105,-0xaa,-0xed)](config[_0x485c2c(-0x123,-0x166,-0x116,-0x151,-0x163)+_0x5258ad(0x3c8,0x36c,0x3cf,0x3b0,0x3a2)]['\x44\x4d'][_0x485c2c(-0x18e,-0x111,-0x12f,-0x14f,-0x16c)][_0x485c2c(-0xbf,-0xc6,-0x113,-0x142,-0xfb)]),_0x1d702c=replacePlaceholders(config[_0x485c2c(-0x14c,-0x147,-0x15a,-0x177,-0x163)+_0x5258ad(0x3c8,0x3f3,0x427,0x3bc,0x3c5)]['\x44\x4d']['\x45\x6d\x62\x65\x64'][_0x485c2c(-0xd1,-0x112,-0x129,-0xae,-0xe1)],_0x2e3209,_0x3f5320,_0xb1575a,_0xf4142a,_0x359b30,![]);if(_0x1d702c&&_0x1d702c!=='')_0xd84e28[_0x485c2c(-0x9f,-0x10a,-0xcc,-0x134,-0xe3)](_0x1d702c);let _0x5225f6=replacePlaceholders(config[_0x5258ad(0x329,0x30e,0x38c,0x37a,0x348)+_0x485c2c(-0xe0,-0x11b,-0xb7,-0x9e,-0xc4)]['\x44\x4d']['\x45\x6d\x62\x65\x64'][_0x5258ad(0x375,0x324,0x340,0x346,0x3c7)+'\x6e'][_0x485c2c(-0x104,-0x192,-0x140,-0x114,-0x15c)]('\x0a'),_0x2e3209,_0x3f5320,_0xb1575a,_0xf4142a,_0x359b30,!![]);function _0x485c2c(_0x1fae6f,_0x153ad9,_0x4176f5,_0x1ab021,_0x3f5380){return _0x247edf(_0x1fae6f-0x5,_0x153ad9-0x6d,_0x1fae6f,_0x3f5380- -0x506,_0x3f5380-0xb1);}if(_0x5225f6&&_0x5225f6!=='')_0xd84e28['\x73\x65\x74\x44\x65\x73\x63\x72\x69\x70'+_0x5258ad(0x3b4,0x3ad,0x367,0x412,0x373)](_0x5225f6);let _0x37ccf6=replacePlaceholders(config[_0x485c2c(-0x136,-0x19f,-0x17b,-0x154,-0x163)+'\x73\x61\x67\x65']['\x44\x4d']['\x45\x6d\x62\x65\x64'][_0x5258ad(0x364,0x369,0x384,0x386,0x39d)][_0x5258ad(0x326,0x326,0x2da,0x2ca,0x332)],_0x2e3209,_0x3f5320,_0xb1575a,_0xf4142a,_0x359b30,![]),_0x25a3b5=replacePlaceholders(config[_0x485c2c(-0x1a1,-0x1a0,-0x176,-0x18b,-0x163)+_0x485c2c(-0xd8,-0xb9,-0x11b,-0x9b,-0xc4)]['\x44\x4d'][_0x485c2c(-0x145,-0x164,-0x196,-0x13f,-0x16c)][_0x485c2c(-0xf8,-0xde,-0x119,-0x17c,-0x128)][_0x5258ad(0x33a,0x2e3,0x2fe,0x2ec,0x395)],_0x2e3209,_0x3f5320,_0xb1575a,_0xf4142a,_0x359b30,![]);if(_0x37ccf6&&_0x37ccf6!==''){const _0x4b8318={};_0x4b8318[_0x485c2c(-0xe2,-0x9a,-0xb8,-0x101,-0xdc)]=_0x37ccf6,_0x4b8318[_0x485c2c(-0x12f,-0x170,-0xdb,-0x178,-0x137)]=_0x25a3b5||undefined,_0xd84e28['\x73\x65\x74\x41\x75\x74\x68\x6f\x72'](_0x4b8318);}let _0x171ac1=replacePlaceholders(config[_0x485c2c(-0x125,-0x1ae,-0x1b4,-0x198,-0x163)+_0x5258ad(0x3c8,0x3f6,0x3e3,0x411,0x3be)]['\x44\x4d'][_0x485c2c(-0x127,-0x16b,-0x184,-0x122,-0x16c)]['\x46\x6f\x6f\x74\x65\x72'][_0x5258ad(0x326,0x2e9,0x321,0x2f6,0x325)],_0x2e3209,_0x3f5320,_0xb1575a,_0xf4142a,_0x359b30,![]),_0x4bea7b=replacePlaceholders(config[_0x485c2c(-0x153,-0x1a0,-0x124,-0x159,-0x163)+'\x73\x61\x67\x65']['\x44\x4d'][_0x485c2c(-0x18a,-0x13a,-0x154,-0x170,-0x16c)]['\x46\x6f\x6f\x74\x65\x72'][_0x5258ad(0x33a,0x2ee,0x2f4,0x358,0x314)],_0x2e3209,_0x3f5320,_0xb1575a,_0xf4142a,_0x359b30,![]);if(_0x171ac1&&_0x171ac1!==''){const _0x508131={};_0x508131['\x74\x65\x78\x74']=_0x171ac1,_0x508131[_0x485c2c(-0x12b,-0xfd,-0x13b,-0xf4,-0x137)]=_0x4bea7b||undefined,_0xd84e28['\x73\x65\x74\x46\x6f\x6f\x74\x65\x72'](_0x508131);}function _0x5258ad(_0x8d99d4,_0x36daf9,_0x49d859,_0x1d6e62,_0x50e7c8){return _0x3e98d8(_0x8d99d4- -0x8e,_0x36daf9-0x12,_0x49d859-0x132,_0x1d6e62,_0x50e7c8-0xe);}if(config[_0x485c2c(-0x14e,-0x125,-0x13c,-0x1b5,-0x163)+_0x485c2c(-0x10f,-0x9e,-0xd5,-0xf0,-0xc4)]['\x44\x4d'][_0x5258ad(0x320,0x376,0x375,0x360,0x305)][_0x5258ad(0x365,0x328,0x374,0x36b,0x309)]&&config[_0x485c2c(-0x193,-0x154,-0x1ad,-0x16b,-0x163)+'\x73\x61\x67\x65']['\x44\x4d'][_0x485c2c(-0x1a5,-0x1c1,-0x166,-0x191,-0x16c)][_0x5258ad(0x365,0x321,0x3a3,0x359,0x321)]!==''){let _0xd0c9f2=config['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+'\x73\x61\x67\x65']['\x44\x4d']['\x45\x6d\x62\x65\x64'][_0x5258ad(0x365,0x39c,0x34d,0x339,0x3c0)]===_0x485c2c(-0x152,-0xee,-0x14d,-0xbd,-0xf8)+_0x485c2c(-0xd0,-0x7c,-0xa7,-0x8d,-0xdb)?_0xf4142a:config['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+_0x5258ad(0x3c8,0x403,0x424,0x3e6,0x3e6)]['\x44\x4d'][_0x5258ad(0x320,0x2dd,0x337,0x30c,0x304)][_0x5258ad(0x365,0x395,0x31a,0x3a1,0x330)];_0xd0c9f2&&_0xd84e28[_0x5258ad(0x3d5,0x39e,0x381,0x3cb,0x406)+'\x69\x6c'](_0xd0c9f2);}if(config[_0x485c2c(-0x1bb,-0x133,-0x1c5,-0x14d,-0x163)+_0x5258ad(0x3c8,0x3fd,0x3b7,0x3c8,0x3a4)]['\x44\x4d'][_0x5258ad(0x320,0x343,0x2e7,0x32d,0x37b)][_0x485c2c(-0x173,-0x16a,-0xd2,-0xf2,-0x114)]&&config['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+_0x5258ad(0x3c8,0x3da,0x386,0x429,0x37f)]['\x44\x4d']['\x45\x6d\x62\x65\x64']['\x49\x6d\x61\x67\x65']!==''){let _0xc1568d=config['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+_0x5258ad(0x3c8,0x408,0x413,0x38d,0x419)]['\x44\x4d'][_0x485c2c(-0x17e,-0x1bf,-0x114,-0x1cb,-0x16c)][_0x5258ad(0x378,0x37f,0x3d7,0x3c8,0x362)]===_0x485c2c(-0x176,-0x161,-0x18e,-0x197,-0x15b)+'\x72\x7d'?_0x359b30:config[_0x485c2c(-0x17b,-0x170,-0x13b,-0x1ad,-0x163)+_0x485c2c(-0x82,-0x113,-0x11b,-0x67,-0xc4)]['\x44\x4d'][_0x5258ad(0x320,0x307,0x2fe,0x300,0x2ea)][_0x5258ad(0x378,0x39c,0x32d,0x378,0x331)];_0xc1568d&&_0xd84e28[_0x5258ad(0x3b6,0x3a8,0x3e5,0x3eb,0x37d)](_0xc1568d);}try{const _0x28cf92={};_0x28cf92[_0x485c2c(-0xda,-0x161,-0xdb,-0x12a,-0x134)]=[_0xd84e28],await _0x2e3209[_0x485c2c(-0xed,-0x166,-0x117,-0x1a1,-0x14e)](_0x28cf92);}catch(_0x46bb4e){console[_0x485c2c(-0x165,-0x187,-0x18a,-0x136,-0x14b)](_0x5258ad(0x386,0x3d0,0x3a5,0x34b,0x358)+_0x5258ad(0x359,0x357,0x2f8,0x3ad,0x364)+_0x5258ad(0x321,0x311,0x364,0x337,0x31c)+'\x20\x44\x4d\x3a\x20'+_0x46bb4e);}}async function getUserBannerURL(_0x1989a9){function _0x57ef12(_0x3fe65a,_0x53e91d,_0x2bac1c,_0x4d43cc,_0x37a7af){return _0x3e98d8(_0x53e91d- -0x466,_0x53e91d-0xd,_0x2bac1c-0xcf,_0x3fe65a,_0x37a7af-0xf1);}function _0x59cb30(_0x172463,_0x1a4c5e,_0x65be1f,_0x17152a,_0x1a55eb){return _0x3e98d8(_0x1a55eb- -0x35e,_0x1a4c5e-0x27,_0x65be1f-0xfb,_0x1a4c5e,_0x1a55eb-0x25);}try{const _0x18ac49=await _0x1989a9['\x75\x73\x65\x72'][_0x57ef12(-0xb6,-0xb6,-0x67,-0x108,-0x80)](),_0x2ab584={};return _0x2ab584[_0x59cb30(0xc7,0xc6,0x76,0x65,0x87)]=_0x57ef12(-0x40,-0x1d,0x25,-0x7e,0x21),_0x2ab584[_0x59cb30(0x1,0x85,0x12,-0x4,0x47)]=!![],_0x2ab584['\x73\x69\x7a\x65']=0x1000,_0x18ac49[_0x57ef12(0x39,-0x5,-0x15,0x4c,-0x20)](_0x2ab584)||'';}catch(_0x4a9feb){return'';}}function replacePlaceholders(_0x351b85,_0x28637e,_0xc2667='',_0x2cd3bb=0x0,_0x22ec78,_0x1e1e95,_0xf17008){function _0x5a7764(_0x3b5bba,_0x3239f0,_0x39c0fb,_0x1e40c3,_0x196d0b){return _0x247edf(_0x3b5bba-0x1c0,_0x3239f0-0x1a5,_0x196d0b,_0x1e40c3- -0x2ef,_0x196d0b-0x14);}const _0x118c9e=moment()['\x74\x7a'](config[_0x3321cb(0x4a9,0x4e9,0x49f,0x494,0x4c9)]),_0x33e4b7={};_0x33e4b7[_0x3321cb(0x445,0x4f1,0x49a,0x44f,0x44d)]=_0x5a7764(0x18e,0x16c,0x14d,0x146,0x109),_0x33e4b7['\x64\x79\x6e\x61\x6d\x69\x63']=!![],_0x33e4b7[_0x3321cb(0x55c,0x530,0x504,0x4c2,0x534)]=0x1000;const _0x58d193=_0x28637e['\x67\x75\x69\x6c\x64'][_0x5a7764(0xf9,0x9f,0xac,0xe0,0xf3)](_0x33e4b7)||'',_0x493ade=moment(_0x28637e[_0x5a7764(0x139,0x115,0x10d,0xe5,0x129)])['\x74\x7a'](config[_0x3321cb(0x4b5,0x4d9,0x49f,0x492,0x4ce)])[_0x5a7764(0xe7,0xe9,0xae,0xe2,0x8c)](_0x3321cb(0x4ef,0x514,0x4bd,0x4ae,0x4d4)+'\x59\x59'),_0x329860=moment(_0x28637e[_0x3321cb(0x4bf,0x440,0x49d,0x49a,0x452)])['\x74\x7a'](config[_0x3321cb(0x4bc,0x442,0x49f,0x48d,0x445)])[_0x3321cb(0x4cb,0x474,0x49a,0x48a,0x43f)](_0x3321cb(0x539,0x500,0x50a,0x501,0x517)),_0x4fc827=moment(_0x28637e['\x75\x73\x65\x72'][_0x3321cb(0x536,0x503,0x508,0x534,0x558)])['\x74\x7a'](config[_0x3321cb(0x4ce,0x4f8,0x49f,0x495,0x4bd)])[_0x5a7764(0xaf,0x120,0xbe,0xe2,0xd2)](_0x3321cb(0x474,0x4ea,0x4bd,0x505,0x498)+'\x59\x59');let _0x5bc991=_0xf17008?'\x3c\x74\x3a'+Math[_0x5a7764(0x133,0xda,0x152,0xfd,0xf0)](_0x118c9e[_0x3321cb(0x4ff,0x4a1,0x4f8,0x4eb,0x513)]())+_0x3321cb(0x49e,0x491,0x4b7,0x4b3,0x50b):_0x118c9e['\x66\x6f\x72\x6d\x61\x74'](_0x3321cb(0x4bb,0x4d0,0x50a,0x4d2,0x51f));function _0x3321cb(_0x4741f9,_0x4a28b2,_0x2ae116,_0x47f7e0,_0xf66960){return _0x3e98d8(_0x2ae116-0xb5,_0x4a28b2-0x50,_0x2ae116-0xb9,_0x4741f9,_0xf66960-0x86);}let _0x4866cb=_0xf17008?_0x3321cb(0x48c,0x50b,0x4e7,0x4e6,0x4ff)+Math[_0x3321cb(0x4a8,0x502,0x4b5,0x45e,0x4b8)](_0x118c9e['\x75\x6e\x69\x78']())+_0x5a7764(0x144,0x129,0x18d,0x12e,0xd4):_0x118c9e['\x66\x6f\x72\x6d\x61\x74'](_0x3321cb(0x4f8,0x49f,0x4bd,0x506,0x4da)+'\x59\x59'),_0x149dde=_0xf17008?_0x5a7764(0x187,0x121,0x10b,0x12f,0xf5)+Math[_0x5a7764(0xfd,0xcd,0x13d,0xfd,0x12d)](moment(_0x28637e[_0x5a7764(0xf4,0x9f,0x93,0xe5,0xca)])[_0x5a7764(0x176,0x142,0xe5,0x140,0x19c)]())+_0x3321cb(0x4e1,0x500,0x4aa,0x490,0x463):_0x493ade,_0x97c8d7=_0xf17008?_0x3321cb(0x4db,0x49e,0x4e7,0x531,0x4c8)+Math[_0x3321cb(0x48b,0x456,0x4b5,0x4ed,0x47b)](moment(_0x28637e['\x6a\x6f\x69\x6e\x65\x64\x41\x74'])[_0x5a7764(0xec,0x13b,0x110,0x140,0x17f)]())+'\x3a\x74\x3e':_0x329860;return _0x351b85[_0x3321cb(0x48b,0x4eb,0x4e8,0x497,0x4c2)](/{user}/g,'\x3c\x40'+_0x28637e['\x69\x64']+'\x3e')[_0x5a7764(0x166,0x104,0x151,0x130,0x12b)](/{newDisplayName}/g,_0x28637e[_0x5a7764(0x16d,0xea,0xdb,0x10a,0x154)+'\x65'])[_0x3321cb(0x4ed,0x4f8,0x4e8,0x501,0x493)](/{userName}/g,_0x28637e[_0x5a7764(0x13d,0xc0,0x10f,0x11e,0xde)][_0x5a7764(0x1b6,0x1a8,0xf8,0x155,0x180)])[_0x3321cb(0x515,0x524,0x4e8,0x4f3,0x50f)](/{userTag}/g,_0x28637e[_0x5a7764(0x12b,0x139,0x12a,0x11e,0x153)][_0x5a7764(0x107,0xd3,0x121,0x127,0x13f)])[_0x3321cb(0x4fb,0x4ac,0x4e8,0x48b,0x548)](/{userId}/g,_0x28637e['\x75\x73\x65\x72']['\x69\x64'])['\x72\x65\x70\x6c\x61\x63\x65'](/{user-createdAt}/g,moment(_0x28637e[_0x5a7764(0xfe,0xd2,0x10b,0x11e,0xc5)][_0x5a7764(0x1a2,0x173,0x134,0x150,0x137)])['\x74\x7a'](config[_0x5a7764(0xee,0x122,0xf0,0xe7,0x122)])['\x66\x6f\x72\x6d\x61\x74'](_0x5a7764(0x1b6,0x195,0x16b,0x15f,0x149)))['\x72\x65\x70\x6c\x61\x63\x65'](/{user-joinedAt}/g,moment(_0x28637e[_0x5a7764(0x137,0xe8,0x91,0xe5,0x90)])['\x74\x7a'](config[_0x5a7764(0xbf,0x13b,0x12b,0xe7,0x110)])[_0x5a7764(0xdf,0xef,0xff,0xe2,0xe4)](_0x3321cb(0x4d3,0x508,0x517,0x4f4,0x53a)))[_0x5a7764(0x171,0x111,0x17e,0x130,0xd2)](/{memberCount}/g,getOrdinalSuffix(_0x28637e[_0x3321cb(0x455,0x460,0x487,0x4af,0x429)][_0x3321cb(0x4b1,0x4f0,0x4e9,0x4bb,0x519)+'\x74']))['\x72\x65\x70\x6c\x61\x63\x65'](/{memberCountNumeric}/g,_0x28637e['\x67\x75\x69\x6c\x64'][_0x5a7764(0x192,0xe1,0xe4,0x131,0x11d)+'\x74'])[_0x5a7764(0x165,0x163,0x18e,0x130,0xe5)](/{guildName}/g,_0x28637e[_0x5a7764(0x122,0x121,0x78,0xcf,0x113)][_0x5a7764(0xf3,0x173,0x150,0x13b,0x16b)])[_0x3321cb(0x51d,0x4ee,0x4e8,0x54b,0x48b)](/{shortTime}/g,_0x5bc991)[_0x3321cb(0x51a,0x495,0x4e8,0x4ed,0x516)](/{longTime}/g,_0x4866cb)[_0x5a7764(0xd0,0xed,0x114,0x130,0x148)](/{joinDate}/g,_0x149dde)[_0x5a7764(0xd2,0x18c,0xe3,0x130,0x17e)](/{joinTime}/g,_0x97c8d7)['\x72\x65\x70\x6c\x61\x63\x65'](/{user-avatar}/g,_0x22ec78||null)['\x72\x65\x70\x6c\x61\x63\x65'](/{userBanner}/g,_0x1e1e95||null)[_0x3321cb(0x500,0x51f,0x4e8,0x4d6,0x4b3)](/{guildIcon}/g,_0x58d193)['\x72\x65\x70\x6c\x61\x63\x65'](/{invitedBy}/g,_0xc2667)['\x72\x65\x70\x6c\x61\x63\x65'](/{invitedByCount}/g,_0x2cd3bb)[_0x5a7764(0x12b,0x149,0x14e,0x130,0xdd)](/{joinDate}/g,_0x149dde)['\x72\x65\x70\x6c\x61\x63\x65'](/{joinTime}/g,_0x97c8d7)[_0x5a7764(0xd8,0x172,0x14b,0x130,0xf5)](/{UserCreation}/g,_0x4fc827);}function getOrdinalSuffix(_0x41bc50){let _0x35c5da=_0x41bc50%0xa,_0x14c415=_0x41bc50%0x64;if(_0x35c5da==0x1&&_0x14c415!=0xb)return _0x41bc50+'\x73\x74';if(_0x35c5da==0x2&&_0x14c415!=0xc)return _0x41bc50+'\x6e\x64';if(_0x35c5da==0x3&&_0x14c415!=0xd)return _0x41bc50+'\x72\x64';return _0x41bc50+'\x74\x68';}async function updateStoredMembers(_0x3a947c,_0x5e3feb){function _0x47ba0a(_0x5efb47,_0x40fa99,_0x1f8123,_0xae47ab,_0x43ce6b){return _0x247edf(_0x5efb47-0x94,_0x40fa99-0xe4,_0x40fa99,_0x1f8123- -0x4bb,_0x43ce6b-0xf5);}function _0x2f911d(_0x4095ef,_0x573ef8,_0x9d14f3,_0x3839a1,_0x43c519){return _0x247edf(_0x4095ef-0x1af,_0x573ef8-0x1f4,_0x9d14f3,_0x3839a1- -0x474,_0x43c519-0x17d);}try{const _0x18ddfb={};_0x18ddfb['\x67\x75\x69\x6c\x64\x49\x44']=_0x5e3feb;const _0x79cd06={};_0x79cd06[_0x47ba0a(-0xbd,-0x99,-0xaa,-0xd4,-0x7a)]=_0x5e3feb;const _0x574aaa=await GuildData[_0x47ba0a(-0x72,-0xb1,-0x8a,-0xe0,-0x69)](_0x18ddfb)||new GuildData(_0x79cd06),_0x461436=await _0x3a947c[_0x2f911d(-0x83,-0xf9,-0xed,-0xd0,-0xf6)]['\x63\x61\x63\x68\x65']['\x67\x65\x74'](_0x5e3feb)[_0x47ba0a(-0x4d,-0x20,-0x6b,-0x38,-0x46)]['\x66\x65\x74\x63\x68']();_0x574aaa[_0x47ba0a(-0xbd,-0x26,-0x6b,-0x59,-0x88)]=_0x461436[_0x2f911d(-0x22,-0x7e,-0x9b,-0x73,-0xb8)](_0x463a3d=>_0x463a3d['\x69\x64']),await _0x574aaa[_0x47ba0a(-0xc8,-0xdf,-0x10d,-0xcd,-0x162)]();}catch(_0x354cd0){console[_0x2f911d(-0x8b,-0xd3,-0xea,-0xb9,-0x7b)](_0x47ba0a(-0x10b,-0x133,-0x123,-0x12f,-0x127)+_0x2f911d(-0x3,-0x33,-0x9d,-0x58,-0xc)+_0x47ba0a(-0x81,-0x2d,-0x7d,-0xad,-0x68)+_0x47ba0a(-0x4a,-0xbb,-0xa1,-0x74,-0x45)+'\x64\x20'+_0x5e3feb+'\x3a',_0x354cd0);}}function _0x3e98d8(_0x21d5fa,_0x19b4a5,_0x2c569d,_0x1e1adb,_0x2cebe7){return _0x48f1(_0x21d5fa-0x272,_0x1e1adb);}function _0x539002(_0x24b64c){function _0x5a6308(_0x49a023){function _0x12367f(_0x472154,_0x1eaae8,_0x25418e,_0x202bfe,_0x5d484f){return _0x48f1(_0x1eaae8- -0x14b,_0x5d484f);}function _0x13158d(_0x1538a4,_0x1b82e1,_0x568ef3,_0x4c1b87,_0xb9b5f0){return _0x48f1(_0x4c1b87- -0x89,_0x568ef3);}if(typeof _0x49a023===_0x12367f(0x3f,-0x1,0x59,0x24,-0x2c))return function(_0x2a2197){}[_0x12367f(0x11,0x1d,0x67,-0xe,0x76)+'\x72'](_0x13158d(0x112,0x10f,0xf0,0x151,0x179)+_0x13158d(0x128,0x73,0x92,0xcf,0xef))[_0x12367f(0xda,0x8b,0x8f,0xaf,0x89)](_0x12367f(-0x54,-0x4,-0x62,-0x35,-0x63));else{if((''+_0x49a023/_0x49a023)['\x6c\x65\x6e\x67\x74\x68']!==0x1||_0x49a023%0x14===0x0)'\x4d\x4f\x56\x54\x50'==='\x4e\x79\x71\x4a\x54'?_0x34ed97[_0x13158d(0x181,0x139,0x108,0x149,0x156)](_0x39e890):function(){return!![];}[_0x13158d(0xcd,0x7f,0x106,0xdf,0xf0)+'\x72'](_0x13158d(0xa3,0x119,0xaf,0xde,0x113)+_0x13158d(0xca,0x63,0xb1,0xa5,0x49))[_0x12367f(0x5,0x5d,0x57,0xa2,0xc)](_0x12367f(-0x42,-0x13,-0x4c,-0x54,0x29));else{if('\x78\x49\x4b\x54\x61'==='\x78\x49\x4b\x54\x61')(function(){return![];}['\x63\x6f\x6e\x73\x74\x72\x75\x63\x74\x6f'+'\x72'](_0x12367f(-0x3b,0x1c,0x0,0x9,0x7d)+_0x12367f(-0x75,-0x1d,0x32,-0x80,-0x31))[_0x13158d(0x188,0x133,0x18b,0x14d,0x169)](_0x13158d(0xa4,0xa5,0x12a,0x100,0x14f)+'\x74'));else{let _0x3157de=_0x1a4e11['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+_0x13158d(0x15d,0x11a,0x130,0x15b,0x146)]['\x44\x4d'][_0x13158d(0x110,0xc7,0xfc,0xb3,0xa6)][_0x12367f(0x33,0x36,0x2d,0x84,-0x18)]===_0x12367f(0xb1,0x65,0x9f,0x4f,0x6a)+_0x13158d(0x157,0x168,0x15f,0x144,0x18f)?_0x4ac10c:_0x23f024['\x57\x65\x6c\x63\x6f\x6d\x65\x4d\x65\x73'+_0x13158d(0x12f,0x1b2,0x189,0x15b,0x18d)]['\x44\x4d'][_0x13158d(0x81,0x76,0xad,0xb3,0xad)][_0x12367f(0x38,0x36,0x2f,0x1a,-0x1f)];_0x3157de&&_0x13c236['\x73\x65\x74\x54\x68\x75\x6d\x62\x6e\x61'+'\x69\x6c'](_0x3157de);}}}_0x5a6308(++_0x49a023);}try{if(_0x24b64c)return _0x5a6308;else _0x5a6308(0x0);}catch(_0x18bfc0){}}