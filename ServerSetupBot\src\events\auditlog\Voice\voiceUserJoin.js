const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.VoiceStateUpdate,
    async execute(oldState, newState, client) {
        try {
            // Only handle user joining voice channel (was not in voice, now is)
            if (oldState.channelId || !newState.channelId) return;
            
            console.log(`🔊 User joined voice: ${newState.member.user.tag} in ${newState.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newState.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'VOICE_USER_JOIN')) return;
            
            // Create event data
            const eventData = {
                eventType: 'VOICE_USER_JOIN',
                user: newState.member.user.tag,
                userId: newState.member.user.id,
                action: 'Tham gia kênh thoại',
                details: `**${newState.member.user.tag}** đã tham gia kênh thoại **${newState.channel.name}**`,
                target: newState.member.user.tag,
                channel: newState.channel.name,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(newState.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔊 Tham gia kênh thoại',
                `Vừa có thành viên tham gia kênh thoại`
            );
            
            embed.setColor(0x2ecc71); // Green for join
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${newState.member.user.tag} (${newState.member.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newState.member.user.id}`,
                    inline: true
                },
                {
                    name: '> Kênh thoại',
                    value: `- ${newState.channel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newState.channel.id}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Số người trong kênh',
                    value: `- ${newState.channel.members.size} thành viên`,
                    inline: true
                }
            ]);
            
            // Add voice state info
            const voiceStates = [];
            if (newState.mute) voiceStates.push('🔇 Muted');
            if (newState.deaf) voiceStates.push('🔇 Deafened');
            if (newState.selfMute) voiceStates.push('🔇 Self Muted');
            if (newState.selfDeaf) voiceStates.push('🔇 Self Deafened');
            if (newState.streaming) voiceStates.push('📺 Streaming');
            if (newState.selfVideo) voiceStates.push('📹 Camera On');
            
            if (voiceStates.length > 0) {
                embed.addFields({
                    name: '> Trạng thái',
                    value: `- ${voiceStates.join(', ')}`,
                    inline: false
                });
            }
            
            // Set user avatar as thumbnail
            if (newState.member.user.displayAvatarURL()) {
                embed.setThumbnail(newState.member.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newState.guild.name} • Voice User Join`,
                iconURL: newState.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Voice user join logged: ${newState.member.user.tag}`);
            
        } catch (error) {
            console.error('Error in voiceUserJoin audit log:', error);
        }
    }
};
