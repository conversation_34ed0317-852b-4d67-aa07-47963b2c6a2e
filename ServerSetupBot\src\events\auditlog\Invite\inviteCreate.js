const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.InviteCreate,
    async execute(invite, client) {
        try {
            console.log(`📨 Invite created: ${invite.code} in ${invite.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(invite.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'INVITE_CREATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'INVITE_CREATE',
                user: invite.inviter?.tag || 'System',
                userId: invite.inviter?.id || null,
                action: 'Lời mời được tạo',
                details: `Lời mời **${invite.code}** đã được tạo cho kênh **${invite.channel.name}**`,
                target: invite.code,
                channel: invite.channel.name,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(invite.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📨 Lời mời được tạo',
                `Vừa có một lời mời mới được tạo`
            );
            
            embed.setColor(0x2ecc71); // Green for create
            
            embed.addFields([
                {
                    name: '> Mã lời mời',
                    value: `- ${invite.code}`,
                    inline: true
                },
                {
                    name: '> Link lời mời',
                    value: `- [discord.gg/${invite.code}](${invite.url})`,
                    inline: true
                },
                {
                    name: '> Kênh mục tiêu',
                    value: `- ${invite.channel}`,
                    inline: true
                },
                {
                    name: '> Người tạo',
                    value: `- ${invite.inviter ? `${invite.inviter.tag} (${invite.inviter})` : 'System'}`,
                    inline: false
                },
                {
                    name: '> Số lần sử dụng tối đa',
                    value: `- ${invite.maxUses === 0 ? 'Không giới hạn' : invite.maxUses}`,
                    inline: true
                },
                {
                    name: '> Thời gian hết hạn',
                    value: `- ${invite.maxAge === 0 ? 'Không bao giờ' : `${invite.maxAge / 3600} giờ`}`,
                    inline: true
                },
                {
                    name: '> Tạm thời',
                    value: `- ${invite.temporary ? 'Có' : 'Không'}`,
                    inline: true
                },
                {
                    name: '> Thời gian tạo',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add expiration time if set
            if (invite.expiresTimestamp) {
                embed.addFields({
                    name: '> Hết hạn lúc',
                    value: `- <t:${Math.floor(invite.expiresTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Set inviter avatar as thumbnail
            if (invite.inviter?.displayAvatarURL()) {
                embed.setThumbnail(invite.inviter.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${invite.guild.name} • Invite Create`,
                iconURL: invite.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Invite create logged: ${invite.code}`);
            
        } catch (error) {
            console.error('Error in inviteCreate audit log:', error);
        }
    }
};
