/**
 * Kiểm tra xem chuỗi có phải là ID Discord hợp lệ không
 */
function isValidDiscordId(id) {
    return /^\d{17,19}$/.test(id);
}

/**
 * Kiểm tra xem emoji có hợp lệ không (Unicode hoặc custom)
 */
function isValidEmoji(emoji) {
    // Unicode emoji regex
    const unicodeEmojiRegex = /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]$/u;
    
    // Custom emoji regex <:name:id> hoặc <a:name:id>
    const customEmojiRegex = /^<a?:\w+:\d{17,19}>$/;
    
    return unicodeEmojiRegex.test(emoji) || customEmojiRegex.test(emoji);
}

/**
 * Kiểm tra xem URL có hợp lệ không
 */
function isValidUrl(url) {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * Kiểm tra xem hex color có hợp lệ không
 */
function isValidHexColor(color) {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
}

/**
 * Kiểm tra độ dài tin nhắn
 */
function isValidMessageLength(message, maxLength = 2000) {
    return message.length <= maxLength && message.length > 0;
}

/**
 * Kiểm tra tên channel hợp lệ
 */
function isValidChannelName(name) {
    // Discord channel names: 1-100 characters, lowercase, no spaces, limited special chars
    return /^[a-z0-9\-_]{1,100}$/.test(name);
}

/**
 * Kiểm tra tên role hợp lệ
 */
function isValidRoleName(name) {
    // Discord role names: 1-100 characters
    return name.length >= 1 && name.length <= 100;
}

/**
 * Kiểm tra template name hợp lệ
 */
function isValidTemplateName(name) {
    // Template names: 1-50 characters, alphanumeric và một số ký tự đặc biệt
    return /^[a-zA-Z0-9\s\-_]{1,50}$/.test(name);
}

/**
 * Kiểm tra delay time hợp lệ (giây)
 */
function isValidDelayTime(seconds) {
    return Number.isInteger(seconds) && seconds >= 0 && seconds <= 3600; // Max 1 hour
}

/**
 * Kiểm tra số lượng channels hợp lệ
 */
function isValidChannelCount(count) {
    return Number.isInteger(count) && count >= 1 && count <= 50; // Max 50 channels at once
}

/**
 * Kiểm tra welcome/goodbye message có placeholder hợp lệ không
 */
function validateWelcomeMessage(message) {
    const validPlaceholders = ['{user}', '{username}', '{server}', '{membercount}'];
    const errors = [];
    
    // Kiểm tra độ dài
    if (!isValidMessageLength(message, 1500)) {
        errors.push('Tin nhắn phải có độ dài từ 1-1500 ký tự');
    }
    
    // Kiểm tra placeholder không hợp lệ
    const placeholderRegex = /{[^}]+}/g;
    const foundPlaceholders = message.match(placeholderRegex) || [];
    
    for (const placeholder of foundPlaceholders) {
        if (!validPlaceholders.includes(placeholder)) {
            errors.push(`Placeholder không hợp lệ: ${placeholder}`);
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors: errors,
        validPlaceholders: validPlaceholders
    };
}

/**
 * Kiểm tra channel permission overwrites
 */
function validateChannelPermissions(permissions) {
    const validPermissions = [
        'ViewChannel', 'SendMessages', 'EmbedLinks', 'AttachFiles', 
        'AddReactions', 'UseExternalEmojis', 'ReadMessageHistory',
        'Connect', 'Speak', 'Stream', 'UseVAD'
    ];
    
    const errors = [];
    
    for (const [roleId, perms] of Object.entries(permissions)) {
        if (!isValidDiscordId(roleId)) {
            errors.push(`ID vai trò không hợp lệ: ${roleId}`);
            continue;
        }
        
        for (const perm of Object.keys(perms)) {
            if (!validPermissions.includes(perm)) {
                errors.push(`Quyền không hợp lệ: ${perm}`);
            }
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors: errors
    };
}

/**
 * Sanitize user input để tránh injection
 */
function sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    
    return input
        .replace(/[<>]/g, '') // Remove potential HTML tags
        .replace(/[@#]/g, '') // Remove @ and # to prevent mentions
        .trim();
}

/**
 * Validate backup name
 */
function isValidBackupName(name) {
    // Backup names: 1-50 characters, safe filename characters
    return /^[a-zA-Z0-9\s\-_()]{1,50}$/.test(name);
}

/**
 * Validate JSON string
 */
function isValidJSON(jsonString) {
    try {
        JSON.parse(jsonString);
        return true;
    } catch {
        return false;
    }
}

/**
 * Validate server template data
 */
function validateTemplateData(templateData) {
    const errors = [];
    
    try {
        const data = typeof templateData === 'string' ? JSON.parse(templateData) : templateData;
        
        // Kiểm tra cấu trúc cơ bản
        if (!data.name || !isValidTemplateName(data.name)) {
            errors.push('Tên template không hợp lệ');
        }
        
        if (!data.description || data.description.length > 200) {
            errors.push('Mô tả template không hợp lệ (1-200 ký tự)');
        }
        
        // Kiểm tra channels nếu có
        if (data.channels && Array.isArray(data.channels)) {
            for (const channel of data.channels) {
                if (!channel.name || !isValidChannelName(channel.name)) {
                    errors.push(`Tên kênh không hợp lệ: ${channel.name}`);
                }
                
                if (channel.type && !['text', 'voice', 'category'].includes(channel.type)) {
                    errors.push(`Loại kênh không hợp lệ: ${channel.type}`);
                }
            }
        }
        
        // Kiểm tra roles nếu có
        if (data.roles && Array.isArray(data.roles)) {
            for (const role of data.roles) {
                if (!role.name || !isValidRoleName(role.name)) {
                    errors.push(`Tên vai trò không hợp lệ: ${role.name}`);
                }
                
                if (role.color && !isValidHexColor(role.color)) {
                    errors.push(`Màu vai trò không hợp lệ: ${role.color}`);
                }
            }
        }
        
    } catch (error) {
        errors.push('Dữ liệu template không phải JSON hợp lệ');
    }
    
    return {
        isValid: errors.length === 0,
        errors: errors
    };
}

module.exports = {
    isValidDiscordId,
    isValidEmoji,
    isValidUrl,
    isValidHexColor,
    isValidMessageLength,
    isValidChannelName,
    isValidRoleName,
    isValidTemplateName,
    isValidDelayTime,
    isValidChannelCount,
    validateWelcomeMessage,
    validateChannelPermissions,
    sanitizeInput,
    isValidBackupName,
    isValidJSON,
    validateTemplateData
};
