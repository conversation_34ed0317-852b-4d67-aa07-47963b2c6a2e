const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.MessageUpdate,
    async execute(oldMessage, newMessage, client) {
        try {
            // Only handle moderation case updates (this would be custom implementation)
            // This is a placeholder for when you implement a case system
            if (!newMessage.guild) return;
            
            // Check if this is a moderation case message (you'd implement this logic)
            const isModerationCase = await client.db.isModerationCaseMessage(newMessage.id);
            if (!isModerationCase) return;
            
            console.log(`✏️ Moderation case updated in ${newMessage.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newMessage.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CASE_UPDATE')) return;
            
            // Get case info from database
            const caseInfo = await client.db.getModerationCase(newMessage.id);
            
            // Create event data
            const eventData = {
                eventType: 'CASE_UPDATE',
                user: newMessage.author?.tag || 'System',
                userId: newMessage.author?.id || null,
                action: 'Case moderation được cập nhật',
                details: `Case moderation đã được cập nhật`,
                target: caseInfo?.caseId || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(newMessage.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Case moderation được cập nhật',
                `Vừa có case moderation được chỉnh sửa`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Case ID',
                    value: `- ${caseInfo?.caseId || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người cập nhật',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian cập nhật',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add case info if available
            if (caseInfo) {
                embed.addFields([
                    {
                        name: '> Loại case',
                        value: `- ${caseInfo.type || 'Unknown'}`,
                        inline: true
                    },
                    {
                        name: '> Đối tượng',
                        value: `- ${caseInfo.targetUser || 'Unknown'}`,
                        inline: true
                    },
                    {
                        name: '> Moderator gốc',
                        value: `- ${caseInfo.moderator || 'Unknown'}`,
                        inline: true
                    }
                ]);
                
                if (caseInfo.createdAt) {
                    embed.addFields({
                        name: '> Thời gian tạo case',
                        value: `- <t:${Math.floor(new Date(caseInfo.createdAt).getTime() / 1000)}:F>`,
                        inline: true
                    });
                }
            }
            
            // Compare old and new content if available
            if (oldMessage.content !== newMessage.content) {
                const oldContent = oldMessage.content.length > 500 
                    ? oldMessage.content.substring(0, 500) + '...'
                    : oldMessage.content;
                const newContent = newMessage.content.length > 500 
                    ? newMessage.content.substring(0, 500) + '...'
                    : newMessage.content;
                
                embed.addFields([
                    {
                        name: '> Nội dung cũ',
                        value: `\`\`\`${oldContent}\`\`\``,
                        inline: false
                    },
                    {
                        name: '> Nội dung mới',
                        value: `\`\`\`${newContent}\`\`\``,
                        inline: false
                    }
                ]);
            }
            
            // Add update impact
            embed.addFields({
                name: '> 📝 Tác động của việc cập nhật case',
                value: [
                    '• Thông tin case được cập nhật',
                    '• Lịch sử moderation được ghi nhận',
                    '• Có thể thay đổi context của case',
                    '• Cần thông báo cho team nếu cần'
                ].join('\n'),
                inline: false
            });
            
            // Add guidelines
            embed.addFields({
                name: '> 💡 Lưu ý khi cập nhật case',
                value: [
                    '• Ghi rõ lý do cập nhật',
                    '• Không thay đổi thông tin quan trọng',
                    '• Giữ nguyên tính chính xác',
                    '• Thông báo cho moderators liên quan'
                ].join('\n'),
                inline: false
            });
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newMessage.guild.name} • Moderation Case Update`,
                iconURL: newMessage.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Case update logged`);
            
        } catch (error) {
            console.error('Error in caseUpdate audit log:', error);
        }
    }
};
