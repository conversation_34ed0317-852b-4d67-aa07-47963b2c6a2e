const { Permissions<PERSON><PERSON><PERSON><PERSON>, Embed<PERSON>uild<PERSON>, ChannelType } = require('discord.js');

module.exports = {
    name: "maintenance",
    description: "Enable or disable maintenance mode",
    aliases: ["mt"],
    async execute(message, args) {
        if (!message.member.permissions.has(PermissionsBitField.Flags.Administrator)) {
            return message.reply({
                embeds: [new EmbedBuilder().setColor("Red").setDescription("❌ You need `Administrator` permission to use this command.")]
            });
        }

        const subCommand = args[0];
        if (!subCommand || (subCommand !== "on" && subCommand !== "off")) {
            return message.reply({
                embeds: [new EmbedBuilder().setColor("Red").setDescription("❌ Usage: `!maintenance on` or `!maintenance off`")]
            });
        }

        const guild = message.guild;
        const everyoneRole = guild.roles.everyone;

        if (subCommand === "on") {
            let category = guild.channels.cache.find(c => c.name === "🔧 Maintenance Mode" && c.type === ChannelType.GuildCategory);
            if (!category) {
                category = await guild.channels.create({
                    name: "🔧 Maintenance Mode",
                    type: ChannelType.GuildCategory,
                }).catch(console.error);
            }

            guild.channels.cache.forEach(channel => {
                if (channel && channel.permissionsFor(everyoneRole)?.has(PermissionsBitField.Flags.ViewChannel)) {
                    channel.permissionOverwrites.edit(everyoneRole, { ViewChannel: false }).catch(console.error);
                }
            });

            const maintenanceChannels = ["📢 maintenance-announcement", "💬 maintenance-chat", "🛠 maintenance-cmds"];
            for (const name of maintenanceChannels) {
                if (!guild.channels.cache.find(c => c.name === name)) {
                    await guild.channels.create({
                        name,
                        type: ChannelType.GuildText,
                        parent: category,
                        permissionOverwrites: [
                            { id: everyoneRole.id, allow: [PermissionsBitField.Flags.ViewChannel, PermissionsBitField.Flags.SendMessages] }
                        ]
                    }).catch(console.error);
                }
            }

            return message.channel.send({
                embeds: [new EmbedBuilder().setColor("Green").setDescription("✅ **Maintenance Mode Enabled!**\nAll channels are hidden, and maintenance channels have been created.")]
            });
        }

        if (subCommand === "off") {
            guild.channels.cache.forEach(channel => {
                if (channel && channel.permissionsFor(everyoneRole)?.has(PermissionsBitField.Flags.ViewChannel) === false) {
                    channel.permissionOverwrites.edit(everyoneRole, { ViewChannel: true }).catch(console.error);
                }
            });

            const maintenanceCategory = guild.channels.cache.find(c => c.name === "🔧 Maintenance Mode" && c.type === ChannelType.GuildCategory);
            if (maintenanceCategory) {
                maintenanceCategory.children.cache.forEach(child => {
                    if (child) {
                        child.setParent(null).catch(console.error);
                    }
                });
                await maintenanceCategory.delete().catch(console.error);
            }

            return message.channel.send({
                embeds: [new EmbedBuilder().setColor("Blue").setDescription("✅ **Maintenance Mode Disabled!**\nAll channels are restored, and the maintenance category has been deleted.")]
            });
        }
    }
};