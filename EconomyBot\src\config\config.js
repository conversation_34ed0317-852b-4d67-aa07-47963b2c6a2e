require('dotenv').config();

module.exports = {
    // Discord Configuration
    discord: {
        token: process.env.DISCORD_TOKEN,
        clientId: process.env.CLIENT_ID,
        prefix: process.env.PREFIX || '!'
    },

    // Database Configuration
    database: {
        path: process.env.DATABASE_PATH || './data/economy.db'
    },

    // Economy Configuration
    economy: {
        currency: {
            name: process.env.CURRENCY_NAME || 'Xu',
            symbol: process.env.CURRENCY_SYMBOL || '💰',
            default: process.env.DEFAULT_CURRENCY || '💰'
        },
        startingBalance: parseInt(process.env.STARTING_BALANCE) || 1000,
        dailyReward: {
            min: parseInt(process.env.DAILY_REWARD_MIN) || 100,
            max: parseInt(process.env.DAILY_REWARD_MAX) || 500
        },
        shop: {
            taxRate: parseFloat(process.env.SHOP_TAX_RATE) || 0.05
        }
    },

    // Bot Configuration
    bot: {
        timezone: process.env.TIMEZONE || 'Asia/Ho_Chi_Minh'
    },

    // Embed Colors
    colors: {
        success: 0x00ff00,
        error: 0xff0000,
        warning: 0xffff00,
        info: 0x0099ff,
        primary: 0x7289da,
        economy: 0xffd700
    },

    // Emojis
    emojis: {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️',
        money: '💰',
        coin: '🪙',
        gem: '💎',
        gift: '🎁',
        shop: '🛒',
        bank: '🏦',
        trophy: '🏆',
        star: '⭐',
        crown: '👑'
    }
};
