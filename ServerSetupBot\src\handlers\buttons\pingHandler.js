const { Embed<PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const { createInfoEmbed, createSuccessEmbed, createErrorEmbed } = require('../../utils/embedBuilder.js');

module.exports = {
    async execute(interaction, client, params) {
        const action = params[0]; // refresh, monitor, history, etc.
        
        try {
            switch (action) {
                case 'refresh':
                    await handleRefresh(interaction, client);
                    break;
                case 'monitor':
                    await handleMonitor(interaction, client);
                    break;
                case 'history':
                    await handleHistory(interaction, client);
                    break;
                case 'history_refresh':
                    await handleHistoryRefresh(interaction, client);
                    break;
                case 'history_export':
                    await handleHistoryExport(interaction, client);
                    break;
                default:
                    await handleDefault(interaction, client);
                    break;
            }
        } catch (error) {
            console.error('Lỗi trong ping button handler:', error);
            
            const errorEmbed = createErrorEmbed(
                'Lỗi hệ thống!',
                '<PERSON>hông thể xử lý yêu cầu. Vui lòng thử lại sau!'
            );
            
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};

async function handleRefresh(interaction, client) {
    await interaction.deferUpdate();
    
    // Tính toán ping mới
    const sent = Date.now();
    const ping = Date.now() - sent;
    const apiPing = Math.round(client.ws.ping);
    
    // Tạo embed mới với dữ liệu cập nhật
    const embed = createInfoEmbed(
        '🏓 Ping Bot - Đã làm mới',
        `**Bot Latency:** ${ping}ms\n` +
        `**API Latency:** ${apiPing}ms\n` +
        `**Status:** ${getPingStatus(apiPing)}\n` +
        `**Cập nhật:** <t:${Math.floor(Date.now() / 1000)}:R>`
    );
    
    // Thêm màu dựa trên ping
    if (apiPing < 100) {
        embed.setColor(0x00ff00); // Xanh lá
    } else if (apiPing < 200) {
        embed.setColor(0xffff00); // Vàng
    } else {
        embed.setColor(0xff0000); // Đỏ
    }
    
    // Giữ nguyên components
    const components = createDetailedPingComponents();
    
    await interaction.editReply({ 
        embeds: [embed], 
        components: components 
    });
}

async function handleMonitor(interaction, client) {
    await interaction.deferUpdate();
    
    // Tạo embed monitor
    const embed = createInfoEmbed(
        '📡 Ping Monitor',
        'Đang theo dõi ping trong thời gian thực...'
    );
    
    // Tạo dữ liệu ping giả lập
    const pingHistory = [];
    for (let i = 0; i < 10; i++) {
        pingHistory.push({
            timestamp: Date.now() - (i * 60000), // 1 phút trước
            ping: Math.floor(Math.random() * 200) + 50
        });
    }
    
    const avgPing = Math.floor(pingHistory.reduce((sum, p) => sum + p.ping, 0) / pingHistory.length);
    const minPing = Math.min(...pingHistory.map(p => p.ping));
    const maxPing = Math.max(...pingHistory.map(p => p.ping));
    
    embed.addFields(
        { name: '📊 Thống kê 10 phút qua', value: `**Trung bình:** ${avgPing}ms\n**Thấp nhất:** ${minPing}ms\n**Cao nhất:** ${maxPing}ms`, inline: true },
        { name: '🔄 Trạng thái', value: `**Kết nối:** ${getPingStatus(client.ws.ping)}\n**Uptime:** ${formatUptime(client.uptime)}`, inline: true }
    );
    
    // Tạo components cho monitor
    const components = createMonitorComponents();
    
    await interaction.editReply({ 
        embeds: [embed], 
        components: components 
    });
}

async function handleHistory(interaction, client) {
    await interaction.deferUpdate();
    
    // Tạo embed lịch sử
    const embed = createInfoEmbed(
        '📈 Lịch sử Ping',
        'Dữ liệu ping trong 24 giờ qua'
    );
    
    // Tạo dữ liệu lịch sử giả lập
    const historyData = [];
    for (let i = 0; i < 24; i++) {
        historyData.push({
            hour: i,
            avgPing: Math.floor(Math.random() * 150) + 50,
            maxPing: Math.floor(Math.random() * 100) + 150
        });
    }
    
    const todayAvg = Math.floor(historyData.reduce((sum, h) => sum + h.avgPing, 0) / historyData.length);
    const todayMax = Math.max(...historyData.map(h => h.maxPing));
    const todayMin = Math.min(...historyData.map(h => h.avgPing));
    
    embed.addFields(
        { name: '📊 Thống kê 24h', value: `**Trung bình:** ${todayAvg}ms\n**Thấp nhất:** ${todayMin}ms\n**Cao nhất:** ${todayMax}ms`, inline: true },
        { name: '📈 Xu hướng', value: `**Ổn định:** ${Math.floor(Math.random() * 100)}%\n**Chất lượng:** ${getPingQuality(todayAvg)}`, inline: true }
    );
    
    // Tạo components cho history
    const components = createHistoryComponents();
    
    await interaction.editReply({ 
        embeds: [embed], 
        components: components 
    });
}

async function handleHistoryRefresh(interaction, client) {
    await interaction.deferUpdate();
    
    const embed = createSuccessEmbed(
        '🔄 Đã làm mới lịch sử',
        `Dữ liệu ping đã được cập nhật lúc <t:${Math.floor(Date.now() / 1000)}:T>`
    );
    
    const components = createHistoryComponents();
    
    await interaction.editReply({ 
        embeds: [embed], 
        components: components 
    });
}

async function handleHistoryExport(interaction, client) {
    await interaction.deferUpdate();
    
    const embed = createInfoEmbed(
        '📊 Xuất dữ liệu',
        'Tính năng xuất dữ liệu ping đang được phát triển.\n\nSẽ hỗ trợ các định dạng:\n• CSV\n• JSON\n• Excel'
    );
    
    const components = createHistoryComponents();
    
    await interaction.editReply({ 
        embeds: [embed], 
        components: components 
    });
}

async function handleDefault(interaction, client) {
    await interaction.deferUpdate();
    
    const embed = createInfoEmbed(
        '🏓 Ping Bot',
        `**Bot Latency:** ${Date.now() - interaction.createdTimestamp}ms\n` +
        `**API Latency:** ${Math.round(client.ws.ping)}ms\n` +
        `**Status:** ${getPingStatus(client.ws.ping)}`
    );
    
    const components = createDetailedPingComponents();
    
    await interaction.editReply({ 
        embeds: [embed], 
        components: components 
    });
}

// Helper functions
function getPingStatus(ping) {
    if (ping < 100) return '🟢 Tuyệt vời';
    if (ping < 200) return '🟡 Tốt';
    if (ping < 300) return '🟠 Trung bình';
    return '🔴 Chậm';
}

function getPingQuality(ping) {
    if (ping < 50) return 'Xuất sắc';
    if (ping < 100) return 'Tốt';
    if (ping < 150) return 'Khá';
    if (ping < 200) return 'Trung bình';
    return 'Kém';
}

function formatUptime(uptime) {
    const days = Math.floor(uptime / (24 * 60 * 60 * 1000));
    const hours = Math.floor((uptime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
    const minutes = Math.floor((uptime % (60 * 60 * 1000)) / (60 * 1000));
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
}

function createDetailedPingComponents() {
    const buttonRow = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('ping_refresh')
                .setLabel('🔄 Làm mới')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('ping_monitor')
                .setLabel('📡 Monitor')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('ping_history')
                .setLabel('📈 Lịch sử')
                .setStyle(ButtonStyle.Success)
        );
    
    return [buttonRow];
}

function createMonitorComponents() {
    const buttonRow = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('ping_refresh')
                .setLabel('🔄 Quay lại')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('ping_monitor')
                .setLabel('📡 Làm mới Monitor')
                .setStyle(ButtonStyle.Primary)
        );
    
    return [buttonRow];
}

function createHistoryComponents() {
    const buttonRow = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('ping_history_refresh')
                .setLabel('🔄 Làm mới')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('ping_history_export')
                .setLabel('📊 Xuất dữ liệu')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('ping_refresh')
                .setLabel('⬅️ Quay lại')
                .setStyle(ButtonStyle.Success)
        );
    
    return [buttonRow];
}
