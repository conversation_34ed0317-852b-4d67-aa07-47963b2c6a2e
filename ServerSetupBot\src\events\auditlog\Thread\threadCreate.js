const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ThreadCreate,
    async execute(thread, newlyCreated, client) {
        try {
            // Only log newly created threads
            if (!newlyCreated) return;
            
            console.log(`🧵 Thread created: ${thread.name} in ${thread.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(thread.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'THREAD_CREATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'THREAD_CREATE',
                user: 'System',
                userId: null,
                action: 'Thread được tạo',
                details: `Thread **${thread.name}** đã được tạo trong ${thread.parent}`,
                target: thread.name,
                channel: thread.parent?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get thread owner
            if (thread.ownerId) {
                try {
                    const owner = await thread.guild.members.fetch(thread.ownerId);
                    eventData.user = owner.user.tag;
                    eventData.userId = owner.user.id;
                    eventData.action = `Thread được tạo bởi ${owner.user.tag}`;
                } catch (error) {
                    console.log('Could not fetch thread owner');
                }
            }
            
            // Add to database
            await client.db.addAuditLog(thread.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🧵 Thread được tạo',
                `Vừa có một thread mới được tạo trong server`
            );
            
            embed.setColor(0x2ecc71); // Green for create
            
            // Get thread type names
            const threadTypes = {
                10: 'Announcement Thread',
                11: 'Public Thread',
                12: 'Private Thread'
            };
            
            embed.addFields([
                {
                    name: '> Tên thread',
                    value: `- ${thread.name}`,
                    inline: true
                },
                {
                    name: '> ID thread',
                    value: `- ${thread.id}`,
                    inline: true
                },
                {
                    name: '> Loại thread',
                    value: `- ${threadTypes[thread.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh cha',
                    value: `- ${thread.parent || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người tạo',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian tạo',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add thread settings
            embed.addFields([
                {
                    name: '> Auto archive',
                    value: `- ${thread.autoArchiveDuration} phút`,
                    inline: true
                },
                {
                    name: '> Slow mode',
                    value: `- ${thread.rateLimitPerUser || 0} giây`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${thread.archived ? '📁 Archived' : '🔓 Active'}`,
                    inline: true
                }
            ]);
            
            // Add member count
            embed.addFields({
                name: '> Số thành viên',
                value: `- ${thread.memberCount || 1} thành viên`,
                inline: true
            });
            
            // Add thread link
            embed.addFields({
                name: '> Link thread',
                value: `- [Nhấn để xem](https://discord.com/channels/${thread.guild.id}/${thread.id})`,
                inline: true
            });
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${thread.guild.name} • Thread Create`,
                iconURL: thread.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Thread create logged: ${thread.name}`);
            
        } catch (error) {
            console.error('Error in threadCreate audit log:', error);
        }
    }
};
