<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>No Access</title>
    <style>
        @import url('https://fonts.googleapis.com/css?family=Fira+Mono:400');

        body{ 
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100vw;
            height: 100vh;
            margin: 0;
            background: #131313;
            color: #fff;
            font-family: 'Fira Mono', monospace;
            overflow: hidden;
        }

        .glitch{
            font-size: 96px;
            letter-spacing: -7px;
            position: relative;
            animation: glitch 1s linear infinite;
        }

        @keyframes glitch{
            0%, 100% { transform: translate(0, 0) skew(0deg); }
            20%, 80% { transform: translate(2px, 0) skew(0deg); }
            40%, 60% { transform: translate(-2px, 0) skew(0deg); }
            50% { transform: translate(0, 0) skew(5deg); }
        }

        .glitch:before,
        .glitch:after{
            content: attr(title);
            position: absolute;
            left: 0;
        }

        .glitch:before{
            animation: glitchTop 1s linear infinite;
            clip-path: polygon(0 0, 100% 0, 100% 33%, 0 33%);
            -webkit-clip-path: polygon(0 0, 100% 0, 100% 33%, 0 33%);
        }

        @keyframes glitchTop{
            0%, 100% { transform: translate(0, 0); }
            20%, 80% { transform: translate(2px, -2px); }
            40%, 60% { transform: translate(-2px, 2px); }
            50% { transform: translate(13px, -1px) skew(-13deg); }
        }

        .glitch:after{
            animation: glitchBottom 1.5s linear infinite;
            clip-path: polygon(0 67%, 100% 67%, 100% 100%, 0 100%);
            -webkit-clip-path: polygon(0 67%, 100% 67%, 100% 100%, 0 100%);
        }

        @keyframes glitchBottom{
            0%, 100% { transform: translate(0, 0); }
            20%, 80% { transform: translate(-2px, 0); }
            40%, 60% { transform: translate(2px, 0); }
            50% { transform: translate(-22px, 5px) skew(21deg); }
        }

        a {
            margin-top: 20px;
            color: #fff;
            text-decoration: none;
            background: #444;
            padding: 10px 20px;
            border-radius: 5px;
            transition: background 0.3s;
        }

        a:hover {
            background: #666;
        }

        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #fff;
            opacity: 0.8;
            animation: float 5s linear infinite, glitchParticle 1s linear infinite;
        }

        @keyframes float {
            0% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(-1000px); opacity: 0; }
        }

        @keyframes glitchParticle {
            0%, 100% { transform: translate(0, 0); }
            20%, 80% { transform: translate(2px, -2px); }
            40%, 60% { transform: translate(-2px, 2px); }
        }
    </style>
</head>
<body>
    <div class="glitch" title="NO ACCESS">NO ACCESS</div>
    <a href="/">Go back to the homepage</a>
    <div class="particles"></div>
    <script>
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = `${Math.random() * 100}vw`;
            particle.style.top = `${Math.random() * 100}vh`;
            particle.style.animationDuration = `${Math.random() * 3 + 2}s, 1s`;
            document.querySelector('.particles').appendChild(particle);
            setTimeout(() => particle.remove(), 5000);
        }

        setInterval(createParticle, 100);
    </script>
</body>
</html>