const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.AutoModerationRuleUpdate,
    async execute(oldRule, newRule, client) {
        try {
            // Only handle trigger metadata (content) changes
            const oldMetadata = JSON.stringify(oldRule.triggerMetadata || {});
            const newMetadata = JSON.stringify(newRule.triggerMetadata || {});
            
            if (oldMetadata === newMetadata) return;
            
            console.log(`📝 AutoMod rule content updated: ${newRule.name} in ${newRule.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newRule.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'AUTOMOD_RULE_CONTENT_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'AUTOMOD_RULE_CONTENT_UPDATE',
                user: 'System',
                userId: null,
                action: 'Nội dung quy tắc AutoMod được cập nhật',
                details: `Nội dung trigger của quy tắc AutoMod **${newRule.name}** đã được cập nhật`,
                target: newRule.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the rule content from audit logs
            try {
                const auditLogs = await newRule.guild.fetchAuditLogs({
                    type: 141, // AUTO_MODERATION_RULE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newRule.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Nội dung quy tắc AutoMod được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for automod rule content update');
            }
            
            // Add to database
            await client.db.addAuditLog(newRule.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📝 Nội dung quy tắc AutoMod được cập nhật',
                `Vừa có nội dung trigger của quy tắc AutoMod được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get trigger type names
            const triggerTypes = {
                1: 'Keyword',
                2: 'Harmful Link',
                3: 'Spam',
                4: 'Keyword Preset',
                5: 'Mention Spam'
            };
            
            embed.addFields([
                {
                    name: '> Tên quy tắc',
                    value: `- ${newRule.name}`,
                    inline: true
                },
                {
                    name: '> ID quy tắc',
                    value: `- ${newRule.id}`,
                    inline: true
                },
                {
                    name: '> Loại trigger',
                    value: `- ${triggerTypes[newRule.triggerType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${newRule.enabled ? '✅ Đang hoạt động' : '❌ Đã tắt'}`,
                    inline: true
                }
            ]);
            
            // Compare trigger metadata
            const oldTriggerMetadata = oldRule.triggerMetadata || {};
            const newTriggerMetadata = newRule.triggerMetadata || {};
            
            // Handle keyword filter changes
            if (newRule.triggerType === 1) { // Keyword trigger
                const oldKeywords = oldTriggerMetadata.keywordFilter || [];
                const newKeywords = newTriggerMetadata.keywordFilter || [];
                
                if (JSON.stringify(oldKeywords) !== JSON.stringify(newKeywords)) {
                    const addedKeywords = newKeywords.filter(keyword => !oldKeywords.includes(keyword));
                    const removedKeywords = oldKeywords.filter(keyword => !newKeywords.includes(keyword));
                    
                    if (addedKeywords.length > 0) {
                        embed.addFields({
                            name: '> ✅ Từ khóa được thêm',
                            value: addedKeywords.map(keyword => `• \`${keyword}\``).join('\n'),
                            inline: false
                        });
                    }
                    
                    if (removedKeywords.length > 0) {
                        embed.addFields({
                            name: '> ❌ Từ khóa được xóa',
                            value: removedKeywords.map(keyword => `• \`${keyword}\``).join('\n'),
                            inline: false
                        });
                    }
                    
                    embed.addFields({
                        name: '> 📊 Tổng số từ khóa',
                        value: `- ${newKeywords.length} từ khóa`,
                        inline: true
                    });
                }
                
                // Handle regex patterns
                const oldRegexPatterns = oldTriggerMetadata.regexPatterns || [];
                const newRegexPatterns = newTriggerMetadata.regexPatterns || [];
                
                if (JSON.stringify(oldRegexPatterns) !== JSON.stringify(newRegexPatterns)) {
                    embed.addFields({
                        name: '> 🔍 Regex patterns',
                        value: `- ${newRegexPatterns.length} patterns`,
                        inline: true
                    });
                }
                
                // Handle allow list
                const oldAllowList = oldTriggerMetadata.allowList || [];
                const newAllowList = newTriggerMetadata.allowList || [];
                
                if (JSON.stringify(oldAllowList) !== JSON.stringify(newAllowList)) {
                    embed.addFields({
                        name: '> ✅ Allow list',
                        value: `- ${newAllowList.length} từ được cho phép`,
                        inline: true
                    });
                }
            }
            
            // Handle keyword preset changes
            if (newRule.triggerType === 4) { // Keyword Preset trigger
                const oldPresets = oldTriggerMetadata.presets || [];
                const newPresets = newTriggerMetadata.presets || [];
                
                if (JSON.stringify(oldPresets) !== JSON.stringify(newPresets)) {
                    const presetNames = {
                        1: 'Profanity',
                        2: 'Sexual Content',
                        3: 'Slurs'
                    };
                    
                    const addedPresets = newPresets.filter(preset => !oldPresets.includes(preset));
                    const removedPresets = oldPresets.filter(preset => !newPresets.includes(preset));
                    
                    if (addedPresets.length > 0) {
                        embed.addFields({
                            name: '> ✅ Preset được thêm',
                            value: addedPresets.map(preset => `• ${presetNames[preset] || 'Unknown'}`).join('\n'),
                            inline: false
                        });
                    }
                    
                    if (removedPresets.length > 0) {
                        embed.addFields({
                            name: '> ❌ Preset được xóa',
                            value: removedPresets.map(preset => `• ${presetNames[preset] || 'Unknown'}`).join('\n'),
                            inline: false
                        });
                    }
                }
            }
            
            // Handle mention spam limit changes
            if (newRule.triggerType === 5) { // Mention Spam trigger
                const oldLimit = oldTriggerMetadata.mentionTotalLimit;
                const newLimit = newTriggerMetadata.mentionTotalLimit;
                
                if (oldLimit !== newLimit) {
                    embed.addFields([
                        {
                            name: '> Giới hạn mention cũ',
                            value: `- ${oldLimit || 'Không thiết lập'}`,
                            inline: true
                        },
                        {
                            name: '> Giới hạn mention mới',
                            value: `- ${newLimit || 'Không thiết lập'}`,
                            inline: true
                        }
                    ]);
                }
            }
            
            // Add impact explanation
            embed.addFields({
                name: '> 📝 Tác động',
                value: [
                    '• Nội dung mới sẽ được kiểm tra theo quy tắc cập nhật',
                    '• Thay đổi ngay lập tức nếu quy tắc đang bật',
                    '• Có thể ảnh hưởng đến độ chính xác của việc phát hiện',
                    '• Cần test để đảm bảo hoạt động đúng'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newRule.guild.name} • AutoMod Rule Content Update`,
                iconURL: newRule.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ AutoMod rule content update logged: ${newRule.name}`);
            
        } catch (error) {
            console.error('Error in automodRuleContentUpdate audit log:', error);
        }
    }
};
