function _0x5ed1(){const _0x49ad20=['13373510nFndPV','5204802jYQdxJ','getSubcommand','9ekzwZl','2172028xnnsnX','timestamp','Edited\x20Message','get','Original\x20Message','1670016utIfOP','734064cIHUVA','5QhsQZq','setDescription','discord.js','readFileSync','guildId','snipes','displayAvatarURL','No\x20relevant\x20sniped\x20message\x20found.','utf8','General','edited','setColor','7mnULaW','message','Get\x20the\x20last\x20edited\x20message','exports','No\x20content','500592QTGGPY','reply','./lang.yml','content','author','js-yaml','setName','addFields','addSubcommand','Get\x20the\x20last\x20deleted\x20message','options','snipe','2184200gvsAmS'];_0x5ed1=function(){return _0x49ad20;};return _0x5ed1();}(function(_0x207b8a,_0x4b4852){function _0x325d0d(_0x5c7d9f,_0x103b46){return _0x122e(_0x5c7d9f- -0x38e,_0x103b46);}const _0x532483=_0x207b8a();while(!![]){try{const _0x4cb662=parseInt(_0x325d0d(-0x2d7,-0x2c5))/0x1+parseInt(_0x325d0d(-0x2f4,-0x2f3))/0x2+parseInt(_0x325d0d(-0x2ea,-0x2f7))/0x3+-parseInt(_0x325d0d(-0x2ef,-0x2ff))/0x4*(-parseInt(_0x325d0d(-0x2e8,-0x2df))/0x5)+parseInt(_0x325d0d(-0x2f2,-0x2fd))/0x6*(-parseInt(_0x325d0d(-0x2dc,-0x2dd))/0x7)+parseInt(_0x325d0d(-0x2e9,-0x2f2))/0x8+-parseInt(_0x325d0d(-0x2f0,-0x2ed))/0x9*(parseInt(_0x325d0d(-0x2f3,-0x2e0))/0xa);if(_0x4cb662===_0x4b4852)break;else _0x532483['push'](_0x532483['shift']());}catch(_0x22ad0f){_0x532483['push'](_0x532483['shift']());}}}(_0x5ed1,0x8d6ef));function _0x122e(_0x18b368,_0x1a16a3){const _0x5ed16b=_0x5ed1();return _0x122e=function(_0x122e7a,_0x2b06ef){_0x122e7a=_0x122e7a-0x98;let _0x176b8d=_0x5ed16b[_0x122e7a];return _0x176b8d;},_0x122e(_0x18b368,_0x1a16a3);}const {SlashCommandBuilder,EmbedBuilder}=require(_0x18c425(-0xed,-0xe1)),fs=require('fs'),yaml=require(_0x18c425(-0xe1,-0xcd)),config=yaml['load'](fs[_0x18c425(-0xde,-0xe0)]('./config.yml',_0x18c425(-0xca,-0xdb)));function _0x18c425(_0x58f30b,_0x587eee){return _0x122e(_0x587eee- -0x189,_0x58f30b);}const lang=yaml['load'](fs['readFileSync'](_0x18c425(-0xd8,-0xd0),_0x18c425(-0xe9,-0xdb)));module[_0x18c425(-0xe2,-0xd4)]={'data':new SlashCommandBuilder()[_0x18c425(-0xd2,-0xcc)](_0x18c425(-0xdf,-0xf0))[_0x18c425(-0xde,-0xe2)]('Retrieve\x20the\x20last\x20deleted\x20or\x20edited\x20message')[_0x18c425(-0xc5,-0xca)](_0x500513=>_0x500513[_0x18c425(-0xcf,-0xcc)]('message')['setDescription'](_0x18c425(-0xc4,-0xc9)))[_0x18c425(-0xb6,-0xca)](_0x3bd629=>_0x3bd629[_0x18c425(-0xcd,-0xcc)](_0x18c425(-0xe0,-0xd9))['setDescription'](_0x18c425(-0xc0,-0xd5))),'category':_0x18c425(-0xd9,-0xda),async 'execute'(_0xe8ccfb,_0x167ca0){const _0x420fa3=_0xe8ccfb[_0x48c231(0xa3,0xa6)][_0x48c231(0xa6,0xab)](),_0x13ba9f=_0x167ca0[_0x48c231(0xa4,0xb9)][_0x48c231(0xab,0xb0)](_0xe8ccfb[_0x48c231(0xa9,0xb8)])?.[_0x48c231(0xb4,0xb0)](_0xe8ccfb['channelId']);if(!_0x13ba9f)return _0xe8ccfb['reply']({'content':lang['SnipeNoMsg'],'ephemeral':!![]});const _0x5b3d95=new EmbedBuilder()['setAuthor']({'name':''+_0x13ba9f[_0x48c231(0xb5,0xc9)],'iconURL':''+_0x13ba9f['member']['user'][_0x48c231(0xad,0xba)]()})[_0x48c231(0xc0,0xbf)](config['EmbedColors'])['setTimestamp'](_0x13ba9f[_0x48c231(0xb4,0xae)]);function _0x48c231(_0x3940f5,_0x123440){return _0x18c425(_0x3940f5,_0x123440-0x197);}if(_0x420fa3===_0x48c231(0xcf,0xbe)&&_0x13ba9f[_0x48c231(0xbf,0xbe)])_0x5b3d95[_0x48c231(0xd7,0xcc)]([{'name':_0x48c231(0xb8,0xb1),'value':_0x13ba9f['oldContent']||_0x48c231(0xd1,0xc4)},{'name':_0x48c231(0xb1,0xaf),'value':_0x13ba9f['newContent']||'No\x20content'}]);else{if(_0x420fa3===_0x48c231(0xc1,0xc1)&&!_0x13ba9f['edited'])_0x5b3d95['setDescription'](_0x13ba9f[_0x48c231(0xd1,0xc8)]);else return _0xe8ccfb[_0x48c231(0xb3,0xc6)]({'content':_0x48c231(0xa7,0xbb),'ephemeral':!![]});}return _0xe8ccfb[_0x48c231(0xce,0xc6)]({'embeds':[_0x5b3d95],'ephemeral':!![]});}};