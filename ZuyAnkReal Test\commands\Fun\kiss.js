(function(_0x4b55de,_0x7eb0a){function _0x19ba78(_0x3507cc,_0x12a21c){return _0x178c(_0x3507cc- -0x31e,_0x12a21c);}const _0x4a9267=_0x4b55de();while(!![]){try{const _0x166188=-parseInt(_0x19ba78(-0x20c,-0x218))/0x1*(-parseInt(_0x19ba78(-0x229,-0x237))/0x2)+parseInt(_0x19ba78(-0x21e,-0x221))/0x3*(-parseInt(_0x19ba78(-0x205,-0x1fc))/0x4)+parseInt(_0x19ba78(-0x215,-0x20c))/0x5+parseInt(_0x19ba78(-0x216,-0x220))/0x6+parseInt(_0x19ba78(-0x220,-0x21a))/0x7+parseInt(_0x19ba78(-0x21b,-0x21e))/0x8+parseInt(_0x19ba78(-0x222,-0x226))/0x9*(-parseInt(_0x19ba78(-0x22a,-0x21e))/0xa);if(_0x166188===_0x7eb0a)break;else _0x4a9267['push'](_0x4a9267['shift']());}catch(_0x848c6){_0x4a9267['push'](_0x4a9267['shift']());}}}(_0x3c9a,0x3f8ed));const {EmbedBuilder,SlashCommandBuilder}=require(_0x7820f8(0x367,0x374)),fs=require('fs'),yaml=require(_0x7820f8(0x373,0x363)),config=yaml[_0x7820f8(0x34b,0x353)](fs['readFileSync'](_0x7820f8(0x364,0x35e),_0x7820f8(0x36c,0x361))),kissGifs=[_0x7820f8(0x36a,0x355),'https://media1.tenor.com/m/GqRRX_s5XLgAAAAd/miss-you-kiss.gif',_0x7820f8(0x35f,0x367),'https://media1.tenor.com/m/ZXBks2QSfdgAAAAd/cats-kittens.gif',_0x7820f8(0x36d,0x36e),'https://media1.tenor.com/m/dnkXvJVb5cAAAAAC/bear-blow-a-kiss.gif',_0x7820f8(0x38c,0x378),'https://media1.tenor.com/m/QPtL6q_2VjkAAAAC/funny-lol.gif',_0x7820f8(0x37b,0x368),_0x7820f8(0x37f,0x372),_0x7820f8(0x361,0x370),_0x7820f8(0x36b,0x377)];function _0x7820f8(_0x357e5b,_0x5cba80){return _0x178c(_0x5cba80-0x25d,_0x357e5b);}function _0x178c(_0x14ebe8,_0x5dd72d){const _0x3c9ae6=_0x3c9a();return _0x178c=function(_0x178c05,_0x179bc1){_0x178c05=_0x178c05-0xf4;let _0x5bb089=_0x3c9ae6[_0x178c05];return _0x5bb089;},_0x178c(_0x14ebe8,_0x5dd72d);}function _0x3c9a(){const _0x2ee8b9=['760984iMyIcj','Send\x20a\x20kiss\x20to\x20someone\x20to\x20brighten\x20their\x20day!','612903oxZxiZ','././config.yml','addUserOption','1477608Rdpgeo','utf8','EmbedColors','js-yaml','floor','3120714ujHsKf','2359600XmGiBh','https://media1.tenor.com/m/dCAFwEwjDAcAAAAC/kisses.gif','https://media1.tenor.com/m/o9wzbXEAlr8AAAAd/seal-seal-kiss.gif','setDescription','setColor','exports','setImage','setRequired','https://media1.tenor.com/m/o_5RQarGvJ0AAAAC/kiss.gif','440567mWXEqS','https://media1.tenor.com/m/nAxkMLHuHrMAAAAC/kiss-kissing.gif','>\x20sends\x20kisses\x20your\x20way\x20<@','https://media1.tenor.com/m/Cc8vDOXSEzQAAAAC/kisses-kiss.gif','user','discord.js','Fun','4epXUhT','https://media1.tenor.com/m/FwbvGXvGE5oAAAAC/goth-girl-goth-kiss.gif','https://media1.tenor.com/m/QQTLF-JE2VcAAAAC/kiss.gif','setName','12613850XULNQn','2cuSDgl','load','getUser','https://media1.tenor.com/m/VchKuu12CBUAAAAd/ted2012-blow-a-kiss.gif','random','target','The\x20user\x20to\x20kiss','9CBNhkG','length'];_0x3c9a=function(){return _0x2ee8b9;};return _0x3c9a();}module[_0x7820f8(0x37c,0x36b)]={'data':new SlashCommandBuilder()[_0x7820f8(0x378,0x379)]('kiss')[_0x7820f8(0x364,0x369)](_0x7820f8(0x370,0x35c))[_0x7820f8(0x36d,0x35f)](_0x3c502c=>_0x3c502c[_0x7820f8(0x384,0x379)](_0x7820f8(0x35d,0x357))['setDescription'](_0x7820f8(0x363,0x358))[_0x7820f8(0x370,0x36d)](!![])),'category':_0x7820f8(0x369,0x375),async 'execute'(_0x3bfa8f,_0x1e17cb){const _0x32cf6f=_0x3bfa8f['options'][_0x5c996c(-0x65,-0x69)]('target');function _0x5c996c(_0x53a271,_0x228a44){return _0x7820f8(_0x228a44,_0x53a271- -0x3b9);}const _0x203baf=kissGifs[Math[_0x5c996c(-0x55,-0x65)](Math[_0x5c996c(-0x63,-0x66)]()*kissGifs[_0x5c996c(-0x5f,-0x62)])],_0x4bb31f=new EmbedBuilder()['setDescription']('<@'+_0x3bfa8f[_0x5c996c(-0x46,-0x49)]['id']+_0x5c996c(-0x48,-0x4e)+_0x32cf6f['id']+'>.')[_0x5c996c(-0x4d,-0x59)](_0x203baf)[_0x5c996c(-0x4f,-0x47)](config[_0x5c996c(-0x57,-0x43)]);await _0x3bfa8f['reply']({'embeds':[_0x4bb31f]});}};