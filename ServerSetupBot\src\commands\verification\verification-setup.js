const { SlashCommandBuilder, ChannelType, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { canSendMessages, canAddReactions, canManageRole } = require('../../utils/permissions.js');
const { isValidEmoji } = require('../../utils/validators.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('verification-setup')
        .setDescription('Thiết lập hệ thống xác minh thành viên')
        .addSubcommand(subcommand =>
            subcommand
                .setName('enable')
                .setDescription('Bật hệ thống xác minh')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Kênh xác minh')
                        .setRequired(true)
                        .addChannelTypes(ChannelType.GuildText)
                )
                .addRoleOption(option =>
                    option.setName('verified_role')
                        .setDescription('Role sẽ được gán sau khi xác minh')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('Loại xác minh')
                        .setRequired(true)
                        .addChoices(
                            { name: '👆 Button - Nhấn nút để xác minh', value: 'button' },
                            { name: '👍 Reaction - React emoji để xác minh', value: 'reaction' },
                            { name: '🔢 Captcha - Giải captcha để xác minh', value: 'captcha' }
                        )
                )
                .addRoleOption(option =>
                    option.setName('unverified_role')
                        .setDescription('Role cho thành viên chưa xác minh (tùy chọn)')
                        .setRequired(false)
                )
                .addStringOption(option =>
                    option.setName('emoji')
                        .setDescription('Emoji cho reaction (chỉ dùng với type reaction)')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('disable')
                .setDescription('Tắt hệ thống xác minh')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('config')
                .setDescription('Xem cấu hình hệ thống xác minh')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('message')
                .setDescription('Gửi tin nhắn xác minh vào channel')
        ),
    category: 'verification',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();
        const guildId = interaction.guild.id;
        
        switch (subcommand) {
            case 'enable':
                await handleEnable(interaction, client, guildId);
                break;
            case 'disable':
                await handleDisable(interaction, client, guildId);
                break;
            case 'config':
                await handleConfig(interaction, client, guildId);
                break;
            case 'message':
                await handleMessage(interaction, client, guildId);
                break;
        }
    },
};

async function handleEnable(interaction, client, guildId) {
    const channel = interaction.options.getChannel('channel');
    const verifiedRole = interaction.options.getRole('verified_role');
    const unverifiedRole = interaction.options.getRole('unverified_role');
    const verificationType = interaction.options.getString('type');
    const emoji = interaction.options.getString('emoji') || '✅';
    
    // Kiểm tra quyền bot trong channel
    const canSend = canSendMessages(channel);
    if (!canSend.canSend) {
        const errorEmbed = createErrorEmbed(
            'Lỗi quyền hạn!',
            `${canSend.reason}\n\nVui lòng cấp quyền cho bot hoặc chọn kênh khác.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    // Kiểm tra quyền quản lý role
    const canManageVerified = canManageRole(interaction.guild, verifiedRole);
    if (!canManageVerified.canManage) {
        const errorEmbed = createErrorEmbed(
            'Bot không thể quản lý role!',
            `${canManageVerified.reason}\n\nRole: ${verifiedRole}`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    if (unverifiedRole) {
        const canManageUnverified = canManageRole(interaction.guild, unverifiedRole);
        if (!canManageUnverified.canManage) {
            const errorEmbed = createErrorEmbed(
                'Bot không thể quản lý role!',
                `${canManageUnverified.reason}\n\nRole: ${unverifiedRole}`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    }
    
    // Kiểm tra emoji nếu dùng reaction
    if (verificationType === 'reaction') {
        if (!isValidEmoji(emoji)) {
            const errorEmbed = createErrorEmbed(
                'Emoji không hợp lệ!',
                'Vui lòng sử dụng emoji Unicode hoặc custom emoji hợp lệ.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const canReact = canAddReactions(channel);
        if (!canReact.canReact) {
            const errorEmbed = createErrorEmbed(
                'Bot không thể thêm reaction!',
                canReact.reason
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    }
    
    try {
        // Lưu cấu hình vào database
        await client.db.updateVerificationConfig(guildId, {
            enabled: 1,
            verification_type: verificationType,
            verification_channel_id: channel.id,
            verified_role_id: verifiedRole.id,
            verification_role_id: unverifiedRole?.id || null,
            verification_emoji: emoji
        });
        
        const successEmbed = createSuccessEmbed(
            'Hệ thống xác minh đã được bật!',
            `**Kênh xác minh:** ${channel}\n` +
            `**Loại xác minh:** ${getVerificationTypeName(verificationType)}\n` +
            `**Role sau xác minh:** ${verifiedRole}\n` +
            `**Role chưa xác minh:** ${unverifiedRole || 'Không có'}\n` +
            `**Emoji:** ${verificationType === 'reaction' ? emoji : 'Không áp dụng'}`
        );
        
        successEmbed.addFields({
            name: '📝 Bước tiếp theo',
            value: 'Sử dụng `/verification-setup message` để gửi tin nhắn xác minh vào channel!',
            inline: false
        });
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi cập nhật verification config:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể lưu cấu hình xác minh. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleDisable(interaction, client, guildId) {
    try {
        await client.db.updateVerificationConfig(guildId, {
            enabled: 0
        });
        
        const successEmbed = createSuccessEmbed(
            'Hệ thống xác minh đã được tắt!',
            'Thành viên mới sẽ không cần xác minh nữa.'
        );
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi tắt verification:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể tắt hệ thống xác minh. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleConfig(interaction, client, guildId) {
    try {
        const config = await client.db.getVerificationConfig(guildId);
        
        if (!config) {
            const infoEmbed = createInfoEmbed(
                'Chưa có cấu hình',
                'Hệ thống xác minh chưa được thiết lập.\n\nSử dụng `/verification-setup enable` để bắt đầu!'
            );
            return await interaction.reply({ embeds: [infoEmbed] });
        }
        
        const channel = interaction.guild.channels.cache.get(config.verification_channel_id);
        const verifiedRole = interaction.guild.roles.cache.get(config.verified_role_id);
        const unverifiedRole = config.verification_role_id ? 
            interaction.guild.roles.cache.get(config.verification_role_id) : null;
        
        const embed = createInfoEmbed(
            'Cấu hình hệ thống xác minh',
            `**Trạng thái:** ${config.enabled ? '✅ Đã bật' : '❌ Đã tắt'}\n` +
            `**Loại xác minh:** ${getVerificationTypeName(config.verification_type)}\n` +
            `**Kênh xác minh:** ${channel || '❌ Đã bị xóa'}\n` +
            `**Role sau xác minh:** ${verifiedRole || '❌ Đã bị xóa'}\n` +
            `**Role chưa xác minh:** ${unverifiedRole || 'Không có'}\n` +
            `**Emoji:** ${config.verification_emoji || '✅'}`
        );
        
        embed.addFields({
            name: '🔧 Quản lý',
            value: '• `/verification-setup enable` - Bật/cập nhật cấu hình\n' +
                   '• `/verification-setup disable` - Tắt hệ thống\n' +
                   '• `/verification-setup message` - Gửi tin nhắn xác minh',
            inline: false
        });
        
        await interaction.reply({ embeds: [embed] });
        
    } catch (error) {
        console.error('Lỗi khi lấy verification config:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể lấy cấu hình xác minh. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleMessage(interaction, client, guildId) {
    try {
        const config = await client.db.getVerificationConfig(guildId);
        
        if (!config || !config.enabled) {
            const errorEmbed = createErrorEmbed(
                'Hệ thống chưa được bật!',
                'Vui lòng sử dụng `/verification-setup enable` để bật hệ thống xác minh trước.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const channel = interaction.guild.channels.cache.get(config.verification_channel_id);
        if (!channel) {
            const errorEmbed = createErrorEmbed(
                'Kênh xác minh không tồn tại!',
                'Kênh xác minh đã bị xóa. Vui lòng thiết lập lại.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        // Tạo embed xác minh
        const verificationEmbed = createInfoEmbed(
            '🔐 Xác minh thành viên',
            `Chào mừng bạn đến với **${interaction.guild.name}**!\n\n` +
            `Để truy cập đầy đủ vào server, bạn cần xác minh tài khoản của mình.\n\n` +
            `**Cách xác minh:**\n${getVerificationInstructions(config.verification_type, config.verification_emoji)}`
        );
        
        verificationEmbed.setThumbnail(interaction.guild.iconURL({ dynamic: true }));
        verificationEmbed.setFooter({
            text: 'Hệ thống xác minh tự động • Server Setup Bot',
            iconURL: client.user.displayAvatarURL()
        });
        
        let messageOptions = { embeds: [verificationEmbed] };
        
        // Thêm button hoặc reaction tùy theo loại
        if (config.verification_type === 'button') {
            const verifyButton = new ButtonBuilder()
                .setCustomId('verify_member')
                .setLabel('Xác minh tài khoản')
                .setStyle(ButtonStyle.Success)
                .setEmoji('✅');
            
            const row = new ActionRowBuilder().addComponents(verifyButton);
            messageOptions.components = [row];
        }
        
        const message = await channel.send(messageOptions);
        
        // Thêm reaction nếu cần
        if (config.verification_type === 'reaction') {
            await message.react(config.verification_emoji);
        }
        
        const successEmbed = createSuccessEmbed(
            'Tin nhắn xác minh đã được gửi!',
            `Tin nhắn xác minh đã được gửi tới ${channel}\n\n` +
            `Thành viên mới có thể xác minh bằng cách ${getVerificationAction(config.verification_type)}.`
        );
        
        await interaction.reply({ embeds: [successEmbed], ephemeral: true });
        
    } catch (error) {
        console.error('Lỗi khi gửi verification message:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi gửi tin nhắn!',
            'Không thể gửi tin nhắn xác minh. Vui lòng kiểm tra quyền bot!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

function getVerificationTypeName(type) {
    const types = {
        'button': '👆 Button (Nhấn nút)',
        'reaction': '👍 Reaction (React emoji)',
        'captcha': '🔢 Captcha (Giải captcha)'
    };
    return types[type] || type;
}

function getVerificationInstructions(type, emoji) {
    const instructions = {
        'button': '• Nhấn nút **"Xác minh tài khoản"** bên dưới',
        'reaction': `• React emoji ${emoji} vào tin nhắn này`,
        'captcha': '• Sử dụng lệnh `/verify` và giải captcha'
    };
    return instructions[type] || 'Làm theo hướng dẫn';
}

function getVerificationAction(type) {
    const actions = {
        'button': 'nhấn nút xác minh',
        'reaction': 'react emoji',
        'captcha': 'giải captcha'
    };
    return actions[type] || 'làm theo hướng dẫn';
}
