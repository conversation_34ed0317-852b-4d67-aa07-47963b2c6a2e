const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle forum tags changes for forum channels
            if (!oldChannel.availableTags || !newChannel.availableTags) return;
            
            // Check if tags actually changed
            const oldTagsString = JSON.stringify(oldChannel.availableTags.map(tag => ({ id: tag.id, name: tag.name })));
            const newTagsString = JSON.stringify(newChannel.availableTags.map(tag => ({ id: tag.id, name: tag.name })));
            
            if (oldTagsString === newTagsString) return;
            
            console.log(`🏷️ Channel forum tags updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_FORUM_TAGS_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_FORUM_TAGS_UPDATE',
                user: 'System',
                userId: null,
                action: 'Forum tags của kênh được cập nhật',
                details: `Forum tags của kênh **${newChannel.name}** đã được cập nhật`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the forum tags from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Forum tags được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for channel forum tags update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🏷️ Forum tags của kênh cập nhật',
                `Vừa có forum tags của kênh được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Analyze tag changes
            const oldTags = oldChannel.availableTags || [];
            const newTags = newChannel.availableTags || [];
            
            const addedTags = newTags.filter(newTag => !oldTags.find(oldTag => oldTag.id === newTag.id));
            const removedTags = oldTags.filter(oldTag => !newTags.find(newTag => newTag.id === oldTag.id));
            const modifiedTags = newTags.filter(newTag => {
                const oldTag = oldTags.find(oldTag => oldTag.id === newTag.id);
                return oldTag && (oldTag.name !== newTag.name || oldTag.emoji?.name !== newTag.emoji?.name);
            });
            
            // Add tag counts
            embed.addFields([
                {
                    name: '> Số tags cũ',
                    value: `- ${oldTags.length} tags`,
                    inline: true
                },
                {
                    name: '> Số tags mới',
                    value: `- ${newTags.length} tags`,
                    inline: true
                }
            ]);
            
            // Show added tags
            if (addedTags.length > 0) {
                const addedTagsList = addedTags.map(tag => {
                    const emoji = tag.emoji ? (tag.emoji.id ? `<:${tag.emoji.name}:${tag.emoji.id}>` : tag.emoji.name) : '';
                    return `${emoji} ${tag.name}`;
                }).join('\n');
                
                embed.addFields({
                    name: '> ✅ Tags được thêm',
                    value: addedTagsList.length > 1000 ? addedTagsList.substring(0, 1000) + '...' : addedTagsList,
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for additions
            }
            
            // Show removed tags
            if (removedTags.length > 0) {
                const removedTagsList = removedTags.map(tag => {
                    const emoji = tag.emoji ? (tag.emoji.id ? `<:${tag.emoji.name}:${tag.emoji.id}>` : tag.emoji.name) : '';
                    return `${emoji} ${tag.name}`;
                }).join('\n');
                
                embed.addFields({
                    name: '> ❌ Tags được xóa',
                    value: removedTagsList.length > 1000 ? removedTagsList.substring(0, 1000) + '...' : removedTagsList,
                    inline: false
                });
                if (addedTags.length === 0) {
                    embed.setColor(0xe74c3c); // Red for removals only
                }
            }
            
            // Show modified tags
            if (modifiedTags.length > 0) {
                const modifiedTagsList = modifiedTags.map(tag => {
                    const oldTag = oldTags.find(oldTag => oldTag.id === tag.id);
                    const emoji = tag.emoji ? (tag.emoji.id ? `<:${tag.emoji.name}:${tag.emoji.id}>` : tag.emoji.name) : '';
                    return `${emoji} ${oldTag.name} → ${tag.name}`;
                }).join('\n');
                
                embed.addFields({
                    name: '> 🔄 Tags được sửa đổi',
                    value: modifiedTagsList.length > 1000 ? modifiedTagsList.substring(0, 1000) + '...' : modifiedTagsList,
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Forum Tags Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel forum tags update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelForumTagsUpdate audit log:', error);
        }
    }
};
