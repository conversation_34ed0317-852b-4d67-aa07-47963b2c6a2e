const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { isValidUrl } = require('../../utils/validators.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('emoji-manage')
        .setDescription('Quản lý emoji tùy chỉnh của server')
        .addSubcommand(subcommand =>
            subcommand
                .setName('add')
                .setDescription('Thêm emoji mới từ URL hoặc file')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('Tên emoji (chỉ chữ cái, số, dấu gạch dưới)')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('url')
                        .setDescription('URL hình ảnh emoji')
                        .setRequired(false)
                )
                .addAttachmentOption(option =>
                    option.setName('file')
                        .setDescription('File hình ảnh emoji')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('remove')
                .setDescription('Xóa emoji khỏi server')
                .addStringOption(option =>
                    option.setName('emoji')
                        .setDescription('Emoji cần xóa (tên hoặc emoji)')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('Xem danh sách emoji của server')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('info')
                .setDescription('Xem thông tin chi tiết của emoji')
                .addStringOption(option =>
                    option.setName('emoji')
                        .setDescription('Emoji cần xem thông tin')
                        .setRequired(true)
                )
        ),
    category: 'content',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền bot
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageEmojisAndStickers)) {
            const errorEmbed = createErrorEmbed(
                'Bot không có quyền!',
                'Bot cần quyền **Manage Emojis and Stickers** để quản lý emoji.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const subcommand = interaction.options.getSubcommand();
        
        switch (subcommand) {
            case 'add':
                await handleAddEmoji(interaction, client);
                break;
            case 'remove':
                await handleRemoveEmoji(interaction, client);
                break;
            case 'list':
                await handleListEmojis(interaction, client);
                break;
            case 'info':
                await handleEmojiInfo(interaction, client);
                break;
        }
    },
};

async function handleAddEmoji(interaction, client) {
    const name = interaction.options.getString('name');
    const url = interaction.options.getString('url');
    const file = interaction.options.getAttachment('file');
    
    // Validate emoji name
    if (!/^[a-zA-Z0-9_]+$/.test(name)) {
        const errorEmbed = createErrorEmbed(
            'Tên emoji không hợp lệ!',
            'Tên emoji chỉ được chứa chữ cái, số và dấu gạch dưới (_).'
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    // Check if emoji name already exists
    const existingEmoji = interaction.guild.emojis.cache.find(emoji => emoji.name === name);
    if (existingEmoji) {
        const errorEmbed = createErrorEmbed(
            'Tên emoji đã tồn tại!',
            `Emoji với tên **${name}** đã tồn tại trong server.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    // Get image source
    let imageSource;
    if (file) {
        // Validate file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.contentType)) {
            const errorEmbed = createErrorEmbed(
                'Định dạng file không hỗ trợ!',
                'Chỉ hỗ trợ các định dạng: PNG, JPEG, JPG, GIF, WEBP'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        // Check file size (256KB limit for Discord)
        if (file.size > 256 * 1024) {
            const errorEmbed = createErrorEmbed(
                'File quá lớn!',
                'Kích thước file không được vượt quá 256KB.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        imageSource = file.url;
    } else if (url) {
        // Validate URL
        if (!isValidUrl(url)) {
            const errorEmbed = createErrorEmbed(
                'URL không hợp lệ!',
                'Vui lòng cung cấp URL hình ảnh hợp lệ.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        imageSource = url;
    } else {
        const errorEmbed = createErrorEmbed(
            'Thiếu hình ảnh!',
            'Vui lòng cung cấp URL hoặc upload file hình ảnh.'
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    try {
        const emoji = await interaction.guild.emojis.create({
            attachment: imageSource,
            name: name,
            reason: `Emoji được thêm bởi ${interaction.user.tag}`
        });
        
        const successEmbed = createSuccessEmbed(
            'Đã thêm emoji thành công!',
            `**Tên:** ${emoji.name}\n` +
            `**Emoji:** ${emoji}\n` +
            `**ID:** ${emoji.id}\n` +
            `**Animated:** ${emoji.animated ? 'Có' : 'Không'}`
        );
        
        successEmbed.setThumbnail(emoji.url);
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi thêm emoji:', error);
        
        let errorMessage = 'Không thể thêm emoji. ';
        if (error.code === 30008) {
            errorMessage += 'Server đã đạt giới hạn số lượng emoji.';
        } else if (error.code === 50035) {
            errorMessage += 'Hình ảnh không hợp lệ hoặc quá lớn.';
        } else {
            errorMessage += 'Vui lòng thử lại sau!';
        }
        
        const errorEmbed = createErrorEmbed('Lỗi thêm emoji!', errorMessage);
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleRemoveEmoji(interaction, client) {
    const emojiInput = interaction.options.getString('emoji');
    
    // Find emoji by name or ID
    let emoji = interaction.guild.emojis.cache.find(e => 
        e.name === emojiInput || 
        e.id === emojiInput ||
        e.toString() === emojiInput
    );
    
    if (!emoji) {
        const errorEmbed = createErrorEmbed(
            'Không tìm thấy emoji!',
            `Emoji **${emojiInput}** không tồn tại trong server.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    try {
        const emojiName = emoji.name;
        const emojiUrl = emoji.url;
        
        await emoji.delete(`Emoji được xóa bởi ${interaction.user.tag}`);
        
        const successEmbed = createSuccessEmbed(
            'Đã xóa emoji thành công!',
            `Emoji **${emojiName}** đã được xóa khỏi server.`
        );
        
        successEmbed.setThumbnail(emojiUrl);
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi xóa emoji:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi xóa emoji!',
            'Không thể xóa emoji. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleListEmojis(interaction, client) {
    const emojis = interaction.guild.emojis.cache;
    
    if (emojis.size === 0) {
        const infoEmbed = createInfoEmbed(
            'Chưa có emoji tùy chỉnh',
            'Server chưa có emoji tùy chỉnh nào.\n\nSử dụng `/emoji-manage add` để thêm emoji!'
        );
        return await interaction.reply({ embeds: [infoEmbed] });
    }
    
    const embed = createInfoEmbed(
        `📝 Danh sách Emoji (${emojis.size})`,
        `Server có ${emojis.size} emoji tùy chỉnh:`
    );
    
    // Group emojis by animated/static
    const staticEmojis = emojis.filter(emoji => !emoji.animated);
    const animatedEmojis = emojis.filter(emoji => emoji.animated);
    
    if (staticEmojis.size > 0) {
        const emojiList = staticEmojis.map(emoji => `${emoji} \`${emoji.name}\``).join('\n');
        embed.addFields({
            name: `🖼️ Static Emojis (${staticEmojis.size})`,
            value: emojiList.length > 1024 ? emojiList.substring(0, 1021) + '...' : emojiList,
            inline: false
        });
    }
    
    if (animatedEmojis.size > 0) {
        const emojiList = animatedEmojis.map(emoji => `${emoji} \`${emoji.name}\``).join('\n');
        embed.addFields({
            name: `🎬 Animated Emojis (${animatedEmojis.size})`,
            value: emojiList.length > 1024 ? emojiList.substring(0, 1021) + '...' : emojiList,
            inline: false
        });
    }
    
    // Show limits
    const maxEmojis = interaction.guild.premiumTier >= 1 ? 100 : 50;
    const maxAnimated = interaction.guild.premiumTier >= 1 ? 100 : 50;
    
    embed.addFields({
        name: '📊 Giới hạn',
        value: `• Static: ${staticEmojis.size}/${maxEmojis}\n• Animated: ${animatedEmojis.size}/${maxAnimated}`,
        inline: false
    });
    
    await interaction.reply({ embeds: [embed] });
}

async function handleEmojiInfo(interaction, client) {
    const emojiInput = interaction.options.getString('emoji');
    
    // Find emoji
    let emoji = interaction.guild.emojis.cache.find(e => 
        e.name === emojiInput || 
        e.id === emojiInput ||
        e.toString() === emojiInput
    );
    
    if (!emoji) {
        const errorEmbed = createErrorEmbed(
            'Không tìm thấy emoji!',
            `Emoji **${emojiInput}** không tồn tại trong server.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    const embed = createInfoEmbed(
        `📝 Thông tin Emoji: ${emoji.name}`,
        `Chi tiết về emoji **${emoji.name}**:`
    );
    
    embed.addFields(
        { name: '📛 Tên', value: emoji.name, inline: true },
        { name: '🆔 ID', value: emoji.id, inline: true },
        { name: '🎬 Animated', value: emoji.animated ? 'Có' : 'Không', inline: true },
        { name: '🔗 URL', value: `[Xem ảnh](${emoji.url})`, inline: true },
        { name: '📅 Tạo lúc', value: emoji.createdAt.toLocaleString('vi-VN'), inline: true },
        { name: '💬 Sử dụng', value: `\`${emoji}\``, inline: true }
    );
    
    embed.setThumbnail(emoji.url);
    
    await interaction.reply({ embeds: [embed] });
}
