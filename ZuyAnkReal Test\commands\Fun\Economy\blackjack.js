(function(_0x3f8824,_0x28bc71){function _0x48aba5(_0x1c7911,_0x525982){return _0x50d1(_0x1c7911- -0x218,_0x525982);}const _0x49c53c=_0x3f8824();while(!![]){try{const _0x14c500=-parseInt(_0x48aba5(-0x164,-0x17f))/0x1+parseInt(_0x48aba5(-0x168,-0x14e))/0x2*(-parseInt(_0x48aba5(-0x12d,-0x110))/0x3)+parseInt(_0x48aba5(-0x12f,-0x102))/0x4+-parseInt(_0x48aba5(-0x13b,-0x16a))/0x5+parseInt(_0x48aba5(-0x117,-0x110))/0x6*(parseInt(_0x48aba5(-0x13d,-0x15f))/0x7)+parseInt(_0x48aba5(-0x118,-0x12a))/0x8*(-parseInt(_0x48aba5(-0x125,-0x101))/0x9)+parseInt(_0x48aba5(-0x139,-0x108))/0xa*(parseInt(_0x48aba5(-0x16d,-0x149))/0xb);if(_0x14c500===_0x28bc71)break;else _0x49c53c['push'](_0x49c53c['shift']());}catch(_0x52ed5e){_0x49c53c['push'](_0x49c53c['shift']());}}}(_0x2c98,0x74391));const {SlashCommandBuilder,ActionRowBuilder,ButtonBuilder,ButtonStyle,EmbedBuilder,AttachmentBuilder}=require(_0x58f00b(0x2de,0x2bf)),{createCanvas}=require(_0x58f00b(0x2b6,0x27a)),User=require(_0x58f00b(0x2a5,0x28e)),fs=require('fs'),yaml=require(_0x58f00b(0x2a8,0x2a7)),config=yaml['load'](fs['readFileSync'](_0x58f00b(0x2ae,0x2a8),_0x58f00b(0x28f,0x25f))),lang=yaml[_0x58f00b(0x2ea,0x2ce)](fs[_0x58f00b(0x291,0x26a)](_0x58f00b(0x2b0,0x2d3),_0x58f00b(0x28f,0x28d))),parseDuration=require('./Utility/parseDuration'),{checkActiveBooster,replacePlaceholders}=require(_0x58f00b(0x2a6,0x2a8));module[_0x58f00b(0x28a,0x2b0)]={'data':new SlashCommandBuilder()['setName'](_0x58f00b(0x2a3,0x2ad))[_0x58f00b(0x28c,0x2b6)](_0x58f00b(0x2d4,0x2db))['addIntegerOption'](_0x3772e9=>_0x3772e9[_0x58f00b(0x2d9,0x2e5)](_0x58f00b(0x290,0x257))['setDescription'](_0x58f00b(0x2bc,0x280))[_0x58f00b(0x29e,0x262)](!![])),'category':_0x58f00b(0x2a2,0x27a),async 'execute'(_0x37b5b5){const _0x36851d=_0x37b5b5[_0x4c31e1(0x194,0x1af)][_0x4c31e1(0x19c,0x1c2)](_0x4c31e1(0x13b,0x11e));if(_0x36851d<=0x0)return _0x37b5b5['reply']({'content':lang[_0x4c31e1(0x14d,0x176)][_0x4c31e1(0x193,0x18a)][_0x4c31e1(0x13f,0x178)],'ephemeral':!![]});await _0x37b5b5[_0x4c31e1(0x141,0x13b)]();function _0x4c31e1(_0x700139,_0xa8cfdf){return _0x58f00b(_0x700139- -0x155,_0xa8cfdf);}try{let _0x385ca0=await User[_0x4c31e1(0x18f,0x1a0)]({'userId':_0x37b5b5[_0x4c31e1(0x166,0x176)]['id'],'guildId':_0x37b5b5[_0x4c31e1(0x157,0x17d)]['id']},{'balance':0x1,'commandData.lastBlackjack':0x1,'transactionLogs':0x1,'boosters':0x1});!_0x385ca0?_0x385ca0=await initializeUser(_0x37b5b5[_0x4c31e1(0x166,0x176)]['id'],_0x37b5b5[_0x4c31e1(0x157,0x176)]['id']):(ensureUserSchema(_0x385ca0),await _0x385ca0[_0x4c31e1(0x1a1,0x16d)]());if(isOnCooldown(_0x385ca0,_0x4c31e1(0x138,0x133),config[_0x4c31e1(0x14d,0x137)]['Blackjack'][_0x4c31e1(0x197,0x15f)])){const _0x7df49d=new Date(_0x385ca0[_0x4c31e1(0x131,0x107)][_0x4c31e1(0x17a,0x18b)][_0x4c31e1(0x176,0x176)]()+parseDuration(config[_0x4c31e1(0x14d,0x111)][_0x4c31e1(0x138,0x150)][_0x4c31e1(0x197,0x188)])),_0x8bbef5=createCooldownEmbed(_0x7df49d);return _0x37b5b5['editReply']({'embeds':[_0x8bbef5]});}if(_0x385ca0[_0x4c31e1(0x142,0x148)]<_0x36851d)return _0x37b5b5[_0x4c31e1(0x180,0x188)]({'embeds':[createNoMoneyEmbed()],'ephemeral':!![]});const _0x35a0db=createDeck(),_0x106250=[drawCard(_0x35a0db),drawCard(_0x35a0db)],_0x49239e=[drawCard(_0x35a0db),drawCard(_0x35a0db)];_0x385ca0[_0x4c31e1(0x142,0x13b)]-=_0x36851d,_0x385ca0[_0x4c31e1(0x131,0x158)][_0x4c31e1(0x17a,0x151)]=new Date(),await _0x385ca0[_0x4c31e1(0x1a1,0x1a7)]();if(calculateHand(_0x106250)===0x15){const _0x23fea8=calculateHand(_0x49239e),_0x4ce256=_0x23fea8!==0x15;await endBlackjackGame(_0x37b5b5,_0x385ca0,_0x36851d,_0x106250,_0x49239e,_0x4ce256,_0x23fea8===0x15);return;}const _0x367b02=await createBlackjackCanvas(_0x106250,_0x49239e,!![]),_0x470dfc=createBlackjackEmbed(_0x36851d);await _0x37b5b5[_0x4c31e1(0x180,0x19d)]({'embeds':[_0x470dfc],'files':[_0x367b02],'components':createBlackjackButtons(_0x385ca0[_0x4c31e1(0x142,0x127)]>=_0x36851d)});const _0x1ff2c3=await _0x37b5b5[_0x4c31e1(0x15f,0x146)]();setupCollector(_0x37b5b5,_0x1ff2c3,_0x35a0db,_0x106250,_0x49239e,_0x385ca0,_0x36851d);}catch(_0x4aeab2){console[_0x4c31e1(0x19f,0x185)](_0x4c31e1(0x196,0x180),_0x4aeab2),!_0x37b5b5[_0x4c31e1(0x1a7,0x1b7)]&&!_0x37b5b5[_0x4c31e1(0x158,0x194)]&&await _0x37b5b5['editReply']({'content':lang[_0x4c31e1(0x14d,0x129)][_0x4c31e1(0x193,0x17e)][_0x4c31e1(0x19f,0x19f)],'ephemeral':!![]});}}};async function initializeUser(_0x6a3f27,_0x413fc9){function _0x25ae99(_0x5bd6b9,_0x43f3fd){return _0x58f00b(_0x43f3fd- -0x599,_0x5bd6b9);}const _0x4f626b=new User({'userId':_0x6a3f27,'guildId':_0x413fc9,'balance':0x0,'commandData':{'lastBlackjack':null,'lastDaily':null,'lastBeg':null,'lastWork':null,'lastCrime':null,'lastSlot':null,'lastRob':null},'boosters':[],'transactionLogs':[],'inventory':[]});return await _0x4f626b[_0x25ae99(-0x2bf,-0x2a3)](),_0x4f626b;}function ensureUserSchema(_0x316440){!_0x316440['commandData']&&(_0x316440[_0x2caabe(0x3e5,0x3af)]={});!_0x316440[_0x2caabe(0x3e9,0x3af)][_0x2caabe(0x42e,0x3f8)]&&(_0x316440[_0x2caabe(0x38c,0x3af)][_0x2caabe(0x41a,0x3f8)]=null);!Array[_0x2caabe(0x406,0x41b)](_0x316440[_0x2caabe(0x3c1,0x3bc)])&&(_0x316440[_0x2caabe(0x388,0x3bc)]=[]);!Array['isArray'](_0x316440[_0x2caabe(0x462,0x427)])&&(_0x316440[_0x2caabe(0x40a,0x427)]=[]);function _0x2caabe(_0x5b7411,_0x4339b6){return _0x58f00b(_0x4339b6-0x129,_0x5b7411);}!Array[_0x2caabe(0x419,0x41b)](_0x316440['inventory'])&&(_0x316440['inventory']=[]),typeof _0x316440[_0x2caabe(0x392,0x3c0)]!==_0x2caabe(0x43c,0x423)&&(_0x316440[_0x2caabe(0x3ab,0x3c0)]=0x0);}function _0x50d1(_0x2c1d32,_0x5499d0){const _0x2c9823=_0x2c98();return _0x50d1=function(_0x50d19c,_0x2701a9){_0x50d19c=_0x50d19c-0x99;let _0x8ff6e3=_0x2c9823[_0x50d19c];return _0x8ff6e3;},_0x50d1(_0x2c1d32,_0x5499d0);}function isOnCooldown(_0x231451,_0xba3f93,_0x8d59a7){const _0x19a14f=_0x231451[_0x4b5d85(-0x39,-0x5b)][_0x4b5d85(0xa,0x3)+_0xba3f93];if(!_0x19a14f)return![];function _0x4b5d85(_0x3b21d5,_0x5b7ff8){return _0x58f00b(_0x3b21d5- -0x2bf,_0x5b7ff8);}const _0x2bf49d=parseDuration(_0x8d59a7);return new Date()-new Date(_0x19a14f)<_0x2bf49d;}function createCooldownEmbed(_0x41b7ba){function _0x3d63c1(_0x1173ae,_0x49359c){return _0x58f00b(_0x1173ae-0x80,_0x49359c);}return new EmbedBuilder()['setDescription'](replacePlaceholders(lang[_0x3d63c1(0x322,0x325)]['Messages'][_0x3d63c1(0x36c,0x399)],{'nextUse':Math[_0x3d63c1(0x35d,0x32f)](_0x41b7ba['getTime']()/0x3e8)}))['setColor'](_0x3d63c1(0x375,0x357));}function _0x2c98(){const _0x45a36d=['782200gsyJOV','getTime','176130DjtWmt','font','setTitle','lastBlackjack','setLabel','suit','toBuffer','fillRect','Play\x20a\x20game\x20of\x20blackjack','editReply','2062408CEJFbP','blackjack_draw','18EovjpX','setName','blackjack_lose','bold\x2028px\x20sans-serif','getContext','floor','discord.js','Lose','8172EqEfcE','strokeRect','setStyle','#FFFFFF','findOne','Games','stop','#FFD700','Messages','options','load','Error\x20in\x20blackjack\x20command:\x20','cooldown','7512jCgzny','174ZHgGkv','setDisabled','push','getInteger','isArray','reply','error','#FF0000','save','length','\x20coins','customId','number','lineWidth','replied','setColor','boosters','commandData','setCustomId','#006400','noMoney','exports','hit','setDescription','Blackjack','abs','utf8','bet','readFileSync','createMessageComponentCollector','transactionLogs','betAmountError','Dealer:\x20','deferReply','balance','1012tTLhjf','blackjack_win','Danger','pop','Draw','222278GupZfR','setRequired','strokeStyle','stand','50560PfKPSC','Economy','blackjack','fillText','../../../models/UserData','./Utility/helpers','setFooter','js-yaml','update','double','#228B22','guild','deferred','./config.yml','beginPath','./lang.yml','Secondary','#00FF00','Win','fetchReply','includes','canvas','Error\x20in\x20endBlackjackGame:\x20','Hit','closePath','random','user','Bet\x20amount','winMultiplier','#000000','Error\x20during\x20button\x20interaction:\x20','Title','value','footer','Player:\x20','fillStyle','addComponents','bold\x2060px\x20sans-serif','blackjack.png','16051wEEYAI','last'];_0x2c98=function(){return _0x45a36d;};return _0x2c98();}function createNoMoneyEmbed(){function _0x5766cd(_0xce9dfc,_0x16d19e){return _0x58f00b(_0x16d19e- -0x377,_0xce9dfc);}return new EmbedBuilder()[_0x5766cd(-0xce,-0xeb)](lang[_0x5766cd(-0x10c,-0xd5)]['Messages'][_0x5766cd(-0xff,-0xee)])[_0x5766cd(-0xb4,-0x7a)]('#FF0000');}function createBlackjackEmbed(_0x1a38e5){function _0x34d87f(_0x32c67d,_0x5cf425){return _0x58f00b(_0x5cf425- -0x211,_0x32c67d);}return new EmbedBuilder()[_0x34d87f(0xaf,0xbd)](_0x34d87f(0x47,0x7c))['setFooter']({'text':'Bet:\x20'+_0x1a38e5+_0x34d87f(0xf1,0xe7)});}function setupCollector(_0x4e942f,_0x3eeb31,_0x4a25e0,_0x15c329,_0x373099,_0x1509bd,_0x405faf){const _0x52f1ab=_0x3eeb31[_0x26b730(0x365,0x333)]({'time':0x927c0,'filter':_0x59d228=>_0x59d228[_0x26b730(0x32c,0x35c)]['id']===_0x4e942f[_0x26b730(0x350,0x35c)]['id']});function _0x26b730(_0x2d018c,_0x35a237){return _0x58f00b(_0x35a237-0xa1,_0x2d018c);}_0x52f1ab['on']('collect',async _0x51e4f0=>{function _0x12d611(_0x109bad,_0x5a572e){return _0x26b730(_0x5a572e,_0x109bad- -0x22a);}try{if(_0x51e4f0[_0x12d611(0x170,0x164)]===_0x12d611(0x102,0xd4)){_0x15c329[_0x12d611(0x167,0x15c)](drawCard(_0x4a25e0));const _0x3ea3e3=createBlackjackButtons(_0x1509bd[_0x12d611(0x10e,0x114)]>=_0x405faf);if(calculateHand(_0x15c329)>=0x15){await endBlackjackGame(_0x4e942f,_0x1509bd,_0x405faf,_0x15c329,_0x373099,calculateHand(_0x15c329)===0x15,![],_0x51e4f0),_0x52f1ab['stop']();return;}const _0x15b8c7=await createBlackjackCanvas(_0x15c329,_0x373099,!![]);await _0x51e4f0[_0x12d611(0x120,0x155)]({'files':[_0x15b8c7],'components':_0x3ea3e3});}else{if(_0x51e4f0[_0x12d611(0x170,0x157)]===_0x12d611(0x117,0x146))await handleStand(_0x4e942f,_0x51e4f0,_0x1509bd,_0x405faf,_0x15c329,_0x373099,_0x4a25e0),_0x52f1ab[_0x12d611(0x15d,0x172)]();else _0x51e4f0[_0x12d611(0x170,0x164)]===_0x12d611(0x121,0xf8)&&(await handleDouble(_0x4e942f,_0x51e4f0,_0x1509bd,_0x405faf,_0x15c329,_0x373099,_0x4a25e0),_0x52f1ab[_0x12d611(0x15d,0x136)]());}}catch(_0x5b3d5d){console[_0x12d611(0x16b,0x141)](_0x12d611(0x136,0x165),_0x5b3d5d),!_0x51e4f0[_0x12d611(0x173,0x170)]&&!_0x51e4f0[_0x12d611(0x124,0x14e)]&&await _0x51e4f0['reply']({'content':lang[_0x12d611(0x119,0x134)][_0x12d611(0x15f,0x12f)][_0x12d611(0x16b,0x160)],'ephemeral':!![]});}});}function _0x58f00b(_0x448681,_0x349cde){return _0x50d1(_0x448681-0x1ed,_0x349cde);}async function handleStand(_0x1d67f4,_0x1aff23,_0x2ad41d,_0x2b8ec0,_0x56d3eb,_0x2c230e,_0xc00da9){function _0x36a13c(_0x41852a,_0x2c4669){return _0x58f00b(_0x2c4669- -0x1a4,_0x41852a);}while(calculateHand(_0x2c230e)<0x11){_0x2c230e[_0x36a13c(0x125,0x14c)](drawCard(_0xc00da9));}const _0x1ee5b9=calculateHand(_0x56d3eb),_0x683533=calculateHand(_0x2c230e),_0x1d2c7b=_0x1ee5b9<=0x15&&(_0x683533>0x15||_0x1ee5b9>_0x683533),_0x5c2e4a=_0x1ee5b9===_0x683533;await endBlackjackGame(_0x1d67f4,_0x2ad41d,_0x2b8ec0,_0x56d3eb,_0x2c230e,_0x1d2c7b,_0x5c2e4a,_0x1aff23);}async function handleDouble(_0x340940,_0x466da9,_0x1a696d,_0x124013,_0x3947af,_0x4d97de,_0x161516){if(_0x1a696d[_0x369a86(0x252,0x251)]<_0x124013){await _0x466da9[_0x369a86(0x2a7,0x2ad)]({'content':lang[_0x369a86(0x297,0x25c)][_0x369a86(0x27e,0x2a2)][_0x369a86(0x274,0x243)],'ephemeral':!![]});return;}_0x1a696d['balance']-=_0x124013,_0x3947af[_0x369a86(0x2db,0x2aa)](drawCard(_0x161516));while(calculateHand(_0x4d97de)<0x11){_0x4d97de[_0x369a86(0x270,0x2aa)](drawCard(_0x161516));}const _0x103e55=calculateHand(_0x3947af);function _0x369a86(_0xed5d6f,_0x12deda){return _0x58f00b(_0x12deda- -0x46,_0xed5d6f);}const _0x531de3=calculateHand(_0x4d97de),_0x5020cb=_0x103e55<=0x15&&(_0x531de3>0x15||_0x103e55>_0x531de3),_0x5b2ab5=_0x103e55===_0x531de3;await endBlackjackGame(_0x340940,_0x1a696d,_0x124013*0x2,_0x3947af,_0x4d97de,_0x5020cb,_0x5b2ab5,_0x466da9),await _0x1a696d[_0x369a86(0x2b4,0x2b0)]();}function createDeck(){const _0x45de96=['♠','♣','♥','♦'],_0x229741=['2','3','4','5','6','7','8','9','10','J','Q','K','A'],_0x33bcb2=[];for(const _0x2ac8ee of _0x45de96){for(const _0x4a6ba9 of _0x229741){_0x33bcb2['push']({'value':_0x4a6ba9,'suit':_0x2ac8ee});}}return shuffle(_0x33bcb2);}function drawCard(_0x2bf7c7){function _0x46fb5c(_0x524b46,_0xa417b7){return _0x58f00b(_0xa417b7- -0x176,_0x524b46);}return _0x2bf7c7[_0x46fb5c(0x15f,0x125)]();}function shuffle(_0x11f13a){for(let _0x3dfbcb=_0x11f13a[_0x1e374a(0x33b,0x342)]-0x1;_0x3dfbcb>0x0;_0x3dfbcb--){const _0x5a0e3e=Math['floor'](Math[_0x1e374a(0x2fe,0x313)]()*(_0x3dfbcb+0x1));[_0x11f13a[_0x3dfbcb],_0x11f13a[_0x5a0e3e]]=[_0x11f13a[_0x5a0e3e],_0x11f13a[_0x3dfbcb]];}function _0x1e374a(_0x10d1d1,_0x136ef2){return _0x58f00b(_0x10d1d1-0x44,_0x136ef2);}return _0x11f13a;}function calculateHand(_0x2909ef){let _0x378deb=0x0,_0x8e1c0a=0x0;for(const _0x3cb685 of _0x2909ef){if(_0x3cb685[_0x3bf15e(0x4ab,0x4ba)]==='A')_0x8e1c0a++,_0x378deb+=0xb;else['K','Q','J'][_0x3bf15e(0x4a9,0x4ae)](_0x3cb685[_0x3bf15e(0x49f,0x4ba)])?_0x378deb+=0xa:_0x378deb+=parseInt(_0x3cb685[_0x3bf15e(0x4dd,0x4ba)]);}while(_0x378deb>0x15&&_0x8e1c0a>0x0){_0x378deb-=0xa,_0x8e1c0a--;}function _0x3bf15e(_0x203d9b,_0x1120bc){return _0x58f00b(_0x1120bc-0x1f9,_0x203d9b);}return _0x378deb;}async function createBlackjackCanvas(_0xbdac2d,_0x5ed91b,_0x21a70a=![]){const _0x4bd7d6=0x64,_0x231c7e=0x96,_0x491a71=_0x4bd7d6*0x7;function _0x5f0e92(_0x4bf98d,_0x2fe6dc){return _0x58f00b(_0x4bf98d-0x118,_0x2fe6dc);}const _0x4da266=_0x231c7e*0x4,_0x1b5997=createCanvas(_0x491a71,_0x4da266),_0x25dda6=_0x1b5997[_0x5f0e92(0x3f4,0x405)]('2d');_0x25dda6[_0x5f0e92(0x3dc,0x3f7)]=_0x5f0e92(0x3c3,0x39d),_0x25dda6[_0x5f0e92(0x3eb,0x3e5)](0x0,0x0,_0x491a71,_0x4da266),_0x25dda6[_0x5f0e92(0x3b7,0x37f)]=_0x5f0e92(0x3ff,0x3ec),_0x25dda6[_0x5f0e92(0x413,0x44e)]=0xa,_0x25dda6[_0x5f0e92(0x3f9,0x410)](0x0,0x0,_0x491a71,_0x4da266),_0x25dda6[_0x5f0e92(0x3c7,0x39d)](),_0x25dda6['arc'](_0x491a71/0x2,_0x4da266,_0x4da266,Math['PI'],0x0),_0x25dda6[_0x5f0e92(0x3dc,0x3af)]=_0x5f0e92(0x3a0,0x3d0),_0x25dda6['fill'](),_0x25dda6[_0x5f0e92(0x3d1,0x3ef)](),_0x25dda6['fillStyle']=_0x5f0e92(0x3fb,0x3fe),_0x25dda6[_0x5f0e92(0x3e5,0x3d0)]='bold\x2030px\x20sans-serif',_0x25dda6[_0x5f0e92(0x3bc,0x39a)](_0x5f0e92(0x3db,0x3fd)+calculateHand(_0xbdac2d),_0x491a71/0x2-0x64,_0x4da266-0xc8),_0x25dda6[_0x5f0e92(0x3bc,0x3a7)](_0x5f0e92(0x3ad,0x3ea)+(_0x21a70a?'?':calculateHand(_0x5ed91b)),_0x491a71/0x2-0x64,0x32);for(let _0x4b018e=0x0;_0x4b018e<_0xbdac2d[_0x5f0e92(0x40f,0x3e7)];_0x4b018e++){const _0x3f13bd=_0xbdac2d[_0x4b018e],_0x39051a=_0x4b018e*(_0x4bd7d6+0x14)+(_0x491a71-_0xbdac2d[_0x5f0e92(0x40f,0x3e1)]*(_0x4bd7d6+0x14))/0x2,_0x20335e=_0x4da266-_0x231c7e-0x14;drawCardFace(_0x25dda6,_0x3f13bd[_0x5f0e92(0x3d9,0x3e8)],_0x3f13bd[_0x5f0e92(0x3e9,0x3bc)],_0x39051a,_0x20335e,_0x4bd7d6,_0x231c7e);}for(let _0x5b02a5=0x0;_0x5b02a5<_0x5ed91b['length'];_0x5b02a5++){const _0x440891=_0x5ed91b[_0x5b02a5],_0x520b41=_0x5b02a5*(_0x4bd7d6+0x14)+(_0x491a71-_0x5ed91b[_0x5f0e92(0x40f,0x41f)]*(_0x4bd7d6+0x14))/0x2,_0x163232=0x46;_0x21a70a&&_0x5b02a5===0x1?drawCardBack(_0x25dda6,_0x520b41,_0x163232,_0x4bd7d6,_0x231c7e):drawCardFace(_0x25dda6,_0x440891[_0x5f0e92(0x3d9,0x3dc)],_0x440891[_0x5f0e92(0x3e9,0x408)],_0x520b41,_0x163232,_0x4bd7d6,_0x231c7e);}return new AttachmentBuilder(_0x1b5997[_0x5f0e92(0x3ea,0x3c8)](),{'name':_0x5f0e92(0x3df,0x3c9)});}function drawCardFace(_0x1a81c7,_0x3767b0,_0x2023a4,_0x446825,_0x4d4713,_0x5a145f,_0x5708b9){_0x1a81c7[_0x95f72c(0x3b1,0x3b4)]=_0x95f72c(0x3d2,0x3d3),_0x1a81c7[_0x95f72c(0x3a2,0x3c3)](_0x446825,_0x4d4713,_0x5a145f,_0x5708b9),_0x1a81c7[_0x95f72c(0x3c8,0x38f)]='#000000',_0x1a81c7['lineWidth']=0x2,_0x1a81c7[_0x95f72c(0x3cb,0x3d1)](_0x446825,_0x4d4713,_0x5a145f,_0x5708b9),_0x1a81c7[_0x95f72c(0x396,0x3b4)]=_0x2023a4==='♠'||_0x2023a4==='♣'?'#000000':_0x95f72c(0x3c1,0x3e5),_0x1a81c7[_0x95f72c(0x393,0x3bd)]=_0x95f72c(0x3e1,0x3cb),_0x1a81c7['fillText'](_0x3767b0,_0x446825+0xa,_0x4d4713+0x1e),_0x1a81c7[_0x95f72c(0x3b1,0x394)](_0x2023a4,_0x446825+0xa,_0x4d4713+0x3c),_0x1a81c7[_0x95f72c(0x3d3,0x3bd)]=_0x95f72c(0x3d9,0x3b6);function _0x95f72c(_0x279ba2,_0x5b765a){return _0x58f00b(_0x5b765a-0xf0,_0x279ba2);}_0x1a81c7[_0x95f72c(0x3b1,0x394)](_0x2023a4,_0x446825+_0x5a145f/0x2-0xf,_0x4d4713+_0x5708b9/0x2+0x14);}function drawCardBack(_0x11ca8c,_0x188342,_0x1d0d46,_0x10f625,_0x331d27){_0x11ca8c[_0x389d82(-0x250,-0x270)]='#1E90FF',_0x11ca8c[_0x389d82(-0x241,-0x263)](_0x188342,_0x1d0d46,_0x10f625,_0x331d27),_0x11ca8c[_0x389d82(-0x275,-0x2a7)]=_0x389d82(-0x256,-0x263),_0x11ca8c[_0x389d82(-0x219,-0x1e4)]=0x2;function _0x389d82(_0x325cd7,_0x2071e0){return _0x58f00b(_0x325cd7- -0x514,_0x2071e0);}_0x11ca8c[_0x389d82(-0x233,-0x26a)](_0x188342,_0x1d0d46,_0x10f625,_0x331d27),_0x11ca8c['fillStyle']=_0x389d82(-0x231,-0x256),_0x11ca8c[_0x389d82(-0x247,-0x252)]=_0x389d82(-0x24e,-0x250),_0x11ca8c['fillText']('?',_0x188342+_0x10f625/0x2-0x14,_0x1d0d46+_0x331d27/0x2+0x14);}function createBlackjackButtons(_0x956055){function _0x4fc92c(_0x41e89d,_0x1ee504){return _0x58f00b(_0x1ee504- -0x1f,_0x41e89d);}return[new ActionRowBuilder()[_0x4fc92c(0x293,0x2a6)](new ButtonBuilder()[_0x4fc92c(0x275,0x268)](_0x4fc92c(0x2a9,0x26c))[_0x4fc92c(0x2cf,0x2b1)](_0x4fc92c(0x2cd,0x299))[_0x4fc92c(0x2fe,0x2c3)](ButtonStyle['Primary']),new ButtonBuilder()[_0x4fc92c(0x295,0x268)]('stand')['setLabel']('Stand')[_0x4fc92c(0x2bd,0x2c3)](ButtonStyle[_0x4fc92c(0x27a,0x292)]),new ButtonBuilder()[_0x4fc92c(0x267,0x268)](_0x4fc92c(0x2a2,0x28b))[_0x4fc92c(0x2d6,0x2b1)]('Double')[_0x4fc92c(0x2ec,0x2c3)](ButtonStyle[_0x4fc92c(0x23f,0x27b)])[_0x4fc92c(0x2ed,0x2d0)](!_0x956055))];}async function endBlackjackGame(_0xb8a821,_0x4206a9,_0x41d1d5,_0x5c8850,_0xb2d131,_0x280214,_0x5ef24e=![],_0x2c6a3e=null){function _0x82cb01(_0x2f4947,_0x546497){return _0x58f00b(_0x2f4947- -0x3de,_0x546497);}try{const _0x10b83f=config[_0x82cb01(-0x13c,-0x110)][_0x82cb01(-0x151,-0x133)][_0x82cb01(-0x121,-0xf8)],_0x426df8=_0x41d1d5;let _0xe5faf4=_0x41d1d5*_0x10b83f;const _0x20e27b=checkActiveBooster(_0x4206a9,'Money');_0xe5faf4*=_0x20e27b;let _0x48f5a6=0x0;if(_0x280214)_0x48f5a6=_0x41d1d5+_0x41d1d5*(_0x10b83f-0x1),_0x4206a9[_0x82cb01(-0x147,-0x137)]+=_0x48f5a6;else _0x5ef24e&&(_0x48f5a6=_0x41d1d5,_0x4206a9[_0x82cb01(-0x147,-0x17e)]+=_0x41d1d5);ensureUserSchema(_0x4206a9);const _0x4355b5=_0x280214?_0x82cb01(-0x145,-0x13b):_0x5ef24e?_0x82cb01(-0x107,-0x127):_0x82cb01(-0x104,-0xcd),_0x27d5f9=_0x280214?_0x48f5a6-_0x426df8:_0x5ef24e?0x0:_0x426df8;_0x4206a9[_0x82cb01(-0x14b,-0x14b)]['push']({'type':_0x4355b5,'amount':Math[_0x82cb01(-0x150,-0x16a)](_0x27d5f9),'timestamp':new Date()}),await _0x4206a9[_0x82cb01(-0xe8,-0xdb)]();const _0x4bd28a=await createBlackjackCanvas(_0x5c8850,_0xb2d131),{messageTemplates:_0x261f11,title:_0x16d11c,color:_0x4202ba}=getResultTemplates(_0x280214,_0x5ef24e),_0x59489c={'user':'<@'+_0xb8a821['user']['id']+'>','balance':_0x280214?_0x48f5a6:_0x426df8,'newBalance':_0x4206a9['balance']},_0x5af1f6=replacePlaceholders(_0x261f11[Math[_0x82cb01(-0x101,-0x111)](Math[_0x82cb01(-0x124,-0x139)]()*_0x261f11[_0x82cb01(-0xe7,-0xfe)])],_0x59489c),_0x125dc1=new EmbedBuilder()[_0x82cb01(-0x110,-0x101)](replacePlaceholders(lang[_0x82cb01(-0x13c,-0x112)]['Games'][_0x82cb01(-0x151,-0x127)][_0x82cb01(-0x11e,-0x140)],{'result':_0x16d11c}))[_0x82cb01(-0x137,-0x121)]({'text':replacePlaceholders(lang['Economy'][_0x82cb01(-0xf6,-0xdc)][_0x82cb01(-0x11c,-0x12b)],{'balance':_0x4206a9[_0x82cb01(-0x147,-0x173)]})})[_0x82cb01(-0xe1,-0x11b)](_0x4202ba)[_0x82cb01(-0x152,-0x142)](_0x5af1f6);_0x2c6a3e?await _0x2c6a3e[_0x82cb01(-0x135,-0x126)]({'embeds':[_0x125dc1],'files':[_0x4bd28a],'components':[]}):await _0xb8a821[_0x82cb01(-0x109,-0xd1)]({'embeds':[_0x125dc1],'files':[_0x4bd28a],'components':[]});}catch(_0x154ac5){console[_0x82cb01(-0xea,-0xc7)](_0x82cb01(-0x127,-0x144),_0x154ac5);if(!_0xb8a821[_0x82cb01(-0xe2,-0xb8)]&&!_0xb8a821['deferred'])await _0xb8a821[_0x82cb01(-0x109,-0x13f)]({'content':lang[_0x82cb01(-0x13c,-0x142)][_0x82cb01(-0xf6,-0xbb)][_0x82cb01(-0xea,-0xd3)],'ephemeral':!![]});else _0x2c6a3e&&await _0x2c6a3e[_0x82cb01(-0x135,-0x106)]({'content':lang['Economy']['Messages'][_0x82cb01(-0xea,-0xb0)],'components':[]});}}function getResultTemplates(_0x336c66,_0x8f98a2){function _0x29b35e(_0x2fe1eb,_0x51ec56){return _0x58f00b(_0x51ec56- -0x565,_0x2fe1eb);}if(_0x336c66)return{'messageTemplates':lang['Economy'][_0x29b35e(-0x2b1,-0x280)][_0x29b35e(-0x30e,-0x2d8)][_0x29b35e(-0x279,-0x2b2)],'title':_0x29b35e(-0x2d2,-0x2b2),'color':_0x29b35e(-0x294,-0x2b3)};else return _0x8f98a2?{'messageTemplates':lang[_0x29b35e(-0x2d8,-0x2c3)][_0x29b35e(-0x24c,-0x280)]['Blackjack'][_0x29b35e(-0x305,-0x2c9)],'title':_0x29b35e(-0x2ca,-0x2c9),'color':'#FFFF00'}:{'messageTemplates':lang[_0x29b35e(-0x2e6,-0x2c3)][_0x29b35e(-0x2a3,-0x280)]['Blackjack'][_0x29b35e(-0x2b5,-0x286)],'title':_0x29b35e(-0x27e,-0x286),'color':_0x29b35e(-0x248,-0x270)};}