const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ThreadUpdate,
    async execute(oldThread, newThread, client) {
        try {
            // Only handle name changes
            if (oldThread.name === newThread.name) return;
            
            console.log(`✏️ Thread name updated: ${oldThread.name} -> ${newThread.name} in ${newThread.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newThread.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'THREAD_NAME_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'THREAD_NAME_UPDATE',
                user: 'System',
                userId: null,
                action: 'Tên thread được thay đổi',
                details: `Tên thread đã được thay đổi từ **${oldThread.name}** thành **${newThread.name}**`,
                target: newThread.name,
                channel: newThread.parent?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the thread name from audit logs
            try {
                const auditLogs = await newThread.guild.fetchAuditLogs({
                    type: 111, // THREAD_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newThread.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Tên thread được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for thread name update');
            }
            
            // Add to database
            await client.db.addAuditLog(newThread.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Tên thread thay đổi',
                `Vừa có tên thread được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get thread type names
            const threadTypes = {
                10: 'Announcement Thread',
                11: 'Public Thread',
                12: 'Private Thread'
            };
            
            embed.addFields([
                {
                    name: '> ID thread',
                    value: `- ${newThread.id}`,
                    inline: true
                },
                {
                    name: '> Loại thread',
                    value: `- ${threadTypes[newThread.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh cha',
                    value: `- ${newThread.parent || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Tên cũ',
                    value: `- ${oldThread.name}`,
                    inline: true
                },
                {
                    name: '> Tên mới',
                    value: `- ${newThread.name}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Số thành viên',
                    value: `- ${newThread.memberCount || 0} thành viên`,
                    inline: true
                },
                {
                    name: '> Số tin nhắn',
                    value: `- ${newThread.messageCount || 0} tin nhắn`,
                    inline: true
                }
            ]);
            
            // Add thread owner info if available
            if (newThread.ownerId) {
                try {
                    const owner = await newThread.guild.members.fetch(newThread.ownerId);
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- ${owner.user.tag}`,
                        inline: true
                    });
                } catch (error) {
                    embed.addFields({
                        name: '> Chủ thread',
                        value: `- Unknown (ID: ${newThread.ownerId})`,
                        inline: true
                    });
                }
            }
            
            // Add thread link
            embed.addFields({
                name: '> Link thread',
                value: `- [Nhấn để xem](https://discord.com/channels/${newThread.guild.id}/${newThread.id})`,
                inline: true
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newThread.guild.name} • Thread Name Update`,
                iconURL: newThread.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Thread name update logged: ${oldThread.name} -> ${newThread.name}`);
            
        } catch (error) {
            console.error('Error in threadNameUpdate audit log:', error);
        }
    }
};
