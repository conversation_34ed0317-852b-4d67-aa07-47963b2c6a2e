const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.VoiceStateUpdate,
    async execute(oldState, newState, client) {
        try {
            // Only handle user switching voice channels (was in voice, now in different voice)
            if (!oldState.channelId || !newState.channelId || oldState.channelId === newState.channelId) return;
            
            console.log(`🔄 User switched voice: ${newState.member.user.tag} in ${newState.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newState.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'VOICE_USER_SWITCH')) return;
            
            // Create event data
            const eventData = {
                eventType: 'VOICE_USER_SWITCH',
                user: newState.member.user.tag,
                userId: newState.member.user.id,
                action: '<PERSON><PERSON><PERSON><PERSON> kênh thoại',
                details: `**${newState.member.user.tag}** đã chuyển từ **${oldState.channel.name}** sang **${newState.channel.name}**`,
                target: newState.member.user.tag,
                channel: newState.channel.name,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(newState.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔄 Chuyển kênh thoại',
                `Vừa có thành viên chuyển kênh thoại`
            );
            
            embed.setColor(0xf39c12); // Orange for switch
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${newState.member.user.tag} (${newState.member.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newState.member.user.id}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add channel information
            embed.addFields([
                {
                    name: '> Kênh cũ',
                    value: `- ${oldState.channel.name}`,
                    inline: true
                },
                {
                    name: '> Kênh mới',
                    value: `- ${newState.channel.name}`,
                    inline: true
                },
                {
                    name: '> ID kênh mới',
                    value: `- ${newState.channel.id}`,
                    inline: true
                }
            ]);
            
            // Add member counts
            embed.addFields([
                {
                    name: '> Số người trong kênh cũ',
                    value: `- ${oldState.channel.members.size} thành viên`,
                    inline: true
                },
                {
                    name: '> Số người trong kênh mới',
                    value: `- ${newState.channel.members.size} thành viên`,
                    inline: true
                }
            ]);
            
            // Add voice state info
            const voiceStates = [];
            if (newState.mute) voiceStates.push('🔇 Muted');
            if (newState.deaf) voiceStates.push('🔇 Deafened');
            if (newState.selfMute) voiceStates.push('🔇 Self Muted');
            if (newState.selfDeaf) voiceStates.push('🔇 Self Deafened');
            if (newState.streaming) voiceStates.push('📺 Streaming');
            if (newState.selfVideo) voiceStates.push('📹 Camera On');
            
            if (voiceStates.length > 0) {
                embed.addFields({
                    name: '> Trạng thái hiện tại',
                    value: `- ${voiceStates.join(', ')}`,
                    inline: false
                });
            }
            
            // Check if user was moved by someone else
            try {
                const auditLogs = await newState.guild.fetchAuditLogs({
                    type: 26, // MEMBER_MOVE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newState.member.user.id) {
                    const timeDiff = Date.now() - auditEntry.createdTimestamp;
                    if (timeDiff < 5000) { // Within 5 seconds
                        embed.addFields({
                            name: '> Được di chuyển bởi',
                            value: `- ${auditEntry.executor.tag}`,
                            inline: true
                        });
                        
                        eventData.action = `Được di chuyển bởi ${auditEntry.executor.tag}`;
                        eventData.user = auditEntry.executor.tag;
                        eventData.userId = auditEntry.executor.id;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for voice move');
            }
            
            // Set user avatar as thumbnail
            if (newState.member.user.displayAvatarURL()) {
                embed.setThumbnail(newState.member.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newState.guild.name} • Voice User Switch`,
                iconURL: newState.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Voice user switch logged: ${newState.member.user.tag}`);
            
        } catch (error) {
            console.error('Error in voiceUserSwitch audit log:', error);
        }
    }
};
