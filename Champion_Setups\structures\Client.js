const Discord = require("discord.js");
const yaml = require('js-yaml');
const fs = require("fs")

module.exports = class Client extends Discord.Client {
  constructor() {
    super({ intents: [Discord.GatewayIntentBits.Guilds, Discord.GatewayIntentBits.GuildMessages, Discord.GatewayIntentBits.GuildMembers, Discord.GatewayIntentBits.GuildPresences, Discord.GatewayIntentBits.MessageContent], 
      partials: [Discord.Partials.Message, Discord.Partials.Channel, Discord.Partials.Reaction, Discord.Partials.User, Discord.Partials.GuildMember]});
    // Files
    
    this.utils = require("../utils/utils.js");
    this.embedBuilder = require("../utils/embedBuilder.js");
   
    // End Of Files
    // Other //
    
    this.config = yaml.load(fs.readFileSync('./configs/config.yml', 'utf8'));
    this.embeds = yaml.load(fs.readFileSync('./configs/embeds.yml', 'utf8'));
    this.cmdConfig = yaml.load(fs.readFileSync('./configs/commands.yml', 'utf8'));
    this.bots = require("../data/bots.json");
    this.language = yaml.load(fs.readFileSync('./configs/language.yml', 'utf8'));
    this.template = (file) => {
      if(!fs.existsSync(`./configs/templates/${file}.yml`)) {
        return false;
      } else {
        return yaml.load(fs.readFileSync(`./configs/templates/${file}.yml`, 'utf8'));
      }
    };

    this.aliases = new Discord.Collection();
    this.commands = new Discord.Collection();
    this.slashCommands = new Discord.Collection();
    this.slashArray = [];
  }
  async login(token = this.token) {
    super.login(token);
  }
}
