const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

// Note: Discord doesn't have a direct kick event, so we detect kicks in userLeave.js
// This file is for manual kick logging or when we can specifically detect a kick

module.exports = {
    name: 'guildMemberKick', // Custom event name
    async execute(member, executor, reason, client) {
        try {
            console.log(`👢 User kicked: ${member.user.tag} in ${member.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(member.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'USER_KICK')) return;
            
            // Create event data
            const eventData = {
                eventType: 'USER_KICK',
                user: executor?.tag || 'System',
                userId: executor?.id || null,
                action: 'Thành viên bị kick',
                details: `**${member.user.tag}** đã bị kick khỏi server`,
                target: member.user.tag,
                reason: reason,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(member.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Calculate how long they were in the server
            const timeInServer = Date.now() - member.joinedTimestamp;
            const daysInServer = Math.floor(timeInServer / (1000 * 60 * 60 * 24));
            const hoursInServer = Math.floor((timeInServer % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            
            // Create embed
            const embed = createInfoEmbed(
                '👢 Thành viên bị kick',
                `Vừa có một thành viên bị kick khỏi server`
            );
            
            embed.setColor(0xe67e22); // Orange for kick
            
            embed.addFields([
                {
                    name: '> Thành viên bị kick',
                    value: `- ${member.user.tag} (${member.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${member.user.id}`,
                    inline: true
                },
                {
                    name: '> Người thực hiện kick',
                    value: `- ${executor ? `${executor.tag} (${executor})` : 'System'}`,
                    inline: true
                },
                {
                    name: '> Thời gian kick',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Tham gia lúc',
                    value: `- <t:${Math.floor(member.joinedTimestamp / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Thời gian trong server',
                    value: `- ${daysInServer} ngày, ${hoursInServer} giờ`,
                    inline: true
                },
                {
                    name: '> Tổng thành viên còn lại',
                    value: `- ${member.guild.memberCount} thành viên`,
                    inline: true
                }
            ]);
            
            // Add account info
            const accountAge = Date.now() - member.user.createdTimestamp;
            const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
            
            embed.addFields([
                {
                    name: '> Tài khoản tạo',
                    value: `- <t:${Math.floor(member.user.createdTimestamp / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                }
            ]);
            
            // Add kick reason
            if (reason) {
                embed.addFields({
                    name: '> Lý do kick',
                    value: `- ${reason}`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> Lý do kick',
                    value: `- Không có lý do`,
                    inline: false
                });
            }
            
            // Add roles the user had
            if (member.roles.cache.size > 1) { // > 1 because @everyone
                const rolesList = member.roles.cache
                    .filter(role => role.name !== '@everyone')
                    .map(role => role.name)
                    .join(', ');
                
                embed.addFields({
                    name: '> Roles đã có',
                    value: rolesList.length > 1000 ? rolesList.substring(0, 1000) + '...' : rolesList,
                    inline: false
                });
            }
            
            // Set user avatar as thumbnail
            if (member.user.displayAvatarURL()) {
                embed.setThumbnail(member.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${member.guild.name} • Member Kick`,
                iconURL: member.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ User kick logged: ${member.user.tag}`);
            
            // Smart alert for kick
            if (config.smartAlerts) {
                try {
                    const owner = await member.guild.fetchOwner();
                    const alertEmbed = createInfoEmbed(
                        '🚨 Smart Alert: Member Kick',
                        `Thành viên **${member.user.tag}** đã bị kick khỏi **${member.guild.name}**`
                    );
                    
                    alertEmbed.setColor(0xe67e22);
                    alertEmbed.addFields([
                        { name: '🎯 Loại', value: 'MEMBER_KICK', inline: true },
                        { name: '📊 Mức độ', value: 'MEDIUM', inline: true },
                        { name: '👤 Thực hiện bởi', value: executor?.tag || 'System', inline: true },
                        { name: '👢 Người bị kick', value: member.user.tag, inline: true },
                        { name: '📝 Lý do', value: reason || 'Không có', inline: true }
                    ]);
                    
                    await owner.send({ embeds: [alertEmbed] });
                } catch (dmError) {
                    console.log('Could not send kick alert to owner');
                }
            }
            
        } catch (error) {
            console.error('Error in userKick audit log:', error);
        }
    }
};
