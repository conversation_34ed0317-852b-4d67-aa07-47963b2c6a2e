const { Events, ActivityType } = require('discord.js');
const { deployCommands } = require('../utils/deployCommands');

module.exports = {
    name: Events.ClientReady,
    once: true,
    async execute(client) {
        console.log(`✅ Bot đã sẵn sàng! Đăng nhập với tên: ${client.user.tag}`);
        
        // Đặt status cho bot
        client.user.setActivity('ZuyAnkReal | /help', { 
            type: ActivityType.Playing 
        });

        // Deploy slash commands
        try {
            await deployCommands(client);
            console.log('✅ Đã deploy slash commands thành công');
        } catch (error) {
            console.error('❌ Lỗi deploy commands:', error);
        }

        // Log thông tin bot
        console.log(`📊 Bot đang hoạt động trên ${client.guilds.cache.size} server(s)`);
        console.log(`👥 Phục vụ ${client.users.cache.size} người dùng`);
    },
};
