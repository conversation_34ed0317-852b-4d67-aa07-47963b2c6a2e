const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'pollVoteRemove', // Custom event name
    async execute(pollVote, client) {
        try {
            console.log(`❌ Poll vote removed: ${pollVote.user?.tag} in ${pollVote.message?.guild?.name}`);
            
            // Skip if no guild or user
            if (!pollVote.message?.guild || !pollVote.user) return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(pollVote.message.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'POLLS_VOTES_REMOVE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'POLLS_VOTES_REMOVE',
                user: pollVote.user.tag,
                userId: pollVote.user.id,
                action: 'Vote được xóa khỏi poll',
                details: `**${pollVote.user.tag}** đã xóa vote trong một poll`,
                target: pollVote.message?.poll?.question?.text || 'Unknown',
                channel: pollVote.message?.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(pollVote.message.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '❌ Vote được xóa khỏi poll',
                `Vừa có vote được xóa khỏi poll trong server`
            );
            
            embed.setColor(0xe74c3c); // Red for vote remove
            
            embed.addFields([
                {
                    name: '> Người bỏ vote',
                    value: `- ${pollVote.user.tag} (${pollVote.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${pollVote.user.id}`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${pollVote.message?.channel || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Thời gian bỏ vote',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add poll question
            if (pollVote.message?.poll?.question?.text) {
                embed.addFields({
                    name: '> Câu hỏi poll',
                    value: `- ${pollVote.message.poll.question.text}`,
                    inline: false
                });
            }
            
            // Add vote details
            if (pollVote.answer) {
                const answerEmoji = pollVote.answer.emoji ? `${pollVote.answer.emoji} ` : '';
                embed.addFields([
                    {
                        name: '> Lựa chọn đã bỏ vote',
                        value: `- ${answerEmoji}${pollVote.answer.text || 'No text'}`,
                        inline: false
                    },
                    {
                        name: '> ID lựa chọn',
                        value: `- ${pollVote.answer.id || 'Unknown'}`,
                        inline: true
                    }
                ]);
                
                // Add current vote count for this answer
                if (pollVote.answer.voteCount !== undefined) {
                    embed.addFields({
                        name: '> Số votes hiện tại cho lựa chọn này',
                        value: `- ${pollVote.answer.voteCount} votes`,
                        inline: true
                    });
                }
            }
            
            // Add poll message info
            embed.addFields([
                {
                    name: '> ID tin nhắn poll',
                    value: `- ${pollVote.message?.id || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Link poll',
                    value: `- [Nhấn để xem poll](${pollVote.message?.url || '#'})`,
                    inline: true
                }
            ]);
            
            // Add poll creator info if available
            if (pollVote.message?.author) {
                embed.addFields({
                    name: '> Người tạo poll',
                    value: `- ${pollVote.message.author.tag}`,
                    inline: true
                });
            }
            
            // Add poll settings
            if (pollVote.message?.poll?.allowMultiselect !== undefined) {
                embed.addFields({
                    name: '> Cho phép chọn nhiều',
                    value: `- ${pollVote.message.poll.allowMultiselect ? 'Có' : 'Không'}`,
                    inline: true
                });
            }
            
            // Add poll status
            if (pollVote.message?.poll?.expiresTimestamp) {
                const isExpired = Date.now() > pollVote.message.poll.expiresTimestamp;
                embed.addFields([
                    {
                        name: '> Trạng thái poll',
                        value: `- ${isExpired ? '⏰ Đã kết thúc' : '🔄 Đang hoạt động'}`,
                        inline: true
                    },
                    {
                        name: '> Thời gian kết thúc',
                        value: `- <t:${Math.floor(pollVote.message.poll.expiresTimestamp / 1000)}:F>`,
                        inline: true
                    }
                ]);
            }
            
            // Add total votes if available
            if (pollVote.message?.poll?.answers) {
                const totalVotes = pollVote.message.poll.answers.reduce((total, answer) => total + (answer.voteCount || 0), 0);
                embed.addFields({
                    name: '> Tổng votes hiện tại',
                    value: `- ${totalVotes} votes`,
                    inline: true
                });
            }
            
            // Add reason for vote removal
            embed.addFields({
                name: '> 💡 Lý do có thể',
                value: [
                    '• Người dùng thay đổi ý kiến',
                    '• Chọn nhầm lựa chọn trước đó',
                    '• Muốn vote cho lựa chọn khác',
                    '• Không muốn tham gia poll nữa'
                ].join('\n'),
                inline: false
            });
            
            // Add user info
            const accountAge = Date.now() - pollVote.user.createdTimestamp;
            const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
            
            embed.addFields({
                name: '> Tuổi tài khoản',
                value: `- ${accountAgeDays} ngày`,
                inline: true
            });
            
            // Add note about vote changes
            embed.addFields({
                name: '> 📝 Lưu ý',
                value: `- Người dùng có thể thay đổi vote bất cứ lúc nào trước khi poll kết thúc`,
                inline: false
            });
            
            // Set user avatar as thumbnail
            if (pollVote.user.displayAvatarURL()) {
                embed.setThumbnail(pollVote.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${pollVote.message.guild.name} • Poll Vote Remove`,
                iconURL: pollVote.message.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Poll vote remove logged: ${pollVote.user.tag}`);
            
        } catch (error) {
            console.error('Error in pollsVotesRemove audit log:', error);
        }
    }
};
