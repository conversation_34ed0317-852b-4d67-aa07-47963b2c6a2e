const fs = require('fs');
const path = require('path');

// Ensure the data folder exists inside your bot project
const dataFolder = path.join(__dirname, '../data');
const filePath = path.join(dataFolder, 'giveaway.json');

// Create the data folder if it doesn't exist
if (!fs.existsSync(dataFolder)) {
  fs.mkdirSync(dataFolder, { recursive: true });
}

// Create giveaway.json if it doesn't exist
if (!fs.existsSync(filePath)) {
  fs.writeFileSync(filePath, '[]');
}

module.exports = async (client) => {
  setInterval(async () => {
    let giveaways;
    try {
      giveaways = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    } catch (err) {
      giveaways = [];
    }

    for (const gw of giveaways) {
      if (gw.ended || Date.now() < gw.endTime) continue;

      const guild = client.guilds.cache.get(gw.guildId);
      if (!guild) continue;

      const channel = guild.channels.cache.get(gw.channelId);
      if (!channel) continue;

      const msg = await channel.messages.fetch(gw.messageId).catch(() => null);
      if (!msg) continue;

      const reaction = msg.reactions.cache.get('🎉');
      if (!reaction) continue;

      const users = await reaction.users.fetch().catch(() => null);
      if (!users) continue;

      const participants = users.filter(u => !u.bot).map(u => u.id);
      if (!participants.length) {
        await msg.reply(`🎉 No valid entries for **${gw.prize}** giveaway.`);
      } else {
        const winners = [];
        const used = new Set();

        while (winners.length < gw.winners && used.size < participants.length) {
          const winner = participants[Math.floor(Math.random() * participants.length)];
          if (!used.has(winner)) {
            winners.push(`<@${winner}>`);
            used.add(winner);
          }
        }

        await msg.reply(`🎉 Congratulations ${winners.join(', ')}! You won **${gw.prize}**`);
      }

      gw.ended = true;
    }

    fs.writeFileSync(filePath, JSON.stringify(giveaways, null, 2));
  }, 5000);
};