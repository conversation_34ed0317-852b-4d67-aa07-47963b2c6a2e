const { SlashCommandBuilder } = require("discord.js");
const User = require("../models/User");
const {
  canWork,
  calculateWorkReward,
  getCooldownTime,
  formatCooldown,
} = require("../utils/economy");
const {
  createSuccessEmbed,
  createErrorEmbed,
  formatCurrency,
} = require("../utils/embedBuilder");
const config = require("../config/config");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("work")
    .setDescription("Làm việc để kiếm tiền"),

  cooldown: 5,

  async execute(interaction) {
    try {
      // Tạo hoặc lấy thông tin user
      const userData = await User.findOrCreate(interaction.user);

      // Kiểm tra cooldown
      if (!canWork(userData.lastWork)) {
        const cooldownSeconds = getCooldownTime(userData.lastWork, 1); // 1 giờ cooldown
        const timeLeft = formatCooldown(cooldownSeconds);

        const errorEmbed = createErrorEmbed(
          "Bạn đang nghỉ ngơi!",
          `Bạn cần nghỉ ngơi trước khi làm việc tiếp.\n\n` +
            `${config.emojis.info} **Thời gian còn lại:** ${timeLeft}\n` +
            `${config.emojis.warning} **Lưu ý:** Làm việc quá sức có thể ảnh hưởng đến sức khỏe!`
        );

        return await interaction.reply({
          embeds: [errorEmbed],
          ephemeral: true,
        });
      }

      // Tính toán phần thưởng work
      const workResult = calculateWorkReward();

      // Cập nhật số dư và thời gian work
      await userData.updateBalance(workResult.amount, "work");
      await userData.updateLastWork();

      // Tạo embed thành công
      const successEmbed = createSuccessEmbed(
        "Hoàn thành công việc!",
        `Bạn đã làm việc với vai trò **${
          workResult.job
        }** và kiếm được **${formatCurrency(workResult.amount)}**!\n\n` +
          `${config.emojis.money} **Số dư hiện tại:** ${formatCurrency(
            userData.balance + workResult.amount
          )}\n` +
          `${config.emojis.info} **Có thể làm việc lại:** <t:${Math.floor(
            (Date.now() + 3600000) / 1000
          )}:R>`
      );

      // Thêm bonus message ngẫu nhiên
      const bonusMessages = [
        "Sếp rất hài lòng với công việc của bạn!",
        "Bạn đã hoàn thành xuất sắc nhiệm vụ!",
        "Khách hàng đánh giá cao dịch vụ của bạn!",
        "Bạn được đồng nghiệp khen ngợi!",
        "Công việc hôm nay diễn ra rất suôn sẻ!",
      ];

      const randomMessage =
        bonusMessages[Math.floor(Math.random() * bonusMessages.length)];

      successEmbed.addFields({
        name: `${config.emojis.star} Đánh giá`,
        value: randomMessage,
        inline: false,
      });

      await interaction.reply({ embeds: [successEmbed] });
    } catch (error) {
      console.error("Lỗi lệnh work:", error);

      const errorEmbed = createErrorEmbed(
        "Lỗi hệ thống",
        "Không thể thực hiện công việc. Vui lòng thử lại sau."
      );

      await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  },
};
