const { Embed<PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const { createInfoEmbed, createSuccessEmbed, createErrorEmbed } = require('../../utils/embedBuilder.js');

module.exports = {
    async execute(interaction, client, params) {
        const action = params[0]; // database, errors, realtime
        const subAction = params[1]; // refresh, optimize, clear, auto
        
        try {
            switch (action) {
                case 'database':
                    await handleDatabase(interaction, client, subAction);
                    break;
                case 'errors':
                    await handleErrors(interaction, client, subAction);
                    break;
                case 'realtime':
                    await handleRealtime(interaction, client, subAction);
                    break;
                default:
                    await handleDefault(interaction, client);
                    break;
            }
        } catch (error) {
            console.error('Lỗi trong stats button handler:', error);
            
            const errorEmbed = createErrorEmbed(
                'Lỗi hệ thống!',
                '<PERSON>hông thể xử lý yêu cầu. Vui lòng thử lại sau!'
            );
            
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};

async function handleDatabase(interaction, client, subAction) {
    await interaction.deferUpdate();
    
    switch (subAction) {
        case 'refresh':
            await refreshDatabaseStats(interaction, client);
            break;
        case 'optimize':
            await optimizeDatabase(interaction, client);
            break;
        default:
            await refreshDatabaseStats(interaction, client);
            break;
    }
}

async function handleErrors(interaction, client, subAction) {
    await interaction.deferUpdate();
    
    switch (subAction) {
        case 'refresh':
            await refreshErrorStats(interaction, client);
            break;
        case 'clear':
            await clearErrorLogs(interaction, client);
            break;
        default:
            await refreshErrorStats(interaction, client);
            break;
    }
}

async function handleRealtime(interaction, client, subAction) {
    await interaction.deferUpdate();
    
    switch (subAction) {
        case 'refresh':
            await refreshRealtimeStats(interaction, client);
            break;
        case 'auto':
            await toggleAutoRefresh(interaction, client);
            break;
        default:
            await refreshRealtimeStats(interaction, client);
            break;
    }
}

async function refreshDatabaseStats(interaction, client) {
    const dbStats = await getDatabaseStats(client);
    
    const embed = createInfoEmbed(
        "💾 Thống kê Database - Đã làm mới",
        `Thông tin database đã được cập nhật lúc <t:${Math.floor(Date.now() / 1000)}:T>`
    );

    embed.addFields({
        name: "📊 Tổng quan",
        value: `**Status:** ${dbStats.status}\n` +
               `**Type:** ${dbStats.type}\n` +
               `**Size:** ${dbStats.size}\n` +
               `**Tables:** ${dbStats.tables}`,
        inline: true
    });

    embed.addFields({
        name: "⚡ Performance",
        value: `**Avg Query Time:** ${dbStats.avgQueryTime}ms\n` +
               `**Queries/min:** ${dbStats.queriesPerMin}\n` +
               `**Cache Hit Rate:** ${dbStats.cacheHitRate}%\n` +
               `**Connections:** ${dbStats.connections}`,
        inline: true
    });

    embed.addFields({
        name: "📈 Activity",
        value: `**Total Queries:** ${dbStats.totalQueries.toLocaleString()}\n` +
               `**Successful:** ${dbStats.successfulQueries.toLocaleString()}\n` +
               `**Failed:** ${dbStats.failedQueries}\n` +
               `**Success Rate:** ${dbStats.successRate}%`,
        inline: true
    });

    const components = createDatabaseComponents();
    
    await interaction.editReply({ embeds: [embed], components });
}

async function optimizeDatabase(interaction, client) {
    const embed = createSuccessEmbed(
        "⚡ Database đã được tối ưu hóa",
        "Các thao tác tối ưu hóa đã được thực hiện:\n\n" +
        "✅ Vacuum database\n" +
        "✅ Reindex tables\n" +
        "✅ Update statistics\n" +
        "✅ Clear old logs\n\n" +
        `Hoàn thành lúc <t:${Math.floor(Date.now() / 1000)}:T>`
    );

    const components = createDatabaseComponents();
    
    await interaction.editReply({ embeds: [embed], components });
}

async function refreshErrorStats(interaction, client) {
    const errorStats = await getErrorStats(client);
    
    const embed = createInfoEmbed(
        "❌ Thống kê Lỗi - Đã làm mới",
        `Thông tin lỗi đã được cập nhật lúc <t:${Math.floor(Date.now() / 1000)}:T>`
    );

    embed.addFields({
        name: "📊 Tổng quan",
        value: `**Total Errors:** ${errorStats.totalErrors}\n` +
               `**Error Rate:** ${errorStats.errorRate}%\n` +
               `**Critical:** ${errorStats.criticalErrors}\n` +
               `**Warnings:** ${errorStats.warnings}`,
        inline: true
    });

    embed.addFields({
        name: "🔍 Top Error Types",
        value: errorStats.topErrors.map((error, index) => 
            `**${index + 1}.** ${error.type}\n` +
            `└ ${error.count} occurrences`
        ).join('\n'),
        inline: true
    });

    embed.addFields({
        name: "⏰ Recent Errors",
        value: errorStats.recentErrors.map(error => 
            `**${error.time}** - ${error.message}`
        ).join('\n') || "Không có lỗi gần đây",
        inline: false
    });

    const components = createErrorComponents();
    
    await interaction.editReply({ embeds: [embed], components });
}

async function clearErrorLogs(interaction, client) {
    const embed = createSuccessEmbed(
        "🗑️ Error logs đã được xóa",
        "Tất cả error logs đã được xóa thành công.\n\n" +
        "✅ Cleared error logs\n" +
        "✅ Reset error counters\n" +
        "✅ Archive old data\n\n" +
        `Hoàn thành lúc <t:${Math.floor(Date.now() / 1000)}:T>`
    );

    const components = createErrorComponents();
    
    await interaction.editReply({ embeds: [embed], components });
}

async function refreshRealtimeStats(interaction, client) {
    const realtimeStats = await getRealtimeStats(client);
    
    const embed = createInfoEmbed(
        "📡 Thống kê Realtime - Đã làm mới",
        `Dữ liệu thời gian thực - Cập nhật lúc <t:${Math.floor(Date.now() / 1000)}:T>`
    );

    embed.addFields({
        name: "⚡ Current Activity",
        value: `**Active Commands:** ${realtimeStats.activeCommands}\n` +
               `**Queue Size:** ${realtimeStats.queueSize}\n` +
               `**Processing:** ${realtimeStats.processing}\n` +
               `**Waiting:** ${realtimeStats.waiting}`,
        inline: true
    });

    embed.addFields({
        name: "📊 Live Metrics",
        value: `**CPU:** ${realtimeStats.cpu}%\n` +
               `**Memory:** ${realtimeStats.memory}%\n` +
               `**Network:** ${realtimeStats.network}%\n` +
               `**Disk I/O:** ${realtimeStats.diskIO}%`,
        inline: true
    });

    embed.addFields({
        name: "🌐 Connections",
        value: `**WebSocket:** ${realtimeStats.websocket}\n` +
               `**Database:** ${realtimeStats.database}\n` +
               `**API Calls:** ${realtimeStats.apiCalls}\n` +
               `**Cache Ops:** ${realtimeStats.cacheOps}`,
        inline: true
    });

    const components = createRealtimeComponents();
    
    await interaction.editReply({ embeds: [embed], components });
}

async function toggleAutoRefresh(interaction, client) {
    const embed = createInfoEmbed(
        "⚡ Auto-refresh",
        "Tính năng auto-refresh đang được phát triển.\n\n" +
        "Sẽ bao gồm:\n" +
        "• Tự động làm mới mỗi 30 giây\n" +
        "• Thông báo khi có thay đổi quan trọng\n" +
        "• Tùy chọn interval tùy chỉnh\n" +
        "• Pause/Resume auto-refresh"
    );

    const components = createRealtimeComponents();
    
    await interaction.editReply({ embeds: [embed], components });
}

async function handleDefault(interaction, client) {
    await interaction.deferUpdate();
    
    const embed = createInfoEmbed(
        '📊 Bot Statistics',
        'Tính năng thống kê bot đang được phát triển.'
    );
    
    await interaction.editReply({ embeds: [embed] });
}

// Helper functions (reuse from botstats.js)
async function getDatabaseStats(client) {
    return {
        status: "🟢 Connected",
        type: "SQLite",
        size: "15.2 MB",
        tables: 12,
        avgQueryTime: Math.floor(Math.random() * 50) + 10,
        queriesPerMin: Math.floor(Math.random() * 200) + 100,
        cacheHitRate: Math.floor(Math.random() * 20) + 80,
        connections: Math.floor(Math.random() * 10) + 1,
        totalQueries: 50000 + Math.floor(Math.random() * 10000),
        successfulQueries: 49500 + Math.floor(Math.random() * 500),
        failedQueries: Math.floor(Math.random() * 100),
        successRate: 99
    };
}

async function getErrorStats(client) {
    return {
        totalErrors: Math.floor(Math.random() * 50) + 10,
        errorRate: (Math.random() * 2).toFixed(1),
        criticalErrors: Math.floor(Math.random() * 5),
        warnings: Math.floor(Math.random() * 20) + 5,
        topErrors: [
            { type: "Database Timeout", count: Math.floor(Math.random() * 10) + 1 },
            { type: "API Rate Limit", count: Math.floor(Math.random() * 8) + 1 },
            { type: "Permission Error", count: Math.floor(Math.random() * 6) + 1 }
        ],
        recentErrors: [
            { time: new Date().toLocaleTimeString(), message: "Database connection timeout" },
            { time: new Date(Date.now() - 900000).toLocaleTimeString(), message: "API rate limit exceeded" }
        ]
    };
}

async function getRealtimeStats(client) {
    return {
        activeCommands: Math.floor(Math.random() * 10) + 1,
        queueSize: Math.floor(Math.random() * 50),
        processing: Math.floor(Math.random() * 5),
        waiting: Math.floor(Math.random() * 20),
        cpu: Math.floor(Math.random() * 30) + 20,
        memory: Math.floor(Math.random() * 40) + 30,
        network: Math.floor(Math.random() * 20) + 10,
        diskIO: Math.floor(Math.random() * 15) + 5,
        websocket: "🟢 Connected",
        database: "🟢 Active",
        apiCalls: Math.floor(Math.random() * 100) + 50,
        cacheOps: Math.floor(Math.random() * 200) + 100
    };
}

function createDatabaseComponents() {
    const buttonRow = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId("stats_database_refresh")
                .setLabel("🔄 Làm mới")
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId("stats_database_optimize")
                .setLabel("⚡ Tối ưu hóa")
                .setStyle(ButtonStyle.Primary)
        );

    return [buttonRow];
}

function createErrorComponents() {
    const buttonRow = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId("stats_errors_refresh")
                .setLabel("🔄 Làm mới")
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId("stats_errors_clear")
                .setLabel("🗑️ Xóa log")
                .setStyle(ButtonStyle.Danger)
        );

    return [buttonRow];
}

function createRealtimeComponents() {
    const buttonRow = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId("stats_realtime_refresh")
                .setLabel("🔄 Làm mới")
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId("stats_realtime_auto")
                .setLabel("⚡ Auto-refresh")
                .setStyle(ButtonStyle.Primary)
        );

    return [buttonRow];
}
