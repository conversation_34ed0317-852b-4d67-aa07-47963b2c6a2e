const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.MessageUpdate,
    async execute(oldMessage, newMessage, client) {
        try {
            // Skip if message is from bot or no content change
            if (newMessage.author?.bot || oldMessage.content === newMessage.content) return;
            
            // Skip if message is partial
            if (oldMessage.partial || newMessage.partial) return;
            
            console.log(`✏️ Message edited: ${newMessage.author?.tag} in ${newMessage.guild?.name}`);
            
            // Skip if no guild
            if (!newMessage.guild) return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newMessage.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'MESSAGES_EDIT')) return;
            
            // Create event data
            const eventData = {
                eventType: 'MESSAGES_EDIT',
                user: newMessage.author?.tag || 'Unknown',
                userId: newMessage.author?.id || null,
                action: 'Tin nhắn được chỉnh sửa',
                details: `**${newMessage.author?.tag || 'Unknown'}** đã chỉnh sửa tin nhắn`,
                target: newMessage.id,
                channel: newMessage.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(newMessage.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Tin nhắn được chỉnh sửa',
                `Vừa có tin nhắn được chỉnh sửa trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for edit
            
            embed.addFields([
                {
                    name: '> Người chỉnh sửa',
                    value: `- ${newMessage.author?.tag || 'Unknown'} (${newMessage.author || 'Unknown'})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newMessage.author?.id || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${newMessage.channel}`,
                    inline: true
                },
                {
                    name: '> ID tin nhắn',
                    value: `- ${newMessage.id}`,
                    inline: true
                },
                {
                    name: '> Thời gian chỉnh sửa',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add original message time
            if (newMessage.createdTimestamp) {
                embed.addFields({
                    name: '> Tin nhắn gốc gửi lúc',
                    value: `- <t:${Math.floor(newMessage.createdTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add content comparison
            const oldContent = oldMessage.content || 'Không có nội dung';
            const newContent = newMessage.content || 'Không có nội dung';
            
            // Truncate content if too long
            const maxLength = 500;
            const truncatedOldContent = oldContent.length > maxLength 
                ? oldContent.substring(0, maxLength) + '...' 
                : oldContent;
            const truncatedNewContent = newContent.length > maxLength 
                ? newContent.substring(0, maxLength) + '...' 
                : newContent;
            
            embed.addFields([
                {
                    name: '> Nội dung cũ',
                    value: `\`\`\`${truncatedOldContent}\`\`\``,
                    inline: false
                },
                {
                    name: '> Nội dung mới',
                    value: `\`\`\`${truncatedNewContent}\`\`\``,
                    inline: false
                }
            ]);
            
            // Add attachments info if changed
            if (oldMessage.attachments.size !== newMessage.attachments.size) {
                embed.addFields([
                    {
                        name: '> File đính kèm cũ',
                        value: `- ${oldMessage.attachments.size} files`,
                        inline: true
                    },
                    {
                        name: '> File đính kèm mới',
                        value: `- ${newMessage.attachments.size} files`,
                        inline: true
                    }
                ]);
            }
            
            // Add embeds info if changed
            if (oldMessage.embeds.length !== newMessage.embeds.length) {
                embed.addFields([
                    {
                        name: '> Embeds cũ',
                        value: `- ${oldMessage.embeds.length} embeds`,
                        inline: true
                    },
                    {
                        name: '> Embeds mới',
                        value: `- ${newMessage.embeds.length} embeds`,
                        inline: true
                    }
                ]);
            }
            
            // Add message link
            embed.addFields({
                name: '> Link tin nhắn',
                value: `- [Nhấn để xem tin nhắn](${newMessage.url})`,
                inline: true
            });
            
            // Add user info
            if (newMessage.author) {
                const accountAge = Date.now() - newMessage.author.createdTimestamp;
                const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
                
                embed.addFields({
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                });
                
                // Add member info if available
                const member = newMessage.guild.members.cache.get(newMessage.author.id);
                if (member) {
                    embed.addFields({
                        name: '> Tham gia server lúc',
                        value: `- <t:${Math.floor(member.joinedTimestamp / 1000)}:F>`,
                        inline: true
                    });
                    
                    const roleCount = member.roles.cache.size - 1; // -1 for @everyone
                    if (roleCount > 0) {
                        embed.addFields({
                            name: '> Số roles',
                            value: `- ${roleCount} roles`,
                            inline: true
                        });
                    }
                }
            }
            
            // Add edit statistics
            const editCount = newMessage.editedTimestamp ? 1 : 0; // Discord doesn't track edit count
            embed.addFields({
                name: '> Lần chỉnh sửa',
                value: `- Ít nhất ${editCount + 1} lần`,
                inline: true
            });
            
            // Add content analysis
            const contentChanges = [];
            if (oldContent.length !== newContent.length) {
                const lengthDiff = newContent.length - oldContent.length;
                contentChanges.push(`Độ dài: ${lengthDiff > 0 ? '+' : ''}${lengthDiff} ký tự`);
            }
            
            if (contentChanges.length > 0) {
                embed.addFields({
                    name: '> Thay đổi',
                    value: `- ${contentChanges.join('\n- ')}`,
                    inline: false
                });
            }
            
            // Set user avatar as thumbnail
            if (newMessage.author?.displayAvatarURL()) {
                embed.setThumbnail(newMessage.author.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newMessage.guild.name} • Message Edit`,
                iconURL: newMessage.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Message edit logged: ${newMessage.author?.tag}`);
            
        } catch (error) {
            console.error('Error in messagesEdit audit log:', error);
        }
    }
};
