    const { SlashCommandBuilder, ChannelType, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { canManageChannel } = require('../../utils/permissions.js');
const { isValidChannelName } = require('../../utils/validators.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('channel-create')
        .setDescription('Tạo kênh mới với các tùy chọn nâng cao')
        .addSubcommand(subcommand =>
            subcommand
                .setName('text')
                .setDescription('Tạo kênh text mới')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('Tên kênh (chỉ chữ thường, số, dấu gạch)')
                        .setRequired(true)
                )
                .addChannelOption(option =>
                    option.setName('category')
                        .setDescription('Danh mục chứa kênh')
                        .setRequired(false)
                        .addChannelTypes(ChannelType.GuildCategory)
                )
                .addStringOption(option =>
                    option.setName('topic')
                        .setDescription('Chủ đề kênh')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('nsfw')
                        .setDescription('Kênh NSFW (18+)')
                        .setRequired(false)
                )
                .addIntegerOption(option =>
                    option.setName('slowmode')
                        .setDescription('Chế độ chậm (giây, 0-21600)')
                        .setRequired(false)
                        .setMinValue(0)
                        .setMaxValue(21600)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('voice')
                .setDescription('Tạo kênh voice mới')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('Tên kênh voice')
                        .setRequired(true)
                )
                .addChannelOption(option =>
                    option.setName('category')
                        .setDescription('Danh mục chứa kênh')
                        .setRequired(false)
                        .addChannelTypes(ChannelType.GuildCategory)
                )
                .addIntegerOption(option =>
                    option.setName('userlimit')
                        .setDescription('Giới hạn người dùng (0 = không giới hạn)')
                        .setRequired(false)
                        .setMinValue(0)
                        .setMaxValue(99)
                )
                .addIntegerOption(option =>
                    option.setName('bitrate')
                        .setDescription('Chất lượng âm thanh (kbps, 8-384)')
                        .setRequired(false)
                        .setMinValue(8)
                        .setMaxValue(384)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('category')
                .setDescription('Tạo danh mục kênh mới')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('Tên danh mục')
                        .setRequired(true)
                )
        ),
    category: 'channels',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();
        
        // Kiểm tra quyền bot
        const canManage = canManageChannel(interaction.guild);
        if (!canManage.canManage) {
            const errorEmbed = createErrorEmbed(
                'Bot không có quyền!',
                canManage.reason
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        switch (subcommand) {
            case 'text':
                await handleTextChannel(interaction, client);
                break;
            case 'voice':
                await handleVoiceChannel(interaction, client);
                break;
            case 'category':
                await handleCategory(interaction, client);
                break;
        }
    },
};

async function handleTextChannel(interaction, client) {
    const name = interaction.options.getString('name');
    const category = interaction.options.getChannel('category');
    const topic = interaction.options.getString('topic');
    const nsfw = interaction.options.getBoolean('nsfw') || false;
    const slowmode = interaction.options.getInteger('slowmode') || 0;
    
    // Validate channel name
    if (!isValidChannelName(name)) {
        const errorEmbed = createErrorEmbed(
            'Tên kênh không hợp lệ!',
            'Tên kênh chỉ được chứa:\n• Chữ thường (a-z)\n• Số (0-9)\n• Dấu gạch ngang (-)\n• Dấu gạch dưới (_)\n• Độ dài: 1-100 ký tự'
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    // Check if channel already exists
    const existingChannel = interaction.guild.channels.cache.find(ch => ch.name === name);
    if (existingChannel) {
        const errorEmbed = createErrorEmbed(
            'Kênh đã tồn tại!',
            `Kênh **${name}** đã tồn tại trong server.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    try {
        const channelOptions = {
            name: name,
            type: ChannelType.GuildText,
            parent: category?.id || null,
            topic: topic || null,
            nsfw: nsfw,
            rateLimitPerUser: slowmode
        };
        
        const newChannel = await interaction.guild.channels.create(channelOptions);
        
        const successEmbed = createSuccessEmbed(
            'Đã tạo kênh text!',
            `**Kênh:** ${newChannel}\n` +
            `**Danh mục:** ${category ? category.name : 'Không có'}\n` +
            `**Chủ đề:** ${topic || 'Không có'}\n` +
            `**NSFW:** ${nsfw ? 'Có' : 'Không'}\n` +
            `**Slowmode:** ${slowmode > 0 ? `${slowmode} giây` : 'Tắt'}`
        );
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi tạo text channel:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi tạo kênh!',
            'Không thể tạo kênh text. Vui lòng kiểm tra quyền bot và thử lại!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleVoiceChannel(interaction, client) {
    const name = interaction.options.getString('name');
    const category = interaction.options.getChannel('category');
    const userLimit = interaction.options.getInteger('userlimit') || 0;
    const bitrate = interaction.options.getInteger('bitrate') || 64;
    
    // Check if channel already exists
    const existingChannel = interaction.guild.channels.cache.find(ch => ch.name === name);
    if (existingChannel) {
        const errorEmbed = createErrorEmbed(
            'Kênh đã tồn tại!',
            `Kênh **${name}** đã tồn tại trong server.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    try {
        const channelOptions = {
            name: name,
            type: ChannelType.GuildVoice,
            parent: category?.id || null,
            userLimit: userLimit,
            bitrate: bitrate * 1000 // Convert to bits
        };
        
        const newChannel = await interaction.guild.channels.create(channelOptions);
        
        const successEmbed = createSuccessEmbed(
            'Đã tạo kênh voice!',
            `**Kênh:** ${newChannel.name}\n` +
            `**Danh mục:** ${category ? category.name : 'Không có'}\n` +
            `**Giới hạn người dùng:** ${userLimit > 0 ? `${userLimit} người` : 'Không giới hạn'}\n` +
            `**Chất lượng âm thanh:** ${bitrate} kbps`
        );
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi tạo voice channel:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi tạo kênh!',
            'Không thể tạo kênh voice. Vui lòng kiểm tra quyền bot và thử lại!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleCategory(interaction, client) {
    const name = interaction.options.getString('name');
    
    // Check if category already exists
    const existingCategory = interaction.guild.channels.cache.find(ch => 
        ch.type === ChannelType.GuildCategory && ch.name === name
    );
    if (existingCategory) {
        const errorEmbed = createErrorEmbed(
            'Danh mục đã tồn tại!',
            `Danh mục **${name}** đã tồn tại trong server.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    try {
        const newCategory = await interaction.guild.channels.create({
            name: name,
            type: ChannelType.GuildCategory
        });
        
        const successEmbed = createSuccessEmbed(
            'Đã tạo danh mục!',
            `**Danh mục:** ${newCategory.name}\n\n` +
            `Bây giờ bạn có thể tạo kênh trong danh mục này bằng cách chọn danh mục khi tạo kênh mới.`
        );
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi tạo category:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi tạo danh mục!',
            'Không thể tạo danh mục. Vui lòng kiểm tra quyền bot và thử lại!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}
