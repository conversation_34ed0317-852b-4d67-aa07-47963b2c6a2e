(function(_0x3c561d,_0x5498fa){function _0x426970(_0x4b52b3,_0xb1b876){return _0x34a7(_0xb1b876- -0xf1,_0x4b52b3);}const _0x2cb5c7=_0x3c561d();while(!![]){try{const _0x17c052=parseInt(_0x426970(0xdb,0xe3))/0x1+parseInt(_0x426970(0x150,0x13a))/0x2*(-parseInt(_0x426970(0x132,0x13b))/0x3)+-parseInt(_0x426970(0x104,0x12f))/0x4+parseInt(_0x426970(0xda,0x10f))/0x5+-parseInt(_0x426970(0x129,0x134))/0x6+parseInt(_0x426970(0xe0,0xdc))/0x7+parseInt(_0x426970(0x168,0x130))/0x8*(parseInt(_0x426970(0x13f,0x137))/0x9);if(_0x17c052===_0x5498fa)break;else _0x2cb5c7['push'](_0x2cb5c7['shift']());}catch(_0x204b9d){_0x2cb5c7['push'](_0x2cb5c7['shift']());}}}(_0x58f5,0x3eadf));const {SlashCommandBuilder,ActionRowBuilder,ButtonBuilder,ButtonStyle,EmbedBuilder,AttachmentBuilder}=require(_0x1f5f5a(-0xe1,-0xe9)),{createCanvas}=require('canvas'),fs=require('fs'),yaml=require(_0x1f5f5a(-0x103,-0x100)),lang=yaml['load'](fs[_0x1f5f5a(-0x129,-0x10a)](_0x1f5f5a(-0x13b,-0x12d),_0x1f5f5a(-0xdd,-0xbc))),config=yaml['load'](fs['readFileSync'](_0x1f5f5a(-0xfd,-0x109),_0x1f5f5a(-0xdd,-0xed)));module[_0x1f5f5a(-0x121,-0x147)]={'data':new SlashCommandBuilder()[_0x1f5f5a(-0x11d,-0x132)](_0x1f5f5a(-0x100,-0xe1))['setDescription']('Play\x20Tic-Tac-Toe\x20against\x20a\x20bot\x20or\x20another\x20player!')[_0x1f5f5a(-0xd9,-0xde)](_0x41169b=>_0x41169b[_0x1f5f5a(-0x11d,-0x154)]('type')['setDescription'](_0x1f5f5a(-0x13a,-0x12e))[_0x1f5f5a(-0xe6,-0xf9)](!![])[_0x1f5f5a(-0x136,-0x153)]({'name':'Bot','value':_0x1f5f5a(-0xcd,-0xee)},{'name':_0x1f5f5a(-0xec,-0x102),'value':_0x1f5f5a(-0x135,-0x15d)}))['addStringOption'](_0x5c167e=>_0x5c167e[_0x1f5f5a(-0x11d,-0x124)]('difficulty')[_0x1f5f5a(-0x10e,-0x12e)](_0x1f5f5a(-0xeb,-0xbd))[_0x1f5f5a(-0x136,-0x164)]({'name':_0x1f5f5a(-0x115,-0x10d),'value':_0x1f5f5a(-0x10c,-0x12e)},{'name':_0x1f5f5a(-0x134,-0x153),'value':_0x1f5f5a(-0x130,-0x153)},{'name':_0x1f5f5a(-0x118,-0x11f),'value':_0x1f5f5a(-0xf7,-0x113)}))[_0x1f5f5a(-0x133,-0xfc)](_0x2669ce=>_0x2669ce[_0x1f5f5a(-0x11d,-0x12b)](_0x1f5f5a(-0x105,-0x117))[_0x1f5f5a(-0x10e,-0x130)](_0x1f5f5a(-0xf5,-0x107)))[_0x1f5f5a(-0xf3,-0x10f)](![]),'category':_0x1f5f5a(-0x114,-0x121),async 'execute'(_0xdc9443){const _0xd04d64=_0xdc9443['options'][_0x1c826b(0x1f,0x1a)](_0x1c826b(0x16,0x22)),_0x23f2be=_0xdc9443['options']['getUser'](_0x1c826b(0x13,-0x1a)),_0x57fd94=_0xdc9443['options'][_0x1c826b(0x1f,0x21)]('difficulty')||_0x1c826b(-0x18,0xb);function _0x1c826b(_0x28d830,_0x18f08a){return _0x1f5f5a(_0x28d830-0x118,_0x18f08a);}const _0x5d2736={'x':lang[_0x1c826b(-0x6,-0x2e)][_0x1c826b(-0x7,-0x2b)]['Emojis']['X'],'o':lang[_0x1c826b(-0x6,-0x2a)][_0x1c826b(-0x7,-0x1a)][_0x1c826b(-0x4,-0x17)]['O'],'blank':lang[_0x1c826b(-0x6,0x4)][_0x1c826b(-0x7,-0x26)][_0x1c826b(-0x4,-0x11)][_0x1c826b(0x3e,0x25)]},_0x5e4ca0=_0x1c826b(0xf,-0x1c)+Date[_0x1c826b(0x43,0x68)]()+'-'+_0xdc9443[_0x1c826b(0x3a,0x36)]['id'];if(_0xd04d64===_0x1c826b(-0x1d,0x11)&&!_0x23f2be)return _0xdc9443[_0x1c826b(-0xa,-0x24)]({'content':lang[_0x1c826b(-0x6,0x16)]['Messages'][_0x1c826b(-0x15,-0x1f)],'ephemeral':!![]});await startGame(_0xdc9443,_0x5e4ca0,_0x5d2736,_0xd04d64===_0x1c826b(0x4b,0x23),_0x23f2be,_0x57fd94);}};async function startGame(_0x144ca8,_0x3fe3c7,_0x599dd5,_0x3aa8ab,_0x228f93,_0x210e3b){const _0x1e76c6=Array(0x3)[_0x55a434(-0x7c,-0x9a)]()['map'](()=>Array(0x3)[_0x55a434(-0xc7,-0x9a)](_0x599dd5[_0x55a434(-0x6e,-0x82)])),_0x5bc994=_0x144ca8[_0x55a434(-0x71,-0x5f)]['id'],_0x42ddad=await createGameBoardCanvas(_0x1e76c6,_0x599dd5);await _0x144ca8[_0x55a434(-0xc5,-0xa3)]({'content':createGameMessage(_0x144ca8[_0x55a434(-0x95,-0x5f)][_0x55a434(-0xb6,-0x9b)],_0x1e76c6,_0x5bc994,_0x144ca8[_0x55a434(-0x28,-0x5f)],_0x228f93),'files':[_0x42ddad],'components':createBoardComponents(_0x1e76c6,_0x599dd5,_0x3fe3c7)});const _0x166f3e=await _0x144ca8[_0x55a434(-0x7f,-0x69)](),_0x450eeb=_0x166f3e['id'];function _0x55a434(_0x1a0960,_0x5044b1){return _0x1f5f5a(_0x5044b1-0x7f,_0x1a0960);}const _0x22a7c6=_0x144ca8[_0x55a434(-0x71,-0x91)]['createMessageComponentCollector']({'componentType':0x2,'time':0xea60*0x5,'filter':_0xeaabbe=>_0xeaabbe[_0x55a434(-0x5f,-0x79)][_0x55a434(-0x5f,-0x6f)](_0x3fe3c7)&&(_0xeaabbe[_0x55a434(-0x81,-0x5f)]['id']===_0x5bc994||!_0x3aa8ab&&_0xeaabbe[_0x55a434(-0x69,-0x5f)]['id']===_0x228f93['id'])});let _0x373efd=_0x5bc994,_0x2212d6=_0x3aa8ab?null:_0x228f93['id'];_0x22a7c6['on'](_0x55a434(-0xb6,-0x85),async _0x5fd27=>{if(_0x5fd27[_0x54360b(0x8c,0xa6)]['id']!==_0x373efd)return _0x5fd27['reply']({'content':'It\x20is\x20not\x20your\x20turn!','ephemeral':!![]});const _0x537b61=_0x5fd27[_0x54360b(0x72,0x91)][_0x54360b(0x31,0x31)]('-');function _0x54360b(_0x38f749,_0x1a03c0){return _0x55a434(_0x1a03c0,_0x38f749-0xeb);}const _0x10dbce=parseInt(_0x537b61[0x3],0xa),_0x45499a=parseInt(_0x537b61[0x4],0xa);if(typeof _0x1e76c6[_0x10dbce]===_0x54360b(0x8a,0x9c)||typeof _0x1e76c6[_0x10dbce][_0x45499a]==='undefined')return;const _0x510675=_0x373efd===_0x144ca8[_0x54360b(0x8c,0x98)]['id']?_0x599dd5['x']:_0x599dd5['o'];_0x1e76c6[_0x10dbce][_0x45499a]=_0x510675;const _0x3eae1c=checkWin(_0x1e76c6,_0x510675);if(_0x3eae1c){await endGame(_0x144ca8,_0x5fd27[_0x54360b(0x6b,0x67)],_0x5fd27[_0x54360b(0x8c,0x92)][_0x54360b(0x50,0x6d)]+_0x54360b(0x58,0x3d),_0x1e76c6,_0x599dd5,_0x3eae1c),_0x22a7c6[_0x54360b(0x63,0x70)]();return;}else{if(isBoardFull(_0x1e76c6,_0x599dd5['blank'])){await endGame(_0x144ca8,_0x5fd27['message'],_0x54360b(0x70,0x74),_0x1e76c6,_0x599dd5),_0x22a7c6[_0x54360b(0x63,0x55)]();return;}}if(_0x3aa8ab){smarterBotMove(_0x1e76c6,_0x599dd5,_0x210e3b);const _0x4a1850=checkWin(_0x1e76c6,_0x599dd5['o']);if(_0x4a1850){await endGame(_0x144ca8,_0x5fd27['message'],_0x54360b(0x87,0x54),_0x1e76c6,_0x599dd5,_0x4a1850),_0x22a7c6[_0x54360b(0x63,0x41)]();return;}else{if(isBoardFull(_0x1e76c6,_0x599dd5['blank'])){await endGame(_0x144ca8,_0x5fd27[_0x54360b(0x6b,0x5d)],_0x54360b(0x70,0x76),_0x1e76c6,_0x599dd5),_0x22a7c6[_0x54360b(0x63,0x32)]();return;}}}else[_0x373efd,_0x2212d6]=[_0x2212d6,_0x373efd];const _0xf7065d=await createGameBoardCanvas(_0x1e76c6,_0x599dd5);await _0x5fd27[_0x54360b(0x2e,0x51)]({'content':createGameMessage(_0x144ca8[_0x54360b(0x8c,0xb1)][_0x54360b(0x50,0x5c)],_0x1e76c6,_0x373efd,_0x144ca8[_0x54360b(0x8c,0x8d)],_0x228f93),'files':[_0xf7065d],'components':createBoardComponents(_0x1e76c6,_0x599dd5,_0x3fe3c7)});});}function _0x58f5(){const _0x4e34dd=['8aiSFHA','Blank','addStringOption','error','2033472dNMAsS','replace','now','1468485ErkEdy','setStyle','lineTo','97294UmFJhb','21evSUKH','setCustomId','##\x20**Sorry\x20{user},\x20you\x20lost!**','bot','update','./lang.yml','Choose\x20game\x20type','split','Secondary','Colors','addChoices','player','Medium','addUserOption','every','map','medium','689010fSpwCx','filter','OwnGame','moveTo','ThanksForPlaying','##\x20**Well\x20done\x20{user}!\x20You\x20won!**','readFileSync','289922yuOJtU','width','includes','setDisabled','\x27s\x20turn!','lineWidth','reply','exports','setEmoji','Board','TicTacToe','setName','Emojis','random','username','fill','Hard','arc','floor','Easy','Fun','height','\x20has\x20won!','Win','channel','strokeStyle','setDescription','GameOver','easy','catch','Messages','ttt-','stroke','stop','Lose','opponent','collect','js-yaml','type','blank','tictactoe','message','{user}','./config.yml','2058760GgcjCT','It\x27s\x20','It\x27s\x20a\x20draw!','getString','customId','hard','Tie','Choose\x20an\x20opponent\x20(required\x20if\x20playing\x20against\x20another\x20player)','Lost','setDMPermission','tic-tac-toe.png','length','fillStyle','##\x20**It\x27s\x20a\x20tie\x20{user}!**','startsWith','#FFD700','Player','Choose\x20difficulty\x20level\x20(only\x20for\x20bot)','setTitle','setColor','fetchReply','has\x20won!','setRequired','edit','displayAvatarURL','Bot\x20has\x20won!','#2C2F33','discord.js','undefined','beginPath','user','utf8','108372qBKLWz'];_0x58f5=function(){return _0x4e34dd;};return _0x58f5();}async function createGameBoardCanvas(_0x3fad78,_0x2bda5,_0x21625e=null){const _0x3cf4f2=createCanvas(0x12c,0x12c),_0x47b994=_0x3cf4f2['getContext']('2d');_0x47b994[_0x4d6c58(0x4cb,0x4eb)]=_0x4d6c58(0x4ff,0x4f9),_0x47b994['fillRect'](0x0,0x0,_0x3cf4f2[_0x4d6c58(0x4ba,0x4b4)],_0x3cf4f2[_0x4d6c58(0x4ec,0x4c8)]),_0x47b994['strokeStyle']=_0x4d6c58(0x4e1,0x4ee),_0x47b994[_0x4d6c58(0x4a2,0x4b8)]=0xa,_0x47b994[_0x4d6c58(0x4f5,0x4fc)](),_0x47b994[_0x4d6c58(0x480,0x4af)](0x64,0x0),_0x47b994['lineTo'](0x64,0x12c),_0x47b994[_0x4d6c58(0x4ac,0x4af)](0xc8,0x0),_0x47b994[_0x4d6c58(0x512,0x509)](0xc8,0x12c),_0x47b994[_0x4d6c58(0x489,0x4af)](0x0,0x64),_0x47b994['lineTo'](0x12c,0x64),_0x47b994[_0x4d6c58(0x4a8,0x4af)](0x0,0xc8),_0x47b994[_0x4d6c58(0x4da,0x509)](0x12c,0xc8),_0x47b994[_0x4d6c58(0x49e,0x4d3)]();function _0x4d6c58(_0x59cb0f,_0x284100){return _0x1f5f5a(_0x284100-0x5db,_0x59cb0f);}for(let _0x4cc43b=0x0;_0x4cc43b<0x3;_0x4cc43b++){for(let _0x480a61=0x0;_0x480a61<0x3;_0x480a61++){const _0x4bae46=_0x480a61*0x64,_0x22770d=_0x4cc43b*0x64;if(_0x3fad78[_0x4cc43b][_0x480a61]===_0x2bda5['x'])drawX(_0x47b994,_0x4bae46,_0x22770d);else _0x3fad78[_0x4cc43b][_0x480a61]===_0x2bda5['o']&&drawO(_0x47b994,_0x4bae46,_0x22770d);}}if(_0x21625e){_0x47b994[_0x4d6c58(0x4aa,0x4cc)]='#FFFF00',_0x47b994[_0x4d6c58(0x486,0x4b8)]=0x5,_0x47b994[_0x4d6c58(0x504,0x4fc)]();const [[_0x25ecb6,_0x738131],,[_0xe236e0,_0x2e6478]]=_0x21625e;if(_0x25ecb6===_0xe236e0)_0x47b994['moveTo'](0x0,_0x25ecb6*0x64+0x32),_0x47b994['lineTo'](0x12c,_0x25ecb6*0x64+0x32);else{if(_0x738131===_0x2e6478)_0x47b994[_0x4d6c58(0x485,0x4af)](_0x738131*0x64+0x32,0x0),_0x47b994[_0x4d6c58(0x51a,0x509)](_0x738131*0x64+0x32,0x12c);else{if(_0x25ecb6===0x0&&_0x738131===0x0&&_0xe236e0===0x2&&_0x2e6478===0x2)_0x47b994[_0x4d6c58(0x48a,0x4af)](0x0,0x0),_0x47b994['lineTo'](0x12c,0x12c);else _0x25ecb6===0x0&&_0x738131===0x2&&_0xe236e0===0x2&&_0x2e6478===0x0&&(_0x47b994[_0x4d6c58(0x481,0x4af)](0x12c,0x0),_0x47b994[_0x4d6c58(0x52b,0x509)](0x0,0x12c));}}_0x47b994[_0x4d6c58(0x4e8,0x4d3)]();}const _0x3e4fbb=_0x3cf4f2['toBuffer']();return new AttachmentBuilder(_0x3e4fbb,{'name':_0x4d6c58(0x4fd,0x4e9)});}function drawX(_0x1de638,_0x3a2b4d,_0x48fba2){_0x1de638[_0x2f0b8e(0x458,0x48e)]='#FF0000',_0x1de638['lineWidth']=0xa,_0x1de638[_0x2f0b8e(0x48e,0x4be)](),_0x1de638[_0x2f0b8e(0x480,0x471)](_0x3a2b4d+0xa,_0x48fba2+0xa);function _0x2f0b8e(_0x1ca0bf,_0x25d42a){return _0x1f5f5a(_0x25d42a-0x59d,_0x1ca0bf);}_0x1de638[_0x2f0b8e(0x4e3,0x4cb)](_0x3a2b4d+0x5a,_0x48fba2+0x5a),_0x1de638['moveTo'](_0x3a2b4d+0x5a,_0x48fba2+0xa),_0x1de638['lineTo'](_0x3a2b4d+0xa,_0x48fba2+0x5a),_0x1de638[_0x2f0b8e(0x462,0x495)]();}function _0x34a7(_0x1a74c9,_0x13d772){const _0x58f5bb=_0x58f5();return _0x34a7=function(_0x34a727,_0x1becdf){_0x34a727=_0x34a727-0x1c0;let _0x539b2c=_0x58f5bb[_0x34a727];return _0x539b2c;},_0x34a7(_0x1a74c9,_0x13d772);}function drawO(_0x5b195e,_0x2a0c51,_0x55481d){function _0x2825d4(_0x4398cb,_0x15fbd5){return _0x1f5f5a(_0x4398cb-0x624,_0x15fbd5);}_0x5b195e[_0x2825d4(0x515,0x4f3)]='#00FF00',_0x5b195e[_0x2825d4(0x501,0x534)]=0xa,_0x5b195e[_0x2825d4(0x545,0x527)](),_0x5b195e[_0x2825d4(0x50d,0x501)](_0x2a0c51+0x32,_0x55481d+0x32,0x28,0x0,Math['PI']*0x2),_0x5b195e[_0x2825d4(0x51c,0x540)]();}function createBoardComponents(_0x2d9ce3,_0x180372,_0x2bafe4){function _0x54ad94(_0x49877d,_0x557d94){return _0x1f5f5a(_0x49877d-0x2c,_0x557d94);}return _0x2d9ce3[_0x54ad94(-0x105,-0xcf)]((_0x1e513e,_0x44177f)=>new ActionRowBuilder()['addComponents'](_0x1e513e[_0x54ad94(-0x105,-0xe1)]((_0x54e144,_0x44bd51)=>new ButtonBuilder()[_0x54ad94(-0xa3,-0xc4)](_0x2bafe4+'-'+_0x44177f+'-'+_0x44bd51)[_0x54ad94(-0xf4,-0x111)](_0x54e144===_0x180372['x']?'❌':_0x54e144===_0x180372['o']?'⭕':'⬛')[_0x54ad94(-0xa7,-0x89)](ButtonStyle[_0x54ad94(-0x10c,-0x13a)])[_0x54ad94(-0xf9,-0x101)](_0x54e144!==_0x180372['blank']))));}function checkWin(_0x3f43b8,_0x3f15ef){const _0x3cb903=[[[0x0,0x0],[0x0,0x1],[0x0,0x2]],[[0x1,0x0],[0x1,0x1],[0x1,0x2]],[[0x2,0x0],[0x2,0x1],[0x2,0x2]],[[0x0,0x0],[0x1,0x0],[0x2,0x0]],[[0x0,0x1],[0x1,0x1],[0x2,0x1]],[[0x0,0x2],[0x1,0x2],[0x2,0x2]],[[0x0,0x0],[0x1,0x1],[0x2,0x2]],[[0x2,0x0],[0x1,0x1],[0x0,0x2]]];for(let _0x440d9b of _0x3cb903){if(_0x440d9b[_0x16107(0x34e,0x370)](([_0x85038b,_0x27c538])=>_0x3f43b8[_0x85038b][_0x27c538]===_0x3f15ef))return _0x440d9b;}function _0x16107(_0x58836c,_0x65e95a){return _0x1f5f5a(_0x65e95a-0x4a2,_0x58836c);}return null;}function _0x1f5f5a(_0x5164e8,_0x1305a8){return _0x34a7(_0x5164e8- -0x2fc,_0x1305a8);}function isBoardFull(_0x586a05,_0x582e73){function _0x33b036(_0x562cd5,_0x5602cb){return _0x1f5f5a(_0x562cd5-0x220,_0x5602cb);}return _0x586a05[_0x33b036(0xee,0xf6)](_0x135d0c=>_0x135d0c['every'](_0x4536f8=>_0x4536f8!==_0x582e73));}function smarterBotMove(_0x1e9a7a,_0x63eab0,_0x5a21ce){const _0x19b0db=_0x5a21ce===_0x320372(0x1ed,0x1f2)?0.1:_0x5a21ce===_0x320372(0x1b4,0x1be)?0.5:0.9,_0x8a5592=()=>{const _0x5339dc=[];function _0x47d298(_0x29fbfd,_0x1e7d1f){return _0x320372(_0x29fbfd- -0x2e6,_0x1e7d1f);}for(let _0x1c46c8=0x0;_0x1c46c8<0x3;_0x1c46c8++){for(let _0x5953df=0x0;_0x5953df<0x3;_0x5953df++){_0x1e9a7a[_0x1c46c8][_0x5953df]===_0x63eab0[_0x47d298(-0x103,-0xdf)]&&_0x5339dc['push']([_0x1c46c8,_0x5953df]);}}if(_0x5339dc['length']>0x0){const [_0x31b6d9,_0x59efec]=_0x5339dc[Math['floor'](Math['random']()*_0x5339dc[_0x47d298(-0xf3,-0x10c)])];return _0x1e9a7a[_0x31b6d9][_0x59efec]=_0x63eab0['o'],!![];}return![];},_0x18d93b=_0x59c35e=>{function _0x55df7f(_0x9c5fb0,_0x3b3276){return _0x320372(_0x3b3276-0x58,_0x9c5fb0);}for(let _0x44fe8d=0x0;_0x44fe8d<0x3;_0x44fe8d++){for(let _0x311639=0x0;_0x311639<0x3;_0x311639++){if(_0x1e9a7a[_0x44fe8d][_0x311639]===_0x63eab0[_0x55df7f(0x21e,0x23b)]){_0x1e9a7a[_0x44fe8d][_0x311639]=_0x59c35e;const _0x518747=checkWin(_0x1e9a7a,_0x59c35e);_0x1e9a7a[_0x44fe8d][_0x311639]=_0x63eab0[_0x55df7f(0x206,0x23b)];if(_0x518747)return[_0x44fe8d,_0x311639];}}}return null;};if(Math[_0x320372(0x1c9,0x1a0)]()<_0x19b0db){if(_0x8a5592())return;}let _0x42ab46=_0x18d93b(_0x63eab0['o']);if(_0x42ab46){_0x1e9a7a[_0x42ab46[0x0]][_0x42ab46[0x1]]=_0x63eab0['o'];return;}_0x42ab46=_0x18d93b(_0x63eab0['x']);if(_0x42ab46){_0x1e9a7a[_0x42ab46[0x0]][_0x42ab46[0x1]]=_0x63eab0['o'];return;}if(_0x1e9a7a[0x1][0x1]===_0x63eab0['blank']){_0x1e9a7a[0x1][0x1]=_0x63eab0['o'];return;}const _0x27fc06=[[0x0,0x0],[0x0,0x2],[0x2,0x0],[0x2,0x2]];function _0x320372(_0x1d0ef6,_0x13c4fa){return _0x1f5f5a(_0x1d0ef6-0x2e4,_0x13c4fa);}const _0xecb7cc=_0x27fc06[_0x320372(0x1b6,0x1e0)](([_0x6e9b40,_0x56da64])=>_0x1e9a7a[_0x6e9b40][_0x56da64]===_0x63eab0['x'])[_0x320372(0x1b3,0x18b)](([_0x214442,_0x23bcb2])=>[0x2-_0x214442,0x2-_0x23bcb2]);for(let [_0x807e60,_0x12f428]of _0xecb7cc){if(_0x1e9a7a[_0x807e60][_0x12f428]===_0x63eab0[_0x320372(0x1e3,0x1e9)]){_0x1e9a7a[_0x807e60][_0x12f428]=_0x63eab0['o'];return;}}const _0x5c3adb=_0x27fc06[_0x320372(0x1b6,0x1af)](([_0x1b4d79,_0xacd5dd])=>_0x1e9a7a[_0x1b4d79][_0xacd5dd]===_0x63eab0[_0x320372(0x1e3,0x1d0)]);if(_0x5c3adb[_0x320372(0x1f3,0x1d7)]>0x0){const [_0x2fe4fd,_0x1b25ec]=_0x5c3adb[Math['floor'](Math[_0x320372(0x1c9,0x1cd)]()*_0x5c3adb[_0x320372(0x1f3,0x1e7)])];_0x1e9a7a[_0x2fe4fd][_0x1b25ec]=_0x63eab0['o'];return;}const _0x4e78fd=[[0x0,0x1],[0x1,0x0],[0x1,0x2],[0x2,0x1]],_0x2f10ec=_0x4e78fd[_0x320372(0x1b6,0x1ee)](([_0x10eab9,_0x2bcaaa])=>_0x1e9a7a[_0x10eab9][_0x2bcaaa]===_0x63eab0['blank']);if(_0x2f10ec[_0x320372(0x1f3,0x1c7)]>0x0){const [_0x548867,_0x17c9e6]=_0x2f10ec[Math[_0x320372(0x1ce,0x1e6)](Math[_0x320372(0x1c9,0x1da)]()*_0x2f10ec[_0x320372(0x1f3,0x1e9)])];_0x1e9a7a[_0x548867][_0x17c9e6]=_0x63eab0['o'];return;}}function createGameMessage(_0x28e5c0,_0x46e4b9,_0x1b845f,_0x2784d8,_0x60d698){function _0x3e8739(_0x41f1c7,_0x4287dd){return _0x1f5f5a(_0x4287dd-0x33f,_0x41f1c7);}const _0x7337b7=_0x1b845f===_0x2784d8['id']?_0x2784d8[_0x3e8739(0x201,0x225)]:_0x60d698[_0x3e8739(0x24c,0x225)];return _0x3e8739(0x219,0x244)+_0x7337b7+_0x3e8739(0x252,0x21b);}async function endGame(_0x11f17c,_0x2a64ff,_0x552589,_0x110102,_0x12763d,_0x43f117=null){const _0xb84421=await createGameBoardCanvas(_0x110102,_0x12763d,_0x43f117);let _0x4d8d0f='#0000FF',_0x399c52;_0x552589[_0x4a6773(0x3a7,0x3c8)](_0x4a6773(0x3e6,0x3dd))?_0x552589[_0x4a6773(0x3a7,0x393)](_0x11f17c[_0x4a6773(0x3ef,0x3d1)][_0x4a6773(0x3b3,0x389)])?(_0x4d8d0f=lang[_0x4a6773(0x3af,0x39c)]['Colors'][_0x4a6773(0x3bc,0x38f)],_0x399c52=(lang[_0x4a6773(0x3af,0x386)]['Messages'][_0x4a6773(0x3bc,0x3bd)]||_0x4a6773(0x3a3,0x3ba))[_0x4a6773(0x3f7,0x421)](_0x4a6773(0x3cf,0x3f0),'<@'+_0x11f17c[_0x4a6773(0x3ef,0x41a)]['id']+'>')):(_0x4d8d0f=lang[_0x4a6773(0x3af,0x3c8)][_0x4a6773(0x396,0x369)][_0x4a6773(0x3c7,0x3e2)],_0x399c52=(lang[_0x4a6773(0x3af,0x3ad)][_0x4a6773(0x3c3,0x38b)][_0x4a6773(0x3d9,0x3d9)]||_0x4a6773(0x3ff,0x3f7))[_0x4a6773(0x3f7,0x3e3)]('{user}','<@'+_0x11f17c[_0x4a6773(0x3ef,0x3df)]['id']+'>')):(_0x4d8d0f=lang[_0x4a6773(0x3af,0x392)][_0x4a6773(0x396,0x3c4)][_0x4a6773(0x3d7,0x3cc)],_0x399c52=(lang[_0x4a6773(0x3af,0x3da)][_0x4a6773(0x3c3,0x38e)][_0x4a6773(0x3d7,0x3e4)]||_0x4a6773(0x3de,0x409))['replace'](_0x4a6773(0x3cf,0x3d8),'<@'+_0x11f17c[_0x4a6773(0x3ef,0x3fe)]['id']+'>'));function _0x4a6773(_0x49caac,_0x199b5b){return _0x1f5f5a(_0x49caac-0x4cd,_0x199b5b);}const _0x4ba104=new EmbedBuilder()[_0x4a6773(0x3e4,0x403)](_0x4d8d0f)[_0x4a6773(0x3e3,0x405)](lang['TicTacToe'][_0x4a6773(0x3c3,0x3b2)][_0x4a6773(0x3c0,0x3f4)])[_0x4a6773(0x3bf,0x38f)](_0x399c52)['setFooter']({'text':lang['TicTacToe'][_0x4a6773(0x3c3,0x3c9)][_0x4a6773(0x3a2,0x3b2)],'iconURL':_0x11f17c[_0x4a6773(0x3ef,0x3c5)][_0x4a6773(0x3e9,0x3c4)]()});await _0x2a64ff[_0x4a6773(0x3e8,0x40a)]({'embeds':[_0x4ba104],'files':[_0xb84421],'components':[]})[_0x4a6773(0x3c2,0x3bf)](console[_0x4a6773(0x3f5,0x3f1)]);}