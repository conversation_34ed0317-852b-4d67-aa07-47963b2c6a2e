const { SlashCommandBuilder } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createLoadingEmbed } = require('../../utils/embedBuilder.js');
const { isValidTemplateName } = require('../../utils/validators.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('template-create')
        .setDescription('Tạo template từ cấu hình server hiện tại')
        .addStringOption(option =>
            option.setName('name')
                .setDescription('Tên template')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('description')
                .setDescription('Mô tả template')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('include')
                .setDescription('Thành phần cần lưu')
                .setRequired(false)
                .addChoices(
                    { name: '🏗️ Tất cả - Channels, roles, settings', value: 'all' },
                    { name: '📝 Channels - Chỉ channels và categories', value: 'channels' },
                    { name: '🎭 Roles - Chỉ roles và permissions', value: 'roles' },
                    { name: '⚙️ Settings - Chỉ cấu hình bot', value: 'settings' }
                )
        )
        .addBooleanOption(option =>
            option.setName('public')
                .setDescription('Chia sẻ template công khai (mặc định: false)')
                .setRequired(false)
        ),
    category: 'templates',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        const name = interaction.options.getString('name');
        const description = interaction.options.getString('description');
        const include = interaction.options.getString('include') || 'all';
        const isPublic = interaction.options.getBoolean('public') || false;
        
        // Validate template name
        if (!isValidTemplateName(name)) {
            const errorEmbed = createErrorEmbed(
                'Tên template không hợp lệ!',
                'Tên template chỉ được chứa:\n• Chữ cái (a-z, A-Z)\n• Số (0-9)\n• Khoảng trắng\n• Dấu gạch ngang (-)\n• Dấu gạch dưới (_)\n• Độ dài: 1-50 ký tự'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        // Validate description
        if (description.length > 200) {
            const errorEmbed = createErrorEmbed(
                'Mô tả quá dài!',
                'Mô tả template không được vượt quá 200 ký tự.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        // Show loading message
        const loadingEmbed = createLoadingEmbed(
            'Đang tạo template...',
            'Vui lòng chờ trong khi bot thu thập dữ liệu server.'
        );
        await interaction.reply({ embeds: [loadingEmbed] });
        
        try {
            // Collect server data
            const templateData = await collectServerData(interaction.guild, include, client);
            
            // Save template to database
            const result = await client.db.createServerTemplate({
                name: name,
                description: description,
                data: templateData,
                createdBy: interaction.user.id,
                isPublic: isPublic
            });
            
            const successEmbed = createSuccessEmbed(
                'Template đã được tạo thành công!',
                `**Tên:** ${name}\n` +
                `**Mô tả:** ${description}\n` +
                `**Thành phần:** ${getIncludeDescription(include)}\n` +
                `**Công khai:** ${isPublic ? 'Có' : 'Không'}\n` +
                `**ID Template:** ${result.id}`
            );
            
            successEmbed.addFields({
                name: '📊 Thống kê',
                value: getTemplateStats(templateData),
                inline: false
            });
            
            if (isPublic) {
                successEmbed.addFields({
                    name: '🌍 Template công khai',
                    value: 'Template này có thể được sử dụng bởi các server khác thông qua `/template-apply`.',
                    inline: false
                });
            }
            
            await interaction.editReply({ embeds: [successEmbed] });
            
        } catch (error) {
            console.error('Lỗi khi tạo template:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi tạo template!',
                'Đã xảy ra lỗi khi tạo template. Vui lòng thử lại sau!'
            );
            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },
};

async function collectServerData(guild, include, client) {
    const data = {
        guildName: guild.name,
        guildIcon: guild.iconURL({ dynamic: true }),
        createdAt: new Date().toISOString(),
        include: include
    };
    
    if (include === 'all' || include === 'channels') {
        data.channels = await collectChannelData(guild);
    }
    
    if (include === 'all' || include === 'roles') {
        data.roles = await collectRoleData(guild);
    }
    
    if (include === 'all' || include === 'settings') {
        data.settings = await collectSettingsData(guild, client);
    }
    
    return data;
}

async function collectChannelData(guild) {
    const channels = [];
    const categories = [];
    
    // Collect categories first
    guild.channels.cache
        .filter(channel => channel.type === 4) // GuildCategory
        .forEach(category => {
            categories.push({
                name: category.name,
                position: category.position,
                permissions: category.permissionOverwrites.cache.map(overwrite => ({
                    id: overwrite.id,
                    type: overwrite.type,
                    allow: overwrite.allow.toArray(),
                    deny: overwrite.deny.toArray()
                }))
            });
        });
    
    // Collect text and voice channels
    guild.channels.cache
        .filter(channel => channel.type === 0 || channel.type === 2) // GuildText or GuildVoice
        .forEach(channel => {
            const channelData = {
                name: channel.name,
                type: channel.type === 0 ? 'text' : 'voice',
                position: channel.position,
                parentName: channel.parent?.name || null
            };
            
            if (channel.type === 0) { // Text channel
                channelData.topic = channel.topic;
                channelData.nsfw = channel.nsfw;
                channelData.rateLimitPerUser = channel.rateLimitPerUser;
            } else { // Voice channel
                channelData.userLimit = channel.userLimit;
                channelData.bitrate = channel.bitrate;
            }
            
            // Permissions
            channelData.permissions = channel.permissionOverwrites.cache.map(overwrite => ({
                id: overwrite.id,
                type: overwrite.type,
                allow: overwrite.allow.toArray(),
                deny: overwrite.deny.toArray()
            }));
            
            channels.push(channelData);
        });
    
    return { categories, channels };
}

async function collectRoleData(guild) {
    const roles = [];
    
    guild.roles.cache
        .filter(role => role.id !== guild.id) // Exclude @everyone
        .forEach(role => {
            roles.push({
                name: role.name,
                color: role.hexColor,
                hoist: role.hoist,
                mentionable: role.mentionable,
                permissions: role.permissions.toArray(),
                position: role.position
            });
        });
    
    return roles;
}

async function collectSettingsData(guild, client) {
    const settings = {};
    
    try {
        // Welcome/Goodbye settings
        const welcomeConfig = await client.db.getWelcomeConfig(guild.id);
        if (welcomeConfig) {
            settings.welcome = {
                enabled: welcomeConfig.welcome_enabled,
                message: welcomeConfig.welcome_message,
                embed: welcomeConfig.welcome_embed,
                dmWelcome: welcomeConfig.dm_welcome
            };
            settings.goodbye = {
                enabled: welcomeConfig.goodbye_enabled,
                message: welcomeConfig.goodbye_message,
                embed: welcomeConfig.goodbye_embed
            };
        }
        
        // Auto roles
        const autoRoles = await client.db.getAutoRoles(guild.id);
        if (autoRoles.length > 0) {
            settings.autoRoles = autoRoles.map(role => ({
                roleName: role.role_name,
                delaySeconds: role.delay_seconds
            }));
        }
        
        // Verification settings
        const verificationConfig = await client.db.getVerificationConfig(guild.id);
        if (verificationConfig) {
            settings.verification = {
                enabled: verificationConfig.enabled,
                type: verificationConfig.verification_type,
                emoji: verificationConfig.verification_emoji
            };
        }
        
        // Moderation settings
        const moderationConfig = await client.db.getModerationConfig(guild.id);
        if (moderationConfig) {
            settings.moderation = {
                automodEnabled: moderationConfig.automod_enabled,
                spamProtection: moderationConfig.spam_protection,
                linkProtection: moderationConfig.link_protection,
                capsProtection: moderationConfig.caps_protection,
                warningThreshold: moderationConfig.warning_threshold,
                banThreshold: moderationConfig.auto_ban_threshold
            };
        }
        
    } catch (error) {
        console.error('Lỗi khi thu thập settings:', error);
    }
    
    return settings;
}

function getIncludeDescription(include) {
    const descriptions = {
        'all': '🏗️ Tất cả (Channels, Roles, Settings)',
        'channels': '📝 Chỉ Channels và Categories',
        'roles': '🎭 Chỉ Roles và Permissions',
        'settings': '⚙️ Chỉ cấu hình Bot'
    };
    return descriptions[include] || include;
}

function getTemplateStats(data) {
    let stats = [];
    
    if (data.channels) {
        stats.push(`• ${data.channels.categories.length} danh mục`);
        stats.push(`• ${data.channels.channels.length} kênh`);
    }
    
    if (data.roles) {
        stats.push(`• ${data.roles.length} vai trò`);
    }
    
    if (data.settings) {
        const settingsCount = Object.keys(data.settings).length;
        stats.push(`• ${settingsCount} cấu hình bot`);
    }
    
    return stats.join('\n') || 'Không có dữ liệu';
}
