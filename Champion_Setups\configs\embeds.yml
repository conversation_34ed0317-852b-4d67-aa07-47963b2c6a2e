# ________________________________________________
#
#         CHAMPION SETUPS EMBEDS FILE
#
# Embed Colors Accept Color Names
# (Red, Green etc.) and HEX (#f1f1f1, #dddddd etc.)
#
# For new Line use \n
#
# ________________________________________________

# Title of every Embed
title: "Champion Setups Bot"
# Footer of Every Embed
footer: "Champion Setups"
# Embed Color for Errors or Negative Responses
error_color: "Red"
# Embed Color for Success or Positive Responses
success_color: "Green"
# Embed Color for other situations
general_color: "Yellow"
setup:
  create:
    # Embed Title
    title: "Create Server"
    # Embed Description, to disable leave empty
    # Valid Placeholders are <template>
    description: "Setup of Server from Config with name `<template>` has started, please select what do you want to create first using drop down menu."
    # Embed Color
    color: "#4CAAFF"
    # Is Footer enabled
    footer: true
    # Thumbnail
    thumbnail: true
    # Valid Placeholders are <template>
    # To use just description set to fields: []
    # Example:
    # - title: "Title"
    #   description: "Description"
    fields: []
  delete:
    # Embed Title
    title: "Delete Server"
    # Embed Description, to disable leave empty
    # Valid Placeholders are <template>
    description: "Select what aspect of Server you want to delete using drop down menu."
    # Embed Color
    color: "#4CAAFF"
    # Is Footer enabled
    footer: true
    # Thumbnail
    thumbnail: true
    # Valid Placeholders are <template>
    # To use just description set to fields: []
    fields: []
  # Template Info Command
  templateInfo:
    # Embed Title
    title: "Template Informations"
    # Embed Description, to disable leave empty
    # Valid Placeholders are <name>, <channels>, <roles>, <emojisCount>
    description: "Informations for Template <name>.\nChannels List:\n```<channels>```\nRoles List:\n```<roles>```"
    # Embed Color
    color: "#4CAAFF"
    # Is Footer enabled
    footer: true
    # Thumbnail
    thumbnail: true
    # Valid Placeholders are <name>, <channels>, <roles>, <emojisCount>
    # To use just description set to fields: []
    fields: []
  # InviteBot Command
  inviteBot:
    # Embed Title
    title: "Invite Bot to Server"
    # Button Text
    button: "Invite Bot"
    # Embed Description, to disable leave empty
    # Valid Placeholders are <name>, <description>, <guilds>
    description: ">>> **Bot Name:** <name>\n**Description:** <description>\n**Guild Count:** <guilds>"
    # Embed Color
    color: "#4CAAFF"
    # Is Footer enabled
    footer: true
    # Thumbnail
    thumbnail: true
    # Valid Placeholders are <name>, <channels>, <roles>, <emojisCount>
    # To use just description set to fields: []
    fields: [] 
  # Symbols Command
  symbols:
    # Embed Title
    title: "Symbols List"
    # Embed Description, to disable leave empty
    # Valid Placeholders are <type>, <symbols>, <count>
    description: "List of Symbols"
    # Embed Color
    color: "#4CAAFF"
    # Is Footer enabled
    footer: true
    # Thumbnail
    thumbnail: true
    # Valid Placeholders are <type>, <symbols>, <count>
    # To use just description set to fields: []
    fields: 
      - title: "Symbol Type"
        description: "<type>"
      - title: "List of Symbols (<count>)"
        description: "<symbols>"
# Help Command Embed
help:
  # Embed Title
  title: "🚀 · Help Menu"
  # Embed Description, to disable leave empty
  # Valid Placeholders are <member>, <setups> & <prefix>
  description: ""
  # Embed Color
  color: "#4CAAFF"
  # Is Footer enabled
  footer: true
  # Thumbnail
  thumbnail: true
  # Valid Placeholders are <member>, <setups> & <prefix>
  # To use just description set to fields: []
  fields:
    - title: "👤 ･ Member"
      description: "<member>"
    - title: "💻 ･ Setups"
      description: "<setups>"
# Command Info Embed
commandInfo:
  # Embed Title
  title: "🚀 · Informations About Command"
  # Embed Description, to disable leave empty
  # Valid Placeholders are <name>, <description>, <usage>, <category> & <prefix>
  description: ""
  # Embed Color
  color: "#4CAAFF"
  # Is Footer enabled
  footer: true
  # Thumbnail
  thumbnail: true
  # Valid Placeholders are <name>, <description>, <usage>, <category> & <prefix>
  # To use just description set to fields: []
  fields:
    - title: "Command Name"
      description: "`<name>`"
    - title: "Description"
      description: "`<description>`"
    - title: "Usage"
      description: "`<prefix><usage>`"
    - title: "Category"
      description: "`<category>`" 
# RoleInfo Command
roleInfo:
  # Embed Title
  title: "Role Informations"
  # Embed Description, to disable leave empty
  # Valid Placeholders are <name>, <color>, <position> & <hoisted>
  description: ""
  # Embed Color
  color: "#4CAAFF"
  # Is Footer enabled
  footer: true
  # Thumbnail
  thumbnail: true
  # Valid Placeholders are <name>, <color>, <position>, <creation> & <hoisted>
  # To use just description set to fields: []
  fields:
    - title: "Name"
      description: "<name>"
    - title: "Color"
      description: "<color>"
    - title: "Position"
      description: "#<position>"
    - title: "Created At"
      description: "<creation>" 