function _0x4ece21(_0x2ec710,_0x362bfd){return _0x5a72(_0x362bfd- -0x163,_0x2ec710);}(function(_0x285200,_0x21bc15){const _0x3e00ac=_0x285200();function _0x258baf(_0x22e337,_0x5d6049){return _0x5a72(_0x5d6049-0x1c1,_0x22e337);}while(!![]){try{const _0x187c61=-parseInt(_0x258baf(0x39f,0x3b3))/0x1+parseInt(_0x258baf(0x39e,0x3b4))/0x2*(parseInt(_0x258baf(0x3a2,0x397))/0x3)+-parseInt(_0x258baf(0x3a1,0x3b5))/0x4+-parseInt(_0x258baf(0x3b4,0x39f))/0x5+parseInt(_0x258baf(0x3d0,0x3c0))/0x6+-parseInt(_0x258baf(0x39b,0x3ab))/0x7*(parseInt(_0x258baf(0x38c,0x393))/0x8)+parseInt(_0x258baf(0x391,0x394))/0x9;if(_0x187c61===_0x21bc15)break;else _0x3e00ac['push'](_0x3e00ac['shift']());}catch(_0x4bb154){_0x3e00ac['push'](_0x3e00ac['shift']());}}}(_0x1168,0x1c62b));const {ContextMenuCommandBuilder,ApplicationCommandType}=require('discord.js'),yaml=require(_0x4ece21(0x86,0x96)),fs=require('fs'),path=require(_0x4ece21(0x6f,0x76)),Suggestion=require('../../models/Suggestion'),suggestionActions=require(_0x4ece21(0x70,0x7f)),config=yaml[_0x4ece21(0x6f,0x75)](fs[_0x4ece21(0x89,0x89)](path['resolve'](__dirname,_0x4ece21(0x70,0x7d)),'utf8')),lang=yaml[_0x4ece21(0x61,0x75)](fs[_0x4ece21(0x8e,0x89)](path[_0x4ece21(0x76,0x88)](__dirname,_0x4ece21(0x7c,0x7c)),'utf8')),acceptCommand=new ContextMenuCommandBuilder()[_0x4ece21(0x9c,0x8e)]('Accept')[_0x4ece21(0x66,0x78)](ApplicationCommandType[_0x4ece21(0xa4,0x8a)]),denyCommand=new ContextMenuCommandBuilder()[_0x4ece21(0x92,0x8e)](_0x4ece21(0xae,0x99))[_0x4ece21(0x63,0x78)](ApplicationCommandType['Message']);function _0x1168(){const _0x72b857=['NoPermsMessage','8xTNbMQ','2654613bMnpcO','Invalid\x20command.','cache','378VJkRqP','exports','load','path','channel','setType','findOne','SuggestionAcceptDenyRoles','166885QRMMvi','../../lang.yml','../../config.yml','denySuggestion','../../events/Suggestions/suggestionActions','Accept','some','client','\x20not\x20found.','targetId','Suggestion\x20','commandName','1533973SncMhv','resolve','readFileSync','Message','member','reply','Error','setName','127433TSJJLW','1618CNFQvQ','270548nFKgdp','has','\x20denied\x20successfully.','acceptSuggestion','Enabled','js-yaml','replied','uniqueId','Deny','Error\x20in\x20suggestion\x20command:\x20','SuggestionsDisabled','1001772NvLDhT','fetch','messages','Suggestion','SuggestionSettings','\x20accepted\x20successfully.'];_0x1168=function(){return _0x72b857;};return _0x1168();}function _0x5a72(_0x2a1320,_0x9c7c2a){const _0x116844=_0x1168();return _0x5a72=function(_0x5a727d,_0x5f1f33){_0x5a727d=_0x5a727d-0x1cf;let _0x2ff123=_0x116844[_0x5a727d];return _0x2ff123;},_0x5a72(_0x2a1320,_0x9c7c2a);}module[_0x4ece21(0x74,0x74)]={'data':[acceptCommand,denyCommand],'category':'General',async 'execute'(_0x5cd556){function _0x1fd4bb(_0x44d845,_0x564f3d){return _0x4ece21(_0x564f3d,_0x44d845-0x337);}try{if(!config[_0x1fd4bb(0x3a3,0x399)][_0x1fd4bb(0x3cc,0x3c6)]){!_0x5cd556[_0x1fd4bb(0x3ce,0x3b9)]&&await _0x5cd556[_0x1fd4bb(0x3c3,0x3d9)]({'content':lang[_0x1fd4bb(0x3d6,0x3cb)][_0x1fd4bb(0x3d2,0x3c3)],'ephemeral':!![]});return;}const _0x1fc3e9=_0x5cd556[_0x1fd4bb(0x3bd,0x3d2)],_0x3978f9=await _0x5cd556[_0x1fd4bb(0x3ae,0x39b)][_0x1fd4bb(0x3d5,0x3db)][_0x1fd4bb(0x3d4,0x3bb)](_0x5cd556[_0x1fd4bb(0x3bb,0x3a5)]),_0x463ba1=_0x3978f9['id'],_0x3c5c05=await Suggestion[_0x1fd4bb(0x3b0,0x3b9)]({'messageId':_0x463ba1});if(!_0x3c5c05){!_0x5cd556[_0x1fd4bb(0x3ce,0x3c2)]&&await _0x5cd556['reply']({'content':_0x1fd4bb(0x3bc,0x3cc)+_0x463ba1+_0x1fd4bb(0x3ba,0x3aa),'ephemeral':!![]});return;}const _0x2da575=config[_0x1fd4bb(0x3a3,0x3b0)][_0x1fd4bb(0x3b1,0x3a0)],_0x5a7120=_0x2da575[_0x1fd4bb(0x3b8,0x39e)](_0x4b0e40=>_0x5cd556[_0x1fd4bb(0x3c2,0x3b1)]['roles'][_0x1fd4bb(0x3a9,0x392)][_0x1fd4bb(0x3c9,0x3d3)](_0x4b0e40));if(!_0x5a7120){!_0x5cd556[_0x1fd4bb(0x3ce,0x3da)]&&await _0x5cd556[_0x1fd4bb(0x3c3,0x3d4)]({'content':lang[_0x1fd4bb(0x3a5,0x3b9)],'ephemeral':!![]});return;}if(_0x1fc3e9===_0x1fd4bb(0x3b7,0x3c6)){const _0x34b86c=await suggestionActions[_0x1fd4bb(0x3cb,0x3e2)](_0x5cd556['client'],_0x5cd556,_0x3c5c05[_0x1fd4bb(0x3cf,0x3be)]);_0x34b86c&&(!_0x5cd556[_0x1fd4bb(0x3ce,0x3cf)]&&await _0x5cd556[_0x1fd4bb(0x3c3,0x3b4)]({'content':_0x1fd4bb(0x3bc,0x3a3)+_0x3c5c05[_0x1fd4bb(0x3cf,0x3d6)]+_0x1fd4bb(0x3a4,0x3b1),'ephemeral':!![]}));}else{if(_0x1fc3e9===_0x1fd4bb(0x3d0,0x3ca)){const _0x13b5a8=await suggestionActions[_0x1fd4bb(0x3b5,0x3c3)](_0x5cd556[_0x1fd4bb(0x3b9,0x3ab)],_0x5cd556,_0x3c5c05[_0x1fd4bb(0x3cf,0x3c3)]);_0x13b5a8&&(!_0x5cd556[_0x1fd4bb(0x3ce,0x3b8)]&&await _0x5cd556[_0x1fd4bb(0x3c3,0x3d8)]({'content':'Suggestion\x20'+_0x3c5c05['uniqueId']+_0x1fd4bb(0x3ca,0x3d0),'ephemeral':!![]}));}else!_0x5cd556[_0x1fd4bb(0x3ce,0x3ce)]&&await _0x5cd556[_0x1fd4bb(0x3c3,0x3ce)]({'content':_0x1fd4bb(0x3a8,0x3bf),'ephemeral':!![]});}}catch(_0xb15d27){console['error'](_0x1fd4bb(0x3d1,0x3e4),_0xb15d27),!_0x5cd556[_0x1fd4bb(0x3ce,0x3e2)]&&await _0x5cd556[_0x1fd4bb(0x3c3,0x3b1)]({'content':lang['Suggestion'][_0x1fd4bb(0x3c4,0x3de)],'ephemeral':!![]});}}};