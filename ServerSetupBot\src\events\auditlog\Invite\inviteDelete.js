const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.InviteDelete,
    async execute(invite, client) {
        try {
            console.log(`🗑️ Invite deleted: ${invite.code} in ${invite.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(invite.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'INVITE_DELETE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'INVITE_DELETE',
                user: 'System',
                userId: null,
                action: 'Lời mời được xóa',
                details: `Lời mời **${invite.code}** đã được xóa`,
                target: invite.code,
                channel: invite.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the invite from audit logs
            try {
                const auditLogs = await invite.guild.fetchAuditLogs({
                    type: 42, // INVITE_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.code === invite.code) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Lời mời được xóa bởi ${auditEntry.executor.tag}`;
                }
            } catch (error) {
                console.log('Could not fetch audit logs for invite delete');
            }
            
            // Add to database
            await client.db.addAuditLog(invite.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Lời mời được xóa',
                `Vừa có một lời mời được xóa`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            embed.addFields([
                {
                    name: '> Mã lời mời',
                    value: `- ${invite.code}`,
                    inline: true
                },
                {
                    name: '> Kênh mục tiêu',
                    value: `- ${invite.channel ? invite.channel.name : 'Không xác định'}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Người tạo ban đầu',
                    value: `- ${invite.inviter ? `${invite.inviter.tag}` : 'Không xác định'}`,
                    inline: true
                },
                {
                    name: '> Số lần đã sử dụng',
                    value: `- ${invite.uses || 0}`,
                    inline: true
                },
                {
                    name: '> Thời gian xóa',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add invite details if available
            if (invite.maxUses !== undefined) {
                embed.addFields({
                    name: '> Thông tin lời mời',
                    value: [
                        `• Giới hạn sử dụng: ${invite.maxUses === 0 ? 'Không giới hạn' : invite.maxUses}`,
                        `• Tạm thời: ${invite.temporary ? 'Có' : 'Không'}`,
                        `• Thời gian tồn tại: ${invite.maxAge === 0 ? 'Vĩnh viễn' : `${invite.maxAge / 3600} giờ`}`
                    ].join('\n'),
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${invite.guild.name} • Invite Delete`,
                iconURL: invite.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Invite delete logged: ${invite.code}`);
            
        } catch (error) {
            console.error('Error in inviteDelete audit log:', error);
        }
    }
};
