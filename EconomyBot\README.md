# Discord Economy Bot

Bot Discord với hệ thống kinh tế hoàn chỉnh, đ<PERSON><PERSON><PERSON> viết bằng Node.js và Discord.js v14. Bot hỗ trợ đầy đủ các tính năng kinh tế như quản lý tiền tệ, c<PERSON><PERSON>, <PERSON><PERSON> hàng, và bảng xếp hạng.

## ✨ Tính năng chính

### 💰 Hệ thống kinh tế nâng cao

- **Quản lý số dư**: Kiểm tra số dư ví và ngân hàng với giao diện đẹp mắt
- **Chuyển tiền**: Gửi tiền cho người dùng khác với nhiều định dạng
- **Ngân hàng**: Gửi/rút tiền với lãi suất hàng ngày và bảo mật cao
- **Thu nhập đa dạng**: Phần thưởng hàng ngà<PERSON>, là<PERSON> việc, và các hoạt động khác

### 🎮 Hệ thống Level & Kinh nghiệm

- **Level System**: <PERSON><PERSON> thống cấp độ với thanh tiến độ trực quan
- **Kinh nghiệm**: Nhận EXP từ mọi hoạt động kinh tế
- **Level Rewards**: Thưởng tiền khi lên cấp
- **Prestige**: Hệ thống uy tín cho người chơi cao cấp

### 🏆 Thành tựu & Streak

- **Achievement System**: Hệ thống thành tựu với nhiều mục tiêu
- **Daily Streaks**: Chuỗi ngày liên tiếp với bonus tăng dần
- **Phần thưởng**: Mỗi thành tựu đều có phần thưởng hấp dẫn
- **Tiến độ**: Theo dõi tiến độ hoàn thành các thành tựu

### 🛒 Cửa hàng & Kho đồ nâng cao

- **Cửa hàng đa dạng**: 14+ vật phẩm với 6 danh mục khác nhau
- **Hệ thống độ hiếm**: Common, Uncommon, Rare, Epic, Legendary
- **Level Requirements**: Vật phẩm yêu cầu level để mua
- **Kho đồ thông minh**: Quản lý vật phẩm với thống kê chi tiết

### 🎰 Hệ thống trò chơi đa dạng

- **Crime System**: 5 loại tội phạm với rủi ro và phần thưởng khác nhau
- **Fishing**: Câu cá với 11 loại cá từ thường đến huyền thoại
- **Hunting**: Săn bắn 12 loại động vật với yêu cầu level
- **Casino Games**: Tung xu, xúc xắc, máy quay số với tỷ lệ thắng khác nhau
- **Blackjack**: Game Blackjack hoàn chỉnh với dealer AI
- **Rock Paper Scissors**: PvP và chơi với bot
- **Lottery**: Hệ thống xổ số với jackpot x1000
- **Risk vs Reward**: Cân bằng hoàn hảo giữa rủi ro và lợi nhuận

### 🏆 Bảng xếp hạng chi tiết

- **Xếp hạng tài sản**: Top người dùng giàu nhất với level
- **Xếp hạng số dư**: Top số dư ví cao nhất
- **Xếp hạng ngân hàng**: Top tiết kiệm nhiều nhất
- **Vị trí cá nhân**: Hiển thị vị trí của bạn trong bảng xếp hạng

### 🎯 Tính năng kỹ thuật

- **Slash Commands**: Sử dụng lệnh slash hiện đại
- **Embeds**: Tất cả phản hồi đều dùng Discord embeds
- **Tiếng Việt**: Toàn bộ nội dung bằng tiếng Việt
- **Database**: Lưu trữ dữ liệu bằng SQLite
- **Modular**: Cấu trúc code dễ bảo trì và mở rộng

## 📋 Yêu cầu hệ thống

- **Node.js**: Phiên bản 16.0.0 trở lên
- **NPM**: Để cài đặt dependencies
- **Discord Bot Token**: Từ Discord Developer Portal

## 🚀 Cài đặt

### 1. Clone repository

```bash
git clone <repository-url>
cd EconomyBot
```

### 2. Cài đặt dependencies

```bash
npm install
```

### 3. Cấu hình môi trường

```bash
# Sao chép file cấu hình mẫu
cp .env.example .env

# Chỉnh sửa file .env với thông tin của bạn
```

### 4. Cấu hình Discord Bot

#### Tạo Discord Application:

1. Truy cập [Discord Developer Portal](https://discord.com/developers/applications)
2. Tạo "New Application"
3. Vào tab "Bot" và tạo bot
4. Sao chép **Token** và **Application ID**

#### Cấu hình file .env:

```env
DISCORD_TOKEN=your_discord_bot_token_here
CLIENT_ID=your_bot_client_id_here
```

#### Mời bot vào server:

1. Vào tab "OAuth2" > "URL Generator"
2. Chọn scopes: `bot`, `applications.commands`
3. Chọn permissions: `Send Messages`, `Use Slash Commands`, `Embed Links`
4. Sử dụng URL được tạo để mời bot

### 5. Khởi động bot

```bash
# Chế độ production
npm start

# Chế độ development (auto-restart)
npm run dev
```

## 📖 Hướng dẫn sử dụng

### 💰 Lệnh cơ bản

- `/balance [user]` - Kiểm tra hồ sơ với level, EXP và thống kê chi tiết
- `/daily` - Nhận phần thưởng hàng ngày với streak bonus (100-500 xu + bonus)
- `/work` - Làm việc để kiếm tiền và EXP (80-600 xu, cooldown 1 giờ)
- `/transfer <user> <amount>` - Chuyển tiền cho người khác (hỗ trợ all, half, 1k, 1m)

### 🏦 Ngân hàng

- `/bank deposit <amount>` - Gửi tiền vào ngân hàng an toàn
- `/bank withdraw <amount>` - Rút tiền từ ngân hàng
- `/bank info` - Xem thông tin tài khoản ngân hàng chi tiết

### 🛒 Cửa hàng & Kho đồ

- `/shop list [category]` - Xem danh sách vật phẩm theo độ hiếm
- `/shop buy <item> [quantity]` - Mua vật phẩm (kiểm tra level requirement)
- `/shop sell <item> [quantity]` - Bán vật phẩm từ kho
- `/inventory [user] [category]` - Xem kho đồ với thống kê giá trị

### 🎰 Trò chơi & Giải trí

- `/crime` - Thực hiện hành vi phạm pháp (5 loại tội phạm, rủi ro cao)
- `/fish` - Câu cá để kiếm tiền (11 loại cá, cooldown 30 phút)
- `/hunt` - Săn bắn động vật (12 loại, yêu cầu level 3+, cooldown 40 phút)
- `/gamble coinflip <amount> <heads/tails>` - Tung xu (x1.8 nếu thắng)
- `/gamble dice <amount> <1-6>` - Đoán xúc xắc (x5 nếu đúng)
- `/gamble slots <amount>` - Máy quay số (jackpot x50)
- `/blackjack <amount>` - Chơi Blackjack với dealer (game hoàn chỉnh)
- `/rps challenge <user> <amount>` - Thách thức PvP oẳn tù tì
- `/rps bot <amount> <choice>` - Chơi oẳn tù tì với bot
- `/lottery buy <numbers>` - Mua vé số với 6 con số tự chọn
- `/lottery quick [quantity]` - Mua vé số ngẫu nhiên (jackpot x1000)

### 🏆 Thành tựu & Bảng xếp hạng

- `/achievements [user]` - Xem thành tựu đã mở khóa và chưa mở khóa
- `/leaderboard [type]` - Xem bảng xếp hạng với vị trí cá nhân
  - `wealth` - Tổng tài sản (mặc định)
  - `balance` - Số dư ví
  - `bank` - Số tiền trong ngân hàng

### ❓ Trợ giúp

- `/help` - Hiển thị hướng dẫn tổng quan với mẹo sử dụng
- `/help <command>` - Hướng dẫn chi tiết cho lệnh cụ thể

## ⚙️ Cấu hình

### File .env

```env
# Discord Bot
DISCORD_TOKEN=your_token
CLIENT_ID=your_client_id

# Database
DATABASE_PATH=./data/economy.db

# Economy Settings
CURRENCY_NAME=Xu
CURRENCY_SYMBOL=💰
STARTING_BALANCE=1000
DAILY_REWARD_MIN=100
DAILY_REWARD_MAX=500

# Shop Settings
SHOP_TAX_RATE=0.05

# Bot Settings
PREFIX=!
TIMEZONE=Asia/Ho_Chi_Minh
```

### Tùy chỉnh cấu hình

Chỉnh sửa file `src/config/config.js` để thay đổi:

- Tên và biểu tượng tiền tệ
- Số dư ban đầu
- Phần thưởng hàng ngày
- Màu sắc embeds
- Emoji và biểu tượng

## 📁 Cấu trúc thư mục

```
EconomyBot/
├── src/
│   ├── commands/          # Slash commands (17 commands)
│   │   ├── balance.js     # Hồ sơ người dùng với level & EXP
│   │   ├── daily.js       # Phần thưởng hàng ngày + streak
│   │   ├── work.js        # Làm việc kiếm tiền + EXP
│   │   ├── transfer.js    # Chuyển tiền giữa users
│   │   ├── bank.js        # Hệ thống ngân hàng
│   │   ├── shop.js        # Cửa hàng với 14+ items
│   │   ├── inventory.js   # Quản lý kho đồ
│   │   ├── leaderboard.js # Bảng xếp hạng đa dạng
│   │   ├── achievements.js # Hệ thống thành tựu
│   │   ├── crime.js       # Hệ thống phạm pháp (5 loại)
│   │   ├── fish.js        # Câu cá (11 loại cá)
│   │   ├── hunt.js        # Săn bắn (12 loại động vật)
│   │   ├── gamble.js      # Casino games (3 trò)
│   │   ├── blackjack.js   # Blackjack hoàn chỉnh
│   │   ├── rps.js         # Rock Paper Scissors PvP
│   │   ├── lottery.js     # Hệ thống xổ số
│   │   └── help.js        # Hướng dẫn chi tiết
│   ├── config/            # Cấu hình
│   │   └── config.js      # Cấu hình tổng thể
│   ├── database/          # Database SQLite
│   │   └── database.js    # 7 bảng với relationships
│   ├── events/            # Discord events
│   │   ├── ready.js       # Bot startup & deploy commands
│   │   └── interactionCreate.js # Command handling
│   ├── models/            # Data models
│   │   ├── User.js        # User với level & achievements
│   │   ├── Item.js        # Items với rarity system
│   │   └── Inventory.js   # Inventory management
│   ├── utils/             # Utilities
│   │   ├── embedBuilder.js # Embed templates + progress bars
│   │   ├── economy.js     # Economy calculations
│   │   └── deployCommands.js # Auto-deploy slash commands
│   └── index.js           # Main bot file
├── data/                  # Database files (auto-created)
├── package.json           # Dependencies & scripts
├── .env.example           # Environment template
├── .gitignore            # Git ignore rules
├── README.md             # Documentation
└── SETUP.md              # Setup guide
```

## 🔧 Phát triển

### Thêm lệnh mới

1. Tạo file trong `src/commands/`
2. Export object với `data` (SlashCommandBuilder) và `execute` function
3. Bot sẽ tự động load lệnh khi khởi động

### Thêm vật phẩm mới

Chỉnh sửa function `insertDefaultItems()` trong `src/database/database.js`

### Thêm tính năng mới

1. Tạo model trong `src/models/` nếu cần
2. Thêm utility functions trong `src/utils/`
3. Tạo commands tương ứng

## 🐛 Troubleshooting

### Bot không phản hồi slash commands

- Kiểm tra bot có quyền `Use Slash Commands`
- Đảm bảo `CLIENT_ID` đúng trong file .env
- Restart bot để deploy commands

### Lỗi database

- Kiểm tra quyền ghi file trong thư mục `data/`
- Xóa file database để tạo lại (mất dữ liệu)

### Lỗi permissions

- Đảm bảo bot có quyền `Send Messages`, `Embed Links`
- Kiểm tra role của bot trong server

## 📝 License

MIT License - Xem file LICENSE để biết thêm chi tiết.

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Tạo Pull Request

## 📞 Hỗ trợ

Nếu gặp vấn đề, vui lòng:

1. Kiểm tra phần Troubleshooting
2. Tạo issue trên GitHub
3. Liên hệ developer

---

**Lưu ý**: Bot này được thiết kế cho mục đích giải trí và học tập. Không sử dụng cho mục đích thương mại mà không có sự cho phép.
