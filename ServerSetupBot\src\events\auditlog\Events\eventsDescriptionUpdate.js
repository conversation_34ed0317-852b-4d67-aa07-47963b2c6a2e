const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildScheduledEventUpdate,
    async execute(oldEvent, newEvent, client) {
        try {
            // Only handle description changes
            if (oldEvent.description === newEvent.description) return;
            
            console.log(`📝 Event description updated: ${newEvent.name} in ${newEvent.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newEvent.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EVENTS_DESCRIPTION_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EVENTS_DESCRIPTION_UPDATE',
                user: 'System',
                userId: null,
                action: '<PERSON><PERSON> tả sự kiện đượ<PERSON> cập nhật',
                details: `<PERSON><PERSON> tả của sự kiện **${newEvent.name}** đã được cập nhật`,
                target: newEvent.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the event description from audit logs
            try {
                const auditLogs = await newEvent.guild.fetchAuditLogs({
                    type: 102, // GUILD_SCHEDULED_EVENT_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newEvent.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Mô tả sự kiện được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for event description update');
            }
            
            // Add to database
            await client.db.addAuditLog(newEvent.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📝 Mô tả sự kiện được cập nhật',
                `Vừa có mô tả sự kiện được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get event entity type names
            const entityTypes = {
                1: 'Stage Instance',
                2: 'Voice Channel',
                3: 'External Location'
            };
            
            // Get event status names
            const statusTypes = {
                1: 'Scheduled',
                2: 'Active',
                3: 'Completed',
                4: 'Canceled'
            };
            
            embed.addFields([
                {
                    name: '> Tên sự kiện',
                    value: `- ${newEvent.name}`,
                    inline: true
                },
                {
                    name: '> ID sự kiện',
                    value: `- ${newEvent.id}`,
                    inline: true
                },
                {
                    name: '> Loại sự kiện',
                    value: `- ${entityTypes[newEvent.entityType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${statusTypes[newEvent.status] || 'Unknown'}`,
                    inline: true
                }
            ]);
            
            // Add description comparison
            const oldDescription = oldEvent.description || 'Không có mô tả';
            const newDescription = newEvent.description || 'Không có mô tả';
            
            // Truncate descriptions if too long
            const maxLength = 500;
            const truncatedOldDesc = oldDescription.length > maxLength 
                ? oldDescription.substring(0, maxLength) + '...' 
                : oldDescription;
            const truncatedNewDesc = newDescription.length > maxLength 
                ? newDescription.substring(0, maxLength) + '...' 
                : newDescription;
            
            embed.addFields([
                {
                    name: '> Mô tả cũ',
                    value: `\`\`\`${truncatedOldDesc}\`\`\``,
                    inline: false
                },
                {
                    name: '> Mô tả mới',
                    value: `\`\`\`${truncatedNewDesc}\`\`\``,
                    inline: false
                }
            ]);
            
            // Add event timing info
            if (newEvent.scheduledStartTimestamp) {
                embed.addFields({
                    name: '> Thời gian bắt đầu',
                    value: `- <t:${Math.floor(newEvent.scheduledStartTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            if (newEvent.scheduledEndTimestamp) {
                embed.addFields({
                    name: '> Thời gian kết thúc',
                    value: `- <t:${Math.floor(newEvent.scheduledEndTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add creator info if available
            if (newEvent.creator) {
                embed.addFields({
                    name: '> Người tạo sự kiện',
                    value: `- ${newEvent.creator.tag}`,
                    inline: true
                });
            }
            
            // Add subscriber count if available
            if (newEvent.userCount !== null) {
                embed.addFields({
                    name: '> Số người quan tâm',
                    value: `- ${newEvent.userCount} người`,
                    inline: true
                });
            }
            
            // Add impact explanation based on description change
            if (newDescription !== 'Không có mô tả' && oldDescription === 'Không có mô tả') {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Sự kiện giờ đây có mô tả chi tiết',
                        '• Thành viên hiểu rõ hơn về nội dung',
                        '• Tăng khả năng thu hút người tham gia',
                        '• Cung cấp thông tin cần thiết'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for added description
            } else if (newDescription === 'Không có mô tả' && oldDescription !== 'Không có mô tả') {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Sự kiện không còn mô tả',
                        '• Có thể gây nhầm lẫn cho thành viên',
                        '• Giảm thông tin về sự kiện',
                        '• Có thể ảnh hưởng đến lượng người tham gia'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0xe74c3c); // Red for removed description
            } else if (newDescription !== oldDescription) {
                embed.addFields({
                    name: '> 🔄 Tác động',
                    value: [
                        '• Thông tin sự kiện đã được cập nhật',
                        '• Thành viên có thông tin mới nhất',
                        '• Có thể thay đổi kỳ vọng về sự kiện',
                        '• Cần thông báo cho người quan tâm'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add content analysis
            const contentChanges = [];
            if (oldDescription.length !== newDescription.length) {
                const lengthDiff = newDescription.length - oldDescription.length;
                contentChanges.push(`Độ dài: ${lengthDiff > 0 ? '+' : ''}${lengthDiff} ký tự`);
            }
            
            if (contentChanges.length > 0) {
                embed.addFields({
                    name: '> 📊 Thay đổi',
                    value: `- ${contentChanges.join('\n- ')}`,
                    inline: false
                });
            }
            
            // Add description guidelines
            embed.addFields({
                name: '> 💡 Lưu ý về mô tả sự kiện',
                value: [
                    '• Mô tả nên rõ ràng và hấp dẫn',
                    '• Bao gồm thông tin quan trọng về sự kiện',
                    '• Có thể sử dụng markdown để format',
                    '• Cập nhật kịp thời khi có thay đổi'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set event image as thumbnail if available
            if (newEvent.coverImageURL()) {
                embed.setThumbnail(newEvent.coverImageURL({ size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newEvent.guild.name} • Event Description Update`,
                iconURL: newEvent.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Event description update logged: ${newEvent.name}`);
            
        } catch (error) {
            console.error('Error in eventsDescriptionUpdate audit log:', error);
        }
    }
};
