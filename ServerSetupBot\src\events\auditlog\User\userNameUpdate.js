const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildMemberUpdate,
    async execute(oldMember, newMember, client) {
        try {
            // Only handle nickname changes
            if (oldMember.nickname === newMember.nickname) return;
            
            console.log(`✏️ Member nickname updated: ${newMember.user.tag} in ${newMember.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newMember.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'USER_NAME_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'USER_NAME_UPDATE',
                user: 'System',
                userId: null,
                action: 'Nickname thành viên được thay đổi',
                details: `Nickname của **${newMember.user.tag}** đã được thay đổi`,
                target: newMember.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the nickname from audit logs
            try {
                const auditLogs = await newMember.guild.fetchAuditLogs({
                    type: 24, // MEMBER_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newMember.user.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Nickname được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for nickname update');
            }
            
            // Add to database
            await client.db.addAuditLog(newMember.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Nickname thành viên thay đổi',
                `Vừa có nickname thành viên được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${newMember.user.tag} (${newMember.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newMember.user.id}`,
                    inline: true
                },
                {
                    name: '> Người thực hiện',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add nickname changes
            const oldNickname = oldMember.nickname || '*Không có nickname*';
            const newNickname = newMember.nickname || '*Không có nickname*';
            
            embed.addFields([
                {
                    name: '> Nickname cũ',
                    value: `- ${oldNickname}`,
                    inline: true
                },
                {
                    name: '> Nickname mới',
                    value: `- ${newNickname}`,
                    inline: true
                },
                {
                    name: '> Username gốc',
                    value: `- ${newMember.user.username}`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set user avatar as thumbnail
            if (newMember.user.displayAvatarURL()) {
                embed.setThumbnail(newMember.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newMember.guild.name} • User Nickname Update`,
                iconURL: newMember.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ User nickname update logged: ${newMember.user.tag}`);
            
        } catch (error) {
            console.error('Error in userNameUpdate audit log:', error);
        }
    }
};
