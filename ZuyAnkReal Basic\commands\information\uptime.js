const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'uptime',
    description: 'Shows the bot\'s uptime in relative time format.',
    execute(message) {
        const startTime = Date.now() - message.client.uptime;
        const unixTimestamp = Math.floor(startTime / 1000);
        
        const uptimeEmbed = new EmbedBuilder()
            .setColor('#00FFAA')
            .setDescription(`I am online since <t:${unixTimestamp}:R>`);

        message.channel.send({ embeds: [uptimeEmbed] });
    }
};