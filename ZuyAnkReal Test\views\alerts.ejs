<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alerts - Drako Development</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --bg-primary: #1a1a1a;
            --bg-secondary: #2c2c2c;
            --text-primary: #e0e0e0;
            --text-secondary: #a0a0a0;
            --accent: #3b82f6;
            --accent-hover: #60a5fa;
        }
        body, html {
            font-family: 'Inter', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }
        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 2rem;
            text-align: center;
        }
        .alerts-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 0.5rem;
            margin-bottom: 2rem;
        }
        .alerts-table th, .alerts-table td {
            padding: 0.75rem;
            text-align: left;
        }
        .alerts-table th {
            background-color: var(--bg-secondary);
            font-weight: 600;
        }
        .alerts-table tr {
            background-color: var(--bg-secondary);
            transition: transform 0.2s ease-in-out;
        }
        .alerts-table tr:hover {
            transform: translateY(-3px);
        }
        .button {
            background-color: var(--accent);
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-size: 0.9rem;
            text-decoration: none;
            transition: background-color 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .button:hover {
            background-color: var(--accent-hover);
        }
        .form-container {
            background-color: var(--bg-secondary);
            padding: 2rem;
            border-radius: 8px;
            margin-top: 2rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 0.5rem;
            border-radius: 4px;
            border: 1px solid var(--text-secondary);
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: var(--accent);
        }
        .hidden {
            display: none;
        }
        /* Adjust the body to account for the fixed nav */
        body {
            padding-top: 70px;
        }
    </style>
</head>
<body>
    <%- include('partials/nav', {currentPage: 'alerts'}) %>

    <div class="container">
        <h1>Alerts Management</h1>
        <table class="alerts-table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Address</th>
                    <th>Type</th>
                    <th>Status</th>
                    <th>Last Checked</th>
                    <th>Notification Channel</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <% alerts.forEach(alert => { %>
                    <tr>
                        <td><%= alert.name %></td>
                        <td><%= alert.address %></td>
                        <td><%= alert.type %></td>
                        <td><%= alert.status === 'Pending' ? 'Pending' : alert.status %></td>
                        <td><%= new Date(alert.lastChecked).toLocaleString() %></td>
                        <td><%= getChannelName(guilds, alert.notificationChannel) %></td>
                        <td>
                            <form action="/alerts/<%= alert._id %>/status" method="post" style="display: inline;">
                                <button class="button" type="submit">Check</button>
                            </form>
                            <form action="/alerts/<%= alert._id %>/delete" method="post" style="display: inline;">
                                <button class="button" type="submit">Delete</button>
                            </form>
                        </td>
                    </tr>
                <% }) %>
            </tbody>
        </table>
        
        <div class="form-container">
            <h2>Add New Alert</h2>
            <form action="/alerts" method="post" id="alertForm">
                <div class="form-group">
                    <label for="name">Alert Name</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="type">Alert Type</label>
                    <select id="type" name="type" required>
                        <option value="">Select a type</option>
                        <option value="Website">Website</option>
                        <option value="Minecraft">Minecraft</option>
                        <option value="Bots">Bots</option>
                        <option value="Pterodactyl">Pterodactyl</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="address">Address</label>
                    <input type="text" id="address" name="address" required>
                </div>
                <div class="form-group hidden" id="pterodactylIdGroup">
                    <label for="pterodactylId">Pterodactyl ID</label>
                    <input type="text" id="pterodactylId" name="pterodactylId">
                </div>
                <div class="form-group hidden" id="apiKeyGroup">
                    <label for="apiKey">API Key</label>
                    <input type="text" id="apiKey" name="apiKey">
                </div>
                <div class="form-group">
                    <label for="notificationChannel">Notification Channel</label>
                    <select id="notificationChannel" name="notificationChannel" required>
                        <option value="">Select a channel</option>
                        <% guilds.forEach(guild => { %>
                            <optgroup label="<%= guild.name %>">
                                <% guild.channels.forEach(channel => { %>
                                    <option value="<%= channel.id %>"><%= channel.name %></option>
                                <% }); %>
                            </optgroup>
                        <% }); %>
                    </select>
                </div>
                <button type="submit" class="button">Add Alert</button>
            </form>
        </div>
    </div>

    <script>
        document.getElementById('type').addEventListener('change', function() {
            const pterodactylIdGroup = document.getElementById('pterodactylIdGroup');
            const apiKeyGroup = document.getElementById('apiKeyGroup');
            const addressInput = document.getElementById('address');
            
            if (this.value === 'Pterodactyl') {
                pterodactylIdGroup.classList.remove('hidden');
                apiKeyGroup.classList.remove('hidden');
            } else {
                pterodactylIdGroup.classList.add('hidden');
                apiKeyGroup.classList.add('hidden');
            }

            if (this.value === 'Minecraft') {
                addressInput.placeholder = 'e.g., play.example.com or ***********:25565';
            } else if (this.value === 'Website' || this.value === 'Bots') {
                addressInput.placeholder = 'e.g., https://example.com or ***********:8080';
            } else {
                addressInput.placeholder = '';
            }
        });

        document.getElementById('alertForm').addEventListener('submit', function(event) {
            const address = document.getElementById('address').value.trim();
            const type = document.getElementById('type').value;
            
            let isValid = false;

            if (type === 'Minecraft') {
                isValid = /^[a-zA-Z0-9.-]+(\.[a-zA-Z]{2,})?$/.test(address) || 
                          /^(\d{1,3}\.){3}\d{1,3}(:\d{1,5})?$/.test(address);
            } else {
                isValid = /^(http:\/\/|https:\/\/)/.test(address) || 
                          /^(\d{1,3}\.){3}\d{1,3}:\d{1,5}$/.test(address);
            }
            
            if (!isValid) {
                event.preventDefault();
                if (type === 'Minecraft') {
                    alert('Please enter a valid Minecraft server address (e.g., play.example.com or ***********:25565).');
                } else {
                    alert('Please enter a valid URL (starting with http:// or https://) or a valid IP:PORT combination.');
                }
            }
        });
    </script>
</body>
</html>