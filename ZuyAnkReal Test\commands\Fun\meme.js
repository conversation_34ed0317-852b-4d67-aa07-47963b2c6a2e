(function(_0x4d0976,_0x107637){const _0x189c05=_0x4d0976();function _0x89d53e(_0x31ed61,_0x520ab1){return _0x5b7f(_0x31ed61- -0x21f,_0x520ab1);}while(!![]){try{const _0x157370=parseInt(_0x89d53e(-0x142,-0x156))/0x1*(-parseInt(_0x89d53e(-0x157,-0x141))/0x2)+parseInt(_0x89d53e(-0x136,-0x165))/0x3+-parseInt(_0x89d53e(-0x17f,-0x18f))/0x4+-parseInt(_0x89d53e(-0x184,-0x166))/0x5*(parseInt(_0x89d53e(-0x13c,-0x15a))/0x6)+-parseInt(_0x89d53e(-0x141,-0x146))/0x7+parseInt(_0x89d53e(-0x135,-0x165))/0x8+-parseInt(_0x89d53e(-0x16d,-0x183))/0x9*(-parseInt(_0x89d53e(-0x158,-0x163))/0xa);if(_0x157370===_0x107637)break;else _0x189c05['push'](_0x189c05['shift']());}catch(_0x3cce43){_0x189c05['push'](_0x189c05['shift']());}}}(_0x1f2f,0xa6508));const {EmbedBuilder,SlashCommandBuilder}=require('discord.js'),fs=require('fs'),yaml=require(_0xb6f887(0x246,0x240)),fetch=require(_0xb6f887(0x248,0x22b)),config=yaml[_0xb6f887(0x25b,0x228)](fs['readFileSync'](_0xb6f887(0x24a,0x231),_0xb6f887(0x296,0x2ba))),lang=yaml[_0xb6f887(0x25b,0x26c)](fs[_0xb6f887(0x239,0x257)](_0xb6f887(0x2a2,0x2b4),_0xb6f887(0x296,0x282)));function _0xb6f887(_0x12d73a,_0x23a424){return _0x5b7f(_0x12d73a-0x1a7,_0x23a424);}function _0x5b7f(_0x3e6318,_0x5e4e35){const _0x1f2f3b=_0x1f2f();return _0x5b7f=function(_0x5b7f06,_0x2cd123){_0x5b7f06=_0x5b7f06-0x92;let _0x4857ea=_0x1f2f3b[_0x5b7f06];return _0x4857ea;},_0x5b7f(_0x3e6318,_0x5e4e35);}const templates=[{'name':'Ancient\x20Aliens\x20Guy','value':_0xb6f887(0x26c,0x290)},{'name':_0xb6f887(0x27c,0x298),'value':_0xb6f887(0x28b,0x261)},{'name':'Afraid\x20to\x20Ask\x20Andy','value':_0xb6f887(0x23f,0x22f)},{'name':_0xb6f887(0x277,0x286),'value':'agnes'},{'name':_0xb6f887(0x23b,0x25c),'value':'aint-got-time'},{'name':_0xb6f887(0x29d,0x28f),'value':'ams'},{'name':_0xb6f887(0x23d,0x21d),'value':_0xb6f887(0x267,0x29a)},{'name':'Almost\x20Politically\x20Correct\x20Redneck','value':'apcr'},{'name':'Always\x20Has\x20Been','value':_0xb6f887(0x28e,0x2a5)},{'name':'And\x20Then\x20I\x20Said','value':'atis'},{'name':_0xb6f887(0x27a,0x27f),'value':_0xb6f887(0x256,0x255)},{'name':_0xb6f887(0x257,0x282),'value':'awesome'},{'name':_0xb6f887(0x271,0x2a6),'value':_0xb6f887(0x255,0x256)},{'name':'Socially\x20Awkward\x20Penguin','value':_0xb6f887(0x245,0x277)},{'name':_0xb6f887(0x289,0x277),'value':_0xb6f887(0x292,0x29d)},{'name':'You\x20Should\x20Feel\x20Bad','value':'bad'},{'name':_0xb6f887(0x240,0x222),'value':'badchoice'},{'name':'Butthurt\x20Dweller','value':'bd'},{'name':'Men\x20in\x20Black','value':'because'},{'name':_0xb6f887(0x24c,0x26c),'value':_0xb6f887(0x293,0x2c0)},{'name':_0xb6f887(0x28c,0x261),'value':_0xb6f887(0x28f,0x265)},{'name':_0xb6f887(0x249,0x244),'value':_0xb6f887(0x23c,0x22e)},{'name':_0xb6f887(0x28d,0x2c2),'value':_0xb6f887(0x253,0x21f)},{'name':'Bad\x20Luck\x20Brian','value':'blb'},{'name':_0xb6f887(0x281,0x253),'value':_0xb6f887(0x24e,0x27c)}];function convertSimplePatternToRegex(_0x21b5f0){let _0x75e7a1=_0x21b5f0[_0xca9653(0x107,0x10b)](/\./g,'\x5c.')['replace'](/\*/g,'.*');function _0xca9653(_0x466874,_0x320b86){return _0xb6f887(_0x320b86- -0x17c,_0x466874);}return new RegExp('^'+_0x75e7a1+'$','i');}async function checkBlacklistWords(_0x290b41){const _0x1f89fe=config['BlacklistWords'][_0x27ae84(0x405,0x43b)][_0x27ae84(0x42c,0x445)](_0x1adab0=>convertSimplePatternToRegex(_0x1adab0));function _0x27ae84(_0x297785,_0x331624){return _0xb6f887(_0x331624-0x1d2,_0x297785);}return _0x1f89fe[_0x27ae84(0x46d,0x43f)](_0x7da17d=>_0x7da17d['test'](_0x290b41));}module['exports']={'data':new SlashCommandBuilder()[_0xb6f887(0x26a,0x23f)]('meme')[_0xb6f887(0x262,0x23a)](_0xb6f887(0x297,0x2c8))[_0xb6f887(0x274,0x26a)](_0x5b19cc=>_0x5b19cc[_0xb6f887(0x26a,0x27e)](_0xb6f887(0x252,0x251))[_0xb6f887(0x262,0x25d)]('Get\x20a\x20random\x20meme\x20from\x20meme-api.com'))[_0xb6f887(0x274,0x247)](_0x252988=>_0x252988['setName']('text')[_0xb6f887(0x262,0x290)](_0xb6f887(0x258,0x26a))[_0xb6f887(0x286,0x267)](_0x528bbd=>_0x528bbd[_0xb6f887(0x26a,0x28c)](_0xb6f887(0x295,0x261))[_0xb6f887(0x262,0x238)](_0xb6f887(0x265,0x272))[_0xb6f887(0x272,0x269)](!![])[_0xb6f887(0x29a,0x2bb)](...templates))[_0xb6f887(0x286,0x277)](_0x3ad99a=>_0x3ad99a[_0xb6f887(0x26a,0x248)](_0xb6f887(0x23e,0x227))[_0xb6f887(0x262,0x23a)](_0xb6f887(0x24d,0x26c))[_0xb6f887(0x272,0x28f)](!![]))[_0xb6f887(0x286,0x279)](_0x36ffa7=>_0x36ffa7[_0xb6f887(0x26a,0x250)](_0xb6f887(0x266,0x28b))[_0xb6f887(0x262,0x251)](_0xb6f887(0x294,0x2c3))[_0xb6f887(0x272,0x23c)](![])))[_0xb6f887(0x274,0x250)](_0x26afa9=>_0x26afa9['setName']('sadcat')[_0xb6f887(0x262,0x26c)]('Generate\x20a\x20sad\x20cat\x20image\x20with\x20your\x20text')[_0xb6f887(0x286,0x2af)](_0x884a97=>_0x884a97['setName'](_0xb6f887(0x275,0x29c))[_0xb6f887(0x262,0x234)](_0xb6f887(0x29f,0x290))[_0xb6f887(0x272,0x299)](!![]))),'category':'Fun',async 'execute'(_0x2d3deb){const _0xf2c925=_0x2d3deb[_0x2bcf85(0x270,0x255)][_0x2bcf85(0x26b,0x255)]();function _0x2bcf85(_0x46c7dd,_0x1bceb9){return _0xb6f887(_0x46c7dd- -0xe,_0x1bceb9);}if(_0xf2c925==='random')await _0x2d3deb['deferReply'](),fetch('https://meme-api.com/gimme')[_0x2bcf85(0x262,0x26c)](_0x4f97b6=>{function _0x4781d4(_0x4b9e27,_0x30425a){return _0x2bcf85(_0x4b9e27- -0x3c7,_0x30425a);}if(!_0x4f97b6['ok'])throw new Error('Failed\x20to\x20fetch\x20meme,\x20meme-api\x20might\x20be\x20down\x20or\x20busy.');return _0x4f97b6[_0x4781d4(-0x186,-0x18f)]();})[_0x2bcf85(0x262,0x256)](_0x41fee1=>{const _0x518b15=_0x41fee1[_0x48698f(0x132,0x138)],_0x36b6d8=_0x41fee1[_0x48698f(0x16a,0x14c)],_0x48db64=_0x41fee1[_0x48698f(0x12c,0x12e)],_0xd9b59=_0x41fee1[_0x48698f(0x173,0x169)];function _0x48698f(_0x31bf35,_0x57d7db){return _0x2bcf85(_0x31bf35- -0x120,_0x57d7db);}const _0x5e6760=_0x41fee1[_0x48698f(0x13d,0x107)],_0x229934=_0x41fee1[_0x48698f(0x154,0x156)],_0x1bd5aa=new EmbedBuilder()[_0x48698f(0x12e,0x130)](_0x48698f(0x12f,0x15a)+_0x48db64)['setURL'](_0x518b15)[_0x48698f(0x175,0x166)](_0x36b6d8)['setColor'](config['EmbedColors'])[_0x48698f(0x176,0x1a9)]({'name':'👍\x20Upvotes','value':''+_0xd9b59,'inline':!![]},{'name':'👤\x20Author','value':''+_0x5e6760,'inline':!![]},{'name':_0x48698f(0x116,0xe4),'value':''+_0x229934,'inline':!![]})['setFooter']({'text':'Enjoy\x20your\x20meme!'});_0x2d3deb[_0x48698f(0x152,0x16a)]({'embeds':[_0x1bd5aa]});})[_0x2bcf85(0x25a,0x231)](_0x2fd5c9=>{console['error'](_0x38a6bb(0x30d,0x33f),_0x2fd5c9);function _0x38a6bb(_0x2160fe,_0x194915){return _0x2bcf85(_0x2160fe-0xa0,_0x194915);}_0x2d3deb[_0x38a6bb(0x312,0x310)]({'content':'Sorry,\x20I\x20couldn\x27t\x20fetch\x20a\x20meme\x20at\x20the\x20moment.\x20Please\x20try\x20again\x20later.'});});else{if(_0xf2c925===_0x2bcf85(0x267,0x289)){await _0x2d3deb[_0x2bcf85(0x250,0x224)]();const _0x4bbc10=_0x2d3deb[_0x2bcf85(0x270,0x286)][_0x2bcf85(0x268,0x27e)]('template'),_0x48dd4f=_0x2d3deb['options'][_0x2bcf85(0x268,0x24e)](_0x2bcf85(0x230,0x263)),_0x5d9cc0=_0x2d3deb[_0x2bcf85(0x270,0x292)][_0x2bcf85(0x268,0x24f)]('bottom_text')||'';if(await checkBlacklistWords(_0x48dd4f)||await checkBlacklistWords(_0x5d9cc0)){const _0x35f8b6=lang[_0x2bcf85(0x26f,0x275)]&&lang['BlacklistWords'][_0x2bcf85(0x290,0x29d)]?lang[_0x2bcf85(0x26f,0x283)][_0x2bcf85(0x290,0x29a)]['replace'](/{user}/g,''+_0x2d3deb[_0x2bcf85(0x242,0x225)]):_0x2bcf85(0x28b,0x29a);return _0x2d3deb[_0x2bcf85(0x272,0x257)]({'content':_0x35f8b6,'ephemeral':!![]});}const _0x30b623=_0x2bcf85(0x292,0x2c6)+_0x4bbc10+'/'+encodeURIComponent(_0x48dd4f)+'/'+encodeURIComponent(_0x5d9cc0)+_0x2bcf85(0x233,0x23e);fetch(_0x30b623)[_0x2bcf85(0x262,0x259)](_0x31e6bc=>{if(!_0x31e6bc['ok'])throw new Error(_0x22634b(-0x2cc,-0x300));function _0x22634b(_0x12dcef,_0x25d702){return _0x2bcf85(_0x25d702- -0x56a,_0x12dcef);}return _0x31e6bc['url'];})[_0x2bcf85(0x262,0x244)](_0x35bdf8=>{const _0x40eb08=new EmbedBuilder()[_0x1dba67(0x1ac,0x1c3)](_0x1dba67(0x1a1,0x174))['setImage'](_0x35bdf8)['setColor'](config['EmbedColors'])['setFooter']({'text':_0x1dba67(0x1d3,0x1d2)});function _0x1dba67(_0x2ebbfa,_0x2c078a){return _0x2bcf85(_0x2ebbfa- -0xa2,_0x2c078a);}_0x2d3deb[_0x1dba67(0x1d0,0x1c1)]({'embeds':[_0x40eb08]});})['catch'](_0x188c22=>{console[_0x8500c6(-0x1a7,-0x173)]('Error\x20generating\x20meme:',_0x188c22);function _0x8500c6(_0x55901e,_0x2a8902){return _0x2bcf85(_0x55901e- -0x418,_0x2a8902);}_0x2d3deb['editReply']({'content':_0x8500c6(-0x1c7,-0x1c0),'ephemeral':!![]});});}else{if(_0xf2c925==='sadcat'){await _0x2d3deb[_0x2bcf85(0x250,0x286)]();const _0x2eaf70=_0x2d3deb[_0x2bcf85(0x270,0x28c)][_0x2bcf85(0x268,0x237)](_0x2bcf85(0x267,0x27d)),_0x56ae86=()=>{const _0x4a46a2=_0x16074a(-0x85,-0xb0);let _0x572644='#';for(let _0x404f1f=0x0;_0x404f1f<0x6;_0x404f1f++){_0x572644+=_0x4a46a2[Math['floor'](Math[_0x16074a(-0xcd,-0xb2)]()*0x10)];}function _0x16074a(_0x1a0476,_0x8871fb){return _0x2bcf85(_0x8871fb- -0x2f6,_0x1a0476);}return _0x572644;};fetch(_0x2bcf85(0x27a,0x290)+encodeURIComponent(_0x2eaf70))[_0x2bcf85(0x262,0x280)](_0x6d3ed2=>{function _0x5698bf(_0x37a353,_0x38f17e){return _0x2bcf85(_0x37a353-0x31,_0x38f17e);}if(!_0x6d3ed2['ok'])throw new Error(_0x5698bf(0x25d,0x258));return _0x6d3ed2['buffer']();})['then'](_0x4f1f6e=>{function _0x25ae34(_0x3bd7c0,_0x443aef){return _0x2bcf85(_0x3bd7c0-0x35,_0x443aef);}const _0x8bd903=new EmbedBuilder()[_0x25ae34(0x283,0x2a7)](_0x25ae34(0x288,0x28a))[_0x25ae34(0x2ca,0x2fb)](_0x25ae34(0x28a,0x281))[_0x25ae34(0x2c2,0x28e)](_0x56ae86())[_0x25ae34(0x26a,0x28a)]({'text':_0x25ae34(0x28b,0x271)});_0x2d3deb[_0x25ae34(0x2a7,0x2b5)]({'embeds':[_0x8bd903],'files':[{'attachment':_0x4f1f6e,'name':'sadcat.png'}]});})['catch'](_0x5c61f9=>{function _0x142508(_0x88ce25,_0x8fb822){return _0x2bcf85(_0x8fb822-0x230,_0x88ce25);}console[_0x142508(0x4ac,0x4a1)](_0x142508(0x4ab,0x4be),_0x5c61f9),_0x2d3deb['editReply']({'content':_0x142508(0x449,0x46d)});});}}}}};function _0x1f2f(){const _0x428714=['BlacklistWords','options','error','editReply','I\x20Should\x20Buy\x20a\x20Boat\x20Cat','subreddit','Enjoy\x20your\x20meme!','2MxAevy','9202431ZLPFnC','addStringOption','replace','https://api.popcat.xyz/sadcat?text=','Socially\x20Awkward\x20Awesome\x20Penguin','283290EPrnfI','ackbar','But\x20It\x27s\x20Honest\x20Work','Baby\x20Insanity\x20Wolf','astronaut','bihw','720810ElWBan','3368dTpqDZ','awkward-awesome','bender','The\x20bottom\x20text\x20for\x20the\x20meme','template','utf8','Generate\x20and\x20get\x20memes.','url','Your\x20text\x20contains\x20blacklisted\x20words.','addChoices','setColor','Error\x20generating\x20sad\x20cat\x20image:','Awkward\x20Moment\x20Seal','Message','The\x20text\x20to\x20display\x20on\x20the\x20sad\x20cat\x20image','https://api.memegen.link/images/','ups','./lang.yml','setImage','addFields','readFileSync','Failed\x20to\x20generate\x20sad\x20cat\x20image,\x20API\x20might\x20be\x20down\x20or\x20busy.','Sweet\x20Brown','bilbo','Do\x20You\x20Want\x20Ants?','top_text','afraid','Milk\x20Was\x20a\x20Bad\x20Choice','.png','95QmowwX','setFooter','📂\x20Subreddit','awkward','js-yaml','257008wAVewv','node-fetch','Why\x20Shouldn\x27t\x20I\x20Keep\x20It','./config.yml','Sorry,\x20I\x20couldn\x27t\x20generate\x20a\x20sad\x20cat\x20image\x20at\x20the\x20moment.\x20Please\x20try\x20again\x20later.','I\x27m\x20Going\x20to\x20Build\x20My\x20Own\x20Theme\x20Park','The\x20top\x20text\x20for\x20the\x20meme','boat','json','user','😂\x20Meme','random','biw','0123456789ABCDEF','awesome-awkward','away','Socially\x20Awesome\x20Penguin','Generate\x20a\x20meme\x20with\x20text','33706161YaXggP','title','load','setTitle','🤣\x20','deferReply','Sorry,\x20I\x20couldn\x27t\x20generate\x20a\x20meme\x20at\x20the\x20moment.\x20Please\x20try\x20again\x20later.','postLink','😿\x20Sad\x20Cat','setDescription','attachment://sadcat.png','Cheer\x20up!','Choose\x20a\x20meme\x20template','bottom_text','ants','catch','Patterns','setName','author','aag','some','10SIivRr','1028626CurboG','then','Socially\x20Awesome\x20Awkward\x20Penguin','setRequired','map','addSubcommand','text','getString','Agnes\x20Harkness\x20Winking','Failed\x20to\x20generate\x20meme,\x20memegen\x20API\x20might\x20be\x20down\x20or\x20busy.','getSubcommand','Life...\x20Finds\x20a\x20Way','Error\x20fetching\x20meme:','It\x27s\x20A\x20Trap!'];_0x1f2f=function(){return _0x428714;};return _0x1f2f();}