const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildRoleDelete,
    async execute(role, client) {
        try {
            console.log(`🗑️ Role deleted: ${role.name} in ${role.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(role.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'ROLE_DELETE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'ROLE_DELETE',
                user: 'System',
                userId: null,
                action: 'Role được xóa',
                details: `Role **${role.name}** đã được xóa`,
                target: role.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the role from audit logs
            try {
                const auditLogs = await role.guild.fetchAuditLogs({
                    type: 32, // ROLE_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === role.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Role được xóa bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for role delete');
            }
            
            // Add to database
            await client.db.addAuditLog(role.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Role được xóa',
                `Vừa có một role được xóa khỏi server`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            embed.addFields([
                {
                    name: '> Tên role',
                    value: `- ${role.name}`,
                    inline: true
                },
                {
                    name: '> ID role',
                    value: `- ${role.id}`,
                    inline: true
                },
                {
                    name: '> Màu sắc',
                    value: `- ${role.hexColor}`,
                    inline: true
                },
                {
                    name: '> Có thể mention',
                    value: `- ${role.mentionable ? 'Có' : 'Không'}`,
                    inline: true
                },
                {
                    name: '> Hiển thị riêng',
                    value: `- ${role.hoist ? 'Có' : 'Không'}`,
                    inline: true
                },
                {
                    name: '> Vị trí',
                    value: `- ${role.position}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add permissions info
            const permissions = role.permissions.toArray();
            if (permissions.length > 0) {
                const permissionsList = permissions.slice(0, 10).join(', ');
                const morePerms = permissions.length > 10 ? ` và ${permissions.length - 10} quyền khác` : '';
                
                embed.addFields({
                    name: '> Quyền hạn đã mất',
                    value: `- ${permissionsList}${morePerms}`,
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${role.guild.name} • Role Delete`,
                iconURL: role.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Role delete logged: ${role.name}`);
            
        } catch (error) {
            console.error('Error in roleDelete audit log:', error);
        }
    }
};
