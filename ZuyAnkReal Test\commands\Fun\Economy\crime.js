(function(_0x24e1c1,_0x365620){function _0xe27616(_0x3cf041,_0x5c3d02){return _0x30e3(_0x3cf041- -0x3b8,_0x5c3d02);}const _0x4a40cc=_0x24e1c1();while(!![]){try{const _0x4c7d01=parseInt(_0xe27616(-0x1e3,-0x1eb))/0x1*(parseInt(_0xe27616(-0x1ed,-0x1f2))/0x2)+-parseInt(_0xe27616(-0x206,-0x204))/0x3*(parseInt(_0xe27616(-0x1ec,-0x1d8))/0x4)+parseInt(_0xe27616(-0x1e0,-0x1cf))/0x5+parseInt(_0xe27616(-0x209,-0x1f6))/0x6+-parseInt(_0xe27616(-0x1dd,-0x1cf))/0x7+-parseInt(_0xe27616(-0x1fc,-0x215))/0x8*(parseInt(_0xe27616(-0x1dc,-0x1ed))/0x9)+-parseInt(_0xe27616(-0x1e2,-0x1d2))/0xa*(-parseInt(_0xe27616(-0x1f2,-0x1ef))/0xb);if(_0x4c7d01===_0x365620)break;else _0x4a40cc['push'](_0x4a40cc['shift']());}catch(_0x5927af){_0x4a40cc['push'](_0x4a40cc['shift']());}}}(_0x4dd1,0x2ddb7));const {SlashCommandBuilder,EmbedBuilder}=require(_0x46067e(-0x16,-0xe)),User=require(_0x46067e(-0x27,-0x26)),fs=require('fs'),yaml=require(_0x46067e(-0x1d,-0x21)),config=yaml[_0x46067e(-0x13,-0x2a)](fs[_0x46067e(-0x21,-0x20)](_0x46067e(-0x33,-0x30),_0x46067e(-0x3a,-0x3d))),lang=yaml[_0x46067e(-0x13,0x0)](fs[_0x46067e(-0x21,-0xf)]('./lang.yml',_0x46067e(-0x3a,-0x21)));function _0x46067e(_0x344220,_0x5d768e){return _0x30e3(_0x344220- -0x1ea,_0x5d768e);}function _0x30e3(_0x662fb4,_0x237c21){const _0x4dd19d=_0x4dd1();return _0x30e3=function(_0x30e3a4,_0x12ce3c){_0x30e3a4=_0x30e3a4-0x1af;let _0x1fa8d5=_0x4dd19d[_0x30e3a4];return _0x1fa8d5;},_0x30e3(_0x662fb4,_0x237c21);}const parseDuration=require('./Utility/parseDuration'),{checkActiveBooster,replacePlaceholders}=require('./Utility/helpers');function _0x4dd1(){const _0xb833e=['setName','8uFuRFv','floor','setColor','min','user','footer','cooldown','../../../models/UserData','setTitle','random','14069YTTXGM','error','reply','readFileSync','guild','26teyRfJ','260016WUxgws','js-yaml','exports','Messages','max','Attempt\x20to\x20commit\x20a\x20crime','transactionLogs','Lose','discord.js','22741nOMmNo','3140UNhTaw','load','167330ykZKtV','lastCrime','Title','1429379DuZREn','2873358dfBFNQ','Economy','Crime','push','Win','setDescription','crime','663558suasmC','utf8','getTime','6FETYuf','balance','lose','#FF0000','save','./config.yml','setFooter','commandData','Games'];_0x4dd1=function(){return _0xb833e;};return _0x4dd1();}module[_0x46067e(-0x1c,-0x21)]={'data':new SlashCommandBuilder()[_0x46067e(-0x2f,-0x1e)](_0x46067e(-0x8,-0xf))[_0x46067e(-0x9,-0x19)](_0x46067e(-0x19,-0x3)),'category':_0x46067e(-0xd,-0x1),async 'execute'(_0x1ba3e6){function _0x69eb61(_0x164afc,_0x115975){return _0x46067e(_0x164afc-0x59a,_0x115975);}try{let _0x48dc1e=await User['findOne']({'userId':_0x1ba3e6[_0x69eb61(0x570,0x583)]['id'],'guildId':_0x1ba3e6[_0x69eb61(0x57a,0x57f)]['id']},{'balance':0x1,'commandData.lastCrime':0x1,'transactionLogs':0x1,'boosters':0x1});const _0x4626d8=new Date(),_0x198b05=parseDuration(config['Economy'][_0x69eb61(0x58e,0x574)]['cooldown']);if(_0x48dc1e&&_0x48dc1e[_0x69eb61(0x569,0x582)][_0x69eb61(0x589,0x58f)]){const _0x1f9694=new Date(_0x48dc1e[_0x69eb61(0x569,0x572)][_0x69eb61(0x589,0x59f)][_0x69eb61(0x561,0x56d)]()+_0x198b05);if(_0x4626d8<_0x1f9694){const _0x1326aa=new EmbedBuilder()[_0x69eb61(0x591,0x590)](replacePlaceholders(lang[_0x69eb61(0x58d,0x59c)][_0x69eb61(0x57f,0x56a)][_0x69eb61(0x572,0x577)],{'nextUse':Math[_0x69eb61(0x56d,0x55e)](_0x1f9694[_0x69eb61(0x561,0x548)]()/0x3e8)}))['setColor']('#FF0000')[_0x69eb61(0x568,0x560)]({'text':replacePlaceholders(lang[_0x69eb61(0x58d,0x595)][_0x69eb61(0x57f,0x572)][_0x69eb61(0x571,0x577)],{'balance':_0x48dc1e['balance']})});return _0x1ba3e6[_0x69eb61(0x578,0x590)]({'embeds':[_0x1326aa]});}}let _0x14efe4=Math[_0x69eb61(0x56d,0x566)](Math[_0x69eb61(0x575,0x56a)]()*(config['Economy'][_0x69eb61(0x58e,0x57b)][_0x69eb61(0x580,0x57d)]-config[_0x69eb61(0x58d,0x594)]['Crime'][_0x69eb61(0x56f,0x560)]+0x1))+config[_0x69eb61(0x58d,0x575)][_0x69eb61(0x58e,0x577)][_0x69eb61(0x56f,0x588)];const _0x4124da=checkActiveBooster(_0x48dc1e,'Money');_0x14efe4*=_0x4124da;const _0x327a00=_0x14efe4>0x0;!_0x48dc1e?_0x48dc1e=new User({'userId':_0x1ba3e6[_0x69eb61(0x570,0x560)]['id'],'guildId':_0x1ba3e6[_0x69eb61(0x57a,0x56a)]['id'],'balance':_0x327a00?_0x14efe4:0x0,'commandData':{'lastCrime':_0x4626d8},'transactionLogs':[]}):(_0x48dc1e['balance']+=_0x14efe4,_0x48dc1e[_0x69eb61(0x563,0x54a)]<0x0&&(_0x48dc1e[_0x69eb61(0x563,0x550)]=0x0),_0x48dc1e[_0x69eb61(0x569,0x581)]['lastCrime']=_0x4626d8);_0x48dc1e[_0x69eb61(0x582,0x599)][_0x69eb61(0x58f,0x595)]({'type':'crime','amount':_0x14efe4,'timestamp':_0x4626d8}),await _0x48dc1e[_0x69eb61(0x566,0x560)]();const _0x8774b={'user':'<@'+_0x1ba3e6[_0x69eb61(0x570,0x564)]['id']+'>','balance':Math['abs'](_0x14efe4)},_0x3fa857=replacePlaceholders(lang[_0x69eb61(0x58d,0x586)][_0x69eb61(0x56a,0x56f)][_0x69eb61(0x58e,0x57e)][_0x69eb61(0x58a,0x587)],{'result':_0x327a00?lang['Economy'][_0x69eb61(0x57f,0x575)]['win']:lang[_0x69eb61(0x58d,0x599)][_0x69eb61(0x57f,0x591)][_0x69eb61(0x564,0x564)]}),_0x515b42=_0x327a00?replacePlaceholders(lang['Economy'][_0x69eb61(0x56a,0x567)][_0x69eb61(0x58e,0x587)]['Win'][Math[_0x69eb61(0x56d,0x567)](Math[_0x69eb61(0x575,0x578)]()*lang['Economy'][_0x69eb61(0x56a,0x574)][_0x69eb61(0x58e,0x58d)][_0x69eb61(0x590,0x58a)]['length'])],_0x8774b):replacePlaceholders(lang[_0x69eb61(0x58d,0x58f)][_0x69eb61(0x56a,0x577)][_0x69eb61(0x58e,0x57c)][_0x69eb61(0x583,0x56d)][Math[_0x69eb61(0x56d,0x587)](Math[_0x69eb61(0x575,0x586)]()*lang[_0x69eb61(0x58d,0x57d)][_0x69eb61(0x56a,0x55c)][_0x69eb61(0x58e,0x589)]['Lose']['length'])],_0x8774b),_0x190cba=new EmbedBuilder()[_0x69eb61(0x574,0x55d)](_0x3fa857)['setDescription'](_0x515b42)['setFooter']({'text':replacePlaceholders(lang[_0x69eb61(0x58d,0x577)]['Messages']['footer'],{'balance':_0x48dc1e['balance']})})[_0x69eb61(0x56e,0x554)](_0x327a00?'#00FF00':_0x69eb61(0x565,0x567));return _0x1ba3e6[_0x69eb61(0x578,0x578)]({'embeds':[_0x190cba]});}catch(_0x39413b){console[_0x69eb61(0x577,0x55e)]('Error\x20in\x20crime\x20command:\x20',_0x39413b),_0x1ba3e6[_0x69eb61(0x578,0x58f)]({'content':lang['Economy'][_0x69eb61(0x57f,0x590)]['error'],'ephemeral':!![]});}}};