(function(_0xd973bc,_0x59fa29){function _0x3deef8(_0x5e039a,_0x4d7ec2){return _0x1981(_0x4d7ec2-0xbb,_0x5e039a);}const _0x5c9fb7=_0xd973bc();while(!![]){try{const _0x2833e8=-parseInt(_0x3deef8(0x16d,0x164))/0x1*(parseInt(_0x3deef8(0x148,0x153))/0x2)+-parseInt(_0x3deef8(0x168,0x160))/0x3*(-parseInt(_0x3deef8(0x14b,0x158))/0x4)+parseInt(_0x3deef8(0x162,0x15f))/0x5*(parseInt(_0x3deef8(0x15c,0x162))/0x6)+parseInt(_0x3deef8(0x14f,0x151))/0x7+-parseInt(_0x3deef8(0x13c,0x14a))/0x8+-parseInt(_0x3deef8(0x15d,0x152))/0x9*(parseInt(_0x3deef8(0x144,0x150))/0xa)+parseInt(_0x3deef8(0x166,0x159))/0xb;if(_0x2833e8===_0x59fa29)break;else _0x5c9fb7['push'](_0x5c9fb7['shift']());}catch(_0x4a1508){_0x5c9fb7['push'](_0x5c9fb7['shift']());}}}(_0x4bb3,0x883cd));function _0x1981(_0xba1e78,_0x32ad46){const _0x4bb3d7=_0x4bb3();return _0x1981=function(_0x1981e1,_0x24e1f4){_0x1981e1=_0x1981e1-0x8e;let _0x1d1d2d=_0x4bb3d7[_0x1981e1];return _0x1d1d2d;},_0x1981(_0xba1e78,_0x32ad46);}function _0x1572bd(_0x229fd6,_0x4c79fe){return _0x1981(_0x229fd6-0x1cf,_0x4c79fe);}function _0x4bb3(){const _0x4066c1=['1308024KANegH','exports','9042OHzRlW','editReply','7321eJkJrm','././config.yml','4801080bRzVUU','setName','js-yaml','discord.js','Fun','error','90QCfEnN','3262063izzWqm','487917OjaSXm','302OFIHgI','setDescription','readFileSync','default','advice','8Mjuspx','13327479XKxnDD','load','././lang.yml','utf8','Get\x20random\x20advice','slip','670dMMfOl'];_0x4bb3=function(){return _0x4066c1;};return _0x4bb3();}const {SlashCommandBuilder}=require(_0x1572bd(0x261,0x254)),fs=require('fs'),yaml=require(_0x1572bd(0x260,0x262)),config=yaml[_0x1572bd(0x26e,0x276)](fs[_0x1572bd(0x269,0x25b)](_0x1572bd(0x25d,0x25a),_0x1572bd(0x270,0x27d))),lang=yaml[_0x1572bd(0x26e,0x27b)](fs[_0x1572bd(0x269,0x25c)](_0x1572bd(0x26f,0x268),_0x1572bd(0x270,0x266)));module[_0x1572bd(0x275,0x26b)]={'data':new SlashCommandBuilder()[_0x1572bd(0x25f,0x26d)]('advice')[_0x1572bd(0x268,0x276)](_0x1572bd(0x271,0x268)),'category':_0x1572bd(0x262,0x267),async 'execute'(_0x2d8cf3,_0x39d9fa){function _0x42715d(_0x182734,_0x11549e){return _0x1572bd(_0x11549e-0x127,_0x182734);}try{await _0x2d8cf3['deferReply']();const _0x4b7b3a=(await import('node-fetch'))[_0x42715d(0x39c,0x391)];let _0x3d28fa=await _0x4b7b3a('http://api.adviceslip.com/advice'),_0x4ad1c7=await _0x3d28fa['json']();_0x2d8cf3['editReply']({'content':_0x4ad1c7[_0x42715d(0x3a5,0x399)][_0x42715d(0x386,0x392)]});}catch(_0x214e30){console[_0x42715d(0x390,0x38a)]('Error\x20fetching\x20advice:\x20',_0x214e30),_0x2d8cf3[_0x42715d(0x3ac,0x39e)]({'content':'Sorry,\x20I\x20couldn\x27t\x20fetch\x20any\x20advice\x20at\x20the\x20moment.'});}}};