const { Client, GatewayIntentBits, Collection } = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('./config/config');
const db = require('./database/database');

// Tạo client Discord
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers
    ]
});

// Collections để lưu commands và cooldowns
client.commands = new Collection();
client.cooldowns = new Collection();

// Load commands
const loadCommands = () => {
    const commandsPath = path.join(__dirname, 'commands');
    if (!fs.existsSync(commandsPath)) {
        fs.mkdirSync(commandsPath, { recursive: true });
        return;
    }

    const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

    for (const file of commandFiles) {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);

        if ('data' in command && 'execute' in command) {
            client.commands.set(command.data.name, command);
            console.log(`✅ Đã load command: ${command.data.name}`);
        } else {
            console.log(`⚠️ Command ${file} thiếu thuộc tính "data" hoặc "execute"`);
        }
    }
};

// Load events
const loadEvents = () => {
    const eventsPath = path.join(__dirname, 'events');
    if (!fs.existsSync(eventsPath)) {
        fs.mkdirSync(eventsPath, { recursive: true });
        return;
    }

    const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));

    for (const file of eventFiles) {
        const filePath = path.join(eventsPath, file);
        const event = require(filePath);

        if (event.once) {
            client.once(event.name, (...args) => event.execute(...args));
        } else {
            client.on(event.name, (...args) => event.execute(...args));
        }
        console.log(`✅ Đã load event: ${event.name}`);
    }
};

// Xử lý lỗi
process.on('unhandledRejection', error => {
    console.error('Unhandled promise rejection:', error);
});

process.on('uncaughtException', error => {
    console.error('Uncaught exception:', error);
    process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🔄 Đang tắt bot...');
    db.close();
    client.destroy();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🔄 Đang tắt bot...');
    db.close();
    client.destroy();
    process.exit(0);
});

// Khởi động bot
const startBot = async () => {
    try {
        console.log('🚀 Đang khởi động Discord Economy Bot...');
        
        // Load commands và events
        loadCommands();
        loadEvents();

        // Đăng nhập Discord
        await client.login(config.discord.token);
        
    } catch (error) {
        console.error('❌ Lỗi khởi động bot:', error);
        process.exit(1);
    }
};

// Bắt đầu
startBot();
