(function(_0x232dcc,_0x5b4d4e){const _0x248a64=_0x232dcc();function _0x30687c(_0x4305c7,_0x10130f){return _0x8cd9(_0x10130f- -0xba,_0x4305c7);}while(!![]){try{const _0x23cf39=-parseInt(_0x30687c(0x75,0x52))/0x1*(parseInt(_0x30687c(0x5b,0x4c))/0x2)+parseInt(_0x30687c(-0x1f,0x8))/0x3+-parseInt(_0x30687c(0x90,0x66))/0x4*(-parseInt(_0x30687c(0x2e,0x5))/0x5)+-parseInt(_0x30687c(0x77,0x55))/0x6+-parseInt(_0x30687c(0x6c,0x44))/0x7*(parseInt(_0x30687c(0x6,0x6))/0x8)+-parseInt(_0x30687c(0x3e,0x49))/0x9+-parseInt(_0x30687c(0x4e,0x48))/0xa*(-parseInt(_0x30687c(0xe,0x17))/0xb);if(_0x23cf39===_0x5b4d4e)break;else _0x248a64['push'](_0x248a64['shift']());}catch(_0xf62e7d){_0x248a64['push'](_0x248a64['shift']());}}}(_0x4b8f,0x9d76c));const {SlashCommandBuilder,ModalBuilder,TextInputBuilder,TextInputStyle,ActionRowBuilder}=require(_0x330a21(0x2a1,0x280)),suggestionActions=require(_0x330a21(0x2e6,0x301)),yaml=require(_0x330a21(0x2aa,0x288));function _0x330a21(_0x30e178,_0x224d21){return _0x8cd9(_0x30e178-0x1dc,_0x224d21);}const fs=require('fs'),path=require(_0x330a21(0x2f7,0x303)),SuggestionBlacklist=require(_0x330a21(0x2b4,0x2a4)),config=yaml[_0x330a21(0x2f5,0x2f8)](fs[_0x330a21(0x2d1,0x2a0)](path[_0x330a21(0x2ef,0x2c2)](__dirname,_0x330a21(0x2d9,0x2c6)),'utf8')),lang=yaml['load'](fs[_0x330a21(0x2d1,0x2d7)](path['resolve'](__dirname,'../..//lang.yml'),_0x330a21(0x2bc,0x29f)));async function openQuestionModal(_0x2a35bf){function _0x201a01(_0x26b5d6,_0x5a2b9e){return _0x330a21(_0x26b5d6- -0x7f,_0x5a2b9e);}try{const _0x1edd25=new ModalBuilder()[_0x201a01(0x24c,0x27b)](_0x201a01(0x27a,0x280))[_0x201a01(0x256,0x272)](lang[_0x201a01(0x259,0x242)][_0x201a01(0x266,0x264)]),_0xbe766b=new TextInputBuilder()['setCustomId'](_0x201a01(0x26a,0x292))[_0x201a01(0x262,0x269)](lang[_0x201a01(0x259,0x27b)][_0x201a01(0x249,0x24d)])[_0x201a01(0x220,0x1f9)](TextInputStyle['Paragraph'])['setRequired'](!![]),_0x3cb56d=new ActionRowBuilder()[_0x201a01(0x27b,0x251)](_0xbe766b);_0x1edd25[_0x201a01(0x27b,0x267)](_0x3cb56d),Object['entries'](config[_0x201a01(0x261,0x281)][_0x201a01(0x221,0x234)])[_0x201a01(0x264,0x250)](([_0x224161,_0x142887])=>{const _0x16f8ac=new TextInputBuilder()[_0x2adc97(0x372,0x37d)](_0x142887['ID'])['setLabel'](_0x142887['Question'])[_0x2adc97(0x352,0x352)](_0x142887[_0x2adc97(0x34f,0x36f)])[_0x2adc97(0x346,0x34c)](_0x142887[_0x2adc97(0x35a,0x344)]===_0x2adc97(0x374,0x36c)?TextInputStyle[_0x2adc97(0x374,0x35c)]:TextInputStyle['Short'])['setRequired'](_0x142887['Required'])[_0x2adc97(0x357,0x378)](_0x142887[_0x2adc97(0x34c,0x32b)]);function _0x2adc97(_0x32f9ec,_0x2202ca){return _0x201a01(_0x32f9ec-0x126,_0x2202ca);}const _0x13200a=new ActionRowBuilder()[_0x2adc97(0x3a1,0x372)](_0x16f8ac);_0x1edd25[_0x2adc97(0x3a1,0x388)](_0x13200a);}),await _0x2a35bf[_0x201a01(0x253,0x222)](_0x1edd25);}catch(_0xad1baa){console[_0x201a01(0x245,0x23b)](_0x201a01(0x27c,0x2aa),_0xad1baa),!_0x2a35bf[_0x201a01(0x274,0x267)]&&await _0x2a35bf[_0x201a01(0x25d,0x281)]({'content':lang[_0x201a01(0x259,0x23e)][_0x201a01(0x228,0x215)],'ephemeral':!![]});}}const command=new SlashCommandBuilder()[_0x330a21(0x2cc,0x2ee)](_0x330a21(0x2e4,0x310))[_0x330a21(0x2c0,0x2e2)]('Manage\x20suggestions'),useQuestionModal=config[_0x330a21(0x2e0,0x2ce)][_0x330a21(0x2f1,0x2e6)];!useQuestionModal?command['addSubcommand'](_0x54207d=>_0x54207d['setName']('create')[_0x330a21(0x2c0,0x2d8)](_0x330a21(0x2db,0x2c0))[_0x330a21(0x2d7,0x2b9)](_0x28bc81=>_0x28bc81[_0x330a21(0x2cc,0x2ed)]('text')[_0x330a21(0x2c0,0x29d)](_0x330a21(0x2bb,0x2e9))[_0x330a21(0x2c2,0x2bc)](!![]))):command[_0x330a21(0x2be,0x29c)](_0x25a439=>_0x25a439[_0x330a21(0x2cc,0x2e8)]('create')[_0x330a21(0x2c0,0x29e)](_0x330a21(0x2db,0x2fe)));function _0x4b8f(){const _0xf1629=['deny','member','BlacklistMessage','reason','Patterns','accept','The\x20suggestion\x20text','utf8','Remove\x20a\x20user\x20from\x20the\x20blacklist','addSubcommand','exports','setDescription','addUserOption','setRequired','SuggestionsDisabled','error','create','replace','blockBlacklistWords','ModalQuestion','SuggestionAcceptDenyRoles','map','setCustomId','setName','Paragraph','roles','user','cache','readFileSync','showModal','some','Manage\x20blacklist','setTitle','Deny\x20a\x20suggestion','addStringOption','Suggestion','../../config.yml','7NdXVkB','Create\x20a\x20new\x20suggestion','reply','Reason\x20for\x20denying\x20the\x20suggestion','4130gPmjtT','7493400ztYawe','SuggestionSettings','setLabel','13884VVEoPT','forEach','suggestion','ModalTitle','../../events/Suggestions/suggestionActions','getUser','139JQcFUc','suggestionText','add','646440SDVsIl','The\x20user\x20to\x20blacklist','getString','options','resolve','\x20has\x20been\x20added\x20to\x20the\x20blacklist.','UseQuestionModal','Reason','replied','Accept\x20a\x20suggestion','load','acceptSuggestion','path','Reason\x20for\x20accepting\x20the\x20suggestion','suggestionModal','addComponents','Error\x20in\x20openQuestionModal:\x20','4IHkwgp','4814370KfuKSN','2600120HoESBR','NoPermsMessage','3345903NlGPaV','setStyle','AdditionalModalInputs','discord.js','The\x20user\x20to\x20remove\x20from\x20the\x20blacklist','test','deleteOne','maxLength','\x20has\x20been\x20removed\x20from\x20the\x20blacklist.','Error','Placeholder','has','js-yaml','setPlaceholder','Message','21230rszjxj','Your\x20suggestion\x20contains\x20blacklisted\x20words.','remove','setMaxLength','BlacklistWords','blacklist','Style','../../models/SuggestionBlacklist'];_0x4b8f=function(){return _0xf1629;};return _0x4b8f();}command[_0x330a21(0x2be,0x2b7)](_0x1e8ffe=>_0x1e8ffe[_0x330a21(0x2cc,0x2b6)](_0x330a21(0x2ba,0x2b9))[_0x330a21(0x2c0,0x2a5)](_0x330a21(0x2f4,0x2c9))['addStringOption'](_0x525c42=>_0x525c42['setName']('id')[_0x330a21(0x2c0,0x2c8)]('The\x20ID\x20of\x20the\x20suggestion\x20to\x20accept')[_0x330a21(0x2c2,0x2de)](!![]))[_0x330a21(0x2d7,0x2ff)](_0x4999f2=>_0x4999f2['setName']('reason')['setDescription'](_0x330a21(0x2f8,0x2f1))[_0x330a21(0x2c2,0x2d6)](![])))[_0x330a21(0x2be,0x2c1)](_0x81cdea=>_0x81cdea[_0x330a21(0x2cc,0x2b6)](_0x330a21(0x2b5,0x2a3))[_0x330a21(0x2c0,0x297)](_0x330a21(0x2d6,0x2bc))['addStringOption'](_0x5e8a96=>_0x5e8a96['setName']('id')['setDescription']('The\x20ID\x20of\x20the\x20suggestion\x20to\x20deny')[_0x330a21(0x2c2,0x2c5)](!![]))[_0x330a21(0x2d7,0x2c7)](_0x4aeb19=>_0x4aeb19[_0x330a21(0x2cc,0x2fc)](_0x330a21(0x2b8,0x2e1))[_0x330a21(0x2c0,0x2a3)](_0x330a21(0x2dd,0x2ac))[_0x330a21(0x2c2,0x2ad)](![])))['addSubcommandGroup'](_0x507786=>_0x507786[_0x330a21(0x2cc,0x2ef)](_0x330a21(0x2b2,0x297))[_0x330a21(0x2c0,0x2ee)](_0x330a21(0x2d4,0x2de))[_0x330a21(0x2be,0x2dc)](_0x12e14f=>_0x12e14f[_0x330a21(0x2cc,0x2e2)](_0x330a21(0x2ea,0x2ee))['setDescription']('Add\x20a\x20user\x20to\x20the\x20blacklist')[_0x330a21(0x2c1,0x2dd)](_0x10911c=>_0x10911c['setName'](_0x330a21(0x2cf,0x2e1))[_0x330a21(0x2c0,0x2f1)](_0x330a21(0x2ec,0x30a))['setRequired'](!![])))[_0x330a21(0x2be,0x2a5)](_0x51d99e=>_0x51d99e[_0x330a21(0x2cc,0x2c4)](_0x330a21(0x2af,0x2dd))['setDescription'](_0x330a21(0x2bd,0x296))['addUserOption'](_0xefd605=>_0xefd605[_0x330a21(0x2cc,0x2dd)](_0x330a21(0x2cf,0x2b8))[_0x330a21(0x2c0,0x2b7)](_0x330a21(0x2a2,0x2ba))[_0x330a21(0x2c2,0x2d6)](!![]))));function _0x8cd9(_0x33c990,_0x1f2862){const _0x4b8f68=_0x4b8f();return _0x8cd9=function(_0x8cd969,_0x4cf90a){_0x8cd969=_0x8cd969-0xbf;let _0x399476=_0x4b8f68[_0x8cd969];return _0x399476;},_0x8cd9(_0x33c990,_0x1f2862);}async function checkBlacklistWords(_0x511869){function _0x12ccdc(_0x249a20,_0x36ba1b){return _0x330a21(_0x36ba1b- -0x28f,_0x249a20);}const _0x47f0f3=config[_0x12ccdc(0x45,0x22)][_0x12ccdc(0x41,0x2a)][_0x12ccdc(0x2a,0x3b)](_0x62843a=>convertSimplePatternToRegex(_0x62843a));return _0x47f0f3[_0x12ccdc(0x14,0x44)](_0x550550=>_0x550550[_0x12ccdc(-0x11,0x14)](_0x511869));}function convertSimplePatternToRegex(_0x10f59a){function _0x2e702f(_0x2c8b2d,_0x25a212){return _0x330a21(_0x25a212-0x31,_0x2c8b2d);}let _0x18b3f9=_0x10f59a['replace'](/\./g,'\x5c.')[_0x2e702f(0x2f1,0x2f7)](/\*/g,'.*');return new RegExp('^'+_0x18b3f9+'$','i');}module[_0x330a21(0x2bf,0x2d0)]={'data':command,'category':'General',async 'execute'(_0x353c94,_0x348877){function _0x599351(_0x324f18,_0x335609){return _0x330a21(_0x335609- -0x397,_0x324f18);}try{if(!config[_0x599351(-0xa7,-0xb7)]['Enabled']){await _0x353c94[_0x599351(-0xd3,-0xbb)]({'content':lang[_0x599351(-0xde,-0xbf)][_0x599351(-0xd4,-0xd4)],'ephemeral':!![]});return;}const _0x26f696=_0x353c94['options']['getSubcommand']();if(_0x26f696===_0x599351(-0xcd,-0xd2)){const _0x80f0e3=config[_0x599351(-0xa4,-0xb7)]['AllowedRoles'],_0x1c7bfa=_0x80f0e3['length']===0x0||_0x80f0e3[_0x599351(-0xa3,-0xc4)](_0xe1c861=>_0x353c94[_0x599351(-0xe9,-0xe1)][_0x599351(-0xd9,-0xc9)][_0x599351(-0xbb,-0xc7)]['has'](_0xe1c861));if(!_0x1c7bfa){await _0x353c94[_0x599351(-0xb7,-0xbb)]({'content':lang[_0x599351(-0xf7,-0xfa)],'ephemeral':!![]});return;}const _0x6d48b5=await SuggestionBlacklist['findOne']({'userId':_0x353c94[_0x599351(-0xdc,-0xc8)]['id']});if(_0x6d48b5){await _0x353c94[_0x599351(-0xec,-0xbb)]({'content':lang['Suggestion'][_0x599351(-0xb8,-0xe0)],'ephemeral':!![]});return;}if(useQuestionModal)await openQuestionModal(_0x353c94);else{const _0x16a4a6=_0x353c94[_0x599351(-0xc1,-0xa9)][_0x599351(-0x8a,-0xaa)]('text');if(config[_0x599351(-0xdf,-0xb7)][_0x599351(-0xf9,-0xd0)]&&await checkBlacklistWords(_0x16a4a6)){const _0x475b89=lang['BlacklistWords']&&lang[_0x599351(-0xc4,-0xe6)]['Message']?lang[_0x599351(-0x109,-0xe6)][_0x599351(-0xe1,-0xeb)][_0x599351(-0xc8,-0xd1)](/{user}/g,''+_0x353c94[_0x599351(-0xb2,-0xc8)]):_0x599351(-0xfe,-0xe9);await _0x353c94['reply']({'content':_0x475b89,'ephemeral':!![]});return;}await suggestionActions['createSuggestion'](_0x348877,_0x353c94,_0x16a4a6);}}else{if(_0x26f696===_0x599351(-0xf8,-0xdd)||_0x26f696===_0x599351(-0xff,-0xe2)){const _0xb22375=config[_0x599351(-0x9e,-0xb7)][_0x599351(-0x9f,-0xce)],_0x2e2a8a=_0xb22375[_0x599351(-0xc2,-0xc4)](_0x22e702=>_0x353c94[_0x599351(-0xd5,-0xe1)][_0x599351(-0xc2,-0xc9)][_0x599351(-0xf6,-0xc7)][_0x599351(-0xc9,-0xee)](_0x22e702));if(!_0x2e2a8a){await _0x353c94[_0x599351(-0xbf,-0xbb)]({'content':lang[_0x599351(-0xd6,-0xfa)],'ephemeral':!![]});return;}const _0xb223c9=_0x353c94[_0x599351(-0x8e,-0xa9)][_0x599351(-0x9f,-0xaa)]('id'),_0x593e99=_0x353c94[_0x599351(-0xca,-0xa9)][_0x599351(-0x8c,-0xaa)](_0x599351(-0xd2,-0xdf))||lang[_0x599351(-0xbe,-0xbf)][_0x599351(-0x90,-0xa5)];_0x26f696===_0x599351(-0xb8,-0xdd)?await suggestionActions[_0x599351(-0x8f,-0xa1)](_0x348877,_0x353c94,_0xb223c9,_0x593e99):await suggestionActions['denySuggestion'](_0x348877,_0x353c94,_0xb223c9,_0x593e99);}else{if(_0x26f696==='add'||_0x26f696===_0x599351(-0xcc,-0xe8)){const _0x4ed5cc=_0x26f696,_0x3d5549=_0x353c94['options'][_0x599351(-0xae,-0xb0)](_0x599351(-0xd7,-0xc8)),_0x5dfafa=config['SuggestionSettings']['SuggestionAcceptDenyRoles'],_0x349f5a=_0x5dfafa[_0x599351(-0x9a,-0xc4)](_0x5dfe38=>_0x353c94['member'][_0x599351(-0xde,-0xc9)][_0x599351(-0xb6,-0xc7)][_0x599351(-0x105,-0xee)](_0x5dfe38));if(!_0x349f5a){await _0x353c94['reply']({'content':lang[_0x599351(-0xd7,-0xfa)],'ephemeral':!![]});return;}if(_0x4ed5cc===_0x599351(-0x83,-0xad))await SuggestionBlacklist['updateOne']({'userId':_0x3d5549['id']},{'userId':_0x3d5549['id']},{'upsert':!![]}),await _0x353c94[_0x599351(-0xc4,-0xbb)]({'content':_0x3d5549+_0x599351(-0x7f,-0xa7),'ephemeral':!![]});else _0x4ed5cc===_0x599351(-0xec,-0xe8)&&(await SuggestionBlacklist[_0x599351(-0x111,-0xf3)]({'userId':_0x3d5549['id']}),await _0x353c94[_0x599351(-0x99,-0xbb)]({'content':_0x3d5549+_0x599351(-0x11c,-0xf1),'ephemeral':!![]}));}}}}catch(_0x5a527d){console[_0x599351(-0x100,-0xd3)]('Error\x20in\x20suggestion\x20command:\x20',_0x5a527d),!_0x353c94['replied']&&await _0x353c94['reply']({'content':lang['Suggestion'][_0x599351(-0xed,-0xf0)],'ephemeral':!![]});}}};