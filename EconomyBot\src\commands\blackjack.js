const {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ComponentType,
} = require("discord.js");
const User = require("../models/User");
const { parseAmount, validateAmount } = require("../utils/economy");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
  formatCurrency,
} = require("../utils/embedBuilder");
const config = require("../config/config");

// Lưu trữ các game đang chơi
const activeGames = new Map();

module.exports = {
  data: new SlashCommandBuilder()
    .setName("blackjack")
    .setDescription("Chơi Blackjack (Xì dách) với dealer")
    .addStringOption((option) =>
      option
        .setName("amount")
        .setDescription("Số tiền đặt cược")
        .setRequired(true)
    ),

  cooldown: 5,

  async execute(interaction) {
    try {
      const amountStr = interaction.options.getString("amount");
      const userData = await User.findOrCreate(interaction.user);

      // Parse và validate số tiền
      let amount = parseAmount(amountStr);
      if (amount === "all") amount = userData.balance;
      if (amount === "half") amount = Math.floor(userData.balance / 2);

      amount = validateAmount(amount, userData.balance);

      if (amount < 100) {
        const errorEmbed = createErrorEmbed(
          "Số tiền quá nhỏ",
          "Số tiền đặt cược tối thiểu cho Blackjack là 100 xu!"
        );
        return await interaction.reply({
          embeds: [errorEmbed],
          ephemeral: true,
        });
      }

      // Kiểm tra xem user có đang chơi game khác không
      if (activeGames.has(interaction.user.id)) {
        const errorEmbed = createErrorEmbed(
          "Đang chơi game khác",
          "Bạn đang có một game Blackjack chưa hoàn thành!"
        );
        return await interaction.reply({
          embeds: [errorEmbed],
          ephemeral: true,
        });
      }

      // Trừ tiền cược
      await userData.updateBalance(-amount, "blackjack_bet");

      // Tạo game mới
      const game = createNewGame(interaction.user.id, amount);
      activeGames.set(interaction.user.id, game);

      // Chia bài ban đầu
      dealInitialCards(game);

      // Kiểm tra Blackjack tự nhiên
      if (getHandValue(game.playerHand) === 21) {
        await handleBlackjack(interaction, game);
        return;
      }

      await displayGame(interaction, game);
    } catch (error) {
      console.error("Lỗi lệnh blackjack:", error);

      const errorEmbed = createErrorEmbed(
        "Lỗi Blackjack",
        "Không thể bắt đầu game Blackjack. Vui lòng thử lại!"
      );

      await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  },
};

function createNewGame(userId, betAmount) {
  return {
    userId: userId,
    betAmount: betAmount,
    deck: createDeck(),
    playerHand: [],
    dealerHand: [],
    gameOver: false,
    playerStand: false,
  };
}

function createDeck() {
  const suits = ["♠️", "♥️", "♦️", "♣️"];
  const ranks = [
    "A",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "10",
    "J",
    "Q",
    "K",
  ];
  const deck = [];

  for (const suit of suits) {
    for (const rank of ranks) {
      deck.push({ suit, rank });
    }
  }

  // Xáo bài
  for (let i = deck.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [deck[i], deck[j]] = [deck[j], deck[i]];
  }

  return deck;
}

function dealCard(game) {
  return game.deck.pop();
}

function dealInitialCards(game) {
  // Chia 2 lá cho người chơi
  game.playerHand.push(dealCard(game));
  game.playerHand.push(dealCard(game));

  // Chia 2 lá cho dealer (1 lá úp)
  game.dealerHand.push(dealCard(game));
  game.dealerHand.push(dealCard(game));
}

function getCardValue(card) {
  if (card.rank === "A") return 11;
  if (["J", "Q", "K"].includes(card.rank)) return 10;
  return parseInt(card.rank);
}

function getHandValue(hand) {
  let value = 0;
  let aces = 0;

  for (const card of hand) {
    if (card.rank === "A") {
      aces++;
      value += 11;
    } else if (["J", "Q", "K"].includes(card.rank)) {
      value += 10;
    } else {
      value += parseInt(card.rank);
    }
  }

  // Xử lý Ace
  while (value > 21 && aces > 0) {
    value -= 10;
    aces--;
  }

  return value;
}

function formatHand(hand, hideFirst = false) {
  return hand
    .map((card, index) => {
      if (hideFirst && index === 0) {
        return "🂠"; // Lá bài úp
      }
      return `${card.rank}${card.suit}`;
    })
    .join(" ");
}

async function displayGame(interaction, game) {
  const playerValue = getHandValue(game.playerHand);
  const dealerValue = getHandValue(game.dealerHand);
  const dealerShownValue = getHandValue([game.dealerHand[1]]); // Chỉ hiển thị lá thứ 2

  const gameEmbed = createInfoEmbed(
    "🃏 Blackjack Game",
    `💰 **Tiền cược:** ${formatCurrency(game.betAmount)}\n\n` +
      `🎯 **Bài của bạn:** ${formatHand(game.playerHand)} (${playerValue})\n` +
      `🤖 **Bài của Dealer:** ${formatHand(
        game.dealerHand,
        true
      )} (${dealerShownValue}+)\n\n` +
      `🎲 **Mục tiêu:** Đạt 21 điểm hoặc gần nhất mà không vượt quá!`
  );

  // Tạo buttons
  const hitButton = new ButtonBuilder()
    .setCustomId("bj_hit")
    .setLabel("Rút thêm (Hit)")
    .setStyle(ButtonStyle.Primary)
    .setEmoji("🃏");

  const standButton = new ButtonBuilder()
    .setCustomId("bj_stand")
    .setLabel("Dừng (Stand)")
    .setStyle(ButtonStyle.Secondary)
    .setEmoji("✋");

  const doubleButton = new ButtonBuilder()
    .setCustomId("bj_double")
    .setLabel("Gấp đôi (Double)")
    .setStyle(ButtonStyle.Success)
    .setEmoji("💰")
    .setDisabled(game.playerHand.length > 2); // Chỉ cho phép double với 2 lá đầu

  const row = new ActionRowBuilder().addComponents(
    hitButton,
    standButton,
    doubleButton
  );

  const response = await interaction.reply({
    embeds: [gameEmbed],
    components: [row],
    fetchReply: true,
  });

  // Collector cho buttons
  const collector = response.createMessageComponentCollector({
    componentType: ComponentType.Button,
    time: 60000, // 1 phút
  });

  collector.on("collect", async (buttonInteraction) => {
    if (buttonInteraction.user.id !== game.userId) {
      await buttonInteraction.reply({
        content: "Đây không phải game của bạn!",
        ephemeral: true,
      });
      return;
    }

    const action = buttonInteraction.customId.replace("bj_", "");

    switch (action) {
      case "hit":
        await handleHit(buttonInteraction, game);
        break;
      case "stand":
        await handleStand(buttonInteraction, game);
        break;
      case "double":
        await handleDouble(buttonInteraction, game);
        break;
    }

    if (game.gameOver) {
      collector.stop();
    }
  });

  collector.on("end", () => {
    if (!game.gameOver) {
      // Tự động stand nếu hết thời gian
      handleStand(interaction, game, true);
    }
    activeGames.delete(game.userId);
  });
}

async function handleHit(interaction, game) {
  game.playerHand.push(dealCard(game));
  const playerValue = getHandValue(game.playerHand);

  if (playerValue > 21) {
    // Bust
    await handleGameEnd(interaction, game, "bust");
  } else if (playerValue === 21) {
    // 21 - tự động stand
    await handleStand(interaction, game);
  } else {
    // Tiếp tục game
    await updateGameDisplay(interaction, game);
  }
}

async function handleStand(interaction, game, timeout = false) {
  game.playerStand = true;

  // Dealer rút bài
  while (getHandValue(game.dealerHand) < 17) {
    game.dealerHand.push(dealCard(game));
  }

  const playerValue = getHandValue(game.playerHand);
  const dealerValue = getHandValue(game.dealerHand);

  let result;
  if (dealerValue > 21) {
    result = "dealer_bust";
  } else if (playerValue > dealerValue) {
    result = "player_win";
  } else if (dealerValue > playerValue) {
    result = "dealer_win";
  } else {
    result = "tie";
  }

  await handleGameEnd(interaction, game, result, timeout);
}

async function handleDouble(interaction, game) {
  // Gấp đôi tiền cược
  const userData = await User.findById(game.userId);

  if (userData.balance < game.betAmount) {
    await interaction.reply({
      content: "Số dư không đủ để gấp đôi!",
      ephemeral: true,
    });
    return;
  }

  await userData.updateBalance(-game.betAmount, "blackjack_double");
  game.betAmount *= 2;

  // Rút đúng 1 lá và tự động stand
  game.playerHand.push(dealCard(game));

  const playerValue = getHandValue(game.playerHand);
  if (playerValue > 21) {
    await handleGameEnd(interaction, game, "bust");
  } else {
    await handleStand(interaction, game);
  }
}

async function handleBlackjack(interaction, game) {
  const dealerValue = getHandValue(game.dealerHand);

  if (dealerValue === 21) {
    // Cả hai đều Blackjack - hòa
    await handleGameEnd(interaction, game, "blackjack_tie");
  } else {
    // Player Blackjack thắng
    await handleGameEnd(interaction, game, "blackjack");
  }
}

async function handleGameEnd(interaction, game, result, timeout = false) {
  game.gameOver = true;

  const userData = await User.findById(game.userId);
  const playerValue = getHandValue(game.playerHand);
  const dealerValue = getHandValue(game.dealerHand);

  let winAmount = 0;
  let resultTitle = "";
  let resultDescription = "";

  switch (result) {
    case "blackjack":
      winAmount = Math.floor(game.betAmount * 2.5); // Blackjack trả 3:2
      resultTitle = "🏆 BLACKJACK!";
      resultDescription = "Bạn có Blackjack tự nhiên!";
      break;
    case "blackjack_tie":
      winAmount = game.betAmount; // Hoàn tiền
      resultTitle = "🤝 Hòa Blackjack";
      resultDescription = "Cả hai đều có Blackjack!";
      break;
    case "player_win":
      winAmount = game.betAmount * 2;
      resultTitle = "🎉 Bạn thắng!";
      resultDescription = "Bài của bạn cao hơn dealer!";
      break;
    case "dealer_bust":
      winAmount = game.betAmount * 2;
      resultTitle = "🎉 Dealer Bust!";
      resultDescription = "Dealer vượt quá 21 điểm!";
      break;
    case "dealer_win":
      winAmount = 0;
      resultTitle = "😢 Dealer thắng!";
      resultDescription = "Bài của dealer cao hơn!";
      break;
    case "bust":
      winAmount = 0;
      resultTitle = "💥 Bust!";
      resultDescription = "Bạn vượt quá 21 điểm!";
      break;
    case "tie":
      winAmount = game.betAmount; // Hoàn tiền
      resultTitle = "🤝 Hòa!";
      resultDescription = "Cả hai có điểm bằng nhau!";
      break;
  }

  if (winAmount > 0) {
    await userData.updateBalance(winAmount, "blackjack_win");
    if (
      result === "blackjack" ||
      result === "player_win" ||
      result === "dealer_bust"
    ) {
      await userData.addExperience(Math.floor(game.betAmount / 20));
    }
  }

  const finalEmbed =
    winAmount > game.betAmount
      ? createSuccessEmbed(
          resultTitle,
          createResultDescription(
            game,
            playerValue,
            dealerValue,
            resultDescription,
            winAmount,
            userData.balance + winAmount
          )
        )
      : createErrorEmbed(
          resultTitle,
          createResultDescription(
            game,
            playerValue,
            dealerValue,
            resultDescription,
            winAmount,
            userData.balance + winAmount
          )
        );

  if (timeout) {
    await interaction.editReply({ embeds: [finalEmbed], components: [] });
  } else {
    await interaction.update({ embeds: [finalEmbed], components: [] });
  }

  activeGames.delete(game.userId);
}

function createResultDescription(
  game,
  playerValue,
  dealerValue,
  resultDescription,
  winAmount,
  newBalance
) {
  return (
    `${resultDescription}\n\n` +
    `🎯 **Bài của bạn:** ${formatHand(game.playerHand)} (${playerValue})\n` +
    `🤖 **Bài của Dealer:** ${formatHand(
      game.dealerHand
    )} (${dealerValue})\n\n` +
    `💰 **Tiền cược:** ${formatCurrency(game.betAmount)}\n` +
    `🏆 **Tiền thắng:** ${formatCurrency(winAmount)}\n` +
    `${config.emojis.money} **Số dư hiện tại:** ${formatCurrency(newBalance)}`
  );
}

async function updateGameDisplay(interaction, game) {
  const playerValue = getHandValue(game.playerHand);
  const dealerShownValue = getHandValue([game.dealerHand[1]]);

  const gameEmbed = createInfoEmbed(
    "🃏 Blackjack Game",
    `💰 **Tiền cược:** ${formatCurrency(game.betAmount)}\n\n` +
      `🎯 **Bài của bạn:** ${formatHand(game.playerHand)} (${playerValue})\n` +
      `🤖 **Bài của Dealer:** ${formatHand(
        game.dealerHand,
        true
      )} (${dealerShownValue}+)\n\n` +
      `🎲 **Mục tiêu:** Đạt 21 điểm hoặc gần nhất mà không vượt quá!`
  );

  // Cập nhật buttons
  const hitButton = new ButtonBuilder()
    .setCustomId("bj_hit")
    .setLabel("Rút thêm (Hit)")
    .setStyle(ButtonStyle.Primary)
    .setEmoji("🃏");

  const standButton = new ButtonBuilder()
    .setCustomId("bj_stand")
    .setLabel("Dừng (Stand)")
    .setStyle(ButtonStyle.Secondary)
    .setEmoji("✋");

  const doubleButton = new ButtonBuilder()
    .setCustomId("bj_double")
    .setLabel("Gấp đôi (Double)")
    .setStyle(ButtonStyle.Success)
    .setEmoji("💰")
    .setDisabled(true); // Disable sau lần hit đầu tiên

  const row = new ActionRowBuilder().addComponents(
    hitButton,
    standButton,
    doubleButton
  );

  await interaction.update({ embeds: [gameEmbed], components: [row] });
}
