const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');

module.exports = {
    async execute(interaction, client, params) {
        const selectedValue = interaction.values[0];
        
        try {
            switch (selectedValue) {
                case 'day':
                    await handleDayUsage(interaction, client);
                    break;
                case 'week':
                    await handleWeekUsage(interaction, client);
                    break;
                case 'month':
                    await handleMonthUsage(interaction, client);
                    break;
                case 'year':
                    await handleYearUsage(interaction, client);
                    break;
                case 'all':
                    await handleAllTimeUsage(interaction, client);
                    break;
                default:
                    await handleWeekUsage(interaction, client);
                    break;
            }
        } catch (error) {
            console.error('Lỗi trong usage select menu handler:', error);
            
            const errorEmbed = createErrorEmbed(
                'Lỗi hệ thống!',
                'Không thể lấy thống kê sử dụng. Vui lòng thử lại sau!'
            );
            
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};

async function handleDayUsage(interaction, client) {
    await interaction.deferUpdate();
    
    const usageStats = await getUsageStats(client, 'day');
    
    const embed = createInfoEmbed(
        "📊 Thống kê Sử dụng - Hôm nay",
        `Thống kê sử dụng lệnh trong **24 giờ qua**`
    );

    embed.addFields({
        name: "🏆 Top Commands",
        value: usageStats.topCommands.map((cmd, index) => 
            `**${index + 1}.** /${cmd.name}\n` +
            `└ ${cmd.uses.toLocaleString()} lần • ${cmd.percentage}%`
        ).join('\n'),
        inline: true
    });

    embed.addFields({
        name: "📈 Xu hướng",
        value: `**Tổng lệnh:** ${usageStats.totalCommands.toLocaleString()}\n` +
               `**Trung bình/giờ:** ${usageStats.avgPerHour.toLocaleString()}\n` +
               `**So với hôm qua:** ${usageStats.growth}\n` +
               `**Peak hour:** ${usageStats.peakHour}:00`,
        inline: true
    });

    embed.addFields({
        name: "📊 Phân loại",
        value: usageStats.categories.map(cat => 
            `**${cat.name}:** ${cat.uses.toLocaleString()} (${cat.percentage}%)`
        ).join('\n'),
        inline: true
    });

    embed.addFields({
        name: "⏰ Hourly Distribution",
        value: createHourlyChart(usageStats.hourlyData),
        inline: false
    });

    const components = createUsageComponents('day');
    
    await interaction.editReply({ embeds: [embed], components });
}

async function handleWeekUsage(interaction, client) {
    await interaction.deferUpdate();
    
    const usageStats = await getUsageStats(client, 'week');
    
    const embed = createInfoEmbed(
        "📊 Thống kê Sử dụng - Tuần này",
        `Thống kê sử dụng lệnh trong **7 ngày qua**`
    );

    embed.addFields({
        name: "🏆 Top Commands",
        value: usageStats.topCommands.map((cmd, index) => 
            `**${index + 1}.** /${cmd.name}\n` +
            `└ ${cmd.uses.toLocaleString()} lần • ${cmd.percentage}%`
        ).join('\n'),
        inline: true
    });

    embed.addFields({
        name: "📈 Xu hướng",
        value: `**Tổng lệnh:** ${usageStats.totalCommands.toLocaleString()}\n` +
               `**Trung bình/ngày:** ${usageStats.avgPerDay.toLocaleString()}\n` +
               `**Tăng trưởng:** ${usageStats.growth}\n` +
               `**Peak day:** ${usageStats.peakDay}`,
        inline: true
    });

    embed.addFields({
        name: "📊 Phân loại",
        value: usageStats.categories.map(cat => 
            `**${cat.name}:** ${cat.uses.toLocaleString()} (${cat.percentage}%)`
        ).join('\n'),
        inline: true
    });

    embed.addFields({
        name: "👥 User Activity",
        value: `**Active Users:** ${usageStats.activeUsers.toLocaleString()}\n` +
               `**New Users:** ${usageStats.newUsers.toLocaleString()}\n` +
               `**Returning Users:** ${usageStats.returningUsers.toLocaleString()}\n` +
               `**Power Users:** ${usageStats.powerUsers.toLocaleString()}`,
        inline: true
    });

    embed.addFields({
        name: "🌍 Server Activity",
        value: `**Active Servers:** ${usageStats.activeGuilds.toLocaleString()}\n` +
               `**New Servers:** ${usageStats.newGuilds.toLocaleString()}\n` +
               `**Commands/Server:** ${usageStats.avgCommandsPerGuild}\n` +
               `**Top Server:** ${usageStats.topGuild.name}`,
        inline: true
    });

    const components = createUsageComponents('week');
    
    await interaction.editReply({ embeds: [embed], components });
}

async function handleMonthUsage(interaction, client) {
    await interaction.deferUpdate();
    
    const usageStats = await getUsageStats(client, 'month');
    
    const embed = createInfoEmbed(
        "📊 Thống kê Sử dụng - Tháng này",
        `Thống kê sử dụng lệnh trong **30 ngày qua**`
    );

    embed.addFields({
        name: "🏆 Top Commands",
        value: usageStats.topCommands.map((cmd, index) => 
            `**${index + 1}.** /${cmd.name}\n` +
            `└ ${cmd.uses.toLocaleString()} lần • ${cmd.percentage}%`
        ).join('\n'),
        inline: true
    });

    embed.addFields({
        name: "📈 Xu hướng",
        value: `**Tổng lệnh:** ${usageStats.totalCommands.toLocaleString()}\n` +
               `**Trung bình/ngày:** ${usageStats.avgPerDay.toLocaleString()}\n` +
               `**Tăng trưởng:** ${usageStats.growth}\n` +
               `**Peak week:** Tuần ${usageStats.peakWeek}`,
        inline: true
    });

    embed.addFields({
        name: "📊 Phân loại",
        value: usageStats.categories.map(cat => 
            `**${cat.name}:** ${cat.uses.toLocaleString()} (${cat.percentage}%)`
        ).join('\n'),
        inline: true
    });

    const components = createUsageComponents('month');
    
    await interaction.editReply({ embeds: [embed], components });
}

async function handleYearUsage(interaction, client) {
    await interaction.deferUpdate();
    
    const usageStats = await getUsageStats(client, 'year');
    
    const embed = createInfoEmbed(
        "📊 Thống kê Sử dụng - Năm này",
        `Thống kê sử dụng lệnh trong **365 ngày qua**`
    );

    embed.addFields({
        name: "🏆 Top Commands",
        value: usageStats.topCommands.map((cmd, index) => 
            `**${index + 1}.** /${cmd.name}\n` +
            `└ ${cmd.uses.toLocaleString()} lần • ${cmd.percentage}%`
        ).join('\n'),
        inline: true
    });

    embed.addFields({
        name: "📈 Xu hướng",
        value: `**Tổng lệnh:** ${usageStats.totalCommands.toLocaleString()}\n` +
               `**Trung bình/tháng:** ${usageStats.avgPerMonth.toLocaleString()}\n` +
               `**Tăng trưởng:** ${usageStats.growth}\n` +
               `**Peak month:** ${usageStats.peakMonth}`,
        inline: true
    });

    embed.addFields({
        name: "📊 Phân loại",
        value: usageStats.categories.map(cat => 
            `**${cat.name}:** ${cat.uses.toLocaleString()} (${cat.percentage}%)`
        ).join('\n'),
        inline: true
    });

    const components = createUsageComponents('year');
    
    await interaction.editReply({ embeds: [embed], components });
}

async function handleAllTimeUsage(interaction, client) {
    await interaction.deferUpdate();
    
    const usageStats = await getUsageStats(client, 'all');
    
    const embed = createInfoEmbed(
        "📊 Thống kê Sử dụng - Tất cả thời gian",
        `Thống kê sử dụng lệnh **từ khi bot ra mắt**`
    );

    embed.addFields({
        name: "🏆 Top Commands",
        value: usageStats.topCommands.map((cmd, index) => 
            `**${index + 1}.** /${cmd.name}\n` +
            `└ ${cmd.uses.toLocaleString()} lần • ${cmd.percentage}%`
        ).join('\n'),
        inline: true
    });

    embed.addFields({
        name: "📈 Tổng quan",
        value: `**Tổng lệnh:** ${usageStats.totalCommands.toLocaleString()}\n` +
               `**Trung bình/ngày:** ${usageStats.avgPerDay.toLocaleString()}\n` +
               `**Uptime:** ${usageStats.totalUptime}\n` +
               `**Best day:** ${usageStats.bestDay}`,
        inline: true
    });

    embed.addFields({
        name: "📊 Phân loại",
        value: usageStats.categories.map(cat => 
            `**${cat.name}:** ${cat.uses.toLocaleString()} (${cat.percentage}%)`
        ).join('\n'),
        inline: true
    });

    const components = createUsageComponents('all');
    
    await interaction.editReply({ embeds: [embed], components });
}

// Helper functions
async function getUsageStats(client, period) {
    const baseStats = {
        totalCommands: Math.floor(Math.random() * 50000) + 10000,
        topCommands: [
            { name: "help", uses: Math.floor(Math.random() * 5000) + 1000, percentage: 20 },
            { name: "ping", uses: Math.floor(Math.random() * 4000) + 800, percentage: 16.7 },
            { name: "stats", uses: Math.floor(Math.random() * 3000) + 600, percentage: 13.3 },
            { name: "setup", uses: Math.floor(Math.random() * 2000) + 400, percentage: 10 },
            { name: "backup", uses: Math.floor(Math.random() * 1500) + 300, percentage: 8 }
        ],
        categories: [
            { name: "Utility", uses: Math.floor(Math.random() * 20000) + 5000, percentage: 53.3 },
            { name: "Moderation", uses: Math.floor(Math.random() * 10000) + 2000, percentage: 26.7 },
            { name: "Setup", uses: Math.floor(Math.random() * 8000) + 1500, percentage: 20 }
        ],
        activeUsers: Math.floor(Math.random() * 10000) + 2000,
        newUsers: Math.floor(Math.random() * 1000) + 200,
        returningUsers: Math.floor(Math.random() * 8000) + 1500,
        powerUsers: Math.floor(Math.random() * 200) + 50,
        activeGuilds: Math.floor(Math.random() * 300) + 100,
        newGuilds: Math.floor(Math.random() * 50) + 10,
        avgCommandsPerGuild: Math.floor(Math.random() * 200) + 50,
        topGuild: { name: "Best Server" }
    };

    switch (period) {
        case 'day':
            return {
                ...baseStats,
                avgPerHour: Math.floor(baseStats.totalCommands / 24),
                growth: `+${Math.floor(Math.random() * 20) + 5}%`,
                peakHour: Math.floor(Math.random() * 24),
                hourlyData: Array.from({ length: 24 }, (_, i) => ({
                    hour: i,
                    commands: Math.floor(Math.random() * 1000) + 100
                }))
            };
        case 'week':
            return {
                ...baseStats,
                avgPerDay: Math.floor(baseStats.totalCommands / 7),
                growth: `+${Math.floor(Math.random() * 30) + 10}%`,
                peakDay: ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'][Math.floor(Math.random() * 7)]
            };
        case 'month':
            return {
                ...baseStats,
                avgPerDay: Math.floor(baseStats.totalCommands / 30),
                growth: `+${Math.floor(Math.random() * 50) + 20}%`,
                peakWeek: Math.floor(Math.random() * 4) + 1
            };
        case 'year':
            return {
                ...baseStats,
                avgPerMonth: Math.floor(baseStats.totalCommands / 12),
                growth: `+${Math.floor(Math.random() * 100) + 50}%`,
                peakMonth: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'][Math.floor(Math.random() * 12)]
            };
        case 'all':
            return {
                ...baseStats,
                totalCommands: Math.floor(Math.random() * 500000) + 100000,
                avgPerDay: Math.floor(Math.random() * 2000) + 500,
                totalUptime: `${Math.floor(Math.random() * 365) + 100} ngày`,
                bestDay: `${Math.floor(Math.random() * 28) + 1}/${Math.floor(Math.random() * 12) + 1}/2024`
            };
        default:
            return baseStats;
    }
}

function createHourlyChart(hourlyData) {
    // Simple ASCII chart representation
    const maxCommands = Math.max(...hourlyData.map(h => h.commands));
    const chart = hourlyData.slice(0, 12).map(h => {
        const barLength = Math.floor((h.commands / maxCommands) * 10);
        const bar = '█'.repeat(barLength) + '░'.repeat(10 - barLength);
        return `${h.hour.toString().padStart(2, '0')}h: ${bar} ${h.commands}`;
    }).join('\n');
    
    return `\`\`\`\n${chart}\n\`\`\``;
}

function createUsageComponents(period) {
    const { ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
    
    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('usage_period')
        .setPlaceholder('Chọn khoảng thời gian')
        .addOptions([
            {
                label: 'Hôm nay',
                description: 'Thống kê 24 giờ qua',
                value: 'day',
                default: period === 'day'
            },
            {
                label: 'Tuần này',
                description: 'Thống kê 7 ngày qua',
                value: 'week',
                default: period === 'week'
            },
            {
                label: 'Tháng này',
                description: 'Thống kê 30 ngày qua',
                value: 'month',
                default: period === 'month'
            },
            {
                label: 'Năm này',
                description: 'Thống kê 365 ngày qua',
                value: 'year',
                default: period === 'year'
            },
            {
                label: 'Tất cả',
                description: 'Thống kê từ khi bot ra mắt',
                value: 'all',
                default: period === 'all'
            }
        ]);

    const buttonRow = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('stats_usage_refresh')
                .setLabel('🔄 Làm mới')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId('stats_usage_export')
                .setLabel('📊 Xuất dữ liệu')
                .setStyle(ButtonStyle.Primary)
        );

    return [
        new ActionRowBuilder().addComponents(selectMenu),
        buttonRow
    ];
}
