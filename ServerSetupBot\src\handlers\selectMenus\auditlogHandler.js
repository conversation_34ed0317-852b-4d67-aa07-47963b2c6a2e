const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');

module.exports = {
    async execute(interaction, client, params) {
        const action = params[0]; // channel, ignore
        const subAction = params[1]; // select, roles, users
        
        try {
            if (action === 'channel') {
                await handleChannelSelect(interaction, client);
            } else if (action === 'ignore') {
                if (subAction === 'roles') {
                    await handleIgnoreRoles(interaction, client);
                } else if (subAction === 'users') {
                    await handleIgnoreUsers(interaction, client);
                }
            } else {
                await handleDefault(interaction, client);
            }
        } catch (error) {
            console.error('Lỗi trong auditlog select menu handler:', error);
            
            const errorEmbed = createErrorEmbed(
                'Lỗi hệ thống!',
                'Không thể xử lý yêu cầu. Vui lòng thử lại sau!'
            );
            
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};

async function handleChannelSelect(interaction, client) {
    await interaction.deferUpdate();
    
    const guildId = interaction.guild.id;
    const selectedChannelId = interaction.values[0];
    const selectedChannel = interaction.guild.channels.cache.get(selectedChannelId);
    
    if (!selectedChannel) {
        const errorEmbed = createErrorEmbed(
            'Kênh không tồn tại!',
            'Không thể tìm thấy kênh được chọn.'
        );
        return await interaction.editReply({ embeds: [errorEmbed], components: [] });
    }
    
    try {
        // Kiểm tra quyền bot trong kênh mới
        const botPermissions = selectedChannel.permissionsFor(interaction.guild.members.me);
        if (!botPermissions.has(['SendMessages', 'EmbedLinks'])) {
            const errorEmbed = createErrorEmbed(
                'Thiếu quyền!',
                `Bot cần quyền **Send Messages** và **Embed Links** trong kênh ${selectedChannel}.`
            );
            return await interaction.editReply({ embeds: [errorEmbed], components: [] });
        }
        
        // Lấy config hiện tại
        const currentConfig = await client.db.getAuditLogConfig(guildId);
        const oldChannelId = currentConfig?.channelId;
        
        // Cập nhật config với kênh mới
        await client.db.updateAuditLogConfig(guildId, {
            channelId: selectedChannelId
        });
        
        const embed = createSuccessEmbed(
            '✅ Đã thay đổi kênh Audit Log!',
            `Kênh audit log đã được chuyển sang ${selectedChannel}`
        );
        
        embed.addFields(
            { name: '📍 Kênh mới', value: `${selectedChannel}`, inline: true },
            { name: '📍 Kênh cũ', value: oldChannelId ? `<#${oldChannelId}>` : 'Không có', inline: true },
            { name: '⏰ Thay đổi lúc', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
        );
        
        await interaction.editReply({ embeds: [embed], components: [] });
        
        // Gửi thông báo vào kênh cũ (nếu có)
        if (oldChannelId) {
            try {
                const oldChannel = await client.channels.fetch(oldChannelId);
                if (oldChannel) {
                    const notifyEmbed = createInfoEmbed(
                        '📍 Audit Log Channel Changed',
                        `Audit log đã được chuyển sang ${selectedChannel} bởi ${interaction.user.tag}`
                    );
                    await oldChannel.send({ embeds: [notifyEmbed] });
                }
            } catch (error) {
                console.log('Không thể gửi thông báo vào kênh cũ');
            }
        }
        
        // Gửi thông báo vào kênh mới
        const welcomeEmbed = createInfoEmbed(
            '🔍 Audit Log System Moved',
            `Hệ thống audit log đã được chuyển đến kênh này bởi ${interaction.user.tag}\n\n` +
            `Từ giờ, tất cả audit logs sẽ được gửi vào kênh này.`
        );
        
        welcomeEmbed.setFooter({
            text: `Server Setup Bot • Audit Log System`,
            iconURL: client.user.displayAvatarURL()
        });
        
        await selectedChannel.send({ embeds: [welcomeEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi thay đổi kênh audit log:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi thay đổi!',
            'Không thể thay đổi kênh audit log. Vui lòng thử lại sau!'
        );
        await interaction.editReply({ embeds: [errorEmbed], components: [] });
    }
}

async function handleIgnoreRoles(interaction, client) {
    await interaction.deferUpdate();
    
    const guildId = interaction.guild.id;
    const selectedRoleIds = interaction.values;
    
    try {
        // Cập nhật config với roles bị bỏ qua
        await client.db.updateAuditLogConfig(guildId, {
            ignoredRoles: selectedRoleIds
        });
        
        const selectedRoles = selectedRoleIds.map(roleId => {
            const role = interaction.guild.roles.cache.get(roleId);
            return role ? `@${role.name}` : `<@&${roleId}>`;
        });
        
        const embed = createSuccessEmbed(
            '✅ Đã cập nhật Ignored Roles!',
            `Các hoạt động của ${selectedRoleIds.length} roles sẽ không được ghi log`
        );
        
        embed.addFields({
            name: '👥 Roles bị bỏ qua',
            value: selectedRoles.length > 0 ? selectedRoles.join(', ') : 'Không có',
            inline: false
        });
        
        embed.addFields(
            { name: '📊 Số lượng', value: `${selectedRoleIds.length} roles`, inline: true },
            { name: '⏰ Cập nhật lúc', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
        );
        
        await interaction.editReply({ embeds: [embed], components: [] });
        
    } catch (error) {
        console.error('Lỗi khi cập nhật ignored roles:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi cập nhật!',
            'Không thể cập nhật ignored roles. Vui lòng thử lại sau!'
        );
        await interaction.editReply({ embeds: [errorEmbed], components: [] });
    }
}

async function handleIgnoreUsers(interaction, client) {
    await interaction.deferUpdate();
    
    const guildId = interaction.guild.id;
    const selectedUserIds = interaction.values;
    
    try {
        // Cập nhật config với users bị bỏ qua
        await client.db.updateAuditLogConfig(guildId, {
            ignoredUsers: selectedUserIds
        });
        
        const selectedUsers = selectedUserIds.map(userId => `<@${userId}>`);
        
        const embed = createSuccessEmbed(
            '✅ Đã cập nhật Ignored Users!',
            `Các hoạt động của ${selectedUserIds.length} users sẽ không được ghi log`
        );
        
        embed.addFields({
            name: '🚫 Users bị bỏ qua',
            value: selectedUsers.length > 0 ? selectedUsers.join(', ') : 'Không có',
            inline: false
        });
        
        embed.addFields(
            { name: '📊 Số lượng', value: `${selectedUserIds.length} users`, inline: true },
            { name: '⏰ Cập nhật lúc', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
        );
        
        embed.addFields({
            name: '💡 Lưu ý',
            value: '• Server owner luôn được ghi log\n• Bot actions vẫn có thể được ghi log\n• Có thể thay đổi bất cứ lúc nào',
            inline: false
        });
        
        await interaction.editReply({ embeds: [embed], components: [] });
        
    } catch (error) {
        console.error('Lỗi khi cập nhật ignored users:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi cập nhật!',
            'Không thể cập nhật ignored users. Vui lòng thử lại sau!'
        );
        await interaction.editReply({ embeds: [errorEmbed], components: [] });
    }
}

async function handleDefault(interaction, client) {
    await interaction.deferUpdate();
    
    const embed = createInfoEmbed(
        '⚙️ Audit Log Select Menu',
        'Tính năng select menu audit log đang được phát triển.'
    );
    
    await interaction.editReply({ embeds: [embed], components: [] });
}
