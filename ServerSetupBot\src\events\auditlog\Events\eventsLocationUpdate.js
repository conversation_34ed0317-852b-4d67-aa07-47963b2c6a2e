const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildScheduledEventUpdate,
    async execute(oldEvent, newEvent, client) {
        try {
            // Only handle location changes
            if (oldEvent.entityMetadata?.location === newEvent.entityMetadata?.location) return;
            
            console.log(`📍 Event location updated: ${newEvent.name} in ${newEvent.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newEvent.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EVENTS_LOCATION_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EVENTS_LOCATION_UPDATE',
                user: 'System',
                userId: null,
                action: 'Đ<PERSON><PERSON> điểm sự kiện được cập nhật',
                details: `<PERSON><PERSON><PERSON> điểm của sự kiện **${newEvent.name}** đ<PERSON> được cập nhật`,
                target: newEvent.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the event location from audit logs
            try {
                const auditLogs = await newEvent.guild.fetchAuditLogs({
                    type: 102, // GUILD_SCHEDULED_EVENT_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newEvent.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Địa điểm sự kiện được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for event location update');
            }
            
            // Add to database
            await client.db.addAuditLog(newEvent.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📍 Địa điểm sự kiện được cập nhật',
                `Vừa có địa điểm sự kiện được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get event entity type names
            const entityTypes = {
                1: 'Stage Instance',
                2: 'Voice Channel',
                3: 'External Location'
            };
            
            // Get event status names
            const statusTypes = {
                1: 'Scheduled',
                2: 'Active',
                3: 'Completed',
                4: 'Canceled'
            };
            
            embed.addFields([
                {
                    name: '> Tên sự kiện',
                    value: `- ${newEvent.name}`,
                    inline: true
                },
                {
                    name: '> ID sự kiện',
                    value: `- ${newEvent.id}`,
                    inline: true
                },
                {
                    name: '> Loại sự kiện',
                    value: `- ${entityTypes[newEvent.entityType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${statusTypes[newEvent.status] || 'Unknown'}`,
                    inline: true
                }
            ]);
            
            // Add location comparison
            const oldLocation = oldEvent.entityMetadata?.location || 'Không có địa điểm';
            const newLocation = newEvent.entityMetadata?.location || 'Không có địa điểm';
            
            embed.addFields([
                {
                    name: '> Địa điểm cũ',
                    value: `- ${oldLocation}`,
                    inline: true
                },
                {
                    name: '> Địa điểm mới',
                    value: `- ${newLocation}`,
                    inline: true
                }
            ]);
            
            // Add event timing info
            if (newEvent.scheduledStartTimestamp) {
                embed.addFields({
                    name: '> Thời gian bắt đầu',
                    value: `- <t:${Math.floor(newEvent.scheduledStartTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            if (newEvent.scheduledEndTimestamp) {
                embed.addFields({
                    name: '> Thời gian kết thúc',
                    value: `- <t:${Math.floor(newEvent.scheduledEndTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add creator info if available
            if (newEvent.creator) {
                embed.addFields({
                    name: '> Người tạo sự kiện',
                    value: `- ${newEvent.creator.tag}`,
                    inline: true
                });
            }
            
            // Add subscriber count if available
            if (newEvent.userCount !== null) {
                embed.addFields({
                    name: '> Số người quan tâm',
                    value: `- ${newEvent.userCount} người`,
                    inline: true
                });
            }
            
            // Add impact explanation based on location change
            if (newLocation !== 'Không có địa điểm' && oldLocation === 'Không có địa điểm') {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Sự kiện giờ đây có địa điểm cụ thể',
                        '• Thành viên biết nơi tham gia',
                        '• Tăng tính chuyên nghiệp',
                        '• Dễ dàng tìm kiếm và tham gia'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for added location
            } else if (newLocation === 'Không có địa điểm' && oldLocation !== 'Không có địa điểm') {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Sự kiện không còn địa điểm cụ thể',
                        '• Có thể gây nhầm lẫn cho thành viên',
                        '• Cần thông báo rõ ràng về thay đổi',
                        '• Có thể chuyển sang online'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0xe74c3c); // Red for removed location
            } else if (newLocation !== oldLocation) {
                embed.addFields({
                    name: '> 🔄 Tác động',
                    value: [
                        '• Địa điểm sự kiện đã thay đổi',
                        '• Thành viên cần cập nhật thông tin',
                        '• Có thể ảnh hưởng đến kế hoạch tham gia',
                        '• Cần thông báo cho người quan tâm'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add location guidelines
            embed.addFields({
                name: '> 💡 Lưu ý về địa điểm sự kiện',
                value: [
                    '• Địa điểm chỉ áp dụng cho External Location events',
                    '• Nên ghi rõ địa chỉ cụ thể',
                    '• Có thể bao gồm link maps hoặc hướng dẫn',
                    '• Cập nhật kịp thời nếu có thay đổi'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set event image as thumbnail if available
            if (newEvent.coverImageURL()) {
                embed.setThumbnail(newEvent.coverImageURL({ size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newEvent.guild.name} • Event Location Update`,
                iconURL: newEvent.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Event location update logged: ${newEvent.name}`);
            
        } catch (error) {
            console.error('Error in eventsLocationUpdate audit log:', error);
        }
    }
};
