const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.AutoModerationActionExecution,
    async execute(autoModerationActionExecution, client) {
        try {
            // Only handle block message actions
            if (!autoModerationActionExecution.action || autoModerationActionExecution.action.type !== 1) return;
            
            console.log(`🚫 AutoMod blocked message from ${autoModerationActionExecution.user?.tag} in ${autoModerationActionExecution.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(autoModerationActionExecution.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'AUTOMOD_BLOCK_MESSAGE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'AUTOMOD_BLOCK_MESSAGE',
                user: autoModerationActionExecution.user?.tag || 'Unknown',
                userId: autoModerationActionExecution.userId || null,
                action: 'Tin nhắn bị AutoMod chặn',
                details: `Tin nhắn của **${autoModerationActionExecution.user?.tag || 'Unknown'}** đã bị AutoMod chặn`,
                target: autoModerationActionExecution.user?.tag || 'Unknown',
                channel: autoModerationActionExecution.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(autoModerationActionExecution.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🚫 Tin nhắn bị AutoMod chặn',
                `AutoMod vừa chặn một tin nhắn vi phạm quy tắc`
            );
            
            embed.setColor(0xe74c3c); // Red for blocked message
            
            embed.addFields([
                {
                    name: '> Người vi phạm',
                    value: `- ${autoModerationActionExecution.user?.tag || 'Unknown'} (${autoModerationActionExecution.user || 'Unknown'})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${autoModerationActionExecution.userId || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${autoModerationActionExecution.channel || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add rule info
            if (autoModerationActionExecution.ruleTriggerType) {
                const triggerTypes = {
                    1: 'Keyword',
                    2: 'Harmful Link',
                    3: 'Spam',
                    4: 'Keyword Preset',
                    5: 'Mention Spam'
                };
                
                embed.addFields({
                    name: '> Loại quy tắc vi phạm',
                    value: `- ${triggerTypes[autoModerationActionExecution.ruleTriggerType] || 'Unknown'}`,
                    inline: true
                });
            }
            
            if (autoModerationActionExecution.ruleId) {
                embed.addFields({
                    name: '> ID quy tắc',
                    value: `- ${autoModerationActionExecution.ruleId}`,
                    inline: true
                });
            }
            
            // Add content info if available
            if (autoModerationActionExecution.content) {
                const content = autoModerationActionExecution.content.length > 500 
                    ? autoModerationActionExecution.content.substring(0, 500) + '...'
                    : autoModerationActionExecution.content;
                
                embed.addFields({
                    name: '> Nội dung bị chặn',
                    value: `\`\`\`${content}\`\`\``,
                    inline: false
                });
            }
            
            // Add matched keyword if available
            if (autoModerationActionExecution.matchedKeyword) {
                embed.addFields({
                    name: '> Từ khóa vi phạm',
                    value: `- \`${autoModerationActionExecution.matchedKeyword}\``,
                    inline: true
                });
            }
            
            // Add matched content if available
            if (autoModerationActionExecution.matchedContent) {
                embed.addFields({
                    name: '> Nội dung khớp',
                    value: `- \`${autoModerationActionExecution.matchedContent}\``,
                    inline: true
                });
            }
            
            // Add user info
            if (autoModerationActionExecution.user) {
                const user = autoModerationActionExecution.user;
                const member = autoModerationActionExecution.guild.members.cache.get(user.id);
                
                const accountAge = Date.now() - user.createdTimestamp;
                const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
                
                embed.addFields({
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                });
                
                if (member) {
                    embed.addFields({
                        name: '> Tham gia server lúc',
                        value: `- <t:${Math.floor(member.joinedTimestamp / 1000)}:F>`,
                        inline: true
                    });
                    
                    const roleCount = member.roles.cache.size - 1; // -1 for @everyone
                    if (roleCount > 0) {
                        embed.addFields({
                            name: '> Số roles',
                            value: `- ${roleCount} roles`,
                            inline: true
                        });
                    }
                }
            }
            
            // Add impact note
            embed.addFields({
                name: '> 📝 Tác động',
                value: [
                    '• Tin nhắn đã bị chặn và không hiển thị',
                    '• Người dùng có thể thấy thông báo lỗi',
                    '• Nội dung vi phạm không được gửi đi',
                    '• Hành động được ghi lại để theo dõi'
                ].join('\n'),
                inline: false
            });
            
            // Add prevention tips
            embed.addFields({
                name: '> 💡 Lưu ý',
                value: [
                    '• Kiểm tra quy tắc AutoMod nếu cần điều chỉnh',
                    '• Thành viên có thể liên hệ moderator nếu bị chặn nhầm',
                    '• Theo dõi tần suất vi phạm của người dùng',
                    '• Cân nhắc thêm miễn trừ cho roles tin cậy'
                ].join('\n'),
                inline: false
            });
            
            // Set user avatar as thumbnail
            if (autoModerationActionExecution.user?.displayAvatarURL()) {
                embed.setThumbnail(autoModerationActionExecution.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${autoModerationActionExecution.guild.name} • AutoMod Block Message`,
                iconURL: autoModerationActionExecution.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ AutoMod block message logged`);
            
        } catch (error) {
            console.error('Error in automodBlockMessage audit log:', error);
        }
    }
};
