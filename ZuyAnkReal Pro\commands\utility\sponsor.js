const { <PERSON><PERSON>ow<PERSON>uilder, <PERSON><PERSON><PERSON>uild<PERSON>, EmbedBuilder } = require("discord.js");

function createCodeButton(client) {
    const button = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setLabel("Website")
            .setStyle("Link")
            .setURL(client.website),
        new ButtonBuilder()
            .setLabel("Discord Server")
            .setStyle("Link")
            .setURL('https://discord.gg/gdW2fudg7Y')
    );

    return button;
}

module.exports = {
    name: "sponsor",
    voteOnly: false,
    run: async (client, message, args) => {
        const button = createCodeButton(client);

        const embed = new EmbedBuilder()
            .setColor(client.color)
            .setAuthor({ name: client.user.username + ' - Sponsor Information', iconURL: client.user.displayAvatarURL() })
            .setTitle('__Team CodeX__')
            .setDescription(`An Optimistic Community of Developers, Based around **Gamer CodeX** on youtube!, Endeavouring to Learn as we grow.
          
**__Check Out Them__**
[Webiste](https://codexdev.me) | [Discord](https://discord.gg/codexdev)`)
            .setImage('https://github.com/RayDev07/images/blob/main/codex.png?raw=true')

        message.channel.send({ embeds: [embed], components: [button] });
    },
};
