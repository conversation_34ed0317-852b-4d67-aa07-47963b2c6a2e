const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildScheduledEventUpdate,
    async execute(oldEvent, newEvent, client) {
        try {
            // Only handle privacy level changes
            if (oldEvent.privacyLevel === newEvent.privacyLevel) return;
            
            console.log(`🔒 Event privacy level updated: ${newEvent.name} in ${newEvent.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newEvent.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EVENTS_PRIVACY_LEVEL_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EVENTS_PRIVACY_LEVEL_UPDATE',
                user: 'System',
                userId: null,
                action: '<PERSON><PERSON><PERSON> độ riêng tư sự kiện được cập nhật',
                details: `<PERSON>ức độ riêng tư của sự kiện **${newEvent.name}** đã được cập nhật`,
                target: newEvent.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the event privacy level from audit logs
            try {
                const auditLogs = await newEvent.guild.fetchAuditLogs({
                    type: 102, // GUILD_SCHEDULED_EVENT_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newEvent.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Mức độ riêng tư sự kiện được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for event privacy level update');
            }
            
            // Add to database
            await client.db.addAuditLog(newEvent.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔒 Mức độ riêng tư sự kiện được cập nhật',
                `Vừa có mức độ riêng tư sự kiện được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get privacy level names
            const privacyLevels = {
                2: 'Guild Only (Chỉ server)'
            };
            
            // Get event entity type names
            const entityTypes = {
                1: 'Stage Instance',
                2: 'Voice Channel',
                3: 'External Location'
            };
            
            // Get event status names
            const statusTypes = {
                1: 'Scheduled',
                2: 'Active',
                3: 'Completed',
                4: 'Canceled'
            };
            
            embed.addFields([
                {
                    name: '> Tên sự kiện',
                    value: `- ${newEvent.name}`,
                    inline: true
                },
                {
                    name: '> ID sự kiện',
                    value: `- ${newEvent.id}`,
                    inline: true
                },
                {
                    name: '> Loại sự kiện',
                    value: `- ${entityTypes[newEvent.entityType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${statusTypes[newEvent.status] || 'Unknown'}`,
                    inline: true
                }
            ]);
            
            // Add privacy level comparison
            embed.addFields([
                {
                    name: '> Mức độ riêng tư cũ',
                    value: `- ${privacyLevels[oldEvent.privacyLevel] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Mức độ riêng tư mới',
                    value: `- ${privacyLevels[newEvent.privacyLevel] || 'Unknown'}`,
                    inline: true
                }
            ]);
            
            // Add event timing info
            if (newEvent.scheduledStartTimestamp) {
                embed.addFields({
                    name: '> Thời gian bắt đầu',
                    value: `- <t:${Math.floor(newEvent.scheduledStartTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            if (newEvent.scheduledEndTimestamp) {
                embed.addFields({
                    name: '> Thời gian kết thúc',
                    value: `- <t:${Math.floor(newEvent.scheduledEndTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add creator info if available
            if (newEvent.creator) {
                embed.addFields({
                    name: '> Người tạo sự kiện',
                    value: `- ${newEvent.creator.tag}`,
                    inline: true
                });
            }
            
            // Add subscriber count if available
            if (newEvent.userCount !== null) {
                embed.addFields({
                    name: '> Số người quan tâm',
                    value: `- ${newEvent.userCount} người`,
                    inline: true
                });
            }
            
            // Add impact explanation
            embed.addFields({
                name: '> 📝 Tác động của thay đổi',
                value: [
                    '• Hiện tại Discord chỉ hỗ trợ "Guild Only"',
                    '• Sự kiện chỉ hiển thị cho thành viên server',
                    '• Không thể chia sẻ công khai bên ngoài',
                    '• Bảo vệ thông tin sự kiện nội bộ'
                ].join('\n'),
                inline: false
            });
            
            // Add privacy explanation
            embed.addFields({
                name: '> 🔒 Về mức độ riêng tư',
                value: [
                    '**Guild Only (Chỉ server):**',
                    '• Chỉ thành viên server mới thấy được',
                    '• Không hiển thị trong discovery',
                    '• Bảo mật thông tin sự kiện',
                    '• Phù hợp cho sự kiện nội bộ'
                ].join('\n'),
                inline: false
            });
            
            // Add best practices
            embed.addFields({
                name: '> 💡 Khuyến nghị',
                value: [
                    '• Sử dụng Guild Only cho hầu hết sự kiện',
                    '• Cân nhắc kỹ trước khi thay đổi',
                    '• Thông báo cho thành viên về thay đổi',
                    '• Kiểm tra ai có thể xem sự kiện'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set event image as thumbnail if available
            if (newEvent.coverImageURL()) {
                embed.setThumbnail(newEvent.coverImageURL({ size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newEvent.guild.name} • Event Privacy Level Update`,
                iconURL: newEvent.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Event privacy level update logged: ${newEvent.name}`);
            
        } catch (error) {
            console.error('Error in eventsPrivacyLevelUpdate audit log:', error);
        }
    }
};
