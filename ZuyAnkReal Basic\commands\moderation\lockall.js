const { Permissions<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    name: 'lockall',
    description: 'Locks all channels in the server',
    aliases: ['lockserver'],

    run: async (message, args, client) => {
        // ✅ Check Permissions
        if (!message.member.permissions.has(PermissionsBitField.Flags.ManageChannels)) {
            return message.reply('❌ You need `Manage Channels` permission to use this command.');
        }
        if (!message.guild.members.me.permissions.has(PermissionsBitField.Flags.ManageChannels)) {
            return message.reply('❌ I need `Manage Channels` permission to lock all channels.');
        }

        // 🟠 Processing Embed
        const embed = new EmbedBuilder()
            .setColor('#FFA500')
            .setDescription('<a:red_loading:1221326019986980894> | **Processing Command Please Wait**');

        const msg = await message.channel.send({ embeds: [embed] });

        setTimeout(async () => {
            embed.setDescription('**Are you sure you want to lock all channels in this server?**');

            const row = new ActionRowBuilder().addComponents(
                new ButtonBuilder()
                    .setCustomId('confirm_lockall')
                    .setLabel('Yes')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('cancel_lockall')
                    .setLabel('No')
                    .setStyle(ButtonStyle.Danger)
            );

            await msg.edit({ embeds: [embed], components: [row] });

            const filter = (interaction) => interaction.user.id === message.author.id;
            const collector = msg.createMessageComponentCollector({ filter, time: 15000 });

            collector.on('collect', async (interaction) => {
                if (interaction.customId === 'confirm_lockall') {
                    await interaction.deferUpdate();

                    embed.setDescription('<:tick:1343079179641557053> | **Successfully started locking all channels.**');
                    await msg.edit({ embeds: [embed], components: [] });

                    let lockedChannels = 0;
                    message.guild.channels.cache.forEach(async (channel) => {
                        if (channel.manageable) {
                            await channel.permissionOverwrites.edit(message.guild.roles.everyone, {
                                SendMessages: false
                            }).catch(() => {});
                            lockedChannels++;
                        }
                    });

                    setTimeout(() => {
                        message.channel.send({
                            embeds: [new EmbedBuilder()
                                .setColor('#00FF00')
                                .setDescription(`<:tick:1180470648053702657> | **Successfully locked ${lockedChannels} channels**`)]
                        });
                    }, 2000);
                } else if (interaction.customId === 'cancel_lockall') {
                    await interaction.deferUpdate();
                    embed.setDescription('❌ | **Locking process cancelled.**');
                    await msg.edit({ embeds: [embed], components: [] });
                }
            });

            collector.on('end', () => {
                msg.edit({ components: [] }).catch(() => {});
            });

        }, 2000);
    }
};