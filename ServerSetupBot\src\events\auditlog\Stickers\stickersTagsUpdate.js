const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildStickerUpdate,
    async execute(oldSticker, newSticker, client) {
        try {
            // Only handle tags changes
            if (oldSticker.tags === newSticker.tags) return;
            
            console.log(`🏷️ Sticker tags updated: ${newSticker.name} in ${newSticker.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newSticker.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'STICKERS_TAGS_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'STICKERS_TAGS_UPDATE',
                user: 'System',
                userId: null,
                action: 'Tags sticker được cập nhật',
                details: `Tags của sticker **${newSticker.name}** đã được cập nhật`,
                target: newSticker.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the sticker tags from audit logs
            try {
                const auditLogs = await newSticker.guild.fetchAuditLogs({
                    type: 91, // STICKER_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newSticker.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Tags sticker được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for sticker tags update');
            }
            
            // Add to database
            await client.db.addAuditLog(newSticker.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🏷️ Tags sticker được cập nhật',
                `Vừa có tags sticker được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Tên sticker',
                    value: `- ${newSticker.name}`,
                    inline: true
                },
                {
                    name: '> ID sticker',
                    value: `- ${newSticker.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add tags comparison
            const oldTags = oldSticker.tags || 'Không có tags';
            const newTags = newSticker.tags || 'Không có tags';
            
            embed.addFields([
                {
                    name: '> Tags cũ',
                    value: `- ${oldTags}`,
                    inline: true
                },
                {
                    name: '> Tags mới',
                    value: `- ${newTags}`,
                    inline: true
                }
            ]);
            
            // Add sticker description if available
            if (newSticker.description) {
                embed.addFields({
                    name: '> Mô tả',
                    value: `- ${newSticker.description}`,
                    inline: false
                });
            }
            
            // Add sticker format
            const formatTypes = {
                1: 'PNG',
                2: 'APNG',
                3: 'Lottie',
                4: 'GIF'
            };
            
            embed.addFields({
                name: '> Định dạng',
                value: `- ${formatTypes[newSticker.format] || 'Unknown'}`,
                inline: true
            });
            
            // Add sticker type
            const stickerTypes = {
                1: 'Standard (Discord)',
                2: 'Guild (Server custom)'
            };
            
            embed.addFields({
                name: '> Loại sticker',
                value: `- ${stickerTypes[newSticker.type] || 'Unknown'}`,
                inline: true
            });
            
            // Add sticker availability
            embed.addFields({
                name: '> Có thể sử dụng',
                value: `- ${newSticker.available ? 'Có' : 'Không'}`,
                inline: true
            });
            
            // Add sticker URL
            if (newSticker.url) {
                embed.addFields({
                    name: '> URL sticker',
                    value: `- [Xem sticker](${newSticker.url})`,
                    inline: true
                });
            }
            
            // Add creation info if available
            if (newSticker.createdTimestamp) {
                embed.addFields({
                    name: '> Được tạo lúc',
                    value: `- <t:${Math.floor(newSticker.createdTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add impact explanation
            if (newSticker.tags && !oldSticker.tags) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Sticker giờ đây có tags để tìm kiếm',
                        '• Dễ dàng phân loại và tổ chức',
                        '• Cải thiện khả năng tìm kiếm',
                        '• Giúp autocomplete hoạt động tốt hơn'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for added tags
            } else if (!newSticker.tags && oldSticker.tags) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Sticker không còn tags',
                        '• Khó tìm kiếm hơn',
                        '• Giảm khả năng phân loại',
                        '• Autocomplete có thể kém hiệu quả'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0xe74c3c); // Red for removed tags
            } else if (newSticker.tags && oldSticker.tags) {
                embed.addFields({
                    name: '> 🔄 Tác động',
                    value: [
                        '• Tags sticker đã được cập nhật',
                        '• Thay đổi cách tìm kiếm sticker',
                        '• Có thể ảnh hưởng đến autocomplete',
                        '• Cải thiện việc phân loại'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add tags guidelines
            embed.addFields({
                name: '> 💡 Lưu ý về tags sticker',
                value: [
                    '• Tags giúp tìm kiếm sticker dễ dàng',
                    '• Sử dụng từ khóa liên quan đến sticker',
                    '• Có thể dùng nhiều tags cách nhau bằng dấu phẩy',
                    '• Tags ảnh hưởng đến autocomplete'
                ].join('\n'),
                inline: false
            });
            
            // Parse and display individual tags if available
            if (newSticker.tags) {
                const tagsList = newSticker.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
                if (tagsList.length > 0) {
                    embed.addFields({
                        name: '> 🔍 Danh sách tags mới',
                        value: tagsList.map(tag => `• ${tag}`).join('\n'),
                        inline: false
                    });
                }
            }
            
            // Add server sticker info
            const currentStickerCount = newSticker.guild.stickers.cache.size;
            const maxStickers = newSticker.guild.premiumTier >= 2 ? 60 : 0;
            
            embed.addFields([
                {
                    name: '> Số sticker trong server',
                    value: `- ${currentStickerCount}/${maxStickers} stickers`,
                    inline: true
                },
                {
                    name: '> Server Boost Level',
                    value: `- Level ${newSticker.guild.premiumTier}`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do thay đổi',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set sticker as thumbnail if possible
            if (newSticker.url) {
                embed.setThumbnail(newSticker.url);
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newSticker.guild.name} • Sticker Tags Update`,
                iconURL: newSticker.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Sticker tags update logged: ${newSticker.name}`);
            
        } catch (error) {
            console.error('Error in stickersTagsUpdate audit log:', error);
        }
    }
};
