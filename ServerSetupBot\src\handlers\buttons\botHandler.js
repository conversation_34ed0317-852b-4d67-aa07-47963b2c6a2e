const {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRow<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ButtonStyle,
} = require("discord.js");
const {
  createInfoEmbed,
  createSuccessEmbed,
  createErrorEmbed,
} = require("../../utils/embedBuilder.js");

module.exports = {
  async execute(interaction, client, params) {
    const action = params[0]; // analysis, search
    const subAction = params[1]; // refresh, security, export, prev, next, info, filter, random
    const param3 = params[2]; // bot ID, page, search term

    try {
      if (action === "analysis") {
        switch (subAction) {
          case "refresh":
            await handleAnalysisRefresh(interaction, client, param3);
            break;
          case "security":
            await handleAnalysisSecurity(interaction, client, param3);
            break;
          case "export":
            await handleAnalysisExport(interaction, client, param3);
            break;
          default:
            await handleDefault(interaction, client);
            break;
        }
      } else if (action === "search") {
        switch (subAction) {
          case "refresh":
            await handleSearchRefresh(interaction, client);
            break;
          case "filter":
            await handleSearchFilter(interaction, client);
            break;
          case "random":
            await handleSearchRandom(interaction, client);
            break;
          case "prev":
            await handleSearchPrev(interaction, client, param3);
            break;
          case "next":
            await handleSearchNext(interaction, client, param3);
            break;
          default:
            await handleDefault(interaction, client);
            break;
        }
      } else {
        await handleDefault(interaction, client);
      }
    } catch (error) {
      console.error("Lỗi trong bot button handler:", error);

      const errorEmbed = createErrorEmbed(
        "Lỗi hệ thống!",
        "Không thể xử lý yêu cầu. Vui lòng thử lại sau!"
      );

      if (interaction.replied || interaction.deferred) {
        await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
      } else {
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
      }
    }
  },
};

async function handleAnalysisRefresh(interaction, client, botId) {
  await interaction.deferUpdate();

  try {
    // Fetch bot info
    const bot = await client.users.fetch(botId).catch(() => null);
    if (!bot) {
      const errorEmbed = createErrorEmbed(
        "Bot không tìm thấy!",
        "Không thể tìm thấy thông tin bot này."
      );
      return await interaction.editReply({ embeds: [errorEmbed] });
    }

    // Create refreshed analysis embed
    const embed = createInfoEmbed(
      `🤖 Phân tích Bot: ${bot.username} - Đã làm mới`,
      `Thông tin bot đã được cập nhật lúc <t:${Math.floor(
        Date.now() / 1000
      )}:T>`
    );

    // Add bot info
    embed.addFields(
      { name: "📛 Tên Bot", value: bot.username, inline: true },
      { name: "🆔 ID", value: bot.id, inline: true },
      {
        name: "📅 Tạo lúc",
        value: `<t:${Math.floor(bot.createdTimestamp / 1000)}:F>`,
        inline: true,
      },
      {
        name: "🔗 Avatar",
        value: bot.displayAvatarURL()
          ? "[Xem Avatar](${bot.displayAvatarURL()})"
          : "Không có",
        inline: true,
      },
      {
        name: "✅ Verified",
        value: bot.flags?.has("VerifiedBot") ? "Có" : "Không",
        inline: true,
      },
      {
        name: "🏷️ Tag",
        value: `${bot.username}#${bot.discriminator}`,
        inline: true,
      }
    );

    // Add analysis data
    const analysisData = generateMockAnalysisData();
    embed.addFields(
      {
        name: "📊 Điểm tin cậy",
        value: `${analysisData.trustScore}/100`,
        inline: true,
      },
      {
        name: "🛡️ Mức độ bảo mật",
        value: analysisData.securityLevel,
        inline: true,
      },
      {
        name: "⚠️ Cảnh báo",
        value: analysisData.warnings.toString(),
        inline: true,
      }
    );

    embed.setThumbnail(bot.displayAvatarURL({ dynamic: true }));
    embed.setColor(
      analysisData.trustScore > 70
        ? 0x00ff00
        : analysisData.trustScore > 40
        ? 0xffff00
        : 0xff0000
    );

    const components = createAnalysisComponents(botId);

    await interaction.editReply({
      embeds: [embed],
      components: components,
    });
  } catch (error) {
    console.error("Lỗi khi refresh bot analysis:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi làm mới!",
      "Không thể làm mới thông tin bot."
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleAnalysisSecurity(interaction, client, botId) {
  await interaction.deferUpdate();

  try {
    const bot = await client.users.fetch(botId).catch(() => null);
    if (!bot) {
      const errorEmbed = createErrorEmbed(
        "Bot không tìm thấy!",
        "Không thể tìm thấy thông tin bot này."
      );
      return await interaction.editReply({ embeds: [errorEmbed] });
    }

    const embed = createInfoEmbed(
      `🛡️ Chi tiết bảo mật: ${bot.username}`,
      "Phân tích chi tiết về mức độ bảo mật của bot"
    );

    const securityData = generateMockSecurityData();

    embed.addFields(
      {
        name: "🔐 Quyền hạn",
        value: securityData.permissions.join("\n"),
        inline: false,
      },
      {
        name: "⚠️ Rủi ro tiềm ẩn",
        value: securityData.risks.join("\n"),
        inline: false,
      },
      {
        name: "✅ Điểm mạnh",
        value: securityData.strengths.join("\n"),
        inline: false,
      },
      {
        name: "📋 Khuyến nghị",
        value: securityData.recommendations.join("\n"),
        inline: false,
      }
    );

    embed.setThumbnail(bot.displayAvatarURL({ dynamic: true }));
    embed.setColor(0x3498db);

    const components = createAnalysisComponents(botId);

    await interaction.editReply({
      embeds: [embed],
      components: components,
    });
  } catch (error) {
    console.error("Lỗi khi xem security details:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi bảo mật!",
      "Không thể lấy thông tin bảo mật của bot."
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleAnalysisExport(interaction, client, botId) {
  await interaction.deferUpdate();

  try {
    const bot = await client.users.fetch(botId).catch(() => null);
    if (!bot) {
      const errorEmbed = createErrorEmbed(
        "Bot không tìm thấy!",
        "Không thể tìm thấy thông tin bot này."
      );
      return await interaction.editReply({ embeds: [errorEmbed] });
    }

    const embed = createInfoEmbed(
      `📊 Xuất báo cáo: ${bot.username}`,
      "Tính năng xuất báo cáo phân tích bot đang được phát triển.\n\nSẽ hỗ trợ các định dạng:\n• PDF Report\n• JSON Data\n• CSV Export\n• HTML Report"
    );

    embed.addFields({
      name: "📋 Nội dung báo cáo",
      value:
        "• Thông tin cơ bản bot\n• Phân tích quyền hạn\n• Đánh giá bảo mật\n• Lịch sử hoạt động\n• Khuyến nghị cải thiện",
      inline: false,
    });

    embed.setThumbnail(bot.displayAvatarURL({ dynamic: true }));
    embed.setColor(0x9b59b6);

    const components = createAnalysisComponents(botId);

    await interaction.editReply({
      embeds: [embed],
      components: components,
    });
  } catch (error) {
    console.error("Lỗi khi export report:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi xuất báo cáo!",
      "Không thể xuất báo cáo phân tích bot."
    );
    await interaction.editReply({ embeds: [errorEmbed] });
  }
}

async function handleDefault(interaction, client) {
  await interaction.deferUpdate();

  const embed = createInfoEmbed(
    "🤖 Bot Analysis",
    "Tính năng phân tích bot đang được phát triển."
  );

  await interaction.editReply({ embeds: [embed] });
}

// Helper functions
function generateMockAnalysisData() {
  return {
    trustScore: Math.floor(Math.random() * 100),
    securityLevel: ["Cao", "Trung bình", "Thấp"][Math.floor(Math.random() * 3)],
    warnings: Math.floor(Math.random() * 5),
  };
}

function generateMockSecurityData() {
  return {
    permissions: [
      "✅ Đọc tin nhắn",
      "✅ Gửi tin nhắn",
      "⚠️ Quản lý tin nhắn",
      "❌ Quản trị viên",
    ],
    risks: [
      "⚠️ Có quyền xóa tin nhắn",
      "⚠️ Có thể kick thành viên",
      "✅ Không có quyền ban",
    ],
    strengths: [
      "✅ Không yêu cầu quyền quá mức",
      "✅ Có verification badge",
      "✅ Hoạt động ổn định",
    ],
    recommendations: [
      "💡 Giới hạn quyền trong các kênh quan trọng",
      "💡 Theo dõi hoạt động định kỳ",
      "💡 Backup dữ liệu trước khi cấp quyền mới",
    ],
  };
}

function createAnalysisComponents(botId) {
  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(`bot_analysis_refresh_${botId}`)
      .setLabel("🔄 Làm mới")
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId(`bot_analysis_security_${botId}`)
      .setLabel("🛡️ Chi tiết bảo mật")
      .setStyle(ButtonStyle.Primary),
    new ButtonBuilder()
      .setCustomId(`bot_analysis_export_${botId}`)
      .setLabel("📊 Xuất báo cáo")
      .setStyle(ButtonStyle.Success)
  );

  return [buttonRow];
}

async function handleSearchRefresh(interaction, client) {
  await interaction.deferUpdate();

  const embed = createInfoEmbed(
    "🔄 Đã làm mới kết quả tìm kiếm",
    `Kết quả tìm kiếm đã được cập nhật lúc <t:${Math.floor(
      Date.now() / 1000
    )}:T>`
  );

  embed.addFields({
    name: "📊 Thống kê mới",
    value:
      "• Cập nhật danh sách bot\n• Làm mới rating và số lượng server\n• Kiểm tra trạng thái hoạt động",
    inline: false,
  });

  const components = createSearchComponents();

  await interaction.editReply({ embeds: [embed], components });
}

async function handleSearchFilter(interaction, client) {
  await interaction.deferUpdate();

  const embed = createInfoEmbed(
    "🔧 Bộ lọc tìm kiếm",
    "Tính năng bộ lọc nâng cao đang được phát triển.\n\nSẽ bao gồm:\n• Lọc theo category\n• Lọc theo rating\n• Lọc theo số lượng server\n• Lọc theo tính năng\n• Lọc theo ngôn ngữ"
  );

  const components = createSearchComponents();

  await interaction.editReply({ embeds: [embed], components });
}

async function handleSearchRandom(interaction, client) {
  await interaction.deferUpdate();

  const randomBots = generateRandomBots();

  const embed = createInfoEmbed(
    "🎲 Bot ngẫu nhiên",
    `Đây là ${randomBots.length} bot được chọn ngẫu nhiên cho bạn`
  );

  embed.addFields({
    name: "🎯 Gợi ý ngẫu nhiên",
    value: randomBots
      .map(
        (bot, index) =>
          `**${index + 1}.** [${bot.name}](${bot.invite_url})\n` +
          `└ ${bot.description}\n` +
          `└ 📊 ${bot.servers.toLocaleString()} servers • ⭐ ${
            bot.rating
          }/5 • 🏷️ ${bot.category}`
      )
      .join("\n\n"),
    inline: false,
  });

  const components = createSearchComponents();

  await interaction.editReply({ embeds: [embed], components });
}

async function handleSearchPrev(interaction, client, currentPage) {
  await interaction.deferUpdate();

  const page = parseInt(currentPage) || 0;
  const newPage = Math.max(0, page - 1);

  const embed = createInfoEmbed(
    `📄 Trang ${newPage + 1}`,
    "Đã chuyển về trang trước"
  );

  const components = createPaginationComponents(newPage);

  await interaction.editReply({ embeds: [embed], components });
}

async function handleSearchNext(interaction, client, currentPage) {
  await interaction.deferUpdate();

  const page = parseInt(currentPage) || 0;
  const newPage = page + 1;

  const embed = createInfoEmbed(
    `📄 Trang ${newPage + 1}`,
    "Đã chuyển sang trang tiếp theo"
  );

  const components = createPaginationComponents(newPage);

  await interaction.editReply({ embeds: [embed], components });
}

function generateRandomBots() {
  const allBots = [
    {
      name: "MEE6",
      description: "Bot moderation và leveling phổ biến",
      invite_url: "https://mee6.xyz/",
      servers: 16500000,
      rating: 4.8,
      category: "moderation",
    },
    {
      name: "Dyno",
      description: "Bot đa năng với nhiều tính năng",
      invite_url: "https://dyno.gg/",
      servers: 8200000,
      rating: 4.6,
      category: "moderation",
    },
    {
      name: "Carl-bot",
      description: "Automod và reaction roles mạnh mẽ",
      invite_url: "https://carl-bot.io/",
      servers: 12000000,
      rating: 4.7,
      category: "utility",
    },
    {
      name: "Ticket Tool",
      description: "Hệ thống ticket chuyên nghiệp",
      invite_url: "https://tickettool.xyz/",
      servers: 3500000,
      rating: 4.9,
      category: "utility",
    },
    {
      name: "Mudae",
      description: "Game waifu và husbando",
      invite_url: "https://mudae.net/",
      servers: 2800000,
      rating: 4.6,
      category: "fun",
    },
  ];

  // Shuffle and return 3 random bots
  const shuffled = allBots.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, 3);
}

function createSearchComponents() {
  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("bot_search_refresh")
      .setLabel("🔄 Làm mới")
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId("bot_search_filter")
      .setLabel("🔧 Bộ lọc")
      .setStyle(ButtonStyle.Primary),
    new ButtonBuilder()
      .setCustomId("bot_search_random")
      .setLabel("🎲 Ngẫu nhiên")
      .setStyle(ButtonStyle.Success)
  );

  return [buttonRow];
}

function createPaginationComponents(currentPage) {
  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(`bot_search_prev_${currentPage}`)
      .setLabel("◀ Trước")
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(currentPage === 0),
    new ButtonBuilder()
      .setCustomId(`bot_search_info_${currentPage}`)
      .setLabel(`Trang ${currentPage + 1}`)
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(true),
    new ButtonBuilder()
      .setCustomId(`bot_search_next_${currentPage}`)
      .setLabel("Sau ▶")
      .setStyle(ButtonStyle.Secondary)
  );

  return [buttonRow];
}
