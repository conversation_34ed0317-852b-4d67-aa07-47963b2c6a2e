const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle type changes
            if (oldChannel.type === newChannel.type) return;
            
            console.log(`🔄 Channel type updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_TYPE_UPDATE')) return;
            
            // Get channel types mapping
            const channelTypes = {
                0: "Kênh văn bản",
                2: "<PERSON>ênh thoại", 
                4: "<PERSON><PERSON> mục",
                5: "<PERSON><PERSON>nh tin tức",
                10: "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> tin tức",
                11: "<PERSON>ễ<PERSON> đàn công khai",
                12: "<PERSON>ễn đàn riêng tư",
                13: "Kênh sân khấu",
                15: "Forum",
                16: "Media"
            };
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_TYPE_UPDATE',
                user: 'System',
                userId: null,
                action: 'Loại kênh được thay đổi',
                details: `Loại kênh **${newChannel.name}** đã được thay đổi từ ${channelTypes[oldChannel.type]} thành ${channelTypes[newChannel.type]}`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the channel type from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Loại kênh được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for channel type update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔄 Loại kênh thay đổi',
                `Vừa có loại kênh được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Loại cũ',
                    value: `- ${channelTypes[oldChannel.type] || 'Không xác định'}`,
                    inline: true
                },
                {
                    name: '> Loại mới',
                    value: `- ${channelTypes[newChannel.type] || 'Không xác định'}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Type Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel type update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelTypeUpdate audit log:', error);
        }
    }
};
