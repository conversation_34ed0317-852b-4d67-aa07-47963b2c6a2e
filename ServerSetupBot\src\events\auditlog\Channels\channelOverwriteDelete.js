const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Check if permission overwrites were removed
            const oldOverwrites = oldChannel.permissionOverwrites.cache;
            const newOverwrites = newChannel.permissionOverwrites.cache;
            
            const removedOverwrites = oldOverwrites.filter(overwrite => !newOverwrites.has(overwrite.id));
            
            if (removedOverwrites.size === 0) return;
            
            console.log(`🗑️ Channel permission overwrite deleted: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_OVERWRITE_DELETE')) return;
            
            // Process each removed overwrite
            for (const overwrite of removedOverwrites.values()) {
                // Create event data
                const eventData = {
                    eventType: 'CHANNEL_OVERWRITE_DELETE',
                    user: 'System',
                    userId: null,
                    action: 'Permission overwrite được xóa',
                    details: `Permission overwrite đã được xóa khỏi kênh **${newChannel.name}**`,
                    target: newChannel.name,
                    channel: newChannel.name,
                    timestamp: new Date().toISOString()
                };
                
                // Try to get who deleted the overwrite from audit logs
                try {
                    const auditLogs = await newChannel.guild.fetchAuditLogs({
                        type: 15, // CHANNEL_OVERWRITE_DELETE
                        limit: 1
                    });
                    
                    const auditEntry = auditLogs.entries.first();
                    if (auditEntry && auditEntry.target?.id === newChannel.id) {
                        eventData.user = auditEntry.executor.tag;
                        eventData.userId = auditEntry.executor.id;
                        eventData.action = `Permission overwrite được xóa bởi ${auditEntry.executor.tag}`;
                        if (auditEntry.reason) {
                            eventData.reason = auditEntry.reason;
                        }
                    }
                } catch (error) {
                    console.log('Could not fetch audit logs for channel overwrite delete');
                }
                
                // Add to database
                await client.db.addAuditLog(newChannel.guild.id, eventData);
                
                // Send to audit log channel
                const auditChannel = await client.channels.fetch(config.channelId);
                if (!auditChannel) continue;
                
                // Create embed
                const embed = createInfoEmbed(
                    '🗑️ Permission overwrite được xóa',
                    `Vừa có permission overwrite được xóa khỏi kênh`
                );
                
                embed.setColor(0xe74c3c); // Red for delete
                
                // Get target info (role or member)
                let targetInfo = 'Unknown';
                let targetType = 'Unknown';
                
                if (overwrite.type === 0) { // Role
                    const role = newChannel.guild.roles.cache.get(overwrite.id);
                    targetInfo = role ? role.name : `Unknown Role (${overwrite.id})`;
                    targetType = 'Role';
                } else if (overwrite.type === 1) { // Member
                    const member = newChannel.guild.members.cache.get(overwrite.id);
                    targetInfo = member ? member.user.tag : `Unknown Member (${overwrite.id})`;
                    targetType = 'Member';
                }
                
                embed.addFields([
                    {
                        name: '> Kênh',
                        value: `- ${newChannel}`,
                        inline: true
                    },
                    {
                        name: '> ID kênh',
                        value: `- ${newChannel.id}`,
                        inline: true
                    },
                    {
                        name: '> Người xóa',
                        value: `- ${eventData.user}`,
                        inline: true
                    },
                    {
                        name: '> Đối tượng bị xóa',
                        value: `- ${targetInfo}`,
                        inline: true
                    },
                    {
                        name: '> Loại đối tượng',
                        value: `- ${targetType}`,
                        inline: true
                    },
                    {
                        name: '> Thời gian',
                        value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                        inline: true
                    }
                ]);
                
                // Add permissions that were removed
                const allowedPerms = [];
                const deniedPerms = [];
                
                overwrite.allow.toArray().forEach(perm => allowedPerms.push(perm));
                overwrite.deny.toArray().forEach(perm => deniedPerms.push(perm));
                
                if (allowedPerms.length > 0) {
                    embed.addFields({
                        name: '> ✅ Permissions đã cho phép (bị xóa)',
                        value: allowedPerms.map(perm => `• ${perm}`).join('\n'),
                        inline: false
                    });
                }
                
                if (deniedPerms.length > 0) {
                    embed.addFields({
                        name: '> ❌ Permissions đã từ chối (bị xóa)',
                        value: deniedPerms.map(perm => `• ${perm}`).join('\n'),
                        inline: false
                    });
                }
                
                // Add impact note
                embed.addFields({
                    name: '> 📝 Tác động',
                    value: `- ${targetType} sẽ sử dụng permissions mặc định của kênh`,
                    inline: false
                });
                
                if (eventData.reason) {
                    embed.addFields({
                        name: '> Lý do',
                        value: `- ${eventData.reason}`,
                        inline: false
                    });
                }
                
                embed.setTimestamp();
                embed.setFooter({
                    text: `${newChannel.guild.name} • Channel Overwrite Delete`,
                    iconURL: newChannel.guild.iconURL()
                });
                
                await auditChannel.send({ embeds: [embed] });
                console.log(`✅ Channel overwrite delete logged: ${newChannel.name}`);
            }
            
        } catch (error) {
            console.error('Error in channelOverwriteDelete audit log:', error);
        }
    }
};
