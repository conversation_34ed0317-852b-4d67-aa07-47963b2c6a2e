(function(_0x20aa9f,_0x64ce96){const _0x639c0f=_0x20aa9f();function _0x439165(_0x69e7e7,_0x4970c0){return _0xb734(_0x4970c0- -0xfb,_0x69e7e7);}while(!![]){try{const _0x258af4=parseInt(_0x439165(0x17,0x2a))/0x1*(-parseInt(_0x439165(-0x2,0x20))/0x2)+-parseInt(_0x439165(-0x1,0x16))/0x3+-parseInt(_0x439165(0x3c,0x1a))/0x4+-parseInt(_0x439165(0xe,0x1c))/0x5*(-parseInt(_0x439165(0x4c,0x5d))/0x6)+-parseInt(_0x439165(-0x4,0x24))/0x7*(parseInt(_0x439165(0x2c,0xe))/0x8)+-parseInt(_0x439165(0x55,0x33))/0x9+parseInt(_0x439165(0x26,0x2b))/0xa;if(_0x258af4===_0x64ce96)break;else _0x639c0f['push'](_0x639c0f['shift']());}catch(_0x151153){_0x639c0f['push'](_0x639c0f['shift']());}}}(_0x4183,0x5a765));const {SlashCommandBuilder,EmbedBuilder,PermissionsBitField}=require('discord.js'),BackupModel=require(_0x110961(0x9b,0x82)),backup=require(_0x110961(0x80,0x6d)),yaml=require(_0x110961(0x70,0x8e)),fs=require('fs'),config=yaml['load'](fs[_0x110961(0xa2,0x95)](_0x110961(0xc6,0x9e),_0x110961(0x87,0xa7))),lang=yaml[_0x110961(0x9a,0xae)](fs[_0x110961(0x91,0x95)](_0x110961(0x99,0xa5),'utf8'));module['exports']={'data':new SlashCommandBuilder()[_0x110961(0x81,0x77)](_0x110961(0x7c,0x90))[_0x110961(0xa1,0x85)](_0x110961(0x71,0x89))[_0x110961(0x8f,0x97)](_0x1b7893=>_0x1b7893['setName'](_0x110961(0x7e,0x80))[_0x110961(0x5d,0x85)](_0x110961(0x73,0x79)))[_0x110961(0x97,0x97)](_0x49fed6=>_0x49fed6[_0x110961(0x57,0x77)](_0x110961(0x91,0x93))[_0x110961(0x78,0x85)](_0x110961(0x65,0x63))[_0x110961(0x8b,0x91)](_0x1c9a37=>_0x1c9a37['setName']('id')['setDescription'](_0x110961(0x76,0x6e))[_0x110961(0xa4,0x7c)](!![])))[_0x110961(0x6d,0x97)](_0x4452bc=>_0x4452bc[_0x110961(0x74,0x77)](_0x110961(0xa3,0xae))[_0x110961(0xaa,0x85)]('Load\x20a\x20backup')[_0x110961(0xbb,0x91)](_0x4c696c=>_0x4c696c[_0x110961(0x77,0x77)]('id')[_0x110961(0x82,0x85)](_0x110961(0x6e,0x6e))['setRequired'](!![])))[_0x110961(0xa2,0x97)](_0x14eba7=>_0x14eba7['setName'](_0x110961(0xa5,0xa0))[_0x110961(0x79,0x85)]('List\x20all\x20server\x20backups'))['addSubcommand'](_0x2fccda=>_0x2fccda[_0x110961(0x9e,0x77)]('info')[_0x110961(0x8e,0x85)](_0x110961(0xa9,0x98))[_0x110961(0xbb,0x91)](_0xf188f4=>_0xf188f4['setName']('id')[_0x110961(0x82,0x85)](_0x110961(0x61,0x6e))[_0x110961(0x5a,0x7c)](!![]))),'category':_0x110961(0x6c,0x5f),async 'execute'(_0x259c4a,_0x4a604c){if(!_0x259c4a['member'][_0x12f118(0x4b5,0x4b3)][_0x12f118(0x47f,0x4a5)](PermissionsBitField[_0x12f118(0x46e,0x497)][_0x12f118(0x4b6,0x4d1)]))return _0x259c4a[_0x12f118(0x47e,0x46c)]({'content':lang[_0x12f118(0x4b9,0x4b7)],'ephemeral':!![]});const _0x5b8bfb=_0x259c4a[_0x12f118(0x49a,0x48d)][_0x12f118(0x4a8,0x48f)](),_0x567925=_0x259c4a[_0x12f118(0x49a,0x4b3)][_0x12f118(0x4b8,0x499)]('id');function _0x12f118(_0x4db68c,_0x1c56c6){return _0x110961(_0x1c56c6,_0x4db68c-0x40d);}switch(_0x5b8bfb){case _0x12f118(0x48d,0x49b):await createBackup(_0x259c4a);break;case'delete':await deleteBackup(_0x259c4a,_0x567925);break;case _0x12f118(0x4bb,0x4da):await loadBackup(_0x259c4a,_0x567925);break;case _0x12f118(0x4ad,0x4d7):await listBackups(_0x259c4a);break;case _0x12f118(0x4ae,0x4b7):await backupInfo(_0x259c4a,_0x567925);break;default:await _0x259c4a[_0x12f118(0x47e,0x4a7)]({'content':_0x12f118(0x4ac,0x4a4),'ephemeral':!![]});}}};function _0x110961(_0x558896,_0xbcb4f6){return _0xb734(_0xbcb4f6- -0xab,_0x558896);}function _0xb734(_0x14cb7e,_0x1eabc5){const _0x41837f=_0x4183();return _0xb734=function(_0xb73402,_0x23411c){_0xb73402=_0xb73402-0x107;let _0x4f164b=_0x41837f[_0xb73402];return _0x4f164b;},_0xb734(_0x14cb7e,_0x1eabc5);}async function createBackup(_0x5c0391){await _0x5c0391[_0x192102(0xc9,0xd3)]({'content':lang[_0x192102(0x107,0xe1)],'ephemeral':!![]});function _0x192102(_0x5b3535,_0xc1c968){return _0x110961(_0x5b3535,_0xc1c968-0x62);}try{const _0x4ed818={'maxMessagesPerChannel':0x3e8,'jsonBeautify':!![],'saveImages':_0x192102(0x10f,0x105),'doNotBackup':[],'doNotSave':[]},_0x4b3e8c=await backup[_0x192102(0x10d,0xe2)](_0x5c0391[_0x192102(0xee,0x112)],_0x4ed818),_0x3db6f2=new BackupModel({'backupId':_0x4b3e8c['id'],'guildId':_0x5c0391[_0x192102(0xf0,0x112)]['id'],'data':_0x4b3e8c,'createdAt':new Date()});await _0x3db6f2[_0x192102(0x11b,0xf1)]();const _0x5ade14=new EmbedBuilder()[_0x192102(0xeb,0xe9)]({'name':lang[_0x192102(0xc2,0xc2)],'iconURL':_0x192102(0xf5,0xcd)})[_0x192102(0xec,0xc6)](config[_0x192102(0xe0,0xd7)])[_0x192102(0xf0,0xe7)](_0x192102(0xe6,0xe6)+_0x4b3e8c['id']);await _0x5c0391[_0x192102(0x11c,0xfe)]({'embeds':[_0x5ade14],'ephemeral':!![]});}catch(_0x4877ce){console[_0x192102(0xf2,0xf4)](_0x192102(0xa0,0xcb),_0x4877ce),await _0x5c0391['followUp']({'content':'An\x20error\x20occurred\x20while\x20creating\x20the\x20backup.','ephemeral':!![]});}}async function deleteBackup(_0x56382d,_0x10d775){function _0x1d4ee7(_0x2dfdc2,_0x3d28d5){return _0x110961(_0x3d28d5,_0x2dfdc2-0x209);}try{const _0x6174eb=await BackupModel['findOneAndDelete']({'backupId':_0x10d775});if(!_0x6174eb){const _0x3256bd=new EmbedBuilder()[_0x1d4ee7(0x290,0x273)]({'name':lang[_0x1d4ee7(0x29f,0x2ca)],'iconURL':_0x1d4ee7(0x265,0x259)})[_0x1d4ee7(0x26d,0x291)](config['ErrorEmbedColor'])[_0x1d4ee7(0x28e,0x29f)](_0x1d4ee7(0x291,0x284)+_0x10d775);await _0x56382d[_0x1d4ee7(0x27a,0x296)]({'embeds':[_0x3256bd],'ephemeral':!![]});return;}const _0x572376=new EmbedBuilder()[_0x1d4ee7(0x290,0x2a0)]({'name':lang['SuccessEmbedTitle'],'iconURL':_0x1d4ee7(0x274,0x27b)})[_0x1d4ee7(0x26d,0x264)](config[_0x1d4ee7(0x27e,0x26a)])[_0x1d4ee7(0x28e,0x29b)](_0x1d4ee7(0x29d,0x27f));await _0x56382d['reply']({'embeds':[_0x572376],'ephemeral':!![]});}catch(_0x10f042){console[_0x1d4ee7(0x29b,0x2a4)](_0x1d4ee7(0x286,0x2a3),_0x10f042);const _0x51bc7b=new EmbedBuilder()[_0x1d4ee7(0x290,0x2ad)]({'name':lang['ErrorEmbedTitle'],'iconURL':'https://i.imgur.com/MdiCK2c.png'})[_0x1d4ee7(0x26d,0x246)](config[_0x1d4ee7(0x27f,0x273)])[_0x1d4ee7(0x28e,0x292)](_0x1d4ee7(0x293,0x2b1)+_0x10f042[_0x1d4ee7(0x266,0x26a)]);await _0x56382d[_0x1d4ee7(0x27a,0x26f)]({'embeds':[_0x51bc7b],'ephemeral':!![]});}}async function loadBackup(_0x250cf3,_0x3c50fd){function _0x424d3b(_0x219e8c,_0x211ffb){return _0x110961(_0x219e8c,_0x211ffb- -0x16d);}try{const _0x530aba=await BackupModel[_0x424d3b(-0xdc,-0xfa)]({'backupId':_0x3c50fd});if(!_0x530aba){await _0x250cf3[_0x424d3b(-0x111,-0xfc)]({'content':_0x424d3b(-0xc0,-0xe5)+_0x3c50fd,'ephemeral':!![]});return;}await backup[_0x424d3b(-0xb3,-0xbf)](_0x3c50fd,_0x250cf3['guild']),await _0x250cf3['reply']({'content':_0x424d3b(-0xe6,-0xcb),'ephemeral':!![]});}catch(_0x30b5b7){console[_0x424d3b(-0xb6,-0xdb)](_0x424d3b(-0xb2,-0xc9),_0x30b5b7),await _0x250cf3[_0x424d3b(-0x116,-0xfc)]({'content':'An\x20error\x20occurred\x20while\x20loading\x20the\x20backup:\x20'+_0x30b5b7[_0x424d3b(-0x11d,-0x110)],'ephemeral':!![]});}}async function listBackups(_0x39d2be){function _0x2088f0(_0x2cc734,_0x257879){return _0x110961(_0x2cc734,_0x257879- -0x62);}try{const _0x161a8b=await BackupModel['find']({'guildId':_0x39d2be[_0x2088f0(0x57,0x4e)]['id']});if(_0x161a8b[_0x2088f0(0x18,0x16)]===0x0){await _0x39d2be[_0x2088f0(0x20,0xf)]({'content':_0x2088f0(0x3d,0x3b),'ephemeral':!![]});return;}const _0x2a165b=_0x161a8b[_0x2088f0(-0xc,0x5)]((_0x38ec50,_0x525b36)=>{function _0x4f9321(_0x51afd4,_0x4127ee){return _0x2088f0(_0x51afd4,_0x4127ee-0xa0);}return _0x4f9321(0xed,0xd8)+_0x38ec50[_0x4f9321(0xb2,0xc9)]+_0x4f9321(0xa1,0xa0)+_0x38ec50['createdAt'][_0x4f9321(0x9c,0xad)]();})[_0x2088f0(0x6e,0x44)]('\x0a');await _0x39d2be[_0x2088f0(0x30,0xf)]({'content':'Available\x20Backups:\x0a'+_0x2a165b,'ephemeral':!![]});}catch(_0x432301){console[_0x2088f0(0x49,0x30)](_0x2088f0(0x3a,0x1c),_0x432301),await _0x39d2be[_0x2088f0(-0x1,0xf)]({'content':_0x2088f0(0x1e,0x3),'ephemeral':!![]});}}function _0x4183(){const _0x143a54=['No\x20backup\x20found\x20with\x20ID:\x20','Manage\x20server\x20backups','An\x20error\x20occurred\x20while\x20trying\x20to\x20delete\x20the\x20backup:\x20','backupId','guildId','options','js-yaml','save','backup','addStringOption','error','delete','Backup\x20deleted\x20successfully.','readFileSync','ErrorEmbedTitle','addSubcommand','Get\x20information\x20about\x20a\x20backup','Error\x20retrieving\x20backup\x20info:','ID:\x20','getSubcommand','followUp','No\x20backups\x20available.','././config.yml','Unknown\x20subcommand','list','info','Backup\x20loaded\x20successfully.','base64','Error\x20loading\x20backup:','././lang.yml','join','utf8','permissions','ManageGuild','\x0aGuild\x20ID:\x20','getString','NoPermsMessage','3032412pAwIxh','load','Backup\x20ID:\x20','guild','https://i.imgur.com/MdiCK2c.png','message','953848LhgDky','Utility','SuccessEmbedTitle','Flags',',\x20Created:\x20','Delete\x20a\x20backup','setColor','An\x20error\x20occurred\x20while\x20listing\x20the\x20backups.','830916AnQpPe','map','createdAt','Error\x20creating\x20backup:','2869672oMVdSi','https://i.imgur.com/7SlmRRa.png','5moAQsr','discord-backup','The\x20backup\x20ID','toLocaleDateString','3460TQEXis','reply','has','findOne','28OGIYFM','SuccessEmbedColor','ErrorEmbedColor','setName','length','Create\x20a\x20backup\x20of\x20the\x20server','227khLrbJ','18547340ICltIM','setRequired','Error\x20deleting\x20backup:','Error\x20listing\x20backups:','BackupCreating','create','\x0aCreated:\x20','../../models/Backup','1130211KawaHa','Backup\x20created\x20successfully.\x20Backup\x20ID:\x20','setDescription','An\x20error\x20occurred\x20while\x20retrieving\x20backup\x20information:\x20','setAuthor'];_0x4183=function(){return _0x143a54;};return _0x4183();}async function backupInfo(_0x2b8033,_0x1e703b){function _0x3c70e4(_0x28ba76,_0x192409){return _0x110961(_0x192409,_0x28ba76-0x14e);}try{const _0x39374d=await BackupModel['findOne']({'backupId':_0x1e703b});if(!_0x39374d){await _0x2b8033['reply']({'content':'No\x20backup\x20found\x20with\x20ID:\x20'+_0x1e703b,'ephemeral':!![]});return;}const _0x187a28=_0x3c70e4(0x1fd,0x221)+_0x39374d[_0x3c70e4(0x1d9,0x1d6)]+_0x3c70e4(0x1cf,0x1a8)+_0x39374d[_0x3c70e4(0x1b6,0x194)][_0x3c70e4(0x1bd,0x1bc)]()+_0x3c70e4(0x1f8,0x1de)+_0x39374d[_0x3c70e4(0x1da,0x1e0)];await _0x2b8033[_0x3c70e4(0x1bf,0x1a8)]({'content':_0x187a28,'ephemeral':!![]});}catch(_0x115a01){console[_0x3c70e4(0x1e0,0x1d1)](_0x3c70e4(0x1e7,0x1c5),_0x115a01),await _0x2b8033[_0x3c70e4(0x1bf,0x196)]({'content':_0x3c70e4(0x1d4,0x1f4)+_0x115a01[_0x3c70e4(0x1ab,0x19c)],'ephemeral':!![]});}}