function _0xf757(){const _0x2e9f14=['\x4d\x45\x53\x53\x41\x47\x45','\x65\x61\x76\x65\x3a','\x35\x30\x36\x39\x38\x35\x33\x5a\x79\x61\x76\x78\x52','\x54\x6e\x55\x53\x49','\x53\x65\x63\x6f\x6e\x64\x61\x72\x79','\x73\x69\x7a\x65','\x72\x6e\x20\x74\x68\x69\x73\x22\x29\x28','\x73\x65\x74\x43\x6f\x6c\x6f\x72','\x3a\x74\x3e','\x73\x6f\x72\x74','\x36\x34\x33\x30\x34\x38\x48\x58\x6f\x6b\x4a\x51','\x54\x79\x70\x65','\x73\x74\x65\x6d','\x23\x46\x46\x30\x30\x30\x30','\x63\x55\x69\x59\x59','\x6c\x69\x6e\x67\x20\x75\x73\x65\x72\x20','\x44\x65\x73\x63\x72\x69\x70\x74\x69\x6f','\x75\x70\x64\x61\x74\x65\x4f\x6e\x65','\x74\x69\x6d\x65\x73\x74\x61\x6d\x70','\x66\x69\x6e\x64','\x6f\x6e\x2f\x6d\x6f\x64\x65\x72\x61\x74','\x63\x6c\x65\x61\x72','\x54\x68\x75\x6d\x62\x6e\x61\x69\x6c','\x4e\x2f\x41','\x43\x4a\x46\x54\x74','\x6e\x6f\x77','\x74\x69\x63\x6b\x65\x74\x49\x64','\x63\x72\x65\x61\x74\x65\x64\x41\x74','\x66\x6f\x72\x6d\x61\x74','\x67\x75\x69\x6c\x64','\x65\x73\x74\x61\x6d\x70','\x4d\x4d\x2f\x44\x44\x2f\x59\x59\x59\x59','\x45\x6d\x6f\x6a\x69','\x55\x6e\x6b\x6e\x6f\x77\x6e','\x64\x69\x73\x70\x6c\x61\x79\x41\x76\x61','\x4e\x61\x6d\x65','\x73\x65\x74\x43\x75\x73\x74\x6f\x6d\x49','\x6f\x70\x65\x6e','\x44\x41\x4e\x47\x45\x52','\x73\x65\x74\x49\x6e\x74\x65\x72\x76\x61','\x65\x73\x73\x69\x6e\x67\x20\x6b\x69\x63','\x73\x65\x6e\x64','\x74\x65\x73\x74','\x6d\x65\x6d\x62\x65\x72\x43\x6f\x75\x6e','\x54\x65\x78\x74','\x63\x68\x65\x6d\x61','\x5c\x28\x20\x2a\x5c\x29','\x70\x6e\x67','\x66\x69\x6e\x64\x4f\x6e\x65\x41\x6e\x64','\x65\x6d\x62\x65\x64\x73','\x75\x74\x66\x38','\x32\x31\x38\x31\x33\x31\x43\x50\x45\x49\x49\x7a','\x49\x6d\x61\x67\x65','\x65\x78\x70\x6f\x72\x74\x73','\x65\x7a\x6f\x6e\x65','\x63\x61\x74\x63\x68','\x67\x50\x6c\x73\x4c','\x69\x63\x6f\x6e\x55\x52\x4c','\x74\x69\x63\x6b\x65\x74\x73','\x4a\x70\x75\x69\x41','\x75\x73\x65\x72','\x74\x69\x6f\x6e','\x66\x69\x6e\x64\x4f\x6e\x65','\x65\x6e\x74\x72\x69\x65\x73','\x44\x61\x6e\x67\x65\x72','\x6a\x73\x2d\x79\x61\x6d\x6c','\x75\x70\x73\x65\x72\x74','\x67\x75\x69\x6c\x64\x44\x61\x74\x61\x53','\x50\x72\x69\x6d\x61\x72\x79','\x31\x34\x48\x74\x6d\x6e\x66\x72','\x6a\x6f\x69\x6e','\x72\x65\x61\x73\x6f\x6e','\x73\x2f\x4d\x6f\x64\x65\x72\x61\x74\x69','\x73\x65\x74\x54\x69\x74\x6c\x65','\x74\x69\x6e\x67\x20\x69\x6e\x76\x69\x74','\x63\x68\x61\x6e\x6e\x65\x6c\x73','\x4d\x65\x6d\x62\x65\x72\x4b\x69\x63\x6b','\x6d\x6f\x6d\x65\x6e\x74\x2d\x74\x69\x6d','\x3a\x46\x3e','\x74\x69\x63\x6b\x65\x74\x64\x65\x6c\x65','\x73\x65\x74\x41\x75\x74\x68\x6f\x72','\x75\x73\x65\x72\x49\x64','\x66\x69\x6c\x74\x65\x72','\x75\x73\x65\x72\x49\x44','\x74\x6f\x55\x70\x70\x65\x72\x43\x61\x73','\x65\x73\x73\x61\x67\x65\x3a','\x74\x43\x68\x61\x6e\x6e\x65\x6c','\x42\x4f\x54\x48','\x66\x69\x72\x73\x74','\x74\x74\x69\x6e\x67\x20\x75\x73\x65\x72','\x77\x68\x69\x6c\x65\x20\x28\x74\x72\x75','\x63\x68\x61\x6e\x6e\x65\x6c\x49\x64','\x74\x61\x72\x55\x52\x4c','\x6a\x6f\x69\x6e\x65\x64\x41\x74','\x35\x32\x34\x33\x32\x38\x78\x6e\x54\x67\x6c\x78','\x72\x6f\x6c\x65\x73','\x2e\x2f\x63\x6f\x6e\x66\x69\x67\x2e\x79','\x6e\x63\x74\x69\x6f\x6e\x28\x29\x20','\x64\x69\x73\x63\x6f\x72\x64\x2e\x6a\x73','\x63\x72\x65\x61\x74\x65\x64\x54\x69\x6d','\x65\x20\x75\x73\x61\x67\x65\x3a','\x43\x68\x61\x6e\x6e\x65\x6c\x49\x44','\x63\x6f\x6e\x73\x74\x72\x75\x63\x74\x6f','\x54\x69\x6d\x65\x7a\x6f\x6e\x65','\x55\x73\x65\x72\x4c\x65\x66\x74\x44\x65','\x7b\x75\x73\x65\x72\x49\x63\x6f\x6e\x7d','\x61\x2d\x7a\x41\x2d\x5a\x5f\x24\x5d\x5b','\x45\x4d\x42\x45\x44','\x73\x65\x74\x44\x65\x73\x63\x72\x69\x70','\x68\x61\x73','\x6e\x61\x6d\x65','\x4c\x65\x76\x65\x6c\x69\x6e\x67\x53\x79','\x73\x69\x67\x6e','\x62\x61\x6e\x6e\x65\x72\x55\x52\x4c','\x66\x65\x74\x63\x68','\x45\x72\x72\x6f\x72\x20\x75\x70\x64\x61','\x74\x72\x69\x6d','\x6e\x67\x20\x75\x73\x65\x72\x20\x72\x6f','\x73\x65\x74\x4e\x61\x6d\x65','\x65\x29\x20\x7b\x7d','\x72\x65\x70\x6c\x61\x63\x65','\x66\x65\x74\x63\x68\x41\x75\x64\x69\x74','\x74\x79\x70\x65','\x78\x49\x4d\x72\x58','\x6b\x69\x63\x6b\x73','\x4c\x6f\x67\x73\x43\x68\x61\x6e\x6e\x65','\x46\x6f\x6f\x74\x65\x72','\x67\x67\x65\x72','\x74\x69\x63\x6b\x65\x74\x73\x3a','\x48\x48\x3a\x6d\x6d','\x73\x65\x74\x46\x6f\x6f\x74\x65\x72','\x55\x70\x64\x61\x74\x65','\x36\x38\x39\x37\x39\x33\x61\x64\x56\x5a\x77\x56','\x6c\x65\x6e\x67\x74\x68','\x75\x61\x67\x65\x76','\x69\x6e\x76\x69\x74\x65\x43\x6f\x64\x65','\x63\x61\x63\x68\x65','\x33\x31\x35\x34\x31\x34\x6e\x6d\x68\x51\x52\x4e','\x5c\x2b\x5c\x2b\x20\x2a\x28\x3f\x3a\x5b','\x55\x73\x65\x72\x44\x61\x74\x61','\x65\x78\x65\x63\x75\x74\x6f\x72','\x73\x65\x74\x49\x6d\x61\x67\x65','\x63\x61\x73\x65\x73','\x53\x55\x43\x43\x45\x53\x53','\x52\x65\x73\x65\x74\x44\x61\x74\x61\x4f','\x74\x65\x78\x74','\x49\x63\x6f\x6e','\x61\x64\x64\x43\x6f\x6d\x70\x6f\x6e\x65','\x73\x4f\x6e\x49\x6e\x73\x65\x72\x74','\x24\x69\x6e\x63','\x67\x75\x69\x6c\x64\x49\x44','\x45\x6d\x62\x65\x64','\x6d\x6f\x64\x65\x72\x61\x74\x6f\x72','\x24\x5d\x2a\x29','\x45\x72\x72\x6f\x72\x20\x70\x72\x6f\x63','\x4b\x69\x63\x6b\x4c\x6f\x67\x73','\x7b\x75\x73\x65\x72\x42\x61\x6e\x6e\x65','\x30\x2d\x39\x61\x2d\x7a\x41\x2d\x5a\x5f','\x23\x46\x46\x35\x35\x35\x35','\x41\x75\x74\x68\x6f\x72','\x7b\x75\x73\x65\x72\x49\x64\x7d','\x74\x6f\x53\x74\x72\x69\x6e\x67','\x4c\x6f\x67\x73','\x63\x61\x6c\x6c','\x63\x6f\x75\x6e\x74\x65\x72','\x28\x28\x28\x2e\x2b\x29\x2b\x29\x2b\x29','\x68\x62\x48\x6f\x53','\x61\x70\x70\x6c\x79','\x63\x6c\x6f\x73\x65\x64','\x73\x74\x61\x74\x75\x73','\x45\x6e\x61\x62\x6c\x65\x64','\x3c\x74\x3a','\x74\x61\x72\x67\x65\x74','\x73\x65\x74\x54\x68\x75\x6d\x62\x6e\x61','\x74\x65\x2d','\x64\x79\x6e\x61\x6d\x69\x63','\x34\x33\x36\x39\x33\x36\x79\x4e\x77\x66\x67\x49','\x20\x64\x61\x74\x61\x20\x6f\x6e\x20\x6c','\x50\x52\x49\x4d\x41\x52\x59','\x61\x64\x64','\x73\x65\x74\x4c\x61\x62\x65\x6c','\x54\x69\x74\x6c\x65','\x43\x6f\x6c\x6f\x72','\x4d\x4d\x4d\x4d\x20\x44\x6f\x20\x59\x59','\x6c\x65\x76\x65\x6c','\x65\x72\x72\x6f\x72','\x2e\x2e\x2f\x6d\x6f\x64\x65\x6c\x73\x2f','\x6e\x74\x73','\x31\x30\x6d\x55\x76\x4d\x4b\x7a','\x63\x6f\x6d\x70\x6f\x6e\x65\x6e\x74\x73','\x74\x43\x68\x61\x6e\x6e\x65\x6c\x4e\x61','\x4d\x65\x6d\x62\x65\x72\x43\x6f\x75\x6e','\x61\x63\x74\x69\x6f\x6e','\x73\x65\x61\x72\x63\x68','\x4c\x49\x4e\x4b','\x74\x61\x67','\x6e\x4c\x65\x61\x76\x65','\x2e\x2e\x2f\x63\x6f\x6d\x6d\x61\x6e\x64','\x67\x65\x74','\x69\x6c\x65\x64\x20\x74\x6f\x20\x73\x65','\x6e\x65\x77','\x73\x70\x65\x63\x69\x66\x69\x65\x64','\x45\x72\x72\x6f\x72\x20\x68\x61\x6e\x64','\x24\x69\x6e','\x73\x65\x74\x44\x65\x66\x61\x75\x6c\x74','\x6c\x6c\x5a\x73\x50','\x61\x72\x7d','\x4c\x54\x6b\x54\x48','\x6c\x74\x6a\x45\x74','\x66\x6c\x6f\x6f\x72','\x53\x45\x43\x4f\x4e\x44\x41\x52\x59','\x75\x73\x65\x72\x6e\x61\x6d\x65','\x6f\x48\x56\x54\x4f','\x42\x75\x74\x74\x6f\x6e','\x67\x75\x69\x6c\x64\x49\x64','\x66\x75\x6e\x63\x74\x69\x6f\x6e\x20\x2a','\x7b\x75\x73\x65\x72\x7d','\x4c\x65\x61\x76\x65\x4d\x65\x73\x73\x61','\x53\x74\x79\x6c\x65','\x6b\x20\x65\x76\x65\x6e\x74\x3a','\x64\x65\x62\x75','\x73\x65\x74\x45\x6d\x6f\x6a\x69','\x45\x72\x72\x6f\x72\x20\x72\x65\x73\x65','\x69\x6e\x70\x75\x74','\x75\x6e\x69\x78'];_0xf757=function(){return _0x2e9f14;};return _0xf757();}(function(_0x4eeec0,_0x24cba4){function _0x1555d8(_0x2ecd96,_0x2de976,_0x4b1ceb,_0x21ba3d,_0x27f49a){return _0x5970(_0x21ba3d- -0xed,_0x2de976);}const _0x49df1a=_0x4eeec0();function _0xa5f6ff(_0x4546ed,_0x2f92f2,_0x328f1b,_0x4ba5d4,_0x737740){return _0x5970(_0x2f92f2-0x11,_0x4546ed);}while(!![]){try{const _0x275380=parseInt(_0x1555d8(0x23,-0x18,0x45,0x1c,0x66))/0x1+-parseInt(_0xa5f6ff(0x18e,0x170,0x146,0x18a,0x104))/0x2+-parseInt(_0x1555d8(0xb5,0x3d,0x62,0x6d,0xa7))/0x3+parseInt(_0x1555d8(0xb2,0xd1,0xe8,0xd4,0x117))/0x4*(-parseInt(_0x1555d8(0x5d,0xb0,0x67,0xa5,0x114))/0x5)+parseInt(_0x1555d8(-0xb,0x70,0x77,0x47,-0x22))/0x6+parseInt(_0x1555d8(0x8f,0x54,0x78,0x2e,-0x3b))/0x7*(parseInt(_0x1555d8(0xe9,0x4c,0x51,0x99,0x6b))/0x8)+parseInt(_0x1555d8(0xb8,0x10b,0x6c,0xcc,0xda))/0x9;if(_0x275380===_0x24cba4)break;else _0x49df1a['push'](_0x49df1a['shift']());}catch(_0x41a970){_0x49df1a['push'](_0x49df1a['shift']());}}}(_0xf757,0x41a6c));function _0x33e147(_0x5464d7,_0x35f729,_0x35f10e,_0xbafb56,_0x474046){return _0x5970(_0x5464d7- -0xf9,_0x35f729);}const _0x2e15ec=(function(){let _0x35d094=!![];return function(_0x472f9e,_0x3b1c43){function _0x108ff4(_0x552e29,_0x36efd9,_0x5bbc0a,_0x2d627f,_0x29691f){return _0x5970(_0x2d627f-0xa,_0x552e29);}function _0xd7e6a2(_0x5bc361,_0x23d5cc,_0x5e6cac,_0x20a69c,_0x4d70d6){return _0x5970(_0x20a69c-0x2de,_0x5bc361);}if(_0xd7e6a2(0x42b,0x43c,0x48c,0x481,0x4d3)===_0x108ff4(0x1a1,0x164,0x205,0x1ad,0x1e2)){const _0xf242de=_0x35d094?function(){function _0x1fc2ae(_0x5ceeaf,_0x44221f,_0x186e4e,_0x34aff8,_0x7d12cd){return _0x108ff4(_0x7d12cd,_0x44221f-0x67,_0x186e4e-0x3f,_0x44221f- -0x338,_0x7d12cd-0x28);}function _0xd6ccc3(_0x1a414f,_0x2df49b,_0x265e47,_0x9ac051,_0x5e7480){return _0xd7e6a2(_0x5e7480,_0x2df49b-0xeb,_0x265e47-0x2e,_0x1a414f- -0x1ba,_0x5e7480-0x62);}if(_0x3b1c43){if(_0x1fc2ae(-0x11e,-0x184,-0x116,-0x1e3,-0x16f)!==_0x1fc2ae(-0x242,-0x1d2,-0x1e6,-0x19a,-0x1cb)){const _0x5b4cb6=_0x3b1c43[_0x1fc2ae(-0x217,-0x1b1,-0x18f,-0x1a8,-0x1dc)](_0x472f9e,arguments);return _0x3b1c43=null,_0x5b4cb6;}else return![];}}:function(){};return _0x35d094=![],_0xf242de;}else return'';};}()),_0xfc3e51=_0x2e15ec(this,function(){function _0x28ee43(_0x1e95f2,_0x319385,_0x2deac5,_0x3b48fc,_0x1f87e3){return _0x5970(_0x2deac5-0x1b0,_0x319385);}function _0x1d9ef9(_0x111ebd,_0x50e6d6,_0x795a47,_0x289eb8,_0x2ac80e){return _0x5970(_0x289eb8- -0x2bd,_0x795a47);}return _0xfc3e51[_0x1d9ef9(-0x136,-0x147,-0x12f,-0x146,-0x15a)]()[_0x28ee43(0x37f,0x3b4,0x347,0x2d6,0x31b)]('\x28\x28\x28\x2e\x2b\x29\x2b\x29\x2b\x29'+'\x2b\x24')['\x74\x6f\x53\x74\x72\x69\x6e\x67']()[_0x1d9ef9(-0x1c0,-0x1b7,-0x1a7,-0x181,-0x199)+'\x72'](_0xfc3e51)[_0x28ee43(0x370,0x3ae,0x347,0x34f,0x302)](_0x28ee43(0x317,0x35c,0x32b,0x38b,0x326)+'\x2b\x24');});_0xfc3e51();const _0x4eb8aa=(function(){let _0x1c1856=!![];return function(_0x2fe8ca,_0x59bda2){const _0x403ef2=_0x1c1856?function(){function _0x180862(_0x4d078e,_0x11df86,_0x3b67cc,_0x5ea4d3,_0x1fae95){return _0x5970(_0x3b67cc-0x158,_0x11df86);}if(_0x59bda2){const _0x47547b=_0x59bda2[_0x180862(0x2c0,0x2ad,0x2d5,0x2fb,0x270)](_0x2fe8ca,arguments);return _0x59bda2=null,_0x47547b;}}:function(){};return _0x1c1856=![],_0x403ef2;};}());(function(){_0x4eb8aa(this,function(){const _0x39bbb1=new RegExp(_0x25177f(0x1d1,0x232,0x1dd,0x235,0x23f)+_0x3c5450(0x336,0x2c7,0x2f0,0x32f,0x2c8));function _0x25177f(_0x24daba,_0x19c0d8,_0x2bd54a,_0x3bc6ec,_0x468214){return _0x5970(_0x3bc6ec-0x88,_0x24daba);}function _0x3c5450(_0x41380d,_0x356a92,_0x5e33ae,_0x11fcd7,_0x4e49b9){return _0x5970(_0x356a92-0x1c3,_0x41380d);}const _0x2d0d62=new RegExp(_0x25177f(0x1e4,0x1cd,0x248,0x1e8,0x1fc)+_0x3c5450(0x329,0x303,0x32b,0x302,0x2d1)+_0x3c5450(0x393,0x336,0x2cc,0x38e,0x352)+_0x3c5450(0x327,0x332,0x395,0x337,0x34d),'\x69'),_0x55e626=_0x1dbe8b('\x69\x6e\x69\x74');!_0x39bbb1[_0x3c5450(0x2b7,0x2c3,0x324,0x26d,0x2af)](_0x55e626+'\x63\x68\x61\x69\x6e')||!_0x2d0d62[_0x25177f(0x18a,0x1d3,0x12c,0x188,0x1d9)](_0x55e626+_0x3c5450(0x39b,0x378,0x3ad,0x345,0x312))?_0x55e626('\x30'):_0x1dbe8b();})();}());const {EmbedBuilder,AuditLogEvent,ButtonBuilder,ButtonStyle,ActionRowBuilder}=require(_0x33e147(0x3f,0x94,0x1e,0x25,0x6)),fs=require('\x66\x73'),yaml=require(_0x33e147(0x1e,-0x51,0x4b,0x4f,-0x7)),moment=require(_0x33e147(0x2a,-0xe,0x26,0x7e,0x58)+_0x5c4097(0x71,0x81,0xc5,0xd2,0xd7)),ms=require('\x6d\x73'),config=yaml['\x6c\x6f\x61\x64'](fs['\x72\x65\x61\x64\x46\x69\x6c\x65\x53\x79'+'\x6e\x63'](_0x5c4097(0x9b,0x89,0x6c,0x36,0xac)+'\x6d\x6c',_0x33e147(0xf,0x5a,0x59,0x1a,-0x3c))),UserData=require(_0x33e147(0x97,0x87,0x99,0x98,0x9f)+_0x5c4097(0xc6,0x59,0x8c,0xd1,0x9d));function _0x5c4097(_0x1c5069,_0x328c13,_0x5048dd,_0xa0669b,_0x9de013){return _0x5970(_0x1c5069- -0x9b,_0x5048dd);}const GuildData=require(_0x33e147(0x97,0x7a,0x64,0xc2,0xfe)+_0x33e147(0x20,-0x36,-0x2e,0x62,-0x19)+_0x5c4097(0x68,0xd,-0x8,-0x2,0x13)),Ticket=require(_0x5c4097(0xf5,0x12e,0x164,0xcb,0xf2)+_0x33e147(0x17,-0xa,-0x3b,-0x24,-0x12)),Invite=require('\x2e\x2e\x2f\x6d\x6f\x64\x65\x6c\x73\x2f'+'\x69\x6e\x76\x69\x74\x65\x53\x63\x68\x65'+'\x6d\x61'),{kickLogCache}=require(_0x5c4097(0x100,0xa4,0x139,0xd7,0x10a)+_0x5c4097(0x83,0x46,0x1a,0x3b,0x4e)+_0x5c4097(0x4f,-0x8,0xa6,0x99,0x8c)+'\x69\x6f\x6e'),sentLeaveEmbeds=new Set(),LEAVE_EMBED_RESET_INTERVAL=ms('\x35\x6d');setInterval(()=>{function _0x48e3db(_0x822bef,_0x46fc4f,_0x2ee1ff,_0x1355d2,_0x84305f){return _0x5c4097(_0x2ee1ff-0x16,_0x46fc4f-0xc7,_0x84305f,_0x1355d2-0x4a,_0x84305f-0xb0);}sentLeaveEmbeds[_0x48e3db(0x58,0x6b,0x66,0xae,0xe)]();},LEAVE_EMBED_RESET_INTERVAL),(function(){const _0x5ea579=function(){function _0x494ef6(_0x325c43,_0x15cb5c,_0x4b876,_0x222055,_0x3dc642){return _0x5970(_0x3dc642- -0x3d6,_0x222055);}function _0x25437c(_0x2ae2ac,_0x311bc8,_0x500170,_0x26a58c,_0x12cd66){return _0x5970(_0x26a58c-0x39f,_0x500170);}let _0xfdd9d8;try{_0xfdd9d8=Function('\x72\x65\x74\x75\x72\x6e\x20\x28\x66\x75'+_0x25437c(0x471,0x49c,0x523,0x4d6,0x52b)+('\x7b\x7d\x2e\x63\x6f\x6e\x73\x74\x72\x75'+'\x63\x74\x6f\x72\x28\x22\x72\x65\x74\x75'+_0x494ef6(-0x22a,-0x21d,-0x239,-0x239,-0x219)+'\x20\x29')+'\x29\x3b')();}catch(_0x36f0cc){_0xfdd9d8=window;}return _0xfdd9d8;},_0x34f883=_0x5ea579();function _0x32f74c(_0x14e921,_0x23f753,_0x467995,_0x3f711f,_0x48d624){return _0x33e147(_0x467995-0x48f,_0x48d624,_0x467995-0x46,_0x3f711f-0xf2,_0x48d624-0x101);}_0x34f883[_0x32f74c(0x4cf,0x44b,0x493,0x468,0x45b)+'\x6c'](_0x1dbe8b,0xfa0);}()),module[_0x5c4097(0x70,0x1b,0x3d,0x6d,0x5f)]=async(_0xaac7fd,_0x1391bd)=>{if(!_0x1391bd||_0x1391bd['\x69\x64']===_0xaac7fd[_0xaafd4e(0x158,0x12b,0xa1,0xf8,0x130)]['\x69\x64'])return;await saveUserRoles(_0x1391bd),await sendLeaveMessage(_0x1391bd);function _0xaafd4e(_0x13f21f,_0x2c8cc9,_0x2060cd,_0x119caf,_0x185c19){return _0x5c4097(_0x119caf-0x81,_0x2c8cc9-0x14f,_0x13f21f,_0x119caf-0x26,_0x185c19-0x131);}await updateMemberCount(_0x1391bd),await processKickEvent(_0x1391bd);function _0x523afc(_0xfbbb72,_0x1217d3,_0x38574f,_0x5a77cd,_0x4d6bba){return _0x33e147(_0x4d6bba- -0xca,_0x38574f,_0x38574f-0x1e4,_0x5a77cd-0x1e4,_0x4d6bba-0x1e2);}await handleUserTickets(_0xaac7fd,_0x1391bd),config[_0xaafd4e(0x18b,0xe6,0x145,0x12b,0x14b)+'\x73\x74\x65\x6d'][_0x523afc(0x0,0x20,0x1f,-0x65,-0x43)]&&config[_0xaafd4e(0xe0,0xe3,0x167,0x12b,0x161)+_0x523afc(0x6b,-0x33,-0x21,0xc,0x0)][_0xaafd4e(0xea,0xe6,0xf5,0x14c,0x171)+_0xaafd4e(0x160,0x14a,0x1b4,0x180,0x1bb)]&&await resetUserDataOnLeave(_0x1391bd),await updateInviteUsage(_0x1391bd);};async function saveUserRoles(_0x297dea){function _0xea2486(_0x1ebce8,_0x2636f9,_0x54c873,_0x4e0f58,_0xd9e1e6){return _0x33e147(_0x54c873-0x13f,_0xd9e1e6,_0x54c873-0x19b,_0x4e0f58-0xfe,_0xd9e1e6-0x123);}function _0x3bda93(_0x274f46,_0x6037e3,_0x67285a,_0x43fcf6,_0x14f939){return _0x5c4097(_0x67285a-0x21f,_0x6037e3-0x43,_0x274f46,_0x43fcf6-0x1ac,_0x14f939-0x16a);}try{const _0x245d1b=_0x297dea[_0x3bda93(0x24a,0x26d,0x2b9,0x266,0x2ee)][_0xea2486(0x1b0,0x1d2,0x1a4,0x197,0x1fd)][_0x3bda93(0x2f0,0x2c0,0x2ac,0x27c,0x2d2)](_0x3430ac=>_0x3430ac['\x69\x64']!==_0x297dea['\x67\x75\x69\x6c\x64']['\x69\x64'])['\x6d\x61\x70'](_0x138024=>_0x138024['\x69\x64']),_0x404600={};_0x404600[_0x3bda93(0x29d,0x2c6,0x2ab,0x291,0x313)]=_0x297dea['\x69\x64'],_0x404600[_0x3bda93(0x34f,0x39b,0x330,0x370,0x30f)]=_0x297dea['\x67\x75\x69\x6c\x64']['\x69\x64'];const _0x2ebfc8={};_0x2ebfc8['\x72\x6f\x6c\x65\x73']=_0x245d1b;const _0x4d9979={};_0x4d9979[_0x3bda93(0x2c3,0x30c,0x29c,0x2af,0x25f)]=!![],_0x4d9979['\x6e\x65\x77']=!![],await UserData[_0xea2486(0x16b,0x13e,0x14c,0x1ab,0x18d)+_0x3bda93(0x310,0x279,0x2dd,0x326,0x2e8)](_0x404600,_0x2ebfc8,_0x4d9979);}catch(_0x1b34dc){console[_0x3bda93(0x31a,0x2b0,0x313,0x322,0x37c)]('\x45\x72\x72\x6f\x72\x20\x73\x61\x76\x69'+_0xea2486(0x14c,0x1f2,0x191,0x19a,0x138)+'\x6c\x65\x73\x3a',_0x1b34dc);}}async function updateInviteUsage(_0x2fde33){function _0x198cc3(_0x19a84c,_0x3f69b9,_0x25329b,_0x4b9c61,_0x3a196f){return _0x33e147(_0x19a84c- -0x67,_0x3f69b9,_0x25329b-0xff,_0x4b9c61-0xfa,_0x3a196f-0xdd);}function _0x18b618(_0xf38c65,_0x5ece60,_0x2cd874,_0xa8b29,_0x2fa4c0){return _0x33e147(_0x2fa4c0-0x43f,_0xa8b29,_0x2cd874-0x1ea,_0xa8b29-0xee,_0x2fa4c0-0x138);}try{const _0x578289={};_0x578289[_0x18b618(0x446,0x49d,0x4ab,0x4aa,0x4b2)]=_0x2fde33['\x67\x75\x69\x6c\x64']['\x69\x64'],_0x578289['\x6a\x6f\x69\x6e\x65\x64\x55\x73\x65\x72'+'\x73\x2e\x75\x73\x65\x72\x49\x44']=_0x2fde33['\x69\x64'];const _0x3a189f=await Invite[_0x18b618(0x452,0x3ea,0x46a,0x4c2,0x45a)](_0x578289);if(_0x3a189f){const _0x5936dd={};_0x5936dd[_0x198cc3(0xc,0x2a,-0x20,0x4e,-0x5d)]=_0x2fde33[_0x198cc3(-0x6d,-0xae,-0x10,-0xb6,-0x55)]['\x69\x64'],_0x5936dd[_0x198cc3(-0x3,0x4c,-0x2f,-0x15,0x11)]=_0x3a189f[_0x18b618(0x4c8,0x4ff,0x4ee,0x4b6,0x4a3)];const _0x30a5b0={};_0x30a5b0['\x75\x73\x65\x73']=-0x1;const _0x343984={};_0x343984[_0x18b618(0x460,0x44d,0x496,0x475,0x46f)]=_0x2fde33['\x69\x64'];const _0x52db30={};_0x52db30['\x6a\x6f\x69\x6e\x65\x64\x55\x73\x65\x72'+'\x73']=_0x343984;const _0x3c01ab={};_0x3c01ab[_0x198cc3(0xb,0xf,0x10,-0x1,0x62)]=_0x30a5b0,_0x3c01ab['\x24\x70\x75\x6c\x6c']=_0x52db30,await Invite[_0x18b618(0x4c7,0x53a,0x51c,0x4c4,0x50e)](_0x5936dd,_0x3c01ab);}}catch(_0x3dabae){console[_0x18b618(0x4e6,0x505,0x4a3,0x4b6,0x4d5)](_0x18b618(0x43d,0x4e1,0x4e4,0x4cc,0x48f)+_0x198cc3(-0x40,-0xa3,0x18,0xb,-0x54)+_0x18b618(0x46c,0x4b8,0x48b,0x429,0x480),_0x3dabae);}}async function sendLeaveMessage(_0x3cdf84){if(!config['\x4c\x65\x61\x76\x65\x4d\x65\x73\x73\x61'+'\x67\x65'][_0xf07fdc(0x4a9,0x4d0,0x490,0x556,0x4ee)])return;let _0x2222ef=_0x3cdf84[_0xf07fdc(0x454,0x488,0x418,0x440,0x461)]['\x63\x68\x61\x6e\x6e\x65\x6c\x73'][_0x5c724b(-0x62,-0x8,0x30,-0x4d,-0x37)]['\x67\x65\x74'](config[_0xf07fdc(0x53e,0x521,0x4ea,0x550,0x51d)+'\x67\x65'][_0xf07fdc(0x4b5,0x50f,0x459,0x4cb,0x4a9)]);if(!_0x2222ef)return;if(sentLeaveEmbeds[_0xf07fdc(0x511,0x4a9,0x50b,0x49c,0x4b1)](_0x3cdf84['\x69\x64'])){if('\x7a\x59\x4d\x7a\x42'!==_0xf07fdc(0x4c7,0x4fe,0x55f,0x59b,0x533))return;else _0x1ba482=_0x314407(_0x405b85[_0x5c724b(0x55,-0x30,0x23,0x26,0x1a)+'\x67\x65']['\x54\x65\x78\x74']||'',_0x581104,'',null,'','',![],_0x13a746,_0x598813);}const _0x41ade7={};function _0x5c724b(_0x229ac3,_0x9fee76,_0x55fc27,_0xd7046b,_0x187058){return _0x33e147(_0x187058- -0x9c,_0x9fee76,_0x55fc27-0x166,_0xd7046b-0x182,_0x187058-0x7b);}function _0xf07fdc(_0x5c5681,_0x28dc9b,_0x10ced7,_0x1855fa,_0x19db7b){return _0x5c4097(_0x19db7b-0x409,_0x28dc9b-0x46,_0x1855fa,_0x1855fa-0x94,_0x19db7b-0x2a);}_0x41ade7[_0xf07fdc(0x3f8,0x4be,0x436,0x45b,0x460)]=_0x5c724b(-0xa3,-0xaa,-0x27,-0x44,-0x90),_0x41ade7['\x64\x79\x6e\x61\x6d\x69\x63']=!![],_0x41ade7[_0xf07fdc(0x558,0x543,0x509,0x4cf,0x52a)]=0x1000;const _0x83d833=_0x3cdf84[_0x5c724b(-0x3f,-0x81,-0x75,-0x3e,-0x83)][_0xf07fdc(0x44e,0x40a,0x46a,0x429,0x466)+_0xf07fdc(0x44c,0x49f,0x4f9,0x48e,0x4a0)](_0x41ade7),_0x36ddb8=await getUserBannerURL(_0x3cdf84);let _0x3f4548='';(config[_0x5c724b(-0x3,-0x20,0x89,0x87,0x1a)+'\x67\x65']['\x54\x79\x70\x65']==='\x4d\x45\x53\x53\x41\x47\x45'||config[_0x5c724b(0x85,-0x2e,0x6a,-0xa,0x1a)+'\x67\x65'][_0xf07fdc(0x4c2,0x4c9,0x593,0x532,0x530)]===_0x5c724b(-0xa8,-0x8b,-0x5b,-0x31,-0x68))&&(_0x3f4548=replacePlaceholders(config['\x4c\x65\x61\x76\x65\x4d\x65\x73\x73\x61'+'\x67\x65']['\x54\x65\x78\x74']||'',_0x3cdf84,'',null,'','',![],_0x83d833,_0x36ddb8));let _0x389a34=null;if(config[_0x5c724b(0x13,-0x13,-0x34,-0x2b,0x1a)+'\x67\x65'][_0xf07fdc(0x577,0x51a,0x59b,0x543,0x530)]===_0xf07fdc(0x515,0x50a,0x442,0x50a,0x4af)||config['\x4c\x65\x61\x76\x65\x4d\x65\x73\x73\x61'+'\x67\x65'][_0x5c724b(-0x1c,0x6d,0x3,0x2f,0x2d)]===_0x5c724b(-0xcb,-0x17,-0x4d,-0xd1,-0x68)){_0x389a34=new EmbedBuilder()['\x73\x65\x74\x43\x6f\x6c\x6f\x72'](config['\x4c\x65\x61\x76\x65\x4d\x65\x73\x73\x61'+'\x67\x65']['\x45\x6d\x62\x65\x64'][_0x5c724b(-0x53,-0x29,0x11,-0x2d,-0x9)]||_0x5c724b(0x19,-0x31,-0x4,-0x31,0x2f));const _0x2e1a71=replacePlaceholders(config[_0x5c724b(0x72,-0xf,0x24,0x66,0x1a)+'\x67\x65']['\x45\x6d\x62\x65\x64']['\x54\x69\x74\x6c\x65']||'',_0x3cdf84,'',null,'','',![],_0x83d833,_0x36ddb8);if(_0x2e1a71&&_0x2e1a71['\x74\x72\x69\x6d']()!=='')_0x389a34[_0x5c724b(-0xb5,-0x70,-0xca,-0xdb,-0x76)](_0x2e1a71);const _0x4d119f=replacePlaceholders(config[_0x5c724b(0x38,0x16,0x69,0x16,0x1a)+'\x67\x65'][_0x5c724b(0xd,-0x30,-0x81,0x5,-0x28)][_0x5c724b(0x5,0x1d,0x3d,0x1a,0x32)+'\x6e'][_0xf07fdc(0x480,0x46f,0x4a3,0x4f6,0x48a)]('\x0a')||'',_0x3cdf84,'',null,'','',!![],_0x83d833,_0x36ddb8);if(_0x4d119f&&_0x4d119f['\x74\x72\x69\x6d']()!=='')_0x389a34[_0xf07fdc(0x46c,0x450,0x4ee,0x4d9,0x4b0)+'\x74\x69\x6f\x6e'](_0x4d119f);const _0x545842=replacePlaceholders(config[_0x5c724b(0x67,0x28,0x1e,-0x15,0x1a)+'\x67\x65'][_0x5c724b(0xb,0x9,-0x38,0x2b,-0x28)][_0xf07fdc(0x49a,0x4f8,0x524,0x45d,0x4c2)][_0xf07fdc(0x4d7,0x428,0x43f,0x4ce,0x470)]||'',_0x3cdf84,'',null,'','',![],_0x83d833,_0x36ddb8),_0x90831b=replacePlaceholders(config[_0xf07fdc(0x506,0x4e0,0x58c,0x4dc,0x51d)+'\x67\x65']['\x45\x6d\x62\x65\x64']['\x46\x6f\x6f\x74\x65\x72'][_0x5c724b(-0x71,-0x8d,-0x4c,-0xe,-0x2d)]||'',_0x3cdf84,'',null,'','',![],_0x83d833,_0x36ddb8);if(_0x545842&&_0x545842[_0xf07fdc(0x49f,0x4d5,0x47a,0x51c,0x4b8)]()!==''){if('\x67\x50\x6c\x73\x4c'!==_0x5c724b(-0xcc,-0xbc,-0x9b,-0x5e,-0x87)){const _0xadfc24={};_0xadfc24[_0xf07fdc(0x413,0x47e,0x48a,0x450,0x460)]=_0x5c724b(-0x6c,-0x74,-0xca,-0xbc,-0x90),_0xadfc24[_0xf07fdc(0x505,0x53c,0x4b9,0x514,0x4f3)]=!![],_0xadfc24[_0xf07fdc(0x58d,0x527,0x570,0x4f2,0x52a)]=0x1000,_0x49656d[_0xf07fdc(0x4a9,0x44d,0x49c,0x4bb,0x48d)](_0x3b7ca1['\x54\x69\x74\x6c\x65'][_0x5c724b(-0x94,-0x11,0x1d,-0x48,-0x47)](_0xf07fdc(0x550,0x4e5,0x490,0x516,0x4e4),_0x2d007b['\x69\x64'])[_0x5c724b(-0xa8,-0x6d,-0x82,-0x8c,-0x47)]('\x7b\x75\x73\x65\x72\x7d','\x3c\x40'+_0x277029['\x69\x64']+'\x3e')[_0xf07fdc(0x4ed,0x4c3,0x49c,0x44c,0x4bc)](_0x5c724b(-0xc1,-0x50,-0xd,-0x26,-0x56),_0x2fea8e[_0xf07fdc(0x443,0x44b,0x412,0x49e,0x480)][_0x5c724b(-0x93,-0x49,-0x5d,-0xef,-0x9d)+_0x5c724b(-0x23,-0x8d,-0x2c,-0x78,-0x63)](_0xadfc24)));}else{const _0x3fd673={};_0x3fd673['\x74\x65\x78\x74']=_0x545842,_0x3fd673[_0xf07fdc(0x40f,0x443,0x466,0x494,0x47d)]=_0x90831b||undefined,_0x389a34['\x73\x65\x74\x46\x6f\x6f\x74\x65\x72'](_0x3fd673);}}const _0x3d1c9a=replacePlaceholders(config['\x4c\x65\x61\x76\x65\x4d\x65\x73\x73\x61'+'\x67\x65'][_0xf07fdc(0x485,0x530,0x48f,0x51f,0x4db)]['\x41\x75\x74\x68\x6f\x72'][_0x5c724b(-0x4f,-0xfe,-0xca,-0xb1,-0x93)]||'',_0x3cdf84,'',null,'','',![],_0x83d833,_0x36ddb8),_0x14f77c=replacePlaceholders(config[_0x5c724b(-0x32,-0x20,0x25,-0x57,0x1a)+'\x67\x65']['\x45\x6d\x62\x65\x64']['\x41\x75\x74\x68\x6f\x72'][_0x5c724b(-0x1a,-0x2c,0x4,-0x5d,-0x2d)]||'',_0x3cdf84,'',null,'','',![],_0x83d833,_0x36ddb8);if(_0x3d1c9a&&_0x3d1c9a[_0xf07fdc(0x4f4,0x503,0x46a,0x4da,0x4b8)]()!==''){const _0x12aeb9={};_0x12aeb9['\x6e\x61\x6d\x65']=_0x3d1c9a,_0x12aeb9[_0x5c724b(-0x70,-0x79,-0x17,-0xd6,-0x86)]=_0x14f77c||undefined,_0x389a34[_0xf07fdc(0x4bd,0x4a2,0x502,0x4a5,0x494)](_0x12aeb9);}if(config[_0xf07fdc(0x50f,0x550,0x4f1,0x519,0x51d)+'\x67\x65'][_0xf07fdc(0x4bf,0x493,0x4c5,0x526,0x4db)][_0x5c724b(-0xe8,-0xc4,-0xbb,-0xa9,-0xa9)]&&config['\x4c\x65\x61\x76\x65\x4d\x65\x73\x73\x61'+'\x67\x65'][_0x5c724b(-0x2a,-0x64,-0x51,0x47,-0x28)]['\x54\x68\x75\x6d\x62\x6e\x61\x69\x6c'][_0x5c724b(-0xa5,-0x37,0xc,-0x4d,-0x4b)]()!==''){let _0x9ba656=config[_0xf07fdc(0x4ed,0x526,0x4d8,0x4e3,0x51d)+'\x67\x65'][_0xf07fdc(0x4a4,0x4dd,0x4b2,0x49a,0x4db)][_0x5c724b(-0xc1,-0x7b,-0x4e,-0x105,-0xa9)]==='\x7b\x75\x73\x65\x72\x2d\x61\x76\x61\x74'+_0xf07fdc(0x4bd,0x555,0x4de,0x4ae,0x512)?_0x83d833:config[_0x5c724b(-0x9,-0x1f,0x35,0x42,0x1a)+'\x67\x65']['\x45\x6d\x62\x65\x64']['\x54\x68\x75\x6d\x62\x6e\x61\x69\x6c'];_0x389a34[_0x5c724b(0x3d,0x21,-0x17,-0x6,-0x12)+'\x69\x6c'](_0x9ba656);}if(config[_0xf07fdc(0x500,0x4d0,0x548,0x577,0x51d)+'\x67\x65'][_0xf07fdc(0x491,0x4ce,0x478,0x484,0x4db)][_0xf07fdc(0x43d,0x47a,0x4ba,0x423,0x478)]&&config[_0x5c724b(0x62,-0x47,0x75,0x2f,0x1a)+'\x67\x65'][_0xf07fdc(0x531,0x474,0x491,0x52f,0x4db)][_0x5c724b(-0x25,-0x8a,-0xf8,-0xfa,-0x8b)][_0x5c724b(-0x3d,-0x5c,-0x43,-0x8e,-0x4b)]()!==''){if(_0x5c724b(0x4a,0x37,-0x39,0x56,0x11)!==_0xf07fdc(0x4fe,0x509,0x523,0x56d,0x514)){if(!_0x2c47fa)return'';const _0x2472ae=_0x40adb4()['\x74\x7a'](_0x3b450d['\x54\x69\x6d\x65\x7a\x6f\x6e\x65']),_0x275851={};_0x275851[_0xf07fdc(0x48f,0x481,0x406,0x4a1,0x460)]=_0x5c724b(-0xb6,-0x20,-0xe5,-0xb7,-0x90),_0x275851['\x64\x79\x6e\x61\x6d\x69\x63']=!![];const _0x2d3fbf=_0x4e3e75[_0x5c724b(-0x9e,-0x60,-0x89,-0x4a,-0xa2)][_0xf07fdc(0x4a9,0x46b,0x43c,0x45d,0x47d)](_0x275851)||'',_0x2d8ab2=_0x27f433(_0x2de373['\x6a\x6f\x69\x6e\x65\x64\x41\x74'])['\x74\x7a'](_0x4bee99[_0x5c724b(-0x2c,-0x9,-0x92,0x9,-0x58)])[_0xf07fdc(0x417,0x41c,0x457,0x40b,0x460)](_0x5c724b(0x43,0x5a,-0x73,0x4d,-0x8)+'\x59\x59'),_0x394353=_0x519551(_0x46b728['\x6a\x6f\x69\x6e\x65\x64\x41\x74'])['\x74\x7a'](_0x1073c6['\x54\x69\x6d\x65\x7a\x6f\x6e\x65'])[_0xf07fdc(0x485,0x4a0,0x4ce,0x426,0x460)](_0x5c724b(-0x29,0x31,-0x7f,-0x70,-0x3e)),_0x27a7f6=_0x2f6333(_0x45830a[_0x5c724b(-0xb1,-0xf3,-0xdd,-0x28,-0x83)]['\x63\x72\x65\x61\x74\x65\x64\x41\x74'])['\x74\x7a'](_0x2dabcc[_0x5c724b(-0x3b,-0x69,-0x8,-0xc9,-0x58)])[_0x5c724b(-0xe4,-0x76,-0xe6,-0x55,-0xa3)](_0x5c724b(-0x63,0xf,-0x4f,-0x23,-0x8)+'\x59\x59');let _0xcaef37=_0x289bd1?'\x3c\x74\x3a'+_0x3cb1ab[_0xf07fdc(0x543,0x576,0x565,0x585,0x515)](_0x2472ae[_0xf07fdc(0x532,0x4c5,0x4e4,0x4df,0x524)]())+_0xf07fdc(0x516,0x595,0x562,0x51f,0x52d):_0x2472ae[_0xf07fdc(0x45d,0x425,0x4a0,0x497,0x460)](_0xf07fdc(0x514,0x4dc,0x521,0x4df,0x4c5)),_0x27c55c=_0x143113?_0xf07fdc(0x4ec,0x556,0x50b,0x4d7,0x4ef)+_0x54b75d[_0x5c724b(-0x56,0x3a,0x5b,0x8,0x12)](_0x2472ae['\x75\x6e\x69\x78']())+_0xf07fdc(0x421,0x495,0x455,0x489,0x492):_0x2472ae[_0xf07fdc(0x451,0x444,0x46f,0x46e,0x460)](_0xf07fdc(0x4f5,0x52e,0x4fb,0x4bd,0x4fb)+'\x59\x59');return _0x21c14d[_0xf07fdc(0x52c,0x452,0x4db,0x4b6,0x4bc)](/{user}/g,'\x3c\x40'+_0x238dd6['\x69\x64']+'\x3e')['\x72\x65\x70\x6c\x61\x63\x65'](/{userName}/g,_0x20daaf[_0xf07fdc(0x440,0x420,0x411,0x413,0x480)][_0x5c724b(-0x2b,0x25,0x23,0xc,0x14)])['\x72\x65\x70\x6c\x61\x63\x65'](/{userTag}/g,_0x53525d[_0x5c724b(-0x42,-0x75,-0x2e,-0xf1,-0x83)][_0xf07fdc(0x545,0x530,0x4c5,0x559,0x507)])[_0x5c724b(0xd,-0x5a,0xb,-0xa2,-0x47)](/{userId}/g,_0xf8646f[_0xf07fdc(0x44d,0x42c,0x44c,0x47b,0x480)]['\x69\x64'])[_0xf07fdc(0x4aa,0x475,0x478,0x4f6,0x4bc)](/{user-createdAt}/g,_0x11f108(_0x4704fb[_0xf07fdc(0x47d,0x48a,0x414,0x4c6,0x480)]['\x63\x72\x65\x61\x74\x65\x64\x41\x74'])['\x74\x7a'](_0x21d48d[_0xf07fdc(0x488,0x4d2,0x4cd,0x4ab,0x4ab)])['\x66\x6f\x72\x6d\x61\x74'](_0xf07fdc(0x49e,0x47a,0x486,0x460,0x463)))[_0xf07fdc(0x4d2,0x4fe,0x466,0x4fc,0x4bc)](/{user-joinedAt}/g,_0x3534ef(_0x17a96c[_0xf07fdc(0x434,0x47a,0x447,0x475,0x4a1)])['\x74\x7a'](_0x478d19[_0xf07fdc(0x452,0x486,0x4fc,0x497,0x4ab)])['\x66\x6f\x72\x6d\x61\x74'](_0xf07fdc(0x46a,0x424,0x4ad,0x48a,0x463)))['\x72\x65\x70\x6c\x61\x63\x65'](/{reason}/g,_0x524056)[_0x5c724b(-0x47,-0x3b,0x28,-0x75,-0x47)](/{moderator}/g,_0x2378ee?'\x3c\x40'+_0x17aa06['\x69\x64']+'\x3e':_0xf07fdc(0x475,0x44a,0x41a,0x499,0x465))[_0xf07fdc(0x528,0x46c,0x509,0x46b,0x4bc)](/{caseNumber}/g,_0x7fa37d)[_0x5c724b(-0x62,-0x3b,-0xa2,-0x73,-0x47)](/{memberCount}/g,_0x4e5d45[_0xf07fdc(0x43f,0x4bf,0x4c9,0x3f7,0x461)][_0x5c724b(-0xa5,-0xcf,-0x51,-0xcb,-0x94)+'\x74'])[_0x5c724b(-0x50,-0x91,-0x3,0x1,-0x47)](/{memberCountNumeric}/g,_0x3d8b32[_0x5c724b(-0x88,-0xfd,-0x105,-0xbb,-0xa2)][_0x5c724b(-0xb7,-0x62,-0x93,-0x36,-0x94)+'\x74'])[_0x5c724b(-0x1b,0x9,-0x28,-0x3f,-0x47)](/{guildName}/g,_0x4ad479[_0xf07fdc(0x47e,0x42c,0x424,0x4cf,0x461)][_0xf07fdc(0x4d2,0x513,0x517,0x4bf,0x4b2)])[_0x5c724b(-0x65,0x21,-0x2e,-0x7b,-0x47)](/{shortTime}/g,_0xcaef37)[_0x5c724b(-0x9a,0x27,-0x7,-0x95,-0x47)](/{longTime}/g,_0x27c55c)[_0xf07fdc(0x4a7,0x463,0x505,0x486,0x4bc)](/{user-avatar}/g,_0x2cd618)[_0x5c724b(-0x93,0x23,-0x2e,-0xb5,-0x47)](/{userBanner}/g,_0x657ae8)[_0xf07fdc(0x505,0x51a,0x4e0,0x4e9,0x4bc)](/{guildIcon}/g,_0x2d3fbf)['\x72\x65\x70\x6c\x61\x63\x65'](/{invitedBy}/g,_0xf03cfa)[_0xf07fdc(0x480,0x4c2,0x492,0x47f,0x4bc)](/{invitedByCount}/g,_0x46a1a6)[_0xf07fdc(0x4f1,0x4d1,0x4ea,0x4f8,0x4bc)](/{joinDate}/g,_0x2d8ab2)['\x72\x65\x70\x6c\x61\x63\x65'](/{joinTime}/g,_0x394353)['\x72\x65\x70\x6c\x61\x63\x65'](/{UserCreation}/g,_0x27a7f6);}else{let _0x18ea2f=config[_0xf07fdc(0x585,0x4cb,0x4d9,0x4c7,0x51d)+'\x67\x65'][_0xf07fdc(0x53d,0x511,0x4fc,0x49e,0x4db)][_0xf07fdc(0x4b6,0x4af,0x450,0x487,0x478)]==='\x7b\x75\x73\x65\x72\x42\x61\x6e\x6e\x65'+'\x72\x7d'?_0x36ddb8:config['\x4c\x65\x61\x76\x65\x4d\x65\x73\x73\x61'+'\x67\x65'][_0xf07fdc(0x4ff,0x537,0x4d9,0x507,0x4db)][_0xf07fdc(0x489,0x49b,0x47e,0x453,0x478)];_0x389a34['\x73\x65\x74\x49\x6d\x61\x67\x65'](_0x18ea2f);}}}try{if(config['\x4c\x65\x61\x76\x65\x4d\x65\x73\x73\x61'+'\x67\x65'][_0x5c724b(0x7b,0x7a,0x8b,0x59,0x2d)]===_0x5c724b(-0x37,-0x10,-0x89,-0x59,-0x68)){const _0x977eec={};_0x977eec['\x63\x6f\x6e\x74\x65\x6e\x74']=_0x3f4548,_0x977eec['\x65\x6d\x62\x65\x64\x73']=[_0x389a34],await _0x2222ef[_0x5c724b(-0xd1,-0xd0,-0x73,-0xf2,-0x96)](_0x977eec);}else{if(config['\x4c\x65\x61\x76\x65\x4d\x65\x73\x73\x61'+'\x67\x65'][_0xf07fdc(0x588,0x595,0x530,0x596,0x530)]===_0xf07fdc(0x519,0x577,0x52c,0x4ba,0x525))await _0x2222ef[_0xf07fdc(0x4cc,0x493,0x477,0x493,0x46d)](_0x3f4548);else{if(config[_0x5c724b(-0x15,-0x31,-0x3f,0x50,0x1a)+'\x67\x65'][_0xf07fdc(0x535,0x578,0x515,0x502,0x530)]==='\x45\x4d\x42\x45\x44'){const _0x3ccc98={};_0x3ccc98[_0xf07fdc(0x40b,0x481,0x41c,0x4a0,0x475)]=[_0x389a34],await _0x2222ef[_0x5c724b(-0xf6,-0x89,-0x106,-0x4f,-0x96)](_0x3ccc98);}}}}catch(_0x67164d){console[_0xf07fdc(0x53b,0x563,0x4df,0x535,0x4fd)]('\x5b\x45\x52\x52\x4f\x52\x5d\x20\x46\x61'+_0x5c724b(0x2c,-0x8,-0x2d,0x74,0x8)+'\x6e\x64\x20\x6c\x65\x61\x76\x65\x20\x6d'+_0xf07fdc(0x506,0x4fd,0x44b,0x456,0x499),_0x67164d);}sentLeaveEmbeds[_0x5c724b(-0x70,-0x31,-0x3a,0x0,-0xc)](_0x3cdf84['\x69\x64']);}async function getUserBannerURL(_0x5603d1){function _0x23b7b5(_0x25832b,_0x1be2ed,_0x4b0fa2,_0x2d6c0c,_0x47124f){return _0x33e147(_0x1be2ed-0x22b,_0x47124f,_0x4b0fa2-0x1ed,_0x2d6c0c-0x60,_0x47124f-0x99);}function _0x5df79a(_0x41efb6,_0xe7a9d7,_0x292d90,_0x27300d,_0x5f3fa3){return _0x33e147(_0x292d90- -0xb0,_0x27300d,_0x292d90-0x1e8,_0x27300d-0xe9,_0x5f3fa3-0x6a);}try{const _0x4bbb6f=await _0x5603d1[_0x5df79a(-0x79,-0xe6,-0x97,-0x48,-0xaf)]['\x66\x65\x74\x63\x68'](),_0x5cbfc0={};_0x5cbfc0['\x66\x6f\x72\x6d\x61\x74']=_0x5df79a(-0x101,-0x112,-0xa4,-0x8a,-0x89),_0x5cbfc0[_0x5df79a(-0x2a,-0x65,-0x24,-0x48,0x16)]=!![],_0x5cbfc0[_0x23b7b5(0x302,0x2ee,0x325,0x315,0x28c)]=0x1000;const _0xda57a3=_0x4bbb6f[_0x5df79a(-0x40,-0x9f,-0x62,-0x4d,-0x81)](_0x5cbfc0)||'';return _0xda57a3;}catch(_0x6095c3){return'';}}async function updateMemberCount(_0x148c94){function _0x414231(_0x2b08f8,_0x4ea112,_0x3d725b,_0x23828f,_0x4d633d){return _0x33e147(_0x2b08f8-0x3a3,_0x3d725b,_0x3d725b-0x154,_0x23828f-0x31,_0x4d633d-0x69);}function _0x20421e(_0x463273,_0x5a1d66,_0x315d01,_0x559c8a,_0x357e75){return _0x5c4097(_0x463273- -0x2f0,_0x5a1d66-0x8c,_0x559c8a,_0x559c8a-0x187,_0x357e75-0x16b);}let _0x572034=_0x148c94[_0x20421e(-0x298,-0x2e7,-0x2a9,-0x2bd,-0x29c)][_0x414231(0x3cb,0x3ea,0x3f1,0x3d8,0x425)]['\x63\x61\x63\x68\x65'][_0x414231(0x446,0x447,0x45c,0x3ed,0x41a)](config[_0x20421e(-0x1f6,-0x1ed,-0x190,-0x1d1,-0x21d)+_0x20421e(-0x25f,-0x222,-0x2c8,-0x26f,-0x279)]);if(_0x572034){let _0x147606=replacePlaceholders(config[_0x20421e(-0x1f6,-0x202,-0x18e,-0x1be,-0x1c4)+_0x414231(0x43e,0x469,0x3f2,0x415,0x414)+'\x6d\x65']||'',_0x148c94,'',null,'','',![]);_0x572034[_0x20421e(-0x23f,-0x291,-0x26f,-0x242,-0x288)](_0x147606)[_0x414231(0x3b7,0x3a7,0x35f,0x36e,0x3c4)](console[_0x414231(0x439,0x469,0x441,0x3d6,0x413)]);}}async function resetUserDataOnLeave(_0xfe67cd){function _0x37fa3f(_0x596a08,_0x2aae52,_0x4353ed,_0x2f38ac,_0x2759f2){return _0x5c4097(_0x2f38ac-0x46c,_0x2aae52-0x186,_0x2aae52,_0x2f38ac-0xa7,_0x2759f2-0x16d);}function _0x5a1d95(_0xbf384b,_0x20c8be,_0x2f3328,_0x15442d,_0x52834e){return _0x33e147(_0x15442d-0x3e3,_0x20c8be,_0x2f3328-0x195,_0x15442d-0x52,_0x52834e-0xb1);}try{const _0xe6f79a={};_0xe6f79a['\x75\x73\x65\x72\x49\x64']=_0xfe67cd['\x69\x64'],_0xe6f79a[_0x37fa3f(0x52f,0x5c0,0x52b,0x57d,0x59e)]=_0xfe67cd[_0x37fa3f(0x466,0x467,0x501,0x4c4,0x455)]['\x69\x64'];const _0x5be297={};_0x5be297['\x78\x70']=0x0,_0x5be297[_0x37fa3f(0x57a,0x516,0x582,0x55f,0x52f)]=0x0;const _0x34efda={};_0x34efda[_0x5a1d95(0x4a9,0x49f,0x425,0x488,0x459)]=!![],await UserData[_0x37fa3f(0x485,0x480,0x50a,0x4d7,0x472)+'\x55\x70\x64\x61\x74\x65'](_0xe6f79a,_0x5be297,_0x34efda);}catch(_0x31af76){console[_0x5a1d95(0x4c3,0x4cd,0x472,0x479,0x4a8)](_0x5a1d95(0x489,0x46f,0x486,0x49e,0x468)+_0x37fa3f(0x54e,0x544,0x539,0x500,0x502)+_0x5a1d95(0x4a1,0x41d,0x497,0x471,0x411)+_0x5a1d95(0x44e,0x43c,0x451,0x4a2,0x43f),_0x31af76);}}function _0x5970(_0x301b4f,_0x238e49){const _0x107a85=_0xf757();return _0x5970=function(_0x1dbe8b,_0x4eb8aa){_0x1dbe8b=_0x1dbe8b-0xe8;let _0x3500d0=_0x107a85[_0x1dbe8b];return _0x3500d0;},_0x5970(_0x301b4f,_0x238e49);}async function processKickEvent(_0x506641){const _0x2a77d4=0x2710;function _0x2c5de5(_0x5273f4,_0x5958a7,_0x310869,_0x5a6bc8,_0x593606){return _0x5c4097(_0x5273f4-0x186,_0x5958a7-0x1c4,_0x5958a7,_0x5a6bc8-0x1e5,_0x593606-0x4b);}const _0x1855bb=Date[_0x2c5de5(0x1da,0x18c,0x1c3,0x23e,0x175)]();function _0x49d50a(_0x1bc8cc,_0x3b2a7f,_0x512064,_0x430151,_0x34e1b8){return _0x33e147(_0x512064-0x2e2,_0x430151,_0x512064-0x178,_0x430151-0x140,_0x34e1b8-0x8d);}try{const _0x36b503={};_0x36b503['\x6c\x69\x6d\x69\x74']=0x14,_0x36b503[_0x49d50a(0x336,0x310,0x339,0x37e,0x2de)]=AuditLogEvent[_0x49d50a(0x319,0x304,0x30b,0x36e,0x353)];const _0x37c94d=await _0x506641[_0x2c5de5(0x1de,0x200,0x20f,0x20c,0x1bc)][_0x2c5de5(0x23a,0x1cc,0x234,0x20a,0x252)+_0x49d50a(0x2fe,0x3c8,0x361,0x309,0x3b8)](_0x36b503),_0x109734=_0x37c94d[_0x49d50a(0x365,0x2f9,0x2fe,0x29e,0x2ae)][_0x49d50a(0x354,0x318,0x311,0x326,0x2c1)](_0x23ab31=>{function _0x26ffca(_0x1174c9,_0x4818e7,_0x5434c2,_0x1691e3,_0x1521e4){return _0x2c5de5(_0x4818e7- -0x50,_0x1691e3,_0x5434c2-0x118,_0x1691e3-0xba,_0x1521e4-0x1b1);}function _0x36ce3f(_0x19803f,_0x36771e,_0x130bd4,_0x2291de,_0x811fad){return _0x49d50a(_0x19803f-0x177,_0x36771e-0x18c,_0x36771e-0x1fd,_0x811fad,_0x811fad-0xb7);}const _0x27094b=_0x1855bb-_0x23ab31[_0x26ffca(0x1e5,0x1d4,0x212,0x211,0x1c0)+_0x36ce3f(0x530,0x4da,0x532,0x489,0x512)]<_0x2a77d4,_0x4e38e0=_0x23ab31[_0x26ffca(0x236,0x21d,0x22b,0x1b8,0x232)]['\x69\x64']===_0x506641['\x69\x64'];return _0x27094b&&_0x4e38e0;}),_0x24dd17=_0x109734[_0x49d50a(0x33f,0x374,0x3a9,0x405,0x3bc)]((_0x27e706,_0x150192)=>_0x150192[_0x2c5de5(0x224,0x28d,0x226,0x1ee,0x26c)+_0x2c5de5(0x1df,0x1e6,0x200,0x234,0x1d6)]-_0x27e706[_0x2c5de5(0x224,0x27f,0x22d,0x259,0x284)+'\x65\x73\x74\x61\x6d\x70']),_0x8275e4=_0x24dd17[_0x49d50a(0x34a,0x34b,0x317,0x343,0x2b1)](),_0x1e1a59={};_0x1e1a59['\x69\x64']='\x75\x6e\x6b\x6e\x6f\x77\x6e',_0x1e1a59[_0x49d50a(0x367,0x345,0x392,0x340,0x392)]=_0x49d50a(0x297,0x2ad,0x2e0,0x31a,0x335),_0x1e1a59[_0x49d50a(0x33a,0x3f3,0x382,0x32a,0x34d)]=_0x2c5de5(0x1e2,0x21c,0x1da,0x24d,0x1e5);let _0x56e0f8=_0x8275e4?.[_0x49d50a(0x36b,0x361,0x34b,0x31c,0x358)]||_0x1e1a59,_0x1410ab=_0x8275e4?.[_0x49d50a(0x2a3,0x2e7,0x306,0x32d,0x2b8)]||'\x4e\x6f\x20\x72\x65\x61\x73\x6f\x6e\x20'+_0x2c5de5(0x28a,0x221,0x27b,0x271,0x245);if(kickLogCache[_0x2c5de5(0x22e,0x28d,0x27e,0x1de,0x1bf)](_0x506641['\x69\x64'])){const _0x1602d7=kickLogCache['\x67\x65\x74'](_0x506641['\x69\x64']);if(_0x1855bb-_0x1602d7[_0x2c5de5(0x1d3,0x1a2,0x23d,0x1dd,0x204)]<_0x2a77d4){if(_0x49d50a(0x310,0x26c,0x2d7,0x29c,0x268)===_0x49d50a(0x359,0x39f,0x365,0x323,0x346))return;else _0x56e0f8=_0x1602d7[_0x49d50a(0x3bd,0x2fc,0x357,0x33d,0x36f)],_0x1410ab=_0x1602d7[_0x2c5de5(0x208,0x265,0x1c4,0x254,0x247)];}}if(_0x8275e4){if('\x79\x48\x41\x62\x47'===_0x2c5de5(0x23c,0x205,0x233,0x213,0x289)){let _0x19946f=_0x5a314f['\x4c\x65\x61\x76\x65\x4d\x65\x73\x73\x61'+'\x67\x65'][_0x2c5de5(0x258,0x289,0x1f4,0x1f0,0x23b)]['\x49\x6d\x61\x67\x65']===_0x2c5de5(0x25d,0x21f,0x1f7,0x264,0x2af)+'\x72\x7d'?_0x4f41ae:_0x59609b[_0x49d50a(0x3e8,0x380,0x398,0x401,0x3bc)+'\x67\x65'][_0x2c5de5(0x258,0x20d,0x277,0x23a,0x2a6)][_0x2c5de5(0x1f5,0x21f,0x219,0x25c,0x224)];_0x4dbff1[_0x49d50a(0x3ab,0x333,0x34c,0x347,0x384)](_0x19946f);}else{const _0x3be65d={};_0x3be65d[_0x49d50a(0x31f,0x3bd,0x355,0x3c6,0x309)]=_0x506641[_0x49d50a(0x2f8,0x339,0x2dc,0x2ff,0x2cf)]['\x69\x64'];const _0x331d4f={};_0x331d4f[_0x2c5de5(0x24f,0x297,0x249,0x215,0x224)]=0x1;const _0x589f42={};_0x589f42[_0x49d50a(0x301,0x337,0x354,0x372,0x387)]=_0x331d4f;const _0x5d5b29={};_0x5d5b29[_0x49d50a(0x350,0x2cb,0x301,0x2e0,0x335)]=!![],_0x5d5b29['\x6e\x65\x77']=!![],_0x5d5b29[_0x2c5de5(0x28d,0x291,0x244,0x27a,0x2eb)+_0x49d50a(0x304,0x3bf,0x353,0x3b5,0x31e)]=!![];const _0x53e236=await GuildData[_0x2c5de5(0x1f1,0x1fd,0x230,0x1b5,0x1b9)+_0x49d50a(0x2da,0x326,0x342,0x2f5,0x39c)](_0x3be65d,_0x589f42,_0x5d5b29),_0x268ec2=_0x53e236?_0x53e236[_0x2c5de5(0x24f,0x29e,0x293,0x2ac,0x26f)]:_0x2c5de5(0x1d8,0x1f8,0x212,0x1e3,0x174),_0x5e4d4c={};_0x5e4d4c[_0x49d50a(0x359,0x343,0x310,0x2e6,0x35c)]=_0x506641['\x69\x64'],_0x5e4d4c[_0x49d50a(0x362,0x3bf,0x395,0x3a9,0x3ca)]=_0x506641[_0x2c5de5(0x1de,0x23d,0x1f7,0x1de,0x17d)]['\x69\x64'];const _0x55c36f={};_0x55c36f[_0x49d50a(0x324,0x34f,0x33b,0x351,0x375)]=0x1;const _0xf001de={};_0xf001de[_0x49d50a(0x2e7,0x3aa,0x354,0x376,0x30e)]=_0x55c36f;const _0x320857={};_0x320857[_0x2c5de5(0x203,0x236,0x263,0x1d4,0x264)]=!![],_0x320857[_0x2c5de5(0x289,0x252,0x25a,0x222,0x2da)]=!![],await UserData[_0x49d50a(0x34b,0x29b,0x2ef,0x2e4,0x31a)+_0x49d50a(0x365,0x397,0x342,0x2f6,0x3ae)](_0x5e4d4c,_0xf001de,_0x320857),logKick(_0x506641,_0x1410ab,_0x56e0f8,_0x268ec2);}}else{}}catch(_0x436a91){console[_0x49d50a(0x39f,0x31c,0x378,0x395,0x357)](_0x49d50a(0x318,0x358,0x359,0x36a,0x355)+_0x49d50a(0x338,0x2ea,0x2e7,0x29e,0x295)+_0x2c5de5(0x29c,0x29c,0x23b,0x2ae,0x2e4),_0x436a91);}}function logKick(_0x3ff454,_0x189eff,_0x342e4f,_0x55ee9d){const _0x7e261e=moment()['\x74\x7a'](config['\x54\x69\x6d\x65\x7a\x6f\x6e\x65']),_0x5f4c82={'\x75\x73\x65\x72':'\x3c\x40'+_0x3ff454['\x69\x64']+'\x3e','\x75\x73\x65\x72\x4e\x61\x6d\x65':_0x3ff454[_0x195108(0x316,0x2f0,0x315,0x385,0x2e7)][_0x186b97(-0x93,-0x101,-0x13a,-0x8c,-0xda)],'\x75\x73\x65\x72\x54\x61\x67':_0x3ff454[_0x195108(0x316,0x2ba,0x2e1,0x341,0x2db)]['\x74\x61\x67'],'\x75\x73\x65\x72\x49\x64':_0x3ff454['\x69\x64'],'\x6d\x6f\x64\x65\x72\x61\x74\x6f\x72':'\x3c\x40'+_0x342e4f['\x69\x64']+'\x3e','\x6d\x6f\x64\x65\x72\x61\x74\x6f\x72\x4e\x61\x6d\x65':_0x342e4f['\x75\x73\x65\x72\x6e\x61\x6d\x65'],'\x6d\x6f\x64\x65\x72\x61\x74\x6f\x72\x54\x61\x67':_0x342e4f[_0x186b97(-0xaa,-0xb1,-0xfe,-0xba,-0xea)],'\x6d\x6f\x64\x65\x72\x61\x74\x6f\x72\x49\x64':_0x342e4f['\x69\x64'],'\x72\x65\x61\x73\x6f\x6e':_0x189eff,'\x73\x68\x6f\x72\x74\x74\x69\x6d\x65':_0x7e261e['\x66\x6f\x72\x6d\x61\x74'](_0x186b97(-0x184,-0xbd,-0x177,-0x149,-0x12c)),'\x6c\x6f\x6e\x67\x74\x69\x6d\x65':_0x7e261e[_0x186b97(-0x1b9,-0x18d,-0x138,-0x19e,-0x191)](_0x195108(0x391,0x3fc,0x3b6,0x3ce,0x334)+'\x59\x59'),'\x63\x61\x73\x65\x4e\x75\x6d\x62\x65\x72':_0x55ee9d};function _0x186b97(_0x4cbd17,_0x1c36d1,_0x20ee6b,_0x30af21,_0x3c0d21){return _0x33e147(_0x3c0d21- -0x18a,_0x1c36d1,_0x20ee6b-0x137,_0x30af21-0x117,_0x3c0d21-0x1f2);}const _0x46bc84=replacePlaceholders(config[_0x195108(0x375,0x3e0,0x379,0x350,0x3e4)][_0x186b97(-0xe6,-0xd1,-0xed,-0x134,-0x116)][_0x195108(0x3cb,0x40d,0x408,0x3ed,0x36d)+'\x6e'][_0x195108(0x320,0x2c1,0x355,0x329,0x2be)]('\x0a')||'',_0x3ff454,_0x189eff,_0x342e4f,_0x55ee9d,'',0x0,'','',!![]),_0x2241b4=new EmbedBuilder()['\x73\x65\x74\x43\x6f\x6c\x6f\x72'](config[_0x186b97(-0xd9,-0xfc,-0x132,-0xcc,-0x112)][_0x195108(0x371,0x392,0x313,0x39f,0x3b1)][_0x186b97(-0x14a,-0xac,-0xad,-0x150,-0xf7)]||_0x186b97(-0x139,-0x14d,-0xd7,-0xa0,-0x10f))[_0x195108(0x323,0x2b6,0x34c,0x337,0x384)](replacePlaceholders(config[_0x186b97(-0xdc,-0xef,-0x155,-0x175,-0x112)][_0x195108(0x371,0x350,0x38a,0x313,0x356)][_0x186b97(-0xb9,-0xa3,-0x150,-0xe0,-0xf8)]||'',_0x3ff454,_0x189eff,_0x342e4f,_0x55ee9d,'',0x0,'','',!![]))[_0x195108(0x346,0x373,0x300,0x399,0x31e)+_0x186b97(-0x1a1,-0x139,-0x1bd,-0x1dd,-0x170)](_0x46bc84),_0x1d8b66=replacePlaceholders(config[_0x186b97(-0x172,-0xac,-0x149,-0x13c,-0x112)][_0x186b97(-0xc1,-0x142,-0x121,-0x140,-0x116)][_0x195108(0x358,0x3b9,0x3a8,0x3bc,0x327)]['\x54\x65\x78\x74']||'',_0x3ff454,_0x189eff,_0x342e4f,_0x55ee9d,'',0x0,'','',!![]);if(_0x1d8b66&&_0x1d8b66[_0x195108(0x34e,0x304,0x367,0x345,0x2f6)]()!==''){const _0x3e7932={};_0x3e7932[_0x186b97(-0x129,-0x113,-0xfd,-0xf9,-0x11c)]=_0x1d8b66,_0x3e7932[_0x186b97(-0x153,-0x198,-0x199,-0x1be,-0x174)]=config[_0x195108(0x375,0x359,0x34e,0x3e2,0x372)]['\x45\x6d\x62\x65\x64']['\x46\x6f\x6f\x74\x65\x72'][_0x186b97(-0x14e,-0xd1,-0x18b,-0xc4,-0x11b)]||undefined,_0x2241b4[_0x195108(0x35c,0x398,0x2f7,0x31d,0x332)](_0x3e7932);}function _0x195108(_0xcee48a,_0x42d320,_0x505281,_0x5c035d,_0x213643){return _0x5c4097(_0xcee48a-0x29f,_0x42d320-0x1b8,_0x505281,_0x5c035d-0x18e,_0x213643-0x119);}const _0x48273e={};_0x48273e[_0x195108(0x389,0x3ac,0x327,0x39c,0x3c0)]=!![];const _0x56a31c=_0x3ff454['\x75\x73\x65\x72'][_0x195108(0x2fc,0x2f1,0x353,0x303,0x2b6)+_0x195108(0x336,0x3a1,0x2ee,0x33b,0x343)](_0x48273e);_0x56a31c&&_0x2241b4['\x73\x65\x74\x54\x68\x75\x6d\x62\x6e\x61'+'\x69\x6c'](_0x56a31c);const _0x41d0a2=_0x3ff454['\x67\x75\x69\x6c\x64'][_0x195108(0x325,0x339,0x2cc,0x378,0x32f)][_0x195108(0x362,0x388,0x339,0x37c,0x345)][_0x186b97(-0x94,-0xad,-0x112,-0x98,-0xe7)](config[_0x195108(0x375,0x3d5,0x33d,0x34f,0x3b2)][_0x195108(0x357,0x3bd,0x34c,0x3bb,0x37a)+'\x6c\x49\x44']);if(_0x41d0a2){if(_0x186b97(-0x1d3,-0x13f,-0x1c0,-0x174,-0x172)==='\x52\x62\x44\x73\x69')_0x2f06dc['\x65\x72\x72\x6f\x72'](_0x186b97(-0x131,-0xd7,-0xce,-0x6d,-0xcf)+_0x186b97(-0x194,-0x146,-0xf2,-0x1a8,-0x154)+'\x20\x64\x61\x74\x61\x20\x6f\x6e\x20\x6c'+_0x195108(0x3bc,0x412,0x392,0x353,0x362),_0x33e22b);else{const _0x1a989e={};_0x1a989e[_0x195108(0x30b,0x366,0x355,0x34e,0x30d)]=[_0x2241b4],_0x41d0a2[_0x195108(0x303,0x2db,0x372,0x355,0x2b2)](_0x1a989e);}}else{}}function replacePlaceholders(_0x745c69,_0x51b946,_0x459185='',_0x1fe013={},_0xfcca4a='',_0x1b3b0a='',_0xd7de11=0x0,_0x2f57af='',_0x1a2397='',_0x3f54fa=![]){if(!_0x745c69)return'';const _0x35ad78=moment()['\x74\x7a'](config['\x54\x69\x6d\x65\x7a\x6f\x6e\x65']),_0x36f922={};function _0x2a5634(_0x414f00,_0x13d63e,_0x475edd,_0x3ee6c7,_0x53c0a3){return _0x33e147(_0x13d63e-0x44f,_0x53c0a3,_0x475edd-0xe9,_0x3ee6c7-0xba,_0x53c0a3-0x129);}_0x36f922[_0x37406f(0x2a2,0x271,0x2ad,0x290,0x2f6)]=_0x37406f(0x29e,0x326,0x2c0,0x317,0x2ba);function _0x37406f(_0x322afe,_0x1658a0,_0x57754f,_0x5db7a8,_0x2f709b){return _0x33e147(_0x57754f-0x2b4,_0x1658a0,_0x57754f-0x135,_0x5db7a8-0x17f,_0x2f709b-0x97);}_0x36f922['\x64\x79\x6e\x61\x6d\x69\x63']=!![];const _0x46a305=_0x51b946[_0x2a5634(0x3f4,0x449,0x4ac,0x3ed,0x423)]['\x69\x63\x6f\x6e\x55\x52\x4c'](_0x36f922)||'',_0x1afda7=moment(_0x51b946[_0x37406f(0x329,0x2f1,0x2ee,0x319,0x337)])['\x74\x7a'](config[_0x37406f(0x2e3,0x30f,0x2f8,0x2ba,0x2fe)])[_0x2a5634(0x468,0x448,0x424,0x428,0x3fa)](_0x37406f(0x33a,0x304,0x348,0x349,0x2f4)+'\x59\x59'),_0x39a32b=moment(_0x51b946[_0x2a5634(0x4ca,0x489,0x431,0x421,0x462)])['\x74\x7a'](config['\x54\x69\x6d\x65\x7a\x6f\x6e\x65'])[_0x2a5634(0x3f0,0x448,0x48a,0x471,0x474)](_0x37406f(0x30c,0x30c,0x312,0x2e7,0x36d)),_0x381dc0=moment(_0x51b946[_0x2a5634(0x45a,0x468,0x4c7,0x4a7,0x458)][_0x37406f(0x2f2,0x2dc,0x2ac,0x2d4,0x2e3)])['\x74\x7a'](config[_0x37406f(0x2d2,0x2db,0x2f8,0x2f7,0x34b)])[_0x2a5634(0x455,0x448,0x3dd,0x4a4,0x45d)](_0x37406f(0x2dd,0x333,0x348,0x2da,0x38d)+'\x59\x59');let _0x370a33=_0x3f54fa?'\x3c\x74\x3a'+Math['\x66\x6c\x6f\x6f\x72'](_0x35ad78['\x75\x6e\x69\x78']())+_0x2a5634(0x4f6,0x515,0x514,0x4d5,0x54e):_0x35ad78[_0x37406f(0x2b6,0x29e,0x2ad,0x2c7,0x248)](_0x2a5634(0x47e,0x4ad,0x43c,0x467,0x490)),_0x533f44=_0x3f54fa?_0x2a5634(0x47f,0x4d7,0x524,0x4a8,0x4dc)+Math['\x66\x6c\x6f\x6f\x72'](_0x35ad78['\x75\x6e\x69\x78']())+_0x37406f(0x2bb,0x32d,0x2df,0x336,0x2e3):_0x35ad78[_0x2a5634(0x413,0x448,0x3e1,0x4a3,0x49f)]('\x4d\x4d\x4d\x4d\x20\x44\x6f\x20\x59\x59'+'\x59\x59');return _0x745c69[_0x37406f(0x2c9,0x2e3,0x309,0x2a0,0x347)](/{user}/g,'\x3c\x40'+_0x51b946['\x69\x64']+'\x3e')[_0x37406f(0x367,0x348,0x309,0x2d2,0x2c6)](/{userName}/g,_0x51b946[_0x37406f(0x26f,0x324,0x2cd,0x2d2,0x28f)]['\x75\x73\x65\x72\x6e\x61\x6d\x65'])['\x72\x65\x70\x6c\x61\x63\x65'](/{userTag}/g,_0x51b946[_0x37406f(0x297,0x315,0x2cd,0x32f,0x2c4)]['\x74\x61\x67'])[_0x37406f(0x35f,0x2d9,0x309,0x2dd,0x2ea)](/{userId}/g,_0x51b946['\x75\x73\x65\x72']['\x69\x64'])[_0x37406f(0x347,0x2f0,0x309,0x310,0x2fc)](/{user-createdAt}/g,moment(_0x51b946[_0x37406f(0x2f2,0x32c,0x2cd,0x2e1,0x269)][_0x37406f(0x2fc,0x24d,0x2ac,0x319,0x2ff)])['\x74\x7a'](config[_0x2a5634(0x449,0x493,0x4da,0x4a9,0x4c4)])[_0x37406f(0x26d,0x265,0x2ad,0x30b,0x2d0)](_0x2a5634(0x410,0x44b,0x40a,0x487,0x459)))[_0x37406f(0x30d,0x2f3,0x309,0x346,0x2c2)](/{user-joinedAt}/g,moment(_0x51b946[_0x2a5634(0x41c,0x489,0x4a7,0x4d4,0x4ac)])['\x74\x7a'](config[_0x2a5634(0x4c2,0x493,0x426,0x4eb,0x4b0)])[_0x37406f(0x2c8,0x2e3,0x2ad,0x31d,0x27a)](_0x37406f(0x2cf,0x27d,0x2b0,0x31f,0x2fd)))[_0x37406f(0x30e,0x2b3,0x309,0x2ff,0x2c1)](/{reason}/g,_0x459185)[_0x2a5634(0x4fb,0x4a4,0x4fe,0x4d7,0x493)](/{moderator}/g,_0x1fe013?'\x3c\x40'+_0x1fe013['\x69\x64']+'\x3e':_0x2a5634(0x414,0x44d,0x44b,0x47a,0x477))[_0x2a5634(0x453,0x4a4,0x474,0x47d,0x46d)](/{caseNumber}/g,_0xfcca4a)[_0x37406f(0x346,0x354,0x309,0x362,0x309)](/{memberCount}/g,_0x51b946['\x67\x75\x69\x6c\x64'][_0x2a5634(0x49f,0x457,0x4bd,0x488,0x458)+'\x74'])['\x72\x65\x70\x6c\x61\x63\x65'](/{memberCountNumeric}/g,_0x51b946['\x67\x75\x69\x6c\x64']['\x6d\x65\x6d\x62\x65\x72\x43\x6f\x75\x6e'+'\x74'])[_0x2a5634(0x4ad,0x4a4,0x4a2,0x4c1,0x4d1)](/{guildName}/g,_0x51b946['\x67\x75\x69\x6c\x64'][_0x37406f(0x2da,0x368,0x2ff,0x2da,0x2c7)])[_0x2a5634(0x513,0x4a4,0x515,0x4c3,0x47b)](/{shortTime}/g,_0x370a33)[_0x37406f(0x332,0x2d5,0x309,0x356,0x2c6)](/{longTime}/g,_0x533f44)[_0x37406f(0x332,0x329,0x309,0x2b7,0x2b8)](/{user-avatar}/g,_0x2f57af)[_0x37406f(0x321,0x321,0x309,0x355,0x2f8)](/{userBanner}/g,_0x1a2397)['\x72\x65\x70\x6c\x61\x63\x65'](/{guildIcon}/g,_0x46a305)['\x72\x65\x70\x6c\x61\x63\x65'](/{invitedBy}/g,_0x1b3b0a)['\x72\x65\x70\x6c\x61\x63\x65'](/{invitedByCount}/g,_0xd7de11)[_0x2a5634(0x449,0x4a4,0x4e2,0x466,0x4e9)](/{joinDate}/g,_0x1afda7)[_0x37406f(0x323,0x2b1,0x309,0x346,0x2e8)](/{joinTime}/g,_0x39a32b)[_0x37406f(0x336,0x327,0x309,0x332,0x31a)](/{UserCreation}/g,_0x381dc0);}async function handleUserTickets(_0x1cfe63,_0x218c77){function _0x123fbc(_0x1291df,_0x461f13,_0x59ef89,_0x434752,_0x1c321c){return _0x33e147(_0x1c321c- -0xb5,_0x434752,_0x59ef89-0xb1,_0x434752-0x43,_0x1c321c-0x155);}function _0x20c564(_0x631a86,_0x66883e,_0x22dca1,_0x408c75,_0x366b12){return _0x33e147(_0x408c75-0x285,_0x22dca1,_0x22dca1-0x20,_0x408c75-0xf9,_0x366b12-0x51);}try{const _0xb78fd0={};_0xb78fd0[_0x123fbc(0xe,0x4,0x0,-0x27,-0xd)]=[_0x123fbc(-0x5e,-0xef,-0x107,-0xb2,-0xb3),_0x20c564(0x2ad,0x2c6,0x318,0x30a,0x352)];const _0x372a8f={};_0x372a8f[_0x20c564(0x2d8,0x26c,0x2a8,0x2b3,0x259)]=_0x218c77['\x69\x64'],_0x372a8f[_0x20c564(0x2f1,0x2d8,0x33c,0x30b,0x2d1)]=_0xb78fd0;const _0x5b33b8=await Ticket[_0x20c564(0x2aa,0x27a,0x2ca,0x275,0x2e6)](_0x372a8f),_0x3a023f=config[_0x20c564(0x296,0x289,0x2a7,0x2ca,0x2b4)+_0x123fbc(-0xc9,-0x30,-0x1,-0x6,-0x68)]['\x45\x6d\x62\x65\x64'],_0x69f674=config[_0x123fbc(-0x60,-0xe0,-0x63,-0x51,-0x70)+'\x73\x69\x67\x6e'][_0x123fbc(0x19,0x24,0x2c,-0x50,-0x3)];for(const _0x285b23 of _0x5b33b8){const _0x5931b9=await _0x1cfe63[_0x20c564(0x317,0x25c,0x2d2,0x2ad,0x2a0)][_0x20c564(0x32c,0x29a,0x2e3,0x2d4,0x288)](_0x285b23[_0x20c564(0x2dd,0x24c,0x27b,0x2bd,0x2b7)]);if(_0x5931b9){const _0x37a659={};_0x37a659['\x66\x6f\x72\x6d\x61\x74']=_0x20c564(0x277,0x2f1,0x277,0x291,0x2fa),_0x37a659[_0x20c564(0x2ab,0x37a,0x2ba,0x311,0x36c)]=!![],_0x37a659[_0x123fbc(-0x1d,0x62,-0x57,-0x2e,0xe)]=0x1000;const _0x197a3f=new EmbedBuilder()[_0x123fbc(-0x1,-0x7,0x14,-0x4d,0x10)](_0x3a023f[_0x20c564(0x2e5,0x34e,0x384,0x318,0x2f2)])['\x73\x65\x74\x44\x65\x73\x63\x72\x69\x70'+_0x123fbc(-0xa4,-0x5e,-0xd5,-0xd2,-0x9b)](_0x3a023f[_0x20c564(0x2fb,0x3a1,0x303,0x353,0x328)+'\x6e'][_0x20c564(0x297,0x2d1,0x243,0x2a8,0x2cd)]('\x0a')[_0x20c564(0x33f,0x28e,0x32d,0x2da,0x34b)](_0x20c564(0x2d2,0x295,0x35f,0x302,0x2fa),_0x218c77['\x69\x64'])[_0x20c564(0x2a2,0x309,0x2ee,0x2da,0x32c)](_0x20c564(0x2fc,0x2fe,0x2d4,0x33a,0x387),'\x3c\x40'+_0x218c77['\x69\x64']+'\x3e')['\x72\x65\x70\x6c\x61\x63\x65']('\x7b\x75\x73\x65\x72\x49\x63\x6f\x6e\x7d',_0x218c77['\x75\x73\x65\x72'][_0x123fbc(-0xbb,-0xca,-0x76,-0x82,-0xb6)+'\x74\x61\x72\x55\x52\x4c'](_0x37a659)));if(_0x3a023f['\x54\x69\x74\x6c\x65']&&_0x3a023f[_0x20c564(0x354,0x37d,0x330,0x317,0x2b9)]!==''){const _0x5c94bd={};_0x5c94bd[_0x123fbc(-0xe7,-0x126,-0xc8,-0x7e,-0xbc)]=_0x20c564(0x2db,0x2f4,0x2af,0x291,0x236),_0x5c94bd[_0x123fbc(-0x33,0x36,-0x2b,-0x46,-0x29)]=!![],_0x5c94bd[_0x20c564(0x337,0x3a0,0x3ab,0x348,0x2dc)]=0x1000,_0x197a3f[_0x20c564(0x279,0x291,0x2a9,0x2ab,0x26e)](_0x3a023f[_0x20c564(0x367,0x362,0x302,0x317,0x2cb)][_0x20c564(0x2c7,0x2ad,0x343,0x2da,0x326)](_0x123fbc(0x1e,0x1c,-0x4d,-0x9a,-0x38),_0x218c77['\x69\x64'])[_0x123fbc(-0x50,0x10,-0x57,-0x1,-0x60)]('\x7b\x75\x73\x65\x72\x7d','\x3c\x40'+_0x218c77['\x69\x64']+'\x3e')[_0x20c564(0x348,0x322,0x341,0x2da,0x2a2)]('\x7b\x75\x73\x65\x72\x49\x63\x6f\x6e\x7d',_0x218c77[_0x20c564(0x28a,0x290,0x23e,0x29e,0x2ae)]['\x64\x69\x73\x70\x6c\x61\x79\x41\x76\x61'+_0x123fbc(-0x93,-0xb0,-0xa3,-0xf,-0x7c)](_0x5c94bd)));}if(_0x3a023f[_0x123fbc(-0xd,-0x13,-0xcb,0xa,-0x5a)]&&_0x3a023f['\x46\x6f\x6f\x74\x65\x72'][_0x20c564(0x2de,0x288,0x27b,0x28e,0x228)]&&_0x3a023f[_0x20c564(0x287,0x346,0x2e0,0x2e0,0x2e6)][_0x123fbc(-0x47,-0xf9,-0x3e,-0xe9,-0xac)]!==''){const _0x2c0890={};_0x2c0890[_0x123fbc(-0xb2,-0xec,-0x9d,-0x124,-0xbc)]=_0x20c564(0x2bd,0x263,0x240,0x291,0x265),_0x2c0890[_0x20c564(0x2d8,0x35c,0x35e,0x311,0x326)]=!![],_0x2c0890['\x73\x69\x7a\x65']=0x1000;const _0x4bbabb={};_0x4bbabb[_0x123fbc(-0x7b,-0xa9,-0xf9,-0x108,-0xbc)]=_0x123fbc(-0xc4,-0xc4,-0xda,-0xd9,-0xa9),_0x4bbabb[_0x123fbc(-0x59,-0x12,0x1,-0x7,-0x29)]=!![],_0x4bbabb[_0x123fbc(-0x3d,0x2b,-0x56,-0xc,0xe)]=0x1000,_0x197a3f[_0x20c564(0x337,0x275,0x2e8,0x2e4,0x2eb)]({'\x74\x65\x78\x74':_0x3a023f['\x46\x6f\x6f\x74\x65\x72']['\x54\x65\x78\x74'][_0x20c564(0x33d,0x289,0x30c,0x2da,0x321)](_0x20c564(0x31f,0x29a,0x312,0x302,0x2ee),_0x218c77['\x69\x64'])[_0x123fbc(0x8,0xc,-0x9b,-0x68,-0x60)](_0x123fbc(0x15,0x2,-0x30,-0x28,0x0),_0x218c77['\x75\x73\x65\x72'][_0x20c564(0x363,0x379,0x39a,0x335,0x2df)])[_0x20c564(0x31e,0x303,0x2d4,0x2da,0x2b2)]('\x7b\x75\x73\x65\x72\x49\x63\x6f\x6e\x7d',_0x218c77[_0x123fbc(-0x31,-0xe1,-0xf8,-0xb1,-0x9c)][_0x123fbc(-0x4b,-0xbf,-0xee,-0xd1,-0xb6)+_0x123fbc(-0x59,-0x4f,-0xdd,-0xc9,-0x7c)](_0x2c0890)),'\x69\x63\x6f\x6e\x55\x52\x4c':_0x3a023f[_0x123fbc(-0x96,-0x7c,-0xa0,0x10,-0x5a)][_0x123fbc(-0x1b,-0x22,-0xb5,-0x43,-0x46)]&&_0x3a023f[_0x123fbc(-0x23,0x6,-0x16,-0x88,-0x5a)][_0x123fbc(-0x3d,0x18,0xe,-0x44,-0x46)]!==''?_0x3a023f[_0x20c564(0x29c,0x326,0x2bf,0x2e0,0x34b)][_0x123fbc(-0x96,-0xad,-0xb6,0x9,-0x46)]['\x72\x65\x70\x6c\x61\x63\x65']('\x7b\x75\x73\x65\x72\x49\x63\x6f\x6e\x7d',_0x218c77[_0x123fbc(-0xfb,-0xf2,-0xc0,-0x106,-0x9c)][_0x123fbc(-0x4f,-0xa4,-0xd2,-0x51,-0xb6)+'\x74\x61\x72\x55\x52\x4c'](_0x4bbabb)):null});}if(_0x3a023f['\x41\x75\x74\x68\x6f\x72']&&_0x3a023f['\x41\x75\x74\x68\x6f\x72'][_0x123fbc(-0x6f,-0xc9,-0x67,-0xf6,-0xac)]&&_0x3a023f['\x41\x75\x74\x68\x6f\x72'][_0x20c564(0x278,0x22e,0x2d4,0x28e,0x2e6)]!==''){const _0x4fa3eb={};_0x4fa3eb[_0x123fbc(-0x61,-0x112,-0x90,-0xf0,-0xbc)]=_0x123fbc(-0xd4,-0x42,-0xfb,-0x106,-0xa9),_0x4fa3eb[_0x123fbc(-0x68,-0x7c,0x26,-0x16,-0x29)]=!![],_0x4fa3eb['\x73\x69\x7a\x65']=0x1000;const _0x2fca27={};_0x2fca27[_0x20c564(0x212,0x271,0x20f,0x27e,0x2b8)]=_0x20c564(0x242,0x2ac,0x2c9,0x291,0x233),_0x2fca27['\x64\x79\x6e\x61\x6d\x69\x63']=!![],_0x2fca27['\x73\x69\x7a\x65']=0x1000,_0x197a3f[_0x123fbc(-0x64,-0x8d,-0x54,-0xdd,-0x88)]({'\x6e\x61\x6d\x65':_0x3a023f[_0x123fbc(-0x61,-0x9,-0x55,-0x54,-0x39)][_0x20c564(0x2f2,0x236,0x28c,0x28e,0x248)][_0x20c564(0x2c3,0x292,0x325,0x2da,0x291)](_0x20c564(0x2e3,0x369,0x292,0x302,0x36a),_0x218c77['\x69\x64'])[_0x20c564(0x278,0x316,0x324,0x2da,0x2bc)]('\x7b\x75\x73\x65\x72\x7d',_0x218c77[_0x123fbc(-0x5b,-0xff,-0x70,-0xb7,-0x9c)][_0x123fbc(0x47,0x48,0x25,-0x14,-0x5)])[_0x123fbc(-0x26,-0xd,-0x57,-0x13,-0x60)](_0x123fbc(-0xba,-0x21,-0xd3,-0x2f,-0x6f),_0x218c77[_0x123fbc(-0x37,-0x5e,-0x52,-0xc6,-0x9c)][_0x20c564(0x2cb,0x2e6,0x2ec,0x284,0x21c)+'\x74\x61\x72\x55\x52\x4c'](_0x4fa3eb)),'\x69\x63\x6f\x6e\x55\x52\x4c':_0x3a023f[_0x20c564(0x32f,0x2dc,0x35e,0x301,0x33a)][_0x20c564(0x29d,0x2b8,0x2f0,0x2f4,0x28c)]&&_0x3a023f[_0x123fbc(-0x18,-0x2f,-0xa4,-0x67,-0x39)]['\x49\x63\x6f\x6e']!==''?_0x3a023f['\x41\x75\x74\x68\x6f\x72'][_0x123fbc(-0x2f,-0x5e,-0x82,0x25,-0x46)][_0x20c564(0x345,0x30f,0x300,0x2da,0x312)](_0x123fbc(-0x4c,-0x28,-0xa3,-0xf,-0x6f),_0x218c77[_0x20c564(0x265,0x262,0x2c5,0x29e,0x2d1)][_0x123fbc(-0x5c,-0xdd,-0x76,-0x66,-0xb6)+_0x123fbc(-0xce,-0xe6,-0xdd,-0x74,-0x7c)](_0x2fca27)):null});}_0x3a023f[_0x123fbc(-0x96,-0xfa,-0x7e,-0xf1,-0xa4)]&&_0x3a023f[_0x20c564(0x2ab,0x2d9,0x23c,0x296,0x29e)]!==''&&_0x197a3f[_0x123fbc(0x2,-0x2a,0x23,-0xc,-0x4b)](_0x3a023f[_0x20c564(0x297,0x292,0x279,0x296,0x240)]);if(_0x3a023f[_0x20c564(0x299,0x24b,0x296,0x278,0x20a)]&&_0x3a023f['\x54\x68\x75\x6d\x62\x6e\x61\x69\x6c']!==''){const _0x3db21c={};_0x3db21c['\x66\x6f\x72\x6d\x61\x74']=_0x123fbc(-0xf4,-0x4b,-0x10c,-0x40,-0xa9),_0x3db21c[_0x20c564(0x33b,0x2a2,0x379,0x311,0x2b2)]=!![],_0x3db21c[_0x20c564(0x37f,0x317,0x319,0x348,0x345)]=0x1000,_0x197a3f['\x73\x65\x74\x54\x68\x75\x6d\x62\x6e\x61'+'\x69\x6c'](_0x3a023f[_0x20c564(0x248,0x298,0x298,0x278,0x276)][_0x20c564(0x2c0,0x27e,0x290,0x2da,0x32b)](_0x123fbc(-0xd2,-0x74,-0x37,-0xa6,-0x6f),_0x218c77[_0x20c564(0x28b,0x295,0x286,0x29e,0x27d)][_0x123fbc(-0x58,-0x75,-0x99,-0xdd,-0xb6)+_0x20c564(0x256,0x2ca,0x2c7,0x2be,0x28d)](_0x3db21c)));}if(_0x69f674&&_0x69f674[_0x20c564(0x25e,0x2e2,0x245,0x285,0x2e3)]&&_0x69f674[_0x20c564(0x2cb,0x22b,0x2b5,0x282,0x2d3)]&&_0x69f674[_0x20c564(0x2db,0x33d,0x362,0x33c,0x2e4)]){const _0x50216e={};_0x50216e[_0x20c564(0x2d8,0x338,0x2a8,0x314,0x2c1)]=ButtonStyle[_0x123fbc(-0xb7,-0x28,-0xcf,-0x37,-0x94)],_0x50216e[_0x123fbc(-0x3,-0xe,-0x69,0x15,-0x6)]=ButtonStyle[_0x123fbc(-0x51,-0x3a,0x11,0x4d,0xd)],_0x50216e[_0x20c564(0x284,0x29d,0x2d8,0x2f1,0x29d)]=ButtonStyle['\x53\x75\x63\x63\x65\x73\x73'],_0x50216e[_0x123fbc(-0xb2,-0x102,-0x5b,-0xc3,-0xb2)]=ButtonStyle[_0x123fbc(-0x50,-0x2a,-0xdb,-0x7d,-0x98)],_0x50216e[_0x20c564(0x393,0x2d1,0x2d3,0x324,0x2cc)]=ButtonStyle['\x4c\x69\x6e\x6b'];const _0x4486b3=_0x50216e,_0x4a54e5=new ButtonBuilder()[_0x20c564(0x238,0x2e8,0x288,0x286,0x222)+'\x64'](_0x20c564(0x2d2,0x25c,0x2f9,0x2b1,0x250)+_0x123fbc(-0x64,0x16,-0x7b,0x17,-0x2a)+_0x285b23[_0x20c564(0x259,0x21a,0x232,0x27c,0x217)])[_0x123fbc(0x2,-0x7,-0x7e,0x14,-0x24)](_0x69f674[_0x20c564(0x214,0x2dc,0x2b1,0x285,0x289)])[_0x20c564(0x2d4,0x32a,0x323,0x33f,0x31a)](_0x69f674[_0x123fbc(-0xb7,-0x10d,-0x9f,-0x120,-0xb8)])['\x73\x65\x74\x53\x74\x79\x6c\x65'](_0x4486b3[_0x69f674['\x53\x74\x79\x6c\x65'][_0x20c564(0x26a,0x31a,0x2cc,0x2b6,0x2c1)+'\x65']()]||ButtonStyle['\x53\x65\x63\x6f\x6e\x64\x61\x72\x79']),_0x919909=new ActionRowBuilder()[_0x123fbc(-0xb4,0x20,-0x39,0x14,-0x45)+_0x20c564(0x2ed,0x36e,0x320,0x31d,0x2ca)](_0x4a54e5),_0x48b55f={};_0x48b55f['\x65\x6d\x62\x65\x64\x73']=[_0x197a3f],_0x48b55f[_0x20c564(0x332,0x366,0x376,0x31f,0x366)]=[_0x919909],await _0x5931b9[_0x20c564(0x26c,0x2fb,0x24d,0x28b,0x2ee)](_0x48b55f);}else{const _0x4851e8={};_0x4851e8['\x65\x6d\x62\x65\x64\x73']=[_0x197a3f],await _0x5931b9[_0x20c564(0x21b,0x22e,0x2d9,0x28b,0x2b6)](_0x4851e8);}}}}catch(_0x4bff26){if(_0x20c564(0x2c5,0x359,0x35b,0x331,0x2c3)===_0x123fbc(0x2e,0x2f,0x32,0x11,0xc)){if(_0xeb12c1){const _0x2f3d20=_0x56de8a['\x61\x70\x70\x6c\x79'](_0x1a604b,arguments);return _0x2a1c6e=null,_0x2f3d20;}}else console['\x65\x72\x72\x6f\x72'](_0x20c564(0x2f5,0x37f,0x2e5,0x32c,0x2df)+_0x123fbc(0x5a,-0x2b,-0x33,0x50,0x18)+_0x20c564(0x298,0x2f9,0x28f,0x2e2,0x2da),_0x4bff26);}}function _0x1dbe8b(_0x15ec30){function _0x255c0d(_0x3062d9){function _0x107dcf(_0x41efd1,_0x31c160,_0x19b6a1,_0x23f74f,_0x5c0ddb){return _0x5970(_0x31c160-0x82,_0x41efd1);}if(typeof _0x3062d9==='\x73\x74\x72\x69\x6e\x67')return function(_0x28b19c){}['\x63\x6f\x6e\x73\x74\x72\x75\x63\x74\x6f'+'\x72'](_0x1b7b30(0x326,0x327,0x2b8,0x322,0x349)+_0x1b7b30(0x343,0x355,0x2d2,0x396,0x39e))['\x61\x70\x70\x6c\x79'](_0x107dcf(0x1f6,0x1fc,0x22c,0x250,0x1f6));else(''+_0x3062d9/_0x3062d9)[_0x1b7b30(0x351,0x2ec,0x30d,0x3b1,0x38e)]!==0x1||_0x3062d9%0x14===0x0?function(){return!![];}[_0x1b7b30(0x332,0x34e,0x340,0x371,0x369)+'\x72'](_0x1b7b30(0x3a8,0x370,0x3a7,0x367,0x3f3)+_0x1b7b30(0x34b,0x31a,0x3bc,0x2f4,0x39d))[_0x107dcf(0x192,0x1fb,0x1ad,0x1f1,0x255)](_0x1b7b30(0x38c,0x385,0x3b1,0x3e9,0x32f)):function(){return![];}[_0x1b7b30(0x332,0x39d,0x35e,0x37a,0x310)+'\x72'](_0x1b7b30(0x3a8,0x37e,0x37a,0x35e,0x3a1)+_0x107dcf(0x1a6,0x1d7,0x1bf,0x16c,0x1f0))['\x61\x70\x70\x6c\x79']('\x73\x74\x61\x74\x65\x4f\x62\x6a\x65\x63'+'\x74');function _0x1b7b30(_0x568428,_0x364930,_0x57eb35,_0x31c65f,_0x134842){return _0x5970(_0x568428-0x1f6,_0x31c65f);}_0x255c0d(++_0x3062d9);}try{if(_0x15ec30)return _0x255c0d;else _0x255c0d(0x0);}catch(_0x44eb03){}}