const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction, client) {
        try {
            // This would be triggered by a custom report command or button
            // Check if this is a report interaction
            if (!interaction.isCommand() && !interaction.isButton()) return;
            if (interaction.isCommand() && interaction.commandName !== 'report') return;
            if (interaction.isButton() && !interaction.customId.startsWith('report_')) return;
            
            console.log(`📋 Report created in ${interaction.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(interaction.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'REPORT_CREATE')) return;
            
            // Get report details from interaction
            let targetUser, reportReason, reportType;
            
            if (interaction.isCommand()) {
                targetUser = interaction.options.getUser('user');
                reportReason = interaction.options.getString('reason') || 'Không có lý do cụ thể';
                reportType = interaction.options.getString('type') || 'general';
            } else if (interaction.isButton()) {
                // Handle button-based reports (e.g., message context menu)
                const reportData = await client.db.getReportData(interaction.customId);
                targetUser = reportData?.targetUser;
                reportReason = reportData?.reason || 'Report từ button';
                reportType = reportData?.type || 'message';
            }
            
            const reporter = interaction.user;
            
            // Create report ID
            const reportId = await client.db.createReport({
                guildId: interaction.guild.id,
                reporterId: reporter.id,
                targetUserId: targetUser?.id,
                reason: reportReason,
                type: reportType,
                status: 'pending'
            });
            
            // Create event data
            const eventData = {
                eventType: 'REPORT_CREATE',
                user: reporter.tag,
                userId: reporter.id,
                action: 'Report được tạo',
                details: `**${reporter.tag}** đã tạo report ${targetUser ? `về **${targetUser.tag}**` : ''}`,
                target: targetUser?.tag || 'Unknown',
                timestamp: new Date().toISOString(),
                reportId: reportId
            };
            
            // Add to database
            await client.db.addAuditLog(interaction.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📋 Report mới được tạo',
                `Vừa có report mới cần xem xét`
            );
            
            embed.setColor(0xf39c12); // Orange for new report
            
            // Get report type names
            const reportTypes = {
                'spam': 'Spam',
                'harassment': 'Quấy rối',
                'inappropriate': 'Nội dung không phù hợp',
                'scam': 'Lừa đảo',
                'raid': 'Raid/Tấn công',
                'general': 'Khác'
            };
            
            embed.addFields([
                {
                    name: '> ID Report',
                    value: `- #${reportId}`,
                    inline: true
                },
                {
                    name: '> Người report',
                    value: `- ${reporter.tag} (${reporter})`,
                    inline: false
                },
                {
                    name: '> ID người report',
                    value: `- ${reporter.id}`,
                    inline: true
                },
                {
                    name: '> Thời gian tạo',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Loại report',
                    value: `- ${reportTypes[reportType] || reportType}`,
                    inline: true
                }
            ]);
            
            // Add target user info if available
            if (targetUser) {
                embed.addFields([
                    {
                        name: '> Đối tượng bị report',
                        value: `- ${targetUser.tag} (${targetUser})`,
                        inline: false
                    },
                    {
                        name: '> ID đối tượng',
                        value: `- ${targetUser.id}`,
                        inline: true
                    }
                ]);
                
                // Add target user info
                const targetMember = interaction.guild.members.cache.get(targetUser.id);
                if (targetMember) {
                    const accountAge = Date.now() - targetUser.createdTimestamp;
                    const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
                    const memberAge = Date.now() - targetMember.joinedTimestamp;
                    const memberAgeDays = Math.floor(memberAge / (1000 * 60 * 60 * 24));
                    
                    embed.addFields([
                        {
                            name: '> Tuổi tài khoản đối tượng',
                            value: `- ${accountAgeDays} ngày`,
                            inline: true
                        },
                        {
                            name: '> Thời gian trong server',
                            value: `- ${memberAgeDays} ngày`,
                            inline: true
                        }
                    ]);
                }
            }
            
            // Add report reason
            embed.addFields({
                name: '> Lý do report',
                value: `- ${reportReason}`,
                inline: false
            });
            
            // Add reporter info
            const reporterMember = interaction.guild.members.cache.get(reporter.id);
            if (reporterMember) {
                const reporterAccountAge = Date.now() - reporter.createdTimestamp;
                const reporterAccountAgeDays = Math.floor(reporterAccountAge / (1000 * 60 * 60 * 24));
                
                embed.addFields({
                    name: '> Tuổi tài khoản người report',
                    value: `- ${reporterAccountAgeDays} ngày`,
                    inline: true
                });
            }
            
            // Add report status
            embed.addFields({
                name: '> Trạng thái',
                value: `- 🟡 Đang chờ xử lý`,
                inline: true
            });
            
            // Add priority assessment
            let priority = 'Thấp';
            let priorityColor = 0xf39c12;
            
            if (reportType === 'raid' || reportType === 'scam') {
                priority = 'Cao';
                priorityColor = 0xe74c3c;
            } else if (reportType === 'harassment' || reportType === 'inappropriate') {
                priority = 'Trung bình';
                priorityColor = 0xe67e22;
            }
            
            embed.setColor(priorityColor);
            embed.addFields({
                name: '> Mức độ ưu tiên',
                value: `- ${priority}`,
                inline: true
            });
            
            // Add next steps
            embed.addFields({
                name: '> 📝 Bước tiếp theo',
                value: [
                    '• Moderator cần xem xét report',
                    '• Kiểm tra bằng chứng và context',
                    '• Quyết định chấp nhận hoặc từ chối',
                    '• Thực hiện hành động nếu cần'
                ].join('\n'),
                inline: false
            });
            
            // Add handling guidelines
            embed.addFields({
                name: '> 💡 Hướng dẫn xử lý',
                value: [
                    '• Xem xét khách quan và công bằng',
                    '• Thu thập đầy đủ thông tin',
                    '• Tham khảo ý kiến team nếu cần',
                    '• Ghi chép quyết định rõ ràng'
                ].join('\n'),
                inline: false
            });
            
            // Set reporter avatar as thumbnail
            if (reporter.displayAvatarURL()) {
                embed.setThumbnail(reporter.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${interaction.guild.name} • Report Create`,
                iconURL: interaction.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Report create logged: #${reportId}`);
            
        } catch (error) {
            console.error('Error in reportCreate audit log:', error);
        }
    }
};
