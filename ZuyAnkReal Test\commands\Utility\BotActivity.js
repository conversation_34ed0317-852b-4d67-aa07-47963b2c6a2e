function _0x27bb(_0x5b2863,_0x2262b1){const _0x305aab=_0x305a();return _0x27bb=function(_0x27bbe6,_0x5bfc3a){_0x27bbe6=_0x27bbe6-0x1ba;let _0x5db5a1=_0x305aab[_0x27bbe6];return _0x5db5a1;},_0x27bb(_0x5b2863,_0x2262b1);}(function(_0x52832d,_0x53d320){const _0x25405d=_0x52832d();function _0x59b653(_0xd7af3f,_0x18deb5){return _0x27bb(_0xd7af3f-0x153,_0x18deb5);}while(!![]){try{const _0x5923b3=-parseInt(_0x59b653(0x35c,0x334))/0x1+-parseInt(_0x59b653(0x34e,0x375))/0x2+parseInt(_0x59b653(0x327,0x345))/0x3*(-parseInt(_0x59b653(0x313,0x33a))/0x4)+parseInt(_0x59b653(0x35a,0x36e))/0x5*(parseInt(_0x59b653(0x342,0x33b))/0x6)+-parseInt(_0x59b653(0x32f,0x303))/0x7+-parseInt(_0x59b653(0x35b,0x381))/0x8+parseInt(_0x59b653(0x35e,0x386))/0x9;if(_0x5923b3===_0x53d320)break;else _0x25405d['push'](_0x25405d['shift']());}catch(_0x2ebdd6){_0x25405d['push'](_0x25405d['shift']());}}}(_0x305a,0x4c03a));function _0x305a(){const _0x2e3fd4=['Bot\x20activity\x20management','status_type','Bot\x20Activity\x20Removed','The\x20status\x20message\x20to\x20display\x20(supports\x20placeholders)','getSubcommand','Manage\x20the\x20bot\x20activity\x20settings','155udmvCo','3858184DVhlDB','373210sISrZB','Yellow','15224013vFzEip','forEach','streaming_url','options','**Type:**\x20','idle','getString','addFields','findOne','exports','Status','ManageGuild','../../models/BotActivity','Permission\x20Denied','activity_type','activityType','push','Add\x20or\x20update\x20a\x20bot\x20activity\x20status','32mfsblZ','WATCHING','status','length','Online','getInteger','index','The\x20activity\x20type\x20(WATCHING,\x20PLAYING,\x20etc.)','No\x20Bot\x20Activities','The\x20index\x20of\x20the\x20status\x20to\x20remove\x20(1-based\x20index)','Use\x20the\x20add\x20command\x20to\x20configure\x20a\x20new\x20status.','A\x20new\x20bot\x20activity\x20status\x20has\x20been\x20successfully\x20added.','List\x20all\x20current\x20bot\x20activity\x20statuses','invisible','setRequired','Use\x20the\x20list\x20command\x20to\x20see\x20current\x20statuses.','save','streamingURL','remove','online','212694UDKJNb','The\x20bot\x27s\x20online\x20status\x20(online,\x20dnd,\x20idle,\x20invisible)','Utility','Orange','addStringOption','setFooter','Configured\x20Bot\x20Activity\x20Statuses','**Status\x20','2328809uBwJgF','CUSTOM','guild','Red','The\x20provided\x20index\x20is\x20invalid.\x20Please\x20provide\x20a\x20valid\x20status\x20index.','activities','setColor','placeholders','add','PLAYING','setName','list','Do\x20Not\x20Disturb','member','statusType','Activity\x20Type','setTitle','addChoices','Bot\x20Activity\x20Added','86532YggOyN','Flags','Blurple','List\x20all\x20available\x20placeholders\x20for\x20bot\x20activities','COMPETING','Removed\x20Status','dnd','Invalid\x20Index','reply','You\x20do\x20not\x20have\x20the\x20required\x20permissions\x20to\x20use\x20this\x20command.','addSubcommand','No\x20bot\x20activity\x20statuses\x20are\x20currently\x20configured.','143862JbcXxP','has','STREAMING',':**\x20`','setDescription','The\x20streaming\x20URL\x20(only\x20needed\x20if\x20activity\x20type\x20is\x20STREAMING)'];_0x305a=function(){return _0x2e3fd4;};return _0x305a();}function _0x15f2d1(_0x47e096,_0x515c4f){return _0x27bb(_0x47e096-0x3b1,_0x515c4f);}const {SlashCommandBuilder,PermissionsBitField,EmbedBuilder,Colors}=require('discord.js'),BotActivity=require(_0x15f2d1(0x56b,0x54f));module[_0x15f2d1(0x5c5,0x5d1)]={'data':new SlashCommandBuilder()['setName']('botactivity')[_0x15f2d1(0x5b0,0x5b3)](_0x15f2d1(0x5b7,0x5b8))['addSubcommand'](_0x168d87=>_0x168d87[_0x15f2d1(0x597,0x596)](_0x15f2d1(0x595,0x5a0))[_0x15f2d1(0x5b0,0x5b1)](_0x15f2d1(0x570,0x577))['addStringOption'](_0x28f5d4=>_0x28f5d4['setName'](_0x15f2d1(0x573,0x595))[_0x15f2d1(0x5b0,0x5ba)](_0x15f2d1(0x5b5,0x598))[_0x15f2d1(0x57f,0x563)](!![]))[_0x15f2d1(0x589,0x5a4)](_0x372d93=>_0x372d93[_0x15f2d1(0x597,0x58b)](_0x15f2d1(0x56d,0x54d))[_0x15f2d1(0x5b0,0x5b9)](_0x15f2d1(0x578,0x5a6))[_0x15f2d1(0x57f,0x598)](!![])[_0x15f2d1(0x59e,0x5b3)]({'name':_0x15f2d1(0x572,0x59e),'value':'WATCHING'},{'name':_0x15f2d1(0x596,0x5bb),'value':_0x15f2d1(0x596,0x5a6)},{'name':_0x15f2d1(0x5a4,0x5a9),'value':_0x15f2d1(0x5a4,0x5c1)},{'name':_0x15f2d1(0x5ae,0x5c6),'value':'STREAMING'},{'name':_0x15f2d1(0x58e,0x56d),'value':_0x15f2d1(0x58e,0x560)}))[_0x15f2d1(0x589,0x57d)](_0x37aa0f=>_0x37aa0f[_0x15f2d1(0x597,0x590)](_0x15f2d1(0x5b3,0x599))[_0x15f2d1(0x5b0,0x5a7)](_0x15f2d1(0x586,0x56d))[_0x15f2d1(0x57f,0x55d)](!![])[_0x15f2d1(0x59e,0x593)]({'name':_0x15f2d1(0x575,0x549),'value':_0x15f2d1(0x584,0x55d)},{'name':_0x15f2d1(0x599,0x56d),'value':_0x15f2d1(0x5a6,0x5b8)},{'name':'Idle','value':_0x15f2d1(0x5c1,0x5d3)},{'name':'Invisible','value':_0x15f2d1(0x57e,0x589)}))[_0x15f2d1(0x589,0x59f)](_0x116d15=>_0x116d15['setName'](_0x15f2d1(0x5be,0x59d))[_0x15f2d1(0x5b0,0x596)](_0x15f2d1(0x5b1,0x5bf))[_0x15f2d1(0x57f,0x596)](![])))[_0x15f2d1(0x5aa,0x5c9)](_0x2dbe3c=>_0x2dbe3c['setName'](_0x15f2d1(0x583,0x593))[_0x15f2d1(0x5b0,0x594)]('Remove\x20a\x20bot\x20activity\x20status\x20by\x20its\x20index')['addIntegerOption'](_0x15270f=>_0x15270f[_0x15f2d1(0x597,0x589)]('index')[_0x15f2d1(0x5b0,0x5da)](_0x15f2d1(0x57a,0x59e))[_0x15f2d1(0x57f,0x56d)](!![])))[_0x15f2d1(0x5aa,0x5ca)](_0x24ffaf=>_0x24ffaf['setName'](_0x15f2d1(0x598,0x597))['setDescription'](_0x15f2d1(0x57d,0x572)))['addSubcommand'](_0xc6eb6b=>_0xc6eb6b[_0x15f2d1(0x597,0x585)](_0x15f2d1(0x594,0x598))[_0x15f2d1(0x5b0,0x596)](_0x15f2d1(0x5a3,0x575))),'category':_0x15f2d1(0x587,0x55c),async 'execute'(_0x2df44c){function _0x192f28(_0x9812dd,_0x1d6350){return _0x15f2d1(_0x9812dd- -0x319,_0x1d6350);}if(!_0x2df44c[_0x192f28(0x281,0x28a)]['permissions'][_0x192f28(0x294,0x269)](PermissionsBitField[_0x192f28(0x288,0x275)][_0x192f28(0x2ae,0x2cf)]))return _0x2df44c[_0x192f28(0x28f,0x27c)]({'embeds':[new EmbedBuilder()[_0x192f28(0x27a,0x29f)](Colors['Red'])[_0x192f28(0x284,0x2a9)](_0x192f28(0x253,0x262))[_0x192f28(0x297,0x273)](_0x192f28(0x290,0x27e))['setFooter']({'text':'Manage\x20Guild\x20permission\x20required.'})],'ephemeral':!![]});const _0x5a3e7f=_0x2df44c[_0x192f28(0x2a6,0x290)][_0x192f28(0x29d,0x2ca)](),_0x977cf9=_0x2df44c[_0x192f28(0x276,0x288)]['id'];let _0x2b2d5f=await BotActivity[_0x192f28(0x2ab,0x2b6)]({'guildId':_0x977cf9});!_0x2b2d5f&&(_0x2b2d5f=new BotActivity({'guildId':_0x977cf9}));if(_0x5a3e7f===_0x192f28(0x27c,0x25b)){const _0x42efb3=_0x2df44c[_0x192f28(0x2a6,0x2d0)][_0x192f28(0x2a9,0x29f)](_0x192f28(0x25a,0x25c)),_0x3c1822=_0x2df44c[_0x192f28(0x2a6,0x2d0)][_0x192f28(0x2a9,0x2ce)]('activity_type'),_0x13e6e3=_0x2df44c[_0x192f28(0x2a6,0x28f)][_0x192f28(0x2a9,0x2bd)](_0x192f28(0x29a,0x2a2)),_0x1d8c42=_0x2df44c[_0x192f28(0x2a6,0x2a1)]['getString'](_0x192f28(0x2a5,0x29c));return _0x2b2d5f[_0x192f28(0x279,0x257)][_0x192f28(0x256,0x27f)]({'status':_0x42efb3,'activityType':_0x3c1822,'statusType':_0x13e6e3,'streamingURL':_0x3c1822===_0x192f28(0x295,0x287)?_0x1d8c42:null}),await _0x2b2d5f['save'](),_0x2df44c[_0x192f28(0x28f,0x26b)]({'embeds':[new EmbedBuilder()[_0x192f28(0x27a,0x258)](Colors['Green'])[_0x192f28(0x284,0x264)](_0x192f28(0x286,0x26b))[_0x192f28(0x297,0x27d)](_0x192f28(0x263,0x23d))[_0x192f28(0x2aa,0x2b2)]({'name':_0x192f28(0x2ad,0x2bd),'value':'`'+_0x42efb3+'`','inline':!![]},{'name':_0x192f28(0x283,0x29b),'value':'`'+_0x3c1822+'`','inline':!![]},{'name':'Status\x20Type','value':'`'+_0x13e6e3+'`','inline':!![]})['setFooter']({'text':_0x192f28(0x299,0x2a4)})],'ephemeral':!![]});}else{if(_0x5a3e7f===_0x192f28(0x26a,0x26d)){const _0x3e2d3e=_0x2df44c[_0x192f28(0x2a6,0x2ca)][_0x192f28(0x25d,0x240)](_0x192f28(0x25e,0x230))-0x1;if(_0x3e2d3e<0x0||_0x3e2d3e>=_0x2b2d5f[_0x192f28(0x279,0x289)]['length'])return _0x2df44c[_0x192f28(0x28f,0x297)]({'embeds':[new EmbedBuilder()[_0x192f28(0x27a,0x291)](Colors[_0x192f28(0x277,0x27a)])[_0x192f28(0x284,0x29d)](_0x192f28(0x28e,0x288))['setDescription'](_0x192f28(0x278,0x25b))[_0x192f28(0x271,0x2a0)]({'text':_0x192f28(0x267,0x293)})],'ephemeral':!![]});const _0x589eb1=_0x2b2d5f['activities']['splice'](_0x3e2d3e,0x1);return await _0x2b2d5f[_0x192f28(0x268,0x260)](),_0x2df44c[_0x192f28(0x28f,0x2bc)]({'embeds':[new EmbedBuilder()[_0x192f28(0x27a,0x2a0)](Colors[_0x192f28(0x26f,0x26c)])[_0x192f28(0x284,0x282)](_0x192f28(0x29b,0x296))[_0x192f28(0x297,0x2b5)]('The\x20bot\x20activity\x20status\x20has\x20been\x20successfully\x20removed.')[_0x192f28(0x2aa,0x2d4)]({'name':_0x192f28(0x28c,0x283),'value':'`'+_0x589eb1[0x0]['status']+'`','inline':!![]},{'name':_0x192f28(0x283,0x28e),'value':'`'+_0x589eb1[0x0][_0x192f28(0x255,0x234)]+'`','inline':!![]})['setFooter']({'text':_0x192f28(0x299,0x296)})],'ephemeral':!![]});}else{if(_0x5a3e7f===_0x192f28(0x27f,0x257)){if(_0x2b2d5f[_0x192f28(0x279,0x298)][_0x192f28(0x25b,0x287)]===0x0)return _0x2df44c['reply']({'embeds':[new EmbedBuilder()[_0x192f28(0x27a,0x268)](Colors[_0x192f28(0x2a2,0x27b)])['setTitle'](_0x192f28(0x260,0x27c))[_0x192f28(0x297,0x28d)](_0x192f28(0x292,0x27d))['setFooter']({'text':_0x192f28(0x262,0x257)})],'ephemeral':!![]});const _0x56913e=new EmbedBuilder()[_0x192f28(0x27a,0x258)](Colors[_0x192f28(0x289,0x288)])['setTitle'](_0x192f28(0x272,0x290))[_0x192f28(0x297,0x29f)]('Below\x20are\x20the\x20current\x20bot\x20activity\x20statuses\x20for\x20this\x20server:');return _0x2b2d5f[_0x192f28(0x279,0x27e)][_0x192f28(0x2a4,0x2c6)]((_0x10bbd9,_0x59a4bc)=>{let _0x430eca=_0x43fce6(0x20,0x44)+(_0x59a4bc+0x1)+_0x43fce6(0x43,0x4d)+_0x10bbd9[_0x43fce6(0x7,0x17)]+'`\x0a';function _0x43fce6(_0x17f1bc,_0x27fe78){return _0x192f28(_0x17f1bc- -0x253,_0x27fe78);}_0x430eca+=_0x43fce6(0x54,0x51)+_0x10bbd9['activityType']+'\x20|\x20**Presence:**\x20'+_0x10bbd9[_0x43fce6(0x2f,0x2)],_0x10bbd9[_0x43fce6(0x2,-0x1f)]===_0x43fce6(0x42,0x49)&&_0x10bbd9['streamingURL']&&(_0x430eca+='\x20|\x20**URL:**\x20[Link]('+_0x10bbd9[_0x43fce6(0x16,-0xd)]+')'),_0x56913e[_0x43fce6(0x57,0x4c)]({'name':'​','value':_0x430eca});}),_0x2df44c[_0x192f28(0x28f,0x2b2)]({'embeds':[_0x56913e],'ephemeral':!![]});}else{if(_0x5a3e7f===_0x192f28(0x27b,0x287)){const _0x5f2f75='\x0a**Available\x20Placeholders:**\x0a`{total-users}`\x20-\x20Total\x20members\x20in\x20the\x20server\x0a`{total-channels}`\x20-\x20Total\x20channels\x20in\x20the\x20server\x0a`{total-messages}`\x20-\x20Total\x20messages\x20sent\x0a`{online-members}`\x20-\x20Number\x20of\x20online\x20members\x0a`{uptime}`\x20-\x20Bot\x27s\x20uptime\x0a`{total-boosts}`\x20-\x20Number\x20of\x20server\x20boosts\x0a`{total-cases}`\x20-\x20Total\x20moderation\x20cases\x20handled\x0a`{total-suggestions}`\x20-\x20Total\x20suggestions\x20submitted\x0a`{times-bot-started}`\x20-\x20Number\x20of\x20times\x20the\x20bot\x20has\x20started\x0a`{open-tickets}`\x20-\x20Number\x20of\x20open\x20tickets\x0a`{closed-tickets}`\x20-\x20Number\x20of\x20closed\x20tickets\x0a`{deleted-tickets}`\x20-\x20Number\x20of\x20deleted\x20tickets\x0a`{total-tickets}`\x20-\x20Total\x20tickets\x20created',_0x553784=new EmbedBuilder()['setColor'](Colors[_0x192f28(0x289,0x290)])['setTitle']('Bot\x20Activity\x20Placeholders')[_0x192f28(0x297,0x286)](_0x5f2f75)[_0x192f28(0x271,0x273)]({'text':'Use\x20these\x20placeholders\x20in\x20your\x20bot\x20activities'});return _0x2df44c[_0x192f28(0x28f,0x27d)]({'embeds':[_0x553784],'ephemeral':!![]});}}}}}};