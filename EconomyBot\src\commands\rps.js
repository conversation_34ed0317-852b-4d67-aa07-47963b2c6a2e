const { <PERSON><PERSON><PERSON>ommandBuilder, <PERSON>tonBuilder, ButtonS<PERSON>le, ActionRowBuilder, ComponentType } = require('discord.js');
const User = require('../models/User');
const { parseAmount, validateAmount } = require('../utils/economy');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed, formatCurrency } = require('../utils/embedBuilder');
const config = require('../config/config');

// Lưu trữ các trận đấu đang chờ
const pendingGames = new Map();

module.exports = {
    data: new SlashCommandBuilder()
        .setName('rps')
        .setNameLocalizations({
            'vi': 'oankeo'
        })
        .setDescription('Chơi oẳn tù tì với người khác hoặc bot')
        .addSubcommand(subcommand =>
            subcommand
                .setName('challenge')
                .setNameLocalizations({
                    'vi': 'thachthuc'
                })
                .setDescription('<PERSON>h<PERSON><PERSON> thức người khác chơi oẳn tù tì')
                .addUserOption(option =>
                    option.setName('opponent')
                        .setNameLocalizations({
                            'vi': 'doithu'
                        })
                        .setDescription('Người bạn muốn thách thức')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('amount')
                        .setNameLocalizations({
                            'vi': 'sotien'
                        })
                        .setDescription('Số tiền đặt cược')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('bot')
                .setNameLocalizations({
                    'vi': 'choidoibot'
                })
                .setDescription('Chơi oẳn tù tì với bot')
                .addStringOption(option =>
                    option.setName('amount')
                        .setNameLocalizations({
                            'vi': 'sotien'
                        })
                        .setDescription('Số tiền đặt cược')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('choice')
                        .setNameLocalizations({
                            'vi': 'luachon'
                        })
                        .setDescription('Lựa chọn của bạn')
                        .setRequired(true)
                        .addChoices(
                            { name: '🪨 Búa (Rock)', value: 'rock' },
                            { name: '📄 Giấy (Paper)', value: 'paper' },
                            { name: '✂️ Kéo (Scissors)', value: 'scissors' }
                        )
                )
        ),
    
    cooldown: 5,

    async execute(interaction) {
        try {
            const subcommand = interaction.options.getSubcommand();

            switch (subcommand) {
                case 'challenge':
                    await handleChallenge(interaction);
                    break;
                case 'bot':
                    await handleBotGame(interaction);
                    break;
            }
            
        } catch (error) {
            console.error('Lỗi lệnh rps:', error);
            
            const errorEmbed = createErrorEmbed(
                'Lỗi trò chơi',
                'Không thể chơi oẳn tù tì. Vui lòng thử lại sau!'
            );
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};

async function handleChallenge(interaction) {
    const opponent = interaction.options.getUser('opponent');
    const amountStr = interaction.options.getString('amount');
    
    try {
        // Kiểm tra không thể thách thức chính mình
        if (opponent.id === interaction.user.id) {
            const errorEmbed = createErrorEmbed(
                'Lỗi',
                'Bạn không thể thách thức chính mình!'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Kiểm tra không thể thách thức bot
        if (opponent.bot) {
            const errorEmbed = createErrorEmbed(
                'Lỗi',
                'Sử dụng `/rps bot` để chơi với bot!'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Parse và validate số tiền
        const challengerData = await User.findOrCreate(interaction.user);
        let amount = parseAmount(amountStr);
        if (amount === 'all') amount = challengerData.balance;
        if (amount === 'half') amount = Math.floor(challengerData.balance / 2);
        
        amount = validateAmount(amount, challengerData.balance);
        
        if (amount < 50) {
            const errorEmbed = createErrorEmbed(
                'Số tiền quá nhỏ',
                'Số tiền đặt cược tối thiểu là 50 xu!'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Kiểm tra số dư của người thách thức
        if (challengerData.balance < amount) {
            const errorEmbed = createErrorEmbed(
                'Số dư không đủ',
                `Bạn cần **${formatCurrency(amount)}** nhưng chỉ có **${formatCurrency(challengerData.balance)}**`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Tạo buttons
        const acceptButton = new ButtonBuilder()
            .setCustomId('rps_accept')
            .setLabel('Chấp nhận')
            .setStyle(ButtonStyle.Success)
            .setEmoji('✅');

        const declineButton = new ButtonBuilder()
            .setCustomId('rps_decline')
            .setLabel('Từ chối')
            .setStyle(ButtonStyle.Danger)
            .setEmoji('❌');

        const row = new ActionRowBuilder().addComponents(acceptButton, declineButton);

        // Tạo embed thách thức
        const challengeEmbed = createInfoEmbed(
            'Thách thức Oẳn Tù Tì!',
            `${interaction.user} thách thức ${opponent} chơi oẳn tù tì!\n\n` +
            `💰 **Tiền cược:** ${formatCurrency(amount)}\n` +
            `🎯 **Luật chơi:** Thắng nhận tất cả, thua mất tiền cược\n\n` +
            `${opponent}, bạn có chấp nhận thách thức này không?`
        );

        const response = await interaction.reply({ 
            embeds: [challengeEmbed], 
            components: [row],
            fetchReply: true 
        });

        // Lưu thông tin trận đấu
        pendingGames.set(response.id, {
            challenger: interaction.user.id,
            opponent: opponent.id,
            amount: amount,
            timestamp: Date.now()
        });

        // Collector cho buttons
        const collector = response.createMessageComponentCollector({
            componentType: ComponentType.Button,
            time: 60000 // 1 phút
        });

        collector.on('collect', async (buttonInteraction) => {
            if (buttonInteraction.user.id !== opponent.id) {
                await buttonInteraction.reply({ 
                    content: 'Chỉ người được thách thức mới có thể phản hồi!', 
                    ephemeral: true 
                });
                return;
            }

            if (buttonInteraction.customId === 'rps_accept') {
                await startPvPGame(buttonInteraction, response.id);
            } else {
                await buttonInteraction.update({
                    embeds: [createErrorEmbed(
                        'Thách thức bị từ chối',
                        `${opponent} đã từ chối thách thức của ${interaction.user}`
                    )],
                    components: []
                });
                pendingGames.delete(response.id);
            }
        });

        collector.on('end', () => {
            if (pendingGames.has(response.id)) {
                interaction.editReply({
                    embeds: [createErrorEmbed(
                        'Thách thức hết hạn',
                        'Thách thức đã hết hạn sau 1 phút'
                    )],
                    components: []
                });
                pendingGames.delete(response.id);
            }
        });
        
    } catch (error) {
        const errorEmbed = createErrorEmbed(
            'Lỗi thách thức',
            error.message || 'Không thể tạo thách thức'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function startPvPGame(interaction, gameId) {
    const gameData = pendingGames.get(gameId);
    if (!gameData) return;

    // Tạo buttons cho lựa chọn
    const rockButton = new ButtonBuilder()
        .setCustomId('rps_rock')
        .setLabel('Búa')
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('🪨');

    const paperButton = new ButtonBuilder()
        .setCustomId('rps_paper')
        .setLabel('Giấy')
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('📄');

    const scissorsButton = new ButtonBuilder()
        .setCustomId('rps_scissors')
        .setLabel('Kéo')
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('✂️');

    const row = new ActionRowBuilder().addComponents(rockButton, paperButton, scissorsButton);

    const gameEmbed = createInfoEmbed(
        'Trận đấu Oẳn Tù Tì bắt đầu!',
        `🥊 **Đối thủ:** <@${gameData.challenger}> vs <@${gameData.opponent}>\n` +
        `💰 **Tiền cược:** ${formatCurrency(gameData.amount)}\n\n` +
        `Cả hai người hãy chọn lựa chọn của mình!`
    );

    await interaction.update({
        embeds: [gameEmbed],
        components: [row]
    });

    // Lưu lựa chọn của người chơi
    const choices = new Map();
    
    const collector = interaction.message.createMessageComponentCollector({
        componentType: ComponentType.Button,
        time: 30000 // 30 giây
    });

    collector.on('collect', async (buttonInteraction) => {
        const userId = buttonInteraction.user.id;
        
        if (userId !== gameData.challenger && userId !== gameData.opponent) {
            await buttonInteraction.reply({ 
                content: 'Bạn không phải là người chơi trong trận này!', 
                ephemeral: true 
            });
            return;
        }

        const choice = buttonInteraction.customId.replace('rps_', '');
        choices.set(userId, choice);

        await buttonInteraction.reply({ 
            content: `Bạn đã chọn ${getChoiceEmoji(choice)}!`, 
            ephemeral: true 
        });

        // Kiểm tra xem cả hai đã chọn chưa
        if (choices.size === 2) {
            await finishPvPGame(interaction, gameData, choices);
            collector.stop();
        }
    });

    collector.on('end', () => {
        if (choices.size < 2) {
            interaction.editReply({
                embeds: [createErrorEmbed(
                    'Trận đấu hết thời gian',
                    'Một hoặc cả hai người chơi không chọn trong thời gian quy định'
                )],
                components: []
            });
        }
        pendingGames.delete(gameId);
    });
}

async function finishPvPGame(interaction, gameData, choices) {
    const challengerChoice = choices.get(gameData.challenger);
    const opponentChoice = choices.get(gameData.opponent);
    
    const result = determineWinner(challengerChoice, opponentChoice);
    
    // Cập nhật số dư
    const challengerData = await User.findById(gameData.challenger);
    const opponentData = await User.findById(gameData.opponent);

    let resultEmbed;
    
    if (result === 'tie') {
        resultEmbed = createInfoEmbed(
            'Hòa!',
            `🤝 **Kết quả:** Hòa\n\n` +
            `<@${gameData.challenger}>: ${getChoiceEmoji(challengerChoice)}\n` +
            `<@${gameData.opponent}>: ${getChoiceEmoji(opponentChoice)}\n\n` +
            `Không ai mất tiền!`
        );
    } else {
        const winnerId = result === 'challenger' ? gameData.challenger : gameData.opponent;
        const loserId = result === 'challenger' ? gameData.opponent : gameData.challenger;
        
        // Chuyển tiền
        await challengerData.updateBalance(result === 'challenger' ? gameData.amount : -gameData.amount, 'rps_pvp');
        await opponentData.updateBalance(result === 'opponent' ? gameData.amount : -gameData.amount, 'rps_pvp');
        
        resultEmbed = createSuccessEmbed(
            'Kết quả trận đấu!',
            `🏆 **Người thắng:** <@${winnerId}>\n` +
            `💸 **Người thua:** <@${loserId}>\n\n` +
            `<@${gameData.challenger}>: ${getChoiceEmoji(challengerChoice)}\n` +
            `<@${gameData.opponent}>: ${getChoiceEmoji(opponentChoice)}\n\n` +
            `💰 **Tiền thắng:** ${formatCurrency(gameData.amount)}`
        );
    }

    await interaction.editReply({
        embeds: [resultEmbed],
        components: []
    });
}

async function handleBotGame(interaction) {
    const amountStr = interaction.options.getString('amount');
    const playerChoice = interaction.options.getString('choice');
    
    try {
        const userData = await User.findOrCreate(interaction.user);
        
        let amount = parseAmount(amountStr);
        if (amount === 'all') amount = userData.balance;
        if (amount === 'half') amount = Math.floor(userData.balance / 2);
        
        amount = validateAmount(amount, userData.balance);
        
        if (amount < 10) {
            const errorEmbed = createErrorEmbed(
                'Số tiền quá nhỏ',
                'Số tiền đặt cược tối thiểu là 10 xu!'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Bot chọn ngẫu nhiên
        const botChoices = ['rock', 'paper', 'scissors'];
        const botChoice = botChoices[Math.floor(Math.random() * botChoices.length)];
        
        const result = determineWinner(playerChoice, botChoice);
        
        let resultEmbed;
        
        if (result === 'tie') {
            resultEmbed = createInfoEmbed(
                'Hòa với Bot!',
                `🤝 **Kết quả:** Hòa\n\n` +
                `Bạn: ${getChoiceEmoji(playerChoice)}\n` +
                `Bot: ${getChoiceEmoji(botChoice)}\n\n` +
                `Không ai mất tiền!`
            );
        } else if (result === 'challenger') {
            const winAmount = Math.floor(amount * 1.5);
            await userData.updateBalance(winAmount - amount, 'rps_bot_win');
            
            resultEmbed = createSuccessEmbed(
                'Thắng Bot!',
                `🏆 **Bạn thắng!**\n\n` +
                `Bạn: ${getChoiceEmoji(playerChoice)}\n` +
                `Bot: ${getChoiceEmoji(botChoice)}\n\n` +
                `💰 **Tiền thắng:** ${formatCurrency(winAmount - amount)}\n` +
                `${config.emojis.money} **Số dư hiện tại:** ${formatCurrency(userData.balance + winAmount - amount)}`
            );
        } else {
            await userData.updateBalance(-amount, 'rps_bot_loss');
            
            resultEmbed = createErrorEmbed(
                'Thua Bot!',
                `😢 **Bot thắng!**\n\n` +
                `Bạn: ${getChoiceEmoji(playerChoice)}\n` +
                `Bot: ${getChoiceEmoji(botChoice)}\n\n` +
                `💸 **Tiền mất:** ${formatCurrency(amount)}\n` +
                `${config.emojis.money} **Số dư còn lại:** ${formatCurrency(userData.balance - amount)}`
            );
        }

        await interaction.reply({ embeds: [resultEmbed] });
        
    } catch (error) {
        const errorEmbed = createErrorEmbed(
            'Lỗi trò chơi',
            error.message || 'Không thể chơi với bot'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

function determineWinner(choice1, choice2) {
    if (choice1 === choice2) return 'tie';
    
    const winConditions = {
        'rock': 'scissors',
        'paper': 'rock',
        'scissors': 'paper'
    };
    
    return winConditions[choice1] === choice2 ? 'challenger' : 'opponent';
}

function getChoiceEmoji(choice) {
    const emojis = {
        'rock': '🪨 Búa',
        'paper': '📄 Giấy',
        'scissors': '✂️ Kéo'
    };
    return emojis[choice] || choice;
}
