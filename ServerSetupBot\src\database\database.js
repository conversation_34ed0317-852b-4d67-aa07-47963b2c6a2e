const sqlite3 = require("sqlite3").verbose();
const fs = require("fs");
const path = require("path");

class Database {
  constructor(dbPath) {
    this.dbPath = dbPath;
    this.db = null;

    // Tạo thư mục data nếu chưa tồn tại
    const dataDir = path.dirname(dbPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
  }

  async initialize() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error("❌ Lỗi kết nối database:", err);
          reject(err);
        } else {
          console.log("✅ Đã kết nối database SQLite");
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  async createTables() {
    const tables = [
      // Bảng cấu hình server chính
      `CREATE TABLE IF NOT EXISTS server_configs (
                guild_id TEXT PRIMARY KEY,
                guild_name TEXT,
                owner_id TEXT,
                prefix TEXT DEFAULT '!',
                language TEXT DEFAULT 'vi',
                timezone TEXT DEFAULT 'Asia/Ho_Chi_Minh',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

      // Bảng cấu hình welcome/goodbye
      `CREATE TABLE IF NOT EXISTS welcome_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                welcome_enabled BOOLEAN DEFAULT 0,
                welcome_channel_id TEXT,
                welcome_message TEXT,
                welcome_embed BOOLEAN DEFAULT 1,
                goodbye_enabled BOOLEAN DEFAULT 0,
                goodbye_channel_id TEXT,
                goodbye_message TEXT,
                goodbye_embed BOOLEAN DEFAULT 1,
                dm_welcome BOOLEAN DEFAULT 0,
                dm_welcome_message TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng auto-roles
      `CREATE TABLE IF NOT EXISTS auto_roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                role_id TEXT,
                role_name TEXT,
                target_type TEXT DEFAULT 'human', -- 'human' or 'bot'
                enabled BOOLEAN DEFAULT 1,
                delay_seconds INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng reaction roles
      `CREATE TABLE IF NOT EXISTS reaction_roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                message_id TEXT,
                channel_id TEXT,
                emoji TEXT,
                role_id TEXT,
                role_name TEXT,
                enabled BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng channel templates
      `CREATE TABLE IF NOT EXISTS channel_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                template_name TEXT,
                channels_data TEXT, -- JSON string
                created_by TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng permission templates
      `CREATE TABLE IF NOT EXISTS permission_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                template_name TEXT,
                permissions_data TEXT, -- JSON string
                created_by TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng verification system
      `CREATE TABLE IF NOT EXISTS verification_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                enabled BOOLEAN DEFAULT 0,
                verification_type TEXT DEFAULT 'button', -- reaction, button, captcha, manual, email, phone
                verification_channel_id TEXT,
                verification_role_id TEXT,
                verified_role_id TEXT,
                verification_message TEXT,
                verification_emoji TEXT DEFAULT '✅',
                auto_kick BOOLEAN DEFAULT 0,
                dm_welcome BOOLEAN DEFAULT 0,
                timeout_hours INTEGER DEFAULT 24,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng audit log system
      `CREATE TABLE IF NOT EXISTS audit_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT UNIQUE,
                enabled BOOLEAN DEFAULT 0,
                log_channel_id TEXT,
                events TEXT DEFAULT 'all', -- all, members, messages, roles, channels, server, moderation, voice, invites, emojis
                include_bots BOOLEAN DEFAULT 0,
                detailed_logs BOOLEAN DEFAULT 0,
                webhook_mode BOOLEAN DEFAULT 0,
                webhook_id TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng audit log events
      `CREATE TABLE IF NOT EXISTS audit_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                event_type TEXT,
                enabled BOOLEAN DEFAULT 1,
                channel_id TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng audit log ignore list
      `CREATE TABLE IF NOT EXISTS audit_ignore (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                ignore_type TEXT, -- channel, role, user
                target_id TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng audit log entries
      `CREATE TABLE IF NOT EXISTS audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                event_type TEXT,
                user_id TEXT,
                target_id TEXT,
                target_type TEXT, -- user, channel, role, message, etc.
                action TEXT,
                details TEXT, -- JSON string with before/after data
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng moderation logs
      `CREATE TABLE IF NOT EXISTS moderation_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                type TEXT, -- ban, unban, kick, timeout, untimeout, warn, unwarn, mute, unmute, purge
                user_id TEXT,
                moderator_id TEXT,
                reason TEXT,
                duration INTEGER, -- minutes for timeout/mute
                delete_days INTEGER, -- for ban
                warning_id INTEGER, -- for unwarn
                expires_at DATETIME, -- for timeout/mute
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng warnings
      `CREATE TABLE IF NOT EXISTS warnings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                user_id TEXT,
                moderator_id TEXT,
                reason TEXT,
                active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                removed_at DATETIME,
                removed_by TEXT,
                remove_reason TEXT,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng mutes
      `CREATE TABLE IF NOT EXISTS mutes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                user_id TEXT,
                moderator_id TEXT,
                reason TEXT,
                expires_at DATETIME,
                active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng moderation configs
      `CREATE TABLE IF NOT EXISTS moderation_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                automod_enabled BOOLEAN DEFAULT 0,
                spam_protection BOOLEAN DEFAULT 0,
                link_protection BOOLEAN DEFAULT 0,
                caps_protection BOOLEAN DEFAULT 0,
                mute_role_id TEXT,
                log_channel_id TEXT,
                warning_threshold INTEGER DEFAULT 3,
                auto_ban_threshold INTEGER DEFAULT 5,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng server templates
      `CREATE TABLE IF NOT EXISTS server_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_name TEXT,
                template_description TEXT,
                template_data TEXT, -- JSON string chứa toàn bộ config
                created_by TEXT,
                is_public BOOLEAN DEFAULT 0,
                usage_count INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

      // Bảng backups
      `CREATE TABLE IF NOT EXISTS server_backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                backup_name TEXT,
                backup_data TEXT, -- JSON string
                backup_type TEXT DEFAULT 'full', -- full, channels, roles, settings, content, permissions
                created_by TEXT,
                file_size INTEGER,
                backup_version TEXT DEFAULT '2.0',
                compressed BOOLEAN DEFAULT 0,
                include_messages BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng backup status
      `CREATE TABLE IF NOT EXISTS backup_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT UNIQUE,
                status TEXT, -- creating, loading, deleting, completed, failed, cancelled
                status_data TEXT, -- JSON string with progress info
                started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng backup intervals
      `CREATE TABLE IF NOT EXISTS backup_intervals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT UNIQUE,
                enabled BOOLEAN DEFAULT 0,
                interval_type TEXT, -- daily, weekly, monthly, custom
                interval_hours INTEGER, -- for custom intervals
                max_backups INTEGER DEFAULT 10,
                last_backup DATETIME,
                next_backup DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,
    ];

    for (const table of tables) {
      await this.run(table);
    }

    console.log("✅ Đã tạo tất cả bảng database");
  }

  // Wrapper methods cho database operations
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function (err) {
        if (err) reject(err);
        else resolve({ id: this.lastID, changes: this.changes });
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  close() {
    return new Promise((resolve, reject) => {
      this.db.close((err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  // Server Config Methods
  async createServerConfig(guildId, config = {}) {
    const sql = `INSERT OR REPLACE INTO server_configs
                     (guild_id, guild_name, owner_id, prefix, language, timezone, updated_at)
                     VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`;

    return await this.run(sql, [
      guildId,
      config.guildName || null,
      config.ownerId || null,
      config.prefix || "!",
      config.language || "vi",
      config.timezone || "Asia/Ho_Chi_Minh",
    ]);
  }

  async getServerConfig(guildId) {
    const sql = `SELECT * FROM server_configs WHERE guild_id = ?`;
    return await this.get(sql, [guildId]);
  }

  async updateServerConfig(guildId, updates) {
    const fields = Object.keys(updates)
      .map((key) => `${key} = ?`)
      .join(", ");
    const values = Object.values(updates);
    values.push(guildId);

    const sql = `UPDATE server_configs SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE guild_id = ?`;
    return await this.run(sql, values);
  }

  // Welcome Config Methods
  async getWelcomeConfig(guildId) {
    const sql = `SELECT * FROM welcome_configs WHERE guild_id = ?`;
    return await this.get(sql, [guildId]);
  }

  async updateWelcomeConfig(guildId, config) {
    const existing = await this.getWelcomeConfig(guildId);

    if (existing) {
      const fields = Object.keys(config)
        .map((key) => `${key} = ?`)
        .join(", ");
      const values = Object.values(config);
      values.push(guildId);

      const sql = `UPDATE welcome_configs SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE guild_id = ?`;
      return await this.run(sql, values);
    } else {
      const fields = ["guild_id", ...Object.keys(config)].join(", ");
      const placeholders = ["?", ...Object.keys(config).map(() => "?")].join(
        ", "
      );
      const values = [guildId, ...Object.values(config)];

      const sql = `INSERT INTO welcome_configs (${fields}) VALUES (${placeholders})`;
      return await this.run(sql, values);
    }
  }

  // Auto Role Methods
  async getAutoRoles(guildId, targetType = null) {
    let sql = `SELECT * FROM auto_roles WHERE guild_id = ? AND enabled = 1`;
    let params = [guildId];

    if (targetType) {
      sql += ` AND target_type = ?`;
      params.push(targetType);
    }

    return await this.all(sql, params);
  }

  async addAutoRole(
    guildId,
    roleId,
    roleName,
    targetType = "human",
    delaySeconds = 0
  ) {
    const sql = `INSERT INTO auto_roles (guild_id, role_id, role_name, target_type, delay_seconds) VALUES (?, ?, ?, ?, ?)`;
    return await this.run(sql, [
      guildId,
      roleId,
      roleName,
      targetType,
      delaySeconds,
    ]);
  }

  async removeAutoRole(guildId, roleId, targetType = null) {
    let sql = `DELETE FROM auto_roles WHERE guild_id = ? AND role_id = ?`;
    let params = [guildId, roleId];

    if (targetType) {
      sql += ` AND target_type = ?`;
      params.push(targetType);
    }

    return await this.run(sql, params);
  }

  async clearAutoRoles(guildId, targetType = null) {
    let sql = `DELETE FROM auto_roles WHERE guild_id = ?`;
    let params = [guildId];

    if (targetType) {
      sql += ` AND target_type = ?`;
      params.push(targetType);
    }

    return await this.run(sql, params);
  }

  // Reaction Role Methods
  async getReactionRoles(guildId, messageId = null) {
    let sql = `SELECT * FROM reaction_roles WHERE guild_id = ? AND enabled = 1`;
    let params = [guildId];

    if (messageId) {
      sql += ` AND message_id = ?`;
      params.push(messageId);
    }

    return await this.all(sql, params);
  }

  async addReactionRole(
    guildId,
    messageId,
    channelId,
    emoji,
    roleId,
    roleName
  ) {
    const sql = `INSERT INTO reaction_roles (guild_id, message_id, channel_id, emoji, role_id, role_name)
                     VALUES (?, ?, ?, ?, ?, ?)`;
    return await this.run(sql, [
      guildId,
      messageId,
      channelId,
      emoji,
      roleId,
      roleName,
    ]);
  }

  async removeReactionRole(guildId, messageId, emoji) {
    const sql = `DELETE FROM reaction_roles WHERE guild_id = ? AND message_id = ? AND emoji = ?`;
    return await this.run(sql, [guildId, messageId, emoji]);
  }

  // Verification Config Methods
  async getVerificationConfig(guildId) {
    const sql = `SELECT * FROM verification_configs WHERE guild_id = ?`;
    return await this.get(sql, [guildId]);
  }

  async updateVerificationConfig(guildId, config) {
    const existing = await this.getVerificationConfig(guildId);

    if (existing) {
      const fields = Object.keys(config)
        .map((key) => `${key} = ?`)
        .join(", ");
      const values = Object.values(config);
      values.push(guildId);

      const sql = `UPDATE verification_configs SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE guild_id = ?`;
      return await this.run(sql, values);
    } else {
      const fields = ["guild_id", ...Object.keys(config)].join(", ");
      const placeholders = ["?", ...Object.keys(config).map(() => "?")].join(
        ", "
      );
      const values = [guildId, ...Object.values(config)];

      const sql = `INSERT INTO verification_configs (${fields}) VALUES (${placeholders})`;
      return await this.run(sql, values);
    }
  }

  // Moderation Config Methods
  async getModerationConfig(guildId) {
    const sql = `SELECT * FROM moderation_configs WHERE guild_id = ?`;
    return await this.get(sql, [guildId]);
  }

  async updateModerationConfig(guildId, config) {
    const existing = await this.getModerationConfig(guildId);

    if (existing) {
      const fields = Object.keys(config)
        .map((key) => `${key} = ?`)
        .join(", ");
      const values = Object.values(config);
      values.push(guildId);

      const sql = `UPDATE moderation_configs SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE guild_id = ?`;
      return await this.run(sql, values);
    } else {
      const fields = ["guild_id", ...Object.keys(config)].join(", ");
      const placeholders = ["?", ...Object.keys(config).map(() => "?")].join(
        ", "
      );
      const values = [guildId, ...Object.values(config)];

      const sql = `INSERT INTO moderation_configs (${fields}) VALUES (${placeholders})`;
      return await this.run(sql, values);
    }
  }

  // Server Template Methods
  async getServerTemplates(isPublic = null) {
    let sql = `SELECT * FROM server_templates`;
    let params = [];

    if (isPublic !== null) {
      sql += ` WHERE is_public = ?`;
      params.push(isPublic ? 1 : 0);
    }

    sql += ` ORDER BY usage_count DESC, created_at DESC`;
    return await this.all(sql, params);
  }

  async getServerTemplate(templateId) {
    const sql = `SELECT * FROM server_templates WHERE id = ?`;
    return await this.get(sql, [templateId]);
  }

  async createServerTemplate(templateData) {
    const sql = `INSERT INTO server_templates
                 (template_name, template_description, template_data, created_by, is_public)
                 VALUES (?, ?, ?, ?, ?)`;

    return await this.run(sql, [
      templateData.name,
      templateData.description,
      JSON.stringify(templateData.data),
      templateData.createdBy,
      templateData.isPublic ? 1 : 0,
    ]);
  }

  async updateTemplateUsage(templateId) {
    const sql = `UPDATE server_templates SET usage_count = usage_count + 1 WHERE id = ?`;
    return await this.run(sql, [templateId]);
  }

  async deleteServerTemplate(templateId) {
    const sql = `DELETE FROM server_templates WHERE id = ?`;
    return await this.run(sql, [templateId]);
  }

  // Server Backup Methods
  async getServerBackups(guildId) {
    const sql = `SELECT * FROM server_backups WHERE guild_id = ? ORDER BY created_at DESC`;
    return await this.all(sql, [guildId]);
  }

  async createServerBackup(guildId, backupData) {
    const sql = `INSERT INTO server_backups
                 (guild_id, backup_name, backup_data, backup_type, created_by, file_size)
                 VALUES (?, ?, ?, ?, ?, ?)`;

    return await this.run(sql, [
      guildId,
      backupData.name,
      JSON.stringify(backupData.data),
      backupData.type || "full",
      backupData.createdBy,
      backupData.fileSize || 0,
    ]);
  }

  async getServerBackup(backupId) {
    const sql = `SELECT * FROM server_backups WHERE id = ?`;
    return await this.get(sql, [backupId]);
  }

  async deleteServerBackup(backupId) {
    const sql = `DELETE FROM server_backups WHERE id = ?`;
    return await this.run(sql, [backupId]);
  }

  async getServerBackupByName(guildId, backupName) {
    const sql = `SELECT * FROM server_backups WHERE guild_id = ? AND backup_name = ?`;
    return await this.get(sql, [guildId, backupName]);
  }

  async deleteServerBackupByName(guildId, backupName) {
    const sql = `DELETE FROM server_backups WHERE guild_id = ? AND backup_name = ?`;
    return await this.run(sql, [guildId, backupName]);
  }

  async purgeServerBackups(guildId, criteria) {
    let sql;
    let params = [guildId];

    switch (criteria) {
      case "all":
        sql = `DELETE FROM server_backups WHERE guild_id = ?`;
        break;
      case "older_30":
        sql = `DELETE FROM server_backups WHERE guild_id = ? AND created_at < datetime('now', '-30 days')`;
        break;
      case "older_7":
        sql = `DELETE FROM server_backups WHERE guild_id = ? AND created_at < datetime('now', '-7 days')`;
        break;
      case "keep_5":
        sql = `DELETE FROM server_backups WHERE guild_id = ? AND id NOT IN (
                 SELECT id FROM server_backups WHERE guild_id = ? ORDER BY created_at DESC LIMIT 5
               )`;
        params = [guildId, guildId];
        break;
      case "keep_10":
        sql = `DELETE FROM server_backups WHERE guild_id = ? AND id NOT IN (
                 SELECT id FROM server_backups WHERE guild_id = ? ORDER BY created_at DESC LIMIT 10
               )`;
        params = [guildId, guildId];
        break;
      default:
        throw new Error("Invalid purge criteria");
    }

    return await this.run(sql, params);
  }

  // Backup Status Methods
  async setBackupStatus(guildId, status, statusData = {}) {
    const sql = `INSERT OR REPLACE INTO backup_status
                 (guild_id, status, status_data, started_at, updated_at)
                 VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`;
    return await this.run(sql, [guildId, status, JSON.stringify(statusData)]);
  }

  async getBackupStatus(guildId) {
    const sql = `SELECT * FROM backup_status WHERE guild_id = ?`;
    return await this.get(sql, [guildId]);
  }

  async updateBackupStatus(guildId, status, statusData = {}) {
    const sql = `UPDATE backup_status
                 SET status = ?, status_data = ?, updated_at = CURRENT_TIMESTAMP
                 WHERE guild_id = ?`;
    return await this.run(sql, [status, JSON.stringify(statusData), guildId]);
  }

  async clearBackupStatus(guildId) {
    const sql = `DELETE FROM backup_status WHERE guild_id = ?`;
    return await this.run(sql, [guildId]);
  }

  // Backup Interval Methods
  async setBackupInterval(guildId, intervalData) {
    const sql = `INSERT OR REPLACE INTO backup_intervals
                 (guild_id, enabled, interval_type, interval_hours, max_backups, next_backup, updated_at)
                 VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`;
    return await this.run(sql, [
      guildId,
      intervalData.enabled ? 1 : 0,
      intervalData.intervalType,
      intervalData.intervalHours || null,
      intervalData.maxBackups || 10,
      intervalData.nextBackup || null,
    ]);
  }

  async getBackupInterval(guildId) {
    const sql = `SELECT * FROM backup_intervals WHERE guild_id = ?`;
    return await this.get(sql, [guildId]);
  }

  async updateBackupInterval(guildId, updates) {
    const fields = [];
    const values = [];

    if (updates.enabled !== undefined) {
      fields.push("enabled = ?");
      values.push(updates.enabled ? 1 : 0);
    }
    if (updates.lastBackup !== undefined) {
      fields.push("last_backup = ?");
      values.push(updates.lastBackup);
    }
    if (updates.nextBackup !== undefined) {
      fields.push("next_backup = ?");
      values.push(updates.nextBackup);
    }

    if (fields.length === 0) return;

    fields.push("updated_at = CURRENT_TIMESTAMP");
    values.push(guildId);

    const sql = `UPDATE backup_intervals SET ${fields.join(
      ", "
    )} WHERE guild_id = ?`;
    return await this.run(sql, values);
  }

  async deleteBackupInterval(guildId) {
    const sql = `DELETE FROM backup_intervals WHERE guild_id = ?`;
    return await this.run(sql, [guildId]);
  }

  async getAllActiveBackupIntervals() {
    const sql = `SELECT * FROM backup_intervals WHERE enabled = 1 AND next_backup <= datetime('now')`;
    return await this.all(sql);
  }

  // Audit Log Methods
  async updateAuditConfig(guildId, config) {
    const existing = await this.getAuditConfig(guildId);

    if (existing) {
      const fields = Object.keys(config)
        .map((key) => `${key} = ?`)
        .join(", ");
      const values = Object.values(config);
      values.push(guildId);

      const sql = `UPDATE audit_configs SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE guild_id = ?`;
      return await this.run(sql, values);
    } else {
      const fields = ["guild_id", ...Object.keys(config)].join(", ");
      const placeholders = ["?", ...Object.keys(config).map(() => "?")].join(
        ", "
      );
      const values = [guildId, ...Object.values(config)];

      const sql = `INSERT INTO audit_configs (${fields}) VALUES (${placeholders})`;
      return await this.run(sql, values);
    }
  }

  async getAuditConfig(guildId) {
    const sql = `SELECT * FROM audit_configs WHERE guild_id = ?`;
    return await this.get(sql, [guildId]);
  }

  async deleteAuditConfig(guildId) {
    const sql = `DELETE FROM audit_configs WHERE guild_id = ?`;
    return await this.run(sql, [guildId]);
  }

  // Audit Events Methods
  async setAuditEvent(guildId, eventType, enabled, channelId = null) {
    const sql = `INSERT OR REPLACE INTO audit_events
                 (guild_id, event_type, enabled, channel_id, updated_at)
                 VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)`;
    return await this.run(sql, [
      guildId,
      eventType,
      enabled ? 1 : 0,
      channelId,
    ]);
  }

  async getAuditEvents(guildId) {
    const sql = `SELECT * FROM audit_events WHERE guild_id = ?`;
    return await this.all(sql, [guildId]);
  }

  async getAuditEvent(guildId, eventType) {
    const sql = `SELECT * FROM audit_events WHERE guild_id = ? AND event_type = ?`;
    return await this.get(sql, [guildId, eventType]);
  }

  async deleteAuditEvent(guildId, eventType) {
    const sql = `DELETE FROM audit_events WHERE guild_id = ? AND event_type = ?`;
    return await this.run(sql, [guildId, eventType]);
  }

  // Audit Ignore Methods
  async addAuditIgnore(guildId, ignoreType, targetId) {
    const sql = `INSERT OR IGNORE INTO audit_ignore (guild_id, ignore_type, target_id) VALUES (?, ?, ?)`;
    return await this.run(sql, [guildId, ignoreType, targetId]);
  }

  async removeAuditIgnore(guildId, ignoreType, targetId) {
    const sql = `DELETE FROM audit_ignore WHERE guild_id = ? AND ignore_type = ? AND target_id = ?`;
    return await this.run(sql, [guildId, ignoreType, targetId]);
  }

  async getAuditIgnoreList(guildId, ignoreType = null) {
    let sql = `SELECT * FROM audit_ignore WHERE guild_id = ?`;
    let params = [guildId];

    if (ignoreType) {
      sql += ` AND ignore_type = ?`;
      params.push(ignoreType);
    }

    return await this.all(sql, params);
  }

  async isAuditIgnored(guildId, ignoreType, targetId) {
    const sql = `SELECT COUNT(*) as count FROM audit_ignore WHERE guild_id = ? AND ignore_type = ? AND target_id = ?`;
    const result = await this.get(sql, [guildId, ignoreType, targetId]);
    return result.count > 0;
  }

  // Audit Log Entries Methods
  async addAuditLog(guildId, logData) {
    const sql = `INSERT INTO audit_logs
                 (guild_id, event_type, user_id, target_id, target_type, action, details)
                 VALUES (?, ?, ?, ?, ?, ?, ?)`;
    return await this.run(sql, [
      guildId,
      logData.eventType,
      logData.userId,
      logData.targetId,
      logData.targetType,
      logData.action,
      JSON.stringify(logData.details || {}),
    ]);
  }

  async getAuditLogs(guildId, options = {}) {
    let sql = `SELECT * FROM audit_logs WHERE guild_id = ?`;
    let params = [guildId];

    if (options.eventType) {
      sql += ` AND event_type = ?`;
      params.push(options.eventType);
    }

    if (options.userId) {
      sql += ` AND user_id = ?`;
      params.push(options.userId);
    }

    if (options.days) {
      sql += ` AND timestamp >= datetime('now', '-${options.days} days')`;
    }

    if (options.search) {
      sql += ` AND (action LIKE ? OR details LIKE ?)`;
      params.push(`%${options.search}%`, `%${options.search}%`);
    }

    sql += ` ORDER BY timestamp DESC`;

    if (options.limit) {
      sql += ` LIMIT ?`;
      params.push(options.limit);
    }

    return await this.all(sql, params);
  }

  async getAuditLogStats(guildId, days = 30) {
    const sql = `SELECT
                   event_type,
                   COUNT(*) as count,
                   DATE(timestamp) as date
                 FROM audit_logs
                 WHERE guild_id = ? AND timestamp >= datetime('now', '-${days} days')
                 GROUP BY event_type, DATE(timestamp)
                 ORDER BY timestamp DESC`;
    return await this.all(sql, [guildId]);
  }

  async cleanupAuditLogs(guildId, days = 90) {
    const sql = `DELETE FROM audit_logs WHERE guild_id = ? AND timestamp < datetime('now', '-${days} days')`;
    return await this.run(sql, [guildId]);
  }

  // Moderation Log Methods
  async addModerationLog(guildId, logData) {
    const sql = `INSERT INTO moderation_logs
                 (guild_id, type, user_id, moderator_id, reason, duration, delete_days, warning_id, expires_at)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    return await this.run(sql, [
      guildId,
      logData.type,
      logData.userId,
      logData.moderatorId,
      logData.reason,
      logData.duration || null,
      logData.deleteDays || null,
      logData.warningId || null,
      logData.expiresAt || null,
    ]);
  }

  async getModerationLogs(guildId, options = {}) {
    let sql = `SELECT * FROM moderation_logs WHERE guild_id = ?`;
    let params = [guildId];

    if (options.type) {
      sql += ` AND type = ?`;
      params.push(options.type);
    }

    if (options.userId) {
      sql += ` AND user_id = ?`;
      params.push(options.userId);
    }

    if (options.moderatorId) {
      sql += ` AND moderator_id = ?`;
      params.push(options.moderatorId);
    }

    if (options.days) {
      sql += ` AND created_at >= datetime('now', '-${options.days} days')`;
    }

    sql += ` ORDER BY created_at DESC`;

    if (options.limit) {
      sql += ` LIMIT ?`;
      params.push(options.limit);
    }

    return await this.all(sql, params);
  }

  // Warning Methods
  async addWarning(guildId, userId, moderatorId, reason) {
    const sql = `INSERT INTO warnings (guild_id, user_id, moderator_id, reason) VALUES (?, ?, ?, ?)`;
    return await this.run(sql, [guildId, userId, moderatorId, reason]);
  }

  async getWarnings(guildId, userId, activeOnly = true) {
    let sql = `SELECT * FROM warnings WHERE guild_id = ? AND user_id = ?`;
    const params = [guildId, userId];

    if (activeOnly) {
      sql += ` AND active = 1`;
    }

    sql += ` ORDER BY created_at DESC`;
    return await this.all(sql, params);
  }

  async removeWarning(guildId, warningId, removedBy, removeReason) {
    const sql = `UPDATE warnings
                 SET active = 0, removed_at = CURRENT_TIMESTAMP, removed_by = ?, remove_reason = ?
                 WHERE guild_id = ? AND id = ? AND active = 1`;
    return await this.run(sql, [removedBy, removeReason, guildId, warningId]);
  }

  async removeAllWarnings(guildId, userId, removedBy, removeReason) {
    const sql = `UPDATE warnings
                 SET active = 0, removed_at = CURRENT_TIMESTAMP, removed_by = ?, remove_reason = ?
                 WHERE guild_id = ? AND user_id = ? AND active = 1`;
    return await this.run(sql, [removedBy, removeReason, guildId, userId]);
  }

  async getWarningCount(guildId, userId) {
    const sql = `SELECT COUNT(*) as count FROM warnings WHERE guild_id = ? AND user_id = ? AND active = 1`;
    const result = await this.get(sql, [guildId, userId]);
    return result.count;
  }

  // Mute Methods
  async addMute(guildId, userId, moderatorId, reason, expiresAt = null) {
    const sql = `INSERT INTO mutes (guild_id, user_id, moderator_id, reason, expires_at) VALUES (?, ?, ?, ?, ?)`;
    return await this.run(sql, [
      guildId,
      userId,
      moderatorId,
      reason,
      expiresAt,
    ]);
  }

  async getMute(guildId, userId) {
    const sql = `SELECT * FROM mutes WHERE guild_id = ? AND user_id = ? AND active = 1`;
    return await this.get(sql, [guildId, userId]);
  }

  async removeMute(guildId, userId) {
    const sql = `UPDATE mutes SET active = 0 WHERE guild_id = ? AND user_id = ? AND active = 1`;
    return await this.run(sql, [guildId, userId]);
  }

  async getExpiredMutes() {
    const sql = `SELECT * FROM mutes WHERE active = 1 AND expires_at IS NOT NULL AND expires_at <= datetime('now')`;
    return await this.all(sql);
  }

  async cleanupModerationLogs(guildId, days = 90) {
    const sql = `DELETE FROM moderation_logs WHERE guild_id = ? AND created_at < datetime('now', '-${days} days')`;
    return await this.run(sql, [guildId]);
  }
}

module.exports = Database;
