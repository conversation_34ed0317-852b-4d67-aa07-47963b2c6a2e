const sqlite3 = require("sqlite3").verbose();
const fs = require("fs");
const path = require("path");

class Database {
  constructor(dbPath) {
    this.dbPath = dbPath;
    this.db = null;

    // Tạo thư mục data nếu chưa tồn tại
    const dataDir = path.dirname(dbPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
  }

  async initialize() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error("❌ Lỗi kết nối database:", err);
          reject(err);
        } else {
          console.log("✅ Đã kết nối database SQLite");
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  async createTables() {
    const tables = [
      // Bảng cấu hình server chính
      `CREATE TABLE IF NOT EXISTS server_configs (
                guild_id TEXT PRIMARY KEY,
                guild_name TEXT,
                owner_id TEXT,
                prefix TEXT DEFAULT '!',
                language TEXT DEFAULT 'vi',
                timezone TEXT DEFAULT 'Asia/Ho_Chi_Minh',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

      // Bảng cấu hình welcome/goodbye
      `CREATE TABLE IF NOT EXISTS welcome_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                welcome_enabled BOOLEAN DEFAULT 0,
                welcome_channel_id TEXT,
                welcome_message TEXT,
                welcome_embed BOOLEAN DEFAULT 1,
                goodbye_enabled BOOLEAN DEFAULT 0,
                goodbye_channel_id TEXT,
                goodbye_message TEXT,
                goodbye_embed BOOLEAN DEFAULT 1,
                dm_welcome BOOLEAN DEFAULT 0,
                dm_welcome_message TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng auto-roles
      `CREATE TABLE IF NOT EXISTS auto_roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                role_id TEXT,
                role_name TEXT,
                enabled BOOLEAN DEFAULT 1,
                delay_seconds INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng reaction roles
      `CREATE TABLE IF NOT EXISTS reaction_roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                message_id TEXT,
                channel_id TEXT,
                emoji TEXT,
                role_id TEXT,
                role_name TEXT,
                enabled BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng channel templates
      `CREATE TABLE IF NOT EXISTS channel_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                template_name TEXT,
                channels_data TEXT, -- JSON string
                created_by TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng permission templates
      `CREATE TABLE IF NOT EXISTS permission_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                template_name TEXT,
                permissions_data TEXT, -- JSON string
                created_by TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng verification system
      `CREATE TABLE IF NOT EXISTS verification_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                enabled BOOLEAN DEFAULT 0,
                verification_type TEXT DEFAULT 'reaction', -- reaction, captcha, manual
                verification_channel_id TEXT,
                verification_role_id TEXT,
                verified_role_id TEXT,
                verification_message TEXT,
                verification_emoji TEXT DEFAULT '✅',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng moderation configs
      `CREATE TABLE IF NOT EXISTS moderation_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                automod_enabled BOOLEAN DEFAULT 0,
                spam_protection BOOLEAN DEFAULT 0,
                link_protection BOOLEAN DEFAULT 0,
                caps_protection BOOLEAN DEFAULT 0,
                mute_role_id TEXT,
                log_channel_id TEXT,
                warning_threshold INTEGER DEFAULT 3,
                auto_ban_threshold INTEGER DEFAULT 5,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,

      // Bảng server templates
      `CREATE TABLE IF NOT EXISTS server_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_name TEXT,
                template_description TEXT,
                template_data TEXT, -- JSON string chứa toàn bộ config
                created_by TEXT,
                is_public BOOLEAN DEFAULT 0,
                usage_count INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

      // Bảng backups
      `CREATE TABLE IF NOT EXISTS server_backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                guild_id TEXT,
                backup_name TEXT,
                backup_data TEXT, -- JSON string
                backup_type TEXT DEFAULT 'full', -- full, partial
                created_by TEXT,
                file_size INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES server_configs (guild_id)
            )`,
    ];

    for (const table of tables) {
      await this.run(table);
    }

    console.log("✅ Đã tạo tất cả bảng database");
  }

  // Wrapper methods cho database operations
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function (err) {
        if (err) reject(err);
        else resolve({ id: this.lastID, changes: this.changes });
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  close() {
    return new Promise((resolve, reject) => {
      this.db.close((err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  // Server Config Methods
  async createServerConfig(guildId, config = {}) {
    const sql = `INSERT OR REPLACE INTO server_configs
                     (guild_id, guild_name, owner_id, prefix, language, timezone, updated_at)
                     VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`;

    return await this.run(sql, [
      guildId,
      config.guildName || null,
      config.ownerId || null,
      config.prefix || "!",
      config.language || "vi",
      config.timezone || "Asia/Ho_Chi_Minh",
    ]);
  }

  async getServerConfig(guildId) {
    const sql = `SELECT * FROM server_configs WHERE guild_id = ?`;
    return await this.get(sql, [guildId]);
  }

  async updateServerConfig(guildId, updates) {
    const fields = Object.keys(updates)
      .map((key) => `${key} = ?`)
      .join(", ");
    const values = Object.values(updates);
    values.push(guildId);

    const sql = `UPDATE server_configs SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE guild_id = ?`;
    return await this.run(sql, values);
  }

  // Welcome Config Methods
  async getWelcomeConfig(guildId) {
    const sql = `SELECT * FROM welcome_configs WHERE guild_id = ?`;
    return await this.get(sql, [guildId]);
  }

  async updateWelcomeConfig(guildId, config) {
    const existing = await this.getWelcomeConfig(guildId);

    if (existing) {
      const fields = Object.keys(config)
        .map((key) => `${key} = ?`)
        .join(", ");
      const values = Object.values(config);
      values.push(guildId);

      const sql = `UPDATE welcome_configs SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE guild_id = ?`;
      return await this.run(sql, values);
    } else {
      const fields = ["guild_id", ...Object.keys(config)].join(", ");
      const placeholders = ["?", ...Object.keys(config).map(() => "?")].join(
        ", "
      );
      const values = [guildId, ...Object.values(config)];

      const sql = `INSERT INTO welcome_configs (${fields}) VALUES (${placeholders})`;
      return await this.run(sql, values);
    }
  }

  // Auto Role Methods
  async getAutoRoles(guildId) {
    const sql = `SELECT * FROM auto_roles WHERE guild_id = ? AND enabled = 1`;
    return await this.all(sql, [guildId]);
  }

  async addAutoRole(guildId, roleId, roleName, delaySeconds = 0) {
    const sql = `INSERT INTO auto_roles (guild_id, role_id, role_name, delay_seconds) VALUES (?, ?, ?, ?)`;
    return await this.run(sql, [guildId, roleId, roleName, delaySeconds]);
  }

  async removeAutoRole(guildId, roleId) {
    const sql = `DELETE FROM auto_roles WHERE guild_id = ? AND role_id = ?`;
    return await this.run(sql, [guildId, roleId]);
  }

  // Reaction Role Methods
  async getReactionRoles(guildId, messageId = null) {
    let sql = `SELECT * FROM reaction_roles WHERE guild_id = ? AND enabled = 1`;
    let params = [guildId];

    if (messageId) {
      sql += ` AND message_id = ?`;
      params.push(messageId);
    }

    return await this.all(sql, params);
  }

  async addReactionRole(
    guildId,
    messageId,
    channelId,
    emoji,
    roleId,
    roleName
  ) {
    const sql = `INSERT INTO reaction_roles (guild_id, message_id, channel_id, emoji, role_id, role_name)
                     VALUES (?, ?, ?, ?, ?, ?)`;
    return await this.run(sql, [
      guildId,
      messageId,
      channelId,
      emoji,
      roleId,
      roleName,
    ]);
  }

  async removeReactionRole(guildId, messageId, emoji) {
    const sql = `DELETE FROM reaction_roles WHERE guild_id = ? AND message_id = ? AND emoji = ?`;
    return await this.run(sql, [guildId, messageId, emoji]);
  }

  // Verification Config Methods
  async getVerificationConfig(guildId) {
    const sql = `SELECT * FROM verification_configs WHERE guild_id = ?`;
    return await this.get(sql, [guildId]);
  }

  async updateVerificationConfig(guildId, config) {
    const existing = await this.getVerificationConfig(guildId);

    if (existing) {
      const fields = Object.keys(config)
        .map((key) => `${key} = ?`)
        .join(", ");
      const values = Object.values(config);
      values.push(guildId);

      const sql = `UPDATE verification_configs SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE guild_id = ?`;
      return await this.run(sql, values);
    } else {
      const fields = ["guild_id", ...Object.keys(config)].join(", ");
      const placeholders = ["?", ...Object.keys(config).map(() => "?")].join(
        ", "
      );
      const values = [guildId, ...Object.values(config)];

      const sql = `INSERT INTO verification_configs (${fields}) VALUES (${placeholders})`;
      return await this.run(sql, values);
    }
  }

  // Moderation Config Methods
  async getModerationConfig(guildId) {
    const sql = `SELECT * FROM moderation_configs WHERE guild_id = ?`;
    return await this.get(sql, [guildId]);
  }

  async updateModerationConfig(guildId, config) {
    const existing = await this.getModerationConfig(guildId);

    if (existing) {
      const fields = Object.keys(config)
        .map((key) => `${key} = ?`)
        .join(", ");
      const values = Object.values(config);
      values.push(guildId);

      const sql = `UPDATE moderation_configs SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE guild_id = ?`;
      return await this.run(sql, values);
    } else {
      const fields = ["guild_id", ...Object.keys(config)].join(", ");
      const placeholders = ["?", ...Object.keys(config).map(() => "?")].join(
        ", "
      );
      const values = [guildId, ...Object.values(config)];

      const sql = `INSERT INTO moderation_configs (${fields}) VALUES (${placeholders})`;
      return await this.run(sql, values);
    }
  }

  // Server Template Methods
  async getServerTemplates(isPublic = null) {
    let sql = `SELECT * FROM server_templates`;
    let params = [];

    if (isPublic !== null) {
      sql += ` WHERE is_public = ?`;
      params.push(isPublic ? 1 : 0);
    }

    sql += ` ORDER BY usage_count DESC, created_at DESC`;
    return await this.all(sql, params);
  }

  async getServerTemplate(templateId) {
    const sql = `SELECT * FROM server_templates WHERE id = ?`;
    return await this.get(sql, [templateId]);
  }

  async createServerTemplate(templateData) {
    const sql = `INSERT INTO server_templates
                 (template_name, template_description, template_data, created_by, is_public)
                 VALUES (?, ?, ?, ?, ?)`;

    return await this.run(sql, [
      templateData.name,
      templateData.description,
      JSON.stringify(templateData.data),
      templateData.createdBy,
      templateData.isPublic ? 1 : 0,
    ]);
  }

  async updateTemplateUsage(templateId) {
    const sql = `UPDATE server_templates SET usage_count = usage_count + 1 WHERE id = ?`;
    return await this.run(sql, [templateId]);
  }

  async deleteServerTemplate(templateId) {
    const sql = `DELETE FROM server_templates WHERE id = ?`;
    return await this.run(sql, [templateId]);
  }

  // Server Backup Methods
  async getServerBackups(guildId) {
    const sql = `SELECT * FROM server_backups WHERE guild_id = ? ORDER BY created_at DESC`;
    return await this.all(sql, [guildId]);
  }

  async createServerBackup(guildId, backupData) {
    const sql = `INSERT INTO server_backups
                 (guild_id, backup_name, backup_data, backup_type, created_by, file_size)
                 VALUES (?, ?, ?, ?, ?, ?)`;

    return await this.run(sql, [
      guildId,
      backupData.name,
      JSON.stringify(backupData.data),
      backupData.type || "full",
      backupData.createdBy,
      backupData.fileSize || 0,
    ]);
  }

  async getServerBackup(backupId) {
    const sql = `SELECT * FROM server_backups WHERE id = ?`;
    return await this.get(sql, [backupId]);
  }

  async deleteServerBackup(backupId) {
    const sql = `DELETE FROM server_backups WHERE id = ?`;
    return await this.run(sql, [backupId]);
  }
}

module.exports = Database;
