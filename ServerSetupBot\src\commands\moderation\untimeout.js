const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('untimeout')
        .setDescription('Hủy timeout cho thành viên')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Thành viên cần hủy timeout')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Lý do hủy timeout')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // <PERSON><PERSON>m tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.ModerateMembers)) {
            const errorEmbed = createErrorEmbed(
                '<PERSON>ạn không có quyền!',
                'Bạn cần quyền `Moderate Members` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ModerateMembers)) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                'Bot cần quyền `Moderate Members` để thực hiện lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const user = interaction.options.getUser('user');
        const reason = interaction.options.getString('reason') || 'Không có lý do';
        const guildId = interaction.guild.id;
        
        try {
            const member = await interaction.guild.members.fetch(user.id);
            
            // Kiểm tra nếu không bị timeout
            if (!member.communicationDisabledUntil || member.communicationDisabledUntil <= Date.now()) {
                const errorEmbed = createErrorEmbed(
                    'Thành viên không bị timeout!',
                    'Thành viên này hiện không bị timeout.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Lưu thời gian timeout cũ để hiển thị
            const oldTimeoutEnd = member.communicationDisabledUntil;
            
            // Thực hiện untimeout
            await member.timeout(null, `${reason} | Bởi: ${interaction.user.tag}`);
            
            // Log vào database
            await client.db.addModerationLog(guildId, {
                type: 'untimeout',
                userId: user.id,
                moderatorId: interaction.user.id,
                reason: reason,
                duration: null
            });
            
            // Gửi DM cho user
            try {
                const dmEmbed = createInfoEmbed(
                    `Timeout của bạn đã được hủy trong ${interaction.guild.name}`,
                    `**Lý do:** ${reason}\n**Bởi:** ${interaction.user.tag}\n**Thời gian:** <t:${Math.floor(Date.now() / 1000)}:F>`
                );
                await user.send({ embeds: [dmEmbed] });
            } catch (error) {
                console.log('Không thể gửi DM cho user được untimeout');
            }
            
            const successEmbed = createSuccessEmbed(
                '🔓 Timeout đã được hủy!',
                `**Thành viên:** ${user.tag} (${user.id})\n` +
                `**Timeout cũ hết hạn:** <t:${Math.floor(oldTimeoutEnd / 1000)}:F>\n` +
                `**Lý do:** ${reason}\n` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            await interaction.reply({ embeds: [successEmbed] });
            
            // Gửi log vào channel
            await sendModerationLog(client, guildId, {
                type: 'untimeout',
                user: user,
                moderator: interaction.user,
                reason: reason
            });
            
        } catch (error) {
            console.error('Lỗi khi untimeout user:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi hủy timeout!',
                'Đã xảy ra lỗi khi hủy timeout. Vui lòng thử lại sau!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
