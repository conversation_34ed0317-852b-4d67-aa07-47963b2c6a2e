(function(_0x365fe0,_0x3df7cc){const _0x128d4f=_0x365fe0();function _0x16731f(_0x4bdbfd,_0x111488){return _0xaece(_0x4bdbfd-0x1d9,_0x111488);}while(!![]){try{const _0x348816=parseInt(_0x16731f(0x28e,0x298))/0x1+-parseInt(_0x16731f(0x281,0x26e))/0x2*(-parseInt(_0x16731f(0x260,0x26e))/0x3)+parseInt(_0x16731f(0x27f,0x260))/0x4*(parseInt(_0x16731f(0x24a,0x24b))/0x5)+-parseInt(_0x16731f(0x290,0x2ad))/0x6*(-parseInt(_0x16731f(0x286,0x26f))/0x7)+parseInt(_0x16731f(0x252,0x230))/0x8*(parseInt(_0x16731f(0x282,0x283))/0x9)+-parseInt(_0x16731f(0x24d,0x254))/0xa*(-parseInt(_0x16731f(0x270,0x283))/0xb)+parseInt(_0x16731f(0x256,0x26e))/0xc*(-parseInt(_0x16731f(0x267,0x26d))/0xd);if(_0x348816===_0x3df7cc)break;else _0x128d4f['push'](_0x128d4f['shift']());}catch(_0x422698){_0x128d4f['push'](_0x128d4f['shift']());}}}(_0x42d9,0xd8016));const {SlashCommandBuilder,EmbedBuilder}=require(_0x3d6760(-0x8a,-0x85)),moment=require('moment'),os=require('os'),process=require('process');function _0x3d6760(_0x303d5d,_0x46bab0){return _0xaece(_0x303d5d- -0x127,_0x46bab0);}function _0x42d9(){const _0x5af92d=['\x20MB','2795jyTtQx','Bot\x20ID','Error\x20in\x20botinfo\x20command:\x20','\x20GB','setThumbnail','floor','Sorry,\x20there\x20was\x20an\x20error\x20retrieving\x20the\x20bot\x20information.','CPU\x20Cores','Uptime','121SHKogF','setDescription','Unknown','reply','user','../../models/guildDataSchema','discord.js','findOne','Bot\x20Name','heapTotal','error','round','replace','Node.js\x20Version','Memory\x20Usage','4BjcYLU','createdTimestamp','757686KRHhpl','189TnVnby','Bot\x20Created\x20On','displayAvatarURL','Get\x20detailed\x20information\x20about\x20the\x20bot','385tUdQDs','setName','setColor','humanize','joinedAt','memoryUsage','username','toFixed','283117GVLOGT','heapUsed','74970qVsImO','\x20GB\x20/\x20','createdAt','tag','4585590EmcKMh','botinfo','exports','895110tMgZhI','now','ping','Bot\x20Starts','setTitle','112088Uvsoxb','toString','guild','get','169632dYJBqR','members','duration','\x20MB\x20/\x20','addFields','cpus','Ping','model',':R>','cache','6HmItRD','setAuthor','client','length','<t:','version'];_0x42d9=function(){return _0x5af92d;};return _0x42d9();}function _0xaece(_0x42c78c,_0x47d51f){const _0x42d937=_0x42d9();return _0xaece=function(_0xaece9b,_0x34edd8){_0xaece9b=_0xaece9b-0x6e;let _0x2f20ec=_0x42d937[_0xaece9b];return _0x2f20ec;},_0xaece(_0x42c78c,_0x47d51f);}const {version}=require('../../package.json');module[_0x3d6760(-0xb4,-0x97)]={'data':new SlashCommandBuilder()[_0x3d6760(-0x79,-0x61)](_0x3d6760(-0xb5,-0x94))[_0x3d6760(-0x8f,-0x6f)](_0x3d6760(-0x7b,-0x7d)),'category':'General',async 'execute'(_0x3129be){function _0x4c8c50(_0x12515e,_0x396be8){return _0x3d6760(_0x12515e- -0x9c,_0x396be8);}try{const _0x440dad=_0x3129be[_0x4c8c50(-0x13a,-0x125)],_0x5789c3=moment[_0x4c8c50(-0x144,-0x168)](_0x440dad['uptime'])[_0x4c8c50(-0x113,-0x100)](),_0x30e425=process[_0x4c8c50(-0x111,-0xf6)](),_0x2873fc=(_0x30e425[_0x4c8c50(-0x123,-0x134)]/0x400/0x400)[_0x4c8c50(-0x10f,-0xf7)](0x2),_0x22872b=(_0x30e425[_0x4c8c50(-0x10d,-0x116)]/0x400/0x400)[_0x4c8c50(-0x10f,-0x104)](0x2),_0x4d2ce6=(_0x100c1d,_0x326c62)=>{function _0xe2323c(_0x591f28,_0x1b0cd6){return _0x4c8c50(_0x591f28- -0xa,_0x1b0cd6);}if(_0x100c1d>0x1800)return(_0x326c62/0x400)[_0xe2323c(-0x119,-0x11a)](0x2)+_0xe2323c(-0x15f,-0x144)+(_0x100c1d/0x400)[_0xe2323c(-0x119,-0x131)](0x2)+_0xe2323c(-0x13c,-0x11e);return _0x326c62+_0xe2323c(-0x14d,-0x137)+_0x100c1d+_0xe2323c(-0x140,-0x13d);};let _0x543281=os[_0x4c8c50(-0x141,-0x159)]()[0x0][_0x4c8c50(-0x13f,-0x152)];const _0x2ca2e1=os['cpus']()[_0x4c8c50(-0x139,-0x15e)],_0x4ec94f=process[_0x4c8c50(-0x137,-0x121)],_0xe31382='<t:'+Math['floor'](_0x440dad[_0x4c8c50(-0x128,-0x11f)][_0x4c8c50(-0x154,-0x168)]/0x3e8)+_0x4c8c50(-0x13e,-0x138),_0x715e5b=_0x3129be[_0x4c8c50(-0x148,-0x14d)][_0x4c8c50(-0x145,-0x141)]['cache']['get'](_0x440dad[_0x4c8c50(-0x128,-0x10b)]['id'])?.[_0x4c8c50(-0x112,-0x101)]?_0x4c8c50(-0x138,-0x133)+Math[_0x4c8c50(-0x130,-0x116)](_0x3129be[_0x4c8c50(-0x148,-0x148)][_0x4c8c50(-0x145,-0x13e)][_0x4c8c50(-0x13d,-0x155)][_0x4c8c50(-0x147,-0x167)](_0x440dad[_0x4c8c50(-0x128,-0x136)]['id'])['joinedAt']/0x3e8)+_0x4c8c50(-0x13e,-0x154):_0x4c8c50(-0x12a,-0x110),_0x3a8314=Date[_0x4c8c50(-0x14e,-0x169)]()-_0x3129be[_0x4c8c50(-0x11c,-0xfa)],_0x9e0ce7=Math[_0x4c8c50(-0x121,-0x13c)](_0x440dad['ws'][_0x4c8c50(-0x14d,-0x15f)]);_0x543281=_0x543281[_0x4c8c50(-0x120,-0x137)](/\s\d+-Core Processor/,'');const _0x4452cf=new EmbedBuilder()[_0x4c8c50(-0x13b,-0x159)]({'name':_0x440dad[_0x4c8c50(-0x128,-0x117)][_0x4c8c50(-0x110,-0x11b)],'iconURL':_0x440dad[_0x4c8c50(-0x128,-0x12c)][_0x4c8c50(-0x118,-0x12f)]()})[_0x4c8c50(-0x14b,-0x153)]('Bot\x20Information')[_0x4c8c50(-0x114,-0xf7)]('#0099ff')[_0x4c8c50(-0x131,-0x125)](_0x440dad[_0x4c8c50(-0x128,-0x107)][_0x4c8c50(-0x118,-0x130)]())[_0x4c8c50(-0x142,-0x15e)]({'name':_0x4c8c50(-0x124,-0x11e),'value':_0x440dad['user']['username'],'inline':!![]},{'name':_0x4c8c50(-0x134,-0x130),'value':_0x440dad[_0x4c8c50(-0x128,-0x141)]['id'],'inline':!![]},{'name':'Bot\x20Version','value':version,'inline':!![]},{'name':_0x4c8c50(-0x119,-0xff),'value':_0xe31382,'inline':!![]},{'name':'Bot\x20Joined\x20On','value':_0x715e5b,'inline':!![]},{'name':_0x4c8c50(-0x14c,-0x12c),'value':await getBotStarts(_0x3129be['guild']['id']),'inline':!![]},{'name':_0x4c8c50(-0x12d,-0x126),'value':_0x5789c3,'inline':!![]},{'name':_0x4c8c50(-0x140,-0x12b),'value':_0x3a8314+'ms','inline':!![]},{'name':_0x4c8c50(-0x11f,-0x12e),'value':_0x4ec94f,'inline':!![]},{'name':_0x4c8c50(-0x11e,-0x10d),'value':_0x4d2ce6(_0x2873fc,_0x22872b),'inline':!![]},{'name':'CPU\x20Model','value':_0x543281,'inline':!![]},{'name':_0x4c8c50(-0x12e,-0x110),'value':_0x2ca2e1[_0x4c8c50(-0x149,-0x169)](),'inline':!![]})['setFooter']({'text':'Requested\x20by\x20'+_0x3129be[_0x4c8c50(-0x128,-0x112)][_0x4c8c50(-0x153,-0x132)],'iconURL':_0x3129be[_0x4c8c50(-0x128,-0x104)][_0x4c8c50(-0x118,-0x126)]()})['setTimestamp']();await _0x3129be['reply']({'embeds':[_0x4452cf]});}catch(_0x2254c6){console[_0x4c8c50(-0x122,-0x121)](_0x4c8c50(-0x133,-0x140),_0x2254c6),_0x3129be[_0x4c8c50(-0x129,-0x132)]({'content':_0x4c8c50(-0x12f,-0x126),'ephemeral':!![]});}}};async function getBotStarts(_0xaa0097){const _0x5b0317=require(_0x44a093(-0x2d8,-0x2cf)),_0x3642ea=await _0x5b0317[_0x44a093(-0x2d6,-0x2cf)]({'guildID':_0xaa0097});function _0x44a093(_0x306eed,_0x3f3ee8){return _0x3d6760(_0x306eed- -0x24d,_0x3f3ee8);}return _0x3642ea?_0x3642ea['timesBotStarted']['toString']():'0';}