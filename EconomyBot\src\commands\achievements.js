const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const User = require('../models/User');
const { createErrorEmbed, formatCurrency } = require('../utils/embedBuilder');
const config = require('../config/config');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('achievements')
        .setNameLocalizations({
            'vi': 'thanhtuu'
        })
        .setDescription('Xem thành tựu của bạn hoặc người khác')
        .addUserOption(option =>
            option.setName('user')
                .setNameLocalizations({
                    'vi': 'nguoidung'
                })
                .setDescription('Người dùng cần xem thành tựu')
                .setRequired(false)
        ),
    
    cooldown: 5,

    async execute(interaction) {
        try {
            const targetUser = interaction.options.getUser('user') || interaction.user;
            
            // Đảm bảo user tồn tại trong database
            const userData = await User.findOrCreate(targetUser);
            
            // Lấy thành tựu
            const achievements = await userData.getAchievements();
            
            if (achievements.length === 0) {
                const errorEmbed = createErrorEmbed(
                    'Chưa có thành tựu',
                    'Hệ thống thành tựu đang được cập nhật. Vui lòng thử lại sau!'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Phân loại thành tựu
            const unlockedAchievements = achievements.filter(ach => ach.unlocked);
            const lockedAchievements = achievements.filter(ach => !ach.unlocked);

            const embed = new EmbedBuilder()
                .setColor(config.colors.economy)
                .setTitle(`${config.emojis.trophy} Thành tựu của ${targetUser.username}`)
                .setThumbnail(targetUser.displayAvatarURL({ dynamic: true }))
                .setDescription(
                    `**${unlockedAchievements.length}/${achievements.length}** thành tựu đã mở khóa\n` +
                    `Tiến độ: ${Math.round((unlockedAchievements.length / achievements.length) * 100)}%`
                )
                .setTimestamp()
                .setFooter({ text: 'Economy Bot' });

            // Thêm thành tựu đã mở khóa
            if (unlockedAchievements.length > 0) {
                const unlockedText = unlockedAchievements.map(ach => {
                    const unlockedDate = new Date(ach.unlocked_at);
                    const timestamp = Math.floor(unlockedDate.getTime() / 1000);
                    return `${ach.emoji} **${ach.name}**\n${ach.description}\n*Mở khóa: <t:${timestamp}:R>*`;
                }).join('\n\n');

                // Chia nhỏ nếu quá dài
                if (unlockedText.length > 1024) {
                    const firstPart = unlockedAchievements.slice(0, 3).map(ach => {
                        const unlockedDate = new Date(ach.unlocked_at);
                        const timestamp = Math.floor(unlockedDate.getTime() / 1000);
                        return `${ach.emoji} **${ach.name}**\n${ach.description}\n*<t:${timestamp}:R>*`;
                    }).join('\n\n');

                    embed.addFields({
                        name: `✅ Đã mở khóa (${unlockedAchievements.length})`,
                        value: firstPart + (unlockedAchievements.length > 3 ? `\n\n*... và ${unlockedAchievements.length - 3} thành tựu khác*` : ''),
                        inline: false
                    });
                } else {
                    embed.addFields({
                        name: `✅ Đã mở khóa (${unlockedAchievements.length})`,
                        value: unlockedText,
                        inline: false
                    });
                }
            }

            // Thêm một số thành tựu chưa mở khóa
            if (lockedAchievements.length > 0) {
                const lockedText = lockedAchievements.slice(0, 3).map(ach => {
                    return `🔒 **${ach.name}**\n${ach.description}\n*Phần thưởng: ${formatCurrency(ach.reward)}*`;
                }).join('\n\n');

                embed.addFields({
                    name: `🔒 Chưa mở khóa (${lockedAchievements.length})`,
                    value: lockedText + (lockedAchievements.length > 3 ? `\n\n*... và ${lockedAchievements.length - 3} thành tựu khác*` : ''),
                    inline: false
                });
            }

            // Thêm thống kê tổng quan
            const totalRewards = unlockedAchievements.reduce((sum, ach) => sum + ach.reward, 0);
            embed.addFields({
                name: `${config.emojis.star} Thống kê`,
                value: `**Tổng phần thưởng nhận được:** ${formatCurrency(totalRewards)}\n` +
                       `**Thành tựu gần nhất:** ${unlockedAchievements.length > 0 ? unlockedAchievements[0].name : 'Chưa có'}\n` +
                       `**Tiến độ hoàn thành:** ${Math.round((unlockedAchievements.length / achievements.length) * 100)}%`,
                inline: false
            });

            // Thêm hướng dẫn
            if (targetUser.id === interaction.user.id && lockedAchievements.length > 0) {
                embed.addFields({
                    name: `${config.emojis.info} Mẹo`,
                    value: '• Sử dụng các lệnh kinh tế để mở khóa thành tựu\n' +
                           '• Thành tựu sẽ tự động mở khóa khi đạt điều kiện\n' +
                           '• Mỗi thành tựu có phần thưởng riêng',
                    inline: false
                });
            }

            await interaction.reply({ embeds: [embed] });
            
        } catch (error) {
            console.error('Lỗi lệnh achievements:', error);
            
            const errorEmbed = createErrorEmbed(
                'Lỗi thành tựu',
                'Không thể hiển thị thành tựu. Vui lòng thử lại sau.'
            );
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
