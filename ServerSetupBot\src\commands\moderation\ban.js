const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createWarningEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('ban')
        .setDescription('Ban thành viên khỏi server')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Thành viên cần ban')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Lý do ban')
                .setRequired(false)
        )
        .addIntegerOption(option =>
            option.setName('delete_days')
                .setDescription('Xóa tin nhắn trong X ngày (0-7)')
                .setRequired(false)
                .setMinValue(0)
                .setMaxValue(7)
        )
        .addBooleanOption(option =>
            option.setName('silent')
                .setDescription('Không gửi DM cho người bị ban')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.BanMembers)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                'Bạn cần quyền `Ban Members` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.BanMembers)) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                'Bot cần quyền `Ban Members` để thực hiện lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const user = interaction.options.getUser('user');
        const reason = interaction.options.getString('reason') || 'Không có lý do';
        const deleteDays = interaction.options.getInteger('delete_days') || 0;
        const silent = interaction.options.getBoolean('silent') || false;
        const guildId = interaction.guild.id;
        
        try {
            const member = await interaction.guild.members.fetch(user.id).catch(() => null);
            
            // Kiểm tra hierarchy
            if (member) {
                if (member.roles.highest.position >= interaction.member.roles.highest.position) {
                    const errorEmbed = createErrorEmbed(
                        'Không thể ban!',
                        'Bạn không thể ban thành viên có role cao hơn hoặc bằng bạn.'
                    );
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
                
                if (member.roles.highest.position >= interaction.guild.members.me.roles.highest.position) {
                    const errorEmbed = createErrorEmbed(
                        'Không thể ban!',
                        'Bot không thể ban thành viên có role cao hơn hoặc bằng bot.'
                    );
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
                
                if (!member.bannable) {
                    const errorEmbed = createErrorEmbed(
                        'Không thể ban!',
                        'Thành viên này không thể bị ban.'
                    );
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
            }
            
            // Gửi DM trước khi ban
            if (!silent && member) {
                const dmEmbed = createWarningEmbed(
                    `Bạn đã bị ban khỏi ${interaction.guild.name}`,
                    `**Lý do:** ${reason}\n**Bởi:** ${interaction.user.tag}\n**Thời gian:** <t:${Math.floor(Date.now() / 1000)}:F>`
                );
                
                try {
                    await user.send({ embeds: [dmEmbed] });
                } catch (error) {
                    console.log('Không thể gửi DM cho user bị ban');
                }
            }
            
            // Thực hiện ban
            await interaction.guild.bans.create(user.id, {
                reason: `${reason} | Bởi: ${interaction.user.tag}`,
                deleteMessageDays: deleteDays
            });
            
            // Log vào database
            await client.db.addModerationLog(guildId, {
                type: 'ban',
                userId: user.id,
                moderatorId: interaction.user.id,
                reason: reason,
                duration: null,
                deleteDays: deleteDays
            });
            
            const successEmbed = createSuccessEmbed(
                '🔨 Thành viên đã bị ban!',
                `**Thành viên:** ${user.tag} (${user.id})\n` +
                `**Lý do:** ${reason}\n` +
                `**Xóa tin nhắn:** ${deleteDays} ngày\n` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            await interaction.reply({ embeds: [successEmbed] });
            
            // Gửi log vào channel
            await sendModerationLog(client, guildId, {
                type: 'ban',
                user: user,
                moderator: interaction.user,
                reason: reason,
                deleteDays: deleteDays
            });
            
        } catch (error) {
            console.error('Lỗi khi ban user:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi ban thành viên!',
                'Đã xảy ra lỗi khi ban thành viên. Vui lòng thử lại sau!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
