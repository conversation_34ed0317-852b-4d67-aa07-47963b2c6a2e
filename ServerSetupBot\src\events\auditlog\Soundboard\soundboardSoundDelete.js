const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'soundboardSoundDelete', // Custom event name
    async execute(sound, client) {
        try {
            console.log(`🗑️ Soundboard sound deleted: ${sound.name} in ${sound.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(sound.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'SOUNDBOARD_SOUND_DELETE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'SOUNDBOARD_SOUND_DELETE',
                user: 'System',
                userId: null,
                action: 'Âm thanh soundboard được xóa',
                details: `Âm thanh soundboard **${sound.name}** đã được xóa`,
                target: sound.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the sound from audit logs
            try {
                const auditLogs = await sound.guild.fetchAuditLogs({
                    type: 132, // SOUNDBOARD_SOUND_DELETE (hypothetical)
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === sound.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Âm thanh soundboard được xóa bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for soundboard delete');
            }
            
            // Add to database
            await client.db.addAuditLog(sound.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Âm thanh soundboard được xóa',
                `Vừa có âm thanh soundboard được xóa khỏi server`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            embed.addFields([
                {
                    name: '> Tên âm thanh',
                    value: `- ${sound.name}`,
                    inline: true
                },
                {
                    name: '> ID âm thanh',
                    value: `- ${sound.id}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                }
            ]);
            
            // Add sound properties that were set
            if (sound.volume !== undefined) {
                embed.addFields({
                    name: '> Âm lượng đã thiết lập',
                    value: `- ${Math.round(sound.volume * 100)}%`,
                    inline: true
                });
            }
            
            if (sound.emoji) {
                embed.addFields({
                    name: '> Emoji đã thiết lập',
                    value: `- ${sound.emoji}`,
                    inline: true
                });
            }
            
            embed.addFields({
                name: '> Thời gian xóa',
                value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                inline: true
            });
            
            // Add creation info if available
            if (sound.createdTimestamp) {
                embed.addFields({
                    name: '> Được tạo lúc',
                    value: `- <t:${Math.floor(sound.createdTimestamp / 1000)}:F>`,
                    inline: true
                });
                
                const lifetime = Date.now() - sound.createdTimestamp;
                const lifetimeDays = Math.floor(lifetime / (1000 * 60 * 60 * 24));
                embed.addFields({
                    name: '> Thời gian tồn tại',
                    value: `- ${lifetimeDays} ngày`,
                    inline: true
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do xóa',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${sound.guild.name} • Soundboard Delete`,
                iconURL: sound.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Soundboard delete logged: ${sound.name}`);
            
        } catch (error) {
            console.error('Error in soundboardSoundDelete audit log:', error);
        }
    }
};
