const { EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const filePath = path.join(__dirname, '../../data/welcomeConfig.json');

module.exports = {
    name: 'guildMemberAdd',
    async execute(member) {
        if (!fs.existsSync(filePath)) return;

        const config = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        const guildId = member.guild.id;
        const data = config[guildId];
        if (!data || !data.channel) return;

        const channel = member.guild.channels.cache.get(data.channel);
        if (!channel || !channel.send) return;

        const embedData = data.embed || {};
        const embed = new EmbedBuilder()
            .setColor(embedData.color || '#2b2d31')
            .setTitle(replaceVars(embedData.title, member) || 'Welcome!')
            .setDescription(replaceVars(embedData.description, member) || `Welcome to **${member.guild.name}**!`)
            .setImage(embedData.image || null)
            .setFooter(embedData.footer ? { text: replaceVars(embedData.footer, member) } : null);

        channel.send({ embeds: [embed] }).catch(() => {});
    }
};

function replaceVars(text, member) {
    if (!text) return null;

    return text
        .replace(/{user}/gi, `<@${member.id}>`)
        .replace(/{server}/gi, member.guild.name)
        .replace(/{memberCount}/gi, member.guild.memberCount.toString());
}