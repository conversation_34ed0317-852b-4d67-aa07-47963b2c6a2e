const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Check if new permission overwrites were added
            const oldOverwrites = oldChannel.permissionOverwrites.cache;
            const newOverwrites = newChannel.permissionOverwrites.cache;
            
            const addedOverwrites = newOverwrites.filter(overwrite => !oldOverwrites.has(overwrite.id));
            
            if (addedOverwrites.size === 0) return;
            
            console.log(`🔐 Channel permission overwrite created: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_OVERWRITE_CREATE')) return;
            
            // Process each added overwrite
            for (const overwrite of addedOverwrites.values()) {
                // Create event data
                const eventData = {
                    eventType: 'CHANNEL_OVERWRITE_CREATE',
                    user: 'System',
                    userId: null,
                    action: 'Permission overwrite được tạo',
                    details: `Permission overwrite mới được tạo cho kênh **${newChannel.name}**`,
                    target: newChannel.name,
                    channel: newChannel.name,
                    timestamp: new Date().toISOString()
                };
                
                // Try to get who created the overwrite from audit logs
                try {
                    const auditLogs = await newChannel.guild.fetchAuditLogs({
                        type: 13, // CHANNEL_OVERWRITE_CREATE
                        limit: 1
                    });
                    
                    const auditEntry = auditLogs.entries.first();
                    if (auditEntry && auditEntry.target?.id === newChannel.id) {
                        eventData.user = auditEntry.executor.tag;
                        eventData.userId = auditEntry.executor.id;
                        eventData.action = `Permission overwrite được tạo bởi ${auditEntry.executor.tag}`;
                        if (auditEntry.reason) {
                            eventData.reason = auditEntry.reason;
                        }
                    }
                } catch (error) {
                    console.log('Could not fetch audit logs for channel overwrite create');
                }
                
                // Add to database
                await client.db.addAuditLog(newChannel.guild.id, eventData);
                
                // Send to audit log channel
                const auditChannel = await client.channels.fetch(config.channelId);
                if (!auditChannel) continue;
                
                // Create embed
                const embed = createInfoEmbed(
                    '🔐 Permission overwrite được tạo',
                    `Vừa có permission overwrite mới được tạo cho kênh`
                );
                
                embed.setColor(0x2ecc71); // Green for create
                
                // Get target info (role or member)
                let targetInfo = 'Unknown';
                let targetType = 'Unknown';
                
                if (overwrite.type === 0) { // Role
                    const role = newChannel.guild.roles.cache.get(overwrite.id);
                    targetInfo = role ? role.name : `Unknown Role (${overwrite.id})`;
                    targetType = 'Role';
                } else if (overwrite.type === 1) { // Member
                    const member = newChannel.guild.members.cache.get(overwrite.id);
                    targetInfo = member ? member.user.tag : `Unknown Member (${overwrite.id})`;
                    targetType = 'Member';
                }
                
                embed.addFields([
                    {
                        name: '> Kênh',
                        value: `- ${newChannel}`,
                        inline: true
                    },
                    {
                        name: '> ID kênh',
                        value: `- ${newChannel.id}`,
                        inline: true
                    },
                    {
                        name: '> Người tạo',
                        value: `- ${eventData.user}`,
                        inline: true
                    },
                    {
                        name: '> Đối tượng',
                        value: `- ${targetInfo}`,
                        inline: true
                    },
                    {
                        name: '> Loại đối tượng',
                        value: `- ${targetType}`,
                        inline: true
                    },
                    {
                        name: '> Thời gian',
                        value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                        inline: true
                    }
                ]);
                
                // Add permissions info
                const allowedPerms = [];
                const deniedPerms = [];
                
                overwrite.allow.toArray().forEach(perm => allowedPerms.push(perm));
                overwrite.deny.toArray().forEach(perm => deniedPerms.push(perm));
                
                if (allowedPerms.length > 0) {
                    embed.addFields({
                        name: '> ✅ Permissions được cho phép',
                        value: allowedPerms.map(perm => `• ${perm}`).join('\n'),
                        inline: false
                    });
                }
                
                if (deniedPerms.length > 0) {
                    embed.addFields({
                        name: '> ❌ Permissions bị từ chối',
                        value: deniedPerms.map(perm => `• ${perm}`).join('\n'),
                        inline: false
                    });
                }
                
                if (eventData.reason) {
                    embed.addFields({
                        name: '> Lý do',
                        value: `- ${eventData.reason}`,
                        inline: false
                    });
                }
                
                embed.setTimestamp();
                embed.setFooter({
                    text: `${newChannel.guild.name} • Channel Overwrite Create`,
                    iconURL: newChannel.guild.iconURL()
                });
                
                await auditChannel.send({ embeds: [embed] });
                console.log(`✅ Channel overwrite create logged: ${newChannel.name}`);
            }
            
        } catch (error) {
            console.error('Error in channelOverwriteCreate audit log:', error);
        }
    }
};
