const Event = require("../../structures/Events");
const chalk = require("chalk");
const { ActivityType } = require("discord.js");

module.exports = class Ready extends Event {
	constructor(client) {
	  super(client, "ready");
		this.client = client;
	}

	async run() {
    const config = this.client.config;
		let error = false;
		let foundErrors = [];
		let foundWarn = [];

		//== Check NodeJS Version ==//
		let nodeVersion = process.version.replace("v", "");
		if(nodeVersion.split(".")[1].length == 1) nodeVersion = nodeVersion.split(".")[0] + ".0" + nodeVersion.split(".")[1];
		
		if(nodeVersion < "16.09" && !process.version.replace("v", "").includes("16.9")) {
		  error = true;
			this.client.utils.sendError("Detected NodeJS Version (" + process.version + ") but expected v16.6+. Please Upgrade.");
			foundErrors.push("UnSupported NodeJS Version");
		}

    if(config.status.change_random == true) {
			const rand = Math.floor(Math.random() * config.status.messages.length);
      
			this.client.user.setActivity(config.status.messages[rand].replace("<members>", this.client.users.cache.size)
				.replace("<version>", this.client.config.version)
			  .replace("<channels>", this.client.channels.cache.size), { type: ActivityType[config.status.type] });
			
			setInterval(() => {
				const index = Math.floor(Math.random() * config.status.messages.length);
					this.client.user.setActivity(config.status.messages[index].replace("<members>", this.client.users.cache.size)
						.replace("<version>", this.client.config.version)
			      .replace("<channels>", this.client.channels.cache.size), { type: ActivityType[config.status.type] });
			}, config.status.interval * 1000);
		} else {
			this.client.user.setActivity(this.client.config.status.message.replace("<members>", this.client.users.cache.size)
				.replace("<version>", this.client.config.version)
			  .replace("<channels>", this.client.channels.cache.size), { type: ActivityType[config.status.type] });
		} 

    if(this.client.config.general.slash == true) {
			try {
				let oldCommands = await this.client.application.commands.fetch();
				oldCommands = Array.from(oldCommands).map((x) => {
					return {
						name: x[1].name,
						description: x[1].description,
						options: x[1].options ?? [],
					}
				}).sort((a, b) => b.name - a.name);

				const newCommands = JSON.stringify(this.client.slashArray.map((x) => {
					return {
						name: x.name,
						description: x.description,
						options: x.options ?? [],
					}
				}).sort((a, b) => b.name - a.name));

				if(oldCommands != newCommands) await this.client.application.commands.set([]);
			} catch(e) {
				error = true;
				this.client.utils.sendError("Bot haven't been invited with applications.commands scope. Please ReInvite Bot with Required Scope(s).");
				foundErrors.push("Invalid Scopes");
				console.log(e)
			}
    } else {
			try {
				await this.client.application.commands.set([]);
			} catch(e) {
				error = true;
				this.client.utils.sendError("Bot haven't been invited with applications.commands scope. Please ReInvite Bot with Required Scope(s).");
				foundErrors.push("Invalid Scopes");
				console.log(e)
			}
    }

		if(error || foundErrors.length > 0) {
			console.log("");
			console.log(chalk.gray("▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃"));
			console.log("")
			console.log(chalk.red.bold(`${config.general.name.toUpperCase()} ${config.version.toUpperCase()}`));
			console.log("");
			console.log(chalk.white(`There was an error while starting bot, please look above for detailed error.`));
			console.log(chalk.white(`Bot should be online if it's not an important error.`));
			console.log("");
			console.log(chalk.red(`Startup Errors (${foundErrors.length}): `) + chalk.white(foundErrors.join(", ").trim() + "."));
			console.log("")
			console.log(chalk.gray("▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃"));
			console.log(" ");
		} else {
			let warns = chalk.keyword("orange")(`Startup Warnings (${foundWarn.length}): `) + chalk.white(foundWarn.join(", ").trim() + ".");
			
			console.log("");
			console.log(chalk.gray("▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃"));
			console.log("")
			console.log(chalk.blue.bold(`${config.general.name.toUpperCase()} ${config.version.toUpperCase()}`));
			console.log("");
			console.log(chalk.white(`Thank you for your purchase, bot has started and is online now!`));
			console.log("")
			console.log(foundWarn.length > 0 ? warns : "No Warnings or Errors on startup, good job!")
			console.log("")
			console.log(chalk.gray("▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃▃"));
			console.log(" ");
		}
	}
};
