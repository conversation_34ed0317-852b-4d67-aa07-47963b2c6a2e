const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('unban')
        .setDescription('Unban thành viên')
        .addStringOption(option =>
            option.setName('user_id')
                .setDescription('ID của thành viên cần unban')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Lý do unban')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.BanMembers)) {
            const errorEmbed = createErrorEmbed(
                '<PERSON>ạn không có quyền!',
                '<PERSON><PERSON>n cần quyền `Ban Members` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.BanMembers)) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                'Bot cần quyền `Ban Members` để thực hiện lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const userId = interaction.options.getString('user_id');
        const reason = interaction.options.getString('reason') || 'Không có lý do';
        const guildId = interaction.guild.id;
        
        // Validate user ID
        if (!/^\d{17,19}$/.test(userId)) {
            const errorEmbed = createErrorEmbed(
                'ID không hợp lệ!',
                'Vui lòng cung cấp ID Discord hợp lệ (17-19 chữ số).'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        try {
            // Kiểm tra xem user có bị ban không
            const ban = await interaction.guild.bans.fetch(userId).catch(() => null);
            if (!ban) {
                const errorEmbed = createErrorEmbed(
                    'User không bị ban!',
                    'User này không có trong danh sách ban của server.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Thực hiện unban
            await interaction.guild.bans.remove(userId, `${reason} | Bởi: ${interaction.user.tag}`);
            
            // Log vào database
            await client.db.addModerationLog(guildId, {
                type: 'unban',
                userId: userId,
                moderatorId: interaction.user.id,
                reason: reason,
                duration: null
            });
            
            const successEmbed = createSuccessEmbed(
                '✅ Thành viên đã được unban!',
                `**Thành viên:** ${ban.user.tag} (${userId})\n` +
                `**Lý do:** ${reason}\n` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            await interaction.reply({ embeds: [successEmbed] });
            
            // Gửi log vào channel
            await sendModerationLog(client, guildId, {
                type: 'unban',
                user: ban.user,
                moderator: interaction.user,
                reason: reason
            });
            
        } catch (error) {
            console.error('Lỗi khi unban user:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi unban thành viên!',
                'Đã xảy ra lỗi khi unban thành viên. Vui lòng kiểm tra ID và thử lại!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
