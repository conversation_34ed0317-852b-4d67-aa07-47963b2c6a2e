const { SlashCommandBuilder, ChannelType } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { canManageChannel } = require('../../utils/permissions.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('category')
        .setDescription('Quản lý danh mục kênh')
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Tạo danh mục kênh mới')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('Tên danh mục')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('Xóa danh mục kênh')
                .addChannelOption(option =>
                    option.setName('category')
                        .setDescription('<PERSON><PERSON> mục cần xóa')
                        .setRequired(true)
                        .addChannelTypes(ChannelType.GuildCategory)
                )
                .addBooleanOption(option =>
                    option.setName('delete_channels')
                        .setDescription('Xóa tất cả kênh trong danh mục (mặc định: false)')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('Xem danh sách tất cả danh mục')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('info')
                .setDescription('Xem thông tin chi tiết danh mục')
                .addChannelOption(option =>
                    option.setName('category')
                        .setDescription('Danh mục cần xem thông tin')
                        .setRequired(true)
                        .addChannelTypes(ChannelType.GuildCategory)
                )
        ),
    category: 'channels',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();
        
        // Kiểm tra quyền bot
        const canManage = canManageChannel(interaction.guild);
        if (!canManage.canManage) {
            const errorEmbed = createErrorEmbed(
                'Bot không có quyền!',
                canManage.reason
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        switch (subcommand) {
            case 'create':
                await handleCreate(interaction, client);
                break;
            case 'delete':
                await handleDelete(interaction, client);
                break;
            case 'list':
                await handleList(interaction, client);
                break;
            case 'info':
                await handleInfo(interaction, client);
                break;
        }
    },
};

async function handleCreate(interaction, client) {
    const name = interaction.options.getString('name');
    
    // Kiểm tra danh mục đã tồn tại chưa
    const existingCategory = interaction.guild.channels.cache.find(ch => 
        ch.type === ChannelType.GuildCategory && ch.name === name
    );
    
    if (existingCategory) {
        const errorEmbed = createErrorEmbed(
            'Danh mục đã tồn tại!',
            `Danh mục **${name}** đã tồn tại trong server.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    try {
        const newCategory = await interaction.guild.channels.create({
            name: name,
            type: ChannelType.GuildCategory,
            reason: `Danh mục được tạo bởi ${interaction.user.tag}`
        });
        
        const successEmbed = createSuccessEmbed(
            'Đã tạo danh mục thành công!',
            `**Danh mục:** ${newCategory.name}\n` +
            `**ID:** ${newCategory.id}\n` +
            `**Vị trí:** ${newCategory.position}`
        );
        
        successEmbed.addFields({
            name: '📝 Bước tiếp theo',
            value: 'Bạn có thể:\n' +
                   '• Tạo kênh trong danh mục này bằng `/channel create`\n' +
                   '• Di chuyển kênh hiện có vào danh mục\n' +
                   '• Thiết lập quyền cho danh mục',
            inline: false
        });
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi tạo category:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi tạo danh mục!',
            'Không thể tạo danh mục. Vui lòng kiểm tra quyền bot và thử lại!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleDelete(interaction, client) {
    const category = interaction.options.getChannel('category');
    const deleteChannels = interaction.options.getBoolean('delete_channels') || false;
    
    // Lấy danh sách kênh trong category
    const channelsInCategory = interaction.guild.channels.cache.filter(ch => 
        ch.parentId === category.id
    );
    
    if (channelsInCategory.size > 0 && !deleteChannels) {
        const errorEmbed = createErrorEmbed(
            'Danh mục không trống!',
            `Danh mục **${category.name}** có ${channelsInCategory.size} kênh.\n\n` +
            `**Tùy chọn:**\n` +
            `• Sử dụng \`delete_channels:true\` để xóa tất cả kênh\n` +
            `• Di chuyển kênh ra khỏi danh mục trước khi xóa`
        );
        
        const channelList = channelsInCategory.map(ch => `• ${ch.name}`).slice(0, 10).join('\n');
        if (channelList) {
            errorEmbed.addFields({
                name: '📝 Kênh trong danh mục',
                value: channelList + (channelsInCategory.size > 10 ? '\n...' : ''),
                inline: false
            });
        }
        
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    try {
        const categoryName = category.name;
        const channelCount = channelsInCategory.size;
        
        // Xóa tất cả kênh trong category nếu được yêu cầu
        if (deleteChannels && channelsInCategory.size > 0) {
            for (const channel of channelsInCategory.values()) {
                await channel.delete(`Xóa cùng với danh mục bởi ${interaction.user.tag}`);
            }
        }
        
        // Xóa category
        await category.delete(`Danh mục được xóa bởi ${interaction.user.tag}`);
        
        const successEmbed = createSuccessEmbed(
            'Đã xóa danh mục thành công!',
            `**Danh mục:** ${categoryName}\n` +
            `**Kênh đã xóa:** ${deleteChannels ? channelCount : 0}\n` +
            `**Kênh di chuyển:** ${deleteChannels ? 0 : channelCount}`
        );
        
        if (!deleteChannels && channelCount > 0) {
            successEmbed.addFields({
                name: '📝 Lưu ý',
                value: `${channelCount} kênh đã được di chuyển ra khỏi danh mục và vẫn tồn tại.`,
                inline: false
            });
        }
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi xóa category:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi xóa danh mục!',
            'Không thể xóa danh mục. Vui lòng kiểm tra quyền bot và thử lại!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleList(interaction, client) {
    const categories = interaction.guild.channels.cache.filter(ch => 
        ch.type === ChannelType.GuildCategory
    ).sort((a, b) => a.position - b.position);
    
    if (categories.size === 0) {
        const infoEmbed = createInfoEmbed(
            'Chưa có danh mục nào',
            'Server chưa có danh mục kênh nào.\n\nSử dụng `/category create` để tạo danh mục đầu tiên!'
        );
        return await interaction.reply({ embeds: [infoEmbed] });
    }
    
    const embed = createInfoEmbed(
        `📁 Danh sách Danh mục (${categories.size})`,
        `Server có ${categories.size} danh mục kênh:`
    );
    
    let categoryList = '';
    for (const category of categories.values()) {
        const channelsInCategory = interaction.guild.channels.cache.filter(ch => 
            ch.parentId === category.id
        );
        
        categoryList += `**${category.name}** (${channelsInCategory.size} kênh)\n`;
        categoryList += `└ ID: \`${category.id}\` • Vị trí: ${category.position}\n\n`;
    }
    
    // Chia nhỏ nếu quá dài
    if (categoryList.length > 4000) {
        categoryList = categoryList.substring(0, 3900) + '\n...\n\n*Danh sách quá dài, chỉ hiển thị một phần*';
    }
    
    embed.addFields({
        name: '📋 Danh mục',
        value: categoryList || 'Không có danh mục nào',
        inline: false
    });
    
    embed.addFields({
        name: '🔧 Quản lý',
        value: '• `/category create` - Tạo danh mục mới\n' +
               '• `/category delete` - Xóa danh mục\n' +
               '• `/category info` - Xem chi tiết danh mục',
        inline: false
    });
    
    await interaction.reply({ embeds: [embed] });
}

async function handleInfo(interaction, client) {
    const category = interaction.options.getChannel('category');
    
    // Lấy danh sách kênh trong category
    const channelsInCategory = interaction.guild.channels.cache.filter(ch => 
        ch.parentId === category.id
    );
    
    const textChannels = channelsInCategory.filter(ch => ch.type === ChannelType.GuildText);
    const voiceChannels = channelsInCategory.filter(ch => ch.type === ChannelType.GuildVoice);
    
    const embed = createInfoEmbed(
        `📁 Thông tin Danh mục: ${category.name}`,
        `Chi tiết về danh mục **${category.name}**:`
    );
    
    embed.addFields(
        { name: '📛 Tên', value: category.name, inline: true },
        { name: '🆔 ID', value: category.id, inline: true },
        { name: '📍 Vị trí', value: category.position.toString(), inline: true },
        { name: '📝 Tổng kênh', value: channelsInCategory.size.toString(), inline: true },
        { name: '💬 Kênh text', value: textChannels.size.toString(), inline: true },
        { name: '🔊 Kênh voice', value: voiceChannels.size.toString(), inline: true },
        { name: '📅 Tạo lúc', value: category.createdAt.toLocaleString('vi-VN'), inline: false }
    );
    
    // Hiển thị danh sách kênh nếu có
    if (channelsInCategory.size > 0) {
        let channelList = '';
        
        if (textChannels.size > 0) {
            channelList += '**💬 Text Channels:**\n';
            channelList += textChannels.map(ch => `• ${ch.name}`).slice(0, 10).join('\n');
            if (textChannels.size > 10) channelList += '\n...';
            channelList += '\n\n';
        }
        
        if (voiceChannels.size > 0) {
            channelList += '**🔊 Voice Channels:**\n';
            channelList += voiceChannels.map(ch => `• ${ch.name}`).slice(0, 10).join('\n');
            if (voiceChannels.size > 10) channelList += '\n...';
        }
        
        if (channelList.length > 1024) {
            channelList = channelList.substring(0, 1000) + '\n...';
        }
        
        embed.addFields({
            name: '📋 Kênh trong danh mục',
            value: channelList || 'Không có kênh nào',
            inline: false
        });
    }
    
    await interaction.reply({ embeds: [embed] });
}
