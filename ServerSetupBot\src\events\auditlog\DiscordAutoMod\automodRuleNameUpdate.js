const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.AutoModerationRuleUpdate,
    async execute(oldRule, newRule, client) {
        try {
            // Only handle name changes
            if (oldRule.name === newRule.name) return;
            
            console.log(`✏️ AutoMod rule name updated: ${oldRule.name} -> ${newRule.name} in ${newRule.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newRule.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'AUTOMOD_RULE_NAME_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'AUTOMOD_RULE_NAME_UPDATE',
                user: 'System',
                userId: null,
                action: 'Tê<PERSON> quy tắc AutoMod được cập nhật',
                details: `Tên quy tắc AutoMod đã được thay đổi từ **${oldRule.name}** thành **${newRule.name}**`,
                target: newRule.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the rule name from audit logs
            try {
                const auditLogs = await newRule.guild.fetchAuditLogs({
                    type: 141, // AUTO_MODERATION_RULE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newRule.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Tên quy tắc AutoMod được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for automod rule name update');
            }
            
            // Add to database
            await client.db.addAuditLog(newRule.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Tên quy tắc AutoMod được cập nhật',
                `Vừa có tên quy tắc AutoMod được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get trigger type names
            const triggerTypes = {
                1: 'Keyword',
                2: 'Harmful Link',
                3: 'Spam',
                4: 'Keyword Preset',
                5: 'Mention Spam'
            };
            
            embed.addFields([
                {
                    name: '> ID quy tắc',
                    value: `- ${newRule.id}`,
                    inline: true
                },
                {
                    name: '> Loại trigger',
                    value: `- ${triggerTypes[newRule.triggerType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Tên cũ',
                    value: `- ${oldRule.name}`,
                    inline: true
                },
                {
                    name: '> Tên mới',
                    value: `- ${newRule.name}`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${newRule.enabled ? '✅ Đang hoạt động' : '❌ Đã tắt'}`,
                    inline: true
                }
            ]);
            
            // Add rule configuration
            if (newRule.actions && newRule.actions.length > 0) {
                const actionTypes = {
                    1: 'Chặn tin nhắn',
                    2: 'Gửi cảnh báo',
                    3: 'Timeout người dùng',
                    4: 'Gửi tin nhắn đến kênh'
                };
                
                const actionsList = newRule.actions.map(action => {
                    return actionTypes[action.type] || 'Unknown Action';
                }).join(', ');
                
                embed.addFields({
                    name: '> Hành động được cấu hình',
                    value: `- ${actionsList}`,
                    inline: false
                });
            }
            
            // Add impact note
            embed.addFields({
                name: '> 📝 Tác động',
                value: [
                    '• Tên mới sẽ hiển thị trong danh sách quy tắc',
                    '• Dễ nhận biết mục đích của quy tắc',
                    '• Không ảnh hưởng đến hoạt động của quy tắc',
                    '• Giúp quản lý quy tắc tốt hơn'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newRule.guild.name} • AutoMod Rule Name Update`,
                iconURL: newRule.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ AutoMod rule name update logged: ${oldRule.name} -> ${newRule.name}`);
            
        } catch (error) {
            console.error('Error in automodRuleNameUpdate audit log:', error);
        }
    }
};
