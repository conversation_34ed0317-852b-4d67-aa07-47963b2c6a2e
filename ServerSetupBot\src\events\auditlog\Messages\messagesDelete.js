const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');
const auditLogDB = require('../../../utils/auditLogDatabase.js');

module.exports = {
    name: Events.MessageDelete,
    async execute(message, client) {
        try {
            // Skip if message is from bot or system
            if (!message.author || message.author.bot) return;
            
            console.log(`🗑️ Message deleted in ${message.guild?.name || 'DM'}`);
            
            // Skip DMs
            if (!message.guild) return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(message.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'MESSAGE_DELETE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'MESSAGE_DELETE',
                user: message.author.tag,
                userId: message.author.id,
                action: 'Tin nhắn được xóa',
                details: `Tin nhắn của **${message.author.tag}** đã được xóa trong ${message.channel}`,
                target: message.author.tag,
                channel: message.channel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the message from audit logs
            let deletedBy = message.author.tag;
            let deletedById = message.author.id;
            
            try {
                const auditLogs = await message.guild.fetchAuditLogs({
                    type: 72, // MESSAGE_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.extra?.channel?.id === message.channel.id) {
                    // Check if the audit log entry is recent (within last 5 seconds)
                    const timeDiff = Date.now() - auditEntry.createdTimestamp;
                    if (timeDiff < 5000) {
                        deletedBy = auditEntry.executor.tag;
                        deletedById = auditEntry.executor.id;
                        eventData.action = `Tin nhắn được xóa bởi ${auditEntry.executor.tag}`;
                        eventData.user = auditEntry.executor.tag;
                        eventData.userId = auditEntry.executor.id;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for message delete');
            }
            
            // Add to database
            await client.db.addAuditLog(message.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Tin nhắn được xóa',
                `Vừa có một tin nhắn được xóa trong server`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            // Truncate message content if too long
            let messageContent = message.content || '*Không có nội dung văn bản*';
            if (messageContent.length > 1000) {
                messageContent = messageContent.substring(0, 1000) + '...';
            }
            
            embed.addFields([
                {
                    name: '> Tác giả tin nhắn',
                    value: `- ${message.author.tag} (${message.author})`,
                    inline: false
                },
                {
                    name: '> Kênh',
                    value: `- ${message.channel}`,
                    inline: true
                },
                {
                    name: '> ID tin nhắn',
                    value: `- ${message.id}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${deletedBy}`,
                    inline: true
                },
                {
                    name: '> Nội dung tin nhắn',
                    value: `\`\`\`${messageContent}\`\`\``,
                    inline: false
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add attachment info if any
            if (message.attachments.size > 0) {
                const attachmentList = message.attachments.map(att => `- ${att.name} (${att.size} bytes)`).join('\n');
                embed.addFields({
                    name: '> File đính kèm',
                    value: attachmentList,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${message.guild.name} • Message Delete`,
                iconURL: message.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Message delete logged in ${message.channel.name}`);
            
        } catch (error) {
            console.error('Error in messageDelete audit log:', error);
        }
    }
};
