const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.StageInstanceDelete,
    async execute(stageInstance, client) {
        try {
            console.log(`🏁 Stage ended: ${stageInstance.topic} in ${stageInstance.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(stageInstance.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'STAGE_END')) return;
            
            // Create event data
            const eventData = {
                eventType: 'STAGE_END',
                user: 'System',
                userId: null,
                action: 'Stage được kết thúc',
                details: `Stage **${stageInstance.topic || 'No topic'}** đã được kết thúc`,
                target: stageInstance.topic || 'No topic',
                channel: stageInstance.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who ended the stage from audit logs
            try {
                const auditLogs = await stageInstance.guild.fetchAuditLogs({
                    type: 85, // STAGE_INSTANCE_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === stageInstance.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Stage được kết thúc bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for stage end');
            }
            
            // Add to database
            await client.db.addAuditLog(stageInstance.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🏁 Stage được kết thúc',
                `Vừa có một stage được kết thúc trong server`
            );
            
            embed.setColor(0xe74c3c); // Red for end
            
            embed.addFields([
                {
                    name: '> Chủ đề stage',
                    value: `- ${stageInstance.topic || 'Không có chủ đề'}`,
                    inline: false
                },
                {
                    name: '> ID stage',
                    value: `- ${stageInstance.id}`,
                    inline: true
                },
                {
                    name: '> Kênh stage',
                    value: `- ${stageInstance.channel?.name || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người kết thúc',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian kết thúc',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add privacy level that was set
            const privacyLevels = {
                1: '🌍 Public (Mọi người có thể tham gia)',
                2: '🏠 Guild Only (Chỉ thành viên server)'
            };
            
            embed.addFields({
                name: '> Mức độ riêng tư đã thiết lập',
                value: `- ${privacyLevels[stageInstance.privacyLevel] || 'Unknown'}`,
                inline: true
            });
            
            // Calculate stage duration
            if (stageInstance.createdTimestamp) {
                const duration = Date.now() - stageInstance.createdTimestamp;
                const hours = Math.floor(duration / (1000 * 60 * 60));
                const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
                
                embed.addFields([
                    {
                        name: '> Được bắt đầu lúc',
                        value: `- <t:${Math.floor(stageInstance.createdTimestamp / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Thời gian diễn ra',
                        value: `- ${hours} giờ, ${minutes} phút`,
                        inline: true
                    }
                ]);
            }
            
            // Add channel info
            if (stageInstance.channel) {
                embed.addFields([
                    {
                        name: '> ID kênh',
                        value: `- ${stageInstance.channel.id}`,
                        inline: true
                    },
                    {
                        name: '> Số người còn lại trong kênh',
                        value: `- ${stageInstance.channel.members?.size || 0} thành viên`,
                        inline: true
                    }
                ]);
            }
            
            // Add stage statistics (if available)
            embed.addFields({
                name: '> 📊 Thống kê stage',
                value: [
                    '• Stage đã kết thúc thành công',
                    '• Kênh voice vẫn có thể sử dụng bình thường',
                    '• Thành viên có thể tiếp tục chat voice',
                    '• Có thể tạo stage mới bất cứ lúc nào'
                ].join('\n'),
                inline: false
            });
            
            // Add what happens after stage ends
            embed.addFields({
                name: '> 🔄 Sau khi stage kết thúc',
                value: [
                    '• Kênh trở về chế độ voice chat thông thường',
                    '• Tất cả thành viên có thể speak tự do',
                    '• Không còn hệ thống "Request to Speak"',
                    '• Moderator permissions trở về bình thường'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do kết thúc',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${stageInstance.guild.name} • Stage End`,
                iconURL: stageInstance.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Stage end logged: ${stageInstance.topic}`);
            
        } catch (error) {
            console.error('Error in stageEnd audit log:', error);
        }
    }
};
