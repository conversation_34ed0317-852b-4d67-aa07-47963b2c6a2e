const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.AutoModerationActionExecution,
    async execute(autoModerationActionExecution, client) {
        try {
            console.log(`⚡ AutoMod action executed in ${autoModerationActionExecution.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(autoModerationActionExecution.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'AUTOMOD_ACTION_EXECUTION')) return;
            
            // Create event data
            const eventData = {
                eventType: 'AUTOMOD_ACTION_EXECUTION',
                user: autoModerationActionExecution.user?.tag || 'Unknown',
                userId: autoModerationActionExecution.userId || null,
                action: '<PERSON><PERSON><PERSON> động AutoMod được thực thi',
                details: `AutoMod đã thực thi hành động đối với **${autoModerationActionExecution.user?.tag || 'Unknown'}**`,
                target: autoModerationActionExecution.user?.tag || 'Unknown',
                channel: autoModerationActionExecution.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(autoModerationActionExecution.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '⚡ Hành động AutoMod được thực thi',
                `AutoMod vừa thực thi hành động trong server`
            );
            
            embed.setColor(0xe67e22); // Orange for action execution
            
            // Get action type names
            const actionTypes = {
                1: 'Chặn tin nhắn',
                2: 'Gửi cảnh báo',
                3: 'Timeout người dùng',
                4: 'Gửi tin nhắn đến kênh'
            };
            
            embed.addFields([
                {
                    name: '> Người vi phạm',
                    value: `- ${autoModerationActionExecution.user?.tag || 'Unknown'} (${autoModerationActionExecution.user || 'Unknown'})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${autoModerationActionExecution.userId || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${autoModerationActionExecution.channel || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add rule info
            if (autoModerationActionExecution.ruleTriggerType) {
                const triggerTypes = {
                    1: 'Keyword',
                    2: 'Harmful Link',
                    3: 'Spam',
                    4: 'Keyword Preset',
                    5: 'Mention Spam'
                };
                
                embed.addFields({
                    name: '> Loại quy tắc vi phạm',
                    value: `- ${triggerTypes[autoModerationActionExecution.ruleTriggerType] || 'Unknown'}`,
                    inline: true
                });
            }
            
            if (autoModerationActionExecution.ruleId) {
                embed.addFields({
                    name: '> ID quy tắc',
                    value: `- ${autoModerationActionExecution.ruleId}`,
                    inline: true
                });
            }
            
            // Add action info
            if (autoModerationActionExecution.action) {
                const action = autoModerationActionExecution.action;
                
                embed.addFields({
                    name: '> Hành động thực thi',
                    value: `- ${actionTypes[action.type] || 'Unknown'}`,
                    inline: true
                });
                
                // Add specific action details
                if (action.type === 3 && action.metadata?.durationSeconds) {
                    const duration = action.metadata.durationSeconds;
                    const minutes = Math.floor(duration / 60);
                    const hours = Math.floor(minutes / 60);
                    const days = Math.floor(hours / 24);
                    
                    let durationText = '';
                    if (days > 0) durationText += `${days} ngày `;
                    if (hours % 24 > 0) durationText += `${hours % 24} giờ `;
                    if (minutes % 60 > 0) durationText += `${minutes % 60} phút`;
                    
                    embed.addFields({
                        name: '> Thời gian timeout',
                        value: `- ${durationText.trim()}`,
                        inline: true
                    });
                }
                
                if (action.metadata?.channelId) {
                    const alertChannel = autoModerationActionExecution.guild.channels.cache.get(action.metadata.channelId);
                    embed.addFields({
                        name: '> Kênh cảnh báo',
                        value: `- ${alertChannel || action.metadata.channelId}`,
                        inline: true
                    });
                }
            }
            
            // Add content info if available
            if (autoModerationActionExecution.content) {
                const content = autoModerationActionExecution.content.length > 500 
                    ? autoModerationActionExecution.content.substring(0, 500) + '...'
                    : autoModerationActionExecution.content;
                
                embed.addFields({
                    name: '> Nội dung vi phạm',
                    value: `\`\`\`${content}\`\`\``,
                    inline: false
                });
            }
            
            // Add matched keyword if available
            if (autoModerationActionExecution.matchedKeyword) {
                embed.addFields({
                    name: '> Từ khóa vi phạm',
                    value: `- \`${autoModerationActionExecution.matchedKeyword}\``,
                    inline: true
                });
            }
            
            // Add matched content if available
            if (autoModerationActionExecution.matchedContent) {
                embed.addFields({
                    name: '> Nội dung khớp',
                    value: `- \`${autoModerationActionExecution.matchedContent}\``,
                    inline: true
                });
            }
            
            // Add user info
            if (autoModerationActionExecution.user) {
                const user = autoModerationActionExecution.user;
                const member = autoModerationActionExecution.guild.members.cache.get(user.id);
                
                const accountAge = Date.now() - user.createdTimestamp;
                const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
                
                embed.addFields({
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                });
                
                if (member) {
                    embed.addFields({
                        name: '> Tham gia server lúc',
                        value: `- <t:${Math.floor(member.joinedTimestamp / 1000)}:F>`,
                        inline: true
                    });
                }
            }
            
            // Set user avatar as thumbnail
            if (autoModerationActionExecution.user?.displayAvatarURL()) {
                embed.setThumbnail(autoModerationActionExecution.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${autoModerationActionExecution.guild.name} • AutoMod Action Execution`,
                iconURL: autoModerationActionExecution.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ AutoMod action execution logged`);
            
        } catch (error) {
            console.error('Error in automodActionExecution audit log:', error);
        }
    }
};
