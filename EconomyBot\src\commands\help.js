const { SlashCommandBuilder, EmbedBuilder } = require("discord.js");
const config = require("../config/config");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("help")
    .setNameLocalizations({
      vi: "trogiup",
    })
    .setDescription("Hiển thị hướng dẫn sử dụng bot")
    .addStringOption((option) =>
      option
        .setName("command")
        .setNameLocalizations({
          vi: "lenh",
        })
        .setDescription("Lệnh cần xem hướng dẫn chi tiết")
        .setRequired(false)
        .addChoices(
          { name: "balance - Kiểm tra số dư", value: "balance" },
          { name: "daily - Phần thưởng hàng ngày", value: "daily" },
          { name: "work - Làm việc kiếm tiền", value: "work" },
          { name: "transfer - Chuyển tiền", value: "transfer" },
          { name: "bank - <PERSON><PERSON> hàng", value: "bank" },
          { name: "shop - <PERSON><PERSON><PERSON> hàng", value: "shop" },
          { name: "inventory - Kho đồ", value: "inventory" },
          { name: "leaderboard - Bảng xếp hạng", value: "leaderboard" }
        )
    ),

  cooldown: 3,

  async execute(interaction) {
    try {
      const command = interaction.options.getString("command");

      if (command) {
        await showCommandHelp(interaction, command);
      } else {
        await showGeneralHelp(interaction);
      }
    } catch (error) {
      console.error("Lỗi lệnh help:", error);

      const errorEmbed = new EmbedBuilder()
        .setColor(config.colors.error)
        .setTitle("❌ Lỗi")
        .setDescription("Không thể hiển thị hướng dẫn. Vui lòng thử lại sau.")
        .setTimestamp();

      await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  },
};

async function showGeneralHelp(interaction) {
  const embed = new EmbedBuilder()
    .setColor(config.colors.primary)
    .setTitle(`${config.emojis.info} Hướng dẫn sử dụng Economy Bot`)
    .setDescription(
      "Bot kinh tế với đầy đủ tính năng để quản lý tài chính và mua sắm!"
    )
    .setThumbnail(interaction.client.user.displayAvatarURL())
    .setTimestamp()
    .setFooter({ text: "Sử dụng /help <tên lệnh> để xem chi tiết" });

  // Lệnh cơ bản
  embed.addFields({
    name: `${config.emojis.money} Lệnh cơ bản`,
    value:
      "`/balance` - Kiểm tra số dư tài khoản\n" +
      "`/daily` - Nhận phần thưởng hàng ngày\n" +
      "`/work` - Làm việc để kiếm tiền\n" +
      "`/transfer` - Chuyển tiền cho người khác",
    inline: false,
  });

  // Ngân hàng
  embed.addFields({
    name: `${config.emojis.bank} Ngân hàng`,
    value:
      "`/bank deposit` - Gửi tiền vào ngân hàng\n" +
      "`/bank withdraw` - Rút tiền từ ngân hàng\n" +
      "`/bank info` - Xem thông tin tài khoản ngân hàng",
    inline: false,
  });

  // Cửa hàng
  embed.addFields({
    name: `${config.emojis.shop} Cửa hàng & Kho đồ`,
    value:
      "`/shop list` - Xem danh sách vật phẩm\n" +
      "`/shop buy` - Mua vật phẩm\n" +
      "`/shop sell` - Bán vật phẩm\n" +
      "`/inventory` - Xem kho đồ",
    inline: false,
  });

  // Trò chơi & Giải trí
  embed.addFields({
    name: `🎮 Trò chơi & Giải trí`,
    value:
      "`/crime` - Phạm pháp để kiếm tiền (có rủi ro)\n" +
      "`/fish` - Câu cá để kiếm tiền\n" +
      "`/hunt` - Săn bắn động vật (yêu cầu level 3)\n" +
      "`/gamble` - Casino games (coinflip, dice, slots)\n" +
      "`/blackjack` - Chơi Blackjack với dealer\n" +
      "`/rps` - Oẳn tù tì với người khác hoặc bot\n" +
      "`/lottery` - Mua vé số và thử vận may",
    inline: false,
  });

  // Thống kê & Thành tựu
  embed.addFields({
    name: `${config.emojis.trophy} Thống kê & Thành tựu`,
    value:
      "`/leaderboard` - Xem bảng xếp hạng\n" +
      "`/achievements` - Xem thành tựu\n" +
      "`/help` - Hiển thị hướng dẫn này",
    inline: false,
  });

  // Thông tin thêm
  embed.addFields({
    name: `${config.emojis.star} Mẹo sử dụng`,
    value:
      "• Gửi tiền vào ngân hàng để bảo vệ khỏi mất mát\n" +
      "• Làm việc mỗi giờ để kiếm thêm thu nhập\n" +
      "• Mua vật phẩm rồi bán lại để kiếm lời\n" +
      "• Kiểm tra bảng xếp hạng để so sánh với người khác",
    inline: false,
  });

  await interaction.reply({ embeds: [embed] });
}

async function showCommandHelp(interaction, commandName) {
  const commandHelp = {
    balance: {
      title: "Lệnh Balance (Số dư)",
      description: "Kiểm tra số dư tài khoản của bạn hoặc người khác",
      usage: "`/balance [user]`",
      examples: [
        "`/balance` - Xem số dư của bạn",
        "`/balance @user` - Xem số dư của người khác",
      ],
      cooldown: "3 giây",
    },
    daily: {
      title: "Lệnh Daily (Hàng ngày)",
      description: "Nhận phần thưởng hàng ngày từ 100-500 xu",
      usage: "`/daily`",
      examples: ["`/daily` - Nhận phần thưởng hàng ngày"],
      cooldown: "24 giờ",
      notes: "Phần thưởng reset vào 00:00 (GMT+7) mỗi ngày",
    },
    work: {
      title: "Lệnh Work (Làm việc)",
      description:
        "Làm việc để kiếm tiền, mỗi công việc cho thu nhập khác nhau",
      usage: "`/work`",
      examples: ["`/work` - Làm một công việc ngẫu nhiên"],
      cooldown: "1 giờ",
      notes: "Thu nhập từ 80-600 xu tùy theo công việc",
    },
    transfer: {
      title: "Lệnh Transfer (Chuyển tiền)",
      description: "Chuyển tiền cho người dùng khác",
      usage: "`/transfer <user> <amount>`",
      examples: [
        "`/transfer @user 1000` - Chuyển 1000 xu",
        "`/transfer @user all` - Chuyển toàn bộ số dư",
        "`/transfer @user half` - Chuyển một nửa số dư",
        "`/transfer @user 1k` - Chuyển 1000 xu (dùng k, m, b)",
      ],
      cooldown: "5 giây",
    },
    bank: {
      title: "Lệnh Bank (Ngân hàng)",
      description: "Quản lý tài khoản ngân hàng để bảo vệ tiền",
      usage: "`/bank <deposit|withdraw|info> [amount]`",
      examples: [
        "`/bank deposit 1000` - Gửi 1000 xu vào ngân hàng",
        "`/bank withdraw all` - Rút toàn bộ tiền",
        "`/bank info` - Xem thông tin tài khoản",
      ],
      cooldown: "3 giây",
      notes: "Tiền trong ngân hàng an toàn và có lãi suất 0.1%/ngày",
    },
    shop: {
      title: "Lệnh Shop (Cửa hàng)",
      description: "Mua bán vật phẩm trong cửa hàng",
      usage: "`/shop <list|buy|sell> [item] [quantity]`",
      examples: [
        "`/shop list` - Xem tất cả vật phẩm",
        "`/shop list food` - Xem vật phẩm danh mục thức ăn",
        '`/shop buy "bánh mì" 2` - Mua 2 ổ bánh mì',
        '`/shop sell "điện thoại"` - Bán điện thoại',
      ],
      cooldown: "5 giây",
    },
    inventory: {
      title: "Lệnh Inventory (Kho đồ)",
      description: "Xem kho đồ của bạn hoặc người khác",
      usage: "`/inventory [user] [category]`",
      examples: [
        "`/inventory` - Xem kho đồ của bạn",
        "`/inventory @user` - Xem kho đồ của người khác",
        "`/inventory food` - Xem vật phẩm thức ăn",
      ],
      cooldown: "5 giây",
    },
    leaderboard: {
      title: "Lệnh Leaderboard (Bảng xếp hạng)",
      description: "Xem bảng xếp hạng người dùng",
      usage: "`/leaderboard [type]`",
      examples: [
        "`/leaderboard` - Bảng xếp hạng tài sản",
        "`/leaderboard balance` - Bảng xếp hạng số dư ví",
        "`/leaderboard bank` - Bảng xếp hạng ngân hàng",
      ],
      cooldown: "10 giây",
    },
  };

  const help = commandHelp[commandName];
  if (!help) {
    const errorEmbed = new EmbedBuilder()
      .setColor(config.colors.error)
      .setTitle("❌ Lệnh không tồn tại")
      .setDescription(`Không tìm thấy hướng dẫn cho lệnh "${commandName}"`)
      .setTimestamp();

    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const embed = new EmbedBuilder()
    .setColor(config.colors.info)
    .setTitle(`${config.emojis.info} ${help.title}`)
    .setDescription(help.description)
    .addFields(
      {
        name: "📝 Cách sử dụng",
        value: help.usage,
        inline: false,
      },
      {
        name: "💡 Ví dụ",
        value: help.examples.join("\n"),
        inline: false,
      },
      {
        name: "⏱️ Cooldown",
        value: help.cooldown,
        inline: true,
      }
    )
    .setTimestamp()
    .setFooter({ text: "Economy Bot" });

  if (help.notes) {
    embed.addFields({
      name: "📌 Lưu ý",
      value: help.notes,
      inline: false,
    });
  }

  await interaction.reply({ embeds: [embed] });
}
