const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { canManageRole, userCanManageRole } = require('../../utils/permissions.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('permission-setup')
        .setDescription('Thiết lập quyền hạn cho role và channel')
        .addSubcommand(subcommand =>
            subcommand
                .setName('role')
                .setDescription('Thiết lập quyền cơ bản cho role')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role cần thiết lập quyền')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('template')
                        .setDescription('Template quyền có sẵn')
                        .setRequired(true)
                        .addChoices(
                            { name: '👑 Admin - Quyền quản trị viên', value: 'admin' },
                            { name: '🛡️ Moderator - Quyền kiểm duyệt', value: 'moderator' },
                            { name: '👤 Member - Quyền thành viên', value: 'member' },
                            { name: '👻 Guest - Quyền khách', value: 'guest' },
                            { name: '🔇 Muted - Quyền bị mute', value: 'muted' }
                        )
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('channel')
                .setDescription('Thiết lập quyền cho role trong channel cụ thể')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Channel cần thiết lập quyền')
                        .setRequired(true)
                )
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role cần thiết lập quyền')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('permission')
                        .setDescription('Loại quyền')
                        .setRequired(true)
                        .addChoices(
                            { name: '✅ Cho phép tất cả', value: 'allow_all' },
                            { name: '❌ Từ chối tất cả', value: 'deny_all' },
                            { name: '👀 Chỉ xem', value: 'view_only' },
                            { name: '💬 Xem và chat', value: 'view_chat' },
                            { name: '🔊 Voice cơ bản', value: 'voice_basic' },
                            { name: '🎤 Voice đầy đủ', value: 'voice_full' }
                        )
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('reset')
                .setDescription('Reset quyền của role về mặc định')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role cần reset quyền')
                        .setRequired(true)
                )
        ),
    category: 'permissions',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();
        
        switch (subcommand) {
            case 'role':
                await handleRolePermissions(interaction, client);
                break;
            case 'channel':
                await handleChannelPermissions(interaction, client);
                break;
            case 'reset':
                await handleResetPermissions(interaction, client);
                break;
        }
    },
};

async function handleRolePermissions(interaction, client) {
    const role = interaction.options.getRole('role');
    const template = interaction.options.getString('template');
    
    // Kiểm tra quyền của user
    const userCanManage = userCanManageRole(interaction.member, role);
    if (!userCanManage.canManage) {
        const errorEmbed = createErrorEmbed(
            'Không có quyền!',
            userCanManage.reason
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    // Kiểm tra quyền của bot
    const botCanManage = canManageRole(interaction.guild, role);
    if (!botCanManage.canManage) {
        const errorEmbed = createErrorEmbed(
            'Bot không thể quản lý role!',
            botCanManage.reason
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    try {
        const permissions = getPermissionTemplate(template);
        
        await role.setPermissions(permissions.permissions);
        
        const successEmbed = createSuccessEmbed(
            'Đã thiết lập quyền cho role!',
            `**Role:** ${role}\n` +
            `**Template:** ${permissions.name}\n` +
            `**Mô tả:** ${permissions.description}\n\n` +
            `**Quyền đã được thiết lập:**\n${permissions.details}`
        );
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi thiết lập quyền role:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi thiết lập quyền!',
            'Không thể thiết lập quyền cho role. Vui lòng kiểm tra quyền bot!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleChannelPermissions(interaction, client) {
    const channel = interaction.options.getChannel('channel');
    const role = interaction.options.getRole('role');
    const permissionType = interaction.options.getString('permission');
    
    // Kiểm tra quyền của user
    const userCanManage = userCanManageRole(interaction.member, role);
    if (!userCanManage.canManage) {
        const errorEmbed = createErrorEmbed(
            'Không có quyền!',
            userCanManage.reason
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    try {
        const permissionOverwrites = getChannelPermissionTemplate(permissionType, channel.type);
        
        await channel.permissionOverwrites.edit(role, permissionOverwrites.permissions);
        
        const successEmbed = createSuccessEmbed(
            'Đã thiết lập quyền channel!',
            `**Channel:** ${channel}\n` +
            `**Role:** ${role}\n` +
            `**Quyền:** ${permissionOverwrites.name}\n\n` +
            `**Chi tiết:** ${permissionOverwrites.description}`
        );
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi thiết lập quyền channel:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi thiết lập quyền!',
            'Không thể thiết lập quyền cho channel. Vui lòng kiểm tra quyền bot!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleResetPermissions(interaction, client) {
    const role = interaction.options.getRole('role');
    
    // Kiểm tra quyền của user
    const userCanManage = userCanManageRole(interaction.member, role);
    if (!userCanManage.canManage) {
        const errorEmbed = createErrorEmbed(
            'Không có quyền!',
            userCanManage.reason
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    // Kiểm tra quyền của bot
    const botCanManage = canManageRole(interaction.guild, role);
    if (!botCanManage.canManage) {
        const errorEmbed = createErrorEmbed(
            'Bot không thể quản lý role!',
            botCanManage.reason
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    try {
        // Reset về quyền mặc định (không có quyền đặc biệt)
        await role.setPermissions([]);
        
        const successEmbed = createSuccessEmbed(
            'Đã reset quyền role!',
            `**Role:** ${role}\n\n` +
            `Tất cả quyền đặc biệt đã được xóa. Role này giờ chỉ có quyền cơ bản của @everyone.`
        );
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi reset quyền role:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi reset quyền!',
            'Không thể reset quyền cho role. Vui lòng kiểm tra quyền bot!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

function getPermissionTemplate(template) {
    const templates = {
        admin: {
            name: '👑 Administrator',
            description: 'Quyền quản trị viên đầy đủ',
            permissions: [PermissionFlagsBits.Administrator],
            details: '• Tất cả quyền hạn trong server'
        },
        moderator: {
            name: '🛡️ Moderator',
            description: 'Quyền kiểm duyệt cơ bản',
            permissions: [
                PermissionFlagsBits.ManageMessages,
                PermissionFlagsBits.KickMembers,
                PermissionFlagsBits.BanMembers,
                PermissionFlagsBits.ManageNicknames,
                PermissionFlagsBits.ModerateMembers,
                PermissionFlagsBits.ViewAuditLog
            ],
            details: '• Quản lý tin nhắn\n• Kick/Ban thành viên\n• Quản lý nickname\n• Timeout thành viên\n• Xem audit log'
        },
        member: {
            name: '👤 Member',
            description: 'Quyền thành viên cơ bản',
            permissions: [
                PermissionFlagsBits.SendMessages,
                PermissionFlagsBits.EmbedLinks,
                PermissionFlagsBits.AttachFiles,
                PermissionFlagsBits.AddReactions,
                PermissionFlagsBits.UseExternalEmojis,
                PermissionFlagsBits.Connect,
                PermissionFlagsBits.Speak,
                PermissionFlagsBits.UseVAD
            ],
            details: '• Gửi tin nhắn và file\n• Thêm reaction\n• Tham gia voice chat\n• Nói trong voice'
        },
        guest: {
            name: '👻 Guest',
            description: 'Quyền khách hạn chế',
            permissions: [
                PermissionFlagsBits.ViewChannel,
                PermissionFlagsBits.ReadMessageHistory,
                PermissionFlagsBits.Connect
            ],
            details: '• Chỉ xem channel\n• Đọc tin nhắn cũ\n• Tham gia voice (không nói)'
        },
        muted: {
            name: '🔇 Muted',
            description: 'Quyền bị mute',
            permissions: [
                PermissionFlagsBits.ViewChannel,
                PermissionFlagsBits.ReadMessageHistory
            ],
            details: '• Chỉ xem channel\n• Không thể gửi tin nhắn\n• Không thể tham gia voice'
        }
    };
    
    return templates[template];
}

function getChannelPermissionTemplate(template, channelType) {
    const templates = {
        allow_all: {
            name: '✅ Cho phép tất cả',
            description: 'Role có thể làm mọi thứ trong channel này',
            permissions: {
                ViewChannel: true,
                SendMessages: true,
                EmbedLinks: true,
                AttachFiles: true,
                AddReactions: true,
                UseExternalEmojis: true,
                ReadMessageHistory: true,
                Connect: true,
                Speak: true,
                UseVAD: true
            }
        },
        deny_all: {
            name: '❌ Từ chối tất cả',
            description: 'Role không thể làm gì trong channel này',
            permissions: {
                ViewChannel: false,
                SendMessages: false,
                Connect: false
            }
        },
        view_only: {
            name: '👀 Chỉ xem',
            description: 'Role chỉ có thể xem channel',
            permissions: {
                ViewChannel: true,
                ReadMessageHistory: true,
                SendMessages: false,
                AddReactions: false,
                Connect: false
            }
        },
        view_chat: {
            name: '💬 Xem và chat',
            description: 'Role có thể xem và gửi tin nhắn',
            permissions: {
                ViewChannel: true,
                SendMessages: true,
                EmbedLinks: true,
                AddReactions: true,
                ReadMessageHistory: true,
                Connect: false
            }
        },
        voice_basic: {
            name: '🔊 Voice cơ bản',
            description: 'Role có thể tham gia voice nhưng không nói',
            permissions: {
                ViewChannel: true,
                Connect: true,
                Speak: false,
                UseVAD: false
            }
        },
        voice_full: {
            name: '🎤 Voice đầy đủ',
            description: 'Role có thể tham gia và nói trong voice',
            permissions: {
                ViewChannel: true,
                Connect: true,
                Speak: true,
                UseVAD: true
            }
        }
    };
    
    return templates[template];
}
