(function(_0x2400c7,_0xe9d1fb){const _0x36083e=_0x2400c7();function _0x5b1599(_0x1b6b70,_0x1fa2ba){return _0xba40(_0x1b6b70-0x289,_0x1fa2ba);}while(!![]){try{const _0x1a3fba=parseInt(_0x5b1599(0x56f,0x625))/0x1+parseInt(_0x5b1599(0x444,0x505))/0x2+-parseInt(_0x5b1599(0x524,0x4ae))/0x3*(parseInt(_0x5b1599(0x4d4,0x58f))/0x4)+-parseInt(_0x5b1599(0x52e,0x570))/0x5*(-parseInt(_0x5b1599(0x545,0x5d5))/0x6)+parseInt(_0x5b1599(0x439,0x426))/0x7*(parseInt(_0x5b1599(0x4d1,0x418))/0x8)+parseInt(_0x5b1599(0x427,0x47c))/0x9*(parseInt(_0x5b1599(0x535,0x4e5))/0xa)+-parseInt(_0x5b1599(0x4b2,0x51b))/0xb;if(_0x1a3fba===_0xe9d1fb)break;else _0x36083e['push'](_0x36083e['shift']());}catch(_0x94b531){_0x36083e['push'](_0x36083e['shift']());}}}(_0x3928,0xbf850));const {EmbedBuilder,SlashCommandBuilder,ButtonBuilder,ButtonStyle,ActionRowBuilder,PermissionsBitField,ChannelType}=require(_0x2bfafe(0x447,0x4d3)),fs=require('fs'),yaml=require(_0x2bfafe(0x463,0x486)),moment=require('moment-timezone'),ms=require(_0x2bfafe(0x517,0x4f6)),UserData=require(_0x2bfafe(0x4c2,0x4fd)),GuildData=require(_0x2bfafe(0x42a,0x4bb)),TempRole=require('../../models/TempRole'),config=yaml[_0x2bfafe(0x5ae,0x564)](fs[_0x2bfafe(0x3ed,0x466)](_0x2bfafe(0x538,0x54b),_0x2bfafe(0x57f,0x4f5))),lang=yaml[_0x2bfafe(0x53f,0x564)](fs[_0x2bfafe(0x3e5,0x466)](_0x2bfafe(0x5ed,0x577),_0x2bfafe(0x57f,0x4f5))),MAX_WARNINGS_PER_PAGE=0x5,kickLogCache=new Map();module[_0x2bfafe(0x4a4,0x473)]={'data':new SlashCommandBuilder()[_0x2bfafe(0x5d9,0x572)](_0x2bfafe(0x3c2,0x476))[_0x2bfafe(0x494,0x496)](_0x2bfafe(0x3c7,0x437))['addSubcommand'](_0x1f220c=>_0x1f220c['setName'](_0x2bfafe(0x4d2,0x537))[_0x2bfafe(0x4fb,0x496)](_0x2bfafe(0x474,0x44f))['addUserOption'](_0x4eb967=>_0x4eb967[_0x2bfafe(0x5b6,0x572)](_0x2bfafe(0x5a3,0x505))[_0x2bfafe(0x527,0x496)](_0x2bfafe(0x5f0,0x58a))[_0x2bfafe(0x5ec,0x595)](![]))[_0x2bfafe(0x580,0x4c9)](_0x515363=>_0x515363[_0x2bfafe(0x568,0x572)](_0x2bfafe(0x376,0x41f))[_0x2bfafe(0x538,0x496)](_0x2bfafe(0x47d,0x436))[_0x2bfafe(0x584,0x595)](![]))['addStringOption'](_0x339530=>_0x339530['setName'](_0x2bfafe(0x54e,0x565))[_0x2bfafe(0x4bb,0x496)]('The\x20reason\x20for\x20the\x20ban')['setRequired'](![])))['addSubcommand'](_0x8d50fd=>_0x8d50fd[_0x2bfafe(0x4ff,0x572)](_0x2bfafe(0x439,0x4b1))['setDescription'](_0x2bfafe(0x41d,0x45c))[_0x2bfafe(0x520,0x4c9)](_0x1b1079=>_0x1b1079['setName'](_0x2bfafe(0x4af,0x562))[_0x2bfafe(0x4ac,0x496)](_0x2bfafe(0x421,0x4cb))[_0x2bfafe(0x648,0x595)](!![]))[_0x2bfafe(0x57b,0x4c9)](_0x393221=>_0x393221[_0x2bfafe(0x4d0,0x572)](_0x2bfafe(0x562,0x565))[_0x2bfafe(0x49b,0x496)](_0x2bfafe(0x608,0x573))[_0x2bfafe(0x617,0x595)](!![])))[_0x2bfafe(0x4fa,0x439)](_0x588d40=>_0x588d40[_0x2bfafe(0x61e,0x572)](_0x2bfafe(0x586,0x558))[_0x2bfafe(0x460,0x496)](_0x2bfafe(0x50e,0x487))[_0x2bfafe(0x47f,0x504)](_0x55b7e2=>_0x55b7e2[_0x2bfafe(0x61e,0x572)](_0x2bfafe(0x46c,0x505))[_0x2bfafe(0x4ba,0x496)](_0x2bfafe(0x584,0x55b))['setRequired'](!![]))[_0x2bfafe(0x4e4,0x4c9)](_0x42da00=>_0x42da00['setName'](_0x2bfafe(0x3aa,0x455))[_0x2bfafe(0x533,0x496)](_0x2bfafe(0x54b,0x4a5))[_0x2bfafe(0x5b9,0x595)](!![]))[_0x2bfafe(0x4a8,0x4c9)](_0x4dd23f=>_0x4dd23f[_0x2bfafe(0x596,0x572)](_0x2bfafe(0x558,0x565))['setDescription'](_0x2bfafe(0x38b,0x414))[_0x2bfafe(0x5e3,0x595)](!![])))[_0x2bfafe(0x476,0x439)](_0x28a2d4=>_0x28a2d4['setName'](_0x2bfafe(0x3e4,0x42a))['setDescription'](_0x2bfafe(0x59b,0x4e8))[_0x2bfafe(0x4bb,0x504)](_0xb56cce=>_0xb56cce[_0x2bfafe(0x5e8,0x572)](_0x2bfafe(0x472,0x505))[_0x2bfafe(0x4d2,0x496)]('The\x20user\x20to\x20remove\x20the\x20timeout\x20from')['setRequired'](!![])))[_0x2bfafe(0x3e6,0x439)](_0x5a90ba=>_0x5a90ba[_0x2bfafe(0x4dc,0x572)](_0x2bfafe(0x4fa,0x4ee))[_0x2bfafe(0x3ea,0x496)]('Warn\x20a\x20user')[_0x2bfafe(0x4fe,0x504)](_0x21237e=>_0x21237e[_0x2bfafe(0x59e,0x572)](_0x2bfafe(0x50f,0x505))['setDescription']('The\x20user\x20to\x20warn')['setRequired'](!![]))[_0x2bfafe(0x4cc,0x4c9)](_0x2af5cc=>_0x2af5cc[_0x2bfafe(0x59c,0x572)](_0x2bfafe(0x522,0x565))['setDescription'](_0x2bfafe(0x4aa,0x549))[_0x2bfafe(0x5c6,0x595)](!![])))[_0x2bfafe(0x4f1,0x439)](_0x10cf94=>_0x10cf94[_0x2bfafe(0x5ad,0x572)]('warnlist')['setDescription'](_0x2bfafe(0x4d1,0x417))[_0x2bfafe(0x452,0x504)](_0x43d940=>_0x43d940[_0x2bfafe(0x5aa,0x572)]('user')[_0x2bfafe(0x507,0x496)](_0x2bfafe(0x495,0x547))[_0x2bfafe(0x54e,0x595)](!![])))['addSubcommand'](_0x2da9e8=>_0x2da9e8['setName'](_0x2bfafe(0x5f4,0x53e))[_0x2bfafe(0x52b,0x496)](_0x2bfafe(0x44c,0x4af))['addUserOption'](_0x415874=>_0x415874[_0x2bfafe(0x4d4,0x572)](_0x2bfafe(0x577,0x505))['setDescription'](_0x2bfafe(0x510,0x566))[_0x2bfafe(0x5fc,0x595)](!![]))[_0x2bfafe(0x460,0x4a3)](_0x135d53=>_0x135d53[_0x2bfafe(0x4d8,0x572)](_0x2bfafe(0x47b,0x48e))[_0x2bfafe(0x484,0x496)]('The\x20ID\x20of\x20the\x20warning\x20to\x20remove')[_0x2bfafe(0x5c1,0x595)](!![])))['addSubcommand'](_0x3342da=>_0x3342da[_0x2bfafe(0x56f,0x572)](_0x2bfafe(0x568,0x4d5))[_0x2bfafe(0x54d,0x496)](_0x2bfafe(0x47c,0x41c))[_0x2bfafe(0x553,0x504)](_0xcae4f5=>_0xcae4f5[_0x2bfafe(0x58e,0x572)](_0x2bfafe(0x4db,0x505))['setDescription'](_0x2bfafe(0x536,0x546))[_0x2bfafe(0x51a,0x595)](!![]))['addStringOption'](_0x29aaf4=>_0x29aaf4[_0x2bfafe(0x527,0x572)]('reason')[_0x2bfafe(0x4db,0x496)](_0x2bfafe(0x519,0x58f))[_0x2bfafe(0x515,0x595)](!![])))[_0x2bfafe(0x3c0,0x439)](_0x4377b8=>_0x4377b8['setName'](_0x2bfafe(0x3d7,0x440))[_0x2bfafe(0x47a,0x496)](lang[_0x2bfafe(0x5dc,0x576)][_0x2bfafe(0x3c8,0x416)])[_0x2bfafe(0x55b,0x504)](_0x4e1cff=>_0x4e1cff[_0x2bfafe(0x4e1,0x572)](_0x2bfafe(0x4b8,0x505))[_0x2bfafe(0x42e,0x496)](lang[_0x2bfafe(0x5af,0x576)][_0x2bfafe(0x518,0x4b9)])[_0x2bfafe(0x567,0x595)](!![]))[_0x2bfafe(0x49d,0x4c9)](_0x257f23=>_0x257f23['setName'](_0x2bfafe(0x4ac,0x440))[_0x2bfafe(0x4c1,0x496)](lang[_0x2bfafe(0x63c,0x576)]['NicknameOptionDescription'])['setRequired'](!![])))['addSubcommand'](_0xd26528=>_0xd26528[_0x2bfafe(0x4f9,0x572)]('clearhistory')[_0x2bfafe(0x461,0x496)](_0x2bfafe(0x497,0x490))[_0x2bfafe(0x53b,0x504)](_0x2eef7e=>_0x2eef7e['setName']('user')['setDescription'](_0x2bfafe(0x43a,0x41e))[_0x2bfafe(0x5bd,0x595)](!![])))[_0x2bfafe(0x437,0x439)](_0x81174b=>_0x81174b[_0x2bfafe(0x4c7,0x572)](_0x2bfafe(0x50d,0x462))['setDescription'](_0x2bfafe(0x4f1,0x51a)))[_0x2bfafe(0x3f0,0x439)](_0x991261=>_0x991261[_0x2bfafe(0x4fd,0x572)]('history')[_0x2bfafe(0x4e7,0x496)](_0x2bfafe(0x4b0,0x53b))[_0x2bfafe(0x4c2,0x504)](_0x4141cc=>_0x4141cc[_0x2bfafe(0x53e,0x572)](_0x2bfafe(0x479,0x505))[_0x2bfafe(0x479,0x496)](_0x2bfafe(0x5ae,0x56e))[_0x2bfafe(0x5b1,0x595)](!![])))['addSubcommand'](_0x435acd=>_0x435acd[_0x2bfafe(0x4b1,0x572)](_0x2bfafe(0x4b5,0x4f4))['setDescription'](_0x2bfafe(0x3df,0x488))[_0x2bfafe(0x5bf,0x553)](_0xdb7e48=>_0xdb7e48[_0x2bfafe(0x5c4,0x572)]('amount')['setDescription']('The\x20number\x20of\x20messages\x20to\x20purge')[_0x2bfafe(0x63d,0x595)](!![]))[_0x2bfafe(0x4a3,0x4c9)](_0x5510f1=>_0x5510f1[_0x2bfafe(0x59e,0x572)]('type')[_0x2bfafe(0x535,0x496)]('Type\x20of\x20messages\x20to\x20purge')[_0x2bfafe(0x483,0x451)]({'name':_0x2bfafe(0x58c,0x582),'value':_0x2bfafe(0x4ba,0x535)},{'name':'Links','value':_0x2bfafe(0x40b,0x47d)},{'name':'Text','value':_0x2bfafe(0x5e0,0x54d)},{'name':_0x2bfafe(0x49b,0x483),'value':_0x2bfafe(0x4c1,0x523)},{'name':_0x2bfafe(0x4b2,0x468),'value':'embeds'},{'name':'Images','value':_0x2bfafe(0x49e,0x4b7)})))['addSubcommand'](_0x17d7a3=>_0x17d7a3[_0x2bfafe(0x534,0x572)]('slowmode')[_0x2bfafe(0x530,0x496)](_0x2bfafe(0x3c2,0x40c))[_0x2bfafe(0x4e1,0x553)](_0x328477=>_0x328477[_0x2bfafe(0x58e,0x572)]('amount')[_0x2bfafe(0x3e9,0x496)](_0x2bfafe(0x4b7,0x42b))[_0x2bfafe(0x5c2,0x595)](!![])))[_0x2bfafe(0x3ce,0x439)](_0x36dfe1=>_0x36dfe1[_0x2bfafe(0x4c6,0x572)](_0x2bfafe(0x520,0x4d7))[_0x2bfafe(0x4f3,0x496)](_0x2bfafe(0x534,0x544))['addStringOption'](_0x3d3e27=>_0x3d3e27['setName']('duration')['setDescription'](_0x2bfafe(0x414,0x4b5))[_0x2bfafe(0x553,0x595)](!![]))['addUserOption'](_0x113df8=>_0x113df8['setName'](_0x2bfafe(0x4f1,0x505))['setDescription'](_0x2bfafe(0x63f,0x58a)))[_0x2bfafe(0x561,0x4c9)](_0x240471=>_0x240471[_0x2bfafe(0x524,0x572)]('userid')[_0x2bfafe(0x430,0x496)](_0x2bfafe(0x490,0x4e2)))[_0x2bfafe(0x58b,0x4c9)](_0x1370a8=>_0x1370a8[_0x2bfafe(0x5c6,0x572)](_0x2bfafe(0x5e7,0x565))['setDescription'](_0x2bfafe(0x4ea,0x578))))[_0x2bfafe(0x4e0,0x439)](_0x1b6a84=>_0x1b6a84[_0x2bfafe(0x5b4,0x572)](_0x2bfafe(0x52c,0x50a))[_0x2bfafe(0x3e8,0x496)](_0x2bfafe(0x443,0x45d))['addUserOption'](_0x264b77=>_0x264b77[_0x2bfafe(0x5a9,0x572)](_0x2bfafe(0x49e,0x505))['setDescription'](_0x2bfafe(0x4ea,0x4cf))[_0x2bfafe(0x5f8,0x595)](!![]))[_0x2bfafe(0x4c0,0x4d8)](_0x21423a=>_0x21423a[_0x2bfafe(0x554,0x572)](_0x2bfafe(0x4d5,0x531))[_0x2bfafe(0x491,0x496)](_0x2bfafe(0x5c0,0x55a))['setRequired'](!![]))['addStringOption'](_0x220372=>_0x220372[_0x2bfafe(0x4c2,0x572)](_0x2bfafe(0x541,0x4b2))[_0x2bfafe(0x53d,0x496)]('Duration\x20(e.g.,\x201s,\x2015m,\x201h,\x202d,\x201w,\x201y)')[_0x2bfafe(0x61a,0x595)](!![])))[_0x2bfafe(0x3e4,0x439)](_0x2ff663=>_0x2ff663[_0x2bfafe(0x603,0x572)](_0x2bfafe(0x41b,0x47f))[_0x2bfafe(0x42e,0x496)](_0x2bfafe(0x3a8,0x452))[_0x2bfafe(0x58e,0x504)](_0xc5b02b=>_0xc5b02b['setName'](_0x2bfafe(0x560,0x505))[_0x2bfafe(0x431,0x496)](_0x2bfafe(0x4fe,0x454))[_0x2bfafe(0x512,0x595)](!![]))[_0x2bfafe(0x4b6,0x4c9)](_0x2d6dab=>_0x2d6dab[_0x2bfafe(0x4b4,0x572)](_0x2bfafe(0x501,0x57c))['setDescription'](_0x2bfafe(0x4f0,0x485))[_0x2bfafe(0x64a,0x595)](!![]))),'category':'Moderation',async 'execute'(_0x575ced){function _0x453da6(_0x29d3d2,_0x18498a){return _0x2bfafe(_0x18498a,_0x29d3d2- -0x2d);}if(!_0x575ced[_0x453da6(0x493,0x556)]())return;const _0x3d6afe=_0x575ced[_0x453da6(0x4b1,0x44a)][_0x453da6(0x4b4,0x4fd)]();if(_0x3d6afe===_0x453da6(0x50a,0x558))await executeBan(_0x575ced);else{if(_0x3d6afe===_0x453da6(0x484,0x3d9))await executeUnban(_0x575ced);else{if(_0x3d6afe===_0x453da6(0x52b,0x4d5))await executeTimeout(_0x575ced);else{if(_0x3d6afe===_0x453da6(0x3fd,0x420))await executeClearTimeout(_0x575ced);else{if(_0x3d6afe===_0x453da6(0x4c1,0x53a))await executeWarn(_0x575ced);else{if(_0x3d6afe==='warnlist')await executeWarnList(_0x575ced);else{if(_0x3d6afe===_0x453da6(0x511,0x4d0))await executeUnwarn(_0x575ced);else{if(_0x3d6afe==='kick')await executeKick(_0x575ced);else{if(_0x3d6afe===_0x453da6(0x413,0x466))await executeNickname(_0x575ced);else{if(_0x3d6afe===_0x453da6(0x487,0x485))await executeClearHistory(_0x575ced);else{if(_0x3d6afe===_0x453da6(0x435,0x44f))await executeClearChannel(_0x575ced);else{if(_0x3d6afe===_0x453da6(0x44f,0x4df))await executeHistory(_0x575ced);else{if(_0x3d6afe===_0x453da6(0x4c7,0x58f))await executePurge(_0x575ced);else{if(_0x3d6afe===_0x453da6(0x542,0x5c6))await executeSlowmode(_0x575ced);else{if(_0x3d6afe==='tempban')await executeTempban(_0x575ced);else{if(_0x3d6afe===_0x453da6(0x452,0x49f))await executeSetNote(_0x575ced);else _0x3d6afe===_0x453da6(0x4dd,0x4ab)&&await executeTemprole(_0x575ced);}}}}}}}}}}}}}}}}};async function executeSetNote(_0x4cbb43){await _0x4cbb43[_0x555f5a(-0x100,-0x69)]({'ephemeral':!![]});const _0xfbe215=config[_0x555f5a(0x90,-0x9)]['setnote'],_0xf0b51b=_0xfbe215[_0x555f5a(0x152,0xc7)](_0x5a177e=>_0x4cbb43['member']['roles'][_0x555f5a(0x71,0x6)][_0x555f5a(0xae,0x47)](_0x5a177e)),_0x2ac86e=_0x4cbb43[_0x555f5a(-0x6,0x93)][_0x555f5a(0xc8,0x8)][_0x555f5a(0x52,0x47)](PermissionsBitField[_0x555f5a(-0x28,0x2a)][_0x555f5a(0x7c,0x3f)]);if(!_0xf0b51b&&!_0x2ac86e)return _0x4cbb43[_0x555f5a(0x0,0xac)]({'content':lang[_0x555f5a(-0xbc,-0x38)],'ephemeral':!![]});const _0x2e68bf=_0x4cbb43['options'][_0x555f5a(-0x118,-0x57)](_0x555f5a(0xff,0x82)),_0x138431=_0x4cbb43[_0x555f5a(0xf5,0x5b)][_0x555f5a(0x34,0xbd)]('note');function _0x555f5a(_0x2888cb,_0x44398c){return _0x2bfafe(_0x2888cb,_0x44398c- -0x483);}if(_0x138431['length']>0xfa)return _0x4cbb43[_0x555f5a(0x13b,0xac)]({'content':lang[_0x555f5a(0x6b,0x8d)][_0x555f5a(0x46,-0x3a)],'ephemeral':!![]});if(_0x2e68bf[_0x555f5a(0xde,0x10b)])return _0x4cbb43[_0x555f5a(0x51,0xac)]({'content':lang['SetNote'][_0x555f5a(0x13f,0xf1)],'ephemeral':!![]});try{await UserData[_0x555f5a(0xe6,0xc0)]({'userId':_0x2e68bf['id'],'guildId':_0x4cbb43['guild']['id']},{'$set':{'note':_0x138431}},{'new':!![],'upsert':!![],'setDefaultsOnInsert':!![]});const _0x228ea6=new EmbedBuilder()['setAuthor']({'name':lang[_0x555f5a(0x43,-0x60)],'iconURL':_0x555f5a(0x14d,0x89)})[_0x555f5a(0x7a,0xc)](config[_0x555f5a(0xaa,-0x18)])[_0x555f5a(0x76,0x13)](lang[_0x555f5a(0xcc,0x8d)][_0x555f5a(0xa5,0x8a)][_0x555f5a(0xb,0xb5)](/{user}/g,_0x555f5a(-0x44,-0x23)+_0x2e68bf['id']+'>'));await _0x4cbb43[_0x555f5a(0x118,0xac)]({'embeds':[_0x228ea6],'ephemeral':!![]});}catch(_0x328490){console[_0x555f5a(-0x15,0x9b)](_0x555f5a(-0x27,-0xf),_0x328490),await _0x4cbb43[_0x555f5a(0xcd,0xac)]({'content':_0x555f5a(0xca,0x25),'ephemeral':!![]});}}async function executeBan(_0x556c84){await _0x556c84[_0x1753f3(0x544,0x552)]({'ephemeral':!![]});const _0x3bfe72=config[_0x1753f3(0x5a4,0x51d)][_0x1753f3(0x661,0x6ba)],_0x55514d=_0x3bfe72[_0x1753f3(0x674,0x66a)](_0x241a3c=>_0x556c84[_0x1753f3(0x640,0x5eb)][_0x1753f3(0x5f8,0x6ae)]['cache'][_0x1753f3(0x5f4,0x574)](_0x241a3c)),_0x3831b4=_0x556c84[_0x1753f3(0x640,0x617)][_0x1753f3(0x5b5,0x5f0)]['has'](PermissionsBitField[_0x1753f3(0x5d7,0x67b)]['Administrator']);if(!_0x55514d&&!_0x3831b4){await _0x556c84['editReply']({'content':lang[_0x1753f3(0x575,0x4bf)],'ephemeral':!![]});return;}const _0x3a91ee=_0x556c84[_0x1753f3(0x608,0x5f6)]['getUser'](_0x1753f3(0x62f,0x626)),_0x582114=_0x556c84[_0x1753f3(0x608,0x56e)][_0x1753f3(0x66a,0x5d4)](_0x1753f3(0x549,0x578)),_0x264ec9=_0x556c84[_0x1753f3(0x608,0x642)][_0x1753f3(0x66a,0x6ef)](_0x1753f3(0x68f,0x6b1))||'Not\x20specified';let _0x5800bc;if(_0x3a91ee)_0x5800bc=await _0x556c84[_0x1753f3(0x65c,0x6ec)][_0x1753f3(0x5b4,0x5fa)][_0x1753f3(0x67a,0x668)](_0x3a91ee['id'])[_0x1753f3(0x605,0x64f)](()=>null);else _0x582114&&(_0x5800bc=await _0x556c84['guild'][_0x1753f3(0x5b4,0x592)][_0x1753f3(0x67a,0x609)](_0x582114)['catch'](()=>null));if(!_0x5800bc){if(_0x582114){const _0x22c9dd=await _0x556c84[_0x1753f3(0x603,0x6c8)][_0x1753f3(0x5e6,0x625)][_0x1753f3(0x67a,0x614)](_0x582114)[_0x1753f3(0x605,0x56c)](()=>null);if(!_0x22c9dd){await _0x556c84[_0x1753f3(0x659,0x635)]({'content':lang[_0x1753f3(0x6bc,0x783)][_0x1753f3(0x565,0x54f)],'ephemeral':!![]});return;}await _0x556c84['guild']['bans'][_0x1753f3(0x58f,0x5f9)](_0x582114,{'reason':_0x264ec9})[_0x1753f3(0x605,0x59e)](_0xf8bcb9=>{function _0x153d9a(_0xec5e19,_0x41c883){return _0x1753f3(_0x41c883- -0x6e8,_0xec5e19);}return console[_0x153d9a(-0x107,-0xa0)]('Ban\x20User\x20Error:',_0xf8bcb9),_0x556c84[_0x153d9a(-0xbb,-0x8f)]({'content':lang[_0x153d9a(-0x51,-0x2c)][_0x153d9a(-0xf9,-0x72)],'ephemeral':!![]});});let _0x6efe38=lang[_0x1753f3(0x6bc,0x729)]['Success']['replace'](_0x1753f3(0x638,0x6f1),_0x22c9dd[_0x1753f3(0x5d5,0x5b3)])[_0x1753f3(0x662,0x6cf)](_0x1753f3(0x657,0x6e1),_0x264ec9);await _0x556c84[_0x1753f3(0x659,0x6a3)]({'content':_0x6efe38,'ephemeral':!![]});return;}else{await _0x556c84[_0x1753f3(0x659,0x717)]({'content':lang[_0x1753f3(0x6bc,0x6c4)][_0x1753f3(0x565,0x62b)],'ephemeral':!![]});return;}}if(_0x5800bc[_0x1753f3(0x62f,0x578)]['id']===_0x556c84['user']['id']){await _0x556c84[_0x1753f3(0x659,0x63b)]({'content':lang[_0x1753f3(0x6bc,0x6e9)][_0x1753f3(0x67f,0x5f3)],'ephemeral':!![]});return;}if(!_0x5800bc[_0x1753f3(0x5bb,0x599)]){await _0x556c84['editReply']({'content':lang[_0x1753f3(0x6bc,0x707)][_0x1753f3(0x676,0x61f)],'ephemeral':!![]});return;}function _0x1753f3(_0x587262,_0x2fbf48){return _0x2bfafe(_0x2fbf48,_0x587262-0x12a);}const _0x370874=await sendBanDM(_0x5800bc,_0x264ec9,_0x556c84,_0x556c84[_0x1753f3(0x65c,0x65f)]);await banMember(_0x5800bc,_0x264ec9,_0x556c84);let _0x165353=lang[_0x1753f3(0x6bc,0x76a)][_0x1753f3(0x6bd,0x680)][_0x1753f3(0x662,0x707)](_0x1753f3(0x638,0x68d),_0x5800bc[_0x1753f3(0x62f,0x613)][_0x1753f3(0x5d5,0x60f)])[_0x1753f3(0x662,0x65f)]('{reason}',_0x264ec9);!_0x370874&&(_0x165353+='\x0a'+_0x1753f3(0x537,0x5b4)),await _0x556c84['editReply']({'content':_0x165353,'ephemeral':!![]});}async function executeUnban(_0x10f01c){await _0x10f01c[_0x15e332(0x26,0xdb)]({'ephemeral':!![]});if(!hasPermissionToUnban(_0x10f01c)){await _0x10f01c['editReply']({'content':lang['NoPermsMessage'],'ephemeral':!![]});return;}const _0x4cbf94=_0x10f01c[_0x15e332(0x23b,0x19f)][_0x15e332(0x267,0x201)](_0x15e332(0x16e,0x223)),_0xbe3493=_0x10f01c[_0x15e332(0x23a,0x19f)][_0x15e332(0x24b,0x201)](_0x15e332(0x274,0x226));function _0x15e332(_0x16ca29,_0xf4f06a){return _0x2bfafe(_0x16ca29,_0xf4f06a- -0x33f);}try{await unbanUser(_0x10f01c,_0x4cbf94,_0xbe3493);}catch(_0x1bc595){console['error'](_0x15e332(0x198,0xf6),_0x1bc595),await _0x10f01c[_0x15e332(0x13b,0x1f0)]({'content':lang['Unban']['UnbanError'],'ephemeral':!![]});}}const MAX_DISCORD_TIMEOUT=0x1c*0x18*0x3c*0x3c*0x3e8;function _0x3928(){const _0x13384d=['guild','SlowmodeSuccess','bans','all','{shorttime}','ban','replace','Unwarn','1734535oCTaNZ','View\x20a\x20user\x27s\x20history','customId','setRateLimitPerUser','unwarn','Timeout','getString','2311150vYUYNi','Footer','findOneAndUpdate','Temporarily\x20ban\x20a\x20user','Enabled','The\x20user\x20to\x20kick','The\x20user\x20to\x20list\x20warnings\x20for','Error','The\x20reason\x20for\x20the\x20warn','some','./config.yml','CantBanUser','text','PrivateThread','Warnings','fetch','6bUqUHR','embeds','addNumberOption','NicknameChangeSuccess','CantBanSelf','TimeoutRemoved','`).\x0a\x0a**Reason\x20for\x20Removal:**\x20','timeout','There\x20was\x20an\x20error\x20trying\x20to\x20execute\x20that\x20command!\x20Please\x20try\x20again\x20later.','The\x20role\x20to\x20assign','The\x20user\x20to\x20timeout','Thumbnail','ErrorEmbedColor','splice','reply','setTitle','Danger','userid','map','load','reason','The\x20user\x20to\x20unwarn','startsWith','ClearChannel','moderatorTag','\x20(`','clone','size','set','The\x20user\x20to\x20view\x20history','slowmode','Error\x20sending\x20DM\x20to\x20user:','Warning\x20Removed','setName','The\x20reason\x20for\x20the\x20unban','NoteCantAddBot','Kick','Nickname','./lang.yml','Reason\x20for\x20the\x20ban','joinedAt','remove','1245335nJnGdU','note','{totalWarnings}','addFields','HistoryEmbedNote','EntryFormat','KickSuccess','All','Slowmode\x20Error:','This\x20user\x20has\x20no\x20warnings.','Next','DD/MM/YYYY','Failed\x20to\x20set\x20permissions\x20in\x20channel\x20','timeouts','next','The\x20user\x20to\x20ban','save','Confirm','ErrorEmbedTitle','bot','The\x20reason\x20for\x20the\x20kick','MMMM\x20Do\x20YYYY','kicks','Ban','Success','WarnLogs','setRequired','SendMessages','ClearTimeout','{user}','PublicThread','Staff','Set\x20slowmode\x20in\x20a\x20channel','Note:\x20Unable\x20to\x20send\x20DM\x20to\x20user.','\x20warnings.','setImage','channels','\x20has\x20been\x20timed\x20out\x20for\x20','GuildAnnouncement','{role}','The\x20reason\x20for\x20the\x20timeout','Color','Description','List\x20warnings\x20for\x20a\x20user','\x20due\x20to\x20reaching\x20','TempBan','deferReply','highest','Kick\x20a\x20user','Image','The\x20user\x20to\x20clear\x20history\x20from','user_id','edit','UnbanMsg','SameOrHigherRoleError','SuccessEmbedTitle','push','moderatorId','ModerationEmbedTitle','position','function','previous','cleartimeout','Slowmode\x20time\x20in\x20seconds\x20(1-21600\x20Seconds),\x20Set\x20to\x200\x20to\x20disable.','getUser','longtime','Secondary','HistoryEmbedWarnings','min','>\x0a``','Unban','27ljoZFX','Error\x20handling\x20button\x20interaction:','Unban\x20Command\x20Error:','The\x20Discord\x20ID\x20of\x20the\x20user\x20to\x20ban','Moderation\x20commands','AddReactions','addSubcommand','bulkDelete','UserNotFound','moderator','None','kickable','exec','nickname','author','>\x20(','tempBans','setTimestamp','1314194VPYNak','RoleAssigned','setAuthor','Reached\x20','NoteLongerThan250','NicknameChangeFailure','NoPermsMessage','History','username','Kick\x20execution\x20failed:','Ban\x20a\x20user\x20by\x20mention\x20or\x20ID','1927090GpaSAw','addChoices','Set\x20a\x20note\x20on\x20a\x20user','caseNumber','The\x20user\x20to\x20set\x20the\x20note\x20on','time','GuildStageVoice','totalMessages','KickLogs','channel','GuildVoice','ClearhistorySuccess','Unban\x20a\x20user','Assign\x20a\x20temporary\x20role\x20to\x20a\x20user','delete','{duration}','<@!','Previous','clearchannel','setLabel','No\x20reason\x20provided','create','readFileSync','Speak','Embeds','length','get','SuccessEmbedColor','addComponents','HistoryEmbedBans','displayAvatarURL','Error\x20applying\x20timeout\x20to\x20user\x20','MissingPermissionsError','max','SlowmodeFailed','exports','Error\x20setting\x20note:','setFooter','moderation','shorttime','``\x20<@!','contentType','ModerationRoles','SendBanDM\x20Error:','history','links','message','setnote','setStyle','Icon','messages','Bots','setCustomId','The\x20note\x20to\x20set\x20on\x20the\x20user','js-yaml','Timeout\x20a\x20user','Purge\x20specific\x20messages\x20in\x20a\x20channel','cache','members','permissions','Text','Cancel','warning_id','setColor','Clear\x20a\x20user\x27s\x20history','bannable','``\x20','collect','CannotKickUser','warnings','setDescription','attachments','Purged\x20Type','GuildText','WarnList','createMessageComponentCollector','image/','HistoryEmbedUserInfo','{longtime}','KickError','type','User\x20','#FFA500','addIntegerOption','getInteger','How\x20long\x20the\x20user\x20should\x20be\x20timed\x20out,\x20for\x20example:\x201d,\x201h,\x201m,\x20or\x20\x22perm\x22\x20to\x20perm\x20mute','Channel','Invalid\x20duration\x20format\x20for\x20punishment:\x20','There\x20was\x20an\x20error\x20setting\x20the\x20note.','setPosition','toLocaleString','tag','A\x20warning\x20was\x20removed\x20from\x20','Flags','Failed\x20to\x20set\x20nickname:','Remove\x20a\x20warning\x20from\x20a\x20user','Embed','unban','duration','NoWarnings','clearhistory','Ban\x20duration\x20(e.g.,\x201d\x202h\x2015m)','Purge','images','Purge\x20Error:','UserOptionDescription','format','../../models/guildDataSchema','users','amount','27504114zhXBdB','content','isChatInputCommand','guildName','Administrator','kickLogCache','toLowerCase','Error\x20in\x20executeTemprole:','PurgeCleared','toDate','replied','addStringOption','has','The\x20user\x27s\x20Discord\x20ID','Clear\x20Timeout\x20Error:','ClearChannelGif','roles','The\x20user\x20to\x20assign\x20the\x20role\x20to','timeoutRoleId','name','Author','discord.js','ErrorAssigningRole','kick','join','tempban','addRoleOption','client','https://i.imgur.com/FxQkyLb.png','catch','userTag','64VYulKB','options','ClearChannelPrompt','760LPYTWC','getSubcommand','The\x20user\x20ID\x20to\x20ban','png','Failed\x20to\x20apply\x20timeout\x20to\x20','moderatorName','HH:mm','seconds','Remove\x20timeout\x20from\x20a\x20user','ModLogsChannelID','comparePositionTo','Temprole','Punishments','HistoryEmbedTimeouts','warn','isArray','includes','CancelClear','code','Primary','purge','utf8','parse-duration','Moderation\x20Action','confirmClear','warns','UserNotFoundInGuild','setDisabled','Timezone','../../models/UserData','perm','\x0aNote:\x20Unable\x20to\x20send\x20DM\x20to\x20user.','findOne','InvalidTime','setTime','log','addUserOption','user','Warn','filter','now','#808080','temprole','UnbanUserNotBanned','https://i.imgur.com/7SlmRRa.png','NoteSuccess','{userTag}','values','SetNote','Title','UnknownRoleError','add','getRole','LogsChannelID','member','\x0a``','date','end','Delete\x20all\x20messages\x20in\x20a\x20channel','userName','SlowmodeReset','AnnouncementThread','error','HistoryEmbedName','setThumbnail','{index}','UnbanLogs','bots','PurgeOld','{time}','BanLogs','PurgeLogChannel','slice','permissionOverwrites','BotOrSelf','InvalidWarningID','cancelClear','{reason}','send','editReply','23151YrfCtp','role'];_0x3928=function(){return _0x13384d;};return _0x3928();}async function executeTimeout(_0x43dc0e){await _0x43dc0e['deferReply']({'ephemeral':!![]});if(!hasTimeoutPermission(_0x43dc0e))return _0x43dc0e['editReply']({'content':lang['NoPermsMessage'],'ephemeral':!![]});const _0x5406de=_0x43dc0e[_0x29ee96(0x2f6,0x330)][_0x29ee96(0x1db,0x27e)](_0x29ee96(0x2c1,0x357));function _0x29ee96(_0x2101f5,_0x2c82be){return _0x2bfafe(_0x2101f5,_0x2c82be- -0x1ae);}const _0x31a28e=_0x43dc0e[_0x29ee96(0x38c,0x330)][_0x29ee96(0x3bb,0x392)](_0x29ee96(0x36d,0x2a7)),_0x1776bf=_0x43dc0e[_0x29ee96(0x29e,0x330)][_0x29ee96(0x2cd,0x392)](_0x29ee96(0x301,0x3b7)),_0x5f41a7=await _0x43dc0e[_0x29ee96(0x3fa,0x384)][_0x29ee96(0x30d,0x2dc)][_0x29ee96(0x2db,0x3a2)](_0x5406de['id'])['catch'](()=>null);if(!_0x5f41a7)return _0x43dc0e['editReply']({'content':lang[_0x29ee96(0x3e6,0x391)][_0x29ee96(0x2e0,0x28d)],'ephemeral':!![]});const _0x5dedde=parseTimeInput(_0x31a28e);if(_0x5dedde===null)return _0x43dc0e[_0x29ee96(0x3e1,0x381)]({'content':lang[_0x29ee96(0x446,0x391)][_0x29ee96(0x348,0x353)],'ephemeral':!![]});try{const _0x74de5b=await GuildData['findOne']({'guildID':_0x43dc0e[_0x29ee96(0x346,0x384)]['id']});if(_0x5dedde===Infinity)await applyMutedRole(_0x43dc0e,_0x5f41a7,_0x1776bf),await _0x43dc0e[_0x29ee96(0x40d,0x381)]({'content':lang[_0x29ee96(0x3d1,0x391)]['PermanentMuteSuccess'][_0x29ee96(0x2f5,0x38a)](_0x29ee96(0x413,0x3ea),_0x5f41a7[_0x29ee96(0x39c,0x357)][_0x29ee96(0x2e6,0x2fd)])[_0x29ee96(0x44c,0x38a)](_0x29ee96(0x2d1,0x37f),_0x1776bf),'ephemeral':!![]});else{if(_0x5dedde<=MAX_DISCORD_TIMEOUT)await _0x5f41a7[_0x29ee96(0x351,0x3aa)](_0x5dedde,_0x1776bf),await _0x43dc0e[_0x29ee96(0x3cb,0x381)]({'content':lang[_0x29ee96(0x345,0x391)][_0x29ee96(0x35b,0x3e5)][_0x29ee96(0x3b3,0x38a)](_0x29ee96(0x444,0x3ea),_0x5f41a7['user'][_0x29ee96(0x36c,0x2fd)])['replace'](_0x29ee96(0x366,0x377),_0x31a28e)['replace']('{reason}',_0x1776bf),'ephemeral':!![]});else{await applyMutedRole(_0x43dc0e,_0x5f41a7,_0x1776bf);const _0x30acdd=new Date(Date[_0x29ee96(0x3d1,0x35a)]()+_0x5dedde);await scheduleRoleRemoval(_0x5f41a7,_0x30acdd,_0x74de5b),await _0x43dc0e[_0x29ee96(0x314,0x381)]({'content':lang[_0x29ee96(0x3b8,0x391)]['LongMuteSuccess'][_0x29ee96(0x445,0x38a)](_0x29ee96(0x32e,0x3ea),_0x5f41a7[_0x29ee96(0x337,0x357)][_0x29ee96(0x299,0x2fd)])[_0x29ee96(0x327,0x38a)](_0x29ee96(0x306,0x377),_0x31a28e)[_0x29ee96(0x446,0x38a)](_0x29ee96(0x37c,0x37f),_0x1776bf),'ephemeral':!![]});}}}catch(_0x51e4e5){console[_0x29ee96(0x3b1,0x370)]('Timeout\x20Error:',_0x51e4e5),await _0x43dc0e[_0x29ee96(0x2cb,0x381)]({'content':lang[_0x29ee96(0x407,0x391)][_0x29ee96(0x400,0x39a)],'ephemeral':!![]});}}async function executeClearTimeout(_0x199630){await _0x199630[_0x2b4f0b(-0xeb,-0x184)]({'ephemeral':!![]});function _0x2b4f0b(_0x4cfe70,_0x33bb8f){return _0x2bfafe(_0x4cfe70,_0x33bb8f- -0x59e);}if(!hasClearTimeoutPermission(_0x199630))return _0x199630['editReply']({'content':lang['NoPermsMessage'],'ephemeral':!![]});const _0x4c2263=_0x199630[_0x2b4f0b(-0x1b,-0xc0)][_0x2b4f0b(-0xe2,-0x172)](_0x2b4f0b(-0x9d,-0x99)),_0x53fb93=await _0x199630[_0x2b4f0b(-0x2f,-0x6c)][_0x2b4f0b(-0x7f,-0x114)]['fetch'](_0x4c2263['id'])['catch'](()=>null);if(!_0x53fb93)return _0x199630[_0x2b4f0b(0x1a,-0x6f)]({'content':lang['ClearTimeout'][_0x2b4f0b(-0x123,-0x163)],'ephemeral':!![]});try{const _0x202d00=await removeMutedRole(_0x53fb93);await _0x53fb93[_0x2b4f0b(0x44,-0x46)](null);const _0x1c7aab=_0x202d00?'MuteRemoved':_0x2b4f0b(-0x67,-0x48);await _0x199630[_0x2b4f0b(0x9,-0x6f)]({'content':lang[_0x2b4f0b(0x59,-0x7)][_0x1c7aab]['replace'](_0x2b4f0b(0xac,-0x6),_0x53fb93[_0x2b4f0b(-0x48,-0x99)][_0x2b4f0b(-0x11f,-0xf3)]),'ephemeral':!![]});}catch(_0x5635cb){console[_0x2b4f0b(-0x2e,-0x80)](_0x2b4f0b(-0x7d,-0xd2),_0x5635cb),await _0x199630[_0x2b4f0b(-0x124,-0x6f)]({'content':lang['ClearTimeout'][_0x2b4f0b(-0x9a,-0x56)],'ephemeral':!![]});}}function hasTimeoutPermission(_0x12cb4a){const _0xc2a63a=config[_0x3a671a(0x1a5,0x125)][_0x3a671a(0x2b6,0x203)];function _0x3a671a(_0x3a71f7,_0x442730){return _0x2bfafe(_0x3a71f7,_0x442730- -0x355);}return hasPermission(_0x12cb4a,_0xc2a63a);}function hasClearTimeoutPermission(_0x106d11){const _0x5ef4e7=config['ModerationRoles']['cleartimeout'];return hasPermission(_0x106d11,_0x5ef4e7);}function hasPermission(_0xdc7a7,_0x147a23){const _0x4ff0af=_0x147a23['some'](_0x55c660=>_0xdc7a7[_0x3ecbb0(0x13d,0x1c6)]['roles'][_0x3ecbb0(0x88,0x139)][_0x3ecbb0(0x128,0x17a)](_0x55c660)),_0x1e8176=_0xdc7a7['member'][_0x3ecbb0(0x85,0x13b)][_0x3ecbb0(0x187,0x17a)](PermissionsBitField[_0x3ecbb0(0x18d,0x15d)]['Administrator']);function _0x3ecbb0(_0x443bfe,_0x55ff27){return _0x2bfafe(_0x443bfe,_0x55ff27- -0x350);}return _0x4ff0af||_0x1e8176;}function parseTimeInput(_0x32822a){if(_0x32822a[_0x540c1c(0x43c,0x472)]()===_0x540c1c(0x476,0x44e))return Infinity;const _0x7fd014=ms(_0x32822a);function _0x540c1c(_0x224204,_0x2df0a8){return _0x2bfafe(_0x2df0a8,_0x224204- -0x88);}return _0x7fd014&&_0x7fd014>=0x2710?_0x7fd014:null;}async function applyMutedRole(_0x25d71e,_0x3160cc,_0x32b918){const _0x4a29ea=await getOrCreateTimeoutRole(_0x25d71e[_0x22b7d8(0x109,0xcf)]);function _0x22b7d8(_0xf13e16,_0x9f599d){return _0x2bfafe(_0xf13e16,_0x9f599d- -0x463);}await _0x3160cc[_0x22b7d8(0xb3,0x6b)]['add'](_0x4a29ea,_0x32b918),await UserData[_0x22b7d8(0x168,0xe0)]({'userId':_0x3160cc['id'],'guildId':_0x25d71e[_0x22b7d8(0xc5,0xcf)]['id']},{'isMuted':!![]},{'upsert':!![]});}async function getOrCreateTimeoutRole(_0x23fe11){let _0x494a8b=await GuildData[_0x42bbb6(0x442,0x405)]({'guildID':_0x23fe11['id']});!_0x494a8b&&(_0x494a8b=new GuildData({'guildID':_0x23fe11['id']}));let _0x4628b5;_0x494a8b[_0x42bbb6(0x40d,0x3d5)]&&(_0x4628b5=_0x23fe11[_0x42bbb6(0x3b8,0x3d3)][_0x42bbb6(0x3fa,0x38e)]['get'](_0x494a8b[_0x42bbb6(0x41d,0x3d5)]));!_0x4628b5&&(_0x4628b5=await createMutedRole(_0x23fe11),_0x494a8b['timeoutRoleId']=_0x4628b5['id'],await _0x494a8b[_0x42bbb6(0x426,0x490)]());function _0x42bbb6(_0x18b69c,_0x501e25){return _0x2bfafe(_0x18b69c,_0x501e25- -0xfb);}return _0x4628b5;}async function createMutedRole(_0x2b12ea){const _0x36d955=await _0x2b12ea[_0xcbb799(0x3c9,0x325)][_0xcbb799(0x360,0x34b)]({'name':'Muted','color':_0xcbb799(0x404,0x44a),'permissions':[]});function _0xcbb799(_0xf81366,_0x631692){return _0x2bfafe(_0x631692,_0xf81366- -0x105);}return await setMutedRolePermissions(_0x2b12ea,_0x36d955),_0x36d955;}async function removeMutedRole(_0x41436a){const _0x31d2c3=await GuildData[_0x30736d(0xdd,0x84)]({'guildID':_0x41436a[_0x30736d(0x10f,0x16b)]['id']});function _0x30736d(_0xb61879,_0x4e8088){return _0x2bfafe(_0x4e8088,_0xb61879- -0x423);}if(!_0x31d2c3['timeoutRoleId'])return![];const _0x44a4d5=_0x41436a['guild']['roles']['cache'][_0x30736d(0x47,0xbb)](_0x31d2c3[_0x30736d(0xad,0xd3)]);if(!_0x44a4d5)return![];if(_0x41436a[_0x30736d(0xab,0x9c)]['cache']['has'](_0x44a4d5['id']))return await _0x41436a[_0x30736d(0xab,0x168)][_0x30736d(0x157,0xcb)](_0x44a4d5),await UserData[_0x30736d(0x120,0x5c)]({'userId':_0x41436a['id'],'guildId':_0x41436a[_0x30736d(0x10f,0x90)]['id']},{'isMuted':![]}),!![];return![];}async function scheduleRoleRemoval(_0x563ae6,_0x5e82e7,_0x4d60f4){if(!_0x4d60f4||!_0x4d60f4[_0x244710(-0x5b,-0x78)])return;function _0x244710(_0x2481bb,_0x6beb47){return _0x2bfafe(_0x2481bb,_0x6beb47- -0x548);}if(_0x5e82e7===Infinity)return;await TempRole[_0x244710(0x7,-0x5)]({'userId':_0x563ae6['id'],'guildId':_0x563ae6[_0x244710(0x35,-0x16)]['id'],'roleId':_0x4d60f4[_0x244710(-0x83,-0x78)]},{'expiration':_0x5e82e7},{'upsert':!![]});}async function setMutedRolePermissions(_0x4e2992,_0x45596d){function _0x25a2d5(_0x2241d3,_0x294007){return _0x2bfafe(_0x2241d3,_0x294007-0xac);}const _0x2a22dc=_0x4e2992[_0x25a2d5(0x56c,0x4bc)][_0x25a2d5(0x537,0x535)][_0x25a2d5(0x525,0x5b3)](_0x5c1ff9=>[ChannelType[_0x25a2d5(0x517,0x545)],ChannelType[_0x25a2d5(0x46f,0x506)],ChannelType[_0x25a2d5(0x558,0x502)],ChannelType[_0x25a2d5(0x4bc,0x4be)],ChannelType[_0x25a2d5(0x687,0x5c9)],ChannelType[_0x25a2d5(0x5d1,0x645)],ChannelType[_0x25a2d5(0x651,0x5fa)]][_0x25a2d5(0x55f,0x59c)](_0x5c1ff9[_0x25a2d5(0x4db,0x54c)]));for(const [_0x4e973a,_0x2678a8]of _0x2a22dc){try{const _0x2eb29a={};[ChannelType[_0x25a2d5(0x506,0x545)],ChannelType[_0x25a2d5(0x514,0x4be)],ChannelType[_0x25a2d5(0x56f,0x5c9)],ChannelType[_0x25a2d5(0x6bb,0x645)],ChannelType[_0x25a2d5(0x5ba,0x5fa)]]['includes'](_0x2678a8[_0x25a2d5(0x56f,0x54c)])&&(_0x2eb29a[PermissionsBitField[_0x25a2d5(0x497,0x559)][_0x25a2d5(0x6ad,0x642)]]=![],_0x2eb29a[PermissionsBitField['Flags'][_0x25a2d5(0x425,0x4e4)]]=![]);[ChannelType[_0x25a2d5(0x46c,0x506)],ChannelType[_0x25a2d5(0x5ac,0x502)]][_0x25a2d5(0x5dc,0x59c)](_0x2678a8[_0x25a2d5(0x5e2,0x54c)])&&(_0x2eb29a[PermissionsBitField['Flags'][_0x25a2d5(0x558,0x513)]]=![]);if(_0x2678a8[_0x25a2d5(0x5a7,0x5d5)]&&typeof _0x2678a8['permissionOverwrites'][_0x25a2d5(0x41a,0x4cc)]===_0x25a2d5(0x4ba,0x4d4))await _0x2678a8['permissionOverwrites'][_0x25a2d5(0x57c,0x4cc)](_0x45596d,_0x2eb29a);else{}}catch(_0x4288fe){console['error'](_0x25a2d5(0x5b2,0x633)+_0x4e973a+'\x20('+_0x2678a8[_0x25a2d5(0x58b,0x54c)]+'):',_0x4288fe);}}}async function executeWarn(_0x4dad2a){await _0x4dad2a[_0x323107(0x300,0x3a7)]({'ephemeral':!![]});const _0x2a1aa4=config[_0x323107(0x49f,0x407)]['warn'],_0x2bdd4f=_0x2a1aa4['some'](_0x277474=>_0x4dad2a[_0x323107(0x4de,0x4a3)][_0x323107(0x454,0x45b)][_0x323107(0x4ad,0x416)][_0x323107(0x4a6,0x457)](_0x277474)),_0x40fc61=_0x4dad2a[_0x323107(0x495,0x4a3)][_0x323107(0x3bc,0x418)][_0x323107(0x4e4,0x457)](PermissionsBitField[_0x323107(0x4f0,0x43a)][_0x323107(0x448,0x44f)]);if(!_0x2bdd4f&&!_0x40fc61)return _0x4dad2a[_0x323107(0x474,0x4bc)]({'content':lang[_0x323107(0x498,0x493)][_0x323107(0x435,0x3d8)],'ephemeral':!![]});const _0xacc137=_0x4dad2a[_0x323107(0x4f7,0x46b)][_0x323107(0x359,0x3b9)](_0x323107(0x4a3,0x492)),_0x2f8c30=_0x4dad2a[_0x323107(0x3c7,0x46b)]['getString'](_0x323107(0x489,0x4f2)),_0x9abe2a=_0x4dad2a[_0x323107(0x47e,0x4bf)]['members'][_0x323107(0x442,0x416)][_0x323107(0x428,0x3f7)](_0xacc137['id']);function _0x323107(_0x312014,_0x3d02b){return _0x2bfafe(_0x312014,_0x3d02b- -0x73);}if(!_0x9abe2a||_0x9abe2a[_0x323107(0x446,0x492)][_0x323107(0x5ad,0x51b)]||_0xacc137['id']===_0x4dad2a[_0x323107(0x555,0x492)]['id'])return _0x4dad2a[_0x323107(0x531,0x4bc)]({'content':lang[_0x323107(0x51f,0x493)][_0x323107(0x487,0x4b7)],'ephemeral':!![]});try{const _0x25c9e0=await updateWarningCount(_0x9abe2a,_0x2f8c30,_0x4dad2a);await warnMember(_0x9abe2a,_0x2f8c30,_0x4dad2a);const _0x2200fe={'user':'<@'+_0x9abe2a['id']+'>','userName':_0x9abe2a[_0x323107(0x3ef,0x492)][_0x323107(0x315,0x3da)],'userTag':_0x9abe2a[_0x323107(0x48d,0x492)][_0x323107(0x3d7,0x438)],'userId':_0x9abe2a['id'],'moderator':'<@'+_0x4dad2a['user']['id']+'>','moderatorName':_0x4dad2a[_0x323107(0x3f0,0x492)][_0x323107(0x443,0x3da)],'moderatorTag':_0x4dad2a['user'][_0x323107(0x4ee,0x438)],'moderatorId':_0x4dad2a[_0x323107(0x517,0x492)]['id'],'reason':_0x2f8c30,'shorttime':moment()['tz'](config[_0x323107(0x49c,0x489)])[_0x323107(0x384,0x447)](_0x323107(0x527,0x473)),'longtime':moment()['tz'](config[_0x323107(0x522,0x489)])[_0x323107(0x3c7,0x447)]('MMMM\x20Do\x20YYYY'),'caseNumber':_0x25c9e0[_0x323107(0x44a,0x422)][_0x323107(0x3ee,0x3f6)]};_0x4dad2a['editReply']({'content':replacePlaceholders(lang[_0x323107(0x4fd,0x493)][_0x323107(0x534,0x520)],_0x2200fe),'ephemeral':!![]});}catch(_0x44263e){console[_0x323107(0x4db,0x4ab)]('Error\x20warning\x20user:',_0x44263e),_0x4dad2a[_0x323107(0x42b,0x4bc)]({'content':lang[_0x323107(0x3eb,0x493)][_0x323107(0x461,0x4d5)],'ephemeral':!![]});}}async function executeWarnList(_0x371446){await _0x371446[_0xd4d1e6(0x128,0x1cc)]({'ephemeral':!![]});function _0xd4d1e6(_0x257c65,_0x4d9100){return _0x2bfafe(_0x4d9100,_0x257c65- -0x2f2);}const _0xfb085e=_0x371446[_0xd4d1e6(0x1ec,0x253)][_0xd4d1e6(0x13a,0x111)](_0xd4d1e6(0x213,0x231));try{const _0x2de02a=await UserData[_0xd4d1e6(0x20e,0x20e)]({'userId':_0xfb085e['id'],'guildId':_0x371446[_0xd4d1e6(0x240,0x2f6)]['id']});if(_0x2de02a&&_0x2de02a[_0xd4d1e6(0x1a3,0x14c)][_0xd4d1e6(0x177,0x135)]>0x0){const _0x1b4981=Math['ceil'](_0x2de02a['warnings']['length']/MAX_WARNINGS_PER_PAGE);let _0x36d984=0x0;const _0x22df10=async _0x5b9a05=>{function _0x28927c(_0x18a5ab,_0x5e59cf){return _0xd4d1e6(_0x18a5ab-0x3d4,_0x5e59cf);}const _0x44f598=_0x5b9a05*MAX_WARNINGS_PER_PAGE,_0x6af10f=_0x44f598+MAX_WARNINGS_PER_PAGE,_0x2e8626=_0x2de02a[_0x28927c(0x577,0x625)]['slice'](_0x44f598,_0x6af10f),_0x4455ed=_0x2e8626[_0x28927c(0x645,0x58b)]((_0x3a9399,_0x50d971)=>{const _0xdab50f=moment(_0x3a9399[_0x1e8a9e(0x64f,0x613)])[_0x1e8a9e(0x5d4,0x5b5)](_0x1e8a9e(0x5f3,0x68b));function _0x1e8a9e(_0x40fd3e,_0x1b8b42){return _0x28927c(_0x1b8b42-0x19,_0x40fd3e);}const _0x11a34c=moment(_0x3a9399['date'])['format']('HH:mm');return lang['WarnList'][_0x1e8a9e(0x63d,0x5ab)][_0x1e8a9e(0x6ca,0x67b)][_0x1e8a9e(0x690,0x65e)](_0xef5459=>_0xef5459['replace'](_0x1e8a9e(0x6c0,0x61c),_0x44f598+_0x50d971+0x1)[_0x1e8a9e(0x5e1,0x633)](_0x1e8a9e(0x5a5,0x599),_0xdab50f)[_0x1e8a9e(0x642,0x633)](_0x1e8a9e(0x612,0x631),_0x11a34c)['replace']('{reason}',_0x3a9399['reason'])['replace']('{moderatorId}',_0x3a9399[_0x1e8a9e(0x490,0x520)]))[_0x1e8a9e(0x5a2,0x5d1)]('\x0a');})[_0x28927c(0x5b8,0x5d2)]('\x0a\x0a'),_0x492dd2=new EmbedBuilder()[_0x28927c(0x642,0x68d)](lang['WarnList'][_0x28927c(0x592,0x53d)]['Title']['replace']('{userName}',_0xfb085e[_0x28927c(0x52f,0x532)]))['setDescription'](_0x4455ed)[_0x28927c(0x571,0x4f5)](lang[_0x28927c(0x57c,0x4bb)][_0x28927c(0x592,0x4e4)][_0x28927c(0x4f7,0x537)])[_0x28927c(0x557,0x53c)]({'text':lang[_0x28927c(0x57c,0x4e5)][_0x28927c(0x592,0x505)][_0x28927c(0x624,0x5a8)][_0x28927c(0x61a,0x65d)](_0x28927c(0x65f,0x645),_0x2de02a[_0x28927c(0x577,0x5ee)][_0x28927c(0x54b,0x5fb)])});lang[_0x28927c(0x57c,0x616)][_0x28927c(0x592,0x62b)][_0x28927c(0x63e,0x61d)]&&_0x492dd2['setThumbnail'](_0xfb085e[_0x28927c(0x550,0x4b3)]());const _0x2c14c6=new ActionRowBuilder()[_0x28927c(0x54e,0x4ab)](new ButtonBuilder()[_0x28927c(0x566,0x52a)](_0x28927c(0x50b,0x557))[_0x28927c(0x545,0x532)](_0x28927c(0x543,0x597))[_0x28927c(0x562,0x5fc)](ButtonStyle[_0x28927c(0x5d5,0x68c)])[_0x28927c(0x5dd,0x673)](_0x5b9a05===0x0),new ButtonBuilder()[_0x28927c(0x566,0x567)]('next')['setLabel'](_0x28927c(0x667,0x5a4))['setStyle'](ButtonStyle[_0x28927c(0x5d5,0x514)])[_0x28927c(0x5dd,0x5b5)](_0x5b9a05===_0x1b4981-0x1));await _0x371446[_0x28927c(0x611,0x549)]({'embeds':[_0x492dd2],'components':[_0x2c14c6]});};await _0x22df10(_0x36d984);const _0x270457=_0x1bed7f=>[_0xd4d1e6(0x137,0x19b),_0xd4d1e6(0x297,0x29c)][_0xd4d1e6(0x1fe,0x27e)](_0x1bed7f[_0xd4d1e6(0x24a,0x192)])&&_0x1bed7f[_0xd4d1e6(0x213,0x259)]['id']===_0x371446[_0xd4d1e6(0x213,0x1a3)]['id'],_0x1829f3=_0x371446[_0xd4d1e6(0x167,0xc1)][_0xd4d1e6(0x1a9,0xed)]({'filter':_0x270457,'time':0xea60});_0x1829f3['on'](_0xd4d1e6(0x1a1,0x18c),async _0x494613=>{function _0x12c323(_0x3d9e1a,_0x421857){return _0xd4d1e6(_0x421857- -0x24c,_0x3d9e1a);}try{await _0x494613['deferUpdate']();if(_0x494613[_0x12c323(-0x98,-0x2)]===_0x12c323(-0x7f,-0x115)&&_0x36d984>0x0)_0x36d984--;else _0x494613['customId']==='next'&&_0x36d984<_0x1b4981-0x1&&_0x36d984++;await _0x22df10(_0x36d984);}catch(_0x3b7669){console['error'](_0x12c323(-0x181,-0x10a),_0x3b7669);}}),_0x1829f3['on'](_0xd4d1e6(0x227,0x212),()=>_0x371446[_0xd4d1e6(0x23d,0x299)]({'components':[]}));}else{const _0xcfe91d=lang[_0xd4d1e6(0x1a8,0xee)][_0xd4d1e6(0x1c1,0x1bc)][_0xd4d1e6(0x246,0x2ce)]('{userName}',_0xfb085e['username'])||_0xd4d1e6(0x292,0x1ee);await _0x371446[_0xd4d1e6(0x23d,0x280)]({'content':_0xcfe91d});}}catch(_0x9685b9){console[_0xd4d1e6(0x22c,0x1a1)]('Error\x20listing\x20warnings:',_0x9685b9),await _0x371446['editReply']({'content':lang[_0xd4d1e6(0x1a8,0x1a4)]['Error'],'ephemeral':!![]});}}async function executeUnwarn(_0x51aade){const _0x2dead1=config[_0x58ac66(0x18a,0x167)]['unwarn'],_0x208fc6=_0x2dead1[_0x58ac66(0x25a,0x209)](_0xbd932=>_0x51aade[_0x58ac66(0x226,0x249)]['roles']['cache']['has'](_0xbd932));function _0x58ac66(_0x350847,_0xd951c7){return _0x2bfafe(_0xd951c7,_0x350847- -0x2f0);}const _0x2aea4c=_0x51aade['member']['permissions'][_0x58ac66(0x1da,0x1d5)](PermissionsBitField[_0x58ac66(0x1bd,0x162)][_0x58ac66(0x1d2,0x25c)]);if(!_0x208fc6&&!_0x2aea4c)return _0x51aade[_0x58ac66(0x26f,0x27e)]({'content':lang[_0x58ac66(0x15b,0x208)],'ephemeral':!![]});const _0x339b06=_0x51aade[_0x58ac66(0x1ee,0x1d4)][_0x58ac66(0x13c,0x154)](_0x58ac66(0x215,0x201)),_0x50de78=_0x51aade['options'][_0x58ac66(0x1b4,0x238)](_0x58ac66(0x19e,0x185)),_0x31dfa7=await UserData[_0x58ac66(0x210,0x2b4)]({'userId':_0x339b06['id'],'guildId':_0x51aade[_0x58ac66(0x242,0x2da)]['id']});if(!_0x31dfa7||_0x31dfa7[_0x58ac66(0x1a5,0x254)]['length']===0x0)return _0x51aade[_0x58ac66(0x26f,0x331)]({'content':lang[_0x58ac66(0x249,0x293)][_0x58ac66(0x1c3,0x188)],'ephemeral':!![]});if(_0x50de78<0x1||_0x50de78>_0x31dfa7[_0x58ac66(0x1a5,0x26c)][_0x58ac66(0x179,0x12d)])return _0x51aade[_0x58ac66(0x26f,0x319)]({'content':lang['Unwarn'][_0x58ac66(0x23b,0x2b4)],'ephemeral':!![]});const _0x174d14=_0x31dfa7[_0x58ac66(0x1a5,0x16b)][_0x58ac66(0x26e,0x1c0)](_0x50de78-0x1,0x1)[0x0];await _0x31dfa7[_0x58ac66(0x29b,0x24f)](),_0x51aade[_0x58ac66(0x26f,0x32b)]({'content':lang[_0x58ac66(0x249,0x1b1)]['WarningRemoved'][_0x58ac66(0x248,0x182)](_0x58ac66(0x21e,0x252),_0x339b06[_0x58ac66(0x1bb,0x1bf)])[_0x58ac66(0x248,0x20a)](_0x58ac66(0x23d,0x25c),_0x174d14[_0x58ac66(0x275,0x254)]),'ephemeral':!![]});const _0x89e686=_0x51aade[_0x58ac66(0x242,0x2cc)][_0x58ac66(0x120,0x180)][_0x58ac66(0x199,0x1c0)][_0x58ac66(0x17a,0x21b)](config[_0x58ac66(0x1f9,0x1f2)]);if(_0x89e686){const _0x4b5252=new EmbedBuilder()[_0x58ac66(0x270,0x31a)](_0x58ac66(0x281,0x230))['setDescription'](_0x58ac66(0x1bc,0x1a2)+_0x339b06[_0x58ac66(0x1bb,0x226)]+_0x58ac66(0x27a,0x334)+_0x339b06['id']+_0x58ac66(0x267,0x2cb)+_0x174d14[_0x58ac66(0x275,0x247)])[_0x58ac66(0x19f,0x1be)](_0x58ac66(0x1b2,0x158))[_0x58ac66(0x154,0x19e)]();_0x89e686[_0x58ac66(0x23e,0x217)]({'embeds':[_0x4b5252]});}}async function warnMember(_0x5b0705,_0x34a570,_0x183e43){const _0x3a99df=moment()['tz'](config['Timezone']),_0x595eb7=lang[_0x121a7c(0x521,0x51b)][_0x121a7c(0x43d,0x4b1)];let _0xcbb7f7=new EmbedBuilder()[_0x121a7c(0x41c,0x3c1)](_0x595eb7['Color']||_0x121a7c(0x42f,0x36e));const _0x433b36={'user':'<@'+_0x5b0705['id']+'>','userName':_0x5b0705[_0x121a7c(0x492,0x518)][_0x121a7c(0x3da,0x358)],'userTag':_0x5b0705[_0x121a7c(0x492,0x46e)]['tag'],'userId':_0x5b0705['id'],'moderator':'<@'+_0x183e43[_0x121a7c(0x492,0x451)]['id']+'>','moderatorName':_0x183e43[_0x121a7c(0x492,0x3cb)][_0x121a7c(0x3da,0x3a9)],'moderatorTag':_0x183e43['user']['tag'],'moderatorId':_0x183e43[_0x121a7c(0x492,0x46f)]['id'],'reason':_0x34a570,'shorttime':_0x3a99df[_0x121a7c(0x447,0x4b2)](_0x121a7c(0x473,0x48c)),'longtime':_0x3a99df[_0x121a7c(0x447,0x37f)](_0x121a7c(0x51d,0x54a))};_0x595eb7['Title']&&_0xcbb7f7[_0x121a7c(0x4ed,0x55a)](replacePlaceholders(_0x595eb7['Title'],_0x433b36));_0x595eb7[_0x121a7c(0x3a3,0x421)]['length']>0x0&&_0xcbb7f7[_0x121a7c(0x423,0x473)](_0x595eb7['Description']['map'](_0x57c65e=>replacePlaceholders(_0x57c65e,_0x433b36))[_0x121a7c(0x463,0x4c9)]('\x0a'));_0x595eb7[_0x121a7c(0x4cf,0x423)]&&_0x595eb7[_0x121a7c(0x4cf,0x4fe)][_0x121a7c(0x419,0x35c)]&&_0xcbb7f7[_0x121a7c(0x402,0x4b3)]({'text':replacePlaceholders(_0x595eb7[_0x121a7c(0x4cf,0x4c4)][_0x121a7c(0x419,0x4d9)],_0x433b36),'iconURL':_0x595eb7[_0x121a7c(0x4cf,0x4ba)]['Icon']||undefined});function _0x121a7c(_0x1c6e7d,_0x14104b){return _0x2bfafe(_0x14104b,_0x1c6e7d- -0x73);}_0x595eb7['Author']&&_0x595eb7[_0x121a7c(0x45f,0x3b9)][_0x121a7c(0x419,0x371)]&&_0xcbb7f7[_0x121a7c(0x3d4,0x3d2)]({'name':_0x595eb7[_0x121a7c(0x45f,0x3ef)][_0x121a7c(0x419,0x49d)],'iconURL':_0x595eb7[_0x121a7c(0x45f,0x4eb)][_0x121a7c(0x40e,0x431)]||undefined});_0x595eb7[_0x121a7c(0x4e9,0x470)]&&_0xcbb7f7[_0x121a7c(0x4ad,0x562)](_0x5b0705['user'][_0x121a7c(0x3fb,0x34e)]({'format':_0x121a7c(0x470,0x3ad),'dynamic':!![]}));_0x595eb7['Image']&&_0xcbb7f7[_0x121a7c(0x39c,0x2ff)](_0x595eb7[_0x121a7c(0x3aa,0x35a)]);try{await _0x5b0705[_0x121a7c(0x4bb,0x4d8)]({'embeds':[_0xcbb7f7]});}catch(_0x237e32){console['error'](_0x121a7c(0x4fd,0x4b8),_0x237e32);}config[_0x121a7c(0x521,0x5d5)]['Enabled']&&logWarning(_0x183e43,_0x5b0705,_0x34a570,_0x3a99df),await applyPunishment(_0x5b0705,_0x183e43);}async function logWarning(_0xff92a4,_0x10a267,_0x53ab1b,_0x5c47e9){function _0x15c490(_0x4fdfcd,_0x18d6b6){return _0x2bfafe(_0x4fdfcd,_0x18d6b6- -0x4d6);}const _0x268dfb=lang['WarnLogs']['Embed'];let _0x333d62=new EmbedBuilder()[_0x15c490(0x29,-0x47)](_0x268dfb['Color']||_0x15c490(-0x25,-0x34));const _0x2d08b0={'user':'<@'+_0x10a267['id']+'>','userName':_0x10a267['user'][_0x15c490(0x1d,-0x89)],'userTag':_0x10a267['user'][_0x15c490(-0x73,-0x2b)],'userId':_0x10a267['id'],'moderator':'<@'+_0xff92a4[_0x15c490(0xb7,0x2f)]['id']+'>','moderatorName':_0xff92a4['user'][_0x15c490(-0x116,-0x89)],'moderatorTag':_0xff92a4[_0x15c490(0xd1,0x2f)][_0x15c490(-0xcf,-0x2b)],'moderatorId':_0xff92a4['user']['id'],'reason':_0x53ab1b,'shorttime':_0x5c47e9['format'](_0x15c490(-0x95,0x10)),'longtime':_0x5c47e9[_0x15c490(-0x11,-0x1c)](_0x15c490(0xce,0xba))};_0x268dfb[_0x15c490(-0x9,0x3b)]&&_0x333d62[_0x15c490(0xd5,0x8a)](replacePlaceholders(_0x268dfb[_0x15c490(-0x47,0x3b)],_0x2d08b0));_0x268dfb[_0x15c490(-0x28,-0xc0)][_0x15c490(0x4c,-0x6d)]>0x0&&_0x333d62['setDescription'](_0x268dfb[_0x15c490(-0x16d,-0xc0)][_0x15c490(0x14,0x8d)](_0x31c3e4=>replacePlaceholders(_0x31c3e4,_0x2d08b0))[_0x15c490(0x2,0x0)]('\x0a'));_0x268dfb['Footer']&&_0x268dfb[_0x15c490(-0x10,0x6c)][_0x15c490(-0x15,-0x4a)]&&_0x333d62['setFooter']({'text':replacePlaceholders(_0x268dfb[_0x15c490(0xc0,0x6c)][_0x15c490(-0xe,-0x4a)],_0x2d08b0),'iconURL':_0x268dfb[_0x15c490(0x67,0x6c)][_0x15c490(-0xa2,-0x55)]||undefined});_0x268dfb[_0x15c490(0x88,-0x4)]&&_0x268dfb[_0x15c490(0x9b,-0x4)][_0x15c490(0x5b,-0x4a)]&&_0x333d62[_0x15c490(-0x8e,-0x8f)]({'name':_0x268dfb[_0x15c490(-0x68,-0x4)][_0x15c490(-0x71,-0x4a)],'iconURL':_0x268dfb[_0x15c490(-0x9,-0x4)][_0x15c490(-0xc6,-0x55)]||undefined});_0x268dfb[_0x15c490(0x12c,0x86)]&&_0x333d62[_0x15c490(0x83,0x4a)](_0x10a267['user'][_0x15c490(-0x75,-0x68)]({'format':'png','dynamic':!![]}));_0x268dfb[_0x15c490(-0x140,-0xb9)]&&_0x333d62[_0x15c490(-0x29,-0xc7)](_0x268dfb[_0x15c490(-0x2,-0xb9)]);const _0x39e6cb=_0xff92a4[_0x15c490(0xd2,0x5c)][_0x15c490(-0x17c,-0xc6)][_0x15c490(0x48,-0x4d)][_0x15c490(-0x120,-0x6c)](config['WarnLogs']['LogsChannelID']);_0x39e6cb&&_0x39e6cb[_0x15c490(0x105,0x58)]({'embeds':[_0x333d62]});}async function updateWarningCount(_0xc46bd3,_0x26c462,_0x4b3e02){const _0x3dec04={'reason':_0x26c462,'date':new Date(),'moderatorId':_0x4b3e02[_0x1446a2(0x30d,0x2b3)]['id']};function _0x1446a2(_0x3c4804,_0x32f15a){return _0x2bfafe(_0x3c4804,_0x32f15a- -0x252);}const _0x2d1f73=await UserData['findOneAndUpdate']({'userId':_0xc46bd3['id'],'guildId':_0x4b3e02[_0x1446a2(0x25e,0x2e0)]['id']},{'$push':{'warnings':_0x3dec04},'$inc':{'warns':0x1}},{'upsert':!![],'new':!![],'setDefaultsOnInsert':!![]});return _0x2d1f73;}async function applyPunishment(_0x3785b9,_0x15105a){function _0x593589(_0x252e8f,_0xc3df){return _0x2bfafe(_0xc3df,_0x252e8f- -0x254);}const _0x50466c=await UserData['findOne']({'userId':_0x3785b9['id'],'guildId':_0x15105a[_0x593589(0x2de,0x26e)]['id']}),_0x19b30f=_0x50466c['warnings'][_0x593589(0x215,0x2c2)];if(config[_0x593589(0x2fb,0x37d)][_0x593589(0x298,0x20b)][_0x19b30f]&&config['Warnings']['Punishments'][_0x19b30f][_0x593589(0x2eb,0x34a)]){const _0x47e708=config[_0x593589(0x2fb,0x322)]['Punishments'][_0x19b30f][_0x593589(0x2eb,0x2ae)];if(_0x47e708){const _0x4b2b1c=ms(_0x47e708);if(_0x4b2b1c)try{_0x4b2b1c<=0x1c*0x18*0x3c*0x3c*0x3e8?(await _0x3785b9['timeout'](_0x4b2b1c,_0x593589(0x1f4,0x264)+_0x19b30f+_0x593589(0x1ba,0x14c)),await _0x15105a['followUp']({'content':_0x593589(0x24d,0x284)+_0x3785b9[_0x593589(0x2b1,0x20e)][_0x593589(0x257,0x1bb)]+_0x593589(0x1bd,0x216)+_0x47e708+_0x593589(0x1c4,0x19b)+_0x19b30f+'\x20warnings.'})):await applyMutedRole(_0x15105a,_0x3785b9,_0x593589(0x1f4,0x12e)+_0x19b30f+'\x20warnings.');}catch(_0x290785){console['error'](_0x593589(0x21b,0x1a8)+_0x3785b9['user'][_0x593589(0x257,0x1f4)]+':',_0x290785),await _0x15105a['followUp']({'content':_0x593589(0x290,0x2ad)+_0x3785b9[_0x593589(0x2b1,0x20c)][_0x593589(0x257,0x312)]+'.','ephemeral':!![]});}else console[_0x593589(0x2ca,0x382)](_0x593589(0x253,0x283)+_0x47e708);}}}async function executeKick(_0x29dd4b){await _0x29dd4b[_0x37a9d7(0x364,0x30e)]({'ephemeral':!![]});const _0x224cb8=config[_0x37a9d7(0x3d9,0x36e)]['kick'],_0x1379ad=_0x224cb8[_0x37a9d7(0x46e,0x43e)](_0x5e3349=>_0x29dd4b[_0x37a9d7(0x429,0x40a)]['roles'][_0x37a9d7(0x2f9,0x37d)][_0x37a9d7(0x41b,0x3be)](_0x5e3349)),_0x57914c=_0x29dd4b[_0x37a9d7(0x404,0x40a)][_0x37a9d7(0x438,0x37f)][_0x37a9d7(0x394,0x3be)](PermissionsBitField[_0x37a9d7(0x452,0x3a1)][_0x37a9d7(0x44c,0x3b6)]);if(!_0x1379ad&&!_0x57914c)return _0x29dd4b[_0x37a9d7(0x3fd,0x423)]({'content':lang['NoPermsMessage'],'ephemeral':!![]});const _0x19fc31=_0x29dd4b[_0x37a9d7(0x338,0x3d2)][_0x37a9d7(0x271,0x320)](_0x37a9d7(0x435,0x3f9)),_0x39af32=_0x29dd4b[_0x37a9d7(0x386,0x3d2)]['getString'](_0x37a9d7(0x4dd,0x459))||_0x37a9d7(0x401,0x358);function _0x37a9d7(_0x14376b,_0x5c00b3){return _0x2bfafe(_0x14376b,_0x5c00b3- -0x10c);}const _0x19a4f2=_0x29dd4b[_0x37a9d7(0x42d,0x426)]['members'][_0x37a9d7(0x33d,0x37d)]['get'](_0x19fc31['id']);if(!_0x19a4f2)return _0x29dd4b[_0x37a9d7(0x46a,0x423)]({'content':lang[_0x37a9d7(0x506,0x469)][_0x37a9d7(0x4b6,0x3ee)]});if(_0x19fc31['id']===_0x29dd4b[_0x37a9d7(0x343,0x3f9)]['id'])return _0x29dd4b[_0x37a9d7(0x43a,0x423)]({'content':lang['Kick']['CannotKickSelf']});if(!_0x19a4f2[_0x37a9d7(0x2b1,0x332)]||_0x19a4f2[_0x37a9d7(0x488,0x3c2)][_0x37a9d7(0x390,0x30f)][_0x37a9d7(0x41b,0x3de)](_0x29dd4b[_0x37a9d7(0x3ec,0x40a)][_0x37a9d7(0x472,0x3c2)]['highest'])>=0x0)return _0x29dd4b[_0x37a9d7(0x398,0x423)]({'content':lang[_0x37a9d7(0x49e,0x469)][_0x37a9d7(0x372,0x388)]});const _0x146915=await sendKickDM(_0x19a4f2,_0x39af32,_0x29dd4b[_0x37a9d7(0x46e,0x3f9)],_0x29dd4b[_0x37a9d7(0x431,0x426)])[_0x37a9d7(0x32d,0x3cf)](_0x1ec5d4=>{return![];});try{await _0x19a4f2[_0x37a9d7(0x3bf,0x3c9)](_0x39af32);const _0x514d64={'user':'<@'+_0x19a4f2['id']+'>','userName':_0x19a4f2[_0x37a9d7(0x3da,0x3f9)][_0x37a9d7(0x2f3,0x341)],'userTag':_0x19a4f2[_0x37a9d7(0x408,0x3f9)]['tag'],'userId':_0x19a4f2['id'],'moderator':'<@'+_0x29dd4b[_0x37a9d7(0x442,0x3f9)]['id']+'>','moderatorName':_0x29dd4b['user']['username'],'moderatorTag':_0x29dd4b[_0x37a9d7(0x448,0x3f9)]['tag'],'moderatorId':_0x29dd4b[_0x37a9d7(0x370,0x3f9)]['id'],'reason':_0x39af32,'shorttime':moment()['tz'](config['Timezone'])['format'](_0x37a9d7(0x3aa,0x3da)),'longtime':moment()['tz'](config[_0x37a9d7(0x3d6,0x3f0)])[_0x37a9d7(0x338,0x3ae)](_0x37a9d7(0x4ba,0x484))};let _0x22cf22=replacePlaceholders(lang[_0x37a9d7(0x46b,0x469)][_0x37a9d7(0x3d7,0x475)],_0x514d64);return!_0x146915&&(_0x22cf22+=_0x37a9d7(0x497,0x3f3)),kickLogCache[_0x37a9d7(0x4f7,0x461)](_0x19a4f2['id'],{'moderator':_0x29dd4b[_0x37a9d7(0x356,0x3f9)],'reason':_0x39af32,'timestamp':Date[_0x37a9d7(0x43c,0x3fc)]()}),setTimeout(()=>{function _0x3be5fb(_0x3567e1,_0x2629ad){return _0x37a9d7(_0x2629ad,_0x3567e1- -0x207);}kickLogCache[_0x3be5fb(0x14b,0x181)](_0x19a4f2['id']);},0x2710),_0x29dd4b[_0x37a9d7(0x45e,0x423)]({'content':_0x22cf22,'ephemeral':!![]});}catch(_0x15d739){return console['error'](_0x37a9d7(0x322,0x342),_0x15d739),_0x29dd4b['editReply']({'content':lang['Kick'][_0x37a9d7(0x3f7,0x393)]});}}module[_0x2bfafe(0x3d8,0x473)][_0x2bfafe(0x581,0x4c3)]=kickLogCache;async function sendKickDM(_0x4b8251,_0x7277c9,_0x3a1918,_0x23b8fd){function _0x392141(_0xe1e8b1,_0x19910c){return _0x2bfafe(_0x19910c,_0xe1e8b1- -0xe1);}try{if(config[_0x392141(0x377,0x32b)]['DM']['Enabled']){const _0x7fa986=config['KickLogs']['DM'][_0x392141(0x3cf,0x344)],_0x3b6248=moment()['tz'](config[_0x392141(0x41b,0x452)]),_0x239d61={'user':'<@'+_0x4b8251['id']+'>','userName':_0x4b8251['user'][_0x392141(0x36c,0x428)],'userTag':_0x4b8251[_0x392141(0x424,0x473)][_0x392141(0x3ca,0x3d5)],'userId':_0x4b8251['id'],'moderator':'<@'+_0x3a1918['id']+_0x392141(0x361,0x39e)+_0x3a1918[_0x392141(0x3ca,0x475)]+')','reason':_0x7277c9,'guildName':_0x23b8fd[_0x392141(0x3f0,0x42f)],'longtime':_0x3b6248[_0x392141(0x3d9,0x47e)]('MMMM\x20Do\x20YYYY'),'shorttime':_0x3b6248[_0x392141(0x3d9,0x44d)](_0x392141(0x405,0x4a2))},_0x5e80bb=_0x7fa986[_0x392141(0x334,0x2df)]?parseInt(_0x7fa986[_0x392141(0x334,0x2fa)][_0x392141(0x457,0x44f)]('#',''),0x10):0xff5555,_0x496a94=new EmbedBuilder()[_0x392141(0x3ae,0x330)](_0x5e80bb)['setTitle'](replacePlaceholders(_0x7fa986[_0x392141(0x430,0x462)],_0x239d61))[_0x392141(0x3b5,0x3da)](_0x7fa986['Description']['map'](_0x37dd7b=>replacePlaceholders(_0x37dd7b,_0x239d61))['join']('\x0a'));return _0x7fa986['Footer'][_0x392141(0x3ab,0x37c)]&&_0x496a94[_0x392141(0x394,0x30a)]({'text':replacePlaceholders(_0x7fa986[_0x392141(0x461,0x4a8)][_0x392141(0x3ab,0x445)],_0x239d61),'iconURL':_0x7fa986[_0x392141(0x461,0x4b0)][_0x392141(0x3a0,0x320)]||undefined}),await _0x4b8251[_0x392141(0x44d,0x455)]({'embeds':[_0x496a94]}),!![];}}catch(_0x330b84){if(_0x330b84['code']===0xc357)return![];else throw _0x330b84;}return![];}async function executeNickname(_0x180b3){await _0x180b3[_0xabb3d1(0x74,0x7f)]({'ephemeral':![]});const _0x4c7139=config['ModerationRoles'][_0xabb3d1(0x130,0xa5)],_0xee31a=_0x4c7139[_0xabb3d1(0x190,0x1af)](_0x4e0bca=>_0x180b3['member'][_0xabb3d1(0x114,0x133)][_0xabb3d1(0xc6,0xee)][_0xabb3d1(0x162,0x12f)](_0x4e0bca)),_0xa89dc=_0x180b3[_0xabb3d1(0x171,0x17b)][_0xabb3d1(0xe3,0xf0)][_0xabb3d1(0x168,0x12f)](PermissionsBitField[_0xabb3d1(0x160,0x112)]['Administrator']);if(!_0xee31a&&!_0xa89dc)return _0x180b3[_0xabb3d1(0x1b1,0x194)]({'content':lang[_0xabb3d1(0x132,0x1db)][_0xabb3d1(0x13,0xb0)],'ephemeral':!![]});const _0x5750f9=_0x180b3['options'][_0xabb3d1(0xba,0x91)](_0xabb3d1(0x187,0x16a)),_0x5e8b80=_0x180b3[_0xabb3d1(0xf9,0x143)][_0xabb3d1(0xeb,0x1a5)](_0xabb3d1(0x108,0xa5)),_0x337fc2=_0x180b3[_0xabb3d1(0x249,0x197)][_0xabb3d1(0x29,0xef)][_0xabb3d1(0x168,0xee)][_0xabb3d1(0x58,0xcf)](_0x5750f9['id']);if(!_0x337fc2)return _0x180b3[_0xabb3d1(0x16c,0x194)]({'content':lang[_0xabb3d1(0x271,0x1db)][_0xabb3d1(0xd6,0xa0)],'ephemeral':!![]});function _0xabb3d1(_0x143233,_0x1e6d22){return _0x2bfafe(_0x143233,_0x1e6d22- -0x39b);}try{return await _0x337fc2['setNickname'](_0x5e8b80),_0x180b3['editReply']({'content':lang[_0xabb3d1(0x218,0x1db)][_0xabb3d1(0x275,0x1b9)][_0xabb3d1(0x1ab,0x19d)](_0xabb3d1(0x1f7,0x1fd),_0x5750f9['username'])[_0xabb3d1(0xdf,0x19d)]('{nickname}',_0x5e8b80),'ephemeral':!![]});}catch(_0x74f0e){return console[_0xabb3d1(0x1c9,0x183)](_0xabb3d1(0x10b,0x113),_0x74f0e),_0x180b3[_0xabb3d1(0x134,0x194)]({'content':lang[_0xabb3d1(0x13b,0x1db)][_0xabb3d1(0xc1,0xaf)],'ephemeral':!![]});}}async function executeClearHistory(_0x434e9d){await _0x434e9d['deferReply']({'ephemeral':!![]});const _0x2f5ada=config[_0x1ab2b7(-0x135,-0x1ca)][_0x1ab2b7(-0x251,-0x190)],_0x1dfec4=_0x2f5ada[_0x1ab2b7(-0x1ab,-0xfa)](_0x523169=>_0x434e9d[_0x1ab2b7(-0x114,-0x12e)][_0x1ab2b7(-0x139,-0x176)][_0x1ab2b7(-0x23d,-0x1bb)][_0x1ab2b7(-0x1cb,-0x17a)](_0x523169)),_0x1ce3ca=_0x434e9d['member'][_0x1ab2b7(-0x1aa,-0x1b9)][_0x1ab2b7(-0xda,-0x17a)](PermissionsBitField['Flags']['Administrator']);if(!_0x1dfec4&&!_0x1ce3ca)return _0x434e9d['editReply']({'content':lang[_0x1ab2b7(-0x194,-0x1f9)],'ephemeral':!![]});function _0x1ab2b7(_0x5b14ee,_0x51db47){return _0x2bfafe(_0x5b14ee,_0x51db47- -0x644);}const _0x3036d9=_0x434e9d['options'][_0x1ab2b7(-0x2d4,-0x218)]('user');await clearUserHistory(_0x3036d9['id'],_0x434e9d[_0x1ab2b7(-0x1af,-0x112)]['id']);const _0x5ec9ca=lang[_0x1ab2b7(-0x19e,-0x1e9)][_0x1ab2b7(-0xf9,-0x10c)](/{user}/g,_0x3036d9['tag']),_0x5093d0=new EmbedBuilder()['setAuthor']({'name':lang['SuccessEmbedTitle'],'iconURL':_0x1ab2b7(-0xc8,-0x138)})[_0x1ab2b7(-0x1c9,-0x1b5)](config[_0x1ab2b7(-0x28e,-0x1d9)])['setDescription'](_0x5ec9ca);_0x434e9d[_0x1ab2b7(-0x134,-0x115)]({'embeds':[_0x5093d0],'ephemeral':!![]});}async function clearUserHistory(_0x24283e,_0x330ca6){function _0x22f36a(_0x162c5a,_0x2c5df8){return _0x2bfafe(_0x2c5df8,_0x162c5a- -0x28c);}await UserData[_0x22f36a(0x2b7,0x28c)]({'userId':_0x24283e,'guildId':_0x330ca6},{'$set':{'warns':0x0,'bans':0x0,'kicks':0x0,'timeouts':0x0,'note':'','warnings':[]}},{'upsert':!![],'new':!![],'setDefaultsOnInsert':!![]});}async function executeClearChannel(_0x36f99c){function _0xcc69d3(_0x1e3e79,_0x7a58b1){return _0x2bfafe(_0x1e3e79,_0x7a58b1- -0x16);}try{await _0x36f99c[_0xcc69d3(0x4b0,0x404)]({'ephemeral':!![]});const _0x467715=config[_0xcc69d3(0x44f,0x464)][_0xcc69d3(0x4b0,0x44c)],_0x5151c7=_0x467715[_0xcc69d3(0x544,0x534)](_0x48a93f=>_0x36f99c[_0xcc69d3(0x4ae,0x500)][_0xcc69d3(0x46f,0x4b8)][_0xcc69d3(0x400,0x473)][_0xcc69d3(0x431,0x4b4)](_0x48a93f)),_0x2eeea5=_0x36f99c['member']['permissions'][_0xcc69d3(0x4eb,0x4b4)](PermissionsBitField[_0xcc69d3(0x51b,0x497)][_0xcc69d3(0x545,0x4ac)]);if(!_0x5151c7&&!_0x2eeea5)return _0x36f99c[_0xcc69d3(0x578,0x519)]({'content':lang[_0xcc69d3(0x474,0x435)],'ephemeral':!![]});const _0x28ef61=new ActionRowBuilder()[_0xcc69d3(0x4b2,0x456)](new ButtonBuilder()[_0xcc69d3(0x501,0x46e)]('confirmClear')[_0xcc69d3(0x4b2,0x44d)](_0xcc69d3(0x61a,0x576))[_0xcc69d3(0x444,0x46a)](ButtonStyle[_0xcc69d3(0x4c8,0x54b)]),new ButtonBuilder()[_0xcc69d3(0x461,0x46e)](_0xcc69d3(0x515,0x516))['setLabel'](_0xcc69d3(0x534,0x477))[_0xcc69d3(0x500,0x46a)](ButtonStyle[_0xcc69d3(0x3c1,0x418)]));await _0x36f99c['editReply']({'content':lang[_0xcc69d3(0x4b2,0x552)][_0xcc69d3(0x52e,0x4c9)],'components':[_0x28ef61],'ephemeral':!![]});const _0x39d35c=_0x5985bb=>_0x5985bb[_0xcc69d3(0x480,0x526)]===_0xcc69d3(0x559,0x4e2)||_0x5985bb[_0xcc69d3(0x522,0x526)]===_0xcc69d3(0x548,0x516),_0x8ee1c3=_0x36f99c['channel'][_0xcc69d3(0x4c8,0x485)]({'filter':_0x39d35c,'time':0x3a98});_0x8ee1c3['on'](_0xcc69d3(0x50b,0x47d),async _0x33f084=>{function _0x559e69(_0x51e749,_0x3828d8){return _0xcc69d3(_0x51e749,_0x3828d8- -0x36b);}if(_0x33f084[_0x559e69(0x18e,0x1bb)]==='confirmClear'){const _0x393746=_0x36f99c['channel']['position'],_0x565ed8=await _0x36f99c[_0x559e69(0xd0,0xd8)][_0x559e69(0x17a,0x1ea)]();await _0x36f99c[_0x559e69(0x7a,0xd8)]['delete'](),_0x565ed8[_0x559e69(0x17c,0x128)](_0x393746);try{await _0x565ed8['send'](lang[_0x559e69(0x138,0x1e7)]['ClearChannelCleared'][_0x559e69(0x25c,0x1b7)]('{user}',_0x36f99c[_0x559e69(0xd1,0x195)])),await _0x565ed8[_0x559e69(0x166,0x1ad)](lang[_0x559e69(0x24c,0x1e7)][_0x559e69(0x15e,0x14c)]);}catch(_0x25e529){console[_0x559e69(0x1bb,0x19d)]('Error\x20sending\x20messages\x20after\x20channel\x20clear:',_0x25e529);}}else await _0x36f99c[_0x559e69(0x18d,0x1ae)]({'content':lang[_0x559e69(0x168,0x1e7)][_0x559e69(0x208,0x170)],'components':[]});}),_0x8ee1c3['on'](_0xcc69d3(0x461,0x503),async _0x1e3455=>{!_0x1e3455['size']&&await _0x36f99c['editReply']({'content':lang['ClearChannel']['ClearTimeout'],'components':[]});});}catch(_0x3123cb){console[_0xcc69d3(0x504,0x508)]('An\x20error\x20occurred\x20while\x20executing\x20the\x20clearchannel\x20command:\x20'+_0x3123cb[_0xcc69d3(0x4c9,0x468)]),_0x36f99c[_0xcc69d3(0x548,0x4b2)]||_0x36f99c['deferred']?await _0x36f99c['editReply']({'content':'There\x20was\x20an\x20error\x20trying\x20to\x20execute\x20that\x20command!\x20Please\x20try\x20again\x20later.','ephemeral':!![]}):await _0x36f99c[_0xcc69d3(0x541,0x549)]({'content':_0xcc69d3(0x4c0,0x543),'ephemeral':!![]});}}async function executeHistory(_0x2dc403){await _0x2dc403[_0xb9ced6(0x80,0x127)]({'ephemeral':!![]});const _0x6c067f=config[_0xb9ced6(0xe0,0x114)][_0xb9ced6(0xe2,0xf7)],_0x5302c4=_0x6c067f[_0xb9ced6(0x1b0,0x184)](_0x54bef0=>_0x2dc403[_0xb9ced6(0x17c,0x23e)][_0xb9ced6(0x134,0x122)]['cache'][_0xb9ced6(0x130,0x1c5)](_0x54bef0)),_0x2d67ff=_0x2dc403['member']['permissions'][_0xb9ced6(0x130,0xb0)](PermissionsBitField[_0xb9ced6(0x113,0x172)]['Administrator']);if(!_0x5302c4&&!_0x2d67ff)return _0x2dc403[_0xb9ced6(0x195,0x12c)]({'content':lang[_0xb9ced6(0xb1,0x100)],'ephemeral':!![]});function _0xb9ced6(_0x5a8c6b,_0x139999){return _0x2bfafe(_0x139999,_0x5a8c6b- -0x39a);}const _0xd42f5e=_0x2dc403[_0xb9ced6(0x144,0x1d2)]['getUser'](_0xb9ced6(0x16b,0x134)),_0x4fb7c8=await getUserHistory(_0xd42f5e['id']),_0x51fa09=createHistoryEmbed(_0xd42f5e,_0x4fb7c8,_0x2dc403);_0x2dc403[_0xb9ced6(0x195,0x1ae)]({'embeds':[_0x51fa09],'ephemeral':!![]});}async function getUserHistory(_0x14eecb){function _0x4cfcbd(_0x33a2f0,_0xfe5c61){return _0x2bfafe(_0xfe5c61,_0x33a2f0- -0x10c);}const _0x5881d0=await UserData[_0x4cfcbd(0x3f4,0x448)]({'userId':_0x14eecb});return _0x5881d0||{};}function createHistoryEmbed(_0x251df9,_0x3de12b,_0x44b155){function _0x2e8201(_0xa1abd4,_0x2572bd){return _0x2bfafe(_0xa1abd4,_0x2572bd-0x68);}const _0x4247c6=_0x251df9[_0x2e8201(0x41d,0x4d6)]({'format':_0x2e8201(0x53e,0x54b),'dynamic':!![],'size':0x400}),_0x3bb161=_0x44b155[_0x2e8201(0x4e8,0x59a)][_0x2e8201(0x5ae,0x4f2)][_0x2e8201(0x520,0x4f1)][_0x2e8201(0x58f,0x4d2)](_0x251df9['id']),_0x42c9eb=_0x3bb161?moment(_0x3bb161[_0x2e8201(0x5b4,0x5e1)])['format'](_0x2e8201(0x5d6,0x5ee)):'Not\x20in\x20server';return new EmbedBuilder()['setColor']('#000000')[_0x2e8201(0x5e4,0x5c8)](lang[_0x2e8201(0x450,0x4b4)]['HistoryEmbedTitle']['replace'](/{user-tag}/g,_0x251df9[_0x2e8201(0x46c,0x4b5)]))[_0x2e8201(0x57a,0x588)](_0x4247c6)[_0x2e8201(0x584,0x5e6)]({'name':lang[_0x2e8201(0x4af,0x4b4)][_0x2e8201(0x4d0,0x505)],'value':'``'+lang['History'][_0x2e8201(0x553,0x587)]+_0x2e8201(0x536,0x4e0)+_0x251df9['id']+_0x2e8201(0x43f,0x499)+lang[_0x2e8201(0x481,0x4b4)]['HistoryEmbedJoinedServer']+_0x2e8201(0x54b,0x4fa)+_0x42c9eb+'\x0a``'+lang[_0x2e8201(0x4c2,0x4b4)]['HistoryTotalMessages']+'``\x20'+(_0x3de12b[_0x2e8201(0x514,0x4bf)]?.[_0x2e8201(0x54c,0x512)]()||'0')+_0x2e8201(0x535,0x57f)+lang[_0x2e8201(0x49c,0x4b4)][_0x2e8201(0x5f4,0x5e7)]+_0x2e8201(0x529,0x4fa)+(_0x3de12b[_0x2e8201(0x550,0x5e4)]||_0x2e8201(0x430,0x4a5)),'inline':!![]},{'name':lang['History'][_0x2e8201(0x3f5,0x497)],'value':''+(_0x3de12b[_0x2e8201(0x5f4,0x561)]||0x0),'inline':!![]},{'name':lang['History'][_0x2e8201(0x4da,0x555)],'value':''+(_0x3de12b[_0x2e8201(0x61a,0x5f0)]||0x0),'inline':!![]},{'name':lang['History']['HistoryEmbedKicks'],'value':''+(_0x3de12b[_0x2e8201(0x55e,0x5f9)]||0x0),'inline':!![]},{'name':lang[_0x2e8201(0x468,0x4b4)][_0x2e8201(0x580,0x4d5)],'value':''+(_0x3de12b[_0x2e8201(0x5c4,0x59c)]||0x0),'inline':!![]})[_0x2e8201(0x4d0,0x4ac)]()[_0x2e8201(0x53d,0x4dd)]({'text':_0x44b155['guild'][_0x2e8201(0x485,0x539)]});}async function executePurge(_0x46907b){await _0x46907b[_0x504e0d(-0x27,-0xa7)]({'ephemeral':!![]});function _0x504e0d(_0x3794cb,_0x2f8535){return _0x2bfafe(_0x3794cb,_0x2f8535- -0x4c1);}const _0x205137=config[_0x504e0d(-0xf3,-0x47)][_0x504e0d(0x2,0x33)],_0x105c54=_0x205137['some'](_0x58f260=>_0x46907b[_0x504e0d(-0x64,0x55)][_0x504e0d(0x41,0xd)][_0x504e0d(-0xda,-0x38)][_0x504e0d(0xf,0x9)](_0x58f260)),_0x172e14=_0x46907b[_0x504e0d(-0x4b,0x55)][_0x504e0d(0x77,-0x36)][_0x504e0d(0x8b,0x9)](PermissionsBitField[_0x504e0d(-0x24,-0x14)][_0x504e0d(0x11,0x1)]);if(!_0x105c54&&!_0x172e14)return _0x46907b[_0x504e0d(0xc5,0x6e)]({'content':lang['NoPermsMessage'],'ephemeral':!![]});let _0x4aa146=_0x46907b[_0x504e0d(-0x34,0x1d)]['getNumber'](_0x504e0d(-0x2f,-0x4));const _0xc19b6c=_0x46907b[_0x504e0d(0x7f,0x1d)][_0x504e0d(0x12c,0x7f)](_0x504e0d(0xa2,-0x21))||_0x504e0d(-0x3e,0x74);try{let _0x3cd50a=_0x4aa146,_0x26c8ad=0x0;while(_0x3cd50a>0x0){const _0x8f7fa8=Math['min'](_0x3cd50a,0x64),_0x11267e=await purgeBatch(_0x46907b[_0x504e0d(-0x82,-0x68)],_0x8f7fa8,_0xc19b6c);if(_0x11267e===0x0)break;_0x26c8ad+=_0x11267e,_0x3cd50a-=_0x11267e;if(_0x11267e<_0x8f7fa8)break;await new Promise(_0x5a915b=>setTimeout(_0x5a915b,0x1388));}const _0x4cc6da=createLogEmbed(_0x46907b,_0x26c8ad,_0xc19b6c);await sendLogMessage(_0x46907b,_0x4cc6da),await _0x46907b[_0x504e0d(0xc9,0x6e)]({'content':lang[_0x504e0d(0x9a,-0xb)][_0x504e0d(0xb7,0x5)][_0x504e0d(0xc9,0x77)](/{amount}/g,''+_0x26c8ad),'ephemeral':!![]});}catch(_0xae0c72){console['error'](_0x504e0d(-0x8,-0x9),_0xae0c72),await _0x46907b['editReply']({'content':lang['Purge'][_0x504e0d(0x122,0x63)],'ephemeral':!![]});}}async function purgeBatch(_0x10540b,_0x732be4,_0xc10218){const _0x4026a0=await _0x10540b[_0x8b27d(-0xbd,-0xf6)]['fetch']({'limit':0x64});function _0x8b27d(_0x320970,_0x5ace71){return _0x2bfafe(_0x320970,_0x5ace71- -0x578);}let _0x4d2f15=filterMessages(_0x4026a0,_0xc10218,_0x732be4);_0x4d2f15=Array[_0x8b27d(-0x141,-0x89)](_0x4d2f15)?_0x4d2f15:[..._0x4d2f15[_0x8b27d(-0x106,-0x69)]()];if(_0x4d2f15[_0x8b27d(-0x6c,-0x10f)]===0x0)return 0x0;const _0x16f839=await _0x10540b[_0x8b27d(-0x1fb,-0x13e)](_0x4d2f15,!![]);return _0x16f839[_0x8b27d(0x8c,-0xc)];}function _0x2bfafe(_0x262b48,_0x48225c){return _0xba40(_0x48225c-0x295,_0x262b48);}function _0xba40(_0x40d4db,_0x3ead3a){const _0x39287c=_0x3928();return _0xba40=function(_0xba4038,_0x1e37f4){_0xba4038=_0xba4038-0x177;let _0x1d9358=_0x39287c[_0xba4038];return _0x1d9358;},_0xba40(_0x40d4db,_0x3ead3a);}function filterMessages(_0xa50acc,_0x1cb614,_0x1d07f3){function _0xa5b3c8(_0x39bd3a,_0x5cb671){return _0x2bfafe(_0x5cb671,_0x39bd3a-0x4a);}const _0x228dbc=_0xa50acc['filter'](_0x1eaf6d=>{function _0x429bb7(_0xd4572f,_0x49bbfb){return _0xba40(_0xd4572f- -0x324,_0x49bbfb);}switch(_0x1cb614){case _0x429bb7(-0x13c,-0x9c):return _0x1eaf6d[_0x429bb7(-0xfa,-0x114)][_0x429bb7(-0xc9,-0x3e)]('http');case _0x429bb7(-0x6c,0x42):return!_0x1eaf6d['embeds'][_0x429bb7(-0x150,-0x210)]&&!_0x1eaf6d[_0x429bb7(-0x122,-0x120)][_0x429bb7(-0x4d,-0x3b)];case _0x429bb7(-0x96,-0x60):return _0x1eaf6d[_0x429bb7(-0x178,-0x176)][_0x429bb7(-0x2b,-0xcc)];case _0x429bb7(-0x67,0x51):return _0x1eaf6d['embeds'][_0x429bb7(-0x150,-0x18a)]>0x0;case'images':return _0x1eaf6d['attachments'][_0x429bb7(-0x6f,-0x80)](_0x49d1e4=>_0x49d1e4[_0x429bb7(-0x140,-0x145)]?.[_0x429bb7(-0x52,-0xa0)](_0x429bb7(-0x11d,-0x1c0)));default:return!![];}});return[..._0x228dbc[_0xa5b3c8(0x559,0x492)]()][_0xa5b3c8(0x572,0x4d6)](0x0,_0x1d07f3);}function createLogEmbed(_0x5b52f3,_0x197c43,_0x49154d){function _0x28dab6(_0x5d80c0,_0xfc6243){return _0x2bfafe(_0xfc6243,_0x5d80c0- -0x184);}return new EmbedBuilder()[_0x28dab6(0x2c3,0x322)]({'name':lang[_0x28dab6(0x332,0x372)][_0x28dab6(0x2a2,0x2da)],'iconURL':_0x28dab6(0x356,0x339)})[_0x28dab6(0x30b,0x3c4)]('Red')[_0x28dab6(0x3fa,0x3f8)]({'name':_0x28dab6(0x373,0x2b3),'value':_0x28dab6(0x332,0x39b)},{'name':_0x28dab6(0x314,0x24f),'value':_0x49154d},{'name':_0x28dab6(0x416,0x47a),'value':'<@'+_0x5b52f3['user']['id']+'>','inline':!![]},{'name':'Amount','value':''+_0x197c43,'inline':!![]},{'name':_0x28dab6(0x322,0x339),'value':''+_0x5b52f3[_0x28dab6(0x2d5,0x26f)],'inline':!![]})[_0x28dab6(0x2c0,0x249)]();}async function sendLogMessage(_0x266a09,_0x1c50ad){function _0x380339(_0x486f73,_0x4e7e2c){return _0x2bfafe(_0x486f73,_0x4e7e2c- -0x1e5);}const _0xdb3261=config[_0x380339(0x354,0x342)],_0x2d49db=_0x266a09[_0x380339(0x3b0,0x34d)][_0x380339(0x1bd,0x22b)][_0x380339(0x2ae,0x2a4)]['get'](_0xdb3261);_0x2d49db&&await _0x2d49db[_0x380339(0x2db,0x349)]({'embeds':[_0x1c50ad]});}async function executeSlowmode(_0x4f7c67){await _0x4f7c67['deferReply']({'ephemeral':!![]});const _0x4f8808=config[_0x52897b(0x4bb,0x4a5)][_0x52897b(0x5b0,0x5a0)],_0x2e14f6=_0x4f8808[_0x52897b(0x58b,0x64f)](_0x91b6ee=>_0x4f7c67[_0x52897b(0x557,0x4f1)][_0x52897b(0x50f,0x58e)][_0x52897b(0x4ca,0x415)][_0x52897b(0x50b,0x4aa)](_0x91b6ee)),_0x2990ab=_0x4f7c67[_0x52897b(0x557,0x5b6)]['permissions'][_0x52897b(0x50b,0x4be)](PermissionsBitField[_0x52897b(0x4ee,0x590)][_0x52897b(0x503,0x440)]);if(!_0x2e14f6&&!_0x2990ab)return _0x4f7c67[_0x52897b(0x570,0x512)]({'content':lang[_0x52897b(0x48c,0x551)],'ephemeral':!![]});let _0x389cc2=_0x4f7c67[_0x52897b(0x51f,0x499)]['getNumber'](_0x52897b(0x4fe,0x48d));_0x389cc2=Math[_0x52897b(0x4b2,0x403)](0x0,Math[_0x52897b(0x471,0x3d2)](_0x389cc2,0x5460));function _0x52897b(_0x1b5e5c,_0x4cdf09){return _0x2bfafe(_0x4cdf09,_0x1b5e5c-0x41);}try{await _0x4f7c67[_0x52897b(0x49a,0x4da)][_0x52897b(0x57e,0x539)](_0x389cc2);const _0xb338d6=_0x389cc2===0x0?lang[_0x52897b(0x55d,0x596)]:lang[_0x52897b(0x574,0x545)][_0x52897b(0x579,0x4c9)](/{time}/g,''+_0x389cc2),_0x3e54dc=createResponseEmbed(_0xb338d6,!![]);await _0x4f7c67[_0x52897b(0x570,0x53a)]({'embeds':[_0x3e54dc],'ephemeral':!![]});}catch(_0xb68494){console['error'](_0x52897b(0x5c4,0x58e),_0xb68494);const _0x505405=createResponseEmbed(lang[_0x52897b(0x4b3,0x54b)],![]);await _0x4f7c67[_0x52897b(0x570,0x603)]({'embeds':[_0x505405],'ephemeral':!![]});}}function createResponseEmbed(_0x1cef80,_0x48eec6){function _0x86a450(_0x58e23e,_0x37ec36){return _0x2bfafe(_0x58e23e,_0x37ec36- -0x2cf);}return new EmbedBuilder()[_0x86a450(0x1fb,0x178)]({'name':_0x48eec6?lang[_0x86a450(0x1da,0x154)]:lang[_0x86a450(0x242,0x2be)],'iconURL':_0x48eec6?'https://i.imgur.com/7SlmRRa.png':'https://i.imgur.com/MdiCK2c.png'})[_0x86a450(0x21a,0x1c0)](_0x48eec6?config[_0x86a450(0x129,0x19c)]:config[_0x86a450(0x351,0x28e)])[_0x86a450(0x27d,0x1c7)](_0x1cef80);}async function executeTempban(_0x2810ec){await _0x2810ec[_0x311f0c(0x1bb,0x187)]({'ephemeral':!![]});const _0x4f5015=config[_0x311f0c(0x21b,0x1f7)][_0x311f0c(0x278,0x229)],_0xacb004=_0x4f5015[_0x311f0c(0x2eb,0x2a7)](_0x5d2c89=>_0x2810ec[_0x311f0c(0x2b7,0x2db)][_0x311f0c(0x26f,0x1b3)][_0x311f0c(0x22a,0x200)][_0x311f0c(0x26b,0x24d)](_0x5d2c89)),_0x4e3f40=_0x2810ec[_0x311f0c(0x2b7,0x27f)][_0x311f0c(0x22c,0x2ed)][_0x311f0c(0x26b,0x221)](PermissionsBitField[_0x311f0c(0x24e,0x2ca)]['Administrator']);if(!_0xacb004&&!_0x4e3f40){await _0x2810ec['editReply']({'content':lang[_0x311f0c(0x1ba,0x1b9)][_0x311f0c(0x1ec,0x134)],'ephemeral':!![]});return;}const _0x1928a6=_0x2810ec[_0x311f0c(0x27f,0x273)][_0x311f0c(0x1cd,0x11e)](_0x311f0c(0x2a6,0x228));function _0x311f0c(_0xb7f8b4,_0x10e669){return _0x2bfafe(_0x10e669,_0xb7f8b4- -0x25f);}const _0x45eaca=_0x2810ec[_0x311f0c(0x27f,0x30b)][_0x311f0c(0x2e1,0x380)](_0x311f0c(0x303,0x34a)),_0x5432b0=_0x2810ec[_0x311f0c(0x27f,0x268)][_0x311f0c(0x2e1,0x270)]('duration'),_0x54c8ea=_0x2810ec[_0x311f0c(0x27f,0x21f)][_0x311f0c(0x2e1,0x2ed)](_0x311f0c(0x306,0x32e))||_0x311f0c(0x205,0x171);let _0x4f325d=_0x1928a6;!_0x4f325d&&_0x45eaca&&(_0x4f325d=await _0x2810ec[_0x311f0c(0x27a,0x246)][_0x311f0c(0x25d,0x239)][_0x311f0c(0x2f1,0x2c2)](_0x45eaca)[_0x311f0c(0x27c,0x316)](()=>null));if(!_0x4f325d){await _0x2810ec[_0x311f0c(0x2d0,0x2a9)]({'content':lang[_0x311f0c(0x1ba,0x119)][_0x311f0c(0x1dc,0x145)],'ephemeral':!![]}),console[_0x311f0c(0x2a4,0x2e4)]('User\x20not\x20found');return;}const _0x408448=parseDuration(_0x5432b0);if(_0x408448===null){await _0x2810ec[_0x311f0c(0x2d0,0x29c)]({'content':lang[_0x311f0c(0x1ba,0x10d)]['InvalidDuration'],'ephemeral':!![]});return;}const _0x41a6be=moment()[_0x311f0c(0x2b4,0x25e)](_0x408448,_0x311f0c(0x288,0x349))[_0x311f0c(0x268,0x2d5)]();try{const _0x5db6ac=await _0x2810ec[_0x311f0c(0x2d3,0x272)][_0x311f0c(0x22b,0x2cc)][_0x311f0c(0x2f1,0x33d)](_0x4f325d['id']);if(!_0x5db6ac[_0x311f0c(0x232,0x28e)]){await _0x2810ec[_0x311f0c(0x2d0,0x298)]({'content':lang[_0x311f0c(0x1ba,0x108)][_0x311f0c(0x2ed,0x3a5)],'ephemeral':!![]});return;}const _0x1ea045=await sendBanDM(_0x5db6ac,_0x54c8ea,_0x2810ec);await _0x5db6ac[_0x311f0c(0x2d8,0x2bd)]({'reason':_0x54c8ea});let _0x400d06=await UserData[_0x311f0c(0x2a1,0x2ee)]({'userId':_0x4f325d['id'],'guildId':_0x2810ec[_0x311f0c(0x2d3,0x290)]['id']});!_0x400d06&&(_0x400d06=new UserData({'userId':_0x4f325d['id'],'guildId':_0x2810ec['guild']['id']}));_0x400d06[_0x311f0c(0x1e4,0x1fe)][_0x311f0c(0x1c5,0x110)]({'endTime':_0x41a6be,'reason':_0x54c8ea,'moderatorId':_0x2810ec[_0x311f0c(0x2a6,0x29b)]['id']}),await _0x400d06[_0x311f0c(0x32c,0x39e)]();let _0x2b9677=lang[_0x311f0c(0x1ba,0x15d)]['Success'][_0x311f0c(0x2d9,0x346)](_0x311f0c(0x2af,0x286),_0x4f325d['tag'])[_0x311f0c(0x2d9,0x286)](_0x311f0c(0x200,0x26b),_0x5432b0)[_0x311f0c(0x2d9,0x2ff)](_0x311f0c(0x2ce,0x365),_0x54c8ea);!_0x1ea045&&(_0x2b9677+=_0x311f0c(0x2a0,0x2ad)),await _0x2810ec[_0x311f0c(0x2d0,0x284)]({'content':_0x2b9677,'ephemeral':!![]});}catch(_0x5983c1){console[_0x311f0c(0x2bf,0x2de)]('TempBan\x20Error:',_0x5983c1),await _0x2810ec['editReply']({'content':lang[_0x311f0c(0x1ba,0x194)][_0x311f0c(0x2e9,0x239)],'ephemeral':!![]});}}async function executeTemprole(_0x3a6770){await _0x3a6770[_0x35a75d(-0x256,-0x1b2)]({'ephemeral':!![]});const _0xb42ae1=config[_0x35a75d(-0xe4,-0x152)][_0x35a75d(-0x136,-0xc2)]||[],_0x39d10f=_0xb42ae1[_0x35a75d(-0x56,-0x82)](_0x5495a5=>_0x3a6770['member'][_0x35a75d(-0x90,-0xfe)]['cache'][_0x35a75d(-0xcd,-0x102)](_0x5495a5)),_0xd85074=_0x3a6770[_0x35a75d(-0x174,-0xb6)][_0x35a75d(-0x15a,-0x141)][_0x35a75d(-0x1b8,-0x102)](PermissionsBitField['Flags'][_0x35a75d(-0x9a,-0x10a)]);if(!_0x39d10f&&!_0xd85074)return _0x3a6770[_0x35a75d(-0x65,-0x9d)]({'content':lang[_0x35a75d(-0x205,-0x181)],'ephemeral':!![]});const _0x2acab8=_0x3a6770[_0x35a75d(-0x18a,-0xee)][_0x35a75d(-0x211,-0x1a0)]('user');function _0x35a75d(_0x321caa,_0x2c200){return _0x2bfafe(_0x321caa,_0x2c200- -0x5cc);}const _0xd1d73c=_0x3a6770[_0x35a75d(-0x18c,-0xee)][_0x35a75d(-0xe5,-0xb8)](_0x35a75d(-0xc6,-0x9b)),_0x3e8a38=_0x3a6770[_0x35a75d(-0x20,-0xb6)][_0x35a75d(-0x19d,-0xfe)][_0x35a75d(-0x22a,-0x1b1)]['position'],_0x33bbc9=_0xd1d73c[_0x35a75d(-0x116,-0x1a5)];if(_0x33bbc9>=_0x3e8a38)return _0x3a6770[_0x35a75d(0x6,-0x9d)]({'content':lang[_0x35a75d(-0x199,-0xe1)][_0x35a75d(-0x152,-0x1aa)],'ephemeral':!![]});if(!_0xd1d73c||_0xd1d73c['id']===_0x3a6770[_0x35a75d(-0x46,-0x9a)]['id'])return _0x3a6770[_0x35a75d(-0xf1,-0x9d)]({'content':lang['Temprole'][_0x35a75d(-0x8b,-0xba)],'ephemeral':!![]});const _0x5a94b0=_0x3a6770[_0x35a75d(-0x159,-0xee)][_0x35a75d(-0x84,-0x8c)](_0x35a75d(-0xeb,-0x11a)),_0x53c448=parseDurationTemprole(_0x5a94b0);if(_0x53c448<=0x0)return _0x3a6770['editReply']({'content':lang[_0x35a75d(-0x107,-0xe1)]['InvalidDurationFormat'],'ephemeral':!![]});const _0x46cd18=new Date();_0x46cd18[_0x35a75d(-0xbb,-0xca)](_0x46cd18['getTime']()+_0x53c448);try{const _0x117df4=await _0x3a6770[_0x35a75d(-0x69,-0x9a)]['members'][_0x35a75d(-0x44,-0x7c)](_0x2acab8['id']);await _0x117df4[_0x35a75d(-0x71,-0xfe)][_0x35a75d(-0xe7,-0xb9)](_0xd1d73c),await TempRole['create']({'userId':_0x2acab8['id'],'guildId':_0x3a6770[_0x35a75d(-0xfd,-0x9a)]['id'],'roleId':_0xd1d73c['id'],'expiration':_0x46cd18});const _0x31be24=lang['Temprole'][_0x35a75d(-0x17d,-0x186)][_0x35a75d(0x28,-0x94)](_0x35a75d(-0x26b,-0x1b9),_0xd1d73c['name'])[_0x35a75d(-0x50,-0x94)](_0x35a75d(0x5c,-0x34),_0x2acab8[_0x35a75d(-0x117,-0x17f)])[_0x35a75d(-0xef,-0x94)](_0x35a75d(-0x172,-0x16d),_0x5a94b0);await _0x3a6770[_0x35a75d(-0x1d,-0x9d)]({'content':_0x31be24,'ephemeral':!![]});}catch(_0x295704){_0x295704[_0x35a75d(-0x19d,-0xda)]===0xc35d?await _0x3a6770['editReply']({'content':lang['Temprole'][_0x35a75d(-0x9f,-0x15c)],'ephemeral':!![]}):(console[_0x35a75d(-0xc5,-0xae)](_0x35a75d(-0xcb,-0x107),_0x295704),await _0x3a6770[_0x35a75d(-0x107,-0x9d)]({'content':lang[_0x35a75d(-0x3d,-0xe1)][_0x35a75d(-0xcf,-0xf8)],'ephemeral':!![]}));}}function parseDurationTemprole(_0x2e4737){const _0x2b1be6=/(\d+)(w|d|h|m|s|y)/g;let _0x2ce6c9=0x0;const _0x267b10={'w':0x240c8400,'d':0x5265c00,'h':0x36ee80,'m':0xea60,'s':0x3e8,'y':0x757b12c00};let _0xced0e7;while((_0xced0e7=_0x2b1be6[_0x38028b(0x43f,0x40c)](_0x2e4737))!==null){const _0x55df3a=parseInt(_0xced0e7[0x1],0xa),_0x16c5bc=_0xced0e7[0x2];_0x2ce6c9+=_0x55df3a*(_0x267b10[_0x16c5bc]||0x0);}function _0x38028b(_0x1fb9ca,_0x333322){return _0x2bfafe(_0x333322,_0x1fb9ca-0x0);}return _0x2ce6c9;}function parseDuration(_0x21ef84){const _0x4aa1be=/(\d+)([smhd])/g;let _0xaf30b2=0x0,_0x465633;while((_0x465633=_0x4aa1be['exec'](_0x21ef84))!==null){const _0x33b8ef=parseInt(_0x465633[0x1],0xa),_0x28fda1=_0x465633[0x2];switch(_0x28fda1){case's':_0xaf30b2+=_0x33b8ef;break;case'm':_0xaf30b2+=_0x33b8ef*0x3c;break;case'h':_0xaf30b2+=_0x33b8ef*0xe10;break;case'd':_0xaf30b2+=_0x33b8ef*0x15180;break;default:return null;}}return _0xaf30b2>0x0?_0xaf30b2:null;}function replacePlaceholders(_0x26289c,_0x3912ad){function _0x2dfcf(_0x445432,_0x4f654b){return _0x2bfafe(_0x445432,_0x4f654b-0x16);}return(_0x26289c||'')['replace'](/{user}/g,_0x3912ad['user']||'')[_0x2dfcf(0x5b6,0x54e)](/{userName}/g,_0x3912ad[_0x2dfcf(0x4ad,0x531)]||'')[_0x2dfcf(0x4ba,0x54e)](/{userTag}/g,_0x3912ad[_0x2dfcf(0x582,0x4f2)]||'')[_0x2dfcf(0x530,0x54e)](/{userId}/g,_0x3912ad['userId']||'')[_0x2dfcf(0x58a,0x54e)](/{moderator}/g,_0x3912ad[_0x2dfcf(0x511,0x452)]||'')[_0x2dfcf(0x52d,0x54e)](/{guildName}/g,_0x3912ad[_0x2dfcf(0x49b,0x4d7)]||'')[_0x2dfcf(0x5b2,0x54e)](/{moderatorName}/g,_0x3912ad[_0x2dfcf(0x566,0x4fb)]||'')[_0x2dfcf(0x4a7,0x54e)](/{moderatorTag}/g,_0x3912ad[_0x2dfcf(0x550,0x57f)]||'')[_0x2dfcf(0x593,0x54e)](/{moderatorId}/g,_0x3912ad[_0x2dfcf(0x449,0x43b)]||'')[_0x2dfcf(0x565,0x54e)](/{reason}/g,_0x3912ad[_0x2dfcf(0x63a,0x57b)]||'No\x20reason\x20provided')[_0x2dfcf(0x4df,0x54e)](/{shorttime}/g,_0x3912ad[_0x2dfcf(0x41e,0x48d)]||'')[_0x2dfcf(0x4a4,0x54e)](/{longtime}/g,_0x3912ad[_0x2dfcf(0x3d9,0x443)]||'')[_0x2dfcf(0x60d,0x54e)](/{caseNumber}/g,_0x3912ad[_0x2dfcf(0x416,0x469)]||'');}async function sendBanDM(_0x4e79ca,_0x425c3e,_0x2bc556,_0x53015b){function _0xe71cde(_0x1258bb,_0x1f2551){return _0x2bfafe(_0x1258bb,_0x1f2551- -0x3bb);}if(!_0x4e79ca||!_0x2bc556||_0x53015b==null)return![];try{if(config[_0xe71cde(0x15f,0x16b)]['DM'][_0xe71cde(0xf3,0x18a)]){const _0x39bc89=config['BanLogs']['DM'][_0xe71cde(0x1b4,0xf5)],_0x1d48ed=moment()['tz'](config[_0xe71cde(0x18e,0x141)]),_0x35cd9f={'user':'<@'+_0x4e79ca[_0xe71cde(0x1e6,0x14a)]['id']+'>','userName':_0x4e79ca[_0xe71cde(0x87,0x14a)][_0xe71cde(0x75,0x92)],'userTag':_0x4e79ca[_0xe71cde(0x1e5,0x14a)][_0xe71cde(0x15e,0xf0)],'userId':_0x4e79ca[_0xe71cde(0x168,0x14a)]['id'],'moderator':'<@'+_0x2bc556[_0xe71cde(0x16d,0x14a)]['id']+_0xe71cde(-0x32,0x87)+_0x2bc556[_0xe71cde(0x107,0x14a)][_0xe71cde(0xbb,0xf0)]+')','reason':_0x425c3e,'guildName':_0x53015b[_0xe71cde(0x1c8,0x116)],'longtime':_0x1d48ed[_0xe71cde(0x171,0xff)](_0xe71cde(0x205,0x1d5)),'shorttime':_0x1d48ed[_0xe71cde(0x19a,0xff)](_0xe71cde(0x8c,0x12b))},_0x1f4df9=_0x39bc89[_0xe71cde(0xab,0x5a)]?parseInt(_0x39bc89[_0xe71cde(0xfa,0x5a)][_0xe71cde(0x11c,0x17d)]('#',''),0x10):0xff5555,_0x35aa40=new EmbedBuilder()[_0xe71cde(0x47,0xd4)](_0x1f4df9)[_0xe71cde(0x231,0x1a5)](replacePlaceholders(_0x39bc89[_0xe71cde(0x191,0x156)],_0x35cd9f))[_0xe71cde(0x5e,0xdb)](_0x39bc89[_0xe71cde(0xa2,0x5b)][_0xe71cde(0x258,0x1a8)](_0x1fce58=>replacePlaceholders(_0x1fce58,_0x35cd9f))[_0xe71cde(0x16a,0x11b)]('\x0a'))['setFooter']({'text':replacePlaceholders(_0x39bc89['Footer'],_0x35cd9f)});return await _0x4e79ca[_0xe71cde(0x1c6,0x173)]({'embeds':[_0x35aa40]}),!![];}}catch(_0x105bd6){return console[_0xe71cde(0x153,0x163)](_0xe71cde(0x14f,0xc0),_0x105bd6),![];}}async function banMember(_0x559074,_0x27254b,_0x2e1e92){function _0x35efe2(_0x2108e4,_0x159e40){return _0x2bfafe(_0x159e40,_0x2108e4- -0x3cc);}await _0x559074[_0x35efe2(0x16b,0x185)]({'reason':_0x27254b});}async function unbanUser(_0x53a67c,_0x1bf405,_0x1661f1){function _0x33ce7a(_0x11a802,_0x5aff73){return _0x2bfafe(_0x5aff73,_0x11a802-0x2d);}const _0x174707=await _0x53a67c[_0x33ce7a(0x55f,0x571)][_0x33ce7a(0x561,0x5a0)]['fetch']();if(!_0x174707['has'](_0x1bf405)){await _0x53a67c[_0x33ce7a(0x55c,0x587)]({'content':lang['Unban'][_0x33ce7a(0x538,0x52f)],'ephemeral':!![]});return;}await _0x53a67c[_0x33ce7a(0x55f,0x57c)][_0x33ce7a(0x4b7,0x421)][_0x33ce7a(0x4de,0x537)](_0x1bf405,_0x1661f1);const _0xe16aa5=lang[_0x33ce7a(0x45f,0x4db)][_0x33ce7a(0x44e,0x4a2)]['replace'](/{user}/g,_0x33ce7a(0x48d,0x471)+_0x1bf405+'>');await _0x53a67c[_0x33ce7a(0x55c,0x5fb)]({'content':_0xe16aa5,'ephemeral':!![]});const _0xb6203e=_0x53a67c['guild'][_0x33ce7a(0x43d,0x469)]['cache'][_0x33ce7a(0x497,0x40d)](config[_0x33ce7a(0x54f,0x4fe)][_0x33ce7a(0x542,0x574)]);if(_0xb6203e&&config[_0x33ce7a(0x54f,0x499)][_0x33ce7a(0x572,0x5bf)]){}}function hasPermissionToUnban(_0x3c0e98){function _0x4880b7(_0x1eb8e5,_0x56ff19){return _0x2bfafe(_0x56ff19,_0x1eb8e5- -0x26c);}const _0x16ae17=config['ModerationRoles'][_0x4880b7(0x245,0x1fe)],_0x26dd73=_0x3c0e98[_0x4880b7(0x2aa,0x2c3)][_0x4880b7(0x21f,0x2a3)][_0x4880b7(0x25e,0x203)](PermissionsBitField[_0x4880b7(0x241,0x221)][_0x4880b7(0x256,0x1b9)]);return _0x16ae17[_0x4880b7(0x2de,0x285)](_0x293911=>_0x3c0e98['member'][_0x4880b7(0x262,0x2b8)][_0x4880b7(0x21d,0x2c2)]['has'](_0x293911))||_0x26dd73;}