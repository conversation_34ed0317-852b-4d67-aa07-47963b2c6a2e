const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildStickerCreate,
    async execute(sticker, client) {
        try {
            console.log(`🏷️ Sticker created: ${sticker.name} in ${sticker.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(sticker.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'STICKERS_CREATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'STICKERS_CREATE',
                user: 'System',
                userId: null,
                action: 'Sticker được tạo',
                details: `Sticker **${sticker.name}** đã được tạo`,
                target: sticker.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who created the sticker from audit logs
            try {
                const auditLogs = await sticker.guild.fetchAuditLogs({
                    type: 90, // STICKER_CREATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === sticker.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Sticker được tạo bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for sticker create');
            }
            
            // Add to database
            await client.db.addAuditLog(sticker.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🏷️ Sticker được tạo',
                `Vừa có một sticker mới được tạo trong server`
            );
            
            embed.setColor(0x2ecc71); // Green for create
            
            embed.addFields([
                {
                    name: '> Tên sticker',
                    value: `- ${sticker.name}`,
                    inline: true
                },
                {
                    name: '> ID sticker',
                    value: `- ${sticker.id}`,
                    inline: true
                },
                {
                    name: '> Người tạo',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian tạo',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add sticker description
            if (sticker.description) {
                embed.addFields({
                    name: '> Mô tả',
                    value: `- ${sticker.description}`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> Mô tả',
                    value: `- Không có mô tả`,
                    inline: false
                });
            }
            
            // Add sticker format
            const formatTypes = {
                1: 'PNG',
                2: 'APNG',
                3: 'Lottie',
                4: 'GIF'
            };
            
            embed.addFields({
                name: '> Định dạng',
                value: `- ${formatTypes[sticker.format] || 'Unknown'}`,
                inline: true
            });
            
            // Add sticker type
            const stickerTypes = {
                1: 'Standard (Discord)',
                2: 'Guild (Server custom)'
            };
            
            embed.addFields({
                name: '> Loại sticker',
                value: `- ${stickerTypes[sticker.type] || 'Unknown'}`,
                inline: true
            });
            
            // Add sticker tags if available
            if (sticker.tags) {
                embed.addFields({
                    name: '> Tags',
                    value: `- ${sticker.tags}`,
                    inline: true
                });
            }
            
            // Add sticker availability
            embed.addFields({
                name: '> Có thể sử dụng',
                value: `- ${sticker.available ? 'Có' : 'Không'}`,
                inline: true
            });
            
            // Add sticker URL
            if (sticker.url) {
                embed.addFields({
                    name: '> URL sticker',
                    value: `- [Xem sticker](${sticker.url})`,
                    inline: true
                });
            }
            
            // Add usage instructions
            embed.addFields({
                name: '> 💡 Cách sử dụng',
                value: [
                    '• Gõ tên sticker trong tin nhắn',
                    '• Sử dụng sticker picker',
                    '• Autocomplete khi gõ `:tên_sticker:`',
                    '• Có thể dùng trong tất cả kênh của server'
                ].join('\n'),
                inline: false
            });
            
            // Add sticker requirements
            embed.addFields({
                name: '> 📋 Yêu cầu sticker',
                value: [
                    '• Server cần Discord Nitro Server Boost Level 2+',
                    '• Kích thước tối đa: 512KB',
                    '• Kích thước khuyến nghị: 320x320px',
                    '• Định dạng hỗ trợ: PNG, APNG, Lottie, GIF'
                ].join('\n'),
                inline: false
            });
            
            // Add server sticker limits
            const currentStickerCount = sticker.guild.stickers.cache.size;
            const maxStickers = sticker.guild.premiumTier >= 2 ? 60 : 0;
            
            embed.addFields([
                {
                    name: '> Số sticker hiện tại',
                    value: `- ${currentStickerCount}/${maxStickers} stickers`,
                    inline: true
                },
                {
                    name: '> Server Boost Level',
                    value: `- Level ${sticker.guild.premiumTier}`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do tạo',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set sticker as thumbnail if possible
            if (sticker.url) {
                embed.setThumbnail(sticker.url);
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${sticker.guild.name} • Sticker Create`,
                iconURL: sticker.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Sticker create logged: ${sticker.name}`);
            
        } catch (error) {
            console.error('Error in stickersCreate audit log:', error);
        }
    }
};
