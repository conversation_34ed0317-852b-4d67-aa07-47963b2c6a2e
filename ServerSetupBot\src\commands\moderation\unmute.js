const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('unmute')
        .setDescription('Unmute thành viên')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Thành viên cần unmute')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Lý do unmute')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // <PERSON><PERSON>m tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.ManageRoles)) {
            const errorEmbed = createErrorEmbed(
                '<PERSON>ạn không có quyền!',
                '<PERSON><PERSON><PERSON> cần quyền `Manage Roles` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                'Bot cần quyền `Manage Roles` để thực hiện lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const user = interaction.options.getUser('user');
        const reason = interaction.options.getString('reason') || 'Không có lý do';
        const guildId = interaction.guild.id;
        
        try {
            const member = await interaction.guild.members.fetch(user.id);
            
            // Tìm mute role
            const muteRole = interaction.guild.roles.cache.find(role => role.name === 'Muted');
            if (!muteRole) {
                const errorEmbed = createErrorEmbed(
                    'Không tìm thấy role mute!',
                    'Server này chưa có role "Muted".'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Kiểm tra nếu không bị mute
            if (!member.roles.cache.has(muteRole.id)) {
                const errorEmbed = createErrorEmbed(
                    'Thành viên không bị mute!',
                    'Thành viên này không có role mute.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Thực hiện unmute
            await member.roles.remove(muteRole, `${reason} | Bởi: ${interaction.user.tag}`);
            
            // Cập nhật database
            await client.db.removeMute(guildId, user.id);
            
            // Log vào moderation logs
            await client.db.addModerationLog(guildId, {
                type: 'unmute',
                userId: user.id,
                moderatorId: interaction.user.id,
                reason: reason,
                duration: null
            });
            
            // Gửi DM cho user
            try {
                const dmEmbed = createInfoEmbed(
                    `Bạn đã được unmute trong ${interaction.guild.name}`,
                    `**Lý do:** ${reason}\n**Bởi:** ${interaction.user.tag}\n**Thời gian:** <t:${Math.floor(Date.now() / 1000)}:F>`
                );
                await user.send({ embeds: [dmEmbed] });
            } catch (error) {
                console.log('Không thể gửi DM cho user được unmute');
            }
            
            const successEmbed = createSuccessEmbed(
                '🔊 Thành viên đã được unmute!',
                `**Thành viên:** ${user.tag} (${user.id})\n` +
                `**Lý do:** ${reason}\n` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            await interaction.reply({ embeds: [successEmbed] });
            
            // Gửi log vào channel
            await sendModerationLog(client, guildId, {
                type: 'unmute',
                user: user,
                moderator: interaction.user,
                reason: reason
            });
            
        } catch (error) {
            console.error('Lỗi khi unmute user:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi unmute thành viên!',
                'Đã xảy ra lỗi khi unmute thành viên. Vui lòng thử lại sau!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
