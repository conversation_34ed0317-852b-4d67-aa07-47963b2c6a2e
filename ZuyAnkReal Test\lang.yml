# ===========================================================================
# Drako Bot Language Configuration
# If you find any issues, need support, or have a suggestion for the bot, please join our support server and create a ticket, 
# http://discord.drakodevelopment.net
# ===========================================================================

# ===========================================================================
# BASIC MESSAGES
# ===========================================================================
NoPermsMessage: "You don't have permission to use this command."
SuccessEmbedTitle: "Successful"
ErrorEmbedTitle: "An error has occurred"

# ===========================================================================
# HELP COMMAND MESSAGES
# ===========================================================================
HelpCommand:
  MainEmbed:
    Title: "Welcome to {botName}'s Help Menu"
    Description:
      - "Please select a category from the dropdown menu to view the available commands."
    Footer:
      Text: "Drako Bot"
      Icon: ""
    Author:
      Text: "{user}" # User's name, {guild} Guilds name
      Icon: "{user-avatar}" # User's avatar, {guild-avatar}
    Color: "#1769FF"
    Image: ""
    Thumbnail: "{guild-avatar}"

  CategorySelectPlaceholder: "Choose a category..."
  BackButtonLabel: "Back to categories"

  Categories:
    General:
      Name: "General"
      Description: "Basic commands for general use"
      Emoji: "📋"
    Moderation:
      Name: "Moderation"
      Description: "Commands for server moderation"
      Emoji: "🛡️"
    Fun:
      Name: "Fun"
      Description: "Fun and entertainment commands"
      Emoji: "🎉"
    Economy:
      Name: "Economy"
      Description: "Commands to manage your virtual economy"
      Emoji: "💰"
    Utility:
      Name: "Utility"
      Description: "Useful tools and utilities"
      Emoji: "🔧"
    Uncategorized:
      Name: "Addon"
      Description: "Addon commands"
      Emoji: "❓"

  CategoryEmbed:
    Title: "{category} Commands"
    Description: []
    Color: "#5865F2"
    Footer:
      Text: "Use the back button to return to the category selection"
      Icon: ""
    Author:
      Text: "{botName}'s Commands"
      Icon: "" 

# ===========================================================================
# CRYPTO MESSAGES
# ===========================================================================
Crypto:
  Embed:
    Title: "Crypto Payment | {coinType}" # Also {coinType_Full}, displays the full coin name, BTC to Bitcoin for example
    Description:
      - "> Scan the QR code to make a payment,"
      - "> or use the below address."
      - " "
      - "* **Sale Information**" 
      - "> **Seller:** {seller}" 
      - "> **Client:** {client}" # User defined in slash command 
      - "> **Service:** {service}" 
      - " "
      - "* **Wallet Information**" 
      - "> **Wallet:** `{walletAddress}`" 
      - "> **Amount:** {cryptoAmount} ({currencySymbol}{currencyAmount})" # {currencySymbol} = $, £, etc
    Footer:
      Text: "Drako Development | Crypto Payments"
      Icon: "https://i.imgur.com/w5XxKpc.png"
    Author:
      Text: ""
      Icon: ""
    Color: "#ADD8E6"
    Image: ""
    Thumbnail: "{coinIcon}" # {qrCode} or {coinIcon}
    Buttons:
      - Name: "Get Wallet Address"
        Emoji: "📥"
        Style: "Primary" # Primary, Secondary, Success, Danger, Link
        Type: "PASTE"    # PASTE, LINK, QR
        Link: ""         # Only if LINK type is used
      - Name: "Terms of Service"
        Emoji: "📥"
        Style: "Link" # Primary, Secondary, Success, Danger, Link
        Type: "LINK"     # PASTE, LINK, QR
        Link: "https://google.com" # Only if LINK type is used
      - Name: "QR Code"
        Emoji: "📥"
        Style: "Primary" # Primary, Secondary, Success, Danger, Link
        Type: "QR"     # PASTE, LINK, QR
        Link: "" # Only if LINK type is used

Wallets:
  BTC: "Example" # Wallet Address
  LTC: "Example" # Wallet Address
  ETH: "Example" # Wallet Address

# ===========================================================================
# BACKUP COMMAND MESSAGES
# ===========================================================================
BackupCreating: "✅ | Creating backup, please wait..."
BackupCreatedEmbed: "Successfully **created a backup** of this guild!"
BackupCreatedMsg: "✅ | Successfully created a backup of this guild, The backup information has been sent to your DMS!"
BackupWrongUsage: "Please specify a valid backup ID!"
BackupDeleted: "Successfully deleted backup!"
BackupNotFound: "No backup found with that ID!"
BackupLoadWarning: "⚠️ All the server channels, roles, and settings will be cleared. Are you sure you want to load the backup?"
BackupLoading: "⚠️ Loading backup..."
BackupLoaded: "Backup loaded successfully!"
BackupCancelled: "Cancelled!"
BackupEmbedUsage: "Usage"
BackupCreatedBy: "Created by:"
BackupButtonConfirm: "Confirm"
BackupButtonCancel: "Cancel"

# ===========================================================================
# AVATAR + BANNER COMMAND MESSAGES
# ===========================================================================
AvatarSearchedBy: "Searched by:"
AvatarClickHere: "Click here for image"

BannerClickHere: "Click here to see the full banner"
BannerSearchedBy: "Banner searched by"
NoBannerSet: "This user does not have a banner set."

# ===========================================================================
# MEME COMMAND MESSAGES
# ===========================================================================
MemeViewThread: "View thread"

# ===========================================================================
# COMPLIMENT COMMAND MESSAGES
# ===========================================================================
compliments:
  messages:
    - "You're more helpful than you realize."
    - "Your talent for kindness is unmatched."
    - "You have an amazing sense of humor."
    - "You bring out the best in other people."
    - "Your perspective is refreshing."
    - "You light up the room."
    - "You have a great sense of style."
    - "Your kindness is a balm to all who encounter it."
    - "You're like a ray of sunshine on a really dreary day."
    - "You're a smart cookie."
    - "I bet you do the crossword puzzle in ink."
    - "You're someone's reason to smile."
    - "You have the best ideas."
    - "You always know how to find that silver lining."
    - "Everyone gets knocked down sometimes; only people like you get back up again and keep going."
    - "You're a candle in the darkness."
    - "You're a great example to others."
    - "Being around you is like being on a happy little vacation."
    - "You always know just what to say."
    - "You're always learning new things and trying to better yourself, which is awesome."
    - "Your creativity is contagious."
    - "You have a natural grace and elegance."
    - "Your kindness is a treasure to all who know you."
    - "You have an incredible work ethic."
    - "Your positivity is inspiring."
    - "You have a way of making everything better."
    - "You're braver than you believe, and stronger than you seem."
    - "You're a true problem-solver, always finding a way through."
    - "Your smile is contagious."
    - "You light up any room you're in."
    - "Your energy is infectious."
    - "You have a great sense of humor that brightens everyone's day."
    - "You're all kinds of awesome."
    - "You have such a great heart."
    - "Your passion for what you love is admirable."
    - "You're making a difference in the world."
    - "You're more fun than bubble wrap."
    - "On a scale from 1 to 10, you're an 11."
    - "You're like a breath of fresh air."
    - "You're someone's reason to smile."
    - "You're even better than a unicorn because you're real."
    - "How do you keep being so funny and making everyone laugh?"
    - "You have a really good taste in [music/books/movies]."
    - "Your ability to recall random factoids at just the right times is impressive."
    - "You're always learning new things and trying to better yourself, which is awesome."
    - "You could survive a zombie apocalypse."
    - "You're more fun than a ball pit filled with candy."
    - "You're the most perfect you there is."
    - "Your kindness is a balm to all who encounter it."

# ===========================================================================
# DARK JOKE COMMAND MESSAGES
# ===========================================================================
darkjokes:
  messages:
    - "Give a man a match, and he'll be warm for a few hours. Set a man on fire, and he will be warm for the rest of his life."
    - "My wife and I have reached the difficult decision that we do not want children. If anybody does, please just send me your contact details and we can drop them off tomorrow."
    - "What do you give an armless child for Christmas?\nNothing, he wouldn’t be able to open it anyways."
    - "I took away my ex-girlfriend’s wheelchair.\nGuess who came crawling back to me?"
    - "When does a joke become a dad joke?\nWhen it leaves and never comes back."
    - "Can orphans eat at a family restaurant?"
    - "A man went into a library and asked for a book on how to commit suicide. The librarian said: “Fuck off, you won’t bring it back.”"
    - "My grandma with Alzheimer's used to tell us a joke.\nShe’d say “Knock knock”, we’d say “Who’s there?”\nThen she’d say “I can’t remember”… and start to cry."
    - "Why can’t orphans play baseball?\nThey don’t know where home is."
    - "Where did Suzy go after getting lost on a minefield?\nEVERYWHERE!"
    - "I’ve been looking for my ex-girlfriend’s killer for the past two years. But no one would do it."
    - "What was Steven Hawking’s last words?\nThe windows xp log out sound"
    - "When you hit a speed bump in a school zone and remember, there are no speed bumps."
    - "Two kids were beating up a kid in an alley, so I stepped in to help. He didn’t stand a chance against the three of us."
    - "My ex got into a bad accident recently. I told the doctors the wrong blood type. Now she will really know what rejection feels like."
    - "When Jim was playing on his phone, my grandfather told him, “You use way too much technology!”. Jim then said, “No, YOU use too much technology!” and then Jim disconnected his grandfather’s life support."
    - "I will always remember my grandpa’s last words: Stop shaking the ladder you cunt!"
    - "Would you like to try African food??\nThey would too."
    - "Kids in the backseat make accidents and accidents in the back seat make kids."
    - "What do you do when you finish a magazine at a hospital? Reload and keep shooting."
    - "How do you throw a surprise party at a hospital?\nBring a strobe light into the epilepsy ward."
    - "I have a fish that can breakdance! Only for 20 seconds, though, and only once."
    - "What’s the last thing to go through a fly’s head as it hits the windshield of a car going 70 miles per hour?\nIts butt."
    - "My dad died when we couldn’t remember his blood type. As he died, he kept insisting for us to “be positive,” but it’s hard without him."
    - "You don’t need a parachute to go skydiving.\nYou need a parachute to go skydiving twice."
    - "My girlfriend, who’s into astronomy, asked me how stars die. “Usually an overdose,” I told her."
    - "My elderly relatives liked to tease me at weddings, saying, “You’ll be next!” They soon stopped, though, once I started doing the same to them at funerals."
    - "My wife and I have made a difficult choice and have decided we do not want children.\nIf anybody does, please just send me your contact details, and we can drop them off tomorrow."
    - "I want to die peacefully in my sleep, just like my grandfather,\nNot screaming like the passengers in his car."
    - "I started crying when dad was cutting onions. Onions was such a good dog."
    - "If at first, you don’t succeed… then skydiving definitely isn’t for you."
    - "They say there’s a person capable of murder in every friendship group.\nI suspected it was Dave, so I killed him before he could cause any harm."
    - "I’ll never forget my Granddad’s last words to me just before he died. “Are you still holding the ladder?”"
    - "What’s yellow and can’t swim? A bus full of children."
    - "The doctor gave me one year to live, so I shot him. The judge gave me 15 years. Problem solved."
    - "When we were kids, we used to be afraid of the dark.\nBut when we grew up, the electricity bill made us afraid of the light!"
    - "Patient: Oh doctor, I’m just so nervous. This is my first operation.\nDoctor: Don’t worry. Mine too."
    - "I hate how funerals are always at 9 a.m. I'm not really a mourning person."
    - "Why are friends a lot like snow? If you pee on them, they disappear."
    - "My therapist says I have a preoccupation with revenge. We'll see about that."
    - "It's important to have a good vocabulary. If I had known the difference between 'antidote' and 'anecdote,' one of my good friends would still be alive."
    - "They say you are what you eat. I don’t remember eating a massive disappointment."
    - "Why can't you hear a psychiatrist using the bathroom? Because the 'P' is silent."
    - "I have a joke about trickle-down economics. But 99% of you will never get it."
    - "It turns out a major new study found that humans eat more bananas than monkeys. I can't remember the last time I ate a monkey."
    - "I told my wife she should embrace her mistakes. She gave me a hug."
    - "My wife left a note on the fridge saying, 'This isn't working, goodbye.' I opened it and it works fine."
    - "You know you’re not liked when you get handed the camera every time they take a group photo."
    - "Why do graveyards never get overcrowded? Because people are dying to get in."
    - "I have an EpiPen. My friend gave it to me when he was dying. It seemed very important to him that I have it."
    - "Today was a terrible day. My ex got hit by a bus, and I lost my job as a bus driver."
    - "I wasn’t planning on going for a run today, but those cops came out of nowhere."
    - "I told the paramedics the wrong blood type for my ex, so he knows what rejection feels like."
    - "My grief counselor died the other day. He was so good at his job, I don’t even care."
    - "The inventor of autocorrect died today. His funfair will be hello on Sundial."
    - "I refused to believe my dad was stealing from his job as a traffic cop, but when I got home, all the signs were there."
    - "Give a man a match, and he'll be warm for a few hours. Set him on fire, and he will be warm for the rest of his life."

# ===========================================================================
# FACT COMMAND MESSAGES
# ===========================================================================
 
# ===========================================================================
# GAMES COMMAND MESSAGES
# ===========================================================================
Roll: "🎲 You rolled a ${rollResult}!"

wordSnake:
  start: "Word Snake game started! The first word is: `${word}`. The next word must start with the letter `${letter}`."
  invalidWord: "Invalid word! Remember, the word must start with the letter `${letter}`."
  timeUp: "Time's up! The last word was `${lastWord}`. The game is over, and the final score is `${score}`."
  gameOver: "The game is over! Final score: `${score}`."

Hangman:
  commandDescription: "Play Hangman!"
  activeGameWarning: "You already have an active game. Please finish it before starting a new one."
  gameStartMessage: "Starting Hangman game... Type your guesses!"
  winMessage: "Congratulations, {username}! You won!"
  gameOverMessage: "Game Over! The word was: {word}"
  continueMessage: "Keep guessing!"
  words:
    - Ocean
    - Pirate
    - Castle
    - Rainbow
    - Forest
    - Dragon
    - Music
    - Cookie
    - Pumpkin
    - Lantern
    - Shadow
    - Comet
    - Candle
    - Guitar
    - Puzzle
    - Dolphin
    - Rocket
    - Wizard
    - Knight
    - Circus
    - Discord 
    - Drako
    - Adventure
    - Balloon
    - Bicycle
    - Butterfly
    - Campfire
    - Chocolate
    - Dinosaur
    - Elephant
    - Fireworks
    - Galaxy
    - Gemstone
    - Helicopter
    - Igloo
    - Jellyfish
    - Kaleidoscope
    - Lighthouse
    - Meteor
    - Necklace
    - Octopus
    - Painting
    - Quicksand
    - Robot
    - Spaceship
    - Treasure
    - Unicorn
    - Volcano
    - Waterfall
    - Xylophone
    - Yeti
    - Zebra
    - Blossom
    - Canyon
    - Desert
    - Enigma
    - Fairy
    - Garden
    - Horizon
    - Island
    - Juggler
    - Kite
    - Labyrinth
    - Meadow
    - Nebula
    - Oasis
    - Palace
    - Quasar
    - Reef
    - Starship
    - Temple
    - Universe
    - Vortex
    - Whistle
    - Xenon
    - Yard
    - Zeppelin
    - Apple
    - Banana
    - Cherry
    - Date
    - Fig
    - Grape
    - Honeydew
    - Jackfruit
    - Kiwi
    - Lemon
    - Mango
    - Nectarine
    - Orange
    - Papaya
  embeds:
    gameTitle: "🔤 Hangman Game: {username}"
    wordToGuess: "Word to Guess"
    guessesLeft: "Guesses Left"
    progress: "Progress"
    useChatToGuess: "Use the chat to guess a letter.\nType only one letter per message."
    guessFooter: "Type your guess in the chat. Incorrect guesses will deplete your guesses left!"
    gameOverTitle: "Game Over"
    finalWord: "Final Word:"
    gameEndMessage: "Game has ended."
    thanksForPlaying: "Thanks for playing!"

Wordle: # 5 letter words (it's wordle)
  Messages:
    Title: "Wordle"
    BottomFirst: "Guess the 5-letter word"
    BottomLast: "Use the chat to guess!"
    KeepGuess: "Keep guessing!"
    GameActive: "You already have an active game!"
    LetsPlay: "Let's play Wordle! Try to guess the 5-letter word."
  Embed:
    Title: "**Game Over**"
    Description: "Game over! The word was {word}"
    Footer: "Thanks for playing!"
  words:
    - Apple
    - Bread
    - Crate
    - Drink
    - Eagle
    - Frost
    - Grape
    - House
    - Ideal
    - Juice
    - Knife
    - Lemon
    - Mango
    - Night
    - Olive
    - Peach
    - Quiet
    - River
    - Stone
    - Table
    - Unity
    - Vivid
    - Whale
    - Xylog
    - Zebra

Connectfour:
  Title: "It's {user}'s turn!"
  Colors:
    Win: "#7ff946"
    Lose: "#df3e3e"
    Tie: "#ffffff"
  Embed:
    Title: "**Game Over**"
    Description: "{user} has won!"
    Footer: "Thanks for playing!"
  Board:
    Emojis:
      Player: "🔴"
      Bot: "🟡"
      Blank: "⬛"
      1: "1️⃣"
      2: "2️⃣"
      3: "3️⃣"
      4: "4️⃣"
      5: "5️⃣"
      6: "6️⃣"
      7: "7️⃣"
  Buttons:
    Style: "Secondary"
    Numbers:
      1:
        Text: ""
        Emoji: "1️⃣"
      2:
        Text: ""
        Emoji: "2️⃣"
      3:
        Text: ""
        Emoji: "3️⃣"
      4:
        Text: ""
        Emoji: "4️⃣"
      5:
        Text: ""
        Emoji: "5️⃣"
      6:
        Text: ""
        Emoji: "6️⃣"
      7:
        Text: ""
        Emoji: "7️⃣"

TicTacToe:
  Board:
    Emojis:
      X: "❌"
      O: "⭕"
      Blank: "⬛"
  Colors:
    Win: '#00FF00'
    Lose: '#FF0000'
    Tie: '#FFFF00'
  Messages:
    OwnGame: "Start your own game!"
    ColumnFull: "This column is full, try another one"
    Win: "## **Well done {user}! You won!**"
    Tie: "## **It's a tie {user}!**"
    Lost: "## **Sorry {user}, you lost!**"
    Columns: "Column numbers. Use the buttons below to make a move."
    GameOver: "Game Over"
    ThanksForPlaying: "Thanks for playing!"

RockPaperScissors:
  Messages:
    RockPaperScissors: "**RockPaperScissors**"
    YourChoice: "## You chose **{userChoice}**, and the bot chose **{botChoice}**."
    Win: "## **Well done {user}! You won!**"
    Tie: "## **It's a tie {user}!**"
    Lost: "## **Sorry {user}, you lost!**"
    GameEnded: "Game ended!"
    ThanksForPlaying: "Thanks for playing!"
    CantPlaySomeoneElsesGame: "You cannot play someone else's game!"
  Embed:
    Color: "#000000"
    Title: "👊 Rock Paper Scissors"
    Description: "**Let's play! Choose your weapon below:**"
    Fields:
      Rock:
        Name: "Rock"
        Value: "🪨"
      Paper:
        Name: "Paper"
        Value: "📰"
      Scissors:
        Name: "Scissors"
        Value: "✂️"
    Footer:
      Text: "Make your choice!"
    Buttons:
      Rock:
        Style: "Secondary"
        Text: "Rock"
        Emoji: "🪨"
      Paper:
        Style: "Secondary"
        Text: "Paper"
        Emoji: "📰"
      Scissors:
        Style: "Secondary"
        Text: "Scissors"
        Emoji: "✂️"

# ===========================================================================
# SNIPE COMMAND MESSAGES
# ===========================================================================
SnipeNoMsg: "No message to snipe in this channel."
ErrorGeneric: "Sorry, there was an error with the command."

# ===========================================================================
# GIVEAWAY COMMAND MESSAGES
# ===========================================================================
Giveaways: # Placeholders: {prize} {serverName} {hostedBy} {whitelistRoles} {blacklistRoles} {channel} {winnerCount}
 EntrySuccessMessage: "🎉 You have successfully entered the giveaway hosted by {hostedBy}!"
 LeaveSuccessMessage: "👋 You have successfully left the giveaway."
 EntryErrorMessage: "An error occurred while trying to enter the giveaway. Please contact Drako Development."
 CheckChance: "{user} you have a {percent}% chance of winning this giveaway. ({entries} entries)"
 GiveawayNotFound: "Giveaway not found or it might have been deleted...."
 GiveawayAlreadyEnded: "Giveaway already ended."
 GiveawayHasntEnded: "Giveaway has not ended yet."
 WinMessage: "Congratulations to the winners of the **{prize}** giveaway!  {winners}" # {winners} placeholder
 WinRerollMessage: "Giveaway rerolled! Congratulations to the new winners of the **{prize}** giveaway!  {winners}" # {winners} placeholder
 WinnerDirectMessage: "Congrats, you won the **{prize}** giveaway! Please open a ticket in **{serverName}** server. {channel}"
 NoParticipationMessage: "No winners, nobody entered the {prize} giveaway."
 IncorrectRoleMessage: "You do not have the required role(s) to join the {prize} giveaway!"
 IncorrectMinimumAccountAgeMessage: "Your account isn't old enough to participate in the {prize} giveaway!"
 IncorrectMinimumServerJoinDateMessage: "You haven't been in the {serverName} discord server long enough to participate in the {prize} giveaway!"
 AlreadyEnteredMessage: "You have already entered the {prize} giveaway!"
 IncorrectInviteCountMessage: "You need at least {minInvites} invites to enter this giveaway."

 Embeds:
  ActiveGiveaway:
    Prize: "## {prize} Giveaway x{winnerCount}!"
    HostedByField: "Hosted by:"
    EndsInField: "Ends in:"
    EntriesField: "Entries:"
    WhitelistRoleField: "Who can enter"
    BlacklistRoleField: "Who can't enter"
    MinimumSeverJoinDateField: "Minimum server join date"
    MinimumAccountAgeField: "Minimum account age"
  
  EndedGiveaway:
    Title: "## The {prize} giveaway has ended!"
    WinnersField: "Winners:"
    EntriesField: "Entries:"

# ===========================================================================
# AFK COMMAND MESSAGES
# ===========================================================================
AFK:
 Success: "AFK status set"
 ErrorMessges:
   NoPermission: "You do not have permission to use the AFK command."
   NickNameMissingPermissions: "Could not set nickname due to missing permissions."
   NickNameOwner: "Your AFK status is set, but I can't change your nickname because you own the server"
   AlreadyMarkedAFK: "You are already marked as AFK!"
   TimeFormat: "Invalid time format. Please use the format: 1s (seconds), 1m (minutes), 1h (hours), 1d (days)"
   TimeParseError: "Could not parse the time input. Please ensure it's in a correct format."
   RoleCreate: "There was an error creating the AFK role. Please check my permissions."

# ===========================================================================
# VERIFICATION MESSAGES
# ===========================================================================
Verify:
  Required: "Verification Required"
  QuestionFormat: "What is {num1} {operator} {num2}? Answer Quickly!"
  AlreadyVerified: "You are already verified!"
  Success: "You have successfully been verified!"
  Timeout: "You took too long to verify. Please try again."
  Incorrect: "Incorrect, try again."
  NoRole: "The unverified role configured in the server does not exist. Please contact the server administrator."
  Error: "There was an error verifying your account. Please contact server staff."

# ===========================================================================
# LEADERBOARD & LEVEL COMMAND MESSAGES
# ===========================================================================
Leaderboard:
  Balance:
    Top: "🏆 Leaderboard - Balance"
    Footer: "Page {pageMin}/{pageMax} • Updated at {time}"
    Messages: "{count} 🪙"
  Levels:
    Top: "🏆 Leaderboard - Levels"
    Footer: "Page {pageMin}/{pageMax} • Updated at {time}"
    Messages: "Level {count_level}, XP {count_xp}"
  Messages:
    Top: "🏆 Leaderboard - Messages"
    Footer: "Page {pageMin}/{pageMax} • Updated at {time}"
    Messages: "{count} messages"
  Invites:
    Top: "🏆 Leaderboard - Invites"
    Footer: "Page {pageMin}/{pageMax} • Updated at {time}"
    Messages: "{count} invites"
  Error: "An error occurred while fetching the leaderboard for {guild}."
  Button:
    Previous: "Previous"
    Next: "Next"
  Embed:
    Colour: "#000000"
    
Levels:
  NoPermission: "Access denied. You lack the necessary permissions to modify XP or levels."
  DataNotFound: "No records found for {user}. Please ensure the user exists within the system."
  CurrentLevelAndXP: "{user} is currently at level {level}, with a total of {xp} XP."
  UpdatedXP: "The XP for {user} has been successfully updated to {xp}."
  UpdatedLevel: "The level for {user} has been successfully updated to {level}."
  UpdatedXPAndLevel: "The XP and level for {user} have been updated to {xp} and {level}, respectively."

  LevelMessages:
    - "Hey {user}, you’ve just leveled up to {newLevel}! Guess all those late-night Discord sessions paid off! 🎉"
    - "{user}, congrats on reaching level {newLevel}! Your dedication to procrastination is truly inspiring. 😅"
    - "{user} is now level {newLevel}! Just imagine what you could achieve if you applied this dedication elsewhere. 😂"
    - "🚨 Alert! {user} has reached level {newLevel}! Please send snacks and energy drinks for further leveling. 🍕🥤"
    - "Ding! {user} leveled up to {newLevel}! Looks like all that XP grinding was worth it, kinda. 🤔"
    - "{user} hit level {newLevel}! Time to take a screenshot and make it your profile banner! 📸"
    - "Wow {user}, you’ve made it to level {newLevel}. Your keyboard must be so proud! ⌨️👏"
    - "Level {newLevel} achieved by {user}! Keep going! 🚀"
    - "🎉 Congrats {user}! You've powered up to level {newLevel}! Keep that momentum going! ⚡️"
    - "🌟 {user} just hit level {newLevel}! Next stop, world domination! 🗺️"
    - "🥳 {user}, you've reached level {newLevel}! Your {guildName} buddies are cheering for you! 🎊"
    - "📈 Level {newLevel} unlocked by {user}! The grind is real, but so are the rewards! 💪"
    - "✨ Boom! {user} is now at level {newLevel}! Time to flex those new powers! 💥"
    - "🏆 {user}, you've made it to level {newLevel}! The {guildName} is proud to have you!"
    - "🎊 Congratulations {user}! Level {newLevel} suits you well! Keep up the great work! 🌟"
    - "💪 {user} has leveled up to {newLevel}! All that hard work is definitely paying off! 🎉"
    - "🔥 {user} reached level {newLevel}! Keep the fire burning and aim for the stars! 🚀"
    - "🎯 {user} is now level {newLevel}! Your determination is impressive! Keep it up! 🏆"
    - "🚀 {user}, welcome to level {newLevel}! Your journey is just beginning! Adventure awaits! 🌟"
    - "😂 {user}, you've leveled up to {newLevel}! Now take a break, your chair needs a rest! 🪑"
    - "👏 {user} hit level {newLevel}! Is there a medal for this? Because you deserve one! 🥇"
    - "😜 Level {newLevel} for {user}! Remember when you thought this was impossible? Look at you now!"
    - "🤣 {user} just leveled up to {newLevel}! Don't forget to step outside sometimes. The sun misses you. 🌞"
    - "🔥 {user} reached level {newLevel}! Your keyboard called, it wants a vacation. ✈️"
    - "💡 {user}, you've hit level {newLevel}! Maybe now is a good time to learn something useful... or not. 🤷‍♂️"
    - "🎉 Congrats {user}! Level {newLevel}! Are you sure you’re not a robot? 🤖"
    - "😂 {user}, at level {newLevel}, you might need a new mouse soon! Keep it up! 🖱️"
    - "🌟 {user} is now level {newLevel}! Your pet must wonder what you're doing. 🐾"
    - "🥳 {user} reached level {newLevel}! Time to celebrate with your virtual friends! 🎈"
    - "🤣 {user} at level {newLevel}! Maybe now you can finally take out the trash? 🗑️"
    - "🚀 {user}, level {newLevel} achieved! Your next mission: Find a life outside Discord! 🌍"
    - "🎊 {user}, congrats on leveling up to {newLevel}! We knew you had it in you, kinda. 😅"
    - "🔥 {user} is now level {newLevel}! Your gaming chair must be on fire! 🔥"

# ===========================================================================
# ADDROLE/REMOVEROLE COMMAND MESSAGES
# ===========================================================================
AddRole:
  AddroleSelfRole: "You cannot add a role to yourself."
  AddroleHighestRole: "The selected role is higher than my highest role."
  AddroleUserRoleNotAbove: "You cannot add a role higher or equal to your highest role."
  AddroleAlreadyHave: "The user already has this role."
  AddroleSuccess: "Successfully added {role} to {user}"
  AddroleError: "There was an error adding the role."
  RoleAllUserHighestRole: "You cannot assign a role that is higher than or equal to your highest role."

RoleAll:
  RoleAllConfirmationAdd: "Are you sure you want to add the {role} role to all users?"
  RoleAllConfirmationRemove: "Are you sure you want to remove the {role} role from all users?"
  RoleAllHighestRole: "The role you selected is higher or equal to the bot's highest role. Action not allowed."
  RoleAllUserHighestRole: "The role you selected is higher or equal to your highest role. Action not allowed."
  RoleAllSuccessAdd: "Successfully added the {role} role to all users."
  RoleAllSuccessRemove: "Successfully removed the {role} role from all users."
  RoleAllError: "An error occurred while adding/removing the role."
  RoleAllCancelled: "Action has been cancelled."
  RoleAllTimeOut: "The command timed out. No action taken."

Temprole:
  SameOrHigherRoleError: "You cannot assign roles that are the same rank or higher than your current highest role."
  InvalidDurationFormat: "Invalid duration format. Please use formats like 1h, 15m, 1d, etc."
  RoleAssigned: "Temporary role {role} assigned to {user} for {duration}."
  ErrorAssigningRole: "There was an error assigning the temp role."
  MissingPermissionsError: "The bot does not have sufficient permissions to assign this role."
  UnknownRoleError: "The specified role is invalid or unknown. Please specify a valid role."

# ===========================================================================
# HISTORY/CLEARHISTORY COMMAND MESSAGES
# ===========================================================================
History:
  HistoryEmbedTitle: "User History for {user-tag}"
  HistoryEmbedUserInfo: "👤 User Information"
  HistoryEmbedName: "Name"
  HistoryEmbedJoinedServer: "📆 Joined Server On"
  HistoryTotalMessages: "💌 Total Messages"
  HistoryEmbedNote: "📝 Note"
  HistoryEmbedWarnings: "⚠️ Warnings"
  HistoryEmbedTimeouts: "⏲️ Timeouts"
  HistoryEmbedKicks: "👢 Kicks"
  HistoryEmbedBans: "🔨 Bans"

ClearhistorySuccess: "Successfully cleared {user}'s history!" # Available variables: {user}

# ===========================================================================
# POLL COMMAND MESSAGES
# ===========================================================================
PollEmbedTitle: "A poll has been started!"
PollEmbedFooter: "Poll Started By:"

# ===========================================================================
# PURGE COMMAND MESSAGES
# ===========================================================================
Purge:
  NoPermsMessage: "You do not have permission to purge messages."
  PurgeCleared: "{amount} messages have been successfully purged."
  PurgeOld: "Error: Unable to purge messages older than 14 days."
  ModerationEmbedTitle: "Moderation Action"
  ModerationEmbedAction: "Action: Purge"
  ModerationEmbedDetails: "Staff: <@{user-id}>\nAmount: {amount}\nChannel: {channel}"

# ===========================================================================
# SLOWMODE COMMAND MESSAGES
# ===========================================================================
SlowmodeReset: "The cooldown for this channel has been reset."
SlowmodeFailed: "Failed to set slowmode in this channel, check your slowmode length."
SlowmodeSuccess: "The cooldown for this channel has been set to **{time}** seconds." # Available variables: {time}

# ===========================================================================
# ANTIHOIST COMMAND MESSAGES
# ===========================================================================
AntiHoist:
  NoPermission: "You do not have permission to use the antihoist command."
  CommandCompleted: "Successfully executed the antihoist command."
  NotifyUserMessage: "Hello {user}! Your username has been changed from `{oldDisplayName}` to `{newDisplayName}` in **{guildName}** because they dont allow name hoisting."
# ===========================================================================
# WARN COMMAND MESSAGES
# ===========================================================================
Unwarn:
  NoPermission: "You don't have permission to use this command."
  NoWarnings: "No warnings found for this user."
  InvalidWarningID: "Invalid warning ID."
  WarningRemoved: "Warning removed from {userTag}: `{reason}`."
  Error: "An error occurred while processing your request."

Warn:
  BotOrSelf: "You cannot warn a bot or yourself."
  Success: "User {userTag} has been warned for the following reason: {reason}"
  Error: "There was an error trying to warn this user."

# ===========================================================================
# REMINDER COMMAND MESSAGES
# ===========================================================================
Reminder:
  Messages:
    invalid_mentions: "Your message contains disallowed mentions. Please remove any `@everyone`, `@here`, or role mentions."
    invalid_format: "Please use a valid format such as '10m', '1h', '2d'."
    permission_denied: "You do not have permission to set reminders for other users."
  Embeds:
    Reminder:
      Title: "Reminder Set Successfully"
      Description:
        - "Reminder set for **{user}** in **{time}**."
      Footer:
        Text: "Drako Development | Reminder System"
        Icon: "https://i.imgur.com/w5XxKpc.png"
      Author:
        Text: ""
        Icon: ""
      Color: "#00FF00"
      Image: ""
      Thumbnail: ""
    DM:
      Title: "🔔 Reminder!"
      Description: "{message}"
      Footer:
        Text: "Reminder time"
        Icon: "https://i.imgur.com/yygn7ni.png"
      Author:
        Text: ""
        Icon: ""
      Color: "#0099FF"
      Image: ""
      Thumbnail: ""

# ===========================================================================
# MUSIC COMMAND MESSAGES
# ===========================================================================
Music:
  NoPermission: "You do not have permission to use the music command."
  Error: "Something went wrong."
  NotInVoiceChannel: "You must be in a voice channel to use them music command."
  NotInSameVoiceChannel: "You must be in the same voice channel to use them music command."
  AddedToQueue: "Added track to queue."
  QueryNotFound: "No results found for your query."
  NoMusicInQueue: "No music in queue."
  QueueCleared: "Cleared the queue!"
  NoPreviousMusic: "No music to go back to."
  NoMusicPlaying: "There is no music currently playing."
  Stopped: "All music has been successfully stopped."
  AddingTrack: "Adding track to queue"
  Skipped: "Skipped **{title}**."
  NothingToSkip: "There is nothing to skip."
  Paused: "Paused **{title}**."
  AlreadyPaused: "**{title}** is already paused."
  Resumed: "Resumed **{title}**."
  AlreadyResumed: "**{title}** is already resumed."
  Seeked:
   Success: "Seeked to **{time}** in the track."
   Error: "Failed to seek to position **{time}** in the track."
  WentBackATrack: "Went back to the previous track."
  Looping:
   Off: "Looping is now **{state}**."
   Track: "Looping mode: **{state}**."
   Queue: "Looping mode: **{state}**."
   Autoplay: "Looping mode: **{state}**."
  Move:
   InvalidPosition: "Invalid track position or move position."
   Success: "Moved **{track}** to position **{newPosition}** in the queue."
   Error: "An error occurred while trying to move **{track}**."
  Filters:
   EmbedError: "An error occurred while trying to show the filters embed."
   Fields:
    Enabled: "Enabled"
    Disabled: "Disabled"
  NowPlaying:
    GeneratingError: "Errror generating the now playing image."
  Volume:
    Success: "Volume set to {volume}%"
    Error: "Failed to set volume. Please try again."

# ===========================================================================
# TICKET MESSAGES
# ===========================================================================
Tickets:
  WorkingHours: "This ticket has been raised outside of working hours. Please wait for our working hours to resume.\nOur working hours are from {workinghours_start} to {workinghours_end}."
  Deleting: "This ticket is now being deleted."
  Deletion: "The ticket will now be deleted."
  DeleteCountDown: "This ticket will be deleted in {time} seconds."
  Archive: "This ticket is now being archived."
  Closed: "This ticket has now been closed."
  TicketTypePlaceholder: "Select a ticket type"
  AlreadyOpen: "You already have a ticket open! Close it before opening another."
  CloseTicketButton: "Close Ticket"
  ReviewAlreadySubmitted: "You have already submitted a review for this ticket."
  ReviewTitle: "Review Feedback"
  ReviewPlaceholder: "Please tell us why you gave this rating:"
  ReviewComplete: "Thank you for your feedback!"
  TicketReopenTitle: "Ticket Reopened"
  TicketReopenDescription: "Ticket has been reopened by <@{userId}>."
  TicketReopen: "Ticket reopened!"
  TranscriptTitle: "Transcript Generated"
  TranscriptDescription: "Transcript for ticket ID {ticketId} has been generated by <@{userId}>."
  TranscriptReady: "Here is the transcript for your ticket:"
  TranscriptNotEnough: "Not enough messages to generate a transcript."

  TicketCreated:
    LinkText: "Go to ticket"
    Embed:
      Title: "Ticket Created"
      Description:
        - "Your ticket has been created. {link}"
      Footer:
        Text: ""
        Icon: ""
      Author:
        Text: ""
        Icon: ""
      Color: "#00FF00"
      Image: ""
      Thumbnail: ""
    Button: 
      Text: "Go to Ticket"
      Emoji: "🟢"

  Blacklisted:
    Embed:
      Title: "🚫 Blacklisted from Opening Tickets"
      Description:
        - "You are blacklisted from opening tickets."
        - "Contact an administator if you'd like to appeal the ban." 
        - " "
        - "> **Blacklist By:** {user}"
        - "> **Reason:** {reason}"
        - "> **Banned At:** {time}"
      Footer:
        Text: "Contact an admin if you believe this is a mistake."
        Icon: "https://e7.pngegg.com/pngimages/1003/312/png-clipart-hammer-game-pension-review-internet-crouton-hammer-technic-discord-thumbnail.png"
      Author:
        Text: ""
        Icon: ""
      Color: "#FF0000"
      Image: ""
      Thumbnail: ""

# ===========================================================================
# MODERATION LOGS
# ===========================================================================
ModerationEmbedTitle: "Moderation Action"
ModerationEmbedAction: "Action:"
ModerationEmbedDetails: "Details:"

Lockdown:
  Start: "The server is now in lockdown mode."
  End: "Lockdown mode has been lifted."
  Error: "An error occurred while toggling lockdown mode."

ClearChannel:
  ClearChannelPrompt: "Are you sure you want to clear this channel?"
  ClearChannelCleared: "This channel was cleared by {user}."
  ClearChannelGif: "https://media1.tenor.com/images/e275783c9a40b4551481a75a542cdc79/tenor.gif?itemid=3429833"
  CancelClear: "Channel clear cancelled."
  ClearTimeout: "Channel clear timed out."

Nickname:
  Description: "Change a user's nickname."
  UserOptionDescription: "The user whose nickname you want to change."
  NicknameOptionDescription: "The new nickname for the user."
  NoPermsMessage: "You do not have permission to change nicknames."
  UserNotFound: "The specified user was not found in the guild."
  NicknameChangeSuccess: "Successfully changed {user}'s nickname to {nickname}."
  NicknameChangeFailure: "Failed to change the user's nickname. Please try again later."

Ban:
  UserNotFound: "The specified user could not be found."
  UserFailedDM: "The user has been banned but I cannot DM them as they have them disabled."
  CantBanSelf: "You cannot ban yourself."
  CantBanUser: "I cannot ban this user. They might have a higher role than me or the same role as me."
  Success: "Successfully banned {userTag} for {reason}."
  Error: "There was an error trying to ban this user."

TempBan:
  NoPermsMessage: "You do not have the necessary permissions to use this command."
  UserNotFound: "The specified user could not be found. Please check the input."
  InvalidDuration: "Invalid duration format. Please use a valid format like '1d 2h 15m'."
  CantBanUser: "This user cannot be banned. They might have a higher role or special permissions."
  Success: "Successfully temp-banned {userTag} for {duration}. Reason: {reason}."
  Error: "An error occurred while trying to temp-ban the user. Please try again later."

Unban:
  UnbanUserNotBanned: "This user is not currently banned."
  UnbanMsg: "Successfully unbanned {user}."
  UnbanError: "An error occurred while processing the unban."

Kick:
  UserNotFoundInGuild: "User not found in this guild."
  CannotKickSelf: "You cannot kick yourself."
  CannotKickUser: "Cannot kick this user."
  DMFailed: "Failed to send DM."
  KickSuccess: "Successfully kicked {userTag} for: {reason}"
  KickError: "There was an error trying to kick this user."

Timeout:
  NoPermsMessage: "You do not have permission to use this command."
  UserNotFound: "User not found."
  InvalidTime: "Invalid time specified. Time must be at least 10 seconds or 'perm' for permanent mute."
  Success: "Successfully timed out {user} for {time}. Reason: {reason}"
  LongMuteSuccess: "Successfully muted {user} for {time}. Reason: {reason}"
  PermanentMuteSuccess: "Successfully muted {user} permanently. Reason: {reason}"
  Error: "An error occurred while trying to timeout/mute the user."

ClearTimeout:
  NoPermsMessage: "You do not have permission to use this command."
  UserNotFound: "User not found."
  TimeoutRemoved: "Timeout has been removed for {user}."
  MuteRemoved: "Mute has been removed for {user}."
  Error: "An error occurred while trying to clear the timeout/mute."

Removerole:
  Self: "You can't remove roles from yourself."
  NoRole: "{user} doesn't have the {role} role."
  AddroleHighestRole: "You cannot modify a role higher or equal to your highest role."
  Error: "An error occurred while removing the role."
  UserMsg: "You have been removed from the role **{role}** in {guild-name}"
  EmbedTitle: "Role Removed"
  EmbedRemovedBy: "Removed By:"
  EmbedRemovedFrom: "Removed From:"
  EmbedIconURL: "https://i.imgur.com/MdiCK2c.png" 
  RemoveroleNoRole: "{user} doesn't have {role}"
  HigherRoleError: "You cannot remove a role higher or equal to your highest role."

SetNote:
  NoteLongerThan250: "Notes can't be longer than 250 characters!"
  NoteCantAddBot: "You can't add a note to a bot!"
  NoteSuccess: "Successfully added note to **{user}**"
  NoteError: "There was an error setting the note."

AddroleSuccess: "Added role {role} to {user}."
AddroleError: "An error occurred while adding the role."

# ===========================================================================
# 8BALL COMMAND MESSAGES
# ===========================================================================
EightBallQuestion: "Question"
EightBallAnswer: "Answer"
EightBallReplies: ["Never", "Absolutely", "Absolutely, You should also go buy some stocks", "Without a doubt", "Yes, definitely", "It is certain", "Not so sure", "You tell me", "Unclear, ask again", "Possible, but not probable", "Yes", "My reply is yes", "For sure", "Could be", "Negative", "Most likely", "As I see it, yes", "No", "My reply is no", "I see good things happening", "Absolutely not", "Better not tell you now", "Signs point to yes", "It is decidedly so"]

# ===========================================================================
# SUGGESTION SYSTEM MESSAGES
# ===========================================================================
Suggestion:
  SuggestionCreated: "Suggestion created successfully."
  Error: "An error occurred. Please contact support."
  SuggestionNotFound: "Suggestion not found."
  AlreadyVoted: "You have already voted on this suggestion."
  Upvoted: "You have upvoted this suggestion!"
  Downvoted: "You have downvoted this suggestion!"
  AlreadyProcessed: "This suggestion has already been processed."
  SuggestionAccepted: "You have accepted this suggestion."
  SuggestionDenied: "You have denied this suggestion."
  ModalTitle: "Post your suggestion!"
  ModalQuestion: "What is your suggestion?"
  SuggestionsDisabled: "Suggestions are currently disabled."
  BlacklistMessage: "You are blacklisted and cannot create suggestions."
  Reason: "No reason provided"
  DirectMessage:
    Accepted: "Your suggestion has been accepted!"
    Denied: "Your suggestion has been denied!"
  Embed:
    Button:
      Discuss: "Discuss"
      

# ===========================================================================
# PROFILE COMMAND
# ===========================================================================
Profile:
  Embed:
    Title: ""
    Description:
      - "**🔍 Member Details**"
      - "> **📅 Joined:** {joinDate}"
      - "> **🏅 Highest Role:** {role}"
      - "> **🔖 Nickname:** {nickname}"
      - " "
      - "**👤 User Details**"
      - "> **🆔 User ID:** {userID}"
      - "> **💻 Username:** {user}"
      - "> **📆 Account Created:** {creationDate} ({creationDays})"
      - "> **🎖️ Badges:** {badges}"
      - " "
      - "**📊 User Statistics**"
      - "> **🔢 Level:** {level}"
      - "> **🌟 XP:** {xp}"
      - "> **💬 Total Messages:** {totalMessages}"
      - "> **🔥 Daily Streak:** {dailyStreak}"
      - " "
      - "**💰 Financial Details**"
      - "> **💵 Balance:** {balance}"
      - "> **🏦 Bank:** {bank}"
      - " "
      - "**🎒 Inventory**"
      - "> **📦 Items:** {inventoryItems}"
    Footer:
      Text: "Drako Bot | Profile Insights"
      Icon: "{guildIcon}"
    Author:
      Text: "{nickname}'s Profile"
      Icon: "{userIcon}"
    Color: "#1e8fff"
    Image: ""
    Thumbnail: "{userIcon}"

# ===========================================================================
# LOGS
# ===========================================================================
RoleAddLogs:
  Embed:
    Title: ":arrow_up_down: Role Change Detected"
    Description:
      - "**👤 Member:** {user}"
      - "**🆕 Added Role:** {addedRoleNames}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🛡️ Role Update Action | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#1E90FF"
    Image: ""
    Thumbnail: ""

RoleRemoveLogs:
  Embed:
    Title: ":arrow_down: Role Removal Detected"
    Description:
      - "**👤 Member:** {user}"
      - "**❌ Removed Role:** {removedRoleNames}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🛡️ Role Update Action | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#FF4500"
    Image: ""
    Thumbnail: ""

ChannelCreatedLogs:
  Embed:
    Title: ":white_check_mark: New Channel Created"
    Description:
      - "**👤 Executor:** {executor}"
      - "**📺 Channel Name:** {channelName}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Action | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#00FF00"
    Image: ""
    Thumbnail: ""

ChannelDeletedLogs:
  Embed:
    Title: ":no_entry: Channel Deleted"
    Description:
      - "**👤 Executor:** {executor}"
      - "**📺 Channel Name:** {channelName}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Action | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#FF0000"
    Image: ""
    Thumbnail: ""

UserUpdateLogs:
  Embed:
    Title: "👥 Nickname Changed"
    Description:
      - "**👤 User:** {userTag}"
      - "**🔵 Old Nickname:** `{oldNickname}`"
      - "**🟢 New Nickname:** `{newNickname}`"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Action | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#00FF00"
    Image: ""
    Thumbnail: ""

TimeoutLogs:
  Embed:
    Title: "🚫 Timeout Action | User: {userName}"
    Description: 
      - "**👤 User:** {user}"
      - "**🛠️ Moderator:** {moderator}"
      - "**⏰ Duration:** {duration}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Action | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#FFA500"
    Image: ""
    Thumbnail: ""

UntimeLogs:
  Embed:
    Title: "✅ Timeout Removed | User: {userName}"
    Description:
      - "**👤 User:** {user}"
      - "**🛠️ Moderator:** {moderator}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Action | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#4CAF50"
    Image: ""
    Thumbnail: ""

WarnLogs:
  Embed:
    Title: "🚫 Warn Action | User: {userName}"
    Description:
      - "**👤 User:** {user}"
      - "**🛠️ Moderator:** {moderator}"
      - "**📅 Reason:** {reason}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Action | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#FFA500"
    Image: ""
    Thumbnail: ""

WarnList:
  Embed:
    Title: "Warnings for {userName}"
    Color: "#FFA500"
    Footer: "Total Warnings: {totalWarnings}"
    Thumbnail: true
    EntryFormat:
      - "**{index}.** Reason: {reason}"
      - "Date: {longtime} at {shorttime}"
      - "Moderator: <@{moderatorId}>"
  NoWarnings: "{userName} has no warnings."
  Error: "An error occurred while retrieving the warnings."


GuildUpdateLogs:
  Embed:
    Title: ":pencil: Guild Name Changed"
    Description:
      - "**🔵 Old Name:** `{oldGuildName}`"
      - "**🟢 New Name:** `{newGuildName}`"
      - "**👤 Updated By:** {executor}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Log | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#00E676"
    Image: ""
    Thumbnail: true

MessageDeleteLogs:
  Embed:
    Title: ":wastebasket: Message Deleted"
    Description:
      - "**👤 User:** {user}"
      - "**💬 Message:** `{deletedmessage}`"
      - "**📺 Channel:** {channel}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Log | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#DD2C00"
    Image: ""
    Thumbnail: true
  Attachments: "**Attachments**"

MessageUpdateLogs:
  Embed:
    Title: ":pencil: Message Edited"
    Description:
      - "**👤 Editor:** {user}"
      - "**📺 Channel:** {channel}"
      - "🔵 **Original Message:** `{oldmessage}`"
      - "🟢 **Updated Message:** `{newmessage}`"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Log | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#FF9800"
    Image: ""
    Thumbnail: true

VoiceChannelStreamStart:
  Embed:
    Title: ":play_pause: Stream Started"
    Description:
      - "**👤 User:** {user}"
      - "**📺 Channel:** {channel}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Log | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#00C853"
    Image: ""
    Thumbnail: true

VoiceChannelStreamStop:
  Embed:
    Title: ":stop_button: Stream Ended"
    Description:
      - "**👤 User:** {user}"
      - "**📺 Channel:** {channel}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Log | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#D50000"
    Image: ""
    Thumbnail: true

VoiceChannelJoin:
  Embed:
    Title: ":loud_sound: Voice Channel Joined"
    Description:
      - "**👤 User:** {user}"
      - "**📺 Channel:** {channel}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Log | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#2196F3"
    Image: ""
    Thumbnail: true

VoiceChannelLeave:
  Embed:
    Title: ":mute: Voice Channel Left"
    Description:
      - "**👤 User:** {user}"
      - "**📺 Channel:** {channel}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Log | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#FF6D00"
    Image: ""
    Thumbnail: true

VoiceChannelSwitch:
  Embed:
    Title: ":left_right_arrow: Voice Channel Switch"
    Description:
      - "**👤 User:** {user}"
      - "🔵 **Old Channel:** {oldChannel}"
      - "🟢 **New Channel:** {newChannel}"
      - "**📅 Date:** {longtime}, {shorttime}"
    Footer:
      Text: "🔒 Moderation Log | DrakoBot"
      Icon: ""
    Author:
      Text: ""
      Icon: ""
    Color: "#3D5AFE"
    Image: ""
    Thumbnail: true

# ===========================================================================
# GAMES
# ===========================================================================
Economy:
  Messages:
    balance: "💼 **Account Balance**\n\n📂 **Wallet:** {balance} \n🏦 **Bank:** {bank}"
    otherBalance: "📊 **Account Details**\n\n👤 **User:** {user} \n📂 **Wallet:** {balance} \n🏦 **Bank:** {bank}"
    topBalances: "🏆 **Top 5 Users:**\n{topUsers}\n\n**Your Position:** #{position}"
    betAmountError: "❌ Invalid or insufficient bet amount. Try again with a valid amount."
    transfer: "🔄 **Transfer Details** \n\nYou transferred **{amount}** 🪙 to **{target}**. Generosity is your middle name!"
    cannotTransferToSelf: "What are you up to? You can't transfer money to yourself!"
    invalidTransferAmount: "You need to transfer more than 0 🪙"
    withdraw: "🏧 **Withdrawal Details** \n\n💰 You withdrew **{coins}** 🪙 from the bank. Spend wisely!"
    deposit: "💸 **Successful Deposit**\n\n{user}, you've deposited {balance} 🪙 into your bank account! Saving for a rainy day?"
    targetRob: "🥷 **Robbery**\n\n🚫 Your target does not have enough money to rob."
    cooldown: "⏰ **Command Cooldown**\n\n⏳ You can use this command again <t:{nextUse}:R>. Patience, my friend!"
    noBoosters: "🚫 You have no active boosters. Time to get some!"
    noMoney: "❌ You don't have enough money for this transaction. Check your balance and try again."
    invalidAmount: "❌ You entered an incorrect amount."
    noPermission: "🚫 You don't have permission to use this command. Nice try!"
    adminGive: "🛠️ **Admin Command: Give** \n\nSuccessfully gave **{amount}** 🪙 to **{user}**'s balance.\n\n📂 **Balance:** {balance}\n🏦 **Bank:** {bank}"
    adminTake: "🛠️ **Admin Command: Take** \n\nSuccessfully took **{amount}** 🪙 from **{user}**'s balance.\n\n📂 **Balance:** {balance}\n🏦 **Bank:** {bank}"
    adminSet: "🛠️ **Admin Command: Set** \n\nSuccessfully set **{user}**'s balance. Added **{amount}** 🪙.\n\n📂 **Balance:** {balance}\n🏦 **Bank:** {bank}"
    error: "An unexpected error occurred. Please try again later."
    footer: "Balance: {balance} 🪙"
    transactionLog: "{user}'s Transaction Log"
    gained: "Total Gained"
    lost: "Total Lost"
    guess: "Guess"
    result: "Result"
    win: "Win"
    lose: "Lose"
    rollResult: "Roll Result"
    outcome: "Result"
    bet: "Your Bet"
    inventoryCategory: "Select a category"

  Games:
    Blackjack:
      Title: "♠️ Blackjack - {result}!"
      Win:
        - "{user}, you beat the table and won **{balance}** 🪙! Cha-ching!"
        - "{user}, you won and earned **{balance}** 🪙! Feeling lucky?"
        - "{user}, Lady Luck is on your side! You bagged **{balance}** 🪙!"
        - "Congratulations, {user}! You walked away with **{balance}** 🪙!"
      Lose:
        - "{user}, it looks like you lost. Say goodbye to **{balance}** 🪙!"
        - "{user}, this is awkward... you lost **{balance}** 🪙. Better luck next time!"
        - "{user}, the house wins! You lost **{balance}** 🪙. Ouch!"
        - "Sorry, {user}. You lost **{balance}** 🪙. Try again?"
      Draw:
        - "Sooo boring... Looks like you have drawn."
        - "{user}, this game is a draw. You have been refunded **{balance}** 🪙."
        - "It's a tie, {user}. You got your **{balance}** 🪙 back."
        - "Draw! No one wins this time. {user}, you get your **{balance}** 🪙 back."

    Slots:
      Title: "🎰 Slots - {result}!"
      Win:
        - "{user}, you won **{balance}** 🪙. Jackpot!"
        - "Is this rigged? You won **{balance}** 🪙. Lucky you!"
        - "{user}, you're on fire! You won **{balance}** 🪙."
        - "Ding ding ding! {user}, you hit the jackpot and won **{balance}** 🪙!"
      Lose:
        - "You lost **{balance}** 🪙. Better luck next time!"
        - "{user}, this is awkward... you lost **{balance}** 🪙. Spin again?"
        - "{user}, the slots weren't in your favor. You lost **{balance}** 🪙."
        - "Better luck next time, {user}. You lost **{balance}** 🪙."

    Roulette:
      Title: "🎡 Roulette - {result}!"
      Win:
        - "{user}, you won **{balance}** 🪙. Bravo!"
        - "Is this rigged? You won **{balance}** 🪙. Incredible!"
        - "{user}, you hit the lucky number! You won **{balance}** 🪙."
        - "Congratulations, {user}! You won **{balance}** 🪙."
      Lose:
        - "You lost **{balance}** 🪙. Better luck next time!"
        - "{user}, this is awkward... you lost **{balance}** 🪙."
        - "{user}, the wheel didn't favor you. You lost **{balance}** 🪙."
        - "No win this time, {user}. You lost **{balance}** 🪙."

    Coinflip:
      Title: "🪙 Coinflip - {result}!"
      Win:
        - "{user}, you won **{balance}** 🪙! Heads or tails, you nailed it!"
        - "Is this rigged? You won **{balance}** 🪙. Impressive!"
        - "{user}, you called it right! You won **{balance}** 🪙!"
        - "Congrats, {user}! You won **{balance}** 🪙 on the coinflip!"
      Lose:
        - "You lost **{balance}** 🪙. Better luck next time!"
        - "{user}, this is awkward... you lost **{balance}** 🪙."
        - "Too bad, {user}. You lost **{balance}** 🪙. Try again?"
        - "Unlucky, {user}. You lost **{balance}** 🪙."

      heads: "Heads"
      tails: "Tails"

    Roll:
      Title: "🎲 Roll - {result}!"
      Win:
        - "{user}, you won **{balance}** 🪙. Well done!"
        - "Is this rigged? You won **{balance}** 🪙. Awesome!"
        - "{user}, you rolled a winner! You won **{balance}** 🪙."
        - "Lucky roll, {user}! You won **{balance}** 🪙."
      Lose:
        - "You lost **{balance}** 🪙. Better luck next time!"
        - "{user}, this is awkward... you lost **{balance}** 🪙."
        - "{user}, the roll didn't favor you. You lost **{balance}** 🪙."
        - "No win this time, {user}. You lost **{balance}** 🪙."

    Crime:
      Title: "🚨 Crime - {result}!"
      Win:
        - "{user}, you committed the crime and gained **{balance}** 🪙! Smooth operator!"
        - "You robbed the bank and got **{balance}** 🪙. Nice haul!"
        - "Success! {user}, you got away with **{balance}** 🪙!"
        - "{user}, you're a criminal mastermind! You stole **{balance}** 🪙!"
      Lose:
        - "Looks like you aren't cut out for this life... Mission failed!"
        - "{user} got locked up during a crime spree and lost **{balance}** 🪙. Better luck next time!"
        - "Busted! {user}, you failed and lost **{balance}** 🪙."
        - "{user}, crime doesn't pay... this time. You lost **{balance}** 🪙."

    Rob:
      Title: "🥷 Robbery - {result}!"
      Win:
        - "{user}, you successfully robbed {victim} for **{balance}** 🪙. Criminal genius!"
        - "{user}, you held {victim} at gunpoint for **{balance}** 🪙. What a heist!"
        - "Robbery success! {user}, you took **{balance}** 🪙 from {victim}."
        - "{user}, you pulled off the perfect heist and got **{balance}** 🪙!"
      Lose:
        - "{user}, you failed to rob {victim}. Better luck next time!"
        - "{user}, this is awkward... {victim} managed to escape."
        - "Robbery failed! {user}, {victim} got away."
        - "{user}, the heist went wrong. You couldn't rob {victim}."

    ScratchOff:
      Title: "Scratch-Off Result"
      Win: 
        - "Congratulations! You won {amount} coins!"
        - "You're on a winning streak! You just scored {amount} coins!"
        - "Lucky you! {amount} coins are now yours!"
      Lose:
        - "Better luck next time! You lost {amount} coins."
        - "Oh no! You didn't win this time."
        - "Sorry, you lost {amount} coins. Try again!"

  Actions:
    Beg:
      Title: "🙇‍♂️ Begging!"
      Messages:
        - "You begged and received **{balance}** 🪙. Every little bit helps!"
        - "{user}, begging is the life! You made **{balance}** 🪙."
        - "{user}, your begging paid off! You received **{balance}** 🪙."
        - "Generous souls gave you **{balance}** 🪙, {user}. Keep it up!"

    Daily:
      Title: "📅 Daily Reward - Claimed!"
      Messages:
        - "{user}, you redeemed your daily reward and earned **{balance}** 🪙.\n\n**Streak:** {streak} day(s)"
        - "Woooo, you gained **{balance}** 🪙 from your daily reward!\n\n**Streak:**  {streak} day(s)"
        - "Daily success! {user}, you collected **{balance}** 🪙. Keep up the streak!\n\n**Streak:**  {streak} day(s)"
        - "Consistent! {user}, you earned **{balance}** 🪙 from your daily reward.\n\n**Streak:**  {streak} day(s)"

    Work:
      Title: "💼 Work - Job Completed!"
      Messages:
        - "{user}, you gained **{balance}** 🪙 from a hard day's work! Good job!"
        - "Well done on today's work, {user}! Here is **{balance}** 🪙."
        - "Hard work pays off! {user}, you earned **{balance}** 🪙."
        - "{user}, you completed your work and received **{balance}** 🪙."

  Other:
    Boosters:
      title: "🚀 Your Active Boosters"
      description: "**{type} Booster** (Multiplier: **{multiplier}**, Expires: <t:{endTime}:R>)"

    Inventory:
      empty: "🚫 Your inventory is empty. Time to shop!"
      noItems: "🚫 No items to display."
      redeem: "🎁 You have redeemed: **{item}**"
      Embed:
        Title: "{category} Inventory"
        Description: ["{itemNum}. **{item}** (x{amount})"]
        Footer:
          Text: "Page {pageCurrent} of {pageMax}"
        Color: "#1769FF"

    Store:
      purchaseSuccess: "✅ You have successfully purchased **{item}**. Your new balance is **{balance}** 🪙."
      higherInterestRate: "You already have a higher or equal interest rate."
      purchaseLimit: "You cannot purchase more than {limit} of {item}."
      Embed:
        Title: "{shopName} Store"
        Description:
          - "{itemCount}: **{item}** ({price} 🪙)"
          - "{description}"
        Footer:
          Text: "Page {pageCurrent} of {pageMax}"
          Icon: ""
        Author:
          Text: ""
          Icon: ""
        Color: "#1769FF"
        Image: ""
        Thumbnail: ""
      Categories:
        - "Ranks"
        - "Boosters"
        - "Items"