# ________________________________________________
#
#        CHAMPION SETUPS BOT CONFIGURATION FILE
#
# Here you can customise main Bot Configurations
# For Server Templates visit configs/templates/
#
# ________________________________________________

version: "1.0.6"

# ____________________________
#
# GENERAL OPTIONS
# ____________________________

general:
  # Bot Name
  name: "Champion Setups"
  # Bot's Prefix
  prefix: "*"
  # Bot Token obtained from Discord Developer Portal
  token: ""
  # ID of User who is gonna have access to EVAL Command
  # Example:
  #
  # eval:
  #   - "1234"
  #
  # If you don't want any, just change to eval: []
  eval: []
  # Whether Slash Commands are enabled
  slash: true

# ____________________________
#
# SETUP CONFIGURATION
# ____________________________ 

setup:
  # Placeholder for Create Select Menu
  create_placeholder: "Choose option to Create from Drop down"
  # Placeholder for Create Select Menu
  delete_placeholder: "Choose option to Delete from Drop down"
  # Format for Templates List
  # Valid Placeholders <name>, <author>, <channels>, <roles>, <emojis> (Number of them)
  list_format: "> `<name>`\n"

save:
  # Whether to Save Server Icon
  icon: true
  # Whether to Save Server Banner
  banner: true
  # Whether to Save Server Verification Level
  verifyLevel: true

# ____________________________
#
# ROLES CONFIGURATION
# ____________________________

roles:
  # Roles which can bypass specific stuff
  # Format is same as for above roles
  bypass:
    # Roles which can bypass Command Cooldown
    cooldown: []
    # Roles which can bypass Command Permissions
    permission: [] 

# ____________________________
#
# CUSTOM STATUS CONFIGURATION
# ____________________________

status:
  # Can be Playing, Listening, Watching and Competing
  type: "Watching"
  # Random Messages in Custom Status, changing every x minutes/hours 
  change_random: true
  # Interval to Change Custom Status (in seconds)
  interval: 180 # 3 minutes, don't go below that
  # Message which is displayed in Custom Status if change_random = false
  message: "Champion Setups Bot"
  # List of Custom Status's if change_random = true
  # Valid Placeholders: <members>, <channels>, <version>
  messages:
    - "<members> total members"
    - "<version> | *help"

# ____________________________
#
# EMOJIS CONFIGURATION
# ____________________________
  
emojis:
  # Create All Channels/Roles/Emojis Emoji
  create_all: "💻"
  # Create Channels Emoji
  create_channels: "🏠"
  # Create Roles Emoji
  create_roles: "🎭"
  # Create Emojis Emoji
  create_emojis: "😃"
  # Delete All Channels/Roles/Emojis Emoji
  delete_all: "💻"
  # Delete Channels Emoji
  delete_channels: "🏠"
  # Delete Roles Emoji
  delete_roles: "🎭"
  # Delete Emojis Emoji
  delete_emojis: "😃"