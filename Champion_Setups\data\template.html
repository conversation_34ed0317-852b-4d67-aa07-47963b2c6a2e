<style>
  @font-face {
    font-family: Whitney;
    src: url('https://discordapp.com/assets/6c6374bad0b0b6d204d8d6dc4a18d820.woff');
    font-weight: 300;
  }
  
  @font-face {
    font-family: Whitney;
    src: url('https://discordapp.com/assets/e8acd7d9bf6207f99350ca9f9e23b168.woff');
    font-weight: 400;
  }
  @font-face {
    font-family: Whitney;
    src: url('https://discordapp.com/assets/3bdef1251a424500c1b3a78dea9b7e57.woff');
    font-weight: 500;
  }
  
  @font-face {
    font-family: Whitney;
    src: url('https://discordapp.com/assets/be0060dafb7a0e31d2a1ca17c0708636.woff');
    font-weight: 600;
  }
  
  @font-face {
    font-family: Whitney;
    src: url('https://discordapp.com/assets/8e12fb4f14d9c4592eb8ec9f22337b04.woff');
    font-weight: 700;
  }
  body {
    background-color: #36393e;
    color: #dcddde;
    font-family: "<PERSON>", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 16px;
    margin: 0;
    padding: 0;
  }
  .message-container {
    display: flex;
    flex-direction: column;
    padding-left: 15px;
  }
  .parent-container {
    padding: 25px;
    display: flex;
  }
  .avatar {
    border-radius: 50%;
    height: 50px;
    width: 50px;
  }
  .guild-image {
    border-radius: 35%;
    padding: 15px;
  }
  .embed {
    display: flex;
    padding: 15px;
    background-color: #333;
    border-radius: 0 7px 7px 0;
    position: relative;
    margin-left: 5px;
    max-width: 500px;
  }
  .embed-color {
    width: 5px;
    position: absolute;
    top: 0;
    left: -5px;
    bottom: 0;
    border-radius: 7px 0 0 7px
  }
  .embed-content {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    width: 100%;
  }
  .embed-title {
    font-size: 1.3em;
    font-weight: bold;
  }
  .embed-description {
    margin-top: 15px;
  }
  
  .chat-image {
    display: block;
    position: relative;
    border-radius: 3px;
    margin-top: 0.2em;
    overflow: hidden;
  }
  
  .chat-media {
    max-width: 45vw;
    max-height: 500px;
    border-radius: 3px;
    display: block;
  }
  </style>