const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle default auto archive duration changes for forum/text channels
            if (oldChannel.defaultAutoArchiveDuration === newChannel.defaultAutoArchiveDuration) return;
            
            console.log(`📁 Channel default auto archive duration updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_DEFAULT_AUTO_ARCHIVE_DURATION_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_DEFAULT_AUTO_ARCHIVE_DURATION_UPDATE',
                user: 'System',
                userId: null,
                action: 'Thời gian archive mặc định của kênh được cập nhật',
                details: `Thời gian archive mặc định của kênh **${newChannel.name}** đã được thay đổi từ ${oldChannel.defaultAutoArchiveDuration} phút thành ${newChannel.defaultAutoArchiveDuration} phút`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the default auto archive duration from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Thời gian archive mặc định được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for channel default auto archive duration update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📁 Thời gian archive mặc định cập nhật',
                `Vừa có thời gian archive mặc định của kênh được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Format duration display
            const formatDuration = (minutes) => {
                if (!minutes) return 'Không thiết lập';
                if (minutes < 60) return `${minutes} phút`;
                if (minutes < 1440) return `${Math.floor(minutes / 60)} giờ`;
                if (minutes < 10080) return `${Math.floor(minutes / 1440)} ngày`;
                return `${Math.floor(minutes / 10080)} tuần`;
            };
            
            // Get channel type names
            const channelTypes = {
                0: 'Kênh văn bản',
                2: 'Kênh thoại',
                4: 'Danh mục',
                5: 'Kênh tin tức',
                10: 'Diễn đàn tin tức',
                11: 'Diễn đàn công khai',
                12: 'Diễn đàn riêng tư',
                13: 'Kênh sân khấu',
                15: 'Forum',
                16: 'Media'
            };
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Loại kênh',
                    value: `- ${channelTypes[newChannel.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Thời gian archive cũ',
                    value: `- ${formatDuration(oldChannel.defaultAutoArchiveDuration)}`,
                    inline: true
                },
                {
                    name: '> Thời gian archive mới',
                    value: `- ${formatDuration(newChannel.defaultAutoArchiveDuration)}`,
                    inline: true
                }
            ]);
            
            // Add parent category info if available
            if (newChannel.parent) {
                embed.addFields({
                    name: '> Danh mục',
                    value: `- ${newChannel.parent.name}`,
                    inline: true
                });
            }
            
            // Add impact explanation
            if (newChannel.defaultAutoArchiveDuration > oldChannel.defaultAutoArchiveDuration) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Threads mới sẽ tồn tại lâu hơn khi không hoạt động',
                        '• Giảm nguy cơ threads bị archive sớm',
                        '• Phù hợp cho thảo luận dài hạn',
                        '• Tăng thời gian để thành viên phản hồi'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for longer duration
            } else if (newChannel.defaultAutoArchiveDuration < oldChannel.defaultAutoArchiveDuration) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Threads mới sẽ bị archive nhanh hơn',
                        '• Giúp giữ kênh gọn gàng',
                        '• Phù hợp cho thảo luận ngắn hạn',
                        '• Cần hoạt động thường xuyên để duy trì threads'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0xe67e22); // Orange-red for shorter duration
            }
            
            // Add duration options explanation
            const durationOptions = {
                60: '1 giờ - Cho thảo luận nhanh',
                1440: '1 ngày - Cho thảo luận thông thường',
                4320: '3 ngày - Cho thảo luận quan trọng',
                10080: '1 tuần - Cho dự án dài hạn'
            };
            
            embed.addFields({
                name: '> 💡 Các tùy chọn thời gian archive',
                value: Object.entries(durationOptions).map(([minutes, desc]) => `• ${desc}`).join('\n'),
                inline: false
            });
            
            // Add note about existing threads
            embed.addFields({
                name: '> 📝 Lưu ý quan trọng',
                value: [
                    '• Chỉ áp dụng cho threads mới được tạo',
                    '• Threads hiện tại giữ nguyên thời gian archive',
                    '• Có thể thay đổi riêng cho từng thread',
                    '• Áp dụng cho cả public và private threads'
                ].join('\n'),
                inline: false
            });
            
            // Add channel specific info
            if (newChannel.type === 15) { // Forum channel
                embed.addFields({
                    name: '> Đặc biệt cho Forum',
                    value: `- Tất cả posts mới trong forum sẽ sử dụng thời gian archive ${formatDuration(newChannel.defaultAutoArchiveDuration)}`,
                    inline: false
                });
            } else if (newChannel.type === 0) { // Text channel
                embed.addFields({
                    name: '> Đặc biệt cho Text Channel',
                    value: `- Tất cả threads mới trong kênh sẽ sử dụng thời gian archive ${formatDuration(newChannel.defaultAutoArchiveDuration)}`,
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Default Auto Archive Duration Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel default auto archive duration update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelDefaultAutoArchiveDurationUpdate audit log:', error);
        }
    }
};
