const { SlashCommandBuilder } = require('discord.js');
const User = require('../models/User');
const { createSuccessEmbed, createErrorEmbed, formatCurrency } = require('../utils/embedBuilder');
const config = require('../config/config');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('hunt')
        .setNameLocalizations({
            'vi': 'sanban'
        })
        .setDescription('Đi săn bắn động vật để kiếm tiền và có thể gặp động vật quý hiếm'),
    
    cooldown: 2400, // 40 phút

    async execute(interaction) {
        try {
            const userData = await User.findOrCreate(interaction.user);
            
            // Kiểm tra level tối thiểu
            if (userData.level < 3) {
                const errorEmbed = createErrorEmbed(
                    'Level không đủ!',
                    `Bạn cần đạt **Level 3** để có thể đi săn bắn.\n\n` +
                    `${config.emojis.star} **Level hiện tại:** ${userData.level}\n` +
                    `💡 **Mẹo:** Sử dụng \`/work\` và \`/daily\` để tăng level!`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Kiểm tra cooldown
            if (userData.lastHunt) {
                const lastHunt = new Date(userData.lastHunt);
                const now = new Date();
                const timeDiff = now - lastHunt;
                const cooldownTime = 2400000; // 40 phút
                
                if (timeDiff < cooldownTime) {
                    const timeLeft = Math.ceil((cooldownTime - timeDiff) / 1000 / 60);
                    const errorEmbed = createErrorEmbed(
                        'Đang nghỉ ngơi!',
                        `Bạn cần đợi **${timeLeft} phút** nữa trước khi có thể đi săn lại.\n\n` +
                        `${config.emojis.info} **Mẹo:** Thời gian nghỉ để chuẩn bị đạn và súng!`
                    );
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
            }

            // Các loại động vật với tỷ lệ và giá trị khác nhau
            const animals = [
                // Động vật thường (60%)
                { name: 'Thỏ rừng', emoji: '🐰', rarity: 'common', chance: 20, value: { min: 100, max: 300 }, exp: 15 },
                { name: 'Gà rừng', emoji: '🐓', rarity: 'common', chance: 15, value: { min: 150, max: 350 }, exp: 20 },
                { name: 'Vịt trời', emoji: '🦆', rarity: 'common', chance: 15, value: { min: 200, max: 400 }, exp: 25 },
                { name: 'Heo rừng', emoji: '🐗', rarity: 'common', chance: 10, value: { min: 300, max: 600 }, exp: 35 },
                
                // Động vật hiếm (30%)
                { name: 'Nai rừng', emoji: '🦌', rarity: 'uncommon', chance: 12, value: { min: 500, max: 1000 }, exp: 50 },
                { name: 'Gấu đen', emoji: '🐻', rarity: 'uncommon', chance: 8, value: { min: 800, max: 1500 }, exp: 70 },
                { name: 'Sói rừng', emoji: '🐺', rarity: 'rare', chance: 6, value: { min: 1000, max: 2000 }, exp: 90 },
                { name: 'Báo đen', emoji: '🐆', rarity: 'rare', chance: 4, value: { min: 1500, max: 3000 }, exp: 120 },
                
                // Động vật siêu hiếm (8%)
                { name: 'Hổ vàng', emoji: '🐅', rarity: 'epic', chance: 3, value: { min: 3000, max: 6000 }, exp: 180 },
                { name: 'Sư tử', emoji: '🦁', rarity: 'epic', chance: 2, value: { min: 4000, max: 8000 }, exp: 220 },
                { name: 'Rồng rừng', emoji: '🐉', rarity: 'legendary', chance: 1, value: { min: 8000, max: 15000 }, exp: 350 },
                
                // Sự kiện đặc biệt (2%)
                { name: 'Kẻ săn trộm', emoji: '🥷', rarity: 'danger', chance: 1, value: { min: -1000, max: -500 }, exp: 0 },
                { name: 'Kho báu cướp biển', emoji: '💰', rarity: 'treasure', chance: 1, value: { min: 5000, max: 12000 }, exp: 300 }
            ];

            // Random để xác định kết quả săn bắn
            const random = Math.random() * 100;
            let cumulativeChance = 0;
            let huntResult = null;

            for (const animal of animals) {
                cumulativeChance += animal.chance;
                if (random <= cumulativeChance) {
                    huntResult = animal;
                    break;
                }
            }

            // Nếu không săn được gì (xác suất thất bại)
            if (!huntResult) {
                const db = require('../database/database');
                await db.run(
                    'UPDATE users SET last_hunt = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [new Date().toISOString(), userData.id]
                );

                const failEmbed = createErrorEmbed(
                    'Không tìm thấy con mồi!',
                    `${config.emojis.warning} Lần này bạn không săn được gì cả.\n\n` +
                    `🎯 **Mẹo:** Động vật có thể đã chạy trốn. Thử lại sau 40 phút!`
                );

                return await interaction.reply({ embeds: [failEmbed] });
            }

            // Tính giá trị thu được
            const baseValue = Math.floor(
                Math.random() * (huntResult.value.max - huntResult.value.min + 1) + 
                huntResult.value.min
            );

            // Bonus dựa trên level (chỉ áp dụng cho giá trị dương)
            let totalValue = baseValue;
            let levelBonus = 0;
            
            if (baseValue > 0) {
                levelBonus = Math.floor(baseValue * (userData.level * 0.08));
                totalValue = baseValue + levelBonus;
            }

            // Cập nhật thời gian hunting
            const db = require('../database/database');
            await db.run(
                'UPDATE users SET last_hunt = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [new Date().toISOString(), userData.id]
            );

            // Xử lý kết quả đặc biệt
            if (huntResult.rarity === 'danger') {
                // Bị kẻ săn trộm tấn công
                const actualLoss = Math.min(Math.abs(totalValue), userData.balance);
                if (actualLoss > 0) {
                    await userData.updateBalance(-actualLoss, 'hunt_danger');
                }

                const dangerEmbed = createErrorEmbed(
                    'Gặp nguy hiểm!',
                    `${huntResult.emoji} **${huntResult.name}**\n\n` +
                    `💥 Bạn đã bị tấn công và mất **${formatCurrency(actualLoss)}**!\n\n` +
                    `${config.emojis.money} **Số dư còn lại:** ${formatCurrency(userData.balance - actualLoss)}\n\n` +
                    `⚠️ **Lời khuyên:** Hãy cẩn thận hơn khi đi săn!`
                );

                return await interaction.reply({ embeds: [dangerEmbed] });
            }

            // Cập nhật số dư và kinh nghiệm cho kết quả bình thường
            await userData.updateBalance(totalValue, 'hunting');
            const levelResult = await userData.addExperience(huntResult.exp);

            // Tạo embed kết quả
            let description = `${huntResult.emoji} **${huntResult.name}** (${getRarityText(huntResult.rarity)})\n\n` +
                `💰 **Giá trị:** ${formatCurrency(totalValue)}\n` +
                `${config.emojis.star} **Kinh nghiệm:** +${huntResult.exp} EXP\n`;

            if (levelBonus > 0) {
                description += `🎯 **Level bonus:** ${formatCurrency(levelBonus)}\n`;
            }

            description += `\n${config.emojis.money} **Số dư hiện tại:** ${formatCurrency(userData.balance + totalValue)}`;

            if (levelResult.leveledUp) {
                description += `\n\n🎉 **LEVEL UP!** Bạn đã lên level ${levelResult.newLevel}!\n` +
                              `💰 **Thưởng level:** ${formatCurrency(levelResult.reward)}`;
            }

            const successEmbed = createSuccessEmbed(
                'Săn bắn thành công!',
                description
            );

            // Thêm thông tin đặc biệt
            if (huntResult.rarity === 'legendary') {
                successEmbed.addFields({
                    name: `${config.emojis.crown} Sinh vật huyền thoại!`,
                    value: 'Bạn đã săn được một sinh vật cực kỳ hiếm! Đây là thành tích đáng tự hào!',
                    inline: false
                });
            } else if (huntResult.rarity === 'epic') {
                successEmbed.addFields({
                    name: `${config.emojis.gem} Động vật quý hiếm!`,
                    value: 'Đây là một con vật rất có giá trị và khó tìm!',
                    inline: false
                });
            } else if (huntResult.rarity === 'treasure') {
                successEmbed.addFields({
                    name: `${config.emojis.star} Phát hiện kho báu!`,
                    value: 'Bạn đã tìm thấy kho báu của những tên cướp biển!',
                    inline: false
                });
            }

            // Thêm thống kê
            successEmbed.addFields({
                name: `${config.emojis.info} Thông tin săn bắn`,
                value: `**Độ hiếm:** ${getRarityText(huntResult.rarity)}\n` +
                       `**Tỷ lệ gặp:** ${huntResult.chance}%\n` +
                       `**Yêu cầu level:** 3+\n` +
                       `**Có thể săn lại:** <t:${Math.floor((Date.now() + 2400000) / 1000)}:R>`,
                inline: false
            });

            await interaction.reply({ embeds: [successEmbed] });

            // Kiểm tra achievements
            const newAchievements = await userData.checkAchievements();
            if (newAchievements.length > 0) {
                // Có thể thêm thông báo achievement sau
            }
            
        } catch (error) {
            console.error('Lỗi lệnh hunt:', error);
            
            const errorEmbed = createErrorEmbed(
                'Lỗi hệ thống',
                'Không thể đi săn bắn. Có thể súng bị kẹt đạn!'
            );
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};

function getRarityText(rarity) {
    const rarityMap = {
        'common': '🟢 Thường',
        'uncommon': '🔵 Không thường',
        'rare': '🟣 Hiếm',
        'epic': '🟠 Sử thi',
        'legendary': '🟡 Huyền thoại',
        'danger': '🔴 Nguy hiểm',
        'treasure': '🟨 Kho báu'
    };
    return rarityMap[rarity] || rarity;
}
