const { SlashCommandBuilder, PermissionFlagsBits, ChannelType } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('unlock')
        .setDescription('Mở khóa kênh hoặc toàn server')
        .addStringOption(option =>
            option.setName('scope')
                .setDescription('Phạm vi unlock')
                .setRequired(true)
                .addChoices(
                    { name: '📍 Channel hiện tại', value: 'current' },
                    { name: '📁 Category hiện tại', value: 'category' },
                    { name: '🏠 Toàn server', value: 'server' },
                    { name: '📝 Chỉ text channels', value: 'text' },
                    { name: '🔊 Chỉ voice channels', value: 'voice' },
                    { name: '🔄 Lockdown gần nhất', value: 'latest' }
                )
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Lý do unlock')
                .setRequired(false)
        )
        .addBooleanOption(option =>
            option.setName('announce')
                .setDescription('Thông báo unlock cho thành viên')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                'Bạn cần quyền `Manage Channels` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageChannels)) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                'Bot cần quyền `Manage Channels` để thực hiện lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const scope = interaction.options.getString('scope');
        const reason = interaction.options.getString('reason') || 'Mở khóa';
        const announce = interaction.options.getBoolean('announce') || false;
        const guildId = interaction.guild.id;
        
        await interaction.deferReply();
        
        try {
            let channelsToUnlock = [];
            let unlockType = '';
            
            // Xác định channels cần unlock
            if (scope === 'latest') {
                // Lấy lockdown gần nhất từ database
                const latestLockdown = await client.db.getLatestLockdown(guildId);
                if (!latestLockdown) {
                    const errorEmbed = createErrorEmbed(
                        'Không có lockdown nào!',
                        'Không tìm thấy lockdown gần đây nào để mở khóa.'
                    );
                    return await interaction.editReply({ embeds: [errorEmbed] });
                }
                
                const lockdownData = JSON.parse(latestLockdown.data);
                channelsToUnlock = lockdownData.channels
                    .map(id => interaction.guild.channels.cache.get(id))
                    .filter(ch => ch); // Lọc bỏ channels đã bị xóa
                unlockType = `Lockdown gần nhất (${lockdownData.scope})`;
            } else {
                // Xác định channels theo scope
                switch (scope) {
                    case 'current':
                        channelsToUnlock = [interaction.channel];
                        unlockType = 'Kênh hiện tại';
                        break;
                        
                    case 'category':
                        if (interaction.channel.parent) {
                            channelsToUnlock = interaction.channel.parent.children.cache
                                .filter(ch => ch.type === ChannelType.GuildText || ch.type === ChannelType.GuildVoice)
                                .map(ch => ch);
                            unlockType = `Category: ${interaction.channel.parent.name}`;
                        } else {
                            channelsToUnlock = [interaction.channel];
                            unlockType = 'Kênh hiện tại (không có category)';
                        }
                        break;
                        
                    case 'server':
                        channelsToUnlock = interaction.guild.channels.cache
                            .filter(ch => ch.type === ChannelType.GuildText || ch.type === ChannelType.GuildVoice)
                            .map(ch => ch);
                        unlockType = 'Toàn server';
                        break;
                        
                    case 'text':
                        channelsToUnlock = interaction.guild.channels.cache
                            .filter(ch => ch.type === ChannelType.GuildText)
                            .map(ch => ch);
                        unlockType = 'Tất cả text channels';
                        break;
                        
                    case 'voice':
                        channelsToUnlock = interaction.guild.channels.cache
                            .filter(ch => ch.type === ChannelType.GuildVoice)
                            .map(ch => ch);
                        unlockType = 'Tất cả voice channels';
                        break;
                }
            }
            
            if (channelsToUnlock.length === 0) {
                const errorEmbed = createErrorEmbed(
                    'Không có kênh nào để unlock!',
                    'Không tìm thấy kênh nào phù hợp với phạm vi đã chọn.'
                );
                return await interaction.editReply({ embeds: [errorEmbed] });
            }
            
            // Thực hiện unlock
            let unlockedCount = 0;
            let failedChannels = [];
            
            for (const channel of channelsToUnlock) {
                try {
                    const everyoneRole = interaction.guild.roles.everyone;
                    
                    if (channel.type === ChannelType.GuildText) {
                        // Unlock text channel - reset về null để sử dụng default permissions
                        await channel.permissionOverwrites.edit(everyoneRole, {
                            SendMessages: null,
                            AddReactions: null,
                            SendMessagesInThreads: null,
                            CreatePublicThreads: null,
                            CreatePrivateThreads: null
                        }, { reason: `Unlock: ${reason} | Bởi: ${interaction.user.tag}` });
                    } else if (channel.type === ChannelType.GuildVoice) {
                        // Unlock voice channel
                        await channel.permissionOverwrites.edit(everyoneRole, {
                            Connect: null,
                            Speak: null
                        }, { reason: `Unlock: ${reason} | Bởi: ${interaction.user.tag}` });
                    }
                    
                    unlockedCount++;
                } catch (error) {
                    console.error(`Lỗi khi unlock channel ${channel.name}:`, error);
                    failedChannels.push(channel.name);
                }
            }
            
            // Xóa lockdown khỏi database nếu unlock latest
            if (scope === 'latest') {
                await client.db.removeLockdown(guildId);
            }
            
            // Log vào moderation logs
            await client.db.addModerationLog(guildId, {
                type: 'unlock',
                userId: null,
                moderatorId: interaction.user.id,
                reason: reason,
                duration: null
            });
            
            // Thông báo unlock nếu được yêu cầu
            if (announce && scope !== 'current') {
                const announceEmbed = createInfoEmbed(
                    '🔓 LOCKDOWN LIFTED',
                    `**Phạm vi:** ${unlockType}\n` +
                    `**Lý do:** ${reason}\n` +
                    `**Bởi:** ${interaction.user.tag}\n` +
                    `**Thời điểm:** <t:${Math.floor(Date.now() / 1000)}:F>\n\n` +
                    `✅ Các kênh đã được mở khóa. Hoạt động bình thường đã được khôi phục.`
                );
                
                try {
                    await interaction.channel.send({ embeds: [announceEmbed] });
                } catch (error) {
                    console.log('Không thể gửi thông báo unlock');
                }
            }
            
            const successEmbed = createSuccessEmbed(
                '🔓 Unlock đã hoàn thành!',
                `**Phạm vi:** ${unlockType}\n` +
                `**Kênh đã mở khóa:** ${unlockedCount}/${channelsToUnlock.length}\n` +
                `${failedChannels.length > 0 ? `**Kênh lỗi:** ${failedChannels.join(', ')}\n` : ''}` +
                `**Lý do:** ${reason}\n` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            if (failedChannels.length > 0) {
                successEmbed.addFields({
                    name: '⚠️ Cảnh báo',
                    value: `Không thể mở khóa ${failedChannels.length} kênh. Kiểm tra quyền bot trong các kênh này.`,
                    inline: false
                });
            }
            
            await interaction.editReply({ embeds: [successEmbed] });
            
            // Gửi log vào channel
            await sendModerationLog(client, guildId, {
                type: 'unlock',
                user: { tag: 'System', id: 'system' },
                moderator: interaction.user,
                reason: reason,
                scope: unlockType,
                count: unlockedCount
            });
            
        } catch (error) {
            console.error('Lỗi khi unlock:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi unlock!',
                'Đã xảy ra lỗi khi thực hiện unlock. Vui lòng thử lại sau!'
            );
            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },
};
