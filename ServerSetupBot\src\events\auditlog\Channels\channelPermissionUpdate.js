const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle permission overwrites changes
            if (oldChannel.permissionOverwrites?.cache?.size === newChannel.permissionOverwrites?.cache?.size &&
                oldChannel.permissionOverwrites?.cache?.every(overwrite => {
                    const newOverwrite = newChannel.permissionOverwrites?.cache?.get(overwrite.id);
                    return newOverwrite && 
                           newOverwrite.allow.bitfield === overwrite.allow.bitfield &&
                           newOverwrite.deny.bitfield === overwrite.deny.bitfield;
                })) {
                return;
            }
            
            console.log(`🔐 Channel permissions updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_PERMISSION_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_PERMISSION_UPDATE',
                user: 'System',
                userId: null,
                action: 'Quyền kênh được cập nhật',
                details: `Quyền của kênh **${newChannel.name}** đã được cập nhật`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated permissions from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 13, // CHANNEL_OVERWRITE_CREATE/UPDATE/DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.extra?.channel?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Quyền kênh được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for permission update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔐 Quyền kênh cập nhật',
                `Vừa có quyền kênh được cập nhật trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Analyze permission changes
            const oldOverwrites = oldChannel.permissionOverwrites?.cache || new Map();
            const newOverwrites = newChannel.permissionOverwrites?.cache || new Map();
            
            const changes = [];
            
            // Check for new overwrites
            newOverwrites.forEach((overwrite, id) => {
                if (!oldOverwrites.has(id)) {
                    const target = overwrite.type === 0 ? `<@&${id}>` : `<@${id}>`;
                    changes.push(`➕ Thêm quyền cho ${target}`);
                }
            });
            
            // Check for removed overwrites
            oldOverwrites.forEach((overwrite, id) => {
                if (!newOverwrites.has(id)) {
                    const target = overwrite.type === 0 ? `Role` : `User`;
                    changes.push(`➖ Xóa quyền cho ${target}`);
                }
            });
            
            // Check for modified overwrites
            newOverwrites.forEach((newOverwrite, id) => {
                const oldOverwrite = oldOverwrites.get(id);
                if (oldOverwrite && 
                    (oldOverwrite.allow.bitfield !== newOverwrite.allow.bitfield ||
                     oldOverwrite.deny.bitfield !== newOverwrite.deny.bitfield)) {
                    const target = newOverwrite.type === 0 ? `<@&${id}>` : `<@${id}>`;
                    changes.push(`🔄 Sửa quyền cho ${target}`);
                }
            });
            
            if (changes.length > 0) {
                embed.addFields({
                    name: '> Thay đổi quyền',
                    value: changes.slice(0, 10).join('\n') + (changes.length > 10 ? `\n... và ${changes.length - 10} thay đổi khác` : ''),
                    inline: false
                });
            }
            
            embed.addFields({
                name: '> Tổng số quyền hiện tại',
                value: `- ${newOverwrites.size} permission overwrites`,
                inline: true
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Permission Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel permission update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelPermissionUpdate audit log:', error);
        }
    }
};
