const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle video quality mode changes for voice channels
            if (oldChannel.videoQualityMode === newChannel.videoQualityMode || newChannel.type !== 2) return;
            
            console.log(`📹 Channel video quality updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_VIDEO_QUALITY_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_VIDEO_QUALITY_UPDATE',
                user: 'System',
                userId: null,
                action: 'Chất lượng video kênh thoại được thay đổi',
                details: `Chất lượng video của kênh thoại **${newChannel.name}** đã được thay đổi`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the video quality from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Chất lượng video được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for video quality update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📹 Chất lượng video kênh thoại thay đổi',
                `Vừa có chất lượng video kênh thoại được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Video quality mode mapping
            const videoQualityModes = {
                1: '📱 Tự động (Auto)',
                2: '🎬 Chất lượng cao (720p)'
            };
            
            const formatVideoQuality = (mode) => {
                return videoQualityModes[mode] || 'Không xác định';
            };
            
            embed.addFields([
                {
                    name: '> Kênh thoại',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Chất lượng cũ',
                    value: `- ${formatVideoQuality(oldChannel.videoQualityMode)}`,
                    inline: true
                },
                {
                    name: '> Chất lượng mới',
                    value: `- ${formatVideoQuality(newChannel.videoQualityMode)}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Số người trong kênh',
                    value: `- ${newChannel.members.size} thành viên`,
                    inline: true
                }
            ]);
            
            // Add impact message based on quality change
            if (newChannel.videoQualityMode === 2 && oldChannel.videoQualityMode === 1) {
                embed.addFields({
                    name: '> Tác động',
                    value: `- Video giờ đây có chất lượng cao hơn (720p) nhưng tốn băng thông nhiều hơn`,
                    inline: false
                });
                embed.setColor(0x3498db); // Blue for high quality
            } else if (newChannel.videoQualityMode === 1 && oldChannel.videoQualityMode === 2) {
                embed.addFields({
                    name: '> Tác động',
                    value: `- Video giờ đây tự động điều chỉnh chất lượng để tiết kiệm băng thông`,
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for auto
            }
            
            // Add technical details
            const techDetails = [];
            if (newChannel.videoQualityMode === 1) {
                techDetails.push('• Tự động điều chỉnh dựa trên kết nối');
                techDetails.push('• Tiết kiệm băng thông');
                techDetails.push('• Phù hợp cho kết nối chậm');
            } else if (newChannel.videoQualityMode === 2) {
                techDetails.push('• Cố định 720p');
                techDetails.push('• Chất lượng cao nhất');
                techDetails.push('• Yêu cầu kết nối tốt');
            }
            
            if (techDetails.length > 0) {
                embed.addFields({
                    name: '> Chi tiết kỹ thuật',
                    value: techDetails.join('\n'),
                    inline: false
                });
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Video Quality Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel video quality update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelVideoQualityUpdate audit log:', error);
        }
    }
};
