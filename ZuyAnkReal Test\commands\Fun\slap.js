function _0x2535(){const _0x153b8c=['https://media1.tenor.com/m/5oguME-x8M0AAAAC/sassy-girl.gif','discord.js','setRequired','The\x20user\x20to\x20slap','slap','received\x20a\x20slap\x20delivered\x20with\x20pixel-perfect\x20precision.','3235XDyQGP','https://media1.tenor.com/m/W2QqtV4k6ykAAAAd/orange-cat-cat-hitting-cat.gif','Fun','exports','received\x20a\x20quick,\x20sharp\x20slap\x20from\x20a\x20digital\x20hand!','505129nbLFSp','7478436sBVWdh','setDescription','././config.yml','was\x20slapped\x20with\x20a\x20virtual\x20reality\x20glove.\x20Ouch!','got\x20a\x20playful\x20smack\x20with\x20a\x20soft,\x20stuffed\x20emoji\x20pillow.','128OrarTY','utf8','setName','setImage','2382288PPPtfo','https://media1.tenor.com/m/ahhvv7XKyVsAAAAd/lilu-cat.gif','https://media1.tenor.com/m/7UwrqI4-r3cAAAAC/smack-hit.gif','got\x20a\x20surprise\x20slap\x20from\x20a\x20sneaky\x20digital\x20shadow.','was\x20given\x20a\x20gentle\x20but\x20firm\x20slap\x20with\x20a\x20virtual\x20newspaper.','setColor','felt\x20the\x20swift\x20smack\x20of\x20a\x20digital\x20pancake.','options','You\x20slapped\x20','user','4Nxkwzo','setTitle','https://media1.tenor.com/m/vgtGULlqLkcAAAAC/slp-baba.gif','https://media1.tenor.com/m/AyGol4CaEDcAAAAd/slapping.gif','was\x20struck\x20by\x20a\x20phantom\x20slap�did\x20that\x20really\x20happen?','random','2190oqdLto','Slap\x20another\x20user\x20for\x20fun!','3352638nhNbal','193878iTEfMg','EmbedColors','22961730AZfToE','floor','https://media1.tenor.com/m/i3cGrnkMWl8AAAAC/slap-slapping.gif','load','got\x20a\x20well-timed\x20slap\x20from\x20a\x20virtual\x20high-five\x20gone\x20wrong.','was\x20humorously\x20slapped\x20by\x20an\x20unseen,\x20mischievous\x20digital\x20hand.','https://media1.tenor.com/m/dkWNqydxCBgAAAAd/pig-slap.gif','was\x20hit\x20by\x20a\x20rogue\x20drone\x20carrying\x20a\x20soft\x20boxing\x20glove.'];_0x2535=function(){return _0x153b8c;};return _0x2535();}(function(_0x1c179c,_0x3bc00b){const _0x38dd3a=_0x1c179c();function _0x5b8119(_0x41f318,_0x2a1fb0){return _0x24d8(_0x2a1fb0- -0x17d,_0x41f318);}while(!![]){try{const _0x5dd8a9=parseInt(_0x5b8119(0x35,0x1d))/0x1*(-parseInt(_0x5b8119(-0x7,-0x1))/0x2)+parseInt(_0x5b8119(-0x12,0x7))/0x3+-parseInt(_0x5b8119(-0x24,-0xb))/0x4+-parseInt(_0x5b8119(0x5,0x18))/0x5*(-parseInt(_0x5b8119(0x1e,0x5))/0x6)+-parseInt(_0x5b8119(0x24,0x1e))/0x7+parseInt(_0x5b8119(0x15,0x23))/0x8*(-parseInt(_0x5b8119(-0xa,0x8))/0x9)+parseInt(_0x5b8119(0x16,0xa))/0xa;if(_0x5dd8a9===_0x3bc00b)break;else _0x38dd3a['push'](_0x38dd3a['shift']());}catch(_0x4f9711){_0x38dd3a['push'](_0x38dd3a['shift']());}}}(_0x2535,0x9a0f0));const {EmbedBuilder,SlashCommandBuilder}=require(_0x3f6edf(0x39f,0x397)),fs=require('fs'),yaml=require('js-yaml'),config=yaml[_0x3f6edf(0x399,0x38b)](fs['readFileSync'](_0x3f6edf(0x3ac,0x3bf),_0x3f6edf(0x3b0,0x3c3))),slapActions=[_0x3f6edf(0x3ad,0x3c5),_0x3f6edf(0x3a8,0x3b1),_0x3f6edf(0x3ae,0x398),_0x3f6edf(0x39d,0x39a),'felt\x20the\x20sting\x20of\x20a\x20comedic\x20slap\x20from\x20an\x20oversized\x20foam\x20finger.',_0x3f6edf(0x385,0x371),_0x3f6edf(0x384,0x378),'was\x20mockingly\x20slapped\x20with\x20an\x20imaginary\x20velvet\x20glove.',_0x3f6edf(0x38f,0x37d),_0x3f6edf(0x3a3,0x3af),'was\x20caught\x20off-guard\x20by\x20a\x20slap\x20from\x20an\x20online\x20meme.',_0x3f6edf(0x387,0x389),'was\x20slapped\x20with\x20a\x20classic,\x20cartoon-style\x20rubber\x20chicken.',_0x3f6edf(0x39a,0x381),_0x3f6edf(0x39b,0x3a1)],slapGifs=[_0x3f6edf(0x3a5,0x399),_0x3f6edf(0x38d,0x37c),'https://media1.tenor.com/m/DFROMUjkKZIAAAAC/smack-whack.gif','https://media1.tenor.com/m/9XdFRVFCdFkAAAAC/bunny-dessert.gif','https://media1.tenor.com/m/bblihRQawfsAAAAC/kitty-slap-kat-slap.gif',_0x3f6edf(0x398,0x3a8),_0x3f6edf(0x39c,0x3ae),_0x3f6edf(0x38e,0x37c),_0x3f6edf(0x39e,0x396),_0x3f6edf(0x382,0x397),_0x3f6edf(0x383,0x374),'https://media1.tenor.com/m/m9PpGqjO3TcAAAAC/slap-slap-through-phone.gif'];function _0x3f6edf(_0x3f3ae6,_0x54518b){return _0x24d8(_0x3f3ae6-0x20f,_0x54518b);}function _0x24d8(_0x4ae173,_0x3f87fc){const _0x253526=_0x2535();return _0x24d8=function(_0x24d8f3,_0x1d0bce){_0x24d8f3=_0x24d8f3-0x171;let _0x1c98ea=_0x253526[_0x24d8f3];return _0x1c98ea;},_0x24d8(_0x4ae173,_0x3f87fc);}module[_0x3f6edf(0x3a7,0x39c)]={'data':new SlashCommandBuilder()['setName'](_0x3f6edf(0x3a2,0x3b5))['setDescription'](_0x3f6edf(0x392,0x3ab))['addUserOption'](_0x3d54b8=>_0x3d54b8[_0x3f6edf(0x3b1,0x39d)](_0x3f6edf(0x38a,0x381))['setDescription'](_0x3f6edf(0x3a1,0x38a))[_0x3f6edf(0x3a0,0x388)](!![])),'category':_0x3f6edf(0x3a6,0x39e),async 'execute'(_0x1c6186,_0x3590d8){const _0x845a46=_0x1c6186[_0x58274d(0x271,0x268)]['getUser'](_0x58274d(0x273,0x28c));function _0x58274d(_0x3dd553,_0x3540ba){return _0x3f6edf(_0x3dd553- -0x117,_0x3540ba);}const _0x34cde9=slapActions[Math[_0x58274d(0x280,0x274)](Math['random']()*slapActions['length'])],_0x5df8fe=slapGifs[Math[_0x58274d(0x280,0x271)](Math[_0x58274d(0x279,0x26e)]()*slapGifs['length'])],_0x39c262=new EmbedBuilder()[_0x58274d(0x275,0x285)](_0x58274d(0x272,0x26b)+_0x845a46['username']+'!')[_0x58274d(0x294,0x2a4)]('<@'+_0x845a46['id']+'>\x20'+_0x34cde9)[_0x58274d(0x269,0x251)](_0x5df8fe)[_0x58274d(0x26f,0x271)](config[_0x58274d(0x27e,0x268)]);await _0x1c6186['reply']({'embeds':[_0x39c262]});}};