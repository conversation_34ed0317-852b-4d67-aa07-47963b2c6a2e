// Script to setup database and required directories
const fs = require('fs');
const path = require('path');

console.log('🔧 Setting up database and directories...');

// Create data directory if it doesn't exist
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
    console.log('✅ Created data directory');
} else {
    console.log('📁 Data directory already exists');
}

// Create audit log events directories
const auditLogDir = path.join(__dirname, 'src', 'events', 'auditlog');
const categories = [
    'Applications',
    'Channels', 
    'DiscordAutoMod',
    'Emoji',
    'Events',
    'Invite',
    'Messages',
    'Polls',
    'Roles',
    'Stage',
    'Server',
    'Stickers',
    'Soundboard',
    'Thread',
    'User',
    'Voice',
    'Webhooks',
    'Moderation'
];

for (const category of categories) {
    const categoryDir = path.join(auditLogDir, category);
    if (!fs.existsSync(categoryDir)) {
        fs.mkdirSync(categoryDir, { recursive: true });
        console.log(`✅ Created ${category} directory`);
    } else {
        console.log(`📁 ${category} directory already exists`);
    }
}

console.log('🎉 Database setup completed!');
console.log('');
console.log('📋 Next steps:');
console.log('1. Install better-sqlite3: npm install better-sqlite3');
console.log('2. Start the bot: node src/index.js');
console.log('3. Run /auditlog setup in Discord');
console.log('4. Test events by creating channels, sending messages, etc.');

// Clean up this setup file
setTimeout(() => {
    try {
        fs.unlinkSync(__filename);
        console.log('🗑️ Setup file cleaned up');
    } catch (error) {
        console.log('⚠️ Could not clean up setup file');
    }
}, 2000);
