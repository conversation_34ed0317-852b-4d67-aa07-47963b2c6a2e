const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle forum layout changes for forum channels
            if (oldChannel.defaultForumLayout === newChannel.defaultForumLayout) return;
            
            console.log(`📋 Channel forum layout updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_FORUM_LAYOUT_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_FORUM_LAYOUT_UPDATE',
                user: 'System',
                userId: null,
                action: 'Layout forum của kênh được cập nhật',
                details: `Layout forum của kênh **${newChannel.name}** đã được cập nhật`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the forum layout from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Layout forum được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for channel forum layout update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📋 Layout forum của kênh cập nhật',
                `Vừa có layout forum của kênh được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Forum layout mapping
            const forumLayouts = {
                0: 'Not Set (Không thiết lập)',
                1: 'List View (Dạng danh sách)',
                2: 'Gallery View (Dạng thư viện)'
            };
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Layout cũ',
                    value: `- ${forumLayouts[oldChannel.defaultForumLayout] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Layout mới',
                    value: `- ${forumLayouts[newChannel.defaultForumLayout] || 'Unknown'}`,
                    inline: true
                }
            ]);
            
            // Add impact explanation based on layout
            if (newChannel.defaultForumLayout === 1 && oldChannel.defaultForumLayout !== 1) {
                embed.addFields({
                    name: '> 📋 Tác động (List View)',
                    value: [
                        '• Posts hiển thị dạng danh sách dọc',
                        '• Dễ đọc tiêu đề và thông tin chi tiết',
                        '• Phù hợp cho discussions và Q&A',
                        '• Hiển thị nhiều thông tin meta'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x3498db); // Blue for list view
            } else if (newChannel.defaultForumLayout === 2 && oldChannel.defaultForumLayout !== 2) {
                embed.addFields({
                    name: '> 🖼️ Tác động (Gallery View)',
                    value: [
                        '• Posts hiển thị dạng lưới với hình ảnh',
                        '• Tập trung vào nội dung visual',
                        '• Phù hợp cho showcase và media',
                        '• Giao diện đẹp mắt hơn'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x9b59b6); // Purple for gallery view
            } else if (newChannel.defaultForumLayout === 0) {
                embed.addFields({
                    name: '> ⚙️ Tác động (Not Set)',
                    value: [
                        '• Sử dụng layout mặc định của Discord',
                        '• Người dùng có thể tự chọn view',
                        '• Không ép buộc layout cụ thể',
                        '• Linh hoạt cho mọi loại nội dung'
                    ].join('\n'),
                    inline: false
                });
                embed.setColor(0x95a5a6); // Gray for not set
            }
            
            // Add layout explanations
            embed.addFields({
                name: '> 💡 Giải thích các loại layout',
                value: [
                    '**List View:**',
                    '• Hiển thị posts dạng danh sách',
                    '• Tốt cho text-based content',
                    '• Dễ scan qua nhiều posts',
                    '',
                    '**Gallery View:**',
                    '• Hiển thị posts dạng grid',
                    '• Tốt cho image/media content',
                    '• Visual appealing hơn'
                ].join('\n'),
                inline: false
            });
            
            // Add user experience note
            embed.addFields({
                name: '> 👥 Trải nghiệm người dùng',
                value: [
                    '• Thành viên sẽ thấy layout mới khi vào forum',
                    '• Có thể tự thay đổi view cá nhân',
                    '• Setting này chỉ là mặc định cho người mới',
                    '• Không ảnh hưởng đến nội dung posts'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Forum Layout Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel forum layout update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelForumLayoutUpdate audit log:', error);
        }
    }
};
