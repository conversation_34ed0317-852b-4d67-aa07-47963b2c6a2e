const chalk = require('chalk');
const dayjs = require('dayjs');

const timestamp = () => chalk.gray(`[${dayjs().format('HH:mm:ss')}]`);

const logger = {
  info: (msg) => {
    console.log(`${timestamp()} ${chalk.blue('[INFO]')} ${msg}`);
  },

  success: (msg) => {
    console.log(`${timestamp()} ${chalk.green('[SUCCESS]')} ${msg}`);
  },

  warn: (msg) => {
    console.warn(`${timestamp()} ${chalk.yellow('[WARN]')} ${msg}`);
  },

  error: (msg) => {
    console.error(`${timestamp()} ${chalk.red('[ERROR]')} ${msg}`);
  },

  debug: (msg) => {
    console.debug(`${timestamp()} ${chalk.magenta('[DEBUG]')} ${msg}`);
  }
};

module.exports = logger;