const { activity } = require('../config.json');
const { EmbedBuilder } = require('discord.js');

module.exports = async (client) => {
    client.user.setPresence({
        status: activity.status,
        activities: [{ name: activity.name, type: activity.type }]
    });
    console.log('Bot status set to:', activity.status);

    client.once("ready", async () => {
        console.log(`✅ Logged in as ${client.user.tag}`);

        // ⚡ Yaha pe apni channel ID likho
        const logChannelID = "1343560109850234944"; // 🔥 Yaha apni channel ID daalo

        try {
            // ✅ Channel fetch karna (forcefully)
            let channel = await client.channels.fetch(logChannelID);
            
            if (!channel) {
                console.log("⚠️ ERROR: Log Channel Not Found! Bot might not have access.");
                return;
            }

            await channel.send("✅ Bot is now online!");
            console.log("✅ Test message sent successfully!");

        } catch (error) {
            console.error("❌ ERROR FETCHING CHANNEL OR SENDING MESSAGE:", error);
        }
    });

    // ✅ Bot Joins a Server
    client.on("guildCreate", async (guild) => {
        const channel = await client.channels.fetch(logChannelID).catch(() => null);
        if (!channel) return console.log("⚠️ Log Channel Not Found!");

        const embed = new EmbedBuilder()
            .setTitle("🟢 Bot Joined a Server!")
            .setColor("Green")
            .setThumbnail(client.user.displayAvatarURL())
            .addFields(
                { name: "📌 Server Name", value: guild.name, inline: true },
                { name: "👥 Members", value: `${guild.memberCount}`, inline: true },
                { name: "🆔 Server ID", value: guild.id }
            )
            .setFooter({ text: `Total Servers: ${client.guilds.cache.size}` })
            .setTimestamp();

        channel.send({ embeds: [embed] }).catch(console.error);
    });

    // ✅ Bot Leaves a Server
    client.on("guildDelete", async (guild) => {
        const channel = await client.channels.fetch(logChannelID).catch(() => null);
        if (!channel) return console.log("⚠️ Log Channel Not Found!");

        const embed = new EmbedBuilder()
            .setTitle("🔴 Bot Left a Server!")
            .setColor("Red")
            .setThumbnail(client.user.displayAvatarURL())
            .addFields(
                { name: "📌 Server Name", value: guild.name || "Unknown", inline: true },
                { name: "🆔 Server ID", value: guild.id }
            )
            .setFooter({ text: `Total Servers: ${client.guilds.cache.size}` })
            .setTimestamp();

        channel.send({ embeds: [embed] }).catch(console.error);
    });
};