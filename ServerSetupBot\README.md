# 🏠 Server Setup Bot

**Discord bot chuyên về thiết lập và cấu hình máy chủ Discord với đầy đủ tính năng quản lý**

[![Discord.js](https://img.shields.io/badge/Discord.js-v14-blue.svg)](https://discord.js.org/)
[![Node.js](https://img.shields.io/badge/Node.js-v16+-green.svg)](https://nodejs.org/)
[![SQLite](https://img.shields.io/badge/Database-SQLite-lightblue.svg)](https://sqlite.org/)
[![Vietnamese](https://img.shields.io/badge/Language-Vietnamese-red.svg)](https://vi.wikipedia.org/)

## 📋 Mục lục

- [✨ Tính năng](#-tính-năng)
- [🚀 Cài đặt](#-cài-đặt)
- [⚙️ Cấu hình](#️-cấu-hình)
- [📖 Hướng dẫn sử dụng](#-hướng-dẫn-sử-dụng)
- [🔧 Lệnh có sẵn](#-lệnh-có-sẵn)
- [🛠️ Phát triển](#️-phát-triển)
- [📞 Hỗ trợ](#-hỗ-trợ)

## ✨ Tính năng

### 🎯 Tính năng chính

- **👋 Hệ thống Welcome/Goodbye**: Chào mừng và tạm biệt thành viên với tin nhắn tùy chỉnh
- **🎭 Auto-Role System**: Gán role tự động cho thành viên mới với thời gian chờ
- **📝 Channel Management**: Tạo và tổ chức kênh hàng loạt
- **🔒 Permission Management**: Quản lý quyền hạn và permission templates
- **✅ Verification System**: Hệ thống xác minh thành viên
- **🛡️ Moderation Tools**: Công cụ kiểm duyệt và automod
- **📋 Server Templates**: Tạo và áp dụng template máy chủ
- **💾 Backup & Restore**: Sao lưu và khôi phục cấu hình

### 🌟 Đặc điểm nổi bật

- **🇻🇳 Hoàn toàn tiếng Việt**: Giao diện và tin nhắn 100% tiếng Việt
- **📱 Slash Commands**: Chỉ sử dụng slash commands hiện đại
- **🎨 Discord Embeds**: Giao diện đẹp mắt với embeds
- **🏗️ Kiến trúc modular**: Cấu trúc file rõ ràng, dễ mở rộng
- **🔐 Bảo mật cao**: Kiểm tra quyền hạn nghiêm ngặt
- **⚡ Hiệu suất cao**: Tối ưu hóa cho server lớn

## 🚀 Cài đặt

### Yêu cầu hệ thống

- **Node.js**: v16.0.0 trở lên
- **NPM**: v7.0.0 trở lên
- **Discord Bot Token**: Từ Discord Developer Portal

### Bước 1: Clone repository

```bash
git clone https://github.com/your-username/ServerSetupBot.git
cd ServerSetupBot
```

### Bước 2: Cài đặt dependencies

```bash
npm install
```

### Bước 3: Thiết lập tự động (Khuyến nghị)

```bash
npm run setup
```

Script sẽ hướng dẫn bạn:

- Tạo file `.env` với thông tin bot
- Deploy slash commands tự động
- Khởi động bot ngay lập tức

### Bước 3 (Thay thế): Thiết lập thủ công

```bash
cp .env.example .env
```

Chỉnh sửa file `.env`:

```env
DISCORD_TOKEN=your_bot_token_here
CLIENT_ID=your_bot_client_id_here
OWNER_ID=your_discord_user_id_here

# Optional: For development
GUILD_ID=your_test_server_id_here
```

### Bước 4: Deploy commands (nếu thiết lập thủ công)

```bash
npm run deploy
```

### Bước 5: Khởi động bot

```bash
# Production (tự động deploy commands)
npm start

# Development (with auto-restart)
npm run dev

# Chỉ deploy commands
npm run deploy
```

## ⚙️ Cấu hình

### Tạo Discord Bot

1. Truy cập [Discord Developer Portal](https://discord.com/developers/applications)
2. Tạo **New Application**
3. Vào tab **Bot** và tạo bot
4. Copy **Token** và **Application ID**
5. Bật **Privileged Gateway Intents**:
   - ✅ Server Members Intent
   - ✅ Message Content Intent

### Mời Bot vào Server

Sử dụng link sau (thay `YOUR_CLIENT_ID`):

```
https://discord.com/api/oauth2/authorize?client_id=YOUR_CLIENT_ID&permissions=8&scope=bot%20applications.commands
```

**Quyền cần thiết:**

- Administrator (khuyến nghị) hoặc:
  - Manage Server
  - Manage Roles
  - Manage Channels
  - Send Messages
  - Embed Links
  - Add Reactions

## 📖 Hướng dẫn sử dụng

### Bắt đầu nhanh

1. **Mời bot vào server** với quyền Administrator
2. **Sử dụng `/help`** để xem tất cả lệnh
3. **Thiết lập welcome**: `/welcome-setup enable #general`
4. **Thêm auto-role**: `/autorole-setup add @Member`
5. **Kiểm tra cấu hình**: `/welcome-setup config`

### Ví dụ thiết lập cơ bản

```bash
# Thiết lập hệ thống chào mừng
/welcome-setup enable channel:#welcome message:"Chào mừng {user} đến với {server}!"

# Thêm auto-role cho thành viên mới
/autorole-setup add role:@Member delay:30

# Thiết lập tạm biệt
/goodbye-setup enable channel:#goodbye message:"Tạm biệt {username}!"
```

## 🔧 Lệnh có sẵn

### 👋 Welcome/Goodbye

- `/welcome-setup enable` - Bật hệ thống chào mừng
- `/welcome-setup disable` - Tắt hệ thống chào mừng
- `/welcome-setup test` - Kiểm tra tin nhắn chào mừng
- `/welcome-setup config` - Xem cấu hình hiện tại
- `/goodbye-setup enable` - Bật hệ thống tạm biệt
- `/goodbye-setup disable` - Tắt hệ thống tạm biệt
- `/goodbye-setup test` - Kiểm tra tin nhắn tạm biệt
- `/goodbye-setup config` - Xem cấu hình hiện tại

### 🎭 Auto Roles

- `/autorole-setup add` - Thêm auto-role
- `/autorole-setup remove` - Xóa auto-role
- `/autorole-setup list` - Xem danh sách auto-role
- `/autorole-setup clear` - Xóa tất cả auto-role

### ⚙️ Utility

- `/help` - Hiển thị hướng dẫn
- `/help [command]` - Chi tiết lệnh cụ thể
- `/help [category]` - Lệnh theo danh mục

## 🛠️ Phát triển

### Cấu trúc thư mục

```
ServerSetupBot/
├── src/
│   ├── commands/          # Slash commands
│   │   ├── welcome/       # Welcome/goodbye commands
│   │   ├── autoroles/     # Auto-role commands
│   │   └── utility/       # Utility commands
│   ├── events/            # Discord events
│   ├── database/          # Database models
│   ├── utils/             # Utility functions
│   ├── config/            # Configuration
│   └── index.js           # Main bot file
├── data/                  # Database files
├── package.json
└── README.md
```

### Thêm lệnh mới

1. Tạo file trong thư mục `src/commands/[category]/`
2. Sử dụng template:

```javascript
const { SlashCommandBuilder } = require("discord.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("command-name")
    .setDescription("Mô tả lệnh"),
  category: "category",
  adminOnly: true,
  manageServer: false,

  async execute(interaction, client) {
    // Logic lệnh
  },
};
```

3. Deploy lại commands: `npm run deploy`

### Database Schema

Bot sử dụng SQLite với các bảng chính:

- `server_configs` - Cấu hình server
- `welcome_configs` - Cấu hình welcome/goodbye
- `auto_roles` - Auto-role settings
- `reaction_roles` - Reaction role settings
- `server_templates` - Server templates
- `server_backups` - Backup data

## 📞 Hỗ trợ

### Báo lỗi

- Tạo [Issue](https://github.com/your-username/ServerSetupBot/issues) trên GitHub
- Cung cấp log lỗi và bước tái hiện

### Đóng góp

1. Fork repository
2. Tạo feature branch: `git checkout -b feature/AmazingFeature`
3. Commit changes: `git commit -m 'Add AmazingFeature'`
4. Push to branch: `git push origin feature/AmazingFeature`
5. Tạo Pull Request

### Liên hệ

- **Discord**: ZarTeam#0000
- **Email**: <EMAIL>
- **GitHub**: [@ZarTeam](https://github.com/ZarTeam)

---

## 📄 License

Dự án này được phân phối dưới giấy phép MIT. Xem file `LICENSE` để biết thêm chi tiết.

## 🙏 Cảm ơn

- [Discord.js](https://discord.js.org/) - Discord API wrapper
- [SQLite](https://sqlite.org/) - Database engine
- Cộng đồng Discord Việt Nam

---

**Được phát triển với ❤️ bởi ZarTeam**
