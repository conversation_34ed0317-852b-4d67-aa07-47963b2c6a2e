const { SlashCommandBuilder, ChannelType } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');
const { canSendMessages } = require('../../utils/permissions.js');
const { validateWelcomeMessage } = require('../../utils/validators.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('goodbye-setup')
        .setDescription('Thiết lập hệ thống tạm biệt khi thành viên rời khỏi server')
        .addSubcommand(subcommand =>
            subcommand
                .setName('enable')
                .setDescription('Bật hệ thống tạm biệt')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Kênh gửi tin nhắn tạm biệt')
                        .setRequired(true)
                        .addChannelTypes(ChannelType.GuildText)
                )
                .addStringOption(option =>
                    option.setName('message')
                        .setDescription('Tin nhắn tạm biệt (sử dụng {user}, {username}, {server}, {membercount})')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('embed')
                        .setDescription('Sử dụng embed cho tin nhắn (mặc định: true)')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('disable')
                .setDescription('Tắt hệ thống tạm biệt')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('test')
                .setDescription('Kiểm tra tin nhắn tạm biệt')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('config')
                .setDescription('Xem cấu hình hiện tại của hệ thống tạm biệt')
        ),
    category: 'welcome',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();
        const guildId = interaction.guild.id;
        
        switch (subcommand) {
            case 'enable':
                await handleEnable(interaction, client, guildId);
                break;
            case 'disable':
                await handleDisable(interaction, client, guildId);
                break;
            case 'test':
                await handleTest(interaction, client, guildId);
                break;
            case 'config':
                await handleConfig(interaction, client, guildId);
                break;
        }
    },
};

async function handleEnable(interaction, client, guildId) {
    const channel = interaction.options.getChannel('channel');
    const customMessage = interaction.options.getString('message');
    const useEmbed = interaction.options.getBoolean('embed') ?? true;
    
    // Kiểm tra quyền bot trong channel
    const canSend = canSendMessages(channel);
    if (!canSend.canSend) {
        const errorEmbed = createErrorEmbed(
            'Lỗi quyền hạn!',
            `${canSend.reason}\n\nVui lòng cấp quyền cho bot hoặc chọn kênh khác.`
        );
        return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    
    // Validate custom message nếu có
    if (customMessage) {
        const validation = validateWelcomeMessage(customMessage);
        if (!validation.isValid) {
            const errorEmbed = createErrorEmbed(
                'Tin nhắn không hợp lệ!',
                `**Lỗi:**\n${validation.errors.join('\n')}\n\n` +
                `**Placeholder hợp lệ:**\n${validation.validPlaceholders.map(p => `\`${p}\``).join(', ')}`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    }
    
    try {
        // Cập nhật cấu hình goodbye
        await client.db.updateWelcomeConfig(guildId, {
            goodbye_enabled: 1,
            goodbye_channel_id: channel.id,
            goodbye_message: customMessage,
            goodbye_embed: useEmbed ? 1 : 0
        });
        
        const successEmbed = createSuccessEmbed(
            'Hệ thống tạm biệt đã được bật!',
            `**Kênh:** ${channel}\n` +
            `**Định dạng:** ${useEmbed ? 'Embed' : 'Tin nhắn thường'}\n` +
            `**Tin nhắn tùy chỉnh:** ${customMessage ? 'Có' : 'Sử dụng mặc định'}`
        );
        
        if (customMessage) {
            successEmbed.addFields({
                name: '📝 Tin nhắn tùy chỉnh',
                value: `\`\`\`${customMessage}\`\`\``,
                inline: false
            });
        }
        
        successEmbed.addFields({
            name: '💡 Mẹo',
            value: 'Sử dụng `/goodbye-setup test` để kiểm tra tin nhắn tạm biệt!',
            inline: false
        });
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi cập nhật goodbye config:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể lưu cấu hình. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleDisable(interaction, client, guildId) {
    try {
        await client.db.updateWelcomeConfig(guildId, {
            goodbye_enabled: 0
        });
        
        const successEmbed = createSuccessEmbed(
            'Hệ thống tạm biệt đã được tắt!',
            'Bot sẽ không gửi tin nhắn tạm biệt khi thành viên rời khỏi server nữa.'
        );
        
        await interaction.reply({ embeds: [successEmbed] });
        
    } catch (error) {
        console.error('Lỗi khi tắt goodbye:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể tắt hệ thống tạm biệt. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleTest(interaction, client, guildId) {
    try {
        const config = await client.db.getWelcomeConfig(guildId);
        
        if (!config || !config.goodbye_enabled) {
            const errorEmbed = createErrorEmbed(
                'Hệ thống chưa được bật!',
                'Vui lòng sử dụng `/goodbye-setup enable` để bật hệ thống tạm biệt trước.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const channel = interaction.guild.channels.cache.get(config.goodbye_channel_id);
        if (!channel) {
            const errorEmbed = createErrorEmbed(
                'Kênh không tồn tại!',
                'Kênh tạm biệt đã bị xóa. Vui lòng thiết lập lại.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        // Tạo tin nhắn test với thông tin của user hiện tại
        const { createGoodbyeEmbed } = require('../../utils/embedBuilder.js');
        
        if (config.goodbye_embed) {
            const goodbyeEmbed = createGoodbyeEmbed(interaction.member, config.goodbye_message);
            goodbyeEmbed.setTitle('🧪 [TEST] Tạm biệt!');
            await channel.send({ embeds: [goodbyeEmbed] });
        } else {
            const defaultMessage = `**{username}** đã rời khỏi **{server}**.\n\nChúc bạn may mắn! ${client.config.emojis.goodbye}`;
            const message = config.goodbye_message || defaultMessage;
            const processedMessage = message
                .replace(/{user}/g, `<@${interaction.user.id}>`)
                .replace(/{username}/g, interaction.user.username)
                .replace(/{server}/g, interaction.guild.name)
                .replace(/{membercount}/g, interaction.guild.memberCount.toString());
            
            await channel.send(`🧪 **[TEST]** ${processedMessage}`);
        }
        
        const successEmbed = createSuccessEmbed(
            'Tin nhắn test đã được gửi!',
            `Kiểm tra tin nhắn tạm biệt tại ${channel}`
        );
        
        await interaction.reply({ embeds: [successEmbed], ephemeral: true });
        
    } catch (error) {
        console.error('Lỗi khi test goodbye:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể gửi tin nhắn test. Vui lòng kiểm tra quyền bot!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleConfig(interaction, client, guildId) {
    try {
        const config = await client.db.getWelcomeConfig(guildId);
        
        if (!config) {
            const infoEmbed = createInfoEmbed(
                'Chưa có cấu hình',
                'Hệ thống tạm biệt chưa được thiết lập.\n\nSử dụng `/goodbye-setup enable` để bắt đầu!'
            );
            return await interaction.reply({ embeds: [infoEmbed] });
        }
        
        const channel = interaction.guild.channels.cache.get(config.goodbye_channel_id);
        const channelText = channel ? `${channel}` : '❌ Kênh đã bị xóa';
        
        const embed = createInfoEmbed(
            'Cấu hình hệ thống tạm biệt',
            `**Trạng thái:** ${config.goodbye_enabled ? '✅ Đã bật' : '❌ Đã tắt'}\n` +
            `**Kênh:** ${channelText}\n` +
            `**Định dạng:** ${config.goodbye_embed ? 'Embed' : 'Tin nhắn thường'}`
        );
        
        if (config.goodbye_message) {
            embed.addFields({
                name: '📝 Tin nhắn tùy chỉnh',
                value: `\`\`\`${config.goodbye_message}\`\`\``,
                inline: false
            });
        }
        
        embed.addFields({
            name: '🔧 Quản lý',
            value: '• `/goodbye-setup enable` - Bật/cập nhật cấu hình\n' +
                   '• `/goodbye-setup disable` - Tắt hệ thống\n' +
                   '• `/goodbye-setup test` - Kiểm tra tin nhắn',
            inline: false
        });
        
        await interaction.reply({ embeds: [embed] });
        
    } catch (error) {
        console.error('Lỗi khi lấy config:', error);
        const errorEmbed = createErrorEmbed(
            'Lỗi hệ thống!',
            'Không thể lấy cấu hình. Vui lòng thử lại sau!'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}
