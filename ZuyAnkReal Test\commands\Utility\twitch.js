function _0x8507(){const _0x263898=['Remove\x20a\x20Twitch\x20streamer\x20from\x20monitoring','**\x20is\x20already\x20being\x20monitored.','list','Name\x20of\x20the\x20Twitch\x20streamer\x20to\x20remove','test','save','editReply','addSubcommand','7158455ifFyXj','11694104cUXEfd','9208344ZniDiV','setRequired','Manage\x20Twitch\x20streamers','findOne','add','Monitored\x20Twitch\x20Streamers','1478svjXEB','find','deferReply','join','Name\x20of\x20the\x20Twitch\x20streamer\x20to\x20add','1626GTJdpi','Twitch\x20Streamer\x20List','discord_user','setName','Select\x20the\x20Discord\x20user\x20of\x20the\x20streamer','name','getUser','setTitle','streamer','An\x20error\x20occurred\x20while\x20executing\x20the\x20command.\x20Please\x20try\x20again\x20later.','7732RujbgY','discordUserId','getString','map','addStringOption','List\x20all\x20monitored\x20Twitch\x20streamers','Not\x20Linked','remove','options','#1E90FF','Streamer\x20**','8571339mPlSIs','setFooter','27183906cAUpAZ','twitch','discord.js','Error\x20executing\x20Twitch\x20command:','Add\x20a\x20Twitch\x20streamer\x20to\x20monitor','setColor','1011hxBVMt','../../models/twitch','**\x20has\x20been\x20removed\x20from\x20monitoring.','**\x20has\x20been\x20added\x20to\x20monitoring.','setDescription','length'];_0x8507=function(){return _0x263898;};return _0x8507();}function _0x3ab0(_0x1b83f6,_0x3807af){const _0x85072b=_0x8507();return _0x3ab0=function(_0x3ab020,_0x26bca9){_0x3ab020=_0x3ab020-0x1d7;let _0x3e5830=_0x85072b[_0x3ab020];return _0x3e5830;},_0x3ab0(_0x1b83f6,_0x3807af);}(function(_0x3807b8,_0x1b2d63){const _0x5e07cc=_0x3807b8();function _0x116696(_0x1cef24,_0x18bf09){return _0x3ab0(_0x1cef24- -0x181,_0x18bf09);}while(!![]){try{const _0x4c41f3=-parseInt(_0x116696(0x78,0x81))/0x1*(-parseInt(_0x116696(0x7d,0x70))/0x2)+parseInt(_0x116696(0x62,0x46))/0x3*(-parseInt(_0x116696(0x87,0x9b))/0x4)+parseInt(_0x116696(0x70,0x54))/0x5+-parseInt(_0x116696(0x72,0x8c))/0x6+-parseInt(_0x116696(0x5a,0x6a))/0x7+-parseInt(_0x116696(0x71,0x81))/0x8+parseInt(_0x116696(0x5c,0x41))/0x9;if(_0x4c41f3===_0x1b2d63)break;else _0x5e07cc['push'](_0x5e07cc['shift']());}catch(_0x5c98db){_0x5e07cc['push'](_0x5e07cc['shift']());}}}(_0x8507,0xbec2a));const {SlashCommandBuilder,EmbedBuilder}=require(_0x1b2786(-0x202,-0x1e8)),TwitchStreamers=require(_0x1b2786(-0x1fd,-0x1e3)),twitchCommand={'data':new SlashCommandBuilder()[_0x1b2786(-0x1b7,-0x1c6)](_0x1b2786(-0x200,-0x1e9))['setDescription'](_0x1b2786(-0x1ea,-0x1d2))['addSubcommand'](_0x47ad7c=>_0x47ad7c[_0x1b2786(-0x1d9,-0x1c6)](_0x1b2786(-0x1d0,-0x1d0))[_0x1b2786(-0x1f2,-0x1e0)](_0x1b2786(-0x1fe,-0x1e6))[_0x1b2786(-0x1b9,-0x1bb)](_0x3f9e75=>_0x3f9e75['setName'](_0x1b2786(-0x1a8,-0x1c1))[_0x1b2786(-0x1c4,-0x1e0)](_0x1b2786(-0x1dc,-0x1ca))[_0x1b2786(-0x1cd,-0x1d3)](!![]))['addUserOption'](_0x5426f8=>_0x5426f8['setName'](_0x1b2786(-0x1b5,-0x1c7))[_0x1b2786(-0x1fc,-0x1e0)](_0x1b2786(-0x1ca,-0x1c5))[_0x1b2786(-0x1db,-0x1d3)](![])))[_0x1b2786(-0x1ca,-0x1d7)](_0x42a329=>_0x42a329[_0x1b2786(-0x1cf,-0x1c6)](_0x1b2786(-0x1d9,-0x1f0))['setDescription'](_0x1b2786(-0x1dc,-0x1de))[_0x1b2786(-0x1c8,-0x1bb)](_0x22e0ca=>_0x22e0ca['setName'](_0x1b2786(-0x1c5,-0x1c1))['setDescription'](_0x1b2786(-0x1db,-0x1db))[_0x1b2786(-0x1e4,-0x1d3)](!![])))[_0x1b2786(-0x1c1,-0x1d7)](_0x3c6607=>_0x3c6607[_0x1b2786(-0x1bd,-0x1c6)](_0x1b2786(-0x1d5,-0x1dc))['setDescription'](_0x1b2786(-0x1aa,-0x1ba))),'category':'Utility',async 'execute'(_0x343dd3){await _0x343dd3[_0x199f03(0x2d3,0x2e4)]({'ephemeral':!![]});const _0x4499e0=_0x343dd3[_0x199f03(0x2b6,0x2c1)]['getSubcommand'](),_0x37af98=_0x343dd3[_0x199f03(0x2d8,0x2c1)][_0x199f03(0x2f3,0x2f3)](_0x199f03(0x2eb,0x2ef),![]),_0x43929b=_0x343dd3[_0x199f03(0x2dd,0x2c1)][_0x199f03(0x2f5,0x2ed)]('discord_user',![]);function _0x199f03(_0x4bb647,_0x5256eb){return _0x1b2786(_0x4bb647,_0x5256eb-0x4b0);}const _0x22cbd8=/https?:\/\/[^\s]+/i;if(_0x37af98&&_0x22cbd8[_0x199f03(0x2e6,0x2d6)](_0x37af98))return _0x343dd3['editReply']({'content':'Please\x20use\x20the\x20streamer\x27s\x20name\x20instead\x20of\x20a\x20URL.','ephemeral':!![]});try{if(_0x4499e0===_0x199f03(0x2f2,0x2e0)){const _0x2e8202=await TwitchStreamers[_0x199f03(0x2f2,0x2df)]({'name':_0x37af98});if(_0x2e8202)return _0x343dd3[_0x199f03(0x2bf,0x2d8)]({'content':_0x199f03(0x2c9,0x2c3)+_0x37af98+_0x199f03(0x2ea,0x2d3),'ephemeral':!![]});const _0x5a1953=new TwitchStreamers({'name':_0x37af98,'discordUserId':_0x43929b?_0x43929b['id']:undefined});return await _0x5a1953[_0x199f03(0x2e8,0x2d7)](),_0x343dd3[_0x199f03(0x2cb,0x2d8)]({'content':_0x199f03(0x2c2,0x2c3)+_0x37af98+_0x199f03(0x2c3,0x2cf),'ephemeral':!![]});}else{if(_0x4499e0==='remove'){const _0x403205=await TwitchStreamers['findOneAndDelete']({'name':_0x37af98});if(!_0x403205)return _0x343dd3['editReply']({'content':'Streamer\x20**'+_0x37af98+'**\x20not\x20found.','ephemeral':!![]});return _0x343dd3[_0x199f03(0x2bd,0x2d8)]({'content':_0x199f03(0x2de,0x2c3)+_0x37af98+_0x199f03(0x2dd,0x2ce),'ephemeral':!![]});}else{if(_0x4499e0==='list'){const _0x47b416=await TwitchStreamers[_0x199f03(0x2e2,0x2e3)]({}),_0x3ab80c=_0x47b416[_0x199f03(0x2da,0x2f4)](_0x22931a=>_0x22931a[_0x199f03(0x2eb,0x2ec)]+'\x20(Discord\x20ID:\x20'+(_0x22931a[_0x199f03(0x2dd,0x2f2)]||_0x199f03(0x2e9,0x2f7))+')'),_0x318cf7=new EmbedBuilder()[_0x199f03(0x305,0x2ee)](_0x199f03(0x2d8,0x2e1))['setDescription'](_0x3ab80c[_0x199f03(0x2e8,0x2d1)]>0x0?_0x3ab80c[_0x199f03(0x2f7,0x2e5)]('\x0a'):'No\x20Twitch\x20streamers\x20are\x20being\x20monitored.')[_0x199f03(0x2e2,0x2cb)](_0x199f03(0x2c3,0x2c2))[_0x199f03(0x2cd,0x2c5)]({'text':_0x199f03(0x2da,0x2e8)});return _0x343dd3['editReply']({'embeds':[_0x318cf7],'ephemeral':!![]});}}}}catch(_0x448350){return console['error'](_0x199f03(0x2d2,0x2c9),_0x448350),_0x343dd3['editReply']({'content':_0x199f03(0x2e3,0x2f0),'ephemeral':!![]});}}};function _0x1b2786(_0x451100,_0x2d3fd7){return _0x3ab0(_0x2d3fd7- -0x3c7,_0x451100);}module['exports']=twitchCommand;