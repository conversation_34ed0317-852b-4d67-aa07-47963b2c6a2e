const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildBanAdd,
    async execute(ban, client) {
        try {
            console.log(`🔨 User banned: ${ban.user.tag} in ${ban.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(ban.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'BAN_ADD')) return;
            
            // Create event data
            const eventData = {
                eventType: 'BAN_ADD',
                user: ban.user.tag,
                userId: ban.user.id,
                action: 'Thành viên bị ban',
                details: `**${ban.user.tag}** đã bị ban khỏi server`,
                target: ban.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who banned the user from audit logs
            try {
                const auditLogs = await ban.guild.fetchAuditLogs({
                    type: 22, // MEMBER_BAN_ADD
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === ban.user.id) {
                    eventData.moderator = auditEntry.executor.tag;
                    eventData.moderatorId = auditEntry.executor.id;
                    eventData.action = `Thành viên bị ban bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for ban add');
            }
            
            // Add to database
            await client.db.addAuditLog(ban.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔨 Thành viên bị ban',
                `Vừa có thành viên bị ban khỏi server`
            );
            
            embed.setColor(0xe74c3c); // Red for ban
            
            embed.addFields([
                {
                    name: '> Thành viên bị ban',
                    value: `- ${ban.user.tag} (${ban.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${ban.user.id}`,
                    inline: true
                },
                {
                    name: '> Thời gian ban',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add moderator info if available
            if (eventData.moderator) {
                embed.addFields({
                    name: '> Moderator',
                    value: `- ${eventData.moderator}`,
                    inline: true
                });
            }
            
            // Add ban reason if available
            if (ban.reason || eventData.reason) {
                embed.addFields({
                    name: '> Lý do ban',
                    value: `- ${ban.reason || eventData.reason}`,
                    inline: false
                });
            }
            
            // Add user info
            const accountAge = Date.now() - ban.user.createdTimestamp;
            const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
            
            embed.addFields([
                {
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                },
                {
                    name: '> Tạo tài khoản lúc',
                    value: `- <t:${Math.floor(ban.user.createdTimestamp / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add ban impact
            embed.addFields({
                name: '> 📝 Tác động của ban',
                value: [
                    '• Thành viên bị loại khỏi server',
                    '• Không thể tham gia lại trừ khi được unban',
                    '• Mất tất cả roles và permissions',
                    '• Tin nhắn vẫn tồn tại trong server'
                ].join('\n'),
                inline: false
            });
            
            // Add moderation guidelines
            embed.addFields({
                name: '> 💡 Lưu ý về ban',
                value: [
                    '• Ban là hình phạt nghiêm trọng nhất',
                    '• Nên cân nhắc kỹ trước khi ban',
                    '• Có thể unban nếu cần thiết',
                    '• Ghi chép lý do rõ ràng'
                ].join('\n'),
                inline: false
            });
            
            // Set user avatar as thumbnail
            if (ban.user.displayAvatarURL()) {
                embed.setThumbnail(ban.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${ban.guild.name} • Member Ban Add`,
                iconURL: ban.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Ban add logged: ${ban.user.tag}`);
            
        } catch (error) {
            console.error('Error in banAdd audit log:', error);
        }
    }
};
