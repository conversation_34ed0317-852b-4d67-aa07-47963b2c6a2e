const Command = require("../../structures/Command");
const Discord = require("discord.js");

module.exports = class TemplateInfo extends Command {
  constructor(client) {
    super(client, {
      name: "info",
      description: client.cmdConfig.info.description,
      usage: client.cmdConfig.info.usage,
      permissions: client.cmdConfig.info.permissions,
      aliases: client.cmdConfig.info.aliases,
      category: "setups",
      listed: client.cmdConfig.info.enabled,
      slash: true,
      options: [{
        name: "name", 
        description: "Template Name",
        type: Discord.ApplicationCommandOptionType.String, 
        required: true
      }]
    });
  }

  async run(message, args) {
    let template = args[0];
    if(!template) return message.channel.send({ embeds: [this.client.utils.usage(this.client, this.client.cmdConfig.info.usage)]});
    if(!this.client.template(template)) return message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
      this.client.language.titles.error, this.client.language.setup.invalid_template, this.client.embeds.error_color)]});
    let findTemplate = this.client.template(template);

    let categories = [];
    let empty = [];

    let categoryList = findTemplate.channels.filter((o) => o.split("|")[2].trim() == "CATEGORY");
    let chFind = findTemplate.channels.filter((o) => o.split("|")[0].trim() != "null");
    let noCategory = findTemplate.channels.filter((o) => o.split("|")[0].trim() == "null" && o.split("|")[2].trim() != "CATEGORY");

    categoryList.forEach((ca) => {
      let caName = ca.split("|")[1].trim();
      categories.push({
        name: "˅ " + caName,
        channels: []
      })
    });

    chFind.forEach((c) => {
      let found = categories.find((f) => f.name == "˅ " + c.split("|")[0].trim());
      let cType = c.split("|")[2].trim();
      found.channels.push(cType == "TEXT" ? "  # " + c.split("|")[1].trim() : "  % " + c.split("|")[1].trim());
    })

    noCategory.forEach((c) => {
      let cType = c.split("|")[2].trim();
      empty.push(cType == "TEXT" ? "# " + c.split("|")[1].trim() : "% " + c.split("|")[1].trim());
    });

    let emptyContent = "";
    empty.forEach((e) => {
      emptyContent += `${e}\n`
    });

    let orderContent = "";
    categories.forEach((e) => {
      orderContent += `\n${e.name}\n${e.channels.join("\n")}`
    });

    let allChannels = emptyContent + "\n" + orderContent;
    let allRoles = "";
    findTemplate.roles.forEach((r) => {
      allRoles += `\n${r.name}`;
    });

    let embed = new Discord.EmbedBuilder()
      .setColor(this.client.embeds.setup.templateInfo.color);

    if (this.client.embeds.setup.templateInfo.title) embed.setTitle(this.client.embeds.setup.templateInfo.title);

    if (this.client.embeds.setup.templateInfo.description) embed.setDescription(this.client.embeds.setup.templateInfo.description.replace("<channels>", allChannels)
      .replace("<roles>", allRoles)
      .replace("<name>", this.client.utils.capitalizeFirstLetter(template))
      .replace("<emojisCount>", findTemplate.emojis.length));

    let field = this.client.embeds.setup.templateInfo.fields;
    for (let i = 0; i < this.client.embeds.setup.templateInfo.fields.length; i++) {
      embed.addFields([{ name: field[i].title, value: field[i].description.replace("<channels>", allChannels)
        .replace("<roles>", allRoles)
        .replace("<name>", this.client.utils.capitalizeFirstLetter(template))
        .replace("<emojisCount>", findTemplate.emojis.length) }]);
    }

    if (this.client.embeds.setup.templateInfo.footer == true) embed.setFooter({ text: message.author.username, iconURL: message.author.displayAvatarURL() }).setTimestamp();
    if (this.client.embeds.setup.templateInfo.thumbnail == true) embed.setThumbnail(message.guild.iconURL());
    
    message.channel.send({ embeds: [embed] });
  }
  async slashRun(interaction, args) {
    let template = interaction.options.getString("name");
    if(!this.client.template(template)) return interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
      this.client.language.titles.error, this.client.language.setup.invalid_template, this.client.embeds.error_color)]});
    let findTemplate = this.client.template(template);

    let categories = [];
    let empty = [];

    let categoryList = findTemplate.channels.filter((o) => o.split("|")[2].trim() == "CATEGORY");
    let chFind = findTemplate.channels.filter((o) => o.split("|")[0].trim() != "null");
    let noCategory = findTemplate.channels.filter((o) => o.split("|")[0].trim() == "null" && o.split("|")[2].trim() != "CATEGORY");

    categoryList.forEach((ca) => {
      let caName = ca.split("|")[1].trim();
      categories.push({
        name: "˅ " + caName,
        channels: []
      })
    });

    chFind.forEach((c) => {
      let found = categories.find((f) => f.name == "˅ " + c.split("|")[0].trim());
      let cType = c.split("|")[2].trim();
      found.channels.push(cType == "TEXT" ? "  # " + c.split("|")[1].trim() : "  % " + c.split("|")[1].trim());
    })

    noCategory.forEach((c) => {
      let cType = c.split("|")[2].trim();
      empty.push(cType == "TEXT" ? "# " + c.split("|")[1].trim() : "% " + c.split("|")[1].trim());
    });

    let emptyContent = "";
    empty.forEach((e) => {
      emptyContent += `${e}\n`
    });

    let orderContent = "";
    categories.forEach((e) => {
      orderContent += `${e.name}\n${e.channels.join("\n")}`
    });

    let allChannels = emptyContent + "\n" + orderContent;
    let allRoles = "";
    findTemplate.roles.forEach((r) => {
      allRoles += `\n${r.name}`;
    });

    let embed = new Discord.EmbedBuilder()
      .setColor(this.client.embeds.setup.templateInfo.color);

    if (this.client.embeds.setup.templateInfo.title) embed.setTitle(this.client.embeds.setup.templateInfo.title);

    if (this.client.embeds.setup.templateInfo.description) embed.setDescription(this.client.embeds.setup.templateInfo.description.replace("<channels>", allChannels)
      .replace("<roles>", allRoles)
      .replace("<name>", this.client.utils.capitalizeFirstLetter(template))
      .replace("<emojisCount>", findTemplate.emojis.length));

    let field = this.client.embeds.setup.templateInfo.fields;
    for (let i = 0; i < this.client.embeds.setup.templateInfo.fields.length; i++) {
      embed.addFields([{ name: field[i].title, value: field[i].description.replace("<channels>", allChannels)
        .replace("<roles>", allRoles)
        .replace("<name>", this.client.utils.capitalizeFirstLetter(template))
        .replace("<emojisCount>", findTemplate.emojis.length) }]);
    }

    if (this.client.embeds.setup.templateInfo.footer == true) embed.setFooter({ text: interaction.user.username, iconURL: interaction.user.displayAvatarURL() }).setTimestamp();
    if (this.client.embeds.setup.templateInfo.thumbnail == true) embed.setThumbnail(interaction.guild.iconURL());

    interaction.reply({ embeds: [embed], ephemeral: this.client.cmdConfig.templates.ephemeral });
  }
};
