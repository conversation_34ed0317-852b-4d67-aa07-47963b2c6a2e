const {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRow<PERSON>uilder,
  ButtonBuilder,
  ButtonStyle,
  ChannelSelectMenuBuilder,
  RoleSelectMenuBuilder,
  UserSelectMenuBuilder,
  ChannelType,
} = require("discord.js");
const {
  createInfoEmbed,
  createSuccessEmbed,
  createErrorEmbed,
} = require("../../utils/embedBuilder.js");

module.exports = {
  async execute(interaction, client, params) {
    console.log(`🔧 AuditLog button handler called with params:`, params);
    const action = params[0]; // settings
    const subAction = params[1]; // channel, events, alerts, ignore
    console.log(`🎯 Action: ${action}, SubAction: ${subAction}`);

    try {
      if (action === "settings") {
        switch (subAction) {
          case "channel":
            await handleChannelSettings(interaction, client);
            break;
          case "events":
            await handleEventsSettings(interaction, client);
            break;
          case "alerts":
            await handleAlertsSettings(interaction, client);
            break;
          case "ignore":
            await handleIgnoreSettings(interaction, client);
            break;
          case "cancel":
            await handleCancel(interaction, client);
            break;
          case "back":
            await handleBack(interaction, client);
            break;
          default:
            await handleDefault(interaction, client);
            break;
        }
      } else if (action === "alerts") {
        switch (subAction) {
          case "toggle":
            await handleAlertsToggle(interaction, client);
            break;
          case "thresholds":
            await handleAlertsThresholds(interaction, client);
            break;
          case "test":
            await handleAlertsTest(interaction, client);
            break;
          default:
            await handleDefault(interaction, client);
            break;
        }
      } else if (action === "ignore") {
        switch (subAction) {
          case "clear":
            const clearType = params[2]; // roles or users
            await handleIgnoreClear(interaction, client, clearType);
            break;
          default:
            await handleDefault(interaction, client);
            break;
        }
      } else {
        await handleDefault(interaction, client);
      }
    } catch (error) {
      console.error("Lỗi trong auditlog button handler:", error);

      const errorEmbed = createErrorEmbed(
        "Lỗi hệ thống!",
        "Không thể xử lý yêu cầu. Vui lòng thử lại sau!"
      );

      if (interaction.replied || interaction.deferred) {
        await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
      } else {
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
      }
    }
  },
};

async function handleChannelSettings(interaction, client) {
  await interaction.deferUpdate();

  const embed = createInfoEmbed(
    "📍 Thay đổi kênh Audit Log",
    "Chọn kênh mới để gửi audit logs"
  );

  embed.addFields({
    name: "📋 Hướng dẫn",
    value:
      "• Chọn kênh text từ menu bên dưới\n• Bot cần quyền Send Messages và Embed Links\n• Kênh cũ sẽ nhận thông báo về việc chuyển đổi",
    inline: false,
  });

  const channelSelect = new ChannelSelectMenuBuilder()
    .setCustomId("auditlog_channel_select")
    .setPlaceholder("Chọn kênh mới cho audit log")
    .addChannelTypes(ChannelType.GuildText)
    .setMaxValues(1);

  const cancelButton = new ButtonBuilder()
    .setCustomId("auditlog_settings_cancel")
    .setLabel("❌ Hủy")
    .setStyle(ButtonStyle.Secondary);

  const components = [
    new ActionRowBuilder().addComponents(channelSelect),
    new ActionRowBuilder().addComponents(cancelButton),
  ];

  await interaction.editReply({ embeds: [embed], components });
}

async function handleEventsSettings(interaction, client) {
  await interaction.deferUpdate();

  const guildId = interaction.guild.id;
  const config = await client.db.getAuditLogConfig(guildId);

  const embed = createInfoEmbed(
    "📋 Cài đặt Events",
    "Chọn loại events muốn theo dõi"
  );

  embed.addFields(
    {
      name: "🌟 Tất cả Events",
      value: "Theo dõi mọi hoạt động trong server (150+ events)",
      inline: false,
    },
    {
      name: "🔨 Moderation Only",
      value: "Chỉ theo dõi các hoạt động moderation (ban, kick, mute, warn...)",
      inline: false,
    },
    {
      name: "🏰 Server Events",
      value: "Theo dõi thay đổi server (settings, boost, member join/leave...)",
      inline: false,
    },
    {
      name: "📝 Channel Events",
      value: "Theo dõi hoạt động kênh (tạo, xóa, chỉnh sửa...)",
      inline: false,
    },
    {
      name: "👥 User Events",
      value: "Theo dõi hoạt động user (role, nickname, avatar...)",
      inline: false,
    }
  );

  const currentEvents = config?.events || "all";

  const components = createEventsComponents(currentEvents);

  await interaction.editReply({ embeds: [embed], components });
}

async function handleAlertsSettings(interaction, client) {
  await interaction.deferUpdate();

  const guildId = interaction.guild.id;
  const config = await client.db.getAuditLogConfig(guildId);

  const embed = createInfoEmbed(
    "🔔 Cài đặt Smart Alerts",
    "Cấu hình cảnh báo thông minh cho các hành vi bất thường"
  );

  const thresholds = config?.alertThresholds || {
    messageDelete: 10,
    roleGrant: 5,
    channelCreate: 3,
    memberKick: 5,
    memberBan: 3,
  };

  embed.addFields(
    {
      name: "⚠️ Ngưỡng cảnh báo hiện tại",
      value:
        `• **Message Delete:** ${thresholds.messageDelete} tin nhắn/5 phút\n` +
        `• **Role Grant:** ${thresholds.roleGrant} roles/10 phút\n` +
        `• **Channel Create:** ${thresholds.channelCreate} kênh/5 phút\n` +
        `• **Member Kick:** ${thresholds.memberKick} kicks/10 phút\n` +
        `• **Member Ban:** ${thresholds.memberBan} bans/10 phút`,
      inline: false,
    },
    {
      name: "🎯 Smart Alerts hoạt động như thế nào?",
      value:
        "• Phát hiện hành vi bất thường dựa trên ngưỡng\n• Gửi cảnh báo DM cho owner server\n• Ghi log chi tiết về incident\n• Đề xuất hành động khắc phục",
      inline: false,
    }
  );

  const alertsEnabled = config?.smartAlerts !== false;

  const components = createAlertsComponents(alertsEnabled);

  await interaction.editReply({ embeds: [embed], components });
}

async function handleIgnoreSettings(interaction, client) {
  await interaction.deferUpdate();

  const guildId = interaction.guild.id;
  const config = await client.db.getAuditLogConfig(guildId);

  const embed = createInfoEmbed(
    "🚫 Cài đặt Bỏ qua",
    "Cấu hình roles và users sẽ không được ghi log"
  );

  const ignoredRoles = config?.ignoredRoles || [];
  const ignoredUsers = config?.ignoredUsers || [];

  embed.addFields(
    {
      name: "👥 Roles bị bỏ qua",
      value:
        ignoredRoles.length > 0
          ? ignoredRoles.map((roleId) => `<@&${roleId}>`).join(", ")
          : "Không có roles nào bị bỏ qua",
      inline: false,
    },
    {
      name: "🚫 Users bị bỏ qua",
      value:
        ignoredUsers.length > 0
          ? ignoredUsers.map((userId) => `<@${userId}>`).join(", ")
          : "Không có users nào bị bỏ qua",
      inline: false,
    },
    {
      name: "💡 Lưu ý",
      value:
        "• Hoạt động của roles/users bị bỏ qua sẽ không được ghi log\n• Hữu ích để loại trừ bot hoặc admin khỏi logs\n• Owner server luôn được ghi log",
      inline: false,
    }
  );

  const components = createIgnoreComponents();

  await interaction.editReply({ embeds: [embed], components });
}

async function handleDefault(interaction, client) {
  await interaction.deferUpdate();

  const embed = createInfoEmbed(
    "⚙️ Audit Log Settings",
    "Tính năng cài đặt audit log đang được phát triển."
  );

  await interaction.editReply({ embeds: [embed] });
}

function createEventsComponents(currentEvents) {
  const buttonRow1 = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("auditlog_events_all")
      .setLabel("🌟 Tất cả")
      .setStyle(
        currentEvents === "all" ? ButtonStyle.Success : ButtonStyle.Secondary
      ),
    new ButtonBuilder()
      .setCustomId("auditlog_events_moderation")
      .setLabel("🔨 Moderation")
      .setStyle(
        currentEvents === "moderation"
          ? ButtonStyle.Success
          : ButtonStyle.Secondary
      ),
    new ButtonBuilder()
      .setCustomId("auditlog_events_server")
      .setLabel("🏰 Server")
      .setStyle(
        currentEvents === "server" ? ButtonStyle.Success : ButtonStyle.Secondary
      )
  );

  const buttonRow2 = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("auditlog_events_channels")
      .setLabel("📝 Channels")
      .setStyle(
        currentEvents === "channels"
          ? ButtonStyle.Success
          : ButtonStyle.Secondary
      ),
    new ButtonBuilder()
      .setCustomId("auditlog_events_users")
      .setLabel("👥 Users")
      .setStyle(
        currentEvents === "users" ? ButtonStyle.Success : ButtonStyle.Secondary
      ),
    new ButtonBuilder()
      .setCustomId("auditlog_settings_cancel")
      .setLabel("❌ Hủy")
      .setStyle(ButtonStyle.Danger)
  );

  return [buttonRow1, buttonRow2];
}

function createAlertsComponents(alertsEnabled) {
  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("auditlog_alerts_toggle")
      .setLabel(alertsEnabled ? "🔔 Tắt Alerts" : "🔕 Bật Alerts")
      .setStyle(alertsEnabled ? ButtonStyle.Danger : ButtonStyle.Success),
    new ButtonBuilder()
      .setCustomId("auditlog_alerts_thresholds")
      .setLabel("⚙️ Chỉnh ngưỡng")
      .setStyle(ButtonStyle.Primary),
    new ButtonBuilder()
      .setCustomId("auditlog_alerts_test")
      .setLabel("🧪 Test Alert")
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId("auditlog_settings_cancel")
      .setLabel("❌ Hủy")
      .setStyle(ButtonStyle.Secondary)
  );

  return [buttonRow];
}

function createIgnoreComponents() {
  const selectRow = new ActionRowBuilder().addComponents(
    new RoleSelectMenuBuilder()
      .setCustomId("auditlog_ignore_roles")
      .setPlaceholder("Chọn roles để bỏ qua")
      .setMaxValues(10)
  );

  const userSelectRow = new ActionRowBuilder().addComponents(
    new UserSelectMenuBuilder()
      .setCustomId("auditlog_ignore_users")
      .setPlaceholder("Chọn users để bỏ qua")
      .setMaxValues(10)
  );

  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("auditlog_ignore_clear_roles")
      .setLabel("🗑️ Xóa tất cả roles")
      .setStyle(ButtonStyle.Danger),
    new ButtonBuilder()
      .setCustomId("auditlog_ignore_clear_users")
      .setLabel("🗑️ Xóa tất cả users")
      .setStyle(ButtonStyle.Danger),
    new ButtonBuilder()
      .setCustomId("auditlog_settings_cancel")
      .setLabel("❌ Hủy")
      .setStyle(ButtonStyle.Secondary)
  );

  return [selectRow, userSelectRow, buttonRow];
}

// New handler functions
async function handleCancel(interaction, client) {
  await interaction.deferUpdate();

  const embed = createInfoEmbed("❌ Đã hủy", "Thao tác đã được hủy bỏ.");

  await interaction.editReply({ embeds: [embed], components: [] });
}

async function handleBack(interaction, client) {
  // Go back to main settings
  const guildId = interaction.guild.id;
  const config = await client.db.getAuditLogConfig(guildId);

  if (!config) {
    const errorEmbed = createErrorEmbed(
      "Chưa thiết lập!",
      "Vui lòng thiết lập audit log trước."
    );
    return await interaction.editReply({
      embeds: [errorEmbed],
      components: [],
    });
  }

  // Recreate main settings embed (same as in auditlog.js handleSettings)
  const embed = createInfoEmbed(
    "⚙️ Cài đặt Audit Log",
    `Cấu hình hệ thống audit log cho **${interaction.guild.name}**`
  );

  embed.addFields([
    { name: "📍 Kênh Log", value: `<#${config.channelId}>`, inline: true },
    { name: "📋 Events", value: config.events, inline: true },
    {
      name: "🔔 Smart Alerts",
      value: config.smartAlerts ? "✅ Bật" : "❌ Tắt",
      inline: true,
    },
    {
      name: "🚫 Ignored Roles",
      value: `${config.ignoredRoles?.length || 0} roles`,
      inline: true,
    },
    {
      name: "🚫 Ignored Users",
      value: `${config.ignoredUsers?.length || 0} users`,
      inline: true,
    },
    {
      name: "⏰ Cập nhật lần cuối",
      value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
      inline: true,
    },
  ]);

  const components = createSettingsComponents();
  await interaction.editReply({ embeds: [embed], components });
}

async function handleAlertsToggle(interaction, client) {
  await interaction.deferUpdate();

  const guildId = interaction.guild.id;

  try {
    const config = await client.db.getAuditLogConfig(guildId);
    if (!config) {
      const errorEmbed = createErrorEmbed(
        "Chưa thiết lập!",
        "Vui lòng thiết lập audit log trước."
      );
      return await interaction.editReply({
        embeds: [errorEmbed],
        components: [],
      });
    }

    // Toggle smart alerts
    const newState = !config.smartAlerts;
    await client.db.updateAuditLogConfig(guildId, { smartAlerts: newState });

    const embed = createSuccessEmbed(
      newState
        ? "✅ Smart Alerts đã được bật!"
        : "❌ Smart Alerts đã được tắt!",
      newState
        ? "Hệ thống sẽ gửi cảnh báo khi phát hiện hành vi bất thường."
        : "Hệ thống sẽ không gửi cảnh báo nữa."
    );

    // Go back to alerts settings
    setTimeout(async () => {
      await handleAlertsSettings(interaction, client);
    }, 2000);

    await interaction.editReply({ embeds: [embed], components: [] });
  } catch (error) {
    console.error("Error toggling alerts:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi!",
      "Không thể thay đổi cài đặt alerts."
    );
    await interaction.editReply({ embeds: [errorEmbed], components: [] });
  }
}

async function handleAlertsThresholds(interaction, client) {
  await interaction.deferUpdate();

  const embed = createInfoEmbed(
    "⚙️ Điều chỉnh ngưỡng cảnh báo",
    "Tính năng điều chỉnh ngưỡng đang được phát triển.\n\nHiện tại sử dụng ngưỡng mặc định:\n• Message Delete: 10/5 phút\n• Role Grant: 5/10 phút\n• Channel Create: 3/5 phút\n• Member Kick: 5/10 phút\n• Member Ban: 3/10 phút"
  );

  const backButton = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("auditlog_settings_back")
      .setLabel("🔙 Quay lại")
      .setStyle(ButtonStyle.Secondary)
  );

  await interaction.editReply({ embeds: [embed], components: [backButton] });
}

async function handleAlertsTest(interaction, client) {
  await interaction.deferUpdate();

  const guildId = interaction.guild.id;

  try {
    const config = await client.db.getAuditLogConfig(guildId);
    if (!config || !config.smartAlerts) {
      const errorEmbed = createErrorEmbed(
        "Smart Alerts chưa được bật!",
        "Vui lòng bật Smart Alerts trước khi test."
      );
      return await interaction.editReply({
        embeds: [errorEmbed],
        components: [],
      });
    }

    // Send test alert to audit log channel
    const auditChannel = await client.channels.fetch(config.channelId);
    if (auditChannel) {
      const testEmbed = createInfoEmbed(
        "🧪 Test Smart Alert",
        "Đây là một test alert từ hệ thống Smart Alerts."
      );

      testEmbed.setColor(0xf39c12);
      testEmbed.addFields([
        { name: "🎯 Loại cảnh báo", value: "TEST_ALERT", inline: true },
        { name: "📊 Mức độ", value: "LOW", inline: true },
        {
          name: "⏰ Thời gian",
          value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
          inline: true,
        },
        {
          name: "📝 Chi tiết",
          value: "Test alert được kích hoạt bởi " + interaction.user.tag,
          inline: false,
        },
      ]);

      await auditChannel.send({ embeds: [testEmbed] });
    }

    // Try to send DM to server owner
    try {
      const owner = await interaction.guild.fetchOwner();
      const dmEmbed = createInfoEmbed(
        "🚨 Smart Alert Test",
        `Test alert từ server **${interaction.guild.name}**`
      );

      dmEmbed.addFields([
        { name: "🎯 Loại", value: "TEST_ALERT", inline: true },
        { name: "📊 Mức độ", value: "LOW", inline: true },
        { name: "👤 Kích hoạt bởi", value: interaction.user.tag, inline: true },
      ]);

      await owner.send({ embeds: [dmEmbed] });
    } catch (dmError) {
      console.log("Could not send DM to owner");
    }

    const embed = createSuccessEmbed(
      "✅ Test Alert đã được gửi!",
      "Kiểm tra kênh audit log và DM của server owner."
    );

    setTimeout(async () => {
      await handleAlertsSettings(interaction, client);
    }, 3000);

    await interaction.editReply({ embeds: [embed], components: [] });
  } catch (error) {
    console.error("Error testing alerts:", error);
    const errorEmbed = createErrorEmbed("Lỗi!", "Không thể gửi test alert.");
    await interaction.editReply({ embeds: [errorEmbed], components: [] });
  }
}

async function handleIgnoreClear(interaction, client, clearType) {
  await interaction.deferUpdate();

  const guildId = interaction.guild.id;

  try {
    const config = await client.db.getAuditLogConfig(guildId);
    if (!config) {
      const errorEmbed = createErrorEmbed(
        "Chưa thiết lập!",
        "Vui lòng thiết lập audit log trước."
      );
      return await interaction.editReply({
        embeds: [errorEmbed],
        components: [],
      });
    }

    if (clearType === "roles") {
      await client.db.updateAuditLogConfig(guildId, { ignoredRoles: [] });
      const embed = createSuccessEmbed(
        "✅ Đã xóa tất cả ignored roles!",
        "Tất cả roles sẽ được ghi log từ bây giờ."
      );
      await interaction.editReply({ embeds: [embed], components: [] });
    } else if (clearType === "users") {
      await client.db.updateAuditLogConfig(guildId, { ignoredUsers: [] });
      const embed = createSuccessEmbed(
        "✅ Đã xóa tất cả ignored users!",
        "Tất cả users sẽ được ghi log từ bây giờ."
      );
      await interaction.editReply({ embeds: [embed], components: [] });
    }

    // Go back to ignore settings after 2 seconds
    setTimeout(async () => {
      await handleIgnoreSettings(interaction, client);
    }, 2000);
  } catch (error) {
    console.error("Error clearing ignore list:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi!",
      "Không thể xóa danh sách ignore."
    );
    await interaction.editReply({ embeds: [errorEmbed], components: [] });
  }
}

function createSettingsComponents() {
  const buttonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("auditlog_settings_channel")
      .setLabel("📍 Đổi kênh")
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId("auditlog_settings_events")
      .setLabel("📋 Chỉnh events")
      .setStyle(ButtonStyle.Primary),
    new ButtonBuilder()
      .setCustomId("auditlog_settings_alerts")
      .setLabel("🔔 Cảnh báo")
      .setStyle(ButtonStyle.Success),
    new ButtonBuilder()
      .setCustomId("auditlog_settings_ignore")
      .setLabel("🚫 Bỏ qua")
      .setStyle(ButtonStyle.Danger)
  );

  return [buttonRow];
}
