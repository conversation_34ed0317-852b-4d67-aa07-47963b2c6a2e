const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildRoleUpdate,
    async execute(oldRole, newRole, client) {
        try {
            // Only handle mentionable changes
            if (oldRole.mentionable === newRole.mentionable) return;
            
            console.log(`📢 Role mentionable updated: ${newRole.name} in ${newRole.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newRole.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'ROLE_MENTIONABLE_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'ROLE_MENTIONABLE_UPDATE',
                user: 'System',
                userId: null,
                action: 'Khả năng mention role được cập nhật',
                details: `<PERSON><PERSON><PERSON> năng mention của role **${newRole.name}** đã được ${newRole.mentionable ? 'bật' : 'tắt'}`,
                target: newRole.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the role mentionable from audit logs
            try {
                const auditLogs = await newRole.guild.fetchAuditLogs({
                    type: 31, // ROLE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newRole.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Khả năng mention role được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for role mentionable update');
            }
            
            // Add to database
            await client.db.addAuditLog(newRole.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📢 Khả năng mention role được cập nhật',
                `Vừa có khả năng mention role được thay đổi trong server`
            );
            
            embed.setColor(newRole.mentionable ? 0x2ecc71 : 0xe74c3c); // Green for enabled, red for disabled
            
            embed.addFields([
                {
                    name: '> Role',
                    value: `- ${newRole} (${newRole.name})`,
                    inline: false
                },
                {
                    name: '> ID role',
                    value: `- ${newRole.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Mentionable cũ',
                    value: `- ${oldRole.mentionable ? '✅ Có' : '❌ Không'}`,
                    inline: true
                },
                {
                    name: '> Mentionable mới',
                    value: `- ${newRole.mentionable ? '✅ Có' : '❌ Không'}`,
                    inline: true
                },
                {
                    name: '> Số thành viên có role',
                    value: `- ${newRole.members.size} thành viên`,
                    inline: true
                }
            ]);
            
            // Add role information
            embed.addFields([
                {
                    name: '> Màu role',
                    value: `- ${newRole.hexColor}`,
                    inline: true
                },
                {
                    name: '> Vị trí role',
                    value: `- ${newRole.position}`,
                    inline: true
                },
                {
                    name: '> Hiển thị riêng',
                    value: `- ${newRole.hoist ? 'Có' : 'Không'}`,
                    inline: true
                }
            ]);
            
            // Add impact explanation
            if (newRole.mentionable && !oldRole.mentionable) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Thành viên có thể mention role này bằng @role',
                        '• Tất cả thành viên có role sẽ nhận thông báo',
                        '• Có thể gây spam nếu bị lạm dụng',
                        '• Hữu ích cho thông báo quan trọng'
                    ].join('\n'),
                    inline: false
                });
                
                // Add warning if role has many members
                if (newRole.members.size > 50) {
                    embed.addFields({
                        name: '⚠️ Cảnh báo',
                        value: `- Role có ${newRole.members.size} thành viên, mention có thể gây nhiều thông báo`,
                        inline: false
                    });
                }
            } else if (!newRole.mentionable && oldRole.mentionable) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Thành viên không thể mention role này nữa',
                        '• Giảm nguy cơ spam thông báo',
                        '• Tăng tính bảo mật cho role',
                        '• Chỉ admin/mod có thể thông báo đến role'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add mention example
            if (newRole.mentionable) {
                embed.addFields({
                    name: '> 💡 Cách sử dụng',
                    value: `- Gõ \`@${newRole.name}\` để mention tất cả thành viên có role này`,
                    inline: false
                });
            }
            
            // Add security note
            embed.addFields({
                name: '> 🔒 Bảo mật',
                value: `- ${newRole.mentionable ? 'Cần giám sát việc sử dụng mention để tránh spam' : 'Role được bảo vệ khỏi mention spam'}`,
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set role color as embed color if it has one
            if (newRole.color !== 0) {
                embed.setColor(newRole.color);
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newRole.guild.name} • Role Mentionable Update`,
                iconURL: newRole.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Role mentionable update logged: ${newRole.name}`);
            
        } catch (error) {
            console.error('Error in roleMentionableUpdate audit log:', error);
        }
    }
};
