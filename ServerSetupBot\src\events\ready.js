const { Events, ActivityType } = require("discord.js");
const { loadAuditLogEvents } = require("./auditlog/auditLogLoader.js");

module.exports = {
  name: Events.ClientReady,
  once: true,
  async execute(client) {
    console.log(`🎉 Bot đã sẵn sàng! Đăng nhập với tên: ${client.user.tag}`);
    console.log(`📊 Đang phục vụ ${client.guilds.cache.size} máy chủ`);

    // Thiết lập trạng thái hoạt động
    const activities = [
      { name: "thiết lập máy chủ Discord", type: ActivityType.Playing },
      { name: "cấu hình server cho bạn", type: ActivityType.Playing },
      { name: "quản lý permissions", type: ActivityType.Playing },
      { name: "/help để xem hướng dẫn", type: ActivityType.Listening },
    ];

    let currentActivity = 0;

    // Đặt trạng thái ban đầu
    client.user.setActivity(activities[currentActivity].name, {
      type: activities[currentActivity].type,
    });

    // Thay đổi trạng thái mỗi 30 giây
    setInterval(() => {
      currentActivity = (currentActivity + 1) % activities.length;
      client.user.setActivity(activities[currentActivity].name, {
        type: activities[currentActivity].type,
      });
    }, 30000);

    // Thiết lập trạng thái online
    client.user.setStatus("online");

    // Load audit log events
    loadAuditLogEvents(client);

    console.log("✅ Bot đã hoàn tất khởi động và sẵn sàng hoạt động!");
  },
};
