/*
 _____  _             _______ _      _        _       
|  __ \| |           |__   __(_)    | |      | |      
| |__) | | _____  __    | |   _  ___| | _____| |_ ___ 
|  ___/| |/ _ \ \/ /    | |  | |/ __| |/ / _ \ __/ __|
| |    | |  __/>  <     | |  | | (__|   <  __/ |_\__ \
|_|    |_|\___/_/\_\    |_|  |_|\___|_|\_\___|\__|___/
                                        
Thank you for purchasing Plex Tickets!
If you find any issues, need support, or have a suggestion for the bot, please join our support server and create a ticket,

*/

const { SlashCommandBuilder } = require('@discordjs/builders');
const { Discord, EmbedBuilder } = require("discord.js");
const fs = require('fs');
const yaml = require("js-yaml")
const config = yaml.load(fs.readFileSync('./config.yml', 'utf8'))
const commands = yaml.load(fs.readFileSync('./commands.yml', 'utf8'))
const utils = require("../../utils.js");
const ticketModel = require("../../models/ticketModel");

module.exports = {
    enabled: commands.Ticket.Close.Enabled,
    data: new SlashCommandBuilder()
        .setName('close')
        .setDescription(commands.Ticket.Close.Description)
        .addStringOption(option => 
          option.setName('reason')
              .setDescription('Reason for closing the ticket')
              .setRequired(config.TicketSettings.TicketCloseReason)
      ),
    async execute(interaction, client) {
      await interaction.deferReply({ ephemeral: true });
    const ticketDB = await ticketModel.findOne({ channelID: interaction.channel.id });
    if(!ticketDB) return interaction.editReply({ content: config.Locale.NotInTicketChannel, ephemeral: true })

    let supportRole = await utils.checkIfUserHasSupportRoles(interaction)

    if (config.TicketSettings.RestrictTicketClose && !supportRole) {
      return interaction.editReply({ content: config.Locale.restrictTicketClose, ephemeral: true });
    }

    let closeReason = interaction.options.getString('reason') || "No reason provided.";

    await ticketModel.updateOne(
      { channelID: interaction.channel.id },
      { 
          $set: {
              closeUserID: interaction.user.id, 
              closedAt: Date.now(),
              closeReason: closeReason
          }
      }
  );

    await client.emit('ticketClose', interaction);

    }

}