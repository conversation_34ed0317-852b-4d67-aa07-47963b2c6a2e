const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.MessageDelete,
    async execute(message, client) {
        try {
            // Only handle deleted messages that had polls
            if (!message.poll || message.author?.bot) return;
            
            console.log(`🗑️ Poll deleted: ${message.poll.question?.text || 'Unknown'} in ${message.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(message.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'POLLS_DELETE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'POLLS_DELETE',
                user: 'System',
                userId: null,
                action: 'Poll được xóa',
                details: `Poll đã được xóa khỏi server`,
                target: message.poll.question?.text || 'Unknown',
                channel: message.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the poll from audit logs
            try {
                const auditLogs = await message.guild.fetchAuditLogs({
                    type: 72, // MESSAGE_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === message.author?.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Poll được xóa bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for poll delete');
            }
            
            // Add to database
            await client.db.addAuditLog(message.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Poll được xóa',
                `Vừa có một poll được xóa khỏi server`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            embed.addFields([
                {
                    name: '> ID tin nhắn',
                    value: `- ${message.id}`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${message.channel || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian xóa',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add original poll creator info
            if (message.author) {
                embed.addFields([
                    {
                        name: '> Người tạo poll',
                        value: `- ${message.author.tag}`,
                        inline: true
                    },
                    {
                        name: '> ID người tạo',
                        value: `- ${message.author.id}`,
                        inline: true
                    }
                ]);
            }
            
            // Add poll question if available
            if (message.poll.question?.text) {
                embed.addFields({
                    name: '> Câu hỏi poll đã xóa',
                    value: `- ${message.poll.question.text}`,
                    inline: false
                });
            }
            
            // Add poll answers if available
            if (message.poll.answers && message.poll.answers.length > 0) {
                const answersList = message.poll.answers.map((answer, index) => {
                    const emoji = answer.emoji ? `${answer.emoji} ` : `${index + 1}. `;
                    return `${emoji}${answer.text || 'No text'}`;
                }).join('\n');
                
                embed.addFields([
                    {
                        name: '> Số lựa chọn đã có',
                        value: `- ${message.poll.answers.length} lựa chọn`,
                        inline: true
                    },
                    {
                        name: '> Danh sách lựa chọn đã có',
                        value: answersList.length > 1000 ? answersList.substring(0, 1000) + '...' : answersList,
                        inline: false
                    }
                ]);
            }
            
            // Add poll results if available
            if (message.poll.answers) {
                const totalVotes = message.poll.answers.reduce((total, answer) => total + (answer.voteCount || 0), 0);
                if (totalVotes > 0) {
                    embed.addFields({
                        name: '> Tổng số votes đã có',
                        value: `- ${totalVotes} votes`,
                        inline: true
                    });
                    
                    // Show vote distribution
                    const voteResults = message.poll.answers.map((answer, index) => {
                        const emoji = answer.emoji ? `${answer.emoji} ` : `${index + 1}. `;
                        const percentage = totalVotes > 0 ? Math.round((answer.voteCount || 0) / totalVotes * 100) : 0;
                        return `${emoji}${answer.voteCount || 0} votes (${percentage}%)`;
                    }).join('\n');
                    
                    embed.addFields({
                        name: '> Kết quả votes cuối cùng',
                        value: voteResults,
                        inline: false
                    });
                }
            }
            
            // Add poll settings that were configured
            if (message.poll.allowMultiselect !== undefined) {
                embed.addFields({
                    name: '> Đã cho phép chọn nhiều',
                    value: `- ${message.poll.allowMultiselect ? 'Có' : 'Không'}`,
                    inline: true
                });
            }
            
            // Add poll duration info if available
            if (message.poll.expiresTimestamp) {
                const wasExpired = Date.now() > message.poll.expiresTimestamp;
                embed.addFields([
                    {
                        name: '> Thời gian kết thúc đã thiết lập',
                        value: `- <t:${Math.floor(message.poll.expiresTimestamp / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Trạng thái khi xóa',
                        value: `- ${wasExpired ? '⏰ Đã hết hạn' : '🔄 Đang hoạt động'}`,
                        inline: true
                    }
                ]);
            }
            
            // Add creation time if available
            if (message.createdTimestamp) {
                const pollLifetime = Date.now() - message.createdTimestamp;
                const lifetimeHours = Math.floor(pollLifetime / (1000 * 60 * 60));
                const lifetimeMinutes = Math.floor((pollLifetime % (1000 * 60 * 60)) / (1000 * 60));
                
                embed.addFields([
                    {
                        name: '> Được tạo lúc',
                        value: `- <t:${Math.floor(message.createdTimestamp / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Thời gian tồn tại',
                        value: `- ${lifetimeHours} giờ, ${lifetimeMinutes} phút`,
                        inline: true
                    }
                ]);
            }
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do xóa',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Add impact note
            embed.addFields({
                name: '> 📝 Tác động',
                value: `- Tất cả dữ liệu votes của poll này đã bị mất`,
                inline: false
            });
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${message.guild.name} • Poll Delete`,
                iconURL: message.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Poll delete logged: ${message.poll.question?.text || 'Unknown'}`);
            
        } catch (error) {
            console.error('Error in pollsDelete audit log:', error);
        }
    }
};
