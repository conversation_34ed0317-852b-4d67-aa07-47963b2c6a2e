const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'voiceUserKick', // Custom event for when user is kicked from voice by moderator
    async execute(member, channel, executor, client) {
        try {
            console.log(`👢 User kicked from voice: ${member.user.tag} in ${member.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(member.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'VOICE_USER_KICK')) return;
            
            // Create event data
            const eventData = {
                eventType: 'VOICE_USER_KICK',
                user: executor?.tag || 'System',
                userId: executor?.id || null,
                action: 'Thành viên bị kick khỏi voice',
                details: `**${member.user.tag}** đã bị kick khỏi kênh thoại **${channel.name}**`,
                target: member.user.tag,
                channel: channel.name,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(member.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '👢 Thành viên bị kick khỏi voice',
                `Vừa có thành viên bị kick khỏi kênh thoại bởi moderator`
            );
            
            embed.setColor(0xe67e22); // Orange for kick
            
            embed.addFields([
                {
                    name: '> Thành viên bị kick',
                    value: `- ${member.user.tag} (${member.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${member.user.id}`,
                    inline: true
                },
                {
                    name: '> Người thực hiện kick',
                    value: `- ${executor ? `${executor.tag} (${executor})` : 'System'}`,
                    inline: true
                },
                {
                    name: '> Thời gian kick',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Kênh thoại',
                    value: `- ${channel.name}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${channel.id}`,
                    inline: true
                },
                {
                    name: '> Số người còn lại',
                    value: `- ${channel.members.size} thành viên`,
                    inline: true
                }
            ]);
            
            // Add voice channel type info
            const channelTypes = {
                2: 'Kênh thoại',
                13: 'Kênh sân khấu'
            };
            
            embed.addFields({
                name: '> Loại kênh',
                value: `- ${channelTypes[channel.type] || 'Không xác định'}`,
                inline: true
            });
            
            // Add moderator action details
            embed.addFields([
                {
                    name: '> Loại hành động',
                    value: `- Kick khỏi voice bởi moderator`,
                    inline: true
                },
                {
                    name: '> Có thể tham gia lại',
                    value: `- Có (trừ khi bị cấm)`,
                    inline: true
                }
            ]);
            
            // Add account info
            const accountAge = Date.now() - member.user.createdTimestamp;
            const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
            
            embed.addFields([
                {
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                }
            ]);
            
            // Set user avatar as thumbnail
            if (member.user.displayAvatarURL()) {
                embed.setThumbnail(member.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${member.guild.name} • Voice User Kick`,
                iconURL: member.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Voice user kick logged: ${member.user.tag}`);
            
            // Smart alert for voice kick
            if (config.smartAlerts) {
                try {
                    const owner = await member.guild.fetchOwner();
                    const alertEmbed = createInfoEmbed(
                        '🚨 Smart Alert: Voice Kick',
                        `Thành viên **${member.user.tag}** đã bị kick khỏi voice trong **${member.guild.name}**`
                    );
                    
                    alertEmbed.setColor(0xe67e22);
                    alertEmbed.addFields([
                        { name: '🎯 Loại', value: 'VOICE_KICK', inline: true },
                        { name: '📊 Mức độ', value: 'LOW', inline: true },
                        { name: '👤 Thực hiện bởi', value: executor?.tag || 'System', inline: true },
                        { name: '👢 Người bị kick', value: member.user.tag, inline: true },
                        { name: '🔊 Kênh', value: channel.name, inline: true }
                    ]);
                    
                    await owner.send({ embeds: [alertEmbed] });
                } catch (dmError) {
                    console.log('Could not send voice kick alert to owner');
                }
            }
            
        } catch (error) {
            console.error('Error in voiceUserKick audit log:', error);
        }
    }
};
