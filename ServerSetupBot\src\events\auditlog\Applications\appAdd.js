const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ApplicationCommandPermissionsUpdate,
    async execute(data, client) {
        try {
            console.log(`📱 Application added in ${data.guild?.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(data.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'APP_ADD')) return;
            
            // Create event data
            const eventData = {
                eventType: 'APP_ADD',
                user: 'System',
                userId: null,
                action: 'Ứng dụng được thêm',
                details: `Ứng dụng **${data.applicationId}** đã được thêm vào server`,
                target: data.applicationId,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who added the app from audit logs
            try {
                const auditLogs = await data.guild.fetchAuditLogs({
                    type: 100, // APPLICATION_COMMAND_PERMISSION_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Ứng dụng được thêm bởi ${auditEntry.executor.tag}`;
                }
            } catch (error) {
                console.log('Could not fetch audit logs for app add');
            }
            
            // Add to database
            await client.db.addAuditLog(data.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📱 Ứng dụng được thêm',
                `Vừa có một ứng dụng được thêm vào server`
            );
            
            embed.setColor(0x2ecc71); // Green for add
            
            embed.addFields([
                {
                    name: '> ID ứng dụng',
                    value: `- ${data.applicationId}`,
                    inline: true
                },
                {
                    name: '> Người thêm',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${data.guild.name} • Application Add`,
                iconURL: data.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Application add logged: ${data.applicationId}`);
            
        } catch (error) {
            console.error('Error in appAdd audit log:', error);
        }
    }
};
