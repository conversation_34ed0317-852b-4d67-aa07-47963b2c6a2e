const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'ping',
    description: 'Replies with bot and database ping!',
    category: 'info',
    premium: true,
    run: async (message, args, client) => {
        let botPing = Date.now() - message.createdTimestamp;
        let wsPing = client.ws.ping;

        // Simulated Database Ping (Replace with real DB latency if needed)
        let databasePing = Math.random() * (30 - 10) + 10;

        let responseSpeed = '';
        if (wsPing <= 20) responseSpeed = 'Very fast!';
        else if (wsPing <= 30) responseSpeed = 'Fast!';
        else if (wsPing <= 50) responseSpeed = 'Good!';
        else if (wsPing <= 70) responseSpeed = 'Moderate!';
        else if (wsPing <= 100) responseSpeed = 'Slow!';
        else responseSpeed = 'Very Slow!';

        const embed = new EmbedBuilder()
            .setColor(client.color || '#ff0000') // Use default bot color if available
            .setAuthor({ 
                name: `${wsPing}ms Pong!\n${databasePing.toFixed(2)}ms Database Ping!`, 
                iconURL: message.author.displayAvatarURL({ dynamic: true }) 
            })
            .setFooter({ text: `Response Speed: ${responseSpeed}`, iconURL: client.user.displayAvatarURL() });

        return message.channel.send({ embeds: [embed] });
    }
};