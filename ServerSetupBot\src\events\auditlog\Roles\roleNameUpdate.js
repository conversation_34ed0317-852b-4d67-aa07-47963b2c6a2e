const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildRoleUpdate,
    async execute(oldRole, newRole, client) {
        try {
            // Only handle name changes
            if (oldRole.name === newRole.name) return;
            
            console.log(`✏️ Role name updated: ${oldRole.name} -> ${newRole.name} in ${newRole.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newRole.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'ROLE_NAME_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'ROLE_NAME_UPDATE',
                user: 'System',
                userId: null,
                action: 'Tên role được thay đổi',
                details: `Tên role đã được thay đổi từ **${oldRole.name}** thành **${newRole.name}**`,
                target: newRole.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the role name from audit logs
            try {
                const auditLogs = await newRole.guild.fetchAuditLogs({
                    type: 31, // ROLE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newRole.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Tên role được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for role name update');
            }
            
            // Add to database
            await client.db.addAuditLog(newRole.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Tên role thay đổi',
                `Vừa có tên role được thay đổi trong server`
            );
            
            embed.setColor(newRole.color || 0xf39c12); // Use role color or orange
            
            embed.addFields([
                {
                    name: '> ID role',
                    value: `- ${newRole.id}`,
                    inline: true
                },
                {
                    name: '> Role mention',
                    value: `- ${newRole}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Tên cũ',
                    value: `- ${oldRole.name}`,
                    inline: true
                },
                {
                    name: '> Tên mới',
                    value: `- ${newRole.name}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Vị trí role',
                    value: `- ${newRole.position}`,
                    inline: true
                },
                {
                    name: '> Số thành viên có role',
                    value: `- ${newRole.members.size} thành viên`,
                    inline: true
                },
                {
                    name: '> Màu role',
                    value: `- ${newRole.hexColor}`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newRole.guild.name} • Role Name Update`,
                iconURL: newRole.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Role name update logged: ${oldRole.name} -> ${newRole.name}`);
            
        } catch (error) {
            console.error('Error in roleNameUpdate audit log:', error);
        }
    }
};
