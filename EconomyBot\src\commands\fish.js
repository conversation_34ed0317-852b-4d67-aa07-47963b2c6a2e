const { SlashCommandBuilder } = require('discord.js');
const User = require('../models/User');
const { createSuccessEmbed, createErrorEmbed, formatCurrency } = require('../utils/embedBuilder');
const config = require('../config/config');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('fish')
        .setNameLocalizations({
            'vi': 'cauca'
        })
        .setDescription('Đi câu cá để kiếm tiền và có thể bắt được cá hiếm'),
    
    cooldown: 1800, // 30 phút

    async execute(interaction) {
        try {
            const userData = await User.findOrCreate(interaction.user);
            
            // Kiểm tra cooldown
            if (userData.lastFish) {
                const lastFish = new Date(userData.lastFish);
                const now = new Date();
                const timeDiff = now - lastFish;
                const cooldownTime = 1800000; // 30 phút
                
                if (timeDiff < cooldownTime) {
                    const timeLeft = Math.ceil((cooldownTime - timeDiff) / 1000 / 60);
                    const errorEmbed = createErrorEmbed(
                        'Đang nghỉ ngơi!',
                        `Bạn cần đợi **${timeLeft} phút** nữa trước khi có thể câu cá lại.\n\n` +
                        `${config.emojis.info} **Mẹo:** Hãy chuẩn bị cần câu và mồi nhử!`
                    );
                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
            }

            // Các loại cá với tỷ lệ và giá trị khác nhau
            const fishTypes = [
                // Cá thường (70%)
                { name: 'Cá rô', emoji: '🐟', rarity: 'common', chance: 25, value: { min: 50, max: 150 }, exp: 10 },
                { name: 'Cá chép', emoji: '🐠', rarity: 'common', chance: 20, value: { min: 80, max: 200 }, exp: 15 },
                { name: 'Cá trê', emoji: '🐡', rarity: 'common', chance: 15, value: { min: 100, max: 250 }, exp: 20 },
                { name: 'Cá lóc', emoji: '🦈', rarity: 'common', chance: 10, value: { min: 150, max: 300 }, exp: 25 },
                
                // Cá hiếm (25%)
                { name: 'Cá hồi', emoji: '🍣', rarity: 'uncommon', chance: 8, value: { min: 300, max: 600 }, exp: 40 },
                { name: 'Cá ngừ', emoji: '🐟', rarity: 'uncommon', chance: 7, value: { min: 400, max: 800 }, exp: 50 },
                { name: 'Cá mập', emoji: '🦈', rarity: 'rare', chance: 5, value: { min: 800, max: 1500 }, exp: 80 },
                
                // Cá siêu hiếm (4%)
                { name: 'Cá vàng', emoji: '🐠', rarity: 'epic', chance: 2, value: { min: 1500, max: 3000 }, exp: 120 },
                { name: 'Cá rồng', emoji: '🐉', rarity: 'legendary', chance: 1, value: { min: 3000, max: 6000 }, exp: 200 },
                
                // Vật phẩm đặc biệt (1%)
                { name: 'Rác thải', emoji: '🗑️', rarity: 'trash', chance: 0.5, value: { min: 1, max: 10 }, exp: 5 },
                { name: 'Kho báu', emoji: '💎', rarity: 'treasure', chance: 0.5, value: { min: 5000, max: 10000 }, exp: 300 }
            ];

            // Random để xác định loại cá bắt được
            const random = Math.random() * 100;
            let cumulativeChance = 0;
            let caughtFish = null;

            for (const fish of fishTypes) {
                cumulativeChance += fish.chance;
                if (random <= cumulativeChance) {
                    caughtFish = fish;
                    break;
                }
            }

            // Nếu không bắt được gì (xác suất thất bại)
            if (!caughtFish) {
                const db = require('../database/database');
                await db.run(
                    'UPDATE users SET last_fish = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [new Date().toISOString(), userData.id]
                );

                const failEmbed = createErrorEmbed(
                    'Không có cá cắn câu!',
                    `${config.emojis.warning} Lần này bạn không bắt được gì cả.\n\n` +
                    `💡 **Mẹo:** Thử lại sau 30 phút, có thể sẽ may mắn hơn!`
                );

                return await interaction.reply({ embeds: [failEmbed] });
            }

            // Tính giá trị cá bắt được
            const fishValue = Math.floor(
                Math.random() * (caughtFish.value.max - caughtFish.value.min + 1) + 
                caughtFish.value.min
            );

            // Bonus dựa trên level
            const levelBonus = Math.floor(fishValue * (userData.level * 0.05));
            const totalValue = fishValue + levelBonus;

            // Cập nhật thời gian fishing
            const db = require('../database/database');
            await db.run(
                'UPDATE users SET last_fish = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [new Date().toISOString(), userData.id]
            );

            // Cập nhật số dư và kinh nghiệm
            await userData.updateBalance(totalValue, 'fishing');
            const levelResult = await userData.addExperience(caughtFish.exp);

            // Tạo embed kết quả
            let description = `${caughtFish.emoji} **${caughtFish.name}** (${getRarityText(caughtFish.rarity)})\n\n` +
                `💰 **Giá trị:** ${formatCurrency(totalValue)}\n` +
                `${config.emojis.star} **Kinh nghiệm:** +${caughtFish.exp} EXP\n`;

            if (levelBonus > 0) {
                description += `🎯 **Level bonus:** ${formatCurrency(levelBonus)}\n`;
            }

            description += `\n${config.emojis.money} **Số dư hiện tại:** ${formatCurrency(userData.balance + totalValue)}`;

            if (levelResult.leveledUp) {
                description += `\n\n🎉 **LEVEL UP!** Bạn đã lên level ${levelResult.newLevel}!\n` +
                              `💰 **Thưởng level:** ${formatCurrency(levelResult.reward)}`;
            }

            const successEmbed = createSuccessEmbed(
                'Câu cá thành công!',
                description
            );

            // Thêm thông tin đặc biệt cho cá hiếm
            if (caughtFish.rarity === 'legendary') {
                successEmbed.addFields({
                    name: `${config.emojis.crown} Cá huyền thoại!`,
                    value: 'Bạn đã bắt được một con cá cực kỳ hiếm! Chúc mừng!',
                    inline: false
                });
            } else if (caughtFish.rarity === 'epic') {
                successEmbed.addFields({
                    name: `${config.emojis.gem} Cá quý hiếm!`,
                    value: 'Đây là một con cá rất có giá trị!',
                    inline: false
                });
            } else if (caughtFish.rarity === 'treasure') {
                successEmbed.addFields({
                    name: `${config.emojis.star} Kho báu!`,
                    value: 'Bạn đã tìm thấy kho báu dưới nước!',
                    inline: false
                });
            }

            // Thêm thống kê
            successEmbed.addFields({
                name: `${config.emojis.info} Thông tin`,
                value: `**Độ hiếm:** ${getRarityText(caughtFish.rarity)}\n` +
                       `**Tỷ lệ bắt được:** ${caughtFish.chance}%\n` +
                       `**Có thể câu lại:** <t:${Math.floor((Date.now() + 1800000) / 1000)}:R>`,
                inline: false
            });

            await interaction.reply({ embeds: [successEmbed] });

            // Kiểm tra achievements
            const newAchievements = await userData.checkAchievements();
            if (newAchievements.length > 0) {
                // Có thể thêm thông báo achievement sau
            }
            
        } catch (error) {
            console.error('Lỗi lệnh fish:', error);
            
            const errorEmbed = createErrorEmbed(
                'Lỗi hệ thống',
                'Không thể đi câu cá. Có thể cần câu bị hỏng!'
            );
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};

function getRarityText(rarity) {
    const rarityMap = {
        'common': '🟢 Thường',
        'uncommon': '🔵 Không thường',
        'rare': '🟣 Hiếm',
        'epic': '🟠 Sử thi',
        'legendary': '🟡 Huyền thoại',
        'trash': '⚫ Rác thải',
        'treasure': '🟨 Kho báu'
    };
    return rarityMap[rarity] || rarity;
}
