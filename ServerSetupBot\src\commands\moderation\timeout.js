const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createWarningEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog, formatDuration } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('timeout')
        .setDescription('Timeout thành viên')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Thành viên cần timeout')
                .setRequired(true)
        )
        .addIntegerOption(option =>
            option.setName('duration')
                .setDescription('Thời gian timeout (phút)')
                .setRequired(true)
                .setMinValue(1)
                .setMaxValue(40320) // 28 days
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Lý do timeout')
                .setRequired(false)
        )
        .addBooleanOption(option =>
            option.setName('silent')
                .setDescription('Không gửi DM cho người bị timeout')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.ModerateMembers)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                'Bạn cần quyền `Moderate Members` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ModerateMembers)) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                'Bot cần quyền `Moderate Members` để thực hiện lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const user = interaction.options.getUser('user');
        const duration = interaction.options.getInteger('duration');
        const reason = interaction.options.getString('reason') || 'Không có lý do';
        const silent = interaction.options.getBoolean('silent') || false;
        const guildId = interaction.guild.id;
        
        try {
            const member = await interaction.guild.members.fetch(user.id);
            
            // Kiểm tra hierarchy
            if (member.roles.highest.position >= interaction.member.roles.highest.position) {
                const errorEmbed = createErrorEmbed(
                    'Không thể timeout!',
                    'Bạn không thể timeout thành viên có role cao hơn hoặc bằng bạn.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            if (member.roles.highest.position >= interaction.guild.members.me.roles.highest.position) {
                const errorEmbed = createErrorEmbed(
                    'Không thể timeout!',
                    'Bot không thể timeout thành viên có role cao hơn hoặc bằng bot.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            if (!member.moderatable) {
                const errorEmbed = createErrorEmbed(
                    'Không thể timeout!',
                    'Thành viên này không thể bị timeout.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Kiểm tra nếu đã bị timeout
            if (member.communicationDisabledUntil && member.communicationDisabledUntil > Date.now()) {
                const errorEmbed = createErrorEmbed(
                    'Thành viên đã bị timeout!',
                    `Thành viên này đã bị timeout đến <t:${Math.floor(member.communicationDisabledUntil / 1000)}:F>`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Gửi DM trước khi timeout
            if (!silent) {
                const dmEmbed = createWarningEmbed(
                    `Bạn đã bị timeout trong ${interaction.guild.name}`,
                    `**Lý do:** ${reason}\n**Thời gian:** ${formatDuration(duration)}\n**Bởi:** ${interaction.user.tag}\n**Hết hạn:** <t:${Math.floor((Date.now() + duration * 60000) / 1000)}:F>`
                );
                
                try {
                    await user.send({ embeds: [dmEmbed] });
                } catch (error) {
                    console.log('Không thể gửi DM cho user bị timeout');
                }
            }
            
            // Thực hiện timeout
            await member.timeout(duration * 60000, `${reason} | Bởi: ${interaction.user.tag}`);
            
            // Log vào database
            await client.db.addModerationLog(guildId, {
                type: 'timeout',
                userId: user.id,
                moderatorId: interaction.user.id,
                reason: reason,
                duration: duration,
                expiresAt: new Date(Date.now() + duration * 60000).toISOString()
            });
            
            const successEmbed = createSuccessEmbed(
                '⏰ Thành viên đã bị timeout!',
                `**Thành viên:** ${user.tag} (${user.id})\n` +
                `**Lý do:** ${reason}\n` +
                `**Thời gian:** ${formatDuration(duration)}\n` +
                `**Hết hạn:** <t:${Math.floor((Date.now() + duration * 60000) / 1000)}:F>\n` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            await interaction.reply({ embeds: [successEmbed] });
            
            // Gửi log vào channel
            await sendModerationLog(client, guildId, {
                type: 'timeout',
                user: user,
                moderator: interaction.user,
                reason: reason,
                duration: formatDuration(duration)
            });
            
        } catch (error) {
            console.error('Lỗi khi timeout user:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi timeout thành viên!',
                'Đã xảy ra lỗi khi timeout thành viên. Vui lòng thử lại sau!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
