(function(_0x4c217e,_0x462f39){const _0x2440d2=_0x4c217e();function _0x193828(_0x395548,_0x5ebfda){return _0x444b(_0x395548-0x142,_0x5ebfda);}while(!![]){try{const _0xe22e62=-parseInt(_0x193828(0x226,0x239))/0x1*(parseInt(_0x193828(0x23b,0x236))/0x2)+-parseInt(_0x193828(0x215,0x22c))/0x3*(-parseInt(_0x193828(0x20f,0x22d))/0x4)+-parseInt(_0x193828(0x231,0x226))/0x5+-parseInt(_0x193828(0x21b,0x231))/0x6*(-parseInt(_0x193828(0x211,0x200))/0x7)+-parseInt(_0x193828(0x223,0x233))/0x8+parseInt(_0x193828(0x239,0x221))/0x9*(-parseInt(_0x193828(0x218,0x1fc))/0xa)+parseInt(_0x193828(0x235,0x247))/0xb;if(_0xe22e62===_0x462f39)break;else _0x2440d2['push'](_0x2440d2['shift']());}catch(_0x2d0af6){_0x2440d2['push'](_0x2440d2['shift']());}}}(_0x33ec,0xc485f));function _0x444b(_0x59c07d,_0x37b872){const _0x33ecff=_0x33ec();return _0x444b=function(_0x444b18,_0x108f05){_0x444b18=_0x444b18-0xc1;let _0x6c91a6=_0x33ecff[_0x444b18];return _0x6c91a6;},_0x444b(_0x59c07d,_0x37b872);}const {SlashCommandBuilder,EmbedBuilder}=require(_0x3016cd(0x370,0x357)),User=require('../../../models/UserData');function _0x3016cd(_0x59636c,_0x17e873){return _0x444b(_0x17e873-0x27d,_0x59636c);}const fs=require('fs'),yaml=require('js-yaml'),config=yaml[_0x3016cd(0x34a,0x34b)](fs['readFileSync']('./config.yml',_0x3016cd(0x343,0x358))),lang=yaml['load'](fs[_0x3016cd(0x362,0x365)](_0x3016cd(0x377,0x35d),_0x3016cd(0x369,0x358))),parseDuration=require(_0x3016cd(0x36c,0x366)),{checkActiveBooster,replacePlaceholders}=require(_0x3016cd(0x369,0x369));module['exports']={'data':new SlashCommandBuilder()['setName']('rob')[_0x3016cd(0x343,0x33e)](_0x3016cd(0x364,0x375))[_0x3016cd(0x369,0x360)](_0x4740b5=>_0x4740b5['setName']('target')['setDescription']('User\x20to\x20rob')[_0x3016cd(0x382,0x371)](!![])),'category':'Economy',async 'execute'(_0x5013ed){const _0x4f5bf5=_0x5013ed[_0x1c73b1(-0x199,-0x1b7)][_0x1c73b1(-0x1a7,-0x1c5)](_0x1c73b1(-0x182,-0x19b));let _0x9b35ba=await User['findOne']({'userId':_0x5013ed[_0x1c73b1(-0x19c,-0x1b8)]['id'],'guildId':_0x5013ed[_0x1c73b1(-0x174,-0x18d)]['id']},{'balance':0x1,'commandData.lastRob':0x1,'transactionLogs':0x1,'boosters':0x1});const _0x2b8923=new Date(),_0x482283=parseDuration(config[_0x1c73b1(-0x18e,-0x1a0)][_0x1c73b1(-0x1a0,-0x1b8)][_0x1c73b1(-0x1a9,-0x18f)]);function _0x1c73b1(_0x4f6ced,_0x4b6342){return _0x3016cd(_0x4b6342,_0x4f6ced- -0x4ed);}if(_0x9b35ba&&_0x9b35ba[_0x1c73b1(-0x17b,-0x194)][_0x1c73b1(-0x1aa,-0x1b0)]){const _0x2349c3=new Date(_0x9b35ba[_0x1c73b1(-0x17b,-0x190)][_0x1c73b1(-0x1aa,-0x1c4)][_0x1c73b1(-0x19b,-0x183)]()+_0x482283);if(_0x2b8923<_0x2349c3){const _0x599e7=new EmbedBuilder()[_0x1c73b1(-0x1af,-0x1ad)](replacePlaceholders(lang['Economy'][_0x1c73b1(-0x1ac,-0x197)][_0x1c73b1(-0x1a9,-0x1b4)],{'nextUse':Math[_0x1c73b1(-0x191,-0x19f)](_0x2349c3[_0x1c73b1(-0x19b,-0x189)]()/0x3e8)}))[_0x1c73b1(-0x180,-0x178)](_0x1c73b1(-0x18b,-0x171));return _0x5013ed[_0x1c73b1(-0x185,-0x16d)]({'embeds':[_0x599e7]});}}if(_0x4f5bf5['id']===_0x5013ed['user']['id']){const _0x47ebc6=new EmbedBuilder()[_0x1c73b1(-0x1af,-0x1c7)](_0x1c73b1(-0x1a4,-0x1b5))[_0x1c73b1(-0x180,-0x175)](_0x1c73b1(-0x18b,-0x179));return _0x5013ed[_0x1c73b1(-0x185,-0x186)]({'embeds':[_0x47ebc6]});}const _0x346b40=await User[_0x1c73b1(-0x1a8,-0x1b2)]({'userId':_0x4f5bf5['id'],'guildId':_0x5013ed[_0x1c73b1(-0x174,-0x173)]['id']},{'balance':0x1,'transactionLogs':0x1});if(!_0x346b40||_0x346b40[_0x1c73b1(-0x17f,-0x17b)]<config[_0x1c73b1(-0x18e,-0x177)][_0x1c73b1(-0x1a0,-0x1a4)]['minBalanceToRob']){const _0x1422a7=new EmbedBuilder()['setDescription'](lang[_0x1c73b1(-0x18e,-0x183)]['Messages'][_0x1c73b1(-0x198,-0x1b2)])[_0x1c73b1(-0x180,-0x189)](_0x1c73b1(-0x18b,-0x172));return _0x5013ed['reply']({'embeds':[_0x1422a7]});}const _0x311eb7=Math['random']()>0.5;let _0x1748ac=Math[_0x1c73b1(-0x194,-0x18c)](_0x346b40[_0x1c73b1(-0x17f,-0x197)],Math[_0x1c73b1(-0x191,-0x19d)](_0x346b40[_0x1c73b1(-0x17f,-0x187)]*parseFloat(config[_0x1c73b1(-0x18e,-0x1a3)][_0x1c73b1(-0x1a0,-0x1b0)][_0x1c73b1(-0x189,-0x19d)])/0x64),parseInt(config[_0x1c73b1(-0x18e,-0x1ab)][_0x1c73b1(-0x1a0,-0x184)]['maxAmount'],0xa));const _0x34e593={'user':'<@'+_0x5013ed['user']['id']+'>','victim':'<@'+_0x4f5bf5['id']+'>','balance':_0x1748ac},_0xdee0fc=replacePlaceholders(lang[_0x1c73b1(-0x18e,-0x170)][_0x1c73b1(-0x17e,-0x179)][_0x1c73b1(-0x1a0,-0x18a)][_0x1c73b1(-0x1a6,-0x1ae)],{'result':_0x311eb7?lang[_0x1c73b1(-0x18e,-0x19b)][_0x1c73b1(-0x1ac,-0x1ca)][_0x1c73b1(-0x1ae,-0x19b)]:lang[_0x1c73b1(-0x18e,-0x18a)][_0x1c73b1(-0x1ac,-0x1a6)][_0x1c73b1(-0x19e,-0x1b5)]});if(_0x311eb7){const _0x30029d=checkActiveBooster(_0x9b35ba,_0x1c73b1(-0x192,-0x19b));_0x1748ac*=_0x30029d,_0x346b40['balance']-=_0x1748ac,_0x9b35ba[_0x1c73b1(-0x17f,-0x16d)]+=_0x1748ac,_0x9b35ba[_0x1c73b1(-0x17b,-0x16a)]['lastRob']=_0x2b8923,_0x9b35ba[_0x1c73b1(-0x176,-0x191)][_0x1c73b1(-0x1a5,-0x18e)]({'type':_0x1c73b1(-0x193,-0x18d),'amount':_0x1748ac,'timestamp':_0x2b8923}),_0x346b40['transactionLogs'][_0x1c73b1(-0x1a5,-0x1a5)]({'type':_0x1c73b1(-0x19f,-0x188),'amount':-_0x1748ac,'timestamp':_0x2b8923}),await _0x346b40[_0x1c73b1(-0x175,-0x182)](),await _0x9b35ba[_0x1c73b1(-0x175,-0x177)]();const _0x2f3da9=replacePlaceholders(lang['Economy'][_0x1c73b1(-0x17e,-0x181)][_0x1c73b1(-0x1a0,-0x18d)][_0x1c73b1(-0x1ab,-0x1c5)][Math[_0x1c73b1(-0x191,-0x199)](Math['random']()*lang[_0x1c73b1(-0x18e,-0x177)][_0x1c73b1(-0x17e,-0x187)][_0x1c73b1(-0x1a0,-0x1a7)][_0x1c73b1(-0x1ab,-0x194)][_0x1c73b1(-0x186,-0x198)])],_0x34e593),_0x5c7804=new EmbedBuilder()[_0x1c73b1(-0x1ad,-0x1b3)](_0xdee0fc)[_0x1c73b1(-0x1af,-0x1c2)](_0x2f3da9)[_0x1c73b1(-0x183,-0x18a)]({'text':replacePlaceholders(lang[_0x1c73b1(-0x18e,-0x1a2)][_0x1c73b1(-0x1ac,-0x1c2)][_0x1c73b1(-0x17a,-0x16d)],{'balance':_0x9b35ba[_0x1c73b1(-0x17f,-0x166)]})})[_0x1c73b1(-0x180,-0x166)]('#00FF00');return _0x5013ed[_0x1c73b1(-0x185,-0x182)]({'embeds':[_0x5c7804]});}else{_0x9b35ba[_0x1c73b1(-0x17b,-0x171)][_0x1c73b1(-0x1aa,-0x1a0)]=_0x2b8923,_0x9b35ba[_0x1c73b1(-0x176,-0x174)][_0x1c73b1(-0x1a5,-0x191)]({'type':'rob_fail','amount':0x0,'timestamp':_0x2b8923}),await _0x9b35ba['save']();const _0x196b78=replacePlaceholders(lang[_0x1c73b1(-0x18e,-0x194)][_0x1c73b1(-0x17e,-0x197)][_0x1c73b1(-0x1a0,-0x1ad)]['Lose'][Math[_0x1c73b1(-0x191,-0x1ae)](Math[_0x1c73b1(-0x18a,-0x1a0)]()*lang[_0x1c73b1(-0x18e,-0x19b)][_0x1c73b1(-0x17e,-0x188)][_0x1c73b1(-0x1a0,-0x1a1)]['Lose'][_0x1c73b1(-0x186,-0x1a3)])],_0x34e593),_0x166291=new EmbedBuilder()[_0x1c73b1(-0x1ad,-0x195)](_0xdee0fc)[_0x1c73b1(-0x1af,-0x1b0)](_0x196b78)[_0x1c73b1(-0x183,-0x19c)]({'text':replacePlaceholders(lang['Economy'][_0x1c73b1(-0x1ac,-0x1a3)][_0x1c73b1(-0x17a,-0x192)],{'balance':_0x9b35ba[_0x1c73b1(-0x17f,-0x18c)]})})[_0x1c73b1(-0x180,-0x199)](_0x1c73b1(-0x18b,-0x18c));return _0x5013ed[_0x1c73b1(-0x185,-0x195)]({'embeds':[_0x166291]});}}};function _0x33ec(){const _0x2dcc51=['reply','./Utility/helpers','setFooter','target','6428525PIsCho','setColor','balance','Games','34070751uCirvT','setRequired','commandData','footer','9RnrxYU','Rob\x20another\x20user','4HGlirn','transactionLogs','save','guild','setDescription','win','setTitle','Messages','Win','lastRob','cooldown','findOne','getUser','Title','push','You\x20cannot\x20rob\x20yourself.','922120snXZqY','load','2792027rPQlEj','Rob','robbed','lose','9rfZMfD','user','getTime','12067490LMgTSM','options','targetRob','6YyCtLd','discord.js','utf8','min','rob','Money','floor','./lang.yml','6155288gFNsue','Economy','addUserOption','60484NnRLYr','#FF0000','random','percent','readFileSync','./Utility/parseDuration','length'];_0x33ec=function(){return _0x2dcc51;};return _0x33ec();}