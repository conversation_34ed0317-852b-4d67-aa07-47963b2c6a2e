const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');
const auditLogDB = require('../../../utils/auditLogDatabase.js');

module.exports = {
    name: Events.ChannelDelete,
    async execute(channel, client) {
        try {
            console.log(`🗑️ Channel deleted: ${channel.name} in ${channel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(channel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_DELETE')) return;
            
            // Get channel types mapping
            const channelTypes = {
                0: "Kênh văn bản",
                1: "DM",
                2: "Kênh thoại", 
                3: "Group DM",
                4: "<PERSON>h mục",
                5: "<PERSON><PERSON><PERSON> tin tức",
                10: "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> tin tức",
                11: "<PERSON>ễn đàn công khai",
                12: "<PERSON>ễn đàn riêng tư",
                13: "<PERSON>ênh sân khấu",
                14: "Thư mục",
                15: "Forum",
                16: "Media"
            };
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_DELETE',
                user: 'System',
                userId: null,
                action: 'Kênh được xóa',
                details: `Kênh **${channel.name}** đã được xóa`,
                target: channel.name,
                channel: channel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the channel from audit logs
            try {
                const auditLogs = await channel.guild.fetchAuditLogs({
                    type: 12, // CHANNEL_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === channel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Kênh được xóa bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for channel delete');
            }
            
            // Add to database
            await client.db.addAuditLog(channel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Kênh được xóa',
                `Vừa có một kênh được xóa khỏi server`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            embed.addFields([
                {
                    name: '> Tên kênh được xóa',
                    value: `- ${channel.name}`,
                    inline: false
                },
                {
                    name: '> ID kênh',
                    value: `- ${channel.id}`,
                    inline: true
                },
                {
                    name: '> Trong danh mục',
                    value: `- ${channel.parent ? channel.parent.name : 'Không có'}`,
                    inline: true
                },
                {
                    name: '> Loại kênh',
                    value: `- ${channelTypes[channel.type] || 'Không xác định'}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${channel.guild.name} • Channel Delete`,
                iconURL: channel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel delete logged: ${channel.name}`);
            
        } catch (error) {
            console.error('Error in channelDelete audit log:', error);
        }
    }
};
