const { EmbedBuilder } = require('discord.js');

// Verification levels mapping
const verificationLevels = {
    NONE: 'None',
    LOW: 'Low',
    MEDIUM: 'Medium',
    HIGH: 'High',
    VERY_HIGH: 'Very High'
};

// Boost tiers mapping
const booster = {
    NONE: 'Level 0',
    TIER_1: 'Level 1',
    TIER_2: 'Level 2',
    TIER_3: 'Level 3'
};

// Custom emojis for enabled/disabled features
const disabled = '❌'; // Replace with your custom emoji if needed
const enabled = '✅'; // Replace with your custom emoji if needed

module.exports = {
    name: 'serverinfo',
    description: 'Shows detailed information about the server.',
    async execute(message) {
        try {
            const { guild } = message;

            if (!guild) {
                console.error("❌ Error: Guild not found!");
                return message.channel.send('❌ | Error: Unable to fetch server details.');
            }

            // Fetch banned members (requires BAN_MEMBERS permission)
            let bans;
            try {
                bans = await guild.bans.fetch().then(bans => bans.size);
            } catch (error) {
                bans = 'No permission to view bans';
            }

            // Get server creation date
            const createdTimestamp = guild.createdTimestamp;

            // Sort roles by position and map them to a string
            const roles = guild.roles.cache
                .sort((a, b) => b.position - a.position)
                .map(role => role.toString())
                .slice(0, -1); // Remove @everyone role

            let rolesdisplay;
            if (roles.length < 15) {
                rolesdisplay = roles.join(' ');
                if (roles.length < 1) rolesdisplay = 'None';
            } else {
                rolesdisplay = `\`Too many roles to show..\``;
            }

            // Truncate roles if they exceed 1024 characters
            if (rolesdisplay.length > 1024) {
                rolesdisplay = `${roles.slice(0, 15).join(' ')} \`more..\``;
            }

            // Get counts for members, channels, and emojis
            const members = guild.memberCount;
            const channels = guild.channels.cache;
            const emojis = guild.emojis.cache;

            // Create the embed
            const embed = new EmbedBuilder()
                .setColor('#00FFAA') // Use a predefined color
                .setTitle(`${guild.name}'s Information`)
                .setThumbnail(guild.iconURL({ dynamic: true }))
                .addFields(
                    {
                        name: '__About__',
                        value: `**Name**: ${guild.name}\n**ID**: ${guild.id}\n**Owner**: <@${guild.ownerId}> (${guild.ownerId})\n**Created at**: <t:${Math.floor(createdTimestamp / 1000)}:R>\n**Members**: ${members}\n**Banned Members**: ${bans}`
                    },
                    {
                        name: '__Server Information__',
                        value: `**Verification Level**: ${verificationLevels[guild.verificationLevel]}\n**Inactive Channel**: ${guild.afkChannelId ? `<#${guild.afkChannelId}>` : disabled}\n**Inactive Timeout**: ${guild.afkTimeout / 60} mins\n**System Messages Channel**: ${guild.systemChannelId ? `<#${guild.systemChannelId}>` : disabled}\n**Boost Bar Enabled**: ${guild.premiumProgressBarEnabled ? enabled : disabled}`
                    },
                    {
                        name: '__Channels__',
                        value: `**Total**: ${channels.size}\n**Text Channels**: ${channels.filter(channel => channel.type === 'GUILD_TEXT').size} | **Voice Channels**: ${channels.filter(channel => channel.type === 'GUILD_VOICE').size}`
                    },
                    {
                        name: '__Emoji Info__',
                        value: `**Regular**: ${emojis.filter(emoji => !emoji.animated).size}\n**Animated**: ${emojis.filter(emoji => emoji.animated).size}\n**Total**: ${emojis.size}`
                    },
                    {
                        name: '__Boost Status__',
                        value: `${booster[guild.premiumTier]} [${guild.premiumSubscriptionCount || '0'} Boosts]`
                    },
                    {
                        name: `__Server Roles__ [${roles.length}]`,
                        value: rolesdisplay
                    }
                )
                .setFooter({ text: `Requested by ${message.author.tag}`, iconURL: message.author.displayAvatarURL() })
                .setTimestamp();

            // Add banner if available
            if (guild.bannerURL()) {
                embed.setImage(guild.bannerURL({ size: 4096 }));
            }

            // Send the embed
            await message.channel.send({ embeds: [embed] });

        } catch (error) {
            console.error('❌ Error in serverinfo command:', error);
            message.channel.send('❌ | An error occurred while fetching server information.');
        }
    }
};