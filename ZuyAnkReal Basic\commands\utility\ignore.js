const fs = require('fs');
const path = require('path');
const ignorePath = path.join(__dirname, '../../data/ignoredChannels.json');
const bypassPath = path.join(__dirname, '../../data/bypassRoles.json');

module.exports = {
    name: "ignore",
    description: "Manage ignored channels and bypass roles",
    async execute(message, args) {
        if (!message.member.permissions.has("ManageChannels")) {
            return message.reply("You don't have permission to use this command.");
        }

        if (!fs.existsSync(ignorePath)) fs.writeFileSync(ignorePath, JSON.stringify([]));
        if (!fs.existsSync(bypassPath)) fs.writeFileSync(bypassPath, JSON.stringify([]));

        let ignored = JSON.parse(fs.readFileSync(ignorePath, 'utf8'));
        let bypass = JSON.parse(fs.readFileSync(bypassPath, 'utf8'));

        const [main, action] = args;
        const channel = message.mentions.channels.first();
        const role = message.mentions.roles.first();

        // Bypass logic
        if (main === "bypass") {
            switch (action) {
                case "add":
                    if (!role) return message.reply("Mention a role to add to bypass.");
                    if (bypass.includes(role.id)) return message.reply("This role already bypasses ignore.");
                    bypass.push(role.id);
                    fs.writeFileSync(bypassPath, JSON.stringify(bypass, null, 2));
                    return message.channel.send(`Role <@&${role.id}> added to bypass list.`);
                case "remove":
                    if (!role) return message.reply("Mention a role to remove from bypass.");
                    if (!bypass.includes(role.id)) return message.reply("This role is not in the bypass list.");
                    bypass = bypass.filter(id => id !== role.id);
                    fs.writeFileSync(bypassPath, JSON.stringify(bypass, null, 2));
                    return message.channel.send(`Role <@&${role.id}> removed from bypass list.`);
                case "list":
                    if (bypass.length === 0) return message.reply("No roles bypass the ignore system.");
                    return message.channel.send(`**Bypass Roles:**\n${bypass.map(id => `<@&${id}>`).join('\n')}`);
                case "reset":
                    bypass = [];
                    fs.writeFileSync(bypassPath, JSON.stringify(bypass, null, 2));
                    return message.channel.send("Bypass list reset.");
                default:
                    return message.reply("Use: `ignore bypass add/remove/list/reset`");
            }
        }

        // Channel ignore logic
        switch (main) {
            case "add":
                if (!channel) return message.reply("Mention a channel to ignore.");
                if (ignored.includes(channel.id)) return message.reply("This channel is already ignored.");
                ignored.push(channel.id);
                fs.writeFileSync(ignorePath, JSON.stringify(ignored, null, 2));
                return message.channel.send(`Channel <#${channel.id}> added to ignore list.`);
            case "remove":
                if (!channel) return message.reply("Mention a channel to remove from ignore.");
                if (!ignored.includes(channel.id)) return message.reply("This channel is not ignored.");
                ignored = ignored.filter(id => id !== channel.id);
                fs.writeFileSync(ignorePath, JSON.stringify(ignored, null, 2));
                return message.channel.send(`Channel <#${channel.id}> removed from ignore list.`);
            case "list":
                if (ignored.length === 0) return message.reply("No ignored channels.");
                return message.channel.send(`**Ignored Channels:**\n${ignored.map(id => `<#${id}>`).join('\n')}`);
            case "reset":
                ignored = [];
                fs.writeFileSync(ignorePath, JSON.stringify(ignored, null, 2));
                return message.channel.send("Ignore list reset.");
            default:
                return message.reply("Use: `ignore add/remove/list/reset` or `ignore bypass add/remove/list/reset`");
        }
    }
};