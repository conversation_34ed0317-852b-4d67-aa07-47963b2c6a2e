# 🚀 Hướng dẫn cài đặt chi tiết - Server Setup Bot

## 📋 <PERSON>ục lục
1. [<PERSON><PERSON><PERSON> cầu hệ thống](#yêu-cầu-hệ-thống)
2. [Tạo Discord Bot](#tạo-discord-bot)
3. [Cà<PERSON> đặt và cấu hình](#cài-đặt-và-cấu-hình)
4. [Deploy commands](#deploy-commands)
5. [Khởi động bot](#khởi-động-bot)
6. [Kiểm tra hoạt động](#kiểm-tra-hoạt-động)
7. [Xử lý sự cố](#xử-lý-sự-cố)

## Y<PERSON>u cầu hệ thống

### Phần mềm cần thiết
- **Node.js**: v16.0.0 trở lên ([Tải tại đây](https://nodejs.org/))
- **Git**: Để clone repository ([Tải tại đây](https://git-scm.com/))
- **Code Editor**: VS Code khuyến nghị ([Tải tại đây](https://code.visualstudio.com/))

### Kiểm tra phiên bản
```bash
node --version    # Phải >= v16.0.0
npm --version     # Phải >= v7.0.0
git --version     # Bất kỳ phiên bản nào
```

## Tạo Discord Bot

### Bước 1: Truy cập Discord Developer Portal
1. Mở [Discord Developer Portal](https://discord.com/developers/applications)
2. Đăng nhập bằng tài khoản Discord
3. Nhấn **"New Application"**
4. Đặt tên cho bot (ví dụ: "Server Setup Bot")
5. Nhấn **"Create"**

### Bước 2: Tạo Bot
1. Vào tab **"Bot"** ở sidebar trái
2. Nhấn **"Add Bot"**
3. Xác nhận **"Yes, do it!"**
4. **Copy Token** và lưu lại (sẽ dùng sau)
5. **Copy Application ID** từ tab "General Information"

### Bước 3: Cấu hình Bot
1. Trong tab **"Bot"**:
   - Bật **"Server Members Intent"**
   - Bật **"Message Content Intent"**
   - Tắt **"Public Bot"** (nếu muốn bot riêng tư)

### Bước 4: Tạo link mời bot
```
https://discord.com/api/oauth2/authorize?client_id=YOUR_APPLICATION_ID&permissions=8&scope=bot%20applications.commands
```
*Thay `YOUR_APPLICATION_ID` bằng Application ID của bạn*

## Cài đặt và cấu hình

### Bước 1: Clone repository
```bash
# Clone repository
git clone https://github.com/your-username/ServerSetupBot.git

# Vào thư mục dự án
cd ServerSetupBot
```

### Bước 2: Cài đặt dependencies
```bash
# Cài đặt tất cả packages
npm install

# Kiểm tra cài đặt thành công
npm list discord.js
```

### Bước 3: Tạo file cấu hình
```bash
# Copy file .env mẫu
cp .env.example .env
```

### Bước 4: Chỉnh sửa file .env
Mở file `.env` và điền thông tin:

```env
# Discord Bot Configuration (BẮT BUỘC)
DISCORD_TOKEN=your_bot_token_here
CLIENT_ID=your_bot_client_id_here

# Database Configuration
DATABASE_PATH=./data/serversetup.db

# Bot Configuration
BOT_PREFIX=!
OWNER_ID=your_discord_user_id_here

# Development Settings
NODE_ENV=production
DEBUG=false

# Optional: Guild ID for development (KHÔNG BẮT BUỘC)
GUILD_ID=your_test_server_id_here
```

**Cách lấy thông tin:**
- `DISCORD_TOKEN`: Token từ Discord Developer Portal
- `CLIENT_ID`: Application ID từ Discord Developer Portal  
- `OWNER_ID`: ID Discord của bạn (Bật Developer Mode → Right click tên → Copy ID)
- `GUILD_ID`: ID server test (chỉ dùng khi phát triển)

## Deploy commands

### Bước 1: Deploy slash commands
```bash
# Deploy commands lên Discord
npm run deploy
```

**Kết quả mong đợi:**
```
🤖 Server Setup Bot - Deploy Commands Script
==========================================
📱 Client ID: 123456789012345678
🌍 Mode: Production (Global)
==========================================

🔄 Đang thu thập lệnh slash...
✅ Đã thu thập lệnh: help
✅ Đã thu thập lệnh: welcome-setup
✅ Đã thu thập lệnh: goodbye-setup
✅ Đã thu thập lệnh: autorole-setup

🚀 Bắt đầu deploy 4 lệnh slash...
🗑️ Đang xóa tất cả lệnh cũ...
✅ Đã xóa tất cả global commands cũ
✅ Đã deploy thành công 4 global commands!

📋 Danh sách lệnh đã deploy:
1. /help - Hiển thị hướng dẫn sử dụng bot và danh sách lệnh
2. /welcome-setup - Thiết lập hệ thống chào mừng thành viên mới
3. /goodbye-setup - Thiết lập hệ thống tạm biệt khi thành viên rời khỏi server
4. /autorole-setup - Thiết lập hệ thống auto-role cho thành viên mới

🎉 Deploy commands hoàn tất!
```

### Bước 2: Mời bot vào server
1. Sử dụng link mời đã tạo ở trên
2. Chọn server muốn thêm bot
3. Cấp quyền **Administrator** (khuyến nghị)
4. Nhấn **"Authorize"**

## Khởi động bot

### Development mode (có auto-restart)
```bash
npm run dev
```

### Production mode
```bash
npm start
```

**Kết quả mong đợi:**
```
🚀 Đang khởi động Server Setup Bot...
✅ Database đã được khởi tạo
✅ Đã tạo tất cả bảng database
✅ Đã tải lệnh: help
✅ Đã tải lệnh: welcome-setup
✅ Đã tải lệnh: goodbye-setup
✅ Đã tải lệnh: autorole-setup
✅ Đã tải sự kiện: ready
✅ Đã tải sự kiện: interactionCreate
✅ Đã tải sự kiện: guildCreate
✅ Đã tải sự kiện: guildMemberAdd
✅ Đã tải sự kiện: guildMemberRemove
🎉 Bot đã sẵn sàng! Đăng nhập với tên: ServerSetupBot#1234
📊 Đang phục vụ 1 máy chủ
✅ Bot đã hoàn tất khởi động và sẵn sàng hoạt động!
```

## Kiểm tra hoạt động

### Bước 1: Kiểm tra bot online
- Bot phải hiển thị **"Online"** trong server
- Trạng thái hoạt động: "thiết lập máy chủ Discord"

### Bước 2: Test lệnh cơ bản
```bash
# Trong Discord, gõ:
/help
```

### Bước 3: Test thiết lập welcome
```bash
# Thiết lập welcome
/welcome-setup enable channel:#general

# Test welcome message
/welcome-setup test
```

### Bước 4: Test auto-role
```bash
# Thêm auto-role
/autorole-setup add role:@Member

# Xem danh sách
/autorole-setup list
```

## Xử lý sự cố

### Lỗi thường gặp

#### 1. "Invalid Token"
```
❌ Lỗi: Token bot không hợp lệ
```
**Giải pháp:**
- Kiểm tra lại `DISCORD_TOKEN` trong file `.env`
- Tạo token mới từ Discord Developer Portal

#### 2. "Missing Permissions"
```
❌ Lỗi: Bot không có quyền truy cập
```
**Giải pháp:**
- Cấp quyền Administrator cho bot
- Kiểm tra role của bot có cao hơn role cần quản lý

#### 3. "Commands not showing"
```
Slash commands không hiển thị trong Discord
```
**Giải pháp:**
- Chờ 1 giờ (global commands cần thời gian)
- Sử dụng `GUILD_ID` trong `.env` để test nhanh
- Chạy lại `npm run deploy`

#### 4. "Database errors"
```
❌ Lỗi kết nối database
```
**Giải pháp:**
- Kiểm tra quyền ghi file trong thư mục `data/`
- Xóa file database và khởi động lại bot

### Debug mode

Để bật debug mode, chỉnh sửa `.env`:
```env
DEBUG=true
NODE_ENV=development
```

### Logs

Bot sẽ ghi log vào console. Để lưu log ra file:
```bash
# Linux/Mac
npm start > bot.log 2>&1

# Windows
npm start > bot.log
```

### Kiểm tra kết nối

```bash
# Test kết nối Discord API
curl -H "Authorization: Bot YOUR_TOKEN" https://discord.com/api/v10/users/@me
```

## 🎉 Hoàn thành!

Nếu tất cả bước trên thành công, bot đã sẵn sàng hoạt động!

**Bước tiếp theo:**
1. Đọc [README.md](README.md) để hiểu đầy đủ tính năng
2. Sử dụng `/help` để xem tất cả lệnh
3. Thiết lập các tính năng cần thiết cho server

**Cần hỗ trợ?**
- Tạo [Issue](https://github.com/your-username/ServerSetupBot/issues) trên GitHub
- Liên hệ: <EMAIL>
