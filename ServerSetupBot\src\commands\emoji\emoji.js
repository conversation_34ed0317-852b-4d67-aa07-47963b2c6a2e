const { SlashCommandBuilder, PermissionFlagsBits } = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
} = require("../../utils/embedBuilder.js");
const { isValidUrl } = require("../../utils/validators.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("emoji")
    .setDescription("Quản lý emoji tùy chỉnh của server")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("create")
        .setDescription("Tạo emoji mới từ URL hoặc file")
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên emoji (chỉ chữ cái, số, dấu gạch dưới)")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("url")
            .setDescription("URL hình ảnh emoji")
            .setRequired(false)
        )
        .addAttachmentOption((option) =>
          option
            .setName("file")
            .setDescription("File hình ảnh emoji")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("delete")
        .setDescription("Xóa emoji khỏi server")
        .addStringOption((option) =>
          option
            .setName("emoji")
            .setDescription("Emoji cần xóa (tên hoặc emoji)")
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("add")
        .setDescription("Thêm emoji từ server khác bằng emoji")
        .addStringOption((option) =>
          option
            .setName("emoji")
            .setDescription("Emoji từ server khác cần thêm")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên mới cho emoji (tùy chọn)")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("edit")
        .setDescription("Chỉnh sửa tên emoji")
        .addStringOption((option) =>
          option
            .setName("emoji")
            .setDescription("Emoji cần đổi tên")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("new_name")
            .setDescription("Tên mới cho emoji")
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("lock")
        .setDescription("Khóa emoji chỉ cho role cụ thể")
        .addStringOption((option) =>
          option
            .setName("emoji")
            .setDescription("Emoji cần khóa")
            .setRequired(true)
        )
        .addRoleOption((option) =>
          option
            .setName("role")
            .setDescription("Role được phép sử dụng emoji")
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("unlock")
        .setDescription("Mở khóa emoji cho tất cả thành viên")
        .addStringOption((option) =>
          option
            .setName("emoji")
            .setDescription("Emoji cần mở khóa")
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("roles_reset")
        .setDescription("Reset quyền sử dụng emoji về mặc định")
        .addStringOption((option) =>
          option
            .setName("emoji")
            .setDescription("Emoji cần reset quyền")
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("steal")
        .setDescription("Lấy một emoji từ máy chủ khác hoặc từ URL")
        .addStringOption((option) =>
          option
            .setName("target")
            .setDescription("Emoji hoặc URL cần lấy")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("name")
            .setDescription("Tên cho emoji mới")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("enlarge")
        .setDescription("Phóng to emoji để xem rõ hơn")
        .addStringOption((option) =>
          option
            .setName("emoji")
            .setDescription("Emoji cần phóng to")
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("preview")
        .setDescription("Xem trước emoji trên nền tối và sáng")
        .addStringOption((option) =>
          option
            .setName("emoji")
            .setDescription("Emoji cần xem trước")
            .setRequired(true)
        )
    ),
  category: "content",
  adminOnly: true,
  manageServer: false,

  async execute(interaction, client) {
    // Kiểm tra quyền bot
    if (
      !interaction.guild.members.me.permissions.has(
        PermissionFlagsBits.ManageEmojisAndStickers
      )
    ) {
      const errorEmbed = createErrorEmbed(
        "Bot không có quyền!",
        "Bot cần quyền **Manage Emojis and Stickers** để quản lý emoji."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case "create":
        await handleCreateEmoji(interaction, client);
        break;
      case "delete":
        await handleDeleteEmoji(interaction, client);
        break;
      case "add":
        await handleAddEmoji(interaction, client);
        break;
      case "edit":
        await handleEditEmoji(interaction, client);
        break;
      case "lock":
        await handleLockEmoji(interaction, client);
        break;
      case "unlock":
        await handleUnlockEmoji(interaction, client);
        break;
      case "roles_reset":
        await handleResetRoles(interaction, client);
        break;
      case "steal":
        await handleStealEmoji(interaction, client);
        break;
      case "enlarge":
        await handleEnlargeEmoji(interaction, client);
        break;
      case "preview":
        await handlePreviewEmoji(interaction, client);
        break;
    }
  },
};

async function handleCreateEmoji(interaction, client) {
  const name = interaction.options.getString("name");
  const url = interaction.options.getString("url");
  const file = interaction.options.getAttachment("file");

  // Validate emoji name
  if (!/^[a-zA-Z0-9_]+$/.test(name)) {
    const errorEmbed = createErrorEmbed(
      "Tên emoji không hợp lệ!",
      "Tên emoji chỉ được chứa chữ cái, số và dấu gạch dưới (_)."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Check if emoji name already exists
  const existingEmoji = interaction.guild.emojis.cache.find(
    (emoji) => emoji.name === name
  );
  if (existingEmoji) {
    const errorEmbed = createErrorEmbed(
      "Tên emoji đã tồn tại!",
      `Emoji với tên **${name}** đã tồn tại trong server.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Get image source
  let imageSource;
  if (file) {
    // Validate file type
    const allowedTypes = [
      "image/png",
      "image/jpeg",
      "image/jpg",
      "image/gif",
      "image/webp",
    ];
    if (!allowedTypes.includes(file.contentType)) {
      const errorEmbed = createErrorEmbed(
        "Định dạng file không hỗ trợ!",
        "Chỉ hỗ trợ các định dạng: PNG, JPEG, JPG, GIF, WEBP"
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    // Check file size (256KB limit for Discord)
    if (file.size > 256 * 1024) {
      const errorEmbed = createErrorEmbed(
        "File quá lớn!",
        "Kích thước file không được vượt quá 256KB."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    imageSource = file.url;
  } else if (url) {
    // Validate URL
    if (!isValidUrl(url)) {
      const errorEmbed = createErrorEmbed(
        "URL không hợp lệ!",
        "Vui lòng cung cấp URL hình ảnh hợp lệ."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
    imageSource = url;
  } else {
    const errorEmbed = createErrorEmbed(
      "Thiếu hình ảnh!",
      "Vui lòng cung cấp URL hoặc upload file hình ảnh."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    const emoji = await interaction.guild.emojis.create({
      attachment: imageSource,
      name: name,
      reason: `Emoji được tạo bởi ${interaction.user.tag}`,
    });

    const successEmbed = createSuccessEmbed(
      "Đã tạo emoji thành công!",
      `**Tên:** ${emoji.name}\n` +
        `**Emoji:** ${emoji}\n` +
        `**ID:** ${emoji.id}\n` +
        `**Animated:** ${emoji.animated ? "Có" : "Không"}`
    );

    successEmbed.setThumbnail(emoji.url);
    successEmbed.addFields({
      name: "📝 Sử dụng",
      value: `\`${emoji}\` hoặc \`:${emoji.name}:\``,
      inline: false,
    });

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi tạo emoji:", error);

    let errorMessage = "Không thể tạo emoji. ";
    if (error.code === 30008) {
      errorMessage += "Server đã đạt giới hạn số lượng emoji.";
    } else if (error.code === 50035) {
      errorMessage += "Hình ảnh không hợp lệ hoặc quá lớn.";
    } else {
      errorMessage += "Vui lòng thử lại sau!";
    }

    const errorEmbed = createErrorEmbed("Lỗi tạo emoji!", errorMessage);
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleDeleteEmoji(interaction, client) {
  const emojiInput = interaction.options.getString("emoji");

  // Find emoji by name or ID
  let emoji = interaction.guild.emojis.cache.find(
    (e) =>
      e.name === emojiInput ||
      e.id === emojiInput ||
      e.toString() === emojiInput
  );

  if (!emoji) {
    const errorEmbed = createErrorEmbed(
      "Không tìm thấy emoji!",
      `Emoji **${emojiInput}** không tồn tại trong server.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    const emojiName = emoji.name;
    const emojiUrl = emoji.url;

    await emoji.delete(`Emoji được xóa bởi ${interaction.user.tag}`);

    const successEmbed = createSuccessEmbed(
      "Đã xóa emoji thành công!",
      `Emoji **${emojiName}** đã được xóa khỏi server.`
    );

    successEmbed.setThumbnail(emojiUrl);

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi xóa emoji:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi xóa emoji!",
      "Không thể xóa emoji. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleAddEmoji(interaction, client) {
  const emojiInput = interaction.options.getString("emoji");
  const customName = interaction.options.getString("name");

  // Parse emoji from input
  const emojiMatch = emojiInput.match(/<a?:(\w+):(\d+)>/);
  if (!emojiMatch) {
    const errorEmbed = createErrorEmbed(
      "Emoji không hợp lệ!",
      "Vui lòng cung cấp emoji từ server khác (ví dụ: :emoji_name:)."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const [, originalName, emojiId] = emojiMatch;
  const name = customName || originalName;
  const isAnimated = emojiInput.startsWith("<a:");
  const extension = isAnimated ? "gif" : "png";
  const emojiUrl = `https://cdn.discordapp.com/emojis/${emojiId}.${extension}`;

  // Validate emoji name
  if (!/^[a-zA-Z0-9_]+$/.test(name)) {
    const errorEmbed = createErrorEmbed(
      "Tên emoji không hợp lệ!",
      "Tên emoji chỉ được chứa chữ cái, số và dấu gạch dưới (_)."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Check if emoji name already exists
  const existingEmoji = interaction.guild.emojis.cache.find(
    (emoji) => emoji.name === name
  );
  if (existingEmoji) {
    const errorEmbed = createErrorEmbed(
      "Tên emoji đã tồn tại!",
      `Emoji với tên **${name}** đã tồn tại trong server.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    const emoji = await interaction.guild.emojis.create({
      attachment: emojiUrl,
      name: name,
      reason: `Emoji được thêm từ server khác bởi ${interaction.user.tag}`,
    });

    const successEmbed = createSuccessEmbed(
      "Đã thêm emoji thành công!",
      `**Tên:** ${emoji.name}\n` +
        `**Emoji:** ${emoji}\n` +
        `**ID:** ${emoji.id}\n` +
        `**Animated:** ${emoji.animated ? "Có" : "Không"}\n` +
        `**Nguồn:** ${originalName} từ server khác`
    );

    successEmbed.setThumbnail(emoji.url);

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi thêm emoji:", error);

    let errorMessage = "Không thể thêm emoji. ";
    if (error.code === 30008) {
      errorMessage += "Server đã đạt giới hạn số lượng emoji.";
    } else if (error.code === 50035) {
      errorMessage += "Emoji không hợp lệ hoặc không thể truy cập.";
    } else {
      errorMessage += "Vui lòng thử lại sau!";
    }

    const errorEmbed = createErrorEmbed("Lỗi thêm emoji!", errorMessage);
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleEditEmoji(interaction, client) {
  const emojiInput = interaction.options.getString("emoji");
  const newName = interaction.options.getString("new_name");

  // Find emoji
  let emoji = interaction.guild.emojis.cache.find(
    (e) =>
      e.name === emojiInput ||
      e.id === emojiInput ||
      e.toString() === emojiInput
  );

  if (!emoji) {
    const errorEmbed = createErrorEmbed(
      "Không tìm thấy emoji!",
      `Emoji **${emojiInput}** không tồn tại trong server.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Validate new name
  if (!/^[a-zA-Z0-9_]+$/.test(newName)) {
    const errorEmbed = createErrorEmbed(
      "Tên mới không hợp lệ!",
      "Tên emoji chỉ được chứa chữ cái, số và dấu gạch dưới (_)."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Check if new name already exists
  const existingEmoji = interaction.guild.emojis.cache.find(
    (e) => e.name === newName && e.id !== emoji.id
  );
  if (existingEmoji) {
    const errorEmbed = createErrorEmbed(
      "Tên emoji đã tồn tại!",
      `Emoji với tên **${newName}** đã tồn tại trong server.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    const oldName = emoji.name;
    await emoji.setName(
      newName,
      `Tên emoji được đổi bởi ${interaction.user.tag}`
    );

    const successEmbed = createSuccessEmbed(
      "Đã đổi tên emoji thành công!",
      `**Tên cũ:** ${oldName}\n` +
        `**Tên mới:** ${newName}\n` +
        `**Emoji:** ${emoji}`
    );

    successEmbed.setThumbnail(emoji.url);

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi đổi tên emoji:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi đổi tên emoji!",
      "Không thể đổi tên emoji. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleLockEmoji(interaction, client) {
  const emojiInput = interaction.options.getString("emoji");
  const role = interaction.options.getRole("role");

  // Find emoji
  let emoji = interaction.guild.emojis.cache.find(
    (e) =>
      e.name === emojiInput ||
      e.id === emojiInput ||
      e.toString() === emojiInput
  );

  if (!emoji) {
    const errorEmbed = createErrorEmbed(
      "Không tìm thấy emoji!",
      `Emoji **${emojiInput}** không tồn tại trong server.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    await emoji.edit({
      roles: [role.id],
      reason: `Emoji được khóa cho role ${role.name} bởi ${interaction.user.tag}`,
    });

    const successEmbed = createSuccessEmbed(
      "Đã khóa emoji thành công!",
      `**Emoji:** ${emoji}\n` +
        `**Role được phép:** ${role}\n\n` +
        `Chỉ thành viên có role ${role.name} mới có thể sử dụng emoji này.`
    );

    successEmbed.setThumbnail(emoji.url);

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi khóa emoji:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi khóa emoji!",
      "Không thể khóa emoji. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleUnlockEmoji(interaction, client) {
  const emojiInput = interaction.options.getString("emoji");

  // Find emoji
  let emoji = interaction.guild.emojis.cache.find(
    (e) =>
      e.name === emojiInput ||
      e.id === emojiInput ||
      e.toString() === emojiInput
  );

  if (!emoji) {
    const errorEmbed = createErrorEmbed(
      "Không tìm thấy emoji!",
      `Emoji **${emojiInput}** không tồn tại trong server.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    await emoji.edit({
      roles: [],
      reason: `Emoji được mở khóa bởi ${interaction.user.tag}`,
    });

    const successEmbed = createSuccessEmbed(
      "Đã mở khóa emoji thành công!",
      `**Emoji:** ${emoji}\n\n` +
        `Tất cả thành viên trong server giờ có thể sử dụng emoji này.`
    );

    successEmbed.setThumbnail(emoji.url);

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi mở khóa emoji:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi mở khóa emoji!",
      "Không thể mở khóa emoji. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleResetRoles(interaction, client) {
  const emojiInput = interaction.options.getString("emoji");

  // Find emoji
  let emoji = interaction.guild.emojis.cache.find(
    (e) =>
      e.name === emojiInput ||
      e.id === emojiInput ||
      e.toString() === emojiInput
  );

  if (!emoji) {
    const errorEmbed = createErrorEmbed(
      "Không tìm thấy emoji!",
      `Emoji **${emojiInput}** không tồn tại trong server.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    await emoji.edit({
      roles: [],
      reason: `Quyền emoji được reset bởi ${interaction.user.tag}`,
    });

    const successEmbed = createSuccessEmbed(
      "Đã reset quyền emoji thành công!",
      `**Emoji:** ${emoji}\n\n` +
        `Quyền sử dụng emoji đã được reset về mặc định. Tất cả thành viên có thể sử dụng.`
    );

    successEmbed.setThumbnail(emoji.url);

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi reset quyền emoji:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi reset quyền emoji!",
      "Không thể reset quyền emoji. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleStealEmoji(interaction, client) {
  const target = interaction.options.getString("target");
  const customName = interaction.options.getString("name");

  let emojiUrl,
    name,
    isAnimated = false;

  // Check if target is URL
  if (isValidUrl(target)) {
    emojiUrl = target;
    name = customName || "stolen_emoji";
  } else {
    // Parse emoji from input
    const emojiMatch = target.match(/<a?:(\w+):(\d+)>/);
    if (!emojiMatch) {
      const errorEmbed = createErrorEmbed(
        "Target không hợp lệ!",
        "Vui lòng cung cấp emoji hoặc URL hình ảnh hợp lệ."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    const [, originalName, emojiId] = emojiMatch;
    name = customName || originalName;
    isAnimated = target.startsWith("<a:");
    const extension = isAnimated ? "gif" : "png";
    emojiUrl = `https://cdn.discordapp.com/emojis/${emojiId}.${extension}`;
  }

  // Validate emoji name
  if (!/^[a-zA-Z0-9_]+$/.test(name)) {
    const errorEmbed = createErrorEmbed(
      "Tên emoji không hợp lệ!",
      "Tên emoji chỉ được chứa chữ cái, số và dấu gạch dưới (_)."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  // Check if emoji name already exists
  const existingEmoji = interaction.guild.emojis.cache.find(
    (emoji) => emoji.name === name
  );
  if (existingEmoji) {
    const errorEmbed = createErrorEmbed(
      "Tên emoji đã tồn tại!",
      `Emoji với tên **${name}** đã tồn tại trong server.`
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  try {
    const emoji = await interaction.guild.emojis.create({
      attachment: emojiUrl,
      name: name,
      reason: `Emoji được lấy từ máy chủ khác hoặc URL bởi ${interaction.user.tag}`,
    });

    const successEmbed = createSuccessEmbed(
      "Đã lấy emoji thành công!",
      `**Tên:** ${emoji.name}\n` +
        `**Emoji:** ${emoji}\n` +
        `**ID:** ${emoji.id}\n` +
        `**Animated:** ${emoji.animated ? "Có" : "Không"}\n` +
        `**Nguồn:** ${isValidUrl(target) ? "URL" : "Emoji từ server khác"}`
    );

    successEmbed.setThumbnail(emoji.url);
    successEmbed.addFields({
      name: "📝 Sử dụng",
      value: `\`${emoji}\` hoặc \`:${emoji.name}:\``,
      inline: false,
    });

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error("Lỗi khi lấy emoji:", error);

    let errorMessage = "Không thể lấy emoji. ";
    if (error.code === 30008) {
      errorMessage += "Server đã đạt giới hạn số lượng emoji.";
    } else if (error.code === 50035) {
      errorMessage += "Hình ảnh không hợp lệ hoặc không thể truy cập.";
    } else {
      errorMessage += "Vui lòng thử lại sau!";
    }

    const errorEmbed = createErrorEmbed("Lỗi lấy emoji!", errorMessage);
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleEnlargeEmoji(interaction, client) {
  const emojiInput = interaction.options.getString("emoji");

  // Parse emoji from input
  const emojiMatch = emojiInput.match(/<a?:(\w+):(\d+)>/);
  if (!emojiMatch) {
    const errorEmbed = createErrorEmbed(
      "Emoji không hợp lệ!",
      "Vui lòng cung cấp emoji hợp lệ để phóng to."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const [, emojiName, emojiId] = emojiMatch;
  const isAnimated = emojiInput.startsWith("<a:");
  const extension = isAnimated ? "gif" : "png";
  const enlargedUrl = `https://cdn.discordapp.com/emojis/${emojiId}.${extension}?size=1024`;

  const embed = createInfoEmbed(
    `🔍 Emoji phóng to: ${emojiName}`,
    `**Tên:** ${emojiName}\n**ID:** ${emojiId}\n**Animated:** ${
      isAnimated ? "Có" : "Không"
    }`
  );

  embed.setImage(enlargedUrl);
  embed.addFields({
    name: "🔗 Liên kết trực tiếp",
    value: `[Xem ảnh gốc](${enlargedUrl})`,
    inline: false,
  });

  await interaction.reply({ embeds: [embed] });
}

async function handlePreviewEmoji(interaction, client) {
  const emojiInput = interaction.options.getString("emoji");

  // Parse emoji from input
  const emojiMatch = emojiInput.match(/<a?:(\w+):(\d+)>/);
  if (!emojiMatch) {
    const errorEmbed = createErrorEmbed(
      "Emoji không hợp lệ!",
      "Vui lòng cung cấp emoji hợp lệ để xem trước."
    );
    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }

  const [, emojiName, emojiId] = emojiMatch;
  const isAnimated = emojiInput.startsWith("<a:");
  const extension = isAnimated ? "gif" : "png";
  const emojiUrl = `https://cdn.discordapp.com/emojis/${emojiId}.${extension}?size=512`;

  // Create preview embed with dark theme
  const darkEmbed = createInfoEmbed(
    `🌙 Preview trên nền tối: ${emojiName}`,
    `**Tên:** ${emojiName}\n**ID:** ${emojiId}\n**Animated:** ${
      isAnimated ? "Có" : "Không"
    }\n\n**Xem trước trên nền Discord tối:**`
  );
  darkEmbed.setColor(0x36393f); // Discord dark theme color
  darkEmbed.setImage(emojiUrl);

  // Create preview embed with light theme
  const lightEmbed = createInfoEmbed(
    `☀️ Preview trên nền sáng: ${emojiName}`,
    `**Xem trước trên nền Discord sáng:**`
  );
  lightEmbed.setColor(0xffffff); // Light theme color
  lightEmbed.setImage(emojiUrl);

  await interaction.reply({ embeds: [darkEmbed, lightEmbed] });
}
