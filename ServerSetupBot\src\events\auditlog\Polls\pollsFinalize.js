const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.MessageUpdate,
    async execute(oldMessage, newMessage, client) {
        try {
            // Only handle poll finalization (poll ended)
            if (!newMessage.poll || !oldMessage.poll) return;
            if (oldMessage.poll.resultsFinalized === newMessage.poll.resultsFinalized) return;
            if (!newMessage.poll.resultsFinalized) return;
            
            console.log(`🏁 Poll finalized: ${newMessage.poll.question?.text || 'Unknown'} in ${newMessage.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newMessage.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'POLLS_FINALIZE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'POLLS_FINALIZE',
                user: 'System',
                userId: null,
                action: 'Poll được hoàn thành',
                details: `Poll **${newMessage.poll.question?.text || 'Unknown'}** đã hoàn thành`,
                target: newMessage.poll.question?.text || 'Unknown',
                channel: newMessage.channel.name,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(newMessage.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🏁 Poll được hoàn thành',
                `Vừa có một poll hoàn thành trong server`
            );
            
            embed.setColor(0x3498db); // Blue for completion
            
            embed.addFields([
                {
                    name: '> ID tin nhắn',
                    value: `- ${newMessage.id}`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${newMessage.channel}`,
                    inline: true
                },
                {
                    name: '> Thời gian hoàn thành',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add poll creator info
            if (newMessage.author) {
                embed.addFields([
                    {
                        name: '> Người tạo poll',
                        value: `- ${newMessage.author.tag}`,
                        inline: true
                    },
                    {
                        name: '> ID người tạo',
                        value: `- ${newMessage.author.id}`,
                        inline: true
                    }
                ]);
            }
            
            // Add poll question
            if (newMessage.poll.question?.text) {
                embed.addFields({
                    name: '> Câu hỏi poll',
                    value: `- ${newMessage.poll.question.text}`,
                    inline: false
                });
            }
            
            // Add poll results
            if (newMessage.poll.answers && newMessage.poll.answers.length > 0) {
                const totalVotes = newMessage.poll.answers.reduce((total, answer) => total + (answer.voteCount || 0), 0);
                
                embed.addFields([
                    {
                        name: '> Tổng số votes',
                        value: `- ${totalVotes} votes`,
                        inline: true
                    },
                    {
                        name: '> Số lựa chọn',
                        value: `- ${newMessage.poll.answers.length} lựa chọn`,
                        inline: true
                    }
                ]);
                
                // Show detailed results
                if (totalVotes > 0) {
                    // Sort answers by vote count (descending)
                    const sortedAnswers = [...newMessage.poll.answers].sort((a, b) => (b.voteCount || 0) - (a.voteCount || 0));
                    
                    const resultsText = sortedAnswers.map((answer, index) => {
                        const emoji = answer.emoji ? `${answer.emoji} ` : `${index + 1}. `;
                        const percentage = Math.round((answer.voteCount || 0) / totalVotes * 100);
                        const barLength = Math.round(percentage / 5); // 20 chars max
                        const bar = '█'.repeat(barLength) + '░'.repeat(20 - barLength);
                        return `${emoji}**${answer.text || 'No text'}**\n${bar} ${answer.voteCount || 0} votes (${percentage}%)`;
                    }).join('\n\n');
                    
                    embed.addFields({
                        name: '> 📊 Kết quả chi tiết',
                        value: resultsText.length > 1000 ? resultsText.substring(0, 1000) + '...' : resultsText,
                        inline: false
                    });
                    
                    // Find winner(s)
                    const maxVotes = Math.max(...newMessage.poll.answers.map(answer => answer.voteCount || 0));
                    const winners = newMessage.poll.answers.filter(answer => (answer.voteCount || 0) === maxVotes);
                    
                    if (winners.length === 1) {
                        const winner = winners[0];
                        const winnerEmoji = winner.emoji ? `${winner.emoji} ` : '';
                        embed.addFields({
                            name: '> 🏆 Lựa chọn thắng cuộc',
                            value: `- ${winnerEmoji}**${winner.text || 'No text'}** với ${winner.voteCount || 0} votes`,
                            inline: false
                        });
                    } else if (winners.length > 1) {
                        const winnersList = winners.map(winner => {
                            const winnerEmoji = winner.emoji ? `${winner.emoji} ` : '';
                            return `${winnerEmoji}${winner.text || 'No text'}`;
                        }).join(', ');
                        embed.addFields({
                            name: '> 🤝 Hòa (nhiều lựa chọn cùng số votes cao nhất)',
                            value: `- ${winnersList} với ${maxVotes} votes`,
                            inline: false
                        });
                    }
                } else {
                    embed.addFields({
                        name: '> 📊 Kết quả',
                        value: `- Không có votes nào`,
                        inline: false
                    });
                }
            }
            
            // Add poll settings
            if (newMessage.poll.allowMultiselect !== undefined) {
                embed.addFields({
                    name: '> Cho phép chọn nhiều',
                    value: `- ${newMessage.poll.allowMultiselect ? 'Có' : 'Không'}`,
                    inline: true
                });
            }
            
            // Add poll duration info
            if (newMessage.poll.expiresTimestamp && newMessage.createdTimestamp) {
                const duration = newMessage.poll.expiresTimestamp - newMessage.createdTimestamp;
                const durationHours = Math.floor(duration / (1000 * 60 * 60));
                const durationMinutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
                
                embed.addFields([
                    {
                        name: '> Thời gian bắt đầu',
                        value: `- <t:${Math.floor(newMessage.createdTimestamp / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Thời gian kết thúc',
                        value: `- <t:${Math.floor(newMessage.poll.expiresTimestamp / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Thời gian diễn ra',
                        value: `- ${durationHours} giờ, ${durationMinutes} phút`,
                        inline: true
                    }
                ]);
            }
            
            // Add message link
            embed.addFields({
                name: '> Link poll',
                value: `- [Nhấn để xem poll](${newMessage.url})`,
                inline: true
            });
            
            // Add participation stats
            if (newMessage.poll.answers && newMessage.poll.answers.length > 0) {
                const totalVotes = newMessage.poll.answers.reduce((total, answer) => total + (answer.voteCount || 0), 0);
                if (totalVotes > 0) {
                    embed.addFields({
                        name: '> 📈 Thống kê tham gia',
                        value: [
                            `• Tổng votes: ${totalVotes}`,
                            `• Trung bình votes/lựa chọn: ${Math.round(totalVotes / newMessage.poll.answers.length)}`,
                            `• Lựa chọn phổ biến nhất: ${Math.max(...newMessage.poll.answers.map(a => a.voteCount || 0))} votes`,
                            `• Lựa chọn ít phổ biến nhất: ${Math.min(...newMessage.poll.answers.map(a => a.voteCount || 0))} votes`
                        ].join('\n'),
                        inline: false
                    });
                }
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newMessage.guild.name} • Poll Finalize`,
                iconURL: newMessage.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Poll finalize logged: ${newMessage.poll.question?.text || 'Unknown'}`);
            
        } catch (error) {
            console.error('Error in pollsFinalize audit log:', error);
        }
    }
};
