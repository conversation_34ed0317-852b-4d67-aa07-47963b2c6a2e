(function(_0x25b348,_0x3a25ee){function _0x4570ad(_0x1d5383,_0x1caa3e){return _0x3935(_0x1d5383-0x253,_0x1caa3e);}const _0x57ee0d=_0x25b348();while(!![]){try{const _0xf07cba=-parseInt(_0x4570ad(0x387,0x387))/0x1*(parseInt(_0x4570ad(0x384,0x363))/0x2)+parseInt(_0x4570ad(0x386,0x366))/0x3*(parseInt(_0x4570ad(0x381,0x396))/0x4)+-parseInt(_0x4570ad(0x358,0x347))/0x5*(-parseInt(_0x4570ad(0x369,0x384))/0x6)+-parseInt(_0x4570ad(0x383,0x38d))/0x7*(-parseInt(_0x4570ad(0x364,0x37b))/0x8)+parseInt(_0x4570ad(0x351,0x32c))/0x9*(parseInt(_0x4570ad(0x396,0x3b9))/0xa)+parseInt(_0x4570ad(0x37f,0x375))/0xb*(-parseInt(_0x4570ad(0x38b,0x378))/0xc)+-parseInt(_0x4570ad(0x389,0x393))/0xd*(-parseInt(_0x4570ad(0x365,0x37f))/0xe);if(_0xf07cba===_0x3a25ee)break;else _0x57ee0d['push'](_0x57ee0d['shift']());}catch(_0x3fbeb9){_0x57ee0d['push'](_0x57ee0d['shift']());}}}(_0x33c6,0xeff85));const {SlashCommandBuilder,EmbedBuilder}=require(_0x3091e0(0x225,0x20d)),User=require(_0x3091e0(0x217,0x21b)),fs=require('fs');function _0x3935(_0x1a6e63,_0x368494){const _0x33c698=_0x33c6();return _0x3935=function(_0x393500,_0x1f4886){_0x393500=_0x393500-0xfb;let _0x316842=_0x33c698[_0x393500];return _0x316842;},_0x3935(_0x1a6e63,_0x368494);}function _0x3091e0(_0x3a303a,_0x390dfe){return _0x3935(_0x3a303a-0xfa,_0x390dfe);}const yaml=require(_0x3091e0(0x227,0x227)),config=yaml[_0x3091e0(0x206,0x208)](fs[_0x3091e0(0x216,0x234)](_0x3091e0(0x218,0x1f8),_0x3091e0(0x201,0x1ea))),lang=yaml['load'](fs[_0x3091e0(0x216,0x221)](_0x3091e0(0x213,0x1f2),_0x3091e0(0x201,0x202))),parseDuration=require('./Utility/parseDuration'),{checkActiveBooster,replacePlaceholders}=require(_0x3091e0(0x219,0x21e));module[_0x3091e0(0x23f,0x21e)]={'data':new SlashCommandBuilder()[_0x3091e0(0x233,0x219)](_0x3091e0(0x23e,0x25f))[_0x3091e0(0x204,0x210)]('Bet\x20on\x20whether\x20the\x20roll\x20will\x20be\x20low\x20or\x20high.')[_0x3091e0(0x207,0x1ff)](_0x4f47cc=>_0x4f47cc[_0x3091e0(0x233,0x24e)]('amount')['setDescription'](_0x3091e0(0x222,0x21a))[_0x3091e0(0x20e,0x225)](!![]))[_0x3091e0(0x235,0x233)](_0x5c4863=>_0x5c4863[_0x3091e0(0x233,0x241)](_0x3091e0(0x1f9,0x205))[_0x3091e0(0x204,0x20d)](_0x3091e0(0x212,0x203))[_0x3091e0(0x20e,0x1f1)](!![])['addChoices']({'name':_0x3091e0(0x1fb,0x20e),'value':_0x3091e0(0x200,0x1f1)},{'name':'High\x20(51-100)','value':'high'})),'category':_0x3091e0(0x208,0x20a),async 'execute'(_0x120ea9){const _0x3931f8=_0x120ea9[_0x36bae6(-0x1e,-0x39)]['getInteger'](_0x36bae6(0x3,0x21)),_0x26aa9e=_0x120ea9[_0x36bae6(-0x1e,0x4)][_0x36bae6(0x16,0x2)](_0x36bae6(-0x22,-0x40))[_0x36bae6(0x9,0x1c)]();if(_0x3931f8<=0x0)return _0x120ea9['reply']({'content':lang[_0x36bae6(-0x13,-0x35)]['Messages'][_0x36bae6(-0x1d,0x1)],'ephemeral':!![]});let _0xec7a7e=await User[_0x36bae6(0x20,0x0)]({'userId':_0x120ea9[_0x36bae6(-0x7,0xf)]['id'],'guildId':_0x120ea9['guild']['id']},{'balance':0x1,'commandData.lastRoll':0x1,'transactionLogs':0x1,'boosters':0x1});if(!_0xec7a7e)_0xec7a7e=new User({'userId':_0x120ea9[_0x36bae6(-0x7,-0x20)]['id'],'guildId':_0x120ea9['guild']['id'],'balance':0x0,'commandData':{},'transactionLogs':[]});else!Array[_0x36bae6(-0x6,0x18)](_0xec7a7e[_0x36bae6(-0xa,-0x22)])&&(_0xec7a7e[_0x36bae6(-0xa,0x0)]=[]);if(_0xec7a7e['balance']<_0x3931f8)return _0x120ea9[_0x36bae6(0xe,0x32)]({'content':lang['Economy'][_0x36bae6(-0x25,-0x28)][_0x36bae6(0x0,0xe)],'ephemeral':!![]});const _0xe87f54=new Date(),_0x8bcd8c=parseDuration(config[_0x36bae6(-0x13,-0x37)][_0x36bae6(-0x12,-0x22)][_0x36bae6(-0x16,-0x18)]);if(_0x8bcd8c>0x0&&_0xec7a7e[_0x36bae6(-0x1,-0x7)]['lastRoll']){const _0x2bd5f8=new Date(_0xec7a7e[_0x36bae6(-0x1,-0x17)]['lastRoll'][_0x36bae6(0x1e,0xe)]()+_0x8bcd8c);if(_0xe87f54<_0x2bd5f8){const _0x11da47=new EmbedBuilder()['setDescription'](replacePlaceholders(lang[_0x36bae6(-0x13,-0x22)]['Messages'][_0x36bae6(-0x16,0xb)],{'nextUse':Math['floor'](_0x2bd5f8['getTime']()/0x3e8)}))[_0x36bae6(-0x18,-0x8)](_0x36bae6(0x8,0x24))[_0x36bae6(-0x19,-0xf)]({'text':replacePlaceholders(lang[_0x36bae6(-0x13,-0xc)][_0x36bae6(-0x25,-0x3b)]['footer'],{'balance':_0xec7a7e[_0x36bae6(0x5,0x0)]})});return _0x120ea9[_0x36bae6(0xe,0x1f)]({'embeds':[_0x11da47]});}}const _0x589137=Math[_0x36bae6(0x1d,0x7)](Math['random']()*0x64)+0x1,_0x1d9dd7=_0x26aa9e===_0x36bae6(-0x1b,-0x13)&&_0x589137<=0x32||_0x26aa9e===_0x36bae6(0x1f,0x36)&&_0x589137>0x32,_0x3f8190=0x2,_0x40e3ba=checkActiveBooster(_0xec7a7e,_0x36bae6(-0x1f,-0x37)),_0x175b00=_0x3f8190*_0x40e3ba;function _0x36bae6(_0x327e1f,_0x5ac768){return _0x3091e0(_0x327e1f- -0x21b,_0x5ac768);}let _0x3cf994={'user':'<@'+_0x120ea9['user']['id']+'>','balance':_0x3931f8,'rollResult':_0x589137},_0x896834,_0x3d32ae,_0x396646;if(_0x1d9dd7){const _0xe983a5=_0x3931f8*_0x175b00;_0xec7a7e['balance']+=_0xe983a5,_0x3cf994[_0x36bae6(0x5,-0x1a)]=_0xe983a5,_0x896834=replacePlaceholders(lang['Economy'][_0x36bae6(0x19,0x24)][_0x36bae6(-0x12,0x7)][_0x36bae6(-0x24,-0x32)][Math[_0x36bae6(0x1d,0x4)](Math[_0x36bae6(-0x21,0x1)]()*lang[_0x36bae6(-0x13,-0x5)][_0x36bae6(0x19,0x32)][_0x36bae6(-0x12,-0x33)][_0x36bae6(-0x24,-0x1c)][_0x36bae6(0x1,0x17)])],_0x3cf994),_0x3d32ae=_0x36bae6(-0xc,-0xb),_0x396646=replacePlaceholders(lang['Economy']['Games'][_0x36bae6(-0x12,-0x12)][_0x36bae6(-0x26,-0x42)],{'result':lang[_0x36bae6(-0x13,-0x18)][_0x36bae6(-0x25,-0x1a)][_0x36bae6(0x11,-0x12)]});}else _0xec7a7e['balance']-=_0x3931f8,_0x896834=replacePlaceholders(lang[_0x36bae6(-0x13,0xa)][_0x36bae6(0x19,0x32)]['Roll']['Lose'][Math['floor'](Math[_0x36bae6(-0x21,-0x3)]()*lang[_0x36bae6(-0x13,-0x1f)][_0x36bae6(0x19,0x2b)][_0x36bae6(-0x12,0x14)]['Lose'][_0x36bae6(0x1,-0xf)])],_0x3cf994),_0x3d32ae='#FF0000',_0x396646=replacePlaceholders(lang[_0x36bae6(-0x13,-0x17)][_0x36bae6(0x19,0x28)]['Roll'][_0x36bae6(-0x26,-0x1b)],{'result':lang[_0x36bae6(-0x13,0x1)][_0x36bae6(-0x25,-0x27)][_0x36bae6(0x6,0xc)]});_0xec7a7e[_0x36bae6(-0x1,0x1f)][_0x36bae6(0x1c,0x26)]=_0xe87f54,_0xec7a7e[_0x36bae6(-0xa,-0x9)]['push']({'type':_0x36bae6(0x23,0x5),'amount':_0x1d9dd7?_0x3931f8*_0x175b00:-_0x3931f8,'timestamp':_0xe87f54}),await _0xec7a7e['save']();const _0xba6d6=new EmbedBuilder()[_0x36bae6(0x21,0x1f)](_0x396646)[_0x36bae6(-0x17,-0x2b)](_0x896834)[_0x36bae6(-0x11,-0x1a)]({'name':lang[_0x36bae6(-0x13,0x5)][_0x36bae6(-0x25,-0x32)][_0x36bae6(0x2,-0x13)],'value':_0x589137[_0x36bae6(0x1b,0x29)](),'inline':!![]},{'name':lang[_0x36bae6(-0x13,0x10)][_0x36bae6(-0x25,-0x2a)]['bet'],'value':_0x26aa9e[_0x36bae6(0x4,-0xe)](0x0)[_0x36bae6(-0xe,0x10)]()+_0x26aa9e[_0x36bae6(0x14,-0x10)](0x1),'inline':!![]})[_0x36bae6(-0x18,-0x13)](_0x3d32ae)['setFooter']({'text':replacePlaceholders(lang[_0x36bae6(-0x13,0x10)][_0x36bae6(-0x25,-0x36)]['footer'],{'balance':_0xec7a7e[_0x36bae6(0x5,-0x20)]})});await _0x120ea9[_0x36bae6(0xe,0xd)]({'embeds':[_0xba6d6]});}};function _0x33c6(){const _0x4ba933=['setName','Games','addStringOption','toString','lastRoll','floor','getTime','high','findOne','setTitle','8236420udjqlP','roll','exports','Title','Messages','Win','9LVvgVa','bet','random','Low\x20(1-50)','Money','options','betAmountError','40030LPRLMX','low','utf8','setFooter','setColor','setDescription','cooldown','load','addIntegerOption','Economy','Roll','addFields','313176zmrTRN','812294UXEugv','toUpperCase','setRequired','#00FF00','1236pAgUvD','transactionLogs','Choose\x20low\x20or\x20high','././lang.yml','user','isArray','readFileSync','../../../models/UserData','././config.yml','./Utility/helpers','commandData','noMoney','length','rollResult','amount','charAt','balance','lose','Amount\x20to\x20bet','#FF0000','toLowerCase','discord.js','16437619tlZYnj','js-yaml','376vNlwJm','reply','14Swsnpu','4710nqAZRL','win','17961xZXzoy','295cJgYOc','slice','13iHxdiW','getString','12pMdshD'];_0x33c6=function(){return _0x4ba933;};return _0x33c6();}