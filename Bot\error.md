<PERSON><PERSON> những thứ cần thay đổi đây:

1. <PERSON><PERSON> không lư<PERSON> kênh logs 
⤷ Tôi /auditlog setup channel:#logs nhưng khi tôi tắt bot rồi khởi động lại nó lại không nhớ kênh tôi setup

2. <PERSON><PERSON> không hề gửi log hoạt động trong server 
⤷ Tôi nhắn helloo và sửa thành -> hello nó không hề báo trong kênh logs được setup
⤷ Tôi sửa tên kênh invites -> invite nó cũng không hề báo trong kênh logs được setup 

3. <PERSON><PERSON><PERSON> có thư mục chứa các events logs

ServerSetupBot/
├── src/
│   ├── events/
│   │   ├── auditlog/
│   │   │   ├── Applications/
│   │   │   │   ├── appAdd.js
│   │   │   │   ├── appRemove.js
│   │   │   │   └── appCommandPermissionUpdate.js
│   │   │   ├── Channels/
│   │   │   │   ├── channelCreate.js
│   │   │   │   ├── channelDelete.js
│   │   │   │   ├── channelPinsUpdate.js
│   │   │   │   ├── channelNameUpdate.js
│   │   │   │   ├── channelTopicUpdate.js
│   │   │   │   ├── channelNsfwUpdate.js
│   │   │   │   ├── channelParentUpdate.js
│   │   │   │   ├── channelPermissionUpdate.js
│   │   │   │   ├── channelTypeUpdate.js
│   │   │   │   ├── channelBitrateUpdate.js
│   │   │   │   ├── channelUserLimitUpdate.js
│   │   │   │   ├── channelSlowModeUpdate.js
│   │   │   │   ├── channelRtcRegionUpdate.js
│   │   │   │   ├── channelVideoQualityUpdate.js
│   │   │   │   ├── channelDefaultArchiveDurationUpdate.js
│   │   │   │   ├── channelDefaultThreadSlowModeUpdate.js
│   │   │   │   ├── channelDefaultReactionEmojiUpdate.js
│   │   │   │   ├── channelDefaultSortOrderUpdate.js
│   │   │   │   ├── channelForumTagsUpdate.js
│   │   │   │   ├── channelForumLayoutUpdate.js
│   │   │   │  	└── channelVoiceStatusUpdate.js
│   │   │   ├── DiscordAutoMod/
│   │   │   │   ├── automodRuleCreate.js
│   │   │   │   ├── automodRuleDelete.js
│   │   │   │   ├── automodRuleToggle.js
│   │   │   │   ├── automodRuleNameUpdate.js
│   │   │   │   ├── automodRuleActionsUpdate.js
│   │   │   │   ├── automodRuleContentUpdate.js
│   │   │   │   ├── automodRuleRolesUpdate.js
│   │   │   │   ├── automodRuleChannelsUpdate.js
│   │   │   │   └── automodRuleWhitelistUpdate.js
│   │   │   ├── Emoji/
│   │   │   │   ├── emojiCreate.js
│   │   │   │   ├── emojiDelete.js
│   │   │   │   ├── emojiNameUpdate.js
│   │   │   │  	└── emojiRolesUpdate.js
│   │   │   ├── Events/
│   │   │   │   ├── eventsCreate.js
│   │   │   │   ├── eventsDelete.js
│   │   │   │   ├── eventsLocationUpdate.js
│   │   │   │   ├── eventsDescriptionUpdate.js
│   │   │   │   ├── eventsNameUpdate.js
│   │   │   │   ├── eventsPrivacyLevelUpdate.js
│   │   │   │   ├── eventsStartTimeUpdate.js
│   │   │   │   ├── eventsEndTimeUpdate.js
│   │   │   │   ├── eventsStatusUpdate.js
│   │   │   │   ├── eventsImageUpdate.js
│   │   │   │   ├── eventsUserSubscribe.js
│   │   │   │   └── eventsUserUnsubscribe.js
│   │   │   ├── Invite/
│   │   │   │   ├── inviteCreate.js
│   │   │   │   ├── inviteDelete.js
│   │   │   │   └── invitePost.js
│   │   │   ├── Messages/
│   │   │   │   ├── messagesDelete.js
│   │   │   │   ├── messagesBulkDelete.js
│   │   │   │   ├── messagesEditPublish.js
│   │   │   │   └── messagesSentUsingCommand.js
│   │   │   ├── Polls/
│   │   │   │   ├── pollsCreate.js
│   │   │   │   ├── pollsDelete.js
│   │   │   │   ├── pollsFinalize.js
│   │   │   │   ├── pollsVotesAdd.js
│   │   │   │   └── pollsVotesRemove.js
│   │   │   ├── Roles/
│   │   │   │   ├── roleCreate.js
│   │   │   │   ├── roleDelete.js
│   │   │   │   ├── roleColorUpdate.js
│   │   │   │   ├── roleHoistUpdate.js
│   │   │   │   ├── roleMentionableUpdate.js
│   │   │   │   ├── roleNameUpdate.js
│   │   │   │   ├── rolePermissionsUpdate.js
│   │   │   │   └── roleIconUpdate.js
│   │   │   ├── Stage/
│   │   │   │   ├── stageStart.js
│   │   │   │   ├── stageEnd.js
│   │   │   │   ├── stageTopicUpdate.js
│   │   │   │   └── stagePrivacyUpdate.js
│   │   │   ├── Server/
│   │   │   │   ├── banAdd.js
│   │   │   │   ├── banRemove.js
│   │   │   │   ├── userJoin.js
│   │   │   │   ├── userLeave.js
│   │   │   │   ├── userKick.js
│   │   │   │   ├── memberPrune.js
│   │   │   │   ├── afkChannelUpdate.js
│   │   │   │   ├── afkTimeoutUpdate.js
│   │   │   │   ├── serverBannerUpdate.js
│   │   │   │   ├── messageNotificationsUpdate.js
│   │   │   │   ├── serverDiscoverySplashUpdate.js
│   │   │   │   ├── serverContentFilterLevelUpdate.js
│   │   │   │   ├── serverFeaturesUpdate.js
│   │   │   │   ├── serverIconUpdate.js
│   │   │   │   ├── mfaLevelUpdate.js
│   │   │   │   ├── serverNameUpdate.js
│   │   │   │   ├── serverDescriptionUpdate.js
│   │   │   │   ├── serverOwnerUpdate.js
│   │   │   │   ├── partneredUpdate.js
│   │   │   │   ├── serverBoostLevelUpdate.js
│   │   │   │   ├── boostProgressBarToggle.js
│   │   │   │   ├── publicUpdatesChannelUpdate.js
│   │   │   │   ├── serverRulesChannelUpdate.js
│   │   │   │   ├── serverSplashUpdate.js
│   │   │   │   ├── systemChannelUpdate.js
│   │   │   │   ├── serverVanityUpdate.js
│   │   │   │   ├── verificationLevelUpdate.js
│   │   │   │   ├── verifiedUpdate.js
│   │   │   │   ├── serverWidgetUpdate.js
│   │   │   │   ├── serverPreferredLocaleUpdate.js
│   │   │   │   ├── onboardingToggle.js
│   │   │   │   ├── onboardingChannelsUpdate.js
│   │   │   │   ├── onboardingQuestionAdd.js
│   │   │   │   ├── onboardingQuestionRemove.js
│   │   │   │   └── onboardingQuestionUpdate.js
│   │   │   ├── Stickers/
│   |   │   │   ├── stickersCreate.js
│   │   │   │   ├── stickersDelete.js
│   │   │   │   ├── stickersNameUpdate.js
│   │   │   │   ├── stickersDescriptionUpdate.js
│   │   │   │   └── stickersRelatedEmojiUpdate.js
│   │   │   ├── Soundboard/
│   │   │   │   ├── soundboardSoundUpload.js
│   │   │   │   ├── soundboardSoundNameUpdate.js
│   │   │   │   ├── soundboardSoundVolumeUpdate.js
│   │   │   │   ├── soundboardSoundEmojiUpdate.js
│   │   │   │   ├── soundboardSoundDelete.js
│   │   │   ├── Thread/
│   │   │   │   ├── threadCreate.js
│   │   │   │   ├── threadDelete.js
│   │   │   │   ├── threadNameUpdate.js
│   │   │   │   ├── threadSlowModeUpdate.js
│   │   │   │   ├── threadArchiveDurationUpdate.js
│   │   │   │   ├── threadArchive.js
│   │   │   │   ├── threadUnarchive.js
│   │   │   │   ├── threadLock.js
│   │   │   │   ├── threadUnlock.js
│   │   │   ├── User/
│   │   │   │   ├── userNameUpdate.js
│   │   │   │   ├── userRolesUpdate.js
│   │   │   │   ├── userRolesAdd.js
│   │   │   │   ├── userRolesRemove.js
│   │   │   │   ├── userAvatarUpdate.js
│   │   │   │   ├── userTimedOut.js
│   │   │   │   └── userTimeoutRemoved.js
│   │   │   ├── Voice/
│   │   │   │   ├── voiceChannelFull.js
│   │   │   │   ├── voiceUserJoin.js
│   │   │   │   ├── voiceUserSwitch.js
│   │   │   │   ├── voiceUserLeave.js
│   │   │   │   ├── voiceUserMove.js
│   │   │   │   └── voiceUserKick.js
│   │   │   ├── Webhooks/
│   │   │   │   ├── webhooksCreate.js
│   │   │   │   ├── webhooksAvatarUpdate.js
│   │   │   │   ├── webhooksNameUpdate.js
│   │   │   │   ├── webhooksChannelUpdate.js
│   │   │   │   └── webhooksDelete.js
│   │   │   ├── Moderation/
│   │   │   │   ├── autoModeration.js
│   │   │   │   ├── banAdd.js
│   │   │   │   ├── banRemove.js
│   │   │   │   ├── caseDelete.js
│   │   │   │   ├── caseUpdate.js
│   │   │   │   ├── kickAdd.js
│   │   │   │   ├── kickRemove.js
│   │   │   │   ├── muteAdd.js
│   │   │   │   ├── muteRemove.js
│   │   │   │   ├── warnAdd.js
│   │   │   │   ├── warnRemove.js
│   │   │   │   ├── reportCreate.js
│   │   │   │   ├── reportIgnore.js
│   │   │   │   ├── reportAccept.js
│   │   │   │   ├── userNoteAdd.js
└───┴───┴───┴───┴── userNoteRemove.js

- Mỗi file có loại embed, ví dụ như file channelCreate.js
- Phía bên dưới chỉ là bản mẫu một file, dựa vào mà tạo các file còn lại (code bên dưới là của bot khác, tôi chỉ thấy nó đẹp nên cho bạn xem)

```
const discord = require('discord.js');

module.exports = async (client, channel) => {
    let types = {
        0: "Kênh văn bản",
        2: "Kênh thoại",
        4: "Danh mục",
        5: "Kênh tin tức",
        10: "Diễn đàn tin tức",
        11: "Diễn đàn công khai",
        12: "Diễn đàn riêng tư",
        13: "Kênh sân khấu",
        14: "",
    }

    const logsChannel = await client.getLogs(channel.guild.id);
    if (!logsChannel) return;

    console.log(channel.type)
    client.embed({
        title: `🔧〡Thông Báo Chỉnh Sửa`,
        desc: `Vừa có một kênh được tạo`,
        fields: [
            {
                name: `> Tên kênh được tạo`,
                value: `- ${channel.name}`
            },
            {
                name: `> ID kênh`,
                value: `- ${channel.id}`
            },
            {
                name: `> Trong danh mục`,
                value: `- ${channel.parent}`
            },
            {
                name: `> Kênh`,
                value: `- <#${channel.id}>`
            },
            {
                name: `> Loại kênh`,
                value: `- ${types[channel.type]}`
            }                        
        ]
    }, logsChannel).catch(() => { })
};
```

4. Chế độ "phát hiện hành vi bất thường (ví dụ: xóa hàng loạt tin nhắn, cấp vai trò quản trị liên tục) và gửi cảnh báo qua DM owner" 

Nó chưa đủ xịn xò để sài ấy, các nút đều không hoạt động
⤷ Nút hủy còn không hoạt động ấy? Bấm vào nó bảo tinh năng hiện tính năng đang phát triển? 
⤷ Các nút bật/tắt không hoạt động
⤷ Nút điều chỉnh cũng không hoạt động

