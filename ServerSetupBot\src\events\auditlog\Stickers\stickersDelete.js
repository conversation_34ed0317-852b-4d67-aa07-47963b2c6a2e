const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildStickerDelete,
    async execute(sticker, client) {
        try {
            console.log(`🗑️ Sticker deleted: ${sticker.name} in ${sticker.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(sticker.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'STICKERS_DELETE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'STICKERS_DELETE',
                user: 'System',
                userId: null,
                action: 'Sticker được xóa',
                details: `Sticker **${sticker.name}** đã được xóa`,
                target: sticker.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the sticker from audit logs
            try {
                const auditLogs = await sticker.guild.fetchAuditLogs({
                    type: 92, // STICKER_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === sticker.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Sticker được xóa bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for sticker delete');
            }
            
            // Add to database
            await client.db.addAuditLog(sticker.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Sticker được xóa',
                `Vừa có một sticker được xóa khỏi server`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            embed.addFields([
                {
                    name: '> Tên sticker',
                    value: `- ${sticker.name}`,
                    inline: true
                },
                {
                    name: '> ID sticker',
                    value: `- ${sticker.id}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian xóa',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add sticker description that was set
            if (sticker.description) {
                embed.addFields({
                    name: '> Mô tả đã có',
                    value: `- ${sticker.description}`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> Mô tả đã có',
                    value: `- Không có mô tả`,
                    inline: false
                });
            }
            
            // Add sticker format that was used
            const formatTypes = {
                1: 'PNG',
                2: 'APNG',
                3: 'Lottie',
                4: 'GIF'
            };
            
            embed.addFields({
                name: '> Định dạng đã có',
                value: `- ${formatTypes[sticker.format] || 'Unknown'}`,
                inline: true
            });
            
            // Add sticker type
            const stickerTypes = {
                1: 'Standard (Discord)',
                2: 'Guild (Server custom)'
            };
            
            embed.addFields({
                name: '> Loại sticker',
                value: `- ${stickerTypes[sticker.type] || 'Unknown'}`,
                inline: true
            });
            
            // Add sticker tags if they were set
            if (sticker.tags) {
                embed.addFields({
                    name: '> Tags đã có',
                    value: `- ${sticker.tags}`,
                    inline: true
                });
            }
            
            // Add creation info if available
            if (sticker.createdTimestamp) {
                const lifetime = Date.now() - sticker.createdTimestamp;
                const lifetimeDays = Math.floor(lifetime / (1000 * 60 * 60 * 24));
                
                embed.addFields([
                    {
                        name: '> Được tạo lúc',
                        value: `- <t:${Math.floor(sticker.createdTimestamp / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Thời gian tồn tại',
                        value: `- ${lifetimeDays} ngày`,
                        inline: true
                    }
                ]);
            }
            
            // Add impact of deletion
            embed.addFields({
                name: '> 📝 Tác động',
                value: [
                    '• Sticker không thể sử dụng được nữa',
                    '• Tin nhắn cũ vẫn hiển thị sticker',
                    '• Autocomplete sẽ không hiển thị sticker này',
                    '• Giải phóng slot cho sticker mới'
                ].join('\n'),
                inline: false
            });
            
            // Add current sticker count after deletion
            const currentStickerCount = sticker.guild.stickers.cache.size;
            const maxStickers = sticker.guild.premiumTier >= 2 ? 60 : 0;
            
            embed.addFields([
                {
                    name: '> Số sticker còn lại',
                    value: `- ${currentStickerCount}/${maxStickers} stickers`,
                    inline: true
                },
                {
                    name: '> Server Boost Level',
                    value: `- Level ${sticker.guild.premiumTier}`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do xóa',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Add note about sticker recovery
            embed.addFields({
                name: '> ⚠️ Lưu ý',
                value: `- Sticker đã xóa không thể khôi phục, cần tải lên lại nếu muốn sử dụng`,
                inline: false
            });
            
            // Set sticker as thumbnail if URL still available
            if (sticker.url) {
                embed.setThumbnail(sticker.url);
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${sticker.guild.name} • Sticker Delete`,
                iconURL: sticker.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Sticker delete logged: ${sticker.name}`);
            
        } catch (error) {
            console.error('Error in stickersDelete audit log:', error);
        }
    }
};
