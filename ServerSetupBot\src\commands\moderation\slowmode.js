const { SlashCommandBuilder, PermissionFlagsBits, ChannelType } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('slowmode')
        .setDescription('Thiết lập slowmode cho kênh')
        .addIntegerOption(option =>
            option.setName('seconds')
                .setDescription('Thời gian slowmode (giây, 0 để tắt)')
                .setRequired(true)
                .setMinValue(0)
                .setMaxValue(21600) // 6 hours
        )
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Kênh cần thiết lập slowmode (mặc định: kênh hiện tại)')
                .setRequired(false)
                .addChannelTypes(ChannelType.GuildText, ChannelType.GuildNews, ChannelType.GuildNewsThread, ChannelType.GuildPublicThread, ChannelType.GuildPrivateThread)
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Lý do thiết lập slowmode')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                'Bạn cần quyền `Manage Channels` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageChannels)) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                'Bot cần quyền `Manage Channels` để thực hiện lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const seconds = interaction.options.getInteger('seconds');
        const targetChannel = interaction.options.getChannel('channel') || interaction.channel;
        const reason = interaction.options.getString('reason') || 'Không có lý do';
        const guildId = interaction.guild.id;
        
        // Kiểm tra loại kênh
        if (![ChannelType.GuildText, ChannelType.GuildNews, ChannelType.GuildNewsThread, ChannelType.GuildPublicThread, ChannelType.GuildPrivateThread].includes(targetChannel.type)) {
            const errorEmbed = createErrorEmbed(
                'Loại kênh không hỗ trợ!',
                'Slowmode chỉ có thể được thiết lập cho text channels, news channels và threads.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        // Kiểm tra quyền trong kênh cụ thể
        if (!targetChannel.permissionsFor(interaction.member).has(PermissionFlagsBits.ManageChannels)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                `Bạn không có quyền quản lý kênh ${targetChannel}.`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        if (!targetChannel.permissionsFor(interaction.guild.members.me).has(PermissionFlagsBits.ManageChannels)) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                `Bot không có quyền quản lý kênh ${targetChannel}.`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        try {
            const oldSlowmode = targetChannel.rateLimitPerUser;
            
            // Thiết lập slowmode
            await targetChannel.setRateLimitPerUser(seconds, `${reason} | Bởi: ${interaction.user.tag}`);
            
            // Log vào database
            await client.db.addModerationLog(guildId, {
                type: 'slowmode',
                userId: null,
                moderatorId: interaction.user.id,
                reason: reason,
                duration: seconds // Store seconds in duration field
            });
            
            const actionText = seconds === 0 ? 'đã được tắt' : `đã được thiết lập ${formatSlowmodeTime(seconds)}`;
            const oldText = oldSlowmode === 0 ? 'Tắt' : formatSlowmodeTime(oldSlowmode);
            
            const successEmbed = createSuccessEmbed(
                `⏱️ Slowmode ${actionText}!`,
                `**Kênh:** ${targetChannel}\n` +
                `**Slowmode cũ:** ${oldText}\n` +
                `**Slowmode mới:** ${seconds === 0 ? 'Tắt' : formatSlowmodeTime(seconds)}\n` +
                `**Lý do:** ${reason}\n` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            await interaction.reply({ embeds: [successEmbed] });
            
            // Gửi log vào channel (nếu khác channel hiện tại)
            if (targetChannel.id !== interaction.channel.id) {
                await sendModerationLog(client, guildId, {
                    type: 'slowmode',
                    user: { tag: 'System', id: 'system' },
                    moderator: interaction.user,
                    reason: reason,
                    channel: targetChannel,
                    oldValue: oldText,
                    newValue: seconds === 0 ? 'Tắt' : formatSlowmodeTime(seconds)
                });
            }
            
        } catch (error) {
            console.error('Lỗi khi thiết lập slowmode:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi thiết lập slowmode!',
                'Đã xảy ra lỗi khi thiết lập slowmode. Vui lòng thử lại sau!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};

function formatSlowmodeTime(seconds) {
    if (seconds < 60) {
        return `${seconds} giây`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return remainingSeconds > 0 ? `${minutes} phút ${remainingSeconds} giây` : `${minutes} phút`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const remainingMinutes = Math.floor((seconds % 3600) / 60);
        return remainingMinutes > 0 ? `${hours} giờ ${remainingMinutes} phút` : `${hours} giờ`;
    }
}
