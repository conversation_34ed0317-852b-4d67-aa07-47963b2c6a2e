const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.VoiceStateUpdate,
    async execute(oldState, newState, client) {
        try {
            // Only handle voice status changes (not channel changes)
            if (!oldState.channel || !newState.channel) return;
            if (oldState.channel.id !== newState.channel.id) return;
            
            // Check if voice status actually changed
            if (oldState.requestToSpeakTimestamp === newState.requestToSpeakTimestamp &&
                oldState.suppress === newState.suppress) return;
            
            console.log(`🎤 Voice status updated: ${newState.member?.user.tag} in ${newState.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newState.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_VOICE_STATUS_UPDATE')) return;
            
            // Determine what changed
            let statusChange = '';
            if (oldState.requestToSpeakTimestamp !== newState.requestToSpeakTimestamp) {
                if (newState.requestToSpeakTimestamp) {
                    statusChange = 'Yêu cầu phát biểu';
                } else {
                    statusChange = 'Hủy yêu cầu phát biểu';
                }
            } else if (oldState.suppress !== newState.suppress) {
                if (newState.suppress) {
                    statusChange = 'Bị tắt tiếng trong stage';
                } else {
                    statusChange = 'Được phép phát biểu trong stage';
                }
            }
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_VOICE_STATUS_UPDATE',
                user: newState.member?.user.tag || 'Unknown',
                userId: newState.member?.user.id || null,
                action: `Trạng thái voice được cập nhật: ${statusChange}`,
                details: `**${newState.member?.user.tag || 'Unknown'}** đã ${statusChange.toLowerCase()}`,
                target: newState.member?.user.tag || 'Unknown',
                channel: newState.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(newState.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🎤 Trạng thái voice được cập nhật',
                `Vừa có thay đổi trạng thái voice trong kênh`
            );
            
            // Set color based on action
            if (statusChange.includes('Yêu cầu')) {
                embed.setColor(0xf39c12); // Orange for request
            } else if (statusChange.includes('Hủy')) {
                embed.setColor(0x95a5a6); // Gray for cancel
            } else if (statusChange.includes('tắt tiếng')) {
                embed.setColor(0xe74c3c); // Red for suppress
            } else {
                embed.setColor(0x2ecc71); // Green for allow
            }
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${newState.member?.user.tag || 'Unknown'} (${newState.member?.user || 'Unknown'})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newState.member?.user.id || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh voice',
                    value: `- ${newState.channel}`,
                    inline: true
                },
                {
                    name: '> Thay đổi',
                    value: `- ${statusChange}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add channel type info
            const channelTypes = {
                2: 'Voice Channel',
                13: 'Stage Channel'
            };
            
            embed.addFields({
                name: '> Loại kênh',
                value: `- ${channelTypes[newState.channel.type] || 'Unknown'}`,
                inline: true
            });
            
            // Add current voice state
            embed.addFields({
                name: '> Trạng thái voice hiện tại',
                value: [
                    `• Server Mute: ${newState.serverMute ? '🔇 Có' : '🔊 Không'}`,
                    `• Self Mute: ${newState.selfMute ? '🔇 Có' : '🔊 Không'}`,
                    `• Server Deafen: ${newState.serverDeaf ? '🔇 Có' : '🔊 Không'}`,
                    `• Self Deafen: ${newState.selfDeaf ? '🔇 Có' : '🔊 Không'}`,
                    `• Suppress: ${newState.suppress ? '🔇 Có' : '🔊 Không'}`,
                    `• Request to Speak: ${newState.requestToSpeakTimestamp ? '✋ Có' : '❌ Không'}`
                ].join('\n'),
                inline: false
            });
            
            // Add stage-specific info for stage channels
            if (newState.channel.type === 13) {
                embed.addFields({
                    name: '> 🎭 Thông tin Stage Channel',
                    value: [
                        '• Stage channels có hệ thống speaker/audience',
                        '• Chỉ speakers mới có thể nói',
                        '• Audience có thể request to speak',
                        '• Moderators có thể invite lên stage'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add member info
            if (newState.member) {
                const accountAge = Date.now() - newState.member.user.createdTimestamp;
                const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
                
                embed.addFields([
                    {
                        name: '> Tuổi tài khoản',
                        value: `- ${accountAgeDays} ngày`,
                        inline: true
                    },
                    {
                        name: '> Tham gia server lúc',
                        value: `- <t:${Math.floor(newState.member.joinedTimestamp / 1000)}:F>`,
                        inline: true
                    }
                ]);
            }
            
            // Set user avatar as thumbnail
            if (newState.member?.user.displayAvatarURL()) {
                embed.setThumbnail(newState.member.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newState.guild.name} • Channel Voice Status Update`,
                iconURL: newState.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel voice status update logged: ${newState.member?.user.tag}`);
            
        } catch (error) {
            console.error('Error in channelVoiceStatusUpdate audit log:', error);
        }
    }
};
