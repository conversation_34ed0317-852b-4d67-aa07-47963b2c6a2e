const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildBanAdd,
    async execute(ban, client) {
        try {
            console.log(`🔨 User banned: ${ban.user.tag} in ${ban.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(ban.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'BAN_ADD')) return;
            
            // Create event data
            const eventData = {
                eventType: 'BAN_ADD',
                user: 'System',
                userId: null,
                action: 'Thành viên bị ban',
                details: `**${ban.user.tag}** đã bị ban khỏi server`,
                target: ban.user.tag,
                reason: ban.reason,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who banned the user from audit logs
            try {
                const auditLogs = await ban.guild.fetchAuditLogs({
                    type: 22, // MEMBER_BAN_ADD
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === ban.user.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Thành viên bị ban bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for ban add');
            }
            
            // Add to database
            await client.db.addAuditLog(ban.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔨 Thành viên bị ban',
                `Vừa có một thành viên bị ban khỏi server`
            );
            
            embed.setColor(0xe74c3c); // Red for ban
            
            embed.addFields([
                {
                    name: '> Thành viên bị ban',
                    value: `- ${ban.user.tag} (${ban.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${ban.user.id}`,
                    inline: true
                },
                {
                    name: '> Người thực hiện ban',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian ban',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add account info
            const accountAge = Date.now() - ban.user.createdTimestamp;
            const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
            
            embed.addFields([
                {
                    name: '> Tài khoản tạo',
                    value: `- <t:${Math.floor(ban.user.createdTimestamp / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                },
                {
                    name: '> Tổng thành viên còn lại',
                    value: `- ${ban.guild.memberCount} thành viên`,
                    inline: true
                }
            ]);
            
            // Add ban reason
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do ban',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> Lý do ban',
                    value: `- Không có lý do`,
                    inline: false
                });
            }
            
            // Set user avatar as thumbnail
            if (ban.user.displayAvatarURL()) {
                embed.setThumbnail(ban.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${ban.guild.name} • Member Ban`,
                iconURL: ban.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Ban add logged: ${ban.user.tag}`);
            
            // Smart alert for ban
            if (config.smartAlerts) {
                try {
                    const owner = await ban.guild.fetchOwner();
                    const alertEmbed = createInfoEmbed(
                        '🚨 Smart Alert: Member Ban',
                        `Thành viên **${ban.user.tag}** đã bị ban khỏi **${ban.guild.name}**`
                    );
                    
                    alertEmbed.setColor(0xe74c3c);
                    alertEmbed.addFields([
                        { name: '🎯 Loại', value: 'MEMBER_BAN', inline: true },
                        { name: '📊 Mức độ', value: 'MEDIUM', inline: true },
                        { name: '👤 Thực hiện bởi', value: eventData.user, inline: true },
                        { name: '🔨 Người bị ban', value: ban.user.tag, inline: true },
                        { name: '📝 Lý do', value: eventData.reason || 'Không có', inline: true }
                    ]);
                    
                    await owner.send({ embeds: [alertEmbed] });
                } catch (dmError) {
                    console.log('Could not send ban alert to owner');
                }
            }
            
        } catch (error) {
            console.error('Error in banAdd audit log:', error);
        }
    }
};
