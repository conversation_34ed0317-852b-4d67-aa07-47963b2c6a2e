const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Check if permission overwrites were updated
            const oldOverwrites = oldChannel.permissionOverwrites.cache;
            const newOverwrites = newChannel.permissionOverwrites.cache;
            
            const updatedOverwrites = [];
            
            // Find overwrites that exist in both but have different permissions
            for (const [id, newOverwrite] of newOverwrites) {
                const oldOverwrite = oldOverwrites.get(id);
                if (oldOverwrite && 
                    (oldOverwrite.allow.bitfield !== newOverwrite.allow.bitfield || 
                     oldOverwrite.deny.bitfield !== newOverwrite.deny.bitfield)) {
                    updatedOverwrites.push({ old: oldOverwrite, new: newOverwrite });
                }
            }
            
            if (updatedOverwrites.length === 0) return;
            
            console.log(`🔄 Channel permission overwrite updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_OVERWRITE_UPDATE')) return;
            
            // Process each updated overwrite
            for (const { old: oldOverwrite, new: newOverwrite } of updatedOverwrites) {
                // Create event data
                const eventData = {
                    eventType: 'CHANNEL_OVERWRITE_UPDATE',
                    user: 'System',
                    userId: null,
                    action: 'Permission overwrite được cập nhật',
                    details: `Permission overwrite đã được cập nhật cho kênh **${newChannel.name}**`,
                    target: newChannel.name,
                    channel: newChannel.name,
                    timestamp: new Date().toISOString()
                };
                
                // Try to get who updated the overwrite from audit logs
                try {
                    const auditLogs = await newChannel.guild.fetchAuditLogs({
                        type: 14, // CHANNEL_OVERWRITE_UPDATE
                        limit: 1
                    });
                    
                    const auditEntry = auditLogs.entries.first();
                    if (auditEntry && auditEntry.target?.id === newChannel.id) {
                        eventData.user = auditEntry.executor.tag;
                        eventData.userId = auditEntry.executor.id;
                        eventData.action = `Permission overwrite được cập nhật bởi ${auditEntry.executor.tag}`;
                        if (auditEntry.reason) {
                            eventData.reason = auditEntry.reason;
                        }
                    }
                } catch (error) {
                    console.log('Could not fetch audit logs for channel overwrite update');
                }
                
                // Add to database
                await client.db.addAuditLog(newChannel.guild.id, eventData);
                
                // Send to audit log channel
                const auditChannel = await client.channels.fetch(config.channelId);
                if (!auditChannel) continue;
                
                // Create embed
                const embed = createInfoEmbed(
                    '🔄 Permission overwrite được cập nhật',
                    `Vừa có permission overwrite được cập nhật cho kênh`
                );
                
                embed.setColor(0xf39c12); // Orange for update
                
                // Get target info (role or member)
                let targetInfo = 'Unknown';
                let targetType = 'Unknown';
                
                if (newOverwrite.type === 0) { // Role
                    const role = newChannel.guild.roles.cache.get(newOverwrite.id);
                    targetInfo = role ? role.name : `Unknown Role (${newOverwrite.id})`;
                    targetType = 'Role';
                } else if (newOverwrite.type === 1) { // Member
                    const member = newChannel.guild.members.cache.get(newOverwrite.id);
                    targetInfo = member ? member.user.tag : `Unknown Member (${newOverwrite.id})`;
                    targetType = 'Member';
                }
                
                embed.addFields([
                    {
                        name: '> Kênh',
                        value: `- ${newChannel}`,
                        inline: true
                    },
                    {
                        name: '> ID kênh',
                        value: `- ${newChannel.id}`,
                        inline: true
                    },
                    {
                        name: '> Người cập nhật',
                        value: `- ${eventData.user}`,
                        inline: true
                    },
                    {
                        name: '> Đối tượng',
                        value: `- ${targetInfo}`,
                        inline: true
                    },
                    {
                        name: '> Loại đối tượng',
                        value: `- ${targetType}`,
                        inline: true
                    },
                    {
                        name: '> Thời gian',
                        value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                        inline: true
                    }
                ]);
                
                // Compare permissions
                const oldAllowed = oldOverwrite.allow.toArray();
                const newAllowed = newOverwrite.allow.toArray();
                const oldDenied = oldOverwrite.deny.toArray();
                const newDenied = newOverwrite.deny.toArray();
                
                // Find permission changes
                const addedAllowed = newAllowed.filter(perm => !oldAllowed.includes(perm));
                const removedAllowed = oldAllowed.filter(perm => !newAllowed.includes(perm));
                const addedDenied = newDenied.filter(perm => !oldDenied.includes(perm));
                const removedDenied = oldDenied.filter(perm => !newDenied.includes(perm));
                
                // Show permission changes
                if (addedAllowed.length > 0) {
                    embed.addFields({
                        name: '> ✅ Permissions được cho phép (mới)',
                        value: addedAllowed.map(perm => `• ${perm}`).join('\n'),
                        inline: false
                    });
                }
                
                if (removedAllowed.length > 0) {
                    embed.addFields({
                        name: '> ➖ Permissions không còn được cho phép',
                        value: removedAllowed.map(perm => `• ${perm}`).join('\n'),
                        inline: false
                    });
                }
                
                if (addedDenied.length > 0) {
                    embed.addFields({
                        name: '> ❌ Permissions bị từ chối (mới)',
                        value: addedDenied.map(perm => `• ${perm}`).join('\n'),
                        inline: false
                    });
                }
                
                if (removedDenied.length > 0) {
                    embed.addFields({
                        name: '> ➕ Permissions không còn bị từ chối',
                        value: removedDenied.map(perm => `• ${perm}`).join('\n'),
                        inline: false
                    });
                }
                
                if (eventData.reason) {
                    embed.addFields({
                        name: '> Lý do',
                        value: `- ${eventData.reason}`,
                        inline: false
                    });
                }
                
                embed.setTimestamp();
                embed.setFooter({
                    text: `${newChannel.guild.name} • Channel Overwrite Update`,
                    iconURL: newChannel.guild.iconURL()
                });
                
                await auditChannel.send({ embeds: [embed] });
                console.log(`✅ Channel overwrite update logged: ${newChannel.name}`);
            }
            
        } catch (error) {
            console.error('Error in channelOverwriteUpdate audit log:', error);
        }
    }
};
