const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildMemberUpdate,
    async execute(oldMember, newMember, client) {
        try {
            // Only handle timeout additions (was not timed out, now is)
            if (oldMember.communicationDisabledUntil || !newMember.communicationDisabledUntil) return;
            
            console.log(`⏰ User timed out: ${newMember.user.tag} in ${newMember.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newMember.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'USER_TIMED_OUT')) return;
            
            // Create event data
            const eventData = {
                eventType: 'USER_TIMED_OUT',
                user: 'System',
                userId: null,
                action: 'Thành viên bị timeout',
                details: `**${newMember.user.tag}** đã bị timeout`,
                target: newMember.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who timed out the user from audit logs
            try {
                const auditLogs = await newMember.guild.fetchAuditLogs({
                    type: 24, // MEMBER_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newMember.user.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Thành viên bị timeout bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for timeout');
            }
            
            // Add to database
            await client.db.addAuditLog(newMember.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Calculate timeout duration
            const timeoutEnd = newMember.communicationDisabledUntil;
            const timeoutDuration = timeoutEnd - Date.now();
            const durationMinutes = Math.floor(timeoutDuration / (1000 * 60));
            const durationHours = Math.floor(durationMinutes / 60);
            const durationDays = Math.floor(durationHours / 24);
            
            let durationText;
            if (durationDays > 0) {
                durationText = `${durationDays} ngày`;
            } else if (durationHours > 0) {
                durationText = `${durationHours} giờ`;
            } else {
                durationText = `${durationMinutes} phút`;
            }
            
            // Create embed
            const embed = createInfoEmbed(
                '⏰ Thành viên bị timeout',
                `Vừa có thành viên bị timeout trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for timeout
            
            embed.addFields([
                {
                    name: '> Thành viên bị timeout',
                    value: `- ${newMember.user.tag} (${newMember.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newMember.user.id}`,
                    inline: true
                },
                {
                    name: '> Người thực hiện',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian timeout',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Thời gian kết thúc',
                    value: `- <t:${Math.floor(timeoutEnd / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Thời lượng timeout',
                    value: `- ${durationText}`,
                    inline: true
                },
                {
                    name: '> Kết thúc sau',
                    value: `- <t:${Math.floor(timeoutEnd / 1000)}:R>`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do timeout',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> Lý do timeout',
                    value: `- Không có lý do`,
                    inline: false
                });
            }
            
            // Add timeout effects
            embed.addFields({
                name: '> Hiệu ứng timeout',
                value: [
                    '• Không thể gửi tin nhắn',
                    '• Không thể tham gia voice chat',
                    '• Không thể thêm reactions',
                    '• Không thể tạo threads',
                    '• Không thể speak trong stage channels'
                ].join('\n'),
                inline: false
            });
            
            // Set user avatar as thumbnail
            if (newMember.user.displayAvatarURL()) {
                embed.setThumbnail(newMember.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newMember.guild.name} • User Timeout`,
                iconURL: newMember.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ User timeout logged: ${newMember.user.tag}`);
            
        } catch (error) {
            console.error('Error in userTimedOut audit log:', error);
        }
    }
};
