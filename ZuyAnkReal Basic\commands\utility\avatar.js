const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: "avatar",
    aliases: ["av"], // ✅ <PERSON><PERSON> added
    description: "Get the avatar of a user",
    execute(message, args) {
        let user = message.mentions.users.first() || message.author; // ✅ Agar user mention nahi hai toh author ka avatar
        let avatarURL = user.displayAvatarURL({ size: 1024, dynamic: true });

        const embed = new EmbedBuilder()
            .setColor("#2b2d31")
            .setTitle(`${user.username}'s Avatar`)
            .setImage(avatarURL)
            .setFooter({ text: `Requested by ${message.author.username}`, iconURL: message.author.displayAvatarURL({ dynamic: true }) });

        message.channel.send({ embeds: [embed] });
    }
};