const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'webhookUpdate', // Custom event name
    async execute(oldWebhook, newWebhook, client) {
        try {
            // Only handle avatar changes
            if (oldWebhook.avatar === newWebhook.avatar) return;
            
            console.log(`🖼️ Webhook avatar updated: ${newWebhook.name} in ${newWebhook.guild?.name || 'Unknown'}`);
            
            // Skip if no guild (DM webhooks)
            if (!newWebhook.guild) return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newWebhook.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'WEBHOOKS_AVATAR_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'WEBHOOKS_AVATAR_UPDATE',
                user: 'System',
                userId: null,
                action: 'Avatar webhook được cập nhật',
                details: `Avatar của webhook **${newWebhook.name}** đã được cập nhật`,
                target: newWebhook.name,
                channel: newWebhook.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the webhook avatar from audit logs
            try {
                const auditLogs = await newWebhook.guild.fetchAuditLogs({
                    type: 51, // WEBHOOK_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newWebhook.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Avatar webhook được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for webhook avatar update');
            }
            
            // Add to database
            await client.db.addAuditLog(newWebhook.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🖼️ Avatar webhook được cập nhật',
                `Vừa có avatar webhook được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get webhook type names
            const webhookTypes = {
                1: 'Incoming Webhook',
                2: 'Channel Follower Webhook',
                3: 'Application Webhook'
            };
            
            embed.addFields([
                {
                    name: '> Webhook',
                    value: `- ${newWebhook.name || 'Unnamed Webhook'}`,
                    inline: true
                },
                {
                    name: '> ID webhook',
                    value: `- ${newWebhook.id}`,
                    inline: true
                },
                {
                    name: '> Loại webhook',
                    value: `- ${webhookTypes[newWebhook.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${newWebhook.channel || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add avatar URLs
            const oldAvatarURL = oldWebhook.avatarURL({ dynamic: true, size: 256 });
            const newAvatarURL = newWebhook.avatarURL({ dynamic: true, size: 256 });
            
            embed.addFields([
                {
                    name: '> Avatar cũ',
                    value: oldAvatarURL ? `- [Xem avatar cũ](${oldAvatarURL})` : '- Không có avatar',
                    inline: true
                },
                {
                    name: '> Avatar mới',
                    value: newAvatarURL ? `- [Xem avatar mới](${newAvatarURL})` : '- Không có avatar',
                    inline: true
                }
            ]);
            
            // Determine change type
            if (newWebhook.avatar && oldWebhook.avatar) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Thay đổi avatar webhook`,
                    inline: true
                });
                embed.setColor(0x3498db); // Blue for change
            } else if (newWebhook.avatar && !oldWebhook.avatar) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Thiết lập avatar webhook mới`,
                    inline: true
                });
                embed.setColor(0x2ecc71); // Green for new avatar
            } else if (!newWebhook.avatar && oldWebhook.avatar) {
                embed.addFields({
                    name: '> Loại thay đổi',
                    value: `- Xóa avatar webhook`,
                    inline: true
                });
                embed.setColor(0xe74c3c); // Red for removed avatar
            }
            
            // Add webhook owner info if available
            if (newWebhook.owner) {
                embed.addFields({
                    name: '> Chủ sở hữu',
                    value: `- ${newWebhook.owner.tag || newWebhook.owner.name}`,
                    inline: true
                });
            }
            
            // Add application info if it's an application webhook
            if (newWebhook.application) {
                embed.addFields({
                    name: '> Ứng dụng',
                    value: `- ${newWebhook.application.name}`,
                    inline: true
                });
            }
            
            // Add creation info if available
            if (newWebhook.createdTimestamp) {
                embed.addFields({
                    name: '> Được tạo lúc',
                    value: `- <t:${Math.floor(newWebhook.createdTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add impact information
            if (newWebhook.avatar && !oldWebhook.avatar) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Webhook giờ đây có avatar riêng',
                        '• Tin nhắn từ webhook sẽ hiển thị avatar này',
                        '• Tăng tính nhận diện cho webhook',
                        '• Dễ phân biệt với các webhook khác'
                    ].join('\n'),
                    inline: false
                });
            } else if (!newWebhook.avatar && oldWebhook.avatar) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Webhook không còn avatar riêng',
                        '• Tin nhắn sẽ hiển thị avatar mặc định',
                        '• Khó phân biệt với webhook khác',
                        '• Giao diện đơn giản hơn'
                    ].join('\n'),
                    inline: false
                });
            } else if (newWebhook.avatar && oldWebhook.avatar) {
                embed.addFields({
                    name: '> 🔄 Tác động',
                    value: [
                        '• Avatar webhook đã được thay đổi',
                        '• Tin nhắn mới sẽ hiển thị avatar mới',
                        '• Cập nhật hình ảnh thương hiệu',
                        '• Có thể cần thời gian để người dùng quen'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add webhook URL info (partially hidden for security)
            if (newWebhook.url) {
                const hiddenUrl = `https://discord.com/api/webhooks/${newWebhook.id}/***`;
                embed.addFields({
                    name: '> URL webhook',
                    value: `- ${hiddenUrl}`,
                    inline: false
                });
            }
            
            // Add security note
            embed.addFields({
                name: '> 🔒 Bảo mật',
                value: `- Avatar webhook có thể ảnh hưởng đến cách người dùng nhận biết tin nhắn`,
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set new avatar as thumbnail if available
            if (newAvatarURL) {
                embed.setThumbnail(newAvatarURL);
            } else if (oldAvatarURL) {
                embed.setThumbnail(oldAvatarURL);
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newWebhook.guild.name} • Webhook Avatar Update`,
                iconURL: newWebhook.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Webhook avatar update logged: ${newWebhook.name}`);
            
        } catch (error) {
            console.error('Error in webhooksAvatarUpdate audit log:', error);
        }
    }
};
