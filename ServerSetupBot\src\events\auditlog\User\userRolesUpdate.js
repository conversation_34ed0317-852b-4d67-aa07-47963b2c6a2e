const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildMemberUpdate,
    async execute(oldMember, newMember, client) {
        try {
            // Only handle role changes
            if (oldMember.roles.cache.size === newMember.roles.cache.size && 
                oldMember.roles.cache.every(role => newMember.roles.cache.has(role.id))) {
                return;
            }
            
            console.log(`🎭 Member roles updated: ${newMember.user.tag} in ${newMember.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newMember.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'USER_ROLES_UPDATE')) return;
            
            // Calculate role changes
            const oldRoles = oldMember.roles.cache;
            const newRoles = newMember.roles.cache;
            
            const addedRoles = newRoles.filter(role => !oldRoles.has(role.id));
            const removedRoles = oldRoles.filter(role => !newRoles.has(role.id));
            
            // Create event data
            const eventData = {
                eventType: 'USER_ROLES_UPDATE',
                user: 'System',
                userId: null,
                action: 'Roles thành viên được cập nhật',
                details: `Roles của **${newMember.user.tag}** đã được cập nhật`,
                target: newMember.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the roles from audit logs
            try {
                const auditLogs = await newMember.guild.fetchAuditLogs({
                    type: 25, // MEMBER_ROLE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newMember.user.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Roles được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for role update');
            }
            
            // Add to database
            await client.db.addAuditLog(newMember.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🎭 Roles thành viên cập nhật',
                `Vừa có roles thành viên được cập nhật trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${newMember.user.tag} (${newMember.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newMember.user.id}`,
                    inline: true
                },
                {
                    name: '> Người thực hiện',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add role changes
            if (addedRoles.size > 0) {
                const addedRolesList = addedRoles.map(role => `<@&${role.id}>`).join(', ');
                embed.addFields({
                    name: '> ✅ Roles được thêm',
                    value: addedRolesList.length > 1000 ? addedRolesList.substring(0, 1000) + '...' : addedRolesList,
                    inline: false
                });
            }
            
            if (removedRoles.size > 0) {
                const removedRolesList = removedRoles.map(role => `~~${role.name}~~`).join(', ');
                embed.addFields({
                    name: '> ❌ Roles được xóa',
                    value: removedRolesList.length > 1000 ? removedRolesList.substring(0, 1000) + '...' : removedRolesList,
                    inline: false
                });
            }
            
            // Add current roles count
            embed.addFields({
                name: '> 📊 Tổng roles hiện tại',
                value: `- ${newRoles.size - 1} roles`, // -1 for @everyone
                inline: true
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set user avatar as thumbnail
            if (newMember.user.displayAvatarURL()) {
                embed.setThumbnail(newMember.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newMember.guild.name} • User Roles Update`,
                iconURL: newMember.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ User roles update logged: ${newMember.user.tag}`);
            
        } catch (error) {
            console.error('Error in userRolesUpdate audit log:', error);
        }
    }
};
