const { SlashCommandBuilder } = require("discord.js");
const User = require("../models/User");
const { parseAmount, validateAmount } = require("../utils/economy");
const {
  createSuccessEmbed,
  createErrorEmbed,
  formatCurrency,
} = require("../utils/embedBuilder");
const config = require("../config/config");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("bank")
    .setDescription("Quản lý tài khoản ngân hàng")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("deposit")
        .setDescription("Gửi tiền vào ngân hàng")
        .addStringOption((option) =>
          option
            .setName("amount")
            .setDescription(
              "Số tiền cần gửi (có thể dùng: all, half, 1k, 1m, v.v.)"
            )
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("withdraw")
        .setDescription("Rút tiền từ ngân hàng")
        .addStringOption((option) =>
          option
            .setName("amount")
            .setDescription(
              "Số tiền cần rút (có thể dùng: all, half, 1k, 1m, v.v.)"
            )
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("info")
        .setDescription("Xem thông tin tài khoản ngân hàng")
    ),

  cooldown: 3,

  async execute(interaction) {
    try {
      const subcommand = interaction.options.getSubcommand();
      const userData = await User.findOrCreate(interaction.user);

      switch (subcommand) {
        case "deposit":
          await handleDeposit(interaction, userData);
          break;
        case "withdraw":
          await handleWithdraw(interaction, userData);
          break;
        case "info":
          await handleInfo(interaction, userData);
          break;
      }
    } catch (error) {
      console.error("Lỗi lệnh bank:", error);

      const errorEmbed = createErrorEmbed(
        "Lỗi ngân hàng",
        "Không thể thực hiện giao dịch ngân hàng. Vui lòng thử lại sau."
      );

      await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  },
};

async function handleDeposit(interaction, userData) {
  const amountStr = interaction.options.getString("amount");

  try {
    // Parse số tiền
    let amount;
    const parsedAmount = parseAmount(amountStr);

    if (parsedAmount === "all") {
      amount = userData.balance;
    } else if (parsedAmount === "half") {
      amount = Math.floor(userData.balance / 2);
    } else {
      amount = parsedAmount;
    }

    // Validate số tiền
    amount = validateAmount(amount, userData.balance);

    if (amount < 1) {
      const errorEmbed = createErrorEmbed(
        "Số tiền quá nhỏ",
        "Số tiền gửi phải ít nhất là 1 xu!"
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    // Thực hiện gửi tiền
    const result = await userData.deposit(amount);

    const successEmbed = createSuccessEmbed(
      "Gửi tiền thành công!",
      `Bạn đã gửi **${formatCurrency(amount)}** vào ngân hàng\n\n` +
        `${config.emojis.money} **Số dư ví:** ${formatCurrency(
          result.balance
        )}\n` +
        `${config.emojis.bank} **Số dư ngân hàng:** ${formatCurrency(
          result.bank
        )}`
    );

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    const errorEmbed = createErrorEmbed(
      "Lỗi gửi tiền",
      error.message || "Không thể gửi tiền vào ngân hàng"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleWithdraw(interaction, userData) {
  const amountStr = interaction.options.getString("amount");

  try {
    // Parse số tiền
    let amount;
    const parsedAmount = parseAmount(amountStr);

    if (parsedAmount === "all") {
      amount = userData.bank;
    } else if (parsedAmount === "half") {
      amount = Math.floor(userData.bank / 2);
    } else {
      amount = parsedAmount;
    }

    // Validate số tiền
    amount = validateAmount(amount, userData.bank);

    if (amount < 1) {
      const errorEmbed = createErrorEmbed(
        "Số tiền quá nhỏ",
        "Số tiền rút phải ít nhất là 1 xu!"
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    // Thực hiện rút tiền
    const result = await userData.withdraw(amount);

    const successEmbed = createSuccessEmbed(
      "Rút tiền thành công!",
      `Bạn đã rút **${formatCurrency(amount)}** từ ngân hàng\n\n` +
        `${config.emojis.money} **Số dư ví:** ${formatCurrency(
          result.balance
        )}\n` +
        `${config.emojis.bank} **Số dư ngân hàng:** ${formatCurrency(
          result.bank
        )}`
    );

    await interaction.reply({ embeds: [successEmbed] });
  } catch (error) {
    const errorEmbed = createErrorEmbed(
      "Lỗi rút tiền",
      error.message || "Không thể rút tiền từ ngân hàng"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleInfo(interaction, userData) {
  const totalWealth = userData.balance + userData.bank;
  const bankPercentage =
    totalWealth > 0 ? ((userData.bank / totalWealth) * 100).toFixed(1) : 0;

  const infoEmbed = createSuccessEmbed(
    "Thông tin ngân hàng",
    `${config.emojis.bank} **Tài khoản của ${interaction.user.username}**`
  );

  infoEmbed.addFields(
    {
      name: `${config.emojis.money} Số dư ví`,
      value: formatCurrency(userData.balance),
      inline: true,
    },
    {
      name: `${config.emojis.bank} Số dư ngân hàng`,
      value: formatCurrency(userData.bank),
      inline: true,
    },
    {
      name: `${config.emojis.gem} Tổng tài sản`,
      value: formatCurrency(totalWealth),
      inline: true,
    },
    {
      name: `${config.emojis.info} Thống kê`,
      value:
        `**Tỷ lệ tiết kiệm:** ${bankPercentage}%\n` +
        `**Lãi suất:** 0.1%/ngày\n` +
        `**Bảo hiểm:** Có`,
      inline: false,
    }
  );

  await interaction.reply({ embeds: [infoEmbed] });
}
