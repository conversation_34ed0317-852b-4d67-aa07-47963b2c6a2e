# ________________________________________________
#
#         CHAMPION SETUPS COMMANDS FILE
#
# Here you can commands configuration like required role,
# description, aliases and similar.
#
# ________________________________________________

# Command Name
create:
  # Role(s) Required to run Command, leave empty for none
  # User need at least one role from list.
  roles: []
  # Command Aliases, leave [] for none,
  # Example:
  # aliases: ["setup"]
  aliases: ["setup"]
  # Cooldown for Command in seconds
  cooldown: 0
  # Discord Permission Required to run Command, leave [] for none
  # Example:
  # permissions: ["ADMINISTRATOR"]
  permissions: ["ManageChannels"]
  # Command Description
  description: "Create Server (or Channels/Roles/Emojis) from Template"
  # Command Usage
  usage: "create [Template Name]"
  # Whether Response of Command is only visible to User who run command (Only Slash Commands!)
  # Not Present on All Commands!
  # !! NOT AVAILABLE FOR THIS COMMAND, THIS IS JUST REFERENCE !!
  # ephemeral: false
  # Is Command Enabled
  enabled: true
delete:
  roles: []
  aliases: []
  cooldown: 0
  permissions: ["ManageChannels"]
  description: "Delete some aspects of server"
  usage: "delete"
  enabled: true
templates:
  roles: []
  aliases: []
  cooldown: 0
  permissions: ["ManageChannels"]
  description: "List of All Templates"
  usage: "templates"
  ephemeral: false
  enabled: true
info:
  roles: []
  aliases: []
  cooldown: 0
  permissions: ["ManageChannels"]
  description: "Get Template Info"
  usage: "info [Template Name]"
  ephemeral: false
  enabled: true
ping:
  roles: []
  aliases: []
  cooldown: 0
  permissions: []
  description: "Get Bot Ping"
  usage: "ping"
  enabled: true
help:
  roles: []
  aliases: []
  cooldown: 0
  permissions: []
  description: "View Commands"
  usage: "help [Command]"
  ephemeral: false
  enabled: true
embed:
  roles: []
  aliases: []
  permissions: ["ManageChannels"]
  description: "Send Embed to Channel"
  # First three parameters are optional, description is required
  usage: "embed [-t Title] [-c #HEX Color] [-i Thumbnail URL] [Description]"
  ephemeral: false
  cooldown: 0
  enabled: true
editchannel:
  roles: []
  aliases: []
  cooldown: 0
  permissions: []
  description: "Edit Channel"
  usage: "editchannel <name/position/topic/parent> <value>"
  enabled: true
invitebot:
  roles: []
  aliases: []
  cooldown: 0
  permissions: []
  description: "Invite Bot with name"
  usage: "invitebot <name>"
  ephemeral: false
  enabled: true
searchbot:
  roles: []
  aliases: []
  cooldown: 0
  permissions: []
  description: "Search for Bots by name/tag"
  usage: "searchbot <name/term>"
  ephemeral: false
  enabled: true
symbols:
  roles: []
  aliases: []
  cooldown: 0
  permissions: []
  description: "Get list of Symbols"
  usage: "symbols <line/bracket/arrow/other>"
  ephemeral: false
  enabled: true
save:
  roles: []
  aliases: []
  cooldown: 0
  permissions: []
  description: "Save Server as Template"
  usage: "save"
  ephemeral: false
  enabled: true
id:
  roles: []
  aliases: []
  cooldown: 0
  permissions: []
  description: "Get ID of Channel/User/Role"
  usage: "id <@User/#Channel/@Role>"
  ephemeral: false
  enabled: true
roleinfo:
  roles: []
  aliases: []
  cooldown: 0
  permissions: []
  description: "Get Role Informations"
  usage: "roleinfo <@Role>"
  ephemeral: false
  enabled: true
transcript:
  roles: []
  aliases: []
  cooldown: 0
  permissions: []
  description: "Get Transcript of Channel"
  usage: "transcript <#Channel>"
  ephemeral: false
  enabled: true
eval:
  roles: []
  aliases: []
  # For this Command you also need your ID to be
  # added into 'eval' field in config.yml
  cooldown: 0
  permissions: ["ADMINISTRATOR"]
  description: "Evaulate JS Code"
  usage: "eval [JS Code]"
  enabled: true

# 0056XLTQVWY51IROC