const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildMemberUpdate,
    async execute(oldMember, newMember, client) {
        try {
            // Only handle timeout removal (was timed out, now is not)
            if (!oldMember.communicationDisabledUntil || newMember.communicationDisabledUntil) return;
            
            console.log(`⏰ User timeout removed: ${newMember.user.tag} in ${newMember.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newMember.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'USER_TIMEOUT_REMOVED')) return;
            
            // Create event data
            const eventData = {
                eventType: 'USER_TIMEOUT_REMOVED',
                user: 'System',
                userId: null,
                action: 'Timeout thành viên đ<PERSON> gỡ bỏ',
                details: `Timeout của **${newMember.user.tag}** đã được gỡ bỏ`,
                target: newMember.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who removed the timeout from audit logs
            try {
                const auditLogs = await newMember.guild.fetchAuditLogs({
                    type: 24, // MEMBER_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newMember.user.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Timeout được gỡ bỏ bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for timeout removal');
            }
            
            // Add to database
            await client.db.addAuditLog(newMember.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Calculate timeout duration that was served
            const timeoutEnd = oldMember.communicationDisabledUntil;
            const timeoutDuration = timeoutEnd - oldMember.communicationDisabledTimestamp;
            const timeServed = Date.now() - oldMember.communicationDisabledTimestamp;
            
            const formatDuration = (ms) => {
                const days = Math.floor(ms / (1000 * 60 * 60 * 24));
                const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
                
                if (days > 0) return `${days} ngày, ${hours} giờ`;
                if (hours > 0) return `${hours} giờ, ${minutes} phút`;
                return `${minutes} phút`;
            };
            
            // Create embed
            const embed = createInfoEmbed(
                '⏰ Timeout thành viên được gỡ bỏ',
                `Vừa có timeout thành viên được gỡ bỏ trong server`
            );
            
            embed.setColor(0x2ecc71); // Green for removal/restore
            
            embed.addFields([
                {
                    name: '> Thành viên',
                    value: `- ${newMember.user.tag} (${newMember.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${newMember.user.id}`,
                    inline: true
                },
                {
                    name: '> Người gỡ bỏ',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian gỡ bỏ',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add timeout details
            if (oldMember.communicationDisabledTimestamp) {
                embed.addFields([
                    {
                        name: '> Timeout bắt đầu',
                        value: `- <t:${Math.floor(oldMember.communicationDisabledTimestamp / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Đã timeout được',
                        value: `- ${formatDuration(timeServed)}`,
                        inline: true
                    }
                ]);
            }
            
            if (timeoutEnd) {
                const wasEarly = Date.now() < timeoutEnd;
                embed.addFields([
                    {
                        name: '> Dự kiến kết thúc',
                        value: `- <t:${Math.floor(timeoutEnd / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Loại gỡ bỏ',
                        value: `- ${wasEarly ? 'Gỡ bỏ sớm' : 'Hết hạn tự động'}`,
                        inline: true
                    }
                ]);
                
                if (wasEarly) {
                    const remainingTime = timeoutEnd - Date.now();
                    embed.addFields({
                        name: '> Thời gian còn lại',
                        value: `- ${formatDuration(remainingTime)}`,
                        inline: true
                    });
                }
            }
            
            // Add restored permissions
            embed.addFields({
                name: '> Quyền được khôi phục',
                value: [
                    '• Có thể gửi tin nhắn',
                    '• Có thể tham gia voice chat',
                    '• Có thể thêm reactions',
                    '• Có thể tạo threads',
                    '• Có thể speak trong stage channels'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do gỡ bỏ',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set user avatar as thumbnail
            if (newMember.user.displayAvatarURL()) {
                embed.setThumbnail(newMember.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newMember.guild.name} • User Timeout Removed`,
                iconURL: newMember.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ User timeout removal logged: ${newMember.user.tag}`);
            
        } catch (error) {
            console.error('Error in userTimeoutRemoved audit log:', error);
        }
    }
};
