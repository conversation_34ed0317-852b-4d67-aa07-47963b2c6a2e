const { SlashCommandBuilder } = require('discord.js');
const { createHelpEmbed, createInfoEmbed } = require('../../utils/embedBuilder.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('help')
        .setDescription('Hiển thị hướng dẫn sử dụng bot và danh sách lệnh')
        .addStringOption(option =>
            option.setName('command')
                .setDescription('Tên lệnh cần xem chi tiết')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('category')
                .setDescription('Danh mục lệnh cần xem')
                .setRequired(false)
                .addChoices(
                    { name: '👋 Welcome/Goodbye', value: 'welcome' },
                    { name: '🎭 Auto Roles', value: 'autoroles' },
                    { name: '📝 Channel Management', value: 'channels' },
                    { name: '🔒 Permissions', value: 'permissions' },
                    { name: '✅ Verification', value: 'verification' },
                    { name: '🛡️ Moderation', value: 'moderation' },
                    { name: '📋 Templates', value: 'templates' },
                    { name: '💾 Backup', value: 'backup' },
                    { name: '⚙️ Utility', value: 'utility' }
                )
        ),
    category: 'utility',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        const commandName = interaction.options.getString('command');
        const category = interaction.options.getString('category');
        
        // Nếu có command cụ thể
        if (commandName) {
            const command = client.commands.get(commandName);
            
            if (!command) {
                const errorEmbed = createInfoEmbed(
                    'Lệnh không tồn tại',
                    `Không tìm thấy lệnh \`${commandName}\`.\n\nSử dụng \`/help\` để xem tất cả lệnh có sẵn.`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Hiển thị chi tiết lệnh
            const embed = createInfoEmbed(
                `Lệnh: /${command.data.name}`,
                `**Mô tả:** ${command.data.description}\n\n` +
                `**Danh mục:** ${command.category || 'Không xác định'}\n` +
                `**Quyền yêu cầu:** ${command.adminOnly ? 'Quản trị viên' : command.manageServer ? 'Quản lý máy chủ' : 'Thành viên'}`
            );
            
            // Thêm thông tin về options nếu có
            if (command.data.options && command.data.options.length > 0) {
                const optionsText = command.data.options.map(option => {
                    const required = option.required ? '**[Bắt buộc]**' : '*[Tùy chọn]*';
                    return `• **${option.name}** ${required}: ${option.description}`;
                }).join('\n');
                
                embed.addFields({
                    name: '📝 Tham số',
                    value: optionsText,
                    inline: false
                });
            }
            
            embed.setFooter({ 
                text: `Sử dụng: /${command.data.name}`,
                iconURL: client.user.displayAvatarURL()
            });
            
            return await interaction.reply({ embeds: [embed] });
        }
        
        // Lấy tất cả commands
        const commands = Array.from(client.commands.values());
        
        // Tạo help embed
        const helpEmbed = createHelpEmbed(commands, category);
        helpEmbed.setThumbnail(client.user.displayAvatarURL());
        helpEmbed.setFooter({ 
            text: `${client.config.text.botName} • Phiên bản 1.0.0`,
            iconURL: client.user.displayAvatarURL()
        });
        
        // Nếu có category cụ thể
        if (category) {
            const categoryCommands = commands.filter(cmd => cmd.category === category);
            
            if (categoryCommands.length === 0) {
                const errorEmbed = createInfoEmbed(
                    'Danh mục trống',
                    `Danh mục \`${category}\` chưa có lệnh nào hoặc không tồn tại.`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Tạo embed cho category
            const categoryNames = {
                'welcome': '👋 Welcome/Goodbye',
                'autoroles': '🎭 Auto Roles',
                'channels': '📝 Channel Management',
                'permissions': '🔒 Permissions',
                'verification': '✅ Verification',
                'moderation': '🛡️ Moderation',
                'templates': '📋 Templates',
                'backup': '💾 Backup',
                'utility': '⚙️ Utility'
            };
            
            const embed = createInfoEmbed(
                `Danh sách lệnh - ${categoryNames[category]}`,
                `Dưới đây là tất cả lệnh trong danh mục **${categoryNames[category]}**:`
            );
            
            const commandList = categoryCommands.map(cmd => {
                const permissions = cmd.adminOnly ? '🔒' : cmd.manageServer ? '⚙️' : '👤';
                return `${permissions} **/${cmd.data.name}** - ${cmd.data.description}`;
            }).join('\n');
            
            embed.addFields({
                name: '📋 Lệnh có sẵn',
                value: commandList || 'Không có lệnh nào',
                inline: false
            });
            
            embed.addFields({
                name: '🔑 Ký hiệu quyền',
                value: '🔒 Quản trị viên • ⚙️ Quản lý máy chủ • 👤 Thành viên',
                inline: false
            });
            
            embed.setThumbnail(client.user.displayAvatarURL());
            embed.setFooter({ 
                text: `Sử dụng /help [tên_lệnh] để xem chi tiết`,
                iconURL: client.user.displayAvatarURL()
            });
            
            return await interaction.reply({ embeds: [embed] });
        }
        
        // Hiển thị help tổng quát
        await interaction.reply({ embeds: [helpEmbed] });
    },
};
