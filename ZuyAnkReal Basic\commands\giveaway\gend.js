const fs = require('fs');
const path = require('path');

const filePath = path.resolve(__dirname, '../../data/giveaway.json');

module.exports = {
  name: 'gend',
  description: 'End a giveaway early',

  async execute(message, args) {
    const msgId = args[0];
    if (!msgId) return message.reply('Usage: `gend <messageID>`');

    const giveaways = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    const gw = giveaways.find(g => g.messageId === msgId);

    if (!gw) return message.reply('Giveaway not found!');
    if (gw.ended) return message.reply('This giveaway has already ended.');

    gw.endTime = Date.now(); // End it now
    fs.writeFileSync(filePath, JSON.stringify(giveaways, null, 2));

    message.reply('Giveaway will end shortly.');
  }
};