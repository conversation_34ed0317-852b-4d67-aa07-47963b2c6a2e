(function(_0x2968b2,_0x2eed87){function _0x49dcec(_0x201c8b,_0x1ba289,_0x492d5f,_0x25e5a1,_0x582362){return _0x42f1(_0x201c8b-0x2ef,_0x582362);}function _0x4166a7(_0x2aa6ab,_0x25a8f7,_0x63966d,_0x3b270a,_0x4fcacf){return _0x42f1(_0x25a8f7- -0x1e3,_0x3b270a);}const _0x140db8=_0x2968b2();while(!![]){try{const _0x3ee384=parseInt(_0x4166a7(0x3f,0x4b,0x40,0x3e,0x1c))/0x1*(parseInt(_0x49dcec(0x4ec,0x4e2,0x510,0x4f5,0x4d7))/0x2)+parseInt(_0x49dcec(0x4f9,0x529,0x4e6,0x4f0,0x4ef))/0x3+parseInt(_0x4166a7(0x2e,0x36,0x60,0x51,0x14))/0x4+-parseInt(_0x49dcec(0x4ed,0x4e6,0x51c,0x4d8,0x50e))/0x5+parseInt(_0x4166a7(0x15,0x18,0x2,0x33,0x0))/0x6*(parseInt(_0x4166a7(-0x13,0xf,0x6,0x39,0x5))/0x7)+parseInt(_0x49dcec(0x51b,0x4f3,0x536,0x514,0x545))/0x8+parseInt(_0x49dcec(0x502,0x4f1,0x4e0,0x506,0x50f))/0x9*(-parseInt(_0x4166a7(-0x16,0x19,0x3e,0x48,0x32))/0xa);if(_0x3ee384===_0x2eed87)break;else _0x140db8['push'](_0x140db8['shift']());}catch(_0x2793c7){_0x140db8['push'](_0x140db8['shift']());}}}(_0x470a,0x56028));const _0x4bf829=(function(){let _0x224f0f=!![];return function(_0x4d27a0,_0x3cc191){const _0x4c6b13=_0x224f0f?function(){function _0x59ad58(_0x1c964c,_0x524767,_0x89eb18,_0x5d00f2,_0x2c2518){return _0x42f1(_0x524767-0x18b,_0x1c964c);}function _0x5a38f6(_0x2d4050,_0x5b5c45,_0x2cbb07,_0x1d3cd3,_0x1967fa){return _0x42f1(_0x1d3cd3-0x2d0,_0x2d4050);}if(_0x3cc191){if(_0x5a38f6(0x4fe,0x514,0x4e8,0x4ec,0x4f3)!=='\x47\x58\x70\x77\x6a'){const _0x51fd78=_0x3cc191[_0x5a38f6(0x4cc,0x4e8,0x4b6,0x4d2,0x4d2)](_0x4d27a0,arguments);return _0x3cc191=null,_0x51fd78;}else _0x3fd173();}}:function(){};return _0x224f0f=![],_0x4c6b13;};}()),_0x3a2bcc=_0x4bf829(this,function(){function _0x528a28(_0x2dcdfe,_0x2762b6,_0x43ee6b,_0x520b99,_0x4d7206){return _0x42f1(_0x2762b6- -0x39a,_0x520b99);}function _0x3c1d4c(_0x6c89ac,_0x235104,_0x3bd4f1,_0x12fcfb,_0x325d48){return _0x42f1(_0x235104-0x19e,_0x6c89ac);}return _0x3a2bcc[_0x3c1d4c(0x374,0x382,0x35e,0x387,0x36a)]()['\x73\x65\x61\x72\x63\x68']('\x28\x28\x28\x2e\x2b\x29\x2b\x29\x2b\x29'+'\x2b\x24')['\x74\x6f\x53\x74\x72\x69\x6e\x67']()[_0x528a28(-0x1aa,-0x194,-0x16f,-0x170,-0x1a7)+'\x72'](_0x3a2bcc)[_0x3c1d4c(0x38c,0x3a9,0x3b2,0x37b,0x3ba)](_0x528a28(-0x16c,-0x19b,-0x18a,-0x176,-0x17d)+'\x2b\x24');});_0x3a2bcc();const _0xef0480=(function(){let _0x93e5ba=!![];return function(_0x546c83,_0x41a739){const _0x294bd5=_0x93e5ba?function(){function _0x11789b(_0x18d4f6,_0x47eef2,_0x49bb01,_0x2bc60b,_0x55674e){return _0x42f1(_0x47eef2- -0x140,_0x55674e);}if(_0x41a739){const _0x1bc76d=_0x41a739[_0x11789b(0x96,0xc2,0xb4,0xd1,0xeb)](_0x546c83,arguments);return _0x41a739=null,_0x1bc76d;}}:function(){};return _0x93e5ba=![],_0x294bd5;};}());(function(){_0xef0480(this,function(){const _0x1a67de=new RegExp(_0xa638e1(0x6,0x35,0x55,0x1a,0x31)+'\x5c\x28\x20\x2a\x5c\x29');function _0xa638e1(_0x14956c,_0x2b3b3f,_0x54f151,_0x7959ad,_0x15c651){return _0x42f1(_0x15c651- -0x1c2,_0x14956c);}function _0x6aabad(_0x2dae0c,_0x4444e9,_0x12f869,_0xf92432,_0x29cc5a){return _0x42f1(_0x2dae0c- -0xe,_0x4444e9);}const _0x1fb327=new RegExp('\x5c\x2b\x5c\x2b\x20\x2a\x28\x3f\x3a\x5b'+_0x6aabad(0x1dc,0x1ee,0x1f4,0x1dd,0x1e0)+_0x6aabad(0x207,0x1e8,0x221,0x232,0x212)+'\x24\x5d\x2a\x29','\x69'),_0x51fde9=_0xbe7dc7(_0xa638e1(0x15,0x25,0x3f,0x16,0x38));!_0x1a67de['\x74\x65\x73\x74'](_0x51fde9+_0xa638e1(0x33,0x89,0x5f,0x5d,0x5c))||!_0x1fb327[_0xa638e1(0x27,0x30,0x41,0x41,0x2a)](_0x51fde9+_0x6aabad(0x1ff,0x209,0x1eb,0x1cf,0x1f2))?_0x51fde9('\x30'):_0xbe7dc7();})();}());function _0x42f1(_0x58a490,_0x168f39){const _0x3d2cfa=_0x470a();return _0x42f1=function(_0xbe7dc7,_0xef0480){_0xbe7dc7=_0xbe7dc7-0x1d7;let _0x3d2ab6=_0x3d2cfa[_0xbe7dc7];return _0x3d2ab6;},_0x42f1(_0x58a490,_0x168f39);}const {EmbedBuilder,AuditLogEvent}=require(_0x39441b(0x3e8,0x405,0x3d8,0x3d3,0x3c0));function _0x39441b(_0x17d2f9,_0x559de6,_0x4e52bc,_0x409ff0,_0x4db548){return _0x42f1(_0x17d2f9-0x1f0,_0x409ff0);}const fs=require('\x66\x73'),yaml=require(_0x3e4bc3(-0x158,-0x13f,-0x13d,-0x146,-0x139)),moment=require(_0x3e4bc3(-0x178,-0x178,-0x137,-0x15d,-0x12e)+_0x39441b(0x41d,0x432,0x446,0x40a,0x447));function _0x470a(){const _0x50fac5=['\x70\x72\x6f\x76\x69\x64\x65\x64','\x63\x6f\x6e\x73\x74\x72\x75\x63\x74\x6f','\x65\x72\x72\x6f\x72','\x73\x74\x61\x74\x65\x4f\x62\x6a\x65\x63','\x72\x65\x70\x6c\x61\x63\x65','\x36\x33\x36\x32\x37\x30\x51\x75\x6e\x5a\x73\x75','\x73\x65\x61\x72\x63\x68','\x67\x75\x69\x6c\x64\x44\x61\x74\x61\x53','\x69\x6e\x70\x75\x74','\x4b\x56\x58\x49\x6d','\x74\x79\x70\x65','\x67\x75\x69\x6c\x64\x49\x44','\x75\x74\x66\x38','\x64\x65\x62\x75','\x39\x75\x47\x75\x43\x46\x73','\x24\x69\x6e\x63','\x30\x2d\x39\x61\x2d\x7a\x41\x2d\x5a\x5f','\x73\x65\x74\x46\x6f\x6f\x74\x65\x72','\x66\x6f\x72\x6d\x61\x74','\x74\x69\x6f\x6e','\x31\x36\x33\x38\x39\x31\x36\x64\x61\x45\x7a\x64\x67','\x73\x65\x6e\x64','\x63\x68\x65\x6d\x61','\x57\x54\x4c\x51\x44','\x6c\x69\x6e\x67\x20\x75\x6e\x62\x61\x6e','\x63\x68\x61\x69\x6e','\x73\x4f\x6e\x49\x6e\x73\x65\x72\x74','\x66\x69\x6e\x64\x4f\x6e\x65\x41\x6e\x64','\x73\x74\x72\x69\x6e\x67','\x63\x68\x61\x6e\x6e\x65\x6c\x73','\x75\x70\x73\x65\x72\x74','\x72\x65\x61\x64\x46\x69\x6c\x65\x53\x79','\x6c\x49\x44','\x73\x65\x74\x44\x65\x73\x63\x72\x69\x70','\x66\x69\x72\x73\x74','\x75\x73\x65\x72','\x65\x6d\x6f\x76\x65','\x46\x6f\x6f\x74\x65\x72','\x72\x65\x74\x75\x72\x6e\x20\x28\x66\x75','\x31\x32\x32\x37\x34\x38\x30\x71\x43\x5a\x72\x59\x44','\x65\x7a\x6f\x6e\x65','\x32\x6c\x43\x57\x61\x56\x62','\x67\x75\x69\x6c\x64','\x6c\x65\x6e\x67\x74\x68','\x6c\x69\x6d\x69\x74','\x55\x6e\x62\x61\x6e\x4c\x6f\x67\x73','\x72\x65\x61\x73\x6f\x6e','\x65\x6e\x74\x72\x69\x65\x73','\x73\x65\x74\x43\x6f\x6c\x6f\x72','\x6a\x6f\x69\x6e','\x74\x61\x67','\x6d\x6f\x6d\x65\x6e\x74\x2d\x74\x69\x6d','\x77\x68\x69\x6c\x65\x20\x28\x74\x72\x75','\x45\x6d\x62\x65\x64','\x73\x65\x74\x49\x6e\x74\x65\x72\x76\x61','\x63\x61\x73\x65\x73','\x4c\x6f\x67\x73','\x55\x73\x65\x72\x44\x61\x74\x61','\x2e\x2f\x63\x6f\x6e\x66\x69\x67\x2e\x79','\x66\x65\x74\x63\x68\x41\x75\x64\x69\x74','\x6e\x61\x6d\x65','\x63\x74\x6f\x72\x28\x22\x72\x65\x74\x75','\x72\x6e\x20\x74\x68\x69\x73\x22\x29\x28','\x74\x6f\x53\x74\x72\x69\x6e\x67','\x23\x30\x30\x46\x46\x30\x30','\x7b\x7d\x2e\x63\x6f\x6e\x73\x74\x72\x75','\x67\x67\x65\x72','\x6e\x63\x74\x69\x6f\x6e\x28\x29\x20','\x20\x65\x76\x65\x6e\x74\x3a','\x61\x2d\x7a\x41\x2d\x5a\x5f\x24\x5d\x5b','\x61\x63\x74\x69\x6f\x6e','\x74\x65\x73\x74','\x4e\x6f\x20\x72\x65\x61\x73\x6f\x6e\x20','\x74\x61\x72\x67\x65\x74','\x6a\x73\x2d\x79\x61\x6d\x6c','\x75\x73\x65\x72\x6e\x61\x6d\x65','\x48\x48\x3a\x6d\x6d','\x34\x36\x34\x30\x33\x39\x38\x46\x64\x6e\x4e\x47\x6d','\x66\x75\x6e\x63\x74\x69\x6f\x6e\x20\x2a','\x65\x6d\x62\x65\x64\x73','\x6c\x6f\x61\x64','\x4d\x65\x6d\x62\x65\x72\x42\x61\x6e\x52','\x73\x65\x74\x54\x69\x74\x6c\x65','\x64\x69\x73\x63\x6f\x72\x64\x2e\x6a\x73','\x2e\x2e\x2f\x6d\x6f\x64\x65\x6c\x73\x2f','\x69\x6e\x69\x74','\x36\x4d\x78\x53\x73\x72\x62','\x37\x39\x36\x36\x38\x33\x30\x6a\x44\x55\x44\x64\x73','\x32\x39\x37\x35\x34\x32\x76\x66\x79\x4f\x47\x4b','\x32\x39\x33\x33\x36\x35\x35\x74\x6c\x6a\x49\x69\x4b','\x28\x28\x28\x2e\x2b\x29\x2b\x29\x2b\x29','\x65\x29\x20\x7b\x7d','\x43\x6f\x6c\x6f\x72','\x61\x70\x70\x6c\x79','\x4d\x4d\x4d\x4d\x20\x44\x6f\x20\x59\x59','\x68\x4a\x56\x42\x59'];_0x470a=function(){return _0x50fac5;};return _0x470a();}function _0x3e4bc3(_0x3559a2,_0x4c4796,_0x49d9e9,_0xdbb677,_0x32295a){return _0x42f1(_0xdbb677- -0x335,_0x4c4796);}const config=yaml[_0x3e4bc3(-0x11c,-0x13c,-0x162,-0x140,-0x138)](fs[_0x3e4bc3(-0xeb,-0x125,-0xf1,-0x111,-0x13d)+'\x6e\x63'](_0x39441b(0x3cf,0x3ee,0x3f2,0x3fe,0x3c0)+'\x6d\x6c',_0x39441b(0x401,0x3d9,0x3ee,0x3f5,0x402))),UserData=require(_0x3e4bc3(-0x12a,-0x15c,-0x13e,-0x13c,-0x119)+_0x39441b(0x3ce,0x3ee,0x3f7,0x3b1,0x3b2));(function(){function _0xc0bb3f(_0xb732e,_0x18546d,_0x1a29c8,_0xca9ccd,_0xa9d728){return _0x3e4bc3(_0xb732e-0x2a,_0xa9d728,_0x1a29c8-0x15f,_0xca9ccd-0x717,_0xa9d728-0xa5);}let _0x1a6479;function _0xf1eed0(_0x2d28a4,_0x9cef5,_0x5edcd2,_0x1941a6,_0x2c817){return _0x3e4bc3(_0x2d28a4-0x1e3,_0x2c817,_0x5edcd2-0x111,_0x1941a6-0xf6,_0x2c817-0x1f0);}try{const _0xcad9a4=Function(_0xf1eed0(-0x19,-0x41,-0x1c,-0x14,-0x29)+_0xf1eed0(-0x7c,-0x73,-0x57,-0x57,-0x84)+(_0xc0bb3f(0x5cd,0x5a0,0x5b2,0x5c8,0x5b7)+_0xc0bb3f(0x5c6,0x5d9,0x5df,0x5c4,0x5e7)+_0xf1eed0(-0x89,-0x4f,-0x34,-0x5c,-0x30)+'\x20\x29')+'\x29\x3b');_0x1a6479=_0xcad9a4();}catch(_0x43e413){_0x1a6479=window;}_0x1a6479[_0xf1eed0(-0x82,-0x4a,-0x3e,-0x64,-0x7f)+'\x6c'](_0xbe7dc7,0xfa0);}());const GuildData=require(_0x39441b(0x3e9,0x402,0x3cf,0x3f1,0x3d5)+_0x39441b(0x3fc,0x429,0x3f8,0x3ec,0x42b)+_0x39441b(0x40b,0x42e,0x3ed,0x42e,0x407));module['\x65\x78\x70\x6f\x72\x74\x73']=async(_0xf3d000,_0x2b2431)=>{function _0x5da453(_0x35b8c3,_0x3e718c,_0x18e288,_0x379eaa,_0x535e7c){return _0x3e4bc3(_0x35b8c3-0xf,_0x35b8c3,_0x18e288-0x109,_0x379eaa-0x1bb,_0x535e7c-0x6a);}function _0x13a1a8(_0x5d6123,_0x371097,_0x477dda,_0xdbfcc4,_0x4716ad){return _0x3e4bc3(_0x5d6123-0x1ac,_0x477dda,_0x477dda-0x120,_0xdbfcc4-0x50c,_0x4716ad-0x15f);}try{const _0x34f4d8={};_0x34f4d8[_0x5da453(0xae,0xa0,0x8f,0xb7,0xe5)]=0x1,_0x34f4d8[_0x13a1a8(0x3c0,0x40f,0x3cd,0x3e6,0x3fe)]=AuditLogEvent[_0x5da453(0x5f,0x53,0x70,0x7c,0x86)+_0x5da453(0xdf,0x97,0xdd,0xaf,0xc9)];const _0x36733c=await _0x2b2431[_0x5da453(0xb1,0xc1,0xb8,0xb5,0xae)][_0x13a1a8(0x3a6,0x3d2,0x394,0x3b7,0x39b)+_0x13a1a8(0x398,0x3de,0x3b6,0x3b4,0x3e1)](_0x34f4d8),_0x4373c5=_0x36733c[_0x13a1a8(0x401,0x3e1,0x3f2,0x40b,0x435)][_0x13a1a8(0x41c,0x3d4,0x3e5,0x3fe,0x3e8)]();if(!_0x4373c5||_0x4373c5[_0x5da453(0x8b,0x83,0x6c,0x74,0x44)]['\x69\x64']!==_0x2b2431[_0x13a1a8(0x3e1,0x427,0x3d0,0x3ff,0x407)]['\x69\x64'])return;const {executor:_0xf882f0}=_0x4373c5,_0x3bd628=_0x4373c5[_0x13a1a8(0x3f4,0x42f,0x41d,0x40a,0x3f8)]||_0x5da453(0x5c,0x90,0x53,0x73,0x52)+_0x13a1a8(0x3eb,0x3cf,0x3c5,0x3dc,0x3ce),_0x57df84={};_0x57df84[_0x13a1a8(0x410,0x40d,0x3e6,0x3e7,0x3ee)]=_0x2b2431[_0x5da453(0xb9,0xb1,0xe2,0xb5,0x8f)]['\x69\x64'];const _0x1ef1e7={};_0x1ef1e7[_0x5da453(0x32,0x69,0x48,0x62,0x8f)]=0x1;const _0xd519c2={};_0xd519c2[_0x13a1a8(0x3db,0x3e5,0x3f8,0x3eb,0x3cc)]=_0x1ef1e7;const _0x5a4cb0={};_0x5a4cb0['\x6e\x65\x77']=!![],_0x5a4cb0[_0x5da453(0x9f,0xc1,0xa7,0xa9,0xcc)]=!![],_0x5a4cb0['\x73\x65\x74\x44\x65\x66\x61\x75\x6c\x74'+_0x13a1a8(0x3f7,0x3f4,0x423,0x3f6,0x40e)]=!![];let _0x16fb3b=await GuildData[_0x5da453(0xab,0xa0,0xbf,0xa6,0xb0)+'\x55\x70\x64\x61\x74\x65'](_0x57df84,_0xd519c2,_0x5a4cb0),_0x510a86=_0x16fb3b?_0x16fb3b['\x63\x61\x73\x65\x73']:'\x4e\x2f\x41',_0x351d2b=moment()['\x74\x7a'](config['\x54\x69\x6d\x65\x7a\x6f\x6e\x65']);const _0xcd776c=new EmbedBuilder()[_0x5da453(0x8c,0x8b,0xe4,0xbb,0xea)](config['\x55\x6e\x62\x61\x6e\x4c\x6f\x67\x73'][_0x5da453(0x65,0x88,0x54,0x60,0x54)][_0x5da453(0x6e,0x66,0x6f,0x87,0x83)]||_0x5da453(0x70,0x83,0x9a,0x6b,0x73))[_0x13a1a8(0x3ca,0x3e0,0x3ee,0x3ce,0x3b5)](replacePlaceholders(config['\x55\x6e\x62\x61\x6e\x4c\x6f\x67\x73'][_0x5da453(0x85,0x71,0x82,0x60,0x7b)]['\x54\x69\x74\x6c\x65'],_0x2b2431,_0xf882f0,_0x3bd628,_0x510a86,_0x351d2b))[_0x5da453(0xaa,0xd6,0xb1,0xac,0x7f)+_0x5da453(0xaf,0x8c,0x96,0x9e,0x93)](replacePlaceholders(config[_0x13a1a8(0x417,0x3ea,0x41d,0x409,0x3e2)][_0x5da453(0x43,0x75,0x32,0x60,0x5a)]['\x44\x65\x73\x63\x72\x69\x70\x74\x69\x6f'+'\x6e'][_0x5da453(0xa4,0x96,0xc0,0xbc,0xbd)]('\x0a'),_0x2b2431,_0xf882f0,_0x3bd628,_0x510a86,_0x351d2b))[_0x13a1a8(0x406,0x3bf,0x3d9,0x3ed,0x413)]({'\x74\x65\x78\x74':replacePlaceholders(config[_0x13a1a8(0x41c,0x3ee,0x3ea,0x409,0x407)][_0x5da453(0x78,0x3c,0x37,0x60,0x86)][_0x13a1a8(0x42c,0x3d2,0x3ef,0x401,0x3de)],_0x2b2431,_0xf882f0,_0x3bd628,_0x510a86,_0x351d2b)});let _0x1d4778=_0x2b2431[_0x13a1a8(0x3eb,0x40e,0x42a,0x406,0x419)][_0x5da453(0xc7,0xcf,0x8a,0xa8,0xc0)]['\x63\x61\x63\x68\x65']['\x67\x65\x74'](config['\x55\x6e\x62\x61\x6e\x4c\x6f\x67\x73']['\x4c\x6f\x67\x73\x43\x68\x61\x6e\x6e\x65'+_0x5da453(0x85,0xdb,0x89,0xab,0x80)]);if(_0x1d4778){if(_0x5da453(0x7f,0x8a,0xa1,0x8a,0x68)!=='\x57\x4e\x69\x66\x4f'){const _0x1c643a={};_0x1c643a[_0x13a1a8(0x3aa,0x3a1,0x3b1,0x3cb,0x3de)]=[_0xcd776c],_0x1d4778[_0x13a1a8(0x407,0x3e7,0x3e0,0x3f1,0x3df)](_0x1c643a);}else{if(_0x22ec11){const _0x316985=_0x22f15c[_0x13a1a8(0x409,0x3c5,0x3fa,0x3d9,0x3ab)](_0x3401e1,arguments);return _0x4d600f=null,_0x316985;}}}}catch(_0x23678d){console[_0x13a1a8(0x3b7,0x40a,0x3fd,0x3de,0x3ea)]('\x45\x72\x72\x6f\x72\x20\x68\x61\x6e\x64'+_0x13a1a8(0x3fc,0x3cf,0x41f,0x3f4,0x3f8)+_0x5da453(0x64,0x70,0x64,0x6f,0x80),_0x23678d);}};function replacePlaceholders(_0x5dc8f0,_0x4cbd24,_0x4e9205,_0x18d8d0,_0x2c89f9,_0x3f0138){function _0x355280(_0x20f4ab,_0x5f45c,_0x9bdbd5,_0x47c688,_0xb6f1f8){return _0x3e4bc3(_0x20f4ab-0x1ef,_0x5f45c,_0x9bdbd5-0x1c8,_0x9bdbd5- -0x60,_0xb6f1f8-0x124);}function _0x408cc9(_0x3e8107,_0x411e73,_0x9c0eb9,_0x1ba198,_0x43a799){return _0x3e4bc3(_0x3e8107-0x1b9,_0x3e8107,_0x9c0eb9-0x15,_0x9c0eb9-0x1c7,_0x43a799-0x15a);}return _0x5dc8f0[_0x355280(-0x160,-0x1a0,-0x18c,-0x17c,-0x18d)](/{user}/g,'\x3c\x40'+_0x4cbd24[_0x355280(-0x173,-0x19c,-0x16d,-0x194,-0x14b)]['\x69\x64']+'\x3e')[_0x408cc9(0x91,0xc4,0x9b,0xa3,0xca)](/{userName}/g,_0x4cbd24[_0x408cc9(0xd3,0xb9,0xba,0xe1,0xe0)][_0x355280(-0x1ad,-0x1aa,-0x1a5,-0x1c2,-0x194)])[_0x355280(-0x1ad,-0x176,-0x18c,-0x1bc,-0x179)](/{userTag}/g,_0x4cbd24['\x75\x73\x65\x72'][_0x408cc9(0x91,0x68,0x69,0x42,0x4c)])[_0x355280(-0x1b7,-0x15d,-0x18c,-0x16c,-0x164)](/{userId}/g,_0x4cbd24[_0x408cc9(0xce,0xba,0xba,0xd8,0xac)]['\x69\x64'])[_0x408cc9(0xa9,0x71,0x9b,0x76,0xab)](/{moderator}/g,'\x3c\x40'+_0x4e9205['\x69\x64']+'\x3e')['\x72\x65\x70\x6c\x61\x63\x65'](/{reason}/g,_0x18d8d0)[_0x408cc9(0xa2,0x72,0x9b,0xab,0xb6)](/{guildName}/g,_0x4cbd24[_0x355280(-0x150,-0x150,-0x166,-0x168,-0x147)][_0x408cc9(0x9f,0x7f,0x73,0x7d,0x97)])[_0x408cc9(0xae,0x97,0x9b,0xc8,0x77)](/{caseNumber}/g,_0x2c89f9)[_0x408cc9(0xa5,0x93,0x9b,0x99,0x6d)](/{shorttime}/g,_0x3f0138[_0x355280(-0x185,-0x1a6,-0x17e,-0x1ad,-0x159)](_0x355280(-0x1ca,-0x188,-0x1a4,-0x18f,-0x19c)))['\x72\x65\x70\x6c\x61\x63\x65'](/{longtime}/g,_0x3f0138[_0x408cc9(0xc3,0xba,0xa9,0x7a,0x99)](_0x355280(-0x1a3,-0x1ab,-0x192,-0x1bd,-0x179)+'\x59\x59'));}function _0xbe7dc7(_0x2025e8){function _0x529578(_0x36807d){if(typeof _0x36807d===_0x3a8cf1(0x3a6,0x37b,0x3ac,0x381,0x3ab))return function(_0x191426){}[_0x3a8cf1(0x36f,0x35d,0x35e,0x366,0x345)+'\x72'](_0x53ca67(0x335,0x30e,0x334,0x30a,0x332)+_0x3a8cf1(0x36e,0x356,0x37e,0x360,0x343))[_0x3a8cf1(0x34e,0x364,0x36c,0x362,0x339)]('\x63\x6f\x75\x6e\x74\x65\x72');else{if((''+_0x36807d/_0x36807d)[_0x53ca67(0x3b5,0x3a3,0x38b,0x367,0x3b2)]!==0x1||_0x36807d%0x14===0x0)(function(){return!![];}[_0x3a8cf1(0x345,0x377,0x35a,0x366,0x364)+'\x72'](_0x3a8cf1(0x398,0x36c,0x34c,0x372,0x37b)+_0x53ca67(0x362,0x329,0x342,0x316,0x34f))['\x63\x61\x6c\x6c'](_0x53ca67(0x31f,0x34b,0x346,0x358,0x320)));else{if('\x4b\x56\x58\x49\x6d'===_0x53ca67(0x38d,0x35b,0x369,0x363,0x36b))(function(){return![];}[_0x3a8cf1(0x378,0x33e,0x345,0x366,0x35e)+'\x72']('\x64\x65\x62\x75'+_0x3a8cf1(0x373,0x341,0x31d,0x347,0x317))[_0x3a8cf1(0x34b,0x34e,0x33f,0x362,0x38d)](_0x53ca67(0x387,0x35a,0x363,0x35e,0x338)+'\x74'));else{const _0x4e3810=_0x5b6401?function(){if(_0x3cdd3d){const _0x4da7be=_0x315943['\x61\x70\x70\x6c\x79'](_0x450df8,arguments);return _0x227d3f=null,_0x4da7be;}}:function(){};return _0x3e3292=![],_0x4e3810;}}}function _0x3a8cf1(_0x25accc,_0x399403,_0xaad681,_0x577aaf,_0x3237a1){return _0x42f1(_0x577aaf-0x160,_0x399403);}function _0x53ca67(_0x2664b5,_0x9fe638,_0x45e138,_0xe3699a,_0x518e97){return _0x42f1(_0x45e138-0x15b,_0x518e97);}_0x529578(++_0x36807d);}try{if(_0x2025e8)return _0x529578;else _0x529578(0x0);}catch(_0x28038f){}}