const Command = require("../../structures/Command");
const Discord = require("discord.js");

module.exports = class EditChannel extends Command {
  constructor(client) {
    super(client, {
      name: "editchannel",
      description: client.cmdConfig.editchannel.description,
      usage: client.cmdConfig.editchannel.usage,
      permissions: client.cmdConfig.editchannel.permissions,
      aliases: client.cmdConfig.editchannel.aliases,
      category: "utility",
      listed: client.cmdConfig.editchannel.enabled,
      slash: true,
      options: [{
        name: 'name',
        type: Discord.ApplicationCommandOptionType.Subcommand,
        description: "Channel Name",
        options: [{
          name: Discord.ApplicationCommandOptionType.Channel, 
          type: Discord.ApplicationCommandOptionType.Channel, 
          description: "Channel to Edit", 
          required: true,
        }, {
          name: 'newname', 
          type: Discord.ApplicationCommandOptionType.String, 
          description: "Channel Name", 
          required: true,
        }]
      }, {
        name: 'topic',
        type: Discord.ApplicationCommandOptionType.Subcommand,
        description: "Channel Topic",
        options: [{
          name: Discord.ApplicationCommandOptionType.Channel, 
          type: Discord.ApplicationCommandOptionType.Channel, 
          description: "Channel to Edit", 
          required: true,
        }, {
          name: 'newtopic', 
          type: Discord.ApplicationCommandOptionType.String, 
          description: "Channel Topic", 
          required: true,
        }]
      }, {
        name: 'position',
        type: Discord.ApplicationCommandOptionType.Subcommand,
        description: "Channel Position",
        options: [{
          name: Discord.ApplicationCommandOptionType.Channel, 
          type: Discord.ApplicationCommandOptionType.Channel, 
          description: "Channel to Edit", 
          required: true,
        }, {
          name: 'newposition', 
          type: Discord.ApplicationCommandOptionType.Number, 
          description: "Channel Position", 
          required: true,
        }]
      }, {
        name: 'parent',
        type: Discord.ApplicationCommandOptionType.Subcommand,
        description: "Channel Parent",
        options: [{
          name: Discord.ApplicationCommandOptionType.Channel, 
          type: Discord.ApplicationCommandOptionType.Channel, 
          description: "Channel to Edit", 
          required: true,
        }, {
          name: 'newparent', 
          type: Discord.ApplicationCommandOptionType.Channel, 
          description: "Channel Parent", 
          required: true,
        }]
      }]
    });
  }

  async run(message, args) {
    let channel = message.mentions.channels.first();
    let option = args[1];
    let value = args[2];

    if(channel.type == Discord.ChannelType.GuildCategory) return message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
      this.client.embeds.title, this.client.language.general.channel_category, this.client.embeds.error_color)]});
    
    if(option.toLowerCase() == "name") {
      message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, this.client.language.edit.name.replace("<name>", value)
            .replace("<channel>", channel), this.client.embeds.success_color)]});
      channel.setName(value);
    } else if(option.toLowerCase() == "topic") {
      message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, this.client.language.edit.topic.replace("<topic>", value)
            .replace("<channel>", channel), this.client.embeds.success_color)]});
      channel.setTopic(value);
    } else if(option.toLowerCase() == "position") {
      if(isNaN(value)) return message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, this.client.language.general.not_number, this.client.embeds.error_color)]});
      value = Number(value);
      
      message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, this.client.language.edit.position.replace("<position>", value)
            .replace("<channel>", channel), this.client.embeds.success_color)]});
      channel.setPosition(value); 
    } else if(option.toLowerCase() == "parent") {
      let parent = [...message.mentions.channels.values()].find((c) => c == Discord.ChannelType.GuildCategory) || message.guild.channels.cache.get(value);
      
      if(!parent || parent.type != Discord.ChannelType.GuildCategory) return message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, this.client.language.general.invalid_category, this.client.embeds.error_color)]});
      
      message.channel.send({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, this.client.language.edit.position.replace("<parent>", parent)
            .replace("<channel>", channel), this.client.embeds.success_color)]}); 
      
      channel.setParent(parent);
    } else {
      message.channel.send({ embeds: [this.client.utils.usage(this.client, this.client.cmdConfig.editchannel.usage)]});
    }
  }
  async slashRun(interaction, args) {
    let option = args[0];
    let channel = interaction.guild.channels.cache.get(args[1]);
    let value = args[2];

    if(channel.type == Discord.ChannelType.GuildCategory) return interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
      this.client.embeds.title, this.client.language.general.channel_category, this.client.embeds.error_color)]});
    
    if(option == "name") {
      interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, this.client.language.edit.name.replace("<name>", value)
            .replace("<channel>", channel), this.client.embeds.success_color)]});
      channel.setName(value);
    } else if(option == "topic") {
      interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, this.client.language.edit.topic.replace("<topic>", value)
            .replace("<channel>", channel), this.client.embeds.success_color)]});
      channel.setTopic(value);
    } else if(option == "position") {
      if(isNaN(value)) return interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, this.client.language.general.not_number, this.client.embeds.error_color)]});
      value = Number(value);
      
      interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, this.client.language.edit.position.replace("<position>", value)
            .replace("<channel>", channel), this.client.embeds.success_color)]});
      channel.setPosition(value); 
    } else if(option == "parent") {
      value = interaction.guild.channels.cache.get(value);
      if(value.type != Discord.ChannelType.GuildCategory) return interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, this.client.language.general.invalid_category, this.client.embeds.error_color)]});
      
      interaction.reply({ embeds: [this.client.embedBuilder(this.client, 
          this.client.embeds.title, this.client.language.edit.position.replace("<parent>", value)
            .replace("<channel>", channel), this.client.embeds.success_color)]}); 
      
      channel.setParent(value);
    } else {
      interaction.reply({ embeds: [this.client.utils.usage(this.client, this.client.cmdConfig.editchannel.usage)]});
    }
  }
};
