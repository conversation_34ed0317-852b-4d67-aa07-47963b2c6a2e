const fs = require('fs');
const path = require('path');

const filePath = path.resolve(__dirname, '../../data/giveaway.json');

module.exports = {
  name: 'greroll',
  description: 'Reroll a giveaway',

  async execute(message, args) {
    const msgId = args[0];
    if (!msgId) return message.reply('Usage: `greroll <messageID>`');

    const giveaways = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    const gw = giveaways.find(g => g.messageId === msgId);

    if (!gw || !gw.ended) return message.reply('Giveaway not found or not ended.');

    const channel = message.guild.channels.cache.get(gw.channelId);
    const msg = await channel.messages.fetch(gw.messageId).catch(() => null);
    if (!msg) return message.reply('Message not found.');

    const users = await msg.reactions.cache.get('🎉')?.users.fetch();
    const participants = users?.filter(u => !u.bot).map(u => u.id);
    if (!participants || participants.length === 0) return message.reply('No valid participants.');

    const winners = [];
    for (let i = 0; i < gw.winners; i++) {
      const winner = participants[Math.floor(Math.random() * participants.length)];
      winners.push(`<@${winner}>`);
    }

    message.channel.send(`🎉 New winner(s): ${winners.join(', ')}`);
  }
};