const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle topic changes
            if (oldChannel.topic === newChannel.topic) return;
            
            console.log(`📝 Channel topic updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_TOPIC_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_TOPIC_UPDATE',
                user: 'System',
                userId: null,
                action: 'Ch<PERSON> đề kênh được thay đổi',
                details: `<PERSON><PERSON> đề kênh **${newChannel.name}** đ<PERSON> được thay đổi`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the topic from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Chủ đề kênh được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for channel topic update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📝 Chủ đề kênh được thay đổi',
                `Vừa có chủ đề kênh được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                }
            ]);
            
            // Add old and new topics
            const oldTopic = oldChannel.topic || '*Không có chủ đề*';
            const newTopic = newChannel.topic || '*Không có chủ đề*';
            
            embed.addFields([
                {
                    name: '> Chủ đề cũ',
                    value: `\`\`\`${oldTopic.length > 500 ? oldTopic.substring(0, 500) + '...' : oldTopic}\`\`\``,
                    inline: false
                },
                {
                    name: '> Chủ đề mới',
                    value: `\`\`\`${newTopic.length > 500 ? newTopic.substring(0, 500) + '...' : newTopic}\`\`\``,
                    inline: false
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Topic Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel topic update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelTopicUpdate audit log:', error);
        }
    }
};
