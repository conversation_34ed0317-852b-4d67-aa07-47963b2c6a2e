const { PermissionsBitField, EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'lock',
    description: 'Locks the current channel',
    run: async (message, args, client) => {
        // Check if user has the required permission
        if (!message.member.permissions.has(PermissionsBitField.Flags.ManageChannels)) {
            return message.reply('❌ You need `Manage Channels` permission to use this command.');
        }

        // Check if the bot has permission
        if (!message.guild.members.me.permissions.has(PermissionsBitField.Flags.ManageChannels)) {
            return message.reply('❌ I need `Manage Channels` permission to lock this channel.');
        }

        try {
            // Lock the channel for @everyone
            await message.channel.permissionOverwrites.edit(message.guild.roles.everyone, {
                SendMessages: false
            });

            // Send embed confirmation
            const lockEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setDescription(`🔒 This channel has been locked for @everyone. Role`)
                .setTimestamp();

            message.channel.send({ embeds: [lockEmbed] });
        } catch (error) {
            console.error('Error locking channel:', error);
            message.reply('❌ An error occurred while locking the channel.');
        }
    }
};