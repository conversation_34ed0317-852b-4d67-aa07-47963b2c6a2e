const {
  SlashCommandBuilder,
  PermissionFlagsBits,
  EmbedBuilder,
} = require("discord.js");
const {
  createSuccessEmbed,
  createErrorEmbed,
  createInfoEmbed,
  createWarningEmbed,
} = require("../../utils/embedBuilder.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("mod")
    .setDescription("Hệ thống moderation toàn diện")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("ban")
        .setDescription("Ban thành viên khỏi server")
        .addUserOption((option) =>
          option
            .setName("user")
            .setDescription("Thành viên cần ban")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do ban")
            .setRequired(false)
        )
        .addIntegerOption((option) =>
          option
            .setName("delete_days")
            .setDescription("Xóa tin nhắn trong X ngày (0-7)")
            .setRequired(false)
            .setMinValue(0)
            .setMaxValue(7)
        )
        .addBooleanOption((option) =>
          option
            .setName("silent")
            .setDescription("Không gửi DM cho người bị ban")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("unban")
        .setDescription("Unban thành viên")
        .addStringOption((option) =>
          option
            .setName("user_id")
            .setDescription("ID của thành viên cần unban")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do unban")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("kick")
        .setDescription("Kick thành viên khỏi server")
        .addUserOption((option) =>
          option
            .setName("user")
            .setDescription("Thành viên cần kick")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do kick")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("silent")
            .setDescription("Không gửi DM cho người bị kick")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("timeout")
        .setDescription("Timeout thành viên")
        .addUserOption((option) =>
          option
            .setName("user")
            .setDescription("Thành viên cần timeout")
            .setRequired(true)
        )
        .addIntegerOption(
          (option) =>
            option
              .setName("duration")
              .setDescription("Thời gian timeout (phút)")
              .setRequired(true)
              .setMinValue(1)
              .setMaxValue(40320) // 28 days
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do timeout")
            .setRequired(false)
        )
        .addBooleanOption((option) =>
          option
            .setName("silent")
            .setDescription("Không gửi DM cho người bị timeout")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("untimeout")
        .setDescription("Hủy timeout cho thành viên")
        .addUserOption((option) =>
          option
            .setName("user")
            .setDescription("Thành viên cần hủy timeout")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do hủy timeout")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("warn")
        .setDescription("Cảnh báo thành viên")
        .addUserOption((option) =>
          option
            .setName("user")
            .setDescription("Thành viên cần cảnh báo")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do cảnh báo")
            .setRequired(true)
        )
        .addBooleanOption((option) =>
          option
            .setName("silent")
            .setDescription("Không gửi DM cho người bị cảnh báo")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("unwarn")
        .setDescription("Hủy cảnh báo cho thành viên")
        .addUserOption((option) =>
          option
            .setName("user")
            .setDescription("Thành viên cần hủy cảnh báo")
            .setRequired(true)
        )
        .addIntegerOption((option) =>
          option
            .setName("warning_id")
            .setDescription("ID cảnh báo cần hủy (để trống để hủy tất cả)")
            .setRequired(false)
            .setMinValue(1)
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do hủy cảnh báo")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("warnings")
        .setDescription("Xem danh sách cảnh báo của thành viên")
        .addUserOption((option) =>
          option
            .setName("user")
            .setDescription("Thành viên cần xem cảnh báo")
            .setRequired(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("mute")
        .setDescription("Mute thành viên (role mute)")
        .addUserOption((option) =>
          option
            .setName("user")
            .setDescription("Thành viên cần mute")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("duration")
            .setDescription("Thời gian mute (ví dụ: 1h, 30m, 2d)")
            .setRequired(false)
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do mute")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("unmute")
        .setDescription("Unmute thành viên")
        .addUserOption((option) =>
          option
            .setName("user")
            .setDescription("Thành viên cần unmute")
            .setRequired(true)
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do unmute")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("slowmode")
        .setDescription("Thiết lập slowmode cho kênh")
        .addIntegerOption(
          (option) =>
            option
              .setName("seconds")
              .setDescription("Thời gian slowmode (giây, 0 để tắt)")
              .setRequired(true)
              .setMinValue(0)
              .setMaxValue(21600) // 6 hours
        )
        .addChannelOption((option) =>
          option
            .setName("channel")
            .setDescription(
              "Kênh cần thiết lập slowmode (mặc định: kênh hiện tại)"
            )
            .setRequired(false)
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do thiết lập slowmode")
            .setRequired(false)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("purge")
        .setDescription("Xóa tin nhắn hàng loạt")
        .addIntegerOption((option) =>
          option
            .setName("amount")
            .setDescription("Số tin nhắn cần xóa (1-100)")
            .setRequired(true)
            .setMinValue(1)
            .setMaxValue(100)
        )
        .addUserOption((option) =>
          option
            .setName("user")
            .setDescription("Chỉ xóa tin nhắn của user này")
            .setRequired(false)
        )
        .addStringOption((option) =>
          option
            .setName("filter")
            .setDescription("Lọc tin nhắn theo loại")
            .setRequired(false)
            .addChoices(
              { name: "Tất cả", value: "all" },
              { name: "Chỉ bot", value: "bots" },
              { name: "Chỉ người dùng", value: "humans" },
              { name: "Có file đính kèm", value: "attachments" },
              { name: "Có embed", value: "embeds" },
              { name: "Có link", value: "links" }
            )
        )
        .addStringOption((option) =>
          option
            .setName("reason")
            .setDescription("Lý do xóa tin nhắn")
            .setRequired(false)
        )
    ),
  category: "moderation",
  adminOnly: false,
  manageServer: false,

  async execute(interaction, client) {
    const subcommand = interaction.options.getSubcommand();
    const guildId = interaction.guild.id;

    // Kiểm tra quyền cơ bản
    const requiredPermissions = getRequiredPermissions(subcommand);
    if (!interaction.member.permissions.has(requiredPermissions)) {
      const errorEmbed = createErrorEmbed(
        "Bạn không có quyền!",
        `Bạn cần quyền: ${requiredPermissions
          .map((p) => `\`${p}\``)
          .join(", ")}`
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    // Kiểm tra quyền bot
    const botPermissions = getBotRequiredPermissions(subcommand);
    if (!interaction.guild.members.me.permissions.has(botPermissions)) {
      const errorEmbed = createErrorEmbed(
        "Bot thiếu quyền!",
        `Bot cần các quyền: ${botPermissions.map((p) => `\`${p}\``).join(", ")}`
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    switch (subcommand) {
      case "ban":
        await handleBan(interaction, client, guildId);
        break;
      case "unban":
        await handleUnban(interaction, client, guildId);
        break;
      case "kick":
        await handleKick(interaction, client, guildId);
        break;
      case "timeout":
        await handleTimeout(interaction, client, guildId);
        break;
      case "untimeout":
        await handleUntimeout(interaction, client, guildId);
        break;
      case "warn":
        await handleWarn(interaction, client, guildId);
        break;
      case "unwarn":
        await handleUnwarn(interaction, client, guildId);
        break;
      case "warnings":
        await handleWarnings(interaction, client, guildId);
        break;
      case "mute":
        await handleMute(interaction, client, guildId);
        break;
      case "unmute":
        await handleUnmute(interaction, client, guildId);
        break;
      case "slowmode":
        await handleSlowmode(interaction, client, guildId);
        break;
      case "purge":
        await handlePurge(interaction, client, guildId);
        break;
    }
  },
};

function getRequiredPermissions(subcommand) {
  const permissions = {
    ban: [PermissionFlagsBits.BanMembers],
    unban: [PermissionFlagsBits.BanMembers],
    kick: [PermissionFlagsBits.KickMembers],
    timeout: [PermissionFlagsBits.ModerateMembers],
    untimeout: [PermissionFlagsBits.ModerateMembers],
    warn: [PermissionFlagsBits.ModerateMembers],
    unwarn: [PermissionFlagsBits.ModerateMembers],
    warnings: [PermissionFlagsBits.ModerateMembers],
    mute: [PermissionFlagsBits.ManageRoles],
    unmute: [PermissionFlagsBits.ManageRoles],
    slowmode: [PermissionFlagsBits.ManageChannels],
    purge: [PermissionFlagsBits.ManageMessages],
  };
  return permissions[subcommand] || [];
}

function getBotRequiredPermissions(subcommand) {
  const permissions = {
    ban: [PermissionFlagsBits.BanMembers],
    unban: [PermissionFlagsBits.BanMembers],
    kick: [PermissionFlagsBits.KickMembers],
    timeout: [PermissionFlagsBits.ModerateMembers],
    untimeout: [PermissionFlagsBits.ModerateMembers],
    warn: [PermissionFlagsBits.SendMessages],
    unwarn: [PermissionFlagsBits.SendMessages],
    warnings: [PermissionFlagsBits.SendMessages],
    mute: [PermissionFlagsBits.ManageRoles],
    unmute: [PermissionFlagsBits.ManageRoles],
    slowmode: [PermissionFlagsBits.ManageChannels],
    purge: [PermissionFlagsBits.ManageMessages],
  };
  return permissions[subcommand] || [];
}

async function handleBan(interaction, client, guildId) {
  const user = interaction.options.getUser("user");
  const reason = interaction.options.getString("reason") || "Không có lý do";
  const deleteDays = interaction.options.getInteger("delete_days") || 0;
  const silent = interaction.options.getBoolean("silent") || false;

  try {
    const member = await interaction.guild.members
      .fetch(user.id)
      .catch(() => null);

    // Kiểm tra hierarchy
    if (member) {
      if (
        member.roles.highest.position >=
        interaction.member.roles.highest.position
      ) {
        const errorEmbed = createErrorEmbed(
          "Không thể ban!",
          "Bạn không thể ban thành viên có role cao hơn hoặc bằng bạn."
        );
        return await interaction.reply({
          embeds: [errorEmbed],
          ephemeral: true,
        });
      }

      if (
        member.roles.highest.position >=
        interaction.guild.members.me.roles.highest.position
      ) {
        const errorEmbed = createErrorEmbed(
          "Không thể ban!",
          "Bot không thể ban thành viên có role cao hơn hoặc bằng bot."
        );
        return await interaction.reply({
          embeds: [errorEmbed],
          ephemeral: true,
        });
      }

      if (!member.bannable) {
        const errorEmbed = createErrorEmbed(
          "Không thể ban!",
          "Thành viên này không thể bị ban."
        );
        return await interaction.reply({
          embeds: [errorEmbed],
          ephemeral: true,
        });
      }
    }

    // Gửi DM trước khi ban
    if (!silent && member) {
      const dmEmbed = createWarningEmbed(
        `Bạn đã bị ban khỏi ${interaction.guild.name}`,
        `**Lý do:** ${reason}\n**Bởi:** ${
          interaction.user.tag
        }\n**Thời gian:** <t:${Math.floor(Date.now() / 1000)}:F>`
      );

      try {
        await user.send({ embeds: [dmEmbed] });
      } catch (error) {
        console.log("Không thể gửi DM cho user bị ban");
      }
    }

    // Thực hiện ban
    await interaction.guild.bans.create(user.id, {
      reason: `${reason} | Bởi: ${interaction.user.tag}`,
      deleteMessageDays: deleteDays,
    });

    // Log vào database
    await client.db.addModerationLog(guildId, {
      type: "ban",
      userId: user.id,
      moderatorId: interaction.user.id,
      reason: reason,
      duration: null,
      deleteDays: deleteDays,
    });

    const successEmbed = createSuccessEmbed(
      "🔨 Thành viên đã bị ban!",
      `**Thành viên:** ${user.tag} (${user.id})\n` +
        `**Lý do:** ${reason}\n` +
        `**Xóa tin nhắn:** ${deleteDays} ngày\n` +
        `**Bởi:** ${interaction.user.tag}`
    );

    await interaction.reply({ embeds: [successEmbed] });

    // Gửi log vào channel
    await sendModerationLog(client, guildId, {
      type: "ban",
      user: user,
      moderator: interaction.user,
      reason: reason,
      deleteDays: deleteDays,
    });
  } catch (error) {
    console.error("Lỗi khi ban user:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi ban thành viên!",
      "Đã xảy ra lỗi khi ban thành viên. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleUnban(interaction, client, guildId) {
  const userId = interaction.options.getString("user_id");
  const reason = interaction.options.getString("reason") || "Không có lý do";

  try {
    // Kiểm tra xem user có bị ban không
    const ban = await interaction.guild.bans.fetch(userId).catch(() => null);
    if (!ban) {
      const errorEmbed = createErrorEmbed(
        "User không bị ban!",
        "User này không có trong danh sách ban của server."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    // Thực hiện unban
    await interaction.guild.bans.remove(
      userId,
      `${reason} | Bởi: ${interaction.user.tag}`
    );

    // Log vào database
    await client.db.addModerationLog(guildId, {
      type: "unban",
      userId: userId,
      moderatorId: interaction.user.id,
      reason: reason,
      duration: null,
    });

    const successEmbed = createSuccessEmbed(
      "✅ Thành viên đã được unban!",
      `**Thành viên:** ${ban.user.tag} (${userId})\n` +
        `**Lý do:** ${reason}\n` +
        `**Bởi:** ${interaction.user.tag}`
    );

    await interaction.reply({ embeds: [successEmbed] });

    // Gửi log vào channel
    await sendModerationLog(client, guildId, {
      type: "unban",
      user: ban.user,
      moderator: interaction.user,
      reason: reason,
    });
  } catch (error) {
    console.error("Lỗi khi unban user:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi unban thành viên!",
      "Đã xảy ra lỗi khi unban thành viên. Vui lòng kiểm tra ID và thử lại!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleKick(interaction, client, guildId) {
  const user = interaction.options.getUser("user");
  const reason = interaction.options.getString("reason") || "Không có lý do";
  const silent = interaction.options.getBoolean("silent") || false;

  try {
    const member = await interaction.guild.members.fetch(user.id);

    // Kiểm tra hierarchy
    if (
      member.roles.highest.position >= interaction.member.roles.highest.position
    ) {
      const errorEmbed = createErrorEmbed(
        "Không thể kick!",
        "Bạn không thể kick thành viên có role cao hơn hoặc bằng bạn."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    if (
      member.roles.highest.position >=
      interaction.guild.members.me.roles.highest.position
    ) {
      const errorEmbed = createErrorEmbed(
        "Không thể kick!",
        "Bot không thể kick thành viên có role cao hơn hoặc bằng bot."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    if (!member.kickable) {
      const errorEmbed = createErrorEmbed(
        "Không thể kick!",
        "Thành viên này không thể bị kick."
      );
      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }

    // Gửi DM trước khi kick
    if (!silent) {
      const dmEmbed = createWarningEmbed(
        `Bạn đã bị kick khỏi ${interaction.guild.name}`,
        `**Lý do:** ${reason}\n**Bởi:** ${
          interaction.user.tag
        }\n**Thời gian:** <t:${Math.floor(Date.now() / 1000)}:F>`
      );

      try {
        await user.send({ embeds: [dmEmbed] });
      } catch (error) {
        console.log("Không thể gửi DM cho user bị kick");
      }
    }

    // Thực hiện kick
    await member.kick(`${reason} | Bởi: ${interaction.user.tag}`);

    // Log vào database
    await client.db.addModerationLog(guildId, {
      type: "kick",
      userId: user.id,
      moderatorId: interaction.user.id,
      reason: reason,
      duration: null,
    });

    const successEmbed = createSuccessEmbed(
      "👢 Thành viên đã bị kick!",
      `**Thành viên:** ${user.tag} (${user.id})\n` +
        `**Lý do:** ${reason}\n` +
        `**Bởi:** ${interaction.user.tag}`
    );

    await interaction.reply({ embeds: [successEmbed] });

    // Gửi log vào channel
    await sendModerationLog(client, guildId, {
      type: "kick",
      user: user,
      moderator: interaction.user,
      reason: reason,
    });
  } catch (error) {
    console.error("Lỗi khi kick user:", error);
    const errorEmbed = createErrorEmbed(
      "Lỗi kick thành viên!",
      "Đã xảy ra lỗi khi kick thành viên. Vui lòng thử lại sau!"
    );
    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  }
}

// Utility functions
async function sendModerationLog(client, guildId, logData) {
  try {
    const config = await client.db.getModerationConfig(guildId);
    if (!config || !config.log_channel_id) return;

    const channel = client.channels.cache.get(config.log_channel_id);
    if (!channel) return;

    const embed = new EmbedBuilder()
      .setColor(getModerationColor(logData.type))
      .setTitle(
        `${getModerationEmoji(logData.type)} ${getModerationTitle(
          logData.type
        )}`
      )
      .addFields(
        {
          name: "👤 Thành viên",
          value: `${logData.user.tag} (${logData.user.id})`,
          inline: true,
        },
        {
          name: "🛡️ Moderator",
          value: `${logData.moderator.tag}`,
          inline: true,
        },
        { name: "📝 Lý do", value: logData.reason, inline: false }
      )
      .setThumbnail(logData.user.displayAvatarURL({ dynamic: true }))
      .setTimestamp()
      .setFooter({
        text: "Moderation Log",
        iconURL: client.user.displayAvatarURL(),
      });

    if (logData.duration) {
      embed.addFields({
        name: "⏰ Thời gian",
        value: logData.duration,
        inline: true,
      });
    }

    if (logData.deleteDays !== undefined) {
      embed.addFields({
        name: "🗑️ Xóa tin nhắn",
        value: `${logData.deleteDays} ngày`,
        inline: true,
      });
    }

    await channel.send({ embeds: [embed] });
  } catch (error) {
    console.error("Lỗi khi gửi moderation log:", error);
  }
}

function getModerationColor(type) {
  const colors = {
    ban: 0xff0000, // Red
    unban: 0x00ff00, // Green
    kick: 0xff8800, // Orange
    timeout: 0xffff00, // Yellow
    untimeout: 0x00ffff, // Cyan
    warn: 0xff4444, // Light Red
    unwarn: 0x44ff44, // Light Green
    mute: 0x8800ff, // Purple
    unmute: 0x88ff88, // Light Green
    purge: 0x444444, // Gray
  };
  return colors[type] || 0x0099ff;
}

function getModerationEmoji(type) {
  const emojis = {
    ban: "🔨",
    unban: "✅",
    kick: "👢",
    timeout: "⏰",
    untimeout: "🔓",
    warn: "⚠️",
    unwarn: "✅",
    mute: "🔇",
    unmute: "🔊",
    purge: "🗑️",
  };
  return emojis[type] || "📝";
}

function getModerationTitle(type) {
  const titles = {
    ban: "Thành viên bị ban",
    unban: "Thành viên được unban",
    kick: "Thành viên bị kick",
    timeout: "Thành viên bị timeout",
    untimeout: "Thành viên được hủy timeout",
    warn: "Thành viên bị cảnh báo",
    unwarn: "Cảnh báo được hủy",
    mute: "Thành viên bị mute",
    unmute: "Thành viên được unmute",
    purge: "Tin nhắn bị xóa",
  };
  return titles[type] || "Hành động moderation";
}

function formatDuration(minutes) {
  if (minutes < 60) {
    return `${minutes} phút`;
  } else if (minutes < 1440) {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0
      ? `${hours} giờ ${remainingMinutes} phút`
      : `${hours} giờ`;
  } else {
    const days = Math.floor(minutes / 1440);
    const remainingHours = Math.floor((minutes % 1440) / 60);
    return remainingHours > 0
      ? `${days} ngày ${remainingHours} giờ`
      : `${days} ngày`;
  }
}

function parseDuration(durationStr) {
  const regex = /(\d+)([smhd])/g;
  let totalMinutes = 0;
  let match;

  while ((match = regex.exec(durationStr)) !== null) {
    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case "s":
        totalMinutes += Math.ceil(value / 60);
        break;
      case "m":
        totalMinutes += value;
        break;
      case "h":
        totalMinutes += value * 60;
        break;
      case "d":
        totalMinutes += value * 1440;
        break;
    }
  }

  return totalMinutes;
}
