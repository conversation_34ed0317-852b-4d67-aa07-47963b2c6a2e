const { Events } = require('discord.js');
const { createGoodbyeEmbed } = require('../utils/embedBuilder.js');
const { canSendMessages } = require('../utils/permissions.js');

module.exports = {
    name: Events.GuildMemberRemove,
    async execute(member, client) {
        const guildId = member.guild.id;
        
        try {
            // Lấy cấu hình goodbye
            const welcomeConfig = await client.db.getWelcomeConfig(guildId);
            
            // Xử lý goodbye message
            if (welcomeConfig && welcomeConfig.goodbye_enabled) {
                await handleGoodbyeMessage(member, client, welcomeConfig);
            }
            
        } catch (error) {
            console.error(`❌ Lỗi khi xử lý thành viên rời khỏi ${member.user.tag} trong ${member.guild.name}:`, error);
        }
    },
};

async function handleGoodbyeMessage(member, client, config) {
    try {
        if (!config.goodbye_channel_id) return;
        
        const channel = member.guild.channels.cache.get(config.goodbye_channel_id);
        
        if (!channel) {
            console.log(`⚠️ Goodbye channel không tồn tại trong ${member.guild.name}`);
            return;
        }
        
        const canSend = canSendMessages(channel);
        if (!canSend.canSend) {
            console.log(`⚠️ Không thể gửi goodbye message: ${canSend.reason}`);
            return;
        }
        
        if (config.goodbye_embed) {
            // Gửi embed
            const goodbyeEmbed = createGoodbyeEmbed(member, config.goodbye_message);
            await channel.send({ embeds: [goodbyeEmbed] });
        } else {
            // Gửi tin nhắn thường
            const defaultMessage = `**{username}** đã rời khỏi **{server}**.\n\nChúc bạn may mắn! ${client.config.emojis.goodbye}`;
            const message = config.goodbye_message || defaultMessage;
            const processedMessage = message
                .replace(/{user}/g, `<@${member.id}>`)
                .replace(/{username}/g, member.user.username)
                .replace(/{server}/g, member.guild.name)
                .replace(/{membercount}/g, member.guild.memberCount.toString());
            
            await channel.send(processedMessage);
        }
        
        console.log(`✅ Đã gửi goodbye message cho ${member.user.tag} trong ${member.guild.name}`);
        
    } catch (error) {
        console.error('❌ Lỗi khi gửi goodbye message:', error);
    }
}
