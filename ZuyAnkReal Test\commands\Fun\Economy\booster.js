function _0x41ed(){const _0x21597c=['options','68450bRaYdh','330567glGHms','addSubcommand','view','load','error','getSubcommand','32jtGCWe','description','28bzEcap','setName','2266338NTAqba','169316yjZHgT','readFileSync','567345kicgGR','Boosters','title','setDescription','floor','join','noBoosters','setTitle','boosters','Economy','booster','type','#00FF00','setColor','discord.js','74950fGpuiZ','endTime','./lang.yml','reply','Manage\x20your\x20boosters','length','utf8','js-yaml','../../../models/UserData','11393667aoFgCn','Messages','./Utility/helpers','map'];_0x41ed=function(){return _0x21597c;};return _0x41ed();}(function(_0x5a6848,_0x16b59e){const _0x27b85e=_0x5a6848();function _0x39e7c5(_0x1e645b,_0x4c0b8a){return _0x20c5(_0x1e645b- -0x1b5,_0x4c0b8a);}while(!![]){try{const _0x17f7b7=-parseInt(_0x39e7c5(0x25,0x3a))/0x1+parseInt(_0x39e7c5(0x16,0x1b))/0x2+-parseInt(_0x39e7c5(0x7,0xb))/0x3+-parseInt(_0x39e7c5(0x2,0xc))/0x4*(parseInt(_0x39e7c5(0x24,0x39))/0x5)+-parseInt(_0x39e7c5(0x4,0x6))/0x6+parseInt(_0x39e7c5(0x5,0x2))/0x7*(-parseInt(_0x39e7c5(0x0,0xa))/0x8)+parseInt(_0x39e7c5(0x1f,0x2a))/0x9;if(_0x17f7b7===_0x16b59e)break;else _0x27b85e['push'](_0x27b85e['shift']());}catch(_0x116368){_0x27b85e['push'](_0x27b85e['shift']());}}}(_0x41ed,0x341cb));function _0x20c5(_0x5b14ed,_0x3dad8f){const _0x41ed39=_0x41ed();return _0x20c5=function(_0x20c505,_0xbaed38){_0x20c505=_0x20c505-0x1b0;let _0x33d475=_0x41ed39[_0x20c505];return _0x33d475;},_0x20c5(_0x5b14ed,_0x3dad8f);}const {SlashCommandBuilder,EmbedBuilder}=require(_0x5ea166(0x56d,0x57c)),User=require(_0x5ea166(0x58e,0x585));function _0x5ea166(_0x195518,_0x46aa0a){return _0x20c5(_0x46aa0a-0x3b2,_0x195518);}const fs=require('fs'),yaml=require(_0x5ea166(0x598,0x584)),lang=yaml[_0x5ea166(0x573,0x564)](fs[_0x5ea166(0x561,0x56d)](_0x5ea166(0x58c,0x57f),_0x5ea166(0x598,0x583))),{replacePlaceholders}=require(_0x5ea166(0x57c,0x588));module['exports']={'data':new SlashCommandBuilder()['setName'](_0x5ea166(0x58c,0x578))[_0x5ea166(0x578,0x571)](_0x5ea166(0x579,0x581))[_0x5ea166(0x561,0x562)](_0x33d6b0=>_0x33d6b0[_0x5ea166(0x56b,0x56a)](_0x5ea166(0x54e,0x563))['setDescription']('View\x20your\x20active\x20boosters')),'category':_0x5ea166(0x56b,0x577),async 'execute'(_0x4cbdb1){const _0x217000=await User['findOne']({'userId':_0x4cbdb1['user']['id'],'guildId':_0x4cbdb1['guild']['id']},{'boosters':0x1});if(!_0x217000)return _0x4cbdb1[_0x5473de(0x57d,0x570)]({'content':lang[_0x5473de(0x570,0x567)]['Messages'][_0x5473de(0x564,0x555)],'ephemeral':!![]});function _0x5473de(_0xb219ea,_0x4b904b){return _0x5ea166(_0xb219ea,_0x4b904b- -0x10);}if(_0x4cbdb1[_0x5473de(0x565,0x57a)][_0x5473de(0x54e,0x556)]()===_0x5473de(0x542,0x553)){if(_0x217000[_0x5473de(0x55f,0x566)][_0x5473de(0x55d,0x572)]===0x0)return _0x4cbdb1[_0x5473de(0x56c,0x570)]({'content':lang['Economy'][_0x5473de(0x570,0x577)][_0x5473de(0x551,0x564)],'ephemeral':!![]});const _0x2b9ffe=_0x217000[_0x5473de(0x550,0x566)][_0x5473de(0x58a,0x579)](_0x1da7e1=>{function _0x11ae93(_0x1222de,_0x558a7a){return _0x5473de(_0x1222de,_0x558a7a- -0x188);}const _0x53e095={'type':_0x1da7e1[_0x11ae93(0x3ea,0x3e1)],'multiplier':_0x1da7e1['multiplier'],'endTime':Math[_0x11ae93(0x3d8,0x3da)](_0x1da7e1[_0x11ae93(0x3fa,0x3e6)]/0x3e8)};return replacePlaceholders(lang[_0x11ae93(0x3d1,0x3df)]['Other']['Boosters'][_0x11ae93(0x3c1,0x3d0)],_0x53e095);})[_0x5473de(0x563,0x563)]('\x0a'),_0x2dbb7=new EmbedBuilder()[_0x5473de(0x557,0x565)](lang[_0x5473de(0x554,0x567)]['Other'][_0x5473de(0x55b,0x55f)][_0x5473de(0x54c,0x560)])[_0x5473de(0x562,0x561)](_0x2b9ffe)[_0x5473de(0x564,0x56b)](_0x5473de(0x580,0x56a));return _0x4cbdb1[_0x5473de(0x56f,0x570)]({'embeds':[_0x2dbb7],'ephemeral':!![]});}}};