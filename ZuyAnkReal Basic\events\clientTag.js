module.exports = {
    name: 'messageCreate',
    execute(message, client) {
        if (message.author.bot || !message.guild) return;

        if (message.content.trim() === `<@${client.user.id}>`) {
            const embed = {
                color: 0x2b2d31,
                author: {
                    name: `Hey It's Me ${client.user.username}`,
                    icon_url: client.user.displayAvatarURL()
                },
                description: `Hi I'm **[${client.user.username}](https://discord.gg/S6AjkyQKNZ)**, Your Ultimate Server Management Bot. My Commands Will Work Without Prefix`,
                fields: [
                    {
                        name: 'Stratos Information',
                        value: `Language: English\nServer ID: ${message.guild.id}`
                    }
                ],
                thumbnail: {
                    url: client.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 })
                },
                footer: {
                    text: 'Developed With ❤️ By The Arnab',
                    icon_url: 'https://cdn.discordapp.com/attachments/1356296979361169448/1356664519631573203/17fd15ab029147415a5b5dd570db6698.jpg'
                }
                // Removed timestamp here
            };
            
            return message.reply({ 
                embeds: [embed],
                components: [
                    {
                        type: 1,
                        components: [
                            {
                                type: 2,
                                style: 5,
                                label: 'Invite',
                                url: 'https://discord.com/oauth2/authorize?client_id=1294987004483862528&permissions=8&response_type=code&redirect_uri=https%3A%2F%2Fdiscord.gg%2FS6AjkyQKNZ&integration_type=0&scope=connections+guilds.join+bot+applications.commands+guilds'
                            },
                            {
                                type: 2,
                                style: 5,
                                label: 'Support',
                                url: 'https://discord.gg/S6AjkyQKNZ'
                            },
                            {
                                type: 2,
                                style: 5,
                                label: 'Vote',
                                url: 'https://discord.gg/S6AjkyQKNZ'
                            }
                        ]
                    }
                ]
            });
        }
    }
};