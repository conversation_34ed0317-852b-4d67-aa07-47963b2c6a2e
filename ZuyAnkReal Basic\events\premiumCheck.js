const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionR<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require("discord.js");
const fs = require("fs");
const path = require("path");

const premiumFile = path.join(__dirname, "../../data/premium.json");

// Read premium data
function readPremiumData() {
    if (!fs.existsSync(premiumFile)) {
        fs.writeFileSync(premiumFile, JSON.stringify({ users: {} }, null, 2));
    }
    return JSON.parse(fs.readFileSync(premiumFile));
}

// Write premium data
function writePremiumData(data) {
    fs.writeFileSync(premiumFile, JSON.stringify(data, null, 2));
}

// Function to format remaining time properly
function formatTimeRemaining(expiry) {
    let now = Math.floor(Date.now() / 1000);
    let remaining = expiry - now;

    if (remaining <= 0) return "Expired";

    let months = Math.floor(remaining / (30 * 24 * 60 * 60));
    remaining %= (30 * 24 * 60 * 60);
    let days = Math.floor(remaining / (24 * 60 * 60));
    remaining %= (24 * 60 * 60);
    let hours = Math.floor(remaining / (60 * 60));
    remaining %= (60 * 60);
    let minutes = Math.floor(remaining / 60);
    let seconds = remaining % 60;

    let parts = [];
    if (months > 0) parts.push(`${months} month${months > 1 ? "s" : ""}`);
    if (days > 0) parts.push(`${days} day${days > 1 ? "s" : ""}`);
    if (hours > 0) parts.push(`${hours} hour${hours > 1 ? "s" : ""}`);
    if (minutes > 0) parts.push(`${minutes} min${minutes > 1 ? "s" : ""}`);
    if (seconds > 0) parts.push(`${seconds} sec${seconds > 1 ? "s" : ""}`);

    return parts.join(", ");
}

module.exports = {
    name: "premiumCheck",
    execute(user) {
        let data = readPremiumData();
        let userPremium = data.users[user.id];

        // Premium expire ho gaya toh JSON se hatao
        if (userPremium && userPremium.expiry < Math.floor(Date.now() / 1000)) {
            delete data.users[user.id];
            writePremiumData(data); // JSON file update karo
            data = readPremiumData(); // Dubara load karo
            userPremium = null;
        }

        // Agar premium user hai
        if (userPremium) {
            let formattedTime = formatTimeRemaining(userPremium.expiry);

            const premiumEmbed = new EmbedBuilder()
                .setColor("#FFD700")
                .setTitle("🌟 Premium Status")
                .setDescription(`**${user.username}** has premium for **${formattedTime}**.`);

            return { embeds: [premiumEmbed] };
        } else {
            // Agar premium nahi hai, premium buy ka button dikhao
            const buyPremiumEmbed = new EmbedBuilder()
                .setColor("#FF0000")
                .setTitle("❌ No Premium Access!")
                .setDescription(`**${user.username}** doesn't have premium. Buy premium for exclusive benefits!`);

            const row = new ActionRowBuilder().addComponents(
                new ButtonBuilder()
                    .setLabel("Buy Premium")
                    .setStyle(ButtonStyle.Link)
                    .setURL("https://discord.gg/5ZJ9TVNafR")
            );

            return { embeds: [buyPremiumEmbed], components: [row] };
        }
    }
};