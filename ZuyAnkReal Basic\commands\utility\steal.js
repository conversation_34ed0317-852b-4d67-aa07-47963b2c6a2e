const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const axios = require('axios');

module.exports = {
    name: 'steal',
    description: 'Steal emojis or stickers from other servers',
    aliases: ['eadd'],
    usage: '[emoji] or reply to a message with attachments/stickers/emojis',
    permissions: [PermissionFlagsBits.ManageEmojisAndStickers],
    cooldown: 3,
    async execute(message, args) {
        // Check permissions
        if (!message.member.permissions.has(PermissionFlagsBits.ManageEmojisAndStickers)) {
            return message.reply({ embeds: [
                new EmbedBuilder()
                    .setDescription("❌ You need `Manage Emojis & Stickers` permission to use this!")
                    .setColor(0x2f3136)
            ]});
        }

        // Check if replying to a message
        if (message.reference) {
            const refMessage = await message.channel.messages.fetch(message.reference.messageId);
            const attachments = refMessage.attachments;
            const stickers = refMessage.stickers;
            const emojis = refMessage.content.match(/<a?:.+?:\d+>/g) || [];

            if (attachments.size > 0 || stickers.size > 0 || emojis.length > 0) {
                return this.createButtons(message, attachments, stickers, emojis);
            }
        }

        // Process direct emoji input
        if (args[0]) {
            return this.processEmoji(message, args[0]);
        }

        return message.reply({ embeds: [
            new EmbedBuilder()
                .setTitle("Steal")
                .setDescription("No emoji or sticker found")
                .setColor(0x2f3136)
        ]});
    },

    async processEmoji(message, emote) {
        try {
            const emojiRegex = /<?(a)?:?(\w{2,32}):(\d{17,19})>?/;
            const match = emote.match(emojiRegex);
            
            if (!match) {
                return message.reply({ embeds: [
                    new EmbedBuilder()
                        .setTitle("Steal")
                        .setDescription("Invalid emoji format")
                        .setColor(0x2f3136)
                ]});
            }

            const [, animated, name, emojiId] = match;
            const url = `https://cdn.discordapp.com/emojis/${emojiId}.${animated ? 'gif' : 'png'}`;
            
            await this.addEmoji(message, url, name, !!animated);
        } catch (error) {
            console.error(error);
            message.reply({ embeds: [
                new EmbedBuilder()
                    .setTitle("Steal")
                    .setDescription(`Failed to add emoji: ${error.message}`)
                    .setColor(0x2f3136)
            ]});
        }
    },

    async addEmoji(message, url, name, animated) {
        try {
            if (!this.hasEmojiSlot(message.guild, animated)) {
                return message.reply({ embeds: [
                    new EmbedBuilder()
                        .setTitle("Steal")
                        .setDescription("No more emoji slots available")
                        .setColor(0x2f3136)
                ]});
            }

            const sanitizedName = this.sanitizeName(name);
            const response = await axios.get(url, { responseType: 'arraybuffer' });
            const emoji = await message.guild.emojis.create({
                attachment: response.data,
                name: sanitizedName
            });

            return message.reply({ embeds: [
                new EmbedBuilder()
                    .setTitle("Steal")
                    .setDescription(`Added emoji **${emoji}**!`)
                    .setColor(0x2f3136)
            ]});
        } catch (error) {
            console.error(error);
            return message.reply({ embeds: [
                new EmbedBuilder()
                    .setTitle("Steal")
                    .setDescription(`Failed to add emoji: ${error.message}`)
                    .setColor(0x2f3136)
            ]});
        }
    },

    async addSticker(message, url, name) {
        try {
            if (message.guild.stickers.cache.size >= this.getMaxStickerCount(message.guild)) {
                return message.reply({ embeds: [
                    new EmbedBuilder()
                        .setTitle("Steal")
                        .setDescription("No more sticker slots available")
                        .setColor(0x2f3136)
                ]});
            }

            const sanitizedName = this.sanitizeName(name);
            const response = await axios.get(url, { responseType: 'arraybuffer' });
            
            // Note: Discord.js v14+ sticker creation requires file upload
            await message.guild.stickers.create({
                file: Buffer.from(response.data),
                name: sanitizedName,
                description: "Added by bot",
                tags: "stolen"
            });

            return message.reply({ embeds: [
                new EmbedBuilder()
                    .setTitle("Steal")
                    .setDescription(`Added sticker **${sanitizedName}**!`)
                    .setColor(0x2f3136)
            ]});
        } catch (error) {
            console.error(error);
            return message.reply({ embeds: [
                new EmbedBuilder()
                    .setTitle("Steal")
                    .setDescription(`Failed to add sticker: ${error.message}`)
                    .setColor(0x2f3136)
            ]});
        }
    },

    sanitizeName(name) {
        return name.replace(/[^a-zA-Z0-9_]/g, '_').slice(0, 32);
    },

    hasEmojiSlot(guild, animated) {
        const normalEmojis = guild.emojis.cache.filter(e => !e.animated).size;
        const animatedEmojis = guild.emojis.cache.filter(e => e.animated).size;
        const [maxNormal, maxAnimated] = this.getMaxEmojiCount(guild);

        return animated ? animatedEmojis < maxAnimated : normalEmojis < maxNormal;
    },

    getMaxEmojiCount(guild) {
        switch (guild.premiumTier) {
            case 'TIER_3': return [250, 250];
            case 'TIER_2': return [150, 150];
            case 'TIER_1': return [100, 100];
            default: return [50, 50];
        }
    },

    getMaxStickerCount(guild) {
        switch (guild.premiumTier) {
            case 'TIER_3': return 60;
            case 'TIER_2': return 30;
            case 'TIER_1': return 15;
            default: return 5;
        }
    },

    async createButtons(message, attachments, stickers, emojis) {
        const embed = new EmbedBuilder()
            .setDescription("Choose what to steal:")
            .setColor(0x2f3136);

        // Set image preview
        if (attachments.size > 0) {
            embed.setImage(attachments.first().url);
        } else if (stickers.size > 0) {
            embed.setImage(stickers.first().url);
        } else if (emojis.length > 0) {
            const emojiId = emojis[0].match(/:(\d+)>/)[1];
            embed.setImage(`https://cdn.discordapp.com/emojis/${emojiId}.png`);
        }

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('steal_emoji')
                    .setLabel('Steal as Emoji')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('steal_sticker')
                    .setLabel('Steal as Sticker')
                    .setStyle(ButtonStyle.Success)
            );

        const reply = await message.reply({ 
            embeds: [embed], 
            components: [row] 
        });

        // Button collector
        const collector = reply.createMessageComponentCollector({ time: 15000 });

        collector.on('collect', async interaction => {
            if (interaction.user.id !== message.author.id) {
                return interaction.reply({ 
                    content: "This interaction is not for you.", 
                    ephemeral: true 
                });
            }

            await interaction.deferUpdate();

            try {
                if (interaction.customId === 'steal_emoji') {
                    // Process stickers as emojis
                    for (const sticker of stickers.values()) {
                        const animated = sticker.format === 'APNG';
                        await this.addEmoji(message, sticker.url, sticker.name.replace(' ', '_'), animated);
                    }
                    
                    // Process attachments as emojis
                    for (const attachment of attachments.values()) {
                        await this.addEmoji(message, attachment.url, attachment.name.split('.')[0].replace(' ', '_'), false);
                    }
                    
                    // Process emojis
                    for (const emote of emojis) {
                        await this.processEmoji(message, emote);
                    }
                } 
                else if (interaction.customId === 'steal_sticker') {
                    // Process stickers
                    for (const sticker of stickers.values()) {
                        await this.addSticker(message, sticker.url, sticker.name);
                    }
                    
                    // Process attachments as stickers
                    for (const attachment of attachments.values()) {
                        await this.addSticker(message, attachment.url, attachment.name.split('.')[0]);
                    }
                    
                    // Process emojis as stickers
                    for (const emote of emojis) {
                        const name = emote.split(':')[1];
                        const emojiId = emote.split(':')[2].replace('>', '');
                        const url = `https://cdn.discordapp.com/emojis/${emojiId}.png`;
                        await this.addSticker(message, url, name);
                    }
                }
            } catch (error) {
                console.error(error);
            }
        });

        collector.on('end', () => {
            reply.edit({ components: [] }).catch(console.error);
        });
    }
};