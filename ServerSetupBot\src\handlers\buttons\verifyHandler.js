const { createSuccessEmbed, createErrorEmbed } = require('../../utils/embedBuilder.js');
const { canManageRole } = require('../../utils/permissions.js');

module.exports = {
    async execute(interaction, client, params) {
        const guildId = interaction.guild.id;
        const member = interaction.member;
        
        try {
            // Lấy cấu hình verification
            const config = await client.db.getVerificationConfig(guildId);
            
            if (!config || !config.enabled) {
                const errorEmbed = createErrorEmbed(
                    '<PERSON>ệ thống xác minh đã tắt!',
                    '<PERSON><PERSON> thống xác minh hiện không hoạt động.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Lấy role verified
            const verifiedRole = interaction.guild.roles.cache.get(config.verified_role_id);
            if (!verifiedRole) {
                const errorEmbed = createErrorEmbed(
                    'Lỗi cấu hình!',
                    'Role xác minh không tồn tại. Vui lòng liên hệ admin!'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Kiểm tra member đã có role chưa
            if (member.roles.cache.has(verifiedRole.id)) {
                const infoEmbed = createSuccessEmbed(
                    'Đã xác minh!',
                    'Bạn đã được xác minh trước đó rồi!'
                );
                return await interaction.reply({ embeds: [infoEmbed], ephemeral: true });
            }
            
            // Kiểm tra quyền bot
            const canManage = canManageRole(interaction.guild, verifiedRole);
            if (!canManage.canManage) {
                const errorEmbed = createErrorEmbed(
                    'Lỗi quyền hạn!',
                    `Bot không thể gán role: ${canManage.reason}\n\nVui lòng liên hệ admin!`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Gán role verified
            await member.roles.add(verifiedRole, 'Verification completed');
            
            // Xóa role unverified nếu có
            if (config.verification_role_id) {
                const unverifiedRole = interaction.guild.roles.cache.get(config.verification_role_id);
                if (unverifiedRole && member.roles.cache.has(unverifiedRole.id)) {
                    await member.roles.remove(unverifiedRole, 'Verification completed');
                }
            }
            
            const successEmbed = createSuccessEmbed(
                'Xác minh thành công! 🎉',
                `Chào mừng **${member.user.username}** đến với **${interaction.guild.name}**!\n\n` +
                `Bạn đã được xác minh và có thể truy cập đầy đủ vào server.\n` +
                `Hãy tận hưởng thời gian tại đây! ${client.config.emojis.welcome}`
            );
            
            successEmbed.setThumbnail(member.user.displayAvatarURL({ dynamic: true }));
            
            await interaction.reply({ embeds: [successEmbed], ephemeral: true });
            
            console.log(`✅ Đã xác minh thành viên ${member.user.tag} trong ${interaction.guild.name}`);
            
        } catch (error) {
            console.error('Lỗi khi xử lý verification button:', error);
            
            const errorEmbed = createErrorEmbed(
                'Lỗi xác minh!',
                'Đã xảy ra lỗi khi xác minh. Vui lòng thử lại hoặc liên hệ admin!'
            );
            
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
