const db = require('../database/database');
const Item = require('./Item');

class Inventory {
    constructor(data) {
        this.id = data.id;
        this.userId = data.user_id;
        this.itemId = data.item_id;
        this.quantity = data.quantity;
        this.acquiredAt = data.acquired_at;
    }

    // Lấy inventory của user
    static async getUserInventory(userId) {
        try {
            const inventory = await db.all(`
                SELECT i.*, it.name, it.description, it.emoji, it.category, it.sell_price
                FROM inventory i
                JOIN items it ON i.item_id = it.id
                WHERE i.user_id = ?
                ORDER BY it.category, it.name
            `, [userId]);

            return inventory.map(item => ({
                ...new Inventory(item),
                item: {
                    name: item.name,
                    description: item.description,
                    emoji: item.emoji,
                    category: item.category,
                    sellPrice: item.sell_price
                }
            }));
        } catch (error) {
            console.error('Lỗi lấy inventory:', error);
            throw error;
        }
    }

    // Kiểm tra user có item không
    static async hasItem(userId, itemId) {
        try {
            const item = await db.get(`
                SELECT quantity FROM inventory 
                WHERE user_id = ? AND item_id = ?
            `, [userId, itemId]);

            return item ? item.quantity : 0;
        } catch (error) {
            console.error('Lỗi kiểm tra item:', error);
            throw error;
        }
    }

    // Thêm item vào inventory
    static async addItem(userId, itemId, quantity = 1) {
        try {
            // Kiểm tra xem đã có item chưa
            const existingItem = await db.get(`
                SELECT * FROM inventory 
                WHERE user_id = ? AND item_id = ?
            `, [userId, itemId]);

            if (existingItem) {
                // Cập nhật số lượng
                await db.run(`
                    UPDATE inventory 
                    SET quantity = quantity + ? 
                    WHERE user_id = ? AND item_id = ?
                `, [quantity, userId, itemId]);
            } else {
                // Thêm mới
                await db.run(`
                    INSERT INTO inventory (user_id, item_id, quantity)
                    VALUES (?, ?, ?)
                `, [userId, itemId, quantity]);
            }

            return true;
        } catch (error) {
            console.error('Lỗi thêm item:', error);
            throw error;
        }
    }

    // Xóa item khỏi inventory
    static async removeItem(userId, itemId, quantity = 1) {
        try {
            const existingItem = await db.get(`
                SELECT quantity FROM inventory 
                WHERE user_id = ? AND item_id = ?
            `, [userId, itemId]);

            if (!existingItem) {
                throw new Error('Bạn không có vật phẩm này');
            }

            if (existingItem.quantity < quantity) {
                throw new Error('Số lượng không đủ');
            }

            if (existingItem.quantity === quantity) {
                // Xóa hoàn toàn
                await db.run(`
                    DELETE FROM inventory 
                    WHERE user_id = ? AND item_id = ?
                `, [userId, itemId]);
            } else {
                // Giảm số lượng
                await db.run(`
                    UPDATE inventory 
                    SET quantity = quantity - ? 
                    WHERE user_id = ? AND item_id = ?
                `, [quantity, userId, itemId]);
            }

            return true;
        } catch (error) {
            console.error('Lỗi xóa item:', error);
            throw error;
        }
    }

    // Lấy tổng giá trị inventory
    static async getInventoryValue(userId) {
        try {
            const result = await db.get(`
                SELECT SUM(i.quantity * it.sell_price) as total_value
                FROM inventory i
                JOIN items it ON i.item_id = it.id
                WHERE i.user_id = ?
            `, [userId]);

            return result ? (result.total_value || 0) : 0;
        } catch (error) {
            console.error('Lỗi tính giá trị inventory:', error);
            throw error;
        }
    }

    // Lấy thống kê inventory
    static async getInventoryStats(userId) {
        try {
            const stats = await db.get(`
                SELECT 
                    COUNT(DISTINCT i.item_id) as unique_items,
                    SUM(i.quantity) as total_items,
                    SUM(i.quantity * it.sell_price) as total_value
                FROM inventory i
                JOIN items it ON i.item_id = it.id
                WHERE i.user_id = ?
            `, [userId]);

            return {
                uniqueItems: stats ? (stats.unique_items || 0) : 0,
                totalItems: stats ? (stats.total_items || 0) : 0,
                totalValue: stats ? (stats.total_value || 0) : 0
            };
        } catch (error) {
            console.error('Lỗi lấy stats inventory:', error);
            throw error;
        }
    }

    // Lấy items theo category trong inventory
    static async getItemsByCategory(userId, category) {
        try {
            const items = await db.all(`
                SELECT i.*, it.name, it.description, it.emoji, it.sell_price
                FROM inventory i
                JOIN items it ON i.item_id = it.id
                WHERE i.user_id = ? AND it.category = ?
                ORDER BY it.name
            `, [userId, category]);

            return items.map(item => ({
                ...new Inventory(item),
                item: {
                    name: item.name,
                    description: item.description,
                    emoji: item.emoji,
                    sellPrice: item.sell_price
                }
            }));
        } catch (error) {
            console.error('Lỗi lấy items theo category:', error);
            throw error;
        }
    }
}

module.exports = Inventory;
