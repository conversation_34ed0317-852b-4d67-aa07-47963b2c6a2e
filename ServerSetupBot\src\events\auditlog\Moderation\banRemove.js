const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildBanRemove,
    async execute(ban, client) {
        try {
            console.log(`🔓 User unbanned: ${ban.user.tag} in ${ban.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(ban.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'BAN_REMOVE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'BAN_REMOVE',
                user: ban.user.tag,
                userId: ban.user.id,
                action: 'Thành viên được unban',
                details: `**${ban.user.tag}** đã được unban khỏi server`,
                target: ban.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who unbanned the user from audit logs
            try {
                const auditLogs = await ban.guild.fetchAuditLogs({
                    type: 23, // MEMBER_BAN_REMOVE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === ban.user.id) {
                    eventData.moderator = auditEntry.executor.tag;
                    eventData.moderatorId = auditEntry.executor.id;
                    eventData.action = `Thành viên được unban bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for ban remove');
            }
            
            // Add to database
            await client.db.addAuditLog(ban.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔓 Thành viên được unban',
                `Vừa có thành viên được unban khỏi server`
            );
            
            embed.setColor(0x2ecc71); // Green for unban
            
            embed.addFields([
                {
                    name: '> Thành viên được unban',
                    value: `- ${ban.user.tag} (${ban.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${ban.user.id}`,
                    inline: true
                },
                {
                    name: '> Thời gian unban',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add moderator info if available
            if (eventData.moderator) {
                embed.addFields({
                    name: '> Moderator',
                    value: `- ${eventData.moderator}`,
                    inline: true
                });
            }
            
            // Add unban reason if available
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do unban',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Add user info
            const accountAge = Date.now() - ban.user.createdTimestamp;
            const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
            
            embed.addFields([
                {
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                },
                {
                    name: '> Tạo tài khoản lúc',
                    value: `- <t:${Math.floor(ban.user.createdTimestamp / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add unban impact
            embed.addFields({
                name: '> 📝 Tác động của unban',
                value: [
                    '• Thành viên có thể tham gia lại server',
                    '• Cần invite link để vào lại',
                    '• Sẽ không có roles cũ',
                    '• Bắt đầu lại như thành viên mới'
                ].join('\n'),
                inline: false
            });
            
            // Add guidelines
            embed.addFields({
                name: '> 💡 Lưu ý sau unban',
                value: [
                    '• Theo dõi hành vi của thành viên',
                    '• Có thể thiết lập thời gian thử thách',
                    '• Ghi chép lý do unban',
                    '• Thông báo cho team moderation'
                ].join('\n'),
                inline: false
            });
            
            // Set user avatar as thumbnail
            if (ban.user.displayAvatarURL()) {
                embed.setThumbnail(ban.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${ban.guild.name} • Member Ban Remove`,
                iconURL: ban.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Ban remove logged: ${ban.user.tag}`);
            
        } catch (error) {
            console.error('Error in banRemove audit log:', error);
        }
    }
};
