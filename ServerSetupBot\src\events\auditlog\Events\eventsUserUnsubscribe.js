const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildScheduledEventUserRemove,
    async execute(guildScheduledEvent, user, client) {
        try {
            console.log(`👤 User unsubscribed from event: ${user.tag} -> ${guildScheduledEvent.name} in ${guildScheduledEvent.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(guildScheduledEvent.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EVENTS_USER_UNSUBSCRIBE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EVENTS_USER_UNSUBSCRIBE',
                user: user.tag,
                userId: user.id,
                action: 'Ngư<PERSON><PERSON> dùng bỏ quan tâm sự kiện',
                details: `**${user.tag}** đã bỏ quan tâm sự kiện **${guildScheduledEvent.name}**`,
                target: guildScheduledEvent.name,
                timestamp: new Date().toISOString()
            };
            
            // Add to database
            await client.db.addAuditLog(guildScheduledEvent.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '👤 Người dùng bỏ quan tâm sự kiện',
                `Vừa có người dùng bỏ quan tâm sự kiện`
            );
            
            embed.setColor(0xe67e22); // Orange for unsubscription
            
            // Get event status names
            const statusTypes = {
                1: 'Scheduled',
                2: 'Active',
                3: 'Completed',
                4: 'Canceled'
            };
            
            // Get event entity type names
            const entityTypes = {
                1: 'Stage Instance',
                2: 'Voice Channel',
                3: 'External Location'
            };
            
            embed.addFields([
                {
                    name: '> Người dùng',
                    value: `- ${user.tag} (${user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${user.id}`,
                    inline: true
                },
                {
                    name: '> Tên sự kiện',
                    value: `- ${guildScheduledEvent.name}`,
                    inline: true
                },
                {
                    name: '> ID sự kiện',
                    value: `- ${guildScheduledEvent.id}`,
                    inline: true
                },
                {
                    name: '> Loại sự kiện',
                    value: `- ${entityTypes[guildScheduledEvent.entityType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Trạng thái sự kiện',
                    value: `- ${statusTypes[guildScheduledEvent.status] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Thời gian bỏ quan tâm',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add event timing info
            if (guildScheduledEvent.scheduledStartTimestamp) {
                embed.addFields({
                    name: '> Thời gian bắt đầu sự kiện',
                    value: `- <t:${Math.floor(guildScheduledEvent.scheduledStartTimestamp / 1000)}:F>`,
                    inline: true
                });
                
                // Calculate time until event
                const timeUntilEvent = guildScheduledEvent.scheduledStartTimestamp - Date.now();
                if (timeUntilEvent > 0) {
                    const days = Math.floor(timeUntilEvent / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((timeUntilEvent % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((timeUntilEvent % (1000 * 60 * 60)) / (1000 * 60));
                    
                    let timeText = '';
                    if (days > 0) timeText += `${days} ngày `;
                    if (hours > 0) timeText += `${hours} giờ `;
                    if (minutes > 0) timeText += `${minutes} phút`;
                    
                    embed.addFields({
                        name: '> Thời gian còn lại',
                        value: `- ${timeText.trim()}`,
                        inline: true
                    });
                }
            }
            
            if (guildScheduledEvent.scheduledEndTimestamp) {
                embed.addFields({
                    name: '> Thời gian kết thúc sự kiện',
                    value: `- <t:${Math.floor(guildScheduledEvent.scheduledEndTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add current subscriber count if available
            if (guildScheduledEvent.userCount !== null) {
                embed.addFields({
                    name: '> Tổng số người quan tâm',
                    value: `- ${guildScheduledEvent.userCount} người`,
                    inline: true
                });
            }
            
            // Add user info
            const member = guildScheduledEvent.guild.members.cache.get(user.id);
            if (member) {
                const accountAge = Date.now() - user.createdTimestamp;
                const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
                
                embed.addFields([
                    {
                        name: '> Tuổi tài khoản',
                        value: `- ${accountAgeDays} ngày`,
                        inline: true
                    },
                    {
                        name: '> Tham gia server lúc',
                        value: `- <t:${Math.floor(member.joinedTimestamp / 1000)}:F>`,
                        inline: true
                    }
                ]);
                
                const roleCount = member.roles.cache.size - 1; // -1 for @everyone
                if (roleCount > 0) {
                    embed.addFields({
                        name: '> Số roles',
                        value: `- ${roleCount} roles`,
                        inline: true
                    });
                }
            }
            
            // Add location info if available
            if (guildScheduledEvent.entityMetadata?.location) {
                embed.addFields({
                    name: '> Địa điểm',
                    value: `- ${guildScheduledEvent.entityMetadata.location}`,
                    inline: true
                });
            }
            
            // Add channel info if it's a voice/stage event
            if (guildScheduledEvent.channel) {
                embed.addFields({
                    name: '> Kênh',
                    value: `- ${guildScheduledEvent.channel}`,
                    inline: true
                });
            }
            
            // Add unsubscription impact
            embed.addFields({
                name: '> 📝 Tác động khi bỏ quan tâm',
                value: [
                    '• Không nhận thông báo khi sự kiện bắt đầu',
                    '• Không hiển thị trong danh sách người quan tâm',
                    '• Không nhận reminder trước sự kiện',
                    '• Có thể đăng ký lại bất cứ lúc nào'
                ].join('\n'),
                inline: false
            });
            
            // Add possible reasons for unsubscription
            embed.addFields({
                name: '> 🤔 Lý do có thể',
                value: [
                    '• Thay đổi lịch trình cá nhân',
                    '• Không còn quan tâm đến sự kiện',
                    '• Nhầm lẫn khi đăng ký',
                    '• Sự kiện bị thay đổi thông tin'
                ].join('\n'),
                inline: false
            });
            
            // Set user avatar as thumbnail
            if (user.displayAvatarURL()) {
                embed.setThumbnail(user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            // Set event image if available
            if (guildScheduledEvent.coverImageURL()) {
                embed.setImage(guildScheduledEvent.coverImageURL({ size: 512 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${guildScheduledEvent.guild.name} • Event User Unsubscribe`,
                iconURL: guildScheduledEvent.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Event user unsubscribe logged: ${user.tag} -> ${guildScheduledEvent.name}`);
            
        } catch (error) {
            console.error('Error in eventsUserUnsubscribe audit log:', error);
        }
    }
};
