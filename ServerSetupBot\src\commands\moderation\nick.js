const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('nick')
        .setDescription('Quản lý nickname của thành viên')
        .addSubcommand(subcommand =>
            subcommand
                .setName('set')
                .setDescription('Đặt nickname cho thành viên')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('Thành viên cần đặt nickname')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('nickname')
                        .setDescription('Nickname mới (để trống để xóa nickname)')
                        .setRequired(false)
                        .setMaxLength(32)
                )
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Lý do thay đổi nickname')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('reset')
                .setDescription('Reset nickname về tên gốc')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('Thành viên cần reset nickname')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Lý do reset nickname')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('lock')
                .setDescription('Khóa nickname (không cho thay đổi)')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('Thành viên cần khóa nickname')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Lý do khóa nickname')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('unlock')
                .setDescription('Mở khóa nickname')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('Thành viên cần mở khóa nickname')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Lý do mở khóa nickname')
                        .setRequired(false)
                )
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.ManageNicknames)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                'Bạn cần quyền `Manage Nicknames` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageNicknames)) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                'Bot cần quyền `Manage Nicknames` để thực hiện lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const subcommand = interaction.options.getSubcommand();
        const user = interaction.options.getUser('user');
        const reason = interaction.options.getString('reason') || 'Không có lý do';
        const guildId = interaction.guild.id;
        
        try {
            const member = await interaction.guild.members.fetch(user.id);
            
            // Kiểm tra hierarchy
            if (member.roles.highest.position >= interaction.member.roles.highest.position) {
                const errorEmbed = createErrorEmbed(
                    'Không thể thay đổi nickname!',
                    'Bạn không thể thay đổi nickname của thành viên có role cao hơn hoặc bằng bạn.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            if (member.roles.highest.position >= interaction.guild.members.me.roles.highest.position) {
                const errorEmbed = createErrorEmbed(
                    'Không thể thay đổi nickname!',
                    'Bot không thể thay đổi nickname của thành viên có role cao hơn hoặc bằng bot.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            const oldNickname = member.nickname || member.user.username;
            let newNickname;
            let action;
            
            switch (subcommand) {
                case 'set':
                    newNickname = interaction.options.getString('nickname') || null;
                    action = newNickname ? 'Đặt nickname' : 'Xóa nickname';
                    
                    await member.setNickname(newNickname, `${reason} | Bởi: ${interaction.user.tag}`);
                    break;
                    
                case 'reset':
                    newNickname = null;
                    action = 'Reset nickname';
                    
                    await member.setNickname(null, `${reason} | Bởi: ${interaction.user.tag}`);
                    break;
                    
                case 'lock':
                    // Lưu trạng thái lock vào database
                    await client.db.setNicknameLock(guildId, user.id, true, interaction.user.id, reason);
                    action = 'Khóa nickname';
                    newNickname = oldNickname;
                    break;
                    
                case 'unlock':
                    // Xóa trạng thái lock khỏi database
                    await client.db.setNicknameLock(guildId, user.id, false, interaction.user.id, reason);
                    action = 'Mở khóa nickname';
                    newNickname = oldNickname;
                    break;
            }
            
            // Log vào database
            await client.db.addModerationLog(guildId, {
                type: 'nickname',
                userId: user.id,
                moderatorId: interaction.user.id,
                reason: reason,
                duration: null
            });
            
            const displayNewNick = newNickname || user.username;
            
            const successEmbed = createSuccessEmbed(
                `✅ ${action} thành công!`,
                `**Thành viên:** ${user.tag} (${user.id})\n` +
                `**Nickname cũ:** ${oldNickname}\n` +
                `**Nickname mới:** ${displayNewNick}\n` +
                `**Lý do:** ${reason}\n` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            if (subcommand === 'lock') {
                successEmbed.addFields({
                    name: '🔒 Nickname đã bị khóa',
                    value: 'Thành viên này không thể thay đổi nickname cho đến khi được mở khóa.',
                    inline: false
                });
            } else if (subcommand === 'unlock') {
                successEmbed.addFields({
                    name: '🔓 Nickname đã được mở khóa',
                    value: 'Thành viên này có thể thay đổi nickname bình thường.',
                    inline: false
                });
            }
            
            await interaction.reply({ embeds: [successEmbed] });
            
            // Gửi log vào channel
            await sendModerationLog(client, guildId, {
                type: 'nickname',
                user: user,
                moderator: interaction.user,
                reason: reason,
                action: action,
                oldValue: oldNickname,
                newValue: displayNewNick
            });
            
        } catch (error) {
            console.error('Lỗi khi thay đổi nickname:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi thay đổi nickname!',
                'Đã xảy ra lỗi khi thay đổi nickname. Vui lòng thử lại sau!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
