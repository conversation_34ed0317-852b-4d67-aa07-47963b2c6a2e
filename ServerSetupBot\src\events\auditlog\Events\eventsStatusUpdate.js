const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildScheduledEventUpdate,
    async execute(oldEvent, newEvent, client) {
        try {
            // Only handle status changes
            if (oldEvent.status === newEvent.status) return;
            
            console.log(`📊 Event status updated: ${newEvent.name} in ${newEvent.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newEvent.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EVENTS_STATUS_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EVENTS_STATUS_UPDATE',
                user: 'System',
                userId: null,
                action: 'Trạng thái sự kiện được cập nhật',
                details: `Tr<PERSON>ng thái của sự kiện **${newEvent.name}** đã được cập nhật`,
                target: newEvent.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the event status from audit logs
            try {
                const auditLogs = await newEvent.guild.fetchAuditLogs({
                    type: 102, // GUILD_SCHEDULED_EVENT_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newEvent.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Trạng thái sự kiện được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for event status update');
            }
            
            // Add to database
            await client.db.addAuditLog(newEvent.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Get event status names and emojis
            const statusTypes = {
                1: { name: 'Scheduled (Đã lên lịch)', emoji: '📅' },
                2: { name: 'Active (Đang diễn ra)', emoji: '🔴' },
                3: { name: 'Completed (Đã hoàn thành)', emoji: '✅' },
                4: { name: 'Canceled (Đã hủy)', emoji: '❌' }
            };
            
            const oldStatus = statusTypes[oldEvent.status] || { name: 'Unknown', emoji: '❓' };
            const newStatus = statusTypes[newEvent.status] || { name: 'Unknown', emoji: '❓' };
            
            // Create embed
            const embed = createInfoEmbed(
                `${newStatus.emoji} Trạng thái sự kiện được cập nhật`,
                `Vừa có trạng thái sự kiện được thay đổi`
            );
            
            // Set color based on new status
            const statusColors = {
                1: 0x3498db, // Blue for scheduled
                2: 0xe74c3c, // Red for active
                3: 0x2ecc71, // Green for completed
                4: 0x95a5a6  // Gray for canceled
            };
            embed.setColor(statusColors[newEvent.status] || 0xf39c12);
            
            embed.addFields([
                {
                    name: '> Tên sự kiện',
                    value: `- ${newEvent.name}`,
                    inline: true
                },
                {
                    name: '> ID sự kiện',
                    value: `- ${newEvent.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian thay đổi',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Trạng thái cũ',
                    value: `- ${oldStatus.emoji} ${oldStatus.name}`,
                    inline: true
                },
                {
                    name: '> Trạng thái mới',
                    value: `- ${newStatus.emoji} ${newStatus.name}`,
                    inline: true
                }
            ]);
            
            // Add event timing info
            if (newEvent.scheduledStartTimestamp) {
                embed.addFields({
                    name: '> Thời gian bắt đầu',
                    value: `- <t:${Math.floor(newEvent.scheduledStartTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            if (newEvent.scheduledEndTimestamp) {
                embed.addFields({
                    name: '> Thời gian kết thúc',
                    value: `- <t:${Math.floor(newEvent.scheduledEndTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add subscriber count if available
            if (newEvent.userCount !== null) {
                embed.addFields({
                    name: '> Số người quan tâm',
                    value: `- ${newEvent.userCount} người`,
                    inline: true
                });
            }
            
            // Add impact explanation based on status change
            if (newEvent.status === 2) { // Active
                embed.addFields({
                    name: '> 🔴 Tác động (Sự kiện đang diễn ra)',
                    value: [
                        '• Sự kiện đã bắt đầu và đang diễn ra',
                        '• Thành viên có thể tham gia ngay',
                        '• Hiển thị trạng thái "Live" cho mọi người',
                        '• Thông báo tự động cho người quan tâm'
                    ].join('\n'),
                    inline: false
                });
            } else if (newEvent.status === 3) { // Completed
                embed.addFields({
                    name: '> ✅ Tác động (Sự kiện đã hoàn thành)',
                    value: [
                        '• Sự kiện đã kết thúc thành công',
                        '• Không thể tham gia thêm',
                        '• Lưu trữ trong lịch sử sự kiện',
                        '• Có thể xem lại thông tin'
                    ].join('\n'),
                    inline: false
                });
            } else if (newEvent.status === 4) { // Canceled
                embed.addFields({
                    name: '> ❌ Tác động (Sự kiện đã hủy)',
                    value: [
                        '• Sự kiện không diễn ra nữa',
                        '• Thông báo hủy cho người quan tâm',
                        '• Không thể tham gia',
                        '• Có thể tạo sự kiện mới thay thế'
                    ].join('\n'),
                    inline: false
                });
            } else if (newEvent.status === 1) { // Scheduled
                embed.addFields({
                    name: '> 📅 Tác động (Sự kiện đã lên lịch)',
                    value: [
                        '• Sự kiện đang chờ thời gian diễn ra',
                        '• Thành viên có thể đăng ký quan tâm',
                        '• Sẽ có thông báo khi bắt đầu',
                        '• Có thể chỉnh sửa thông tin'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add status explanation
            embed.addFields({
                name: '> 💡 Giải thích trạng thái sự kiện',
                value: [
                    '📅 **Scheduled:** Sự kiện đã lên lịch, chờ diễn ra',
                    '🔴 **Active:** Sự kiện đang diễn ra',
                    '✅ **Completed:** Sự kiện đã hoàn thành',
                    '❌ **Canceled:** Sự kiện đã bị hủy'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set event image as thumbnail if available
            if (newEvent.coverImageURL()) {
                embed.setThumbnail(newEvent.coverImageURL({ size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newEvent.guild.name} • Event Status Update`,
                iconURL: newEvent.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Event status update logged: ${newEvent.name}`);
            
        } catch (error) {
            console.error('Error in eventsStatusUpdate audit log:', error);
        }
    }
};
