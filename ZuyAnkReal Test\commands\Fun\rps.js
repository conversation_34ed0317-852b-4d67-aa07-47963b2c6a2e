function _0x43fc(_0x4d4b38,_0x4b1f1e){const _0x51140e=_0x5114();return _0x43fc=function(_0x43fcc3,_0x167838){_0x43fcc3=_0x43fcc3-0xa7;let _0x1afb04=_0x51140e[_0x43fcc3];return _0x1afb04;},_0x43fc(_0x4d4b38,_0x4b1f1e);}function _0x22432b(_0x2c9a28,_0x11419b){return _0x43fc(_0x11419b-0x385,_0x2c9a28);}(function(_0x422e15,_0x39aba8){function _0x27d7c1(_0x400836,_0x503efa){return _0x43fc(_0x400836- -0x2e4,_0x503efa);}const _0xf68f80=_0x422e15();while(!![]){try{const _0x2143f8=parseInt(_0x27d7c1(-0x21f,-0x202))/0x1+-parseInt(_0x27d7c1(-0x1f8,-0x202))/0x2*(-parseInt(_0x27d7c1(-0x21c,-0x23e))/0x3)+parseInt(_0x27d7c1(-0x20e,-0x232))/0x4*(parseInt(_0x27d7c1(-0x208,-0x1f2))/0x5)+-parseInt(_0x27d7c1(-0x212,-0x207))/0x6*(parseInt(_0x27d7c1(-0x1f7,-0x1ec))/0x7)+parseInt(_0x27d7c1(-0x20a,-0x220))/0x8+-parseInt(_0x27d7c1(-0x237,-0x23f))/0x9+-parseInt(_0x27d7c1(-0x223,-0x20b))/0xa*(-parseInt(_0x27d7c1(-0x236,-0x258))/0xb);if(_0x2143f8===_0x39aba8)break;else _0xf68f80['push'](_0xf68f80['shift']());}catch(_0x124ebb){_0xf68f80['push'](_0xf68f80['shift']());}}}(_0x5114,0x5e57d));const {SlashCommandBuilder,ActionRowBuilder,ButtonBuilder,ButtonStyle,EmbedBuilder}=require(_0x22432b(0x445,0x439)),fs=require('fs'),yaml=require(_0x22432b(0x43b,0x453)),lang=yaml[_0x22432b(0x41e,0x42f)](fs[_0x22432b(0x478,0x45e)](_0x22432b(0x440,0x42d),_0x22432b(0x45b,0x43b)));module[_0x22432b(0x482,0x46e)]={'data':new SlashCommandBuilder()[_0x22432b(0x44a,0x434)](_0x22432b(0x42d,0x440))[_0x22432b(0x47d,0x469)]('Play\x20Rock\x20Paper\x20Scissors!'),'category':'Fun',async 'execute'(_0x12e05f){function _0x50e37b(_0x1cfe49,_0x51844e){return _0x22432b(_0x51844e,_0x1cfe49- -0x49);}try{const _0x4e5bbf=new EmbedBuilder()['setColor'](lang[_0x50e37b(0x41b,0x43a)][_0x50e37b(0x3e5,0x3dc)]['Color'])['setTitle'](lang[_0x50e37b(0x41b,0x417)]['Embed']['Title'])['setDescription'](lang['RockPaperScissors'][_0x50e37b(0x3e5,0x3d3)][_0x50e37b(0x421,0x445)])[_0x50e37b(0x3ff,0x416)]({'name':lang[_0x50e37b(0x41b,0x41a)][_0x50e37b(0x3e5,0x3e0)]['Fields'][_0x50e37b(0x408,0x424)][_0x50e37b(0x3fe,0x3ec)],'value':lang[_0x50e37b(0x41b,0x43f)][_0x50e37b(0x3e5,0x3d8)][_0x50e37b(0x3fa,0x418)][_0x50e37b(0x408,0x427)]['Value'],'inline':!![]},{'name':lang[_0x50e37b(0x41b,0x415)]['Embed'][_0x50e37b(0x3fa,0x413)][_0x50e37b(0x3f8,0x417)][_0x50e37b(0x3fe,0x41a)],'value':lang[_0x50e37b(0x41b,0x403)][_0x50e37b(0x3e5,0x3e5)][_0x50e37b(0x3fa,0x3ee)][_0x50e37b(0x3f8,0x405)][_0x50e37b(0x3f4,0x3d2)],'inline':!![]},{'name':lang[_0x50e37b(0x41b,0x426)]['Embed'][_0x50e37b(0x3fa,0x40a)]['Scissors'][_0x50e37b(0x3fe,0x419)],'value':lang[_0x50e37b(0x41b,0x41c)]['Embed'][_0x50e37b(0x3fa,0x410)][_0x50e37b(0x409,0x418)][_0x50e37b(0x3f4,0x3d6)],'inline':!![]})['setFooter']({'text':lang[_0x50e37b(0x41b,0x43c)]['Embed']['Footer'][_0x50e37b(0x407,0x3ea)],'iconURL':_0x12e05f[_0x50e37b(0x414,0x42d)][_0x50e37b(0x42b,0x43f)]()})['setTimestamp'](),_0xc35ccd=new ActionRowBuilder()[_0x50e37b(0x41f,0x41a)](new ButtonBuilder()[_0x50e37b(0x413,0x404)]('rock_'+_0x12e05f['id'])[_0x50e37b(0x40f,0x3ed)](lang[_0x50e37b(0x41b,0x42b)][_0x50e37b(0x3e5,0x3d2)][_0x50e37b(0x3ef,0x3e4)][_0x50e37b(0x408,0x40a)][_0x50e37b(0x407,0x41e)])[_0x50e37b(0x427,0x431)](getButtonStyle(lang[_0x50e37b(0x41b,0x438)][_0x50e37b(0x3e5,0x3f8)]['Buttons']['Rock']['Style'][_0x50e37b(0x42a,0x445)]()))['setEmoji'](lang[_0x50e37b(0x41b,0x40f)]['Embed'][_0x50e37b(0x3ef,0x3f0)][_0x50e37b(0x408,0x3f3)][_0x50e37b(0x3f6,0x3f2)]),new ButtonBuilder()[_0x50e37b(0x413,0x437)](_0x50e37b(0x423,0x42b)+_0x12e05f['id'])[_0x50e37b(0x40f,0x412)](lang['RockPaperScissors'][_0x50e37b(0x3e5,0x3ee)][_0x50e37b(0x3ef,0x3cd)][_0x50e37b(0x3f8,0x410)][_0x50e37b(0x407,0x423)])[_0x50e37b(0x427,0x430)](getButtonStyle(lang[_0x50e37b(0x41b,0x43f)][_0x50e37b(0x3e5,0x3e7)]['Buttons'][_0x50e37b(0x3f8,0x414)][_0x50e37b(0x402,0x400)][_0x50e37b(0x42a,0x414)]()))[_0x50e37b(0x3ed,0x3f1)](lang[_0x50e37b(0x41b,0x41c)][_0x50e37b(0x3e5,0x3c9)][_0x50e37b(0x3ef,0x403)][_0x50e37b(0x3f8,0x408)][_0x50e37b(0x3f6,0x3eb)]),new ButtonBuilder()[_0x50e37b(0x413,0x40f)](_0x50e37b(0x424,0x413)+_0x12e05f['id'])[_0x50e37b(0x40f,0x41e)](lang[_0x50e37b(0x41b,0x437)][_0x50e37b(0x3e5,0x3ca)]['Buttons'][_0x50e37b(0x409,0x3f1)][_0x50e37b(0x407,0x414)])[_0x50e37b(0x427,0x430)](getButtonStyle(lang[_0x50e37b(0x41b,0x3f8)][_0x50e37b(0x3e5,0x3d3)]['Buttons']['Scissors'][_0x50e37b(0x402,0x3f8)]['toUpperCase']()))[_0x50e37b(0x3ed,0x408)](lang['RockPaperScissors']['Embed'][_0x50e37b(0x3ef,0x40c)][_0x50e37b(0x409,0x42d)][_0x50e37b(0x3f6,0x3d2)]));await _0x12e05f['reply']({'embeds':[_0x4e5bbf],'components':[_0xc35ccd],'ephemeral':![]});const _0x5587de=_0x604c97=>_0x604c97[_0x50e37b(0x40d,0x3ec)][_0x50e37b(0x3e3,0x3f0)](_0x12e05f['id']),_0xb339ac=_0x12e05f['channel'][_0x50e37b(0x3fb,0x41e)]({'filter':_0x5587de,'time':0x493e0});_0xb339ac['on'](_0x50e37b(0x3f3,0x3fc),async _0x25196c=>{function _0x2f275e(_0x2ffefe,_0xf026fc){return _0x50e37b(_0x2ffefe- -0x325,_0xf026fc);}if(_0x25196c[_0x2f275e(0xef,0x10e)]['id']!==_0x12e05f['user']['id']){await _0x25196c[_0x2f275e(0xd7,0xda)]({'content':lang[_0x2f275e(0xf6,0xf5)][_0x2f275e(0xf7,0xe6)][_0x2f275e(0xe1,0xe4)],'ephemeral':!![]});return;}const _0x5a72c4=_0x25196c['customId'][_0x2f275e(0x101,0xf4)]('_'),_0x552410=_0x5a72c4[0x0],_0x2b6a88=[_0x2f275e(0xeb,0xe9),_0x2f275e(0xe6,0xcd),_0x2f275e(0xc7,0xcb)],_0x30cee2=_0x2b6a88[Math['floor'](Math['random']()*_0x2b6a88[_0x2f275e(0xde,0xcb)])],_0x2cf2f9=new EmbedBuilder()[_0x2f275e(0xfd,0x120)](lang[_0x2f275e(0xf6,0x10e)][_0x2f275e(0xc0,0xa5)]['Color'])[_0x2f275e(0xfb,0x11c)](lang[_0x2f275e(0xf6,0xf8)][_0x2f275e(0xf7,0xe9)][_0x2f275e(0xc3,0xc4)]['replace']('{userChoice}',_0x552410)['replace']('{botChoice}',_0x30cee2))[_0x2f275e(0xe7,0xe1)]({'text':lang['RockPaperScissors'][_0x2f275e(0xf7,0xfa)]['ThanksForPlaying'],'iconURL':_0x12e05f['user'][_0x2f275e(0xd0,0xef)]()})[_0x2f275e(0xf4,0x105)]();let _0x36a1d;if(_0x552410===_0x30cee2)_0x36a1d=lang['RockPaperScissors'][_0x2f275e(0xf7,0xf6)][_0x2f275e(0xcc,0xcb)][_0x2f275e(0xc9,0xb0)](_0x2f275e(0xdb,0xb7),'<@'+_0x12e05f[_0x2f275e(0xef,0xd6)]['id']+'>');else _0x552410==='scissors'&&_0x30cee2===_0x2f275e(0xe6,0xce)||_0x552410==='rock'&&_0x30cee2==='scissors'||_0x552410===_0x2f275e(0xe6,0xee)&&_0x30cee2==='rock'?_0x36a1d=lang['RockPaperScissors']['Messages'][_0x2f275e(0xd4,0xda)][_0x2f275e(0xc9,0xdf)]('{user}','<@'+_0x12e05f[_0x2f275e(0xef,0xda)]['id']+'>'):_0x36a1d=lang[_0x2f275e(0xf6,0x102)][_0x2f275e(0xf7,0xe5)][_0x2f275e(0xe0,0x103)][_0x2f275e(0xc9,0xce)](_0x2f275e(0xdb,0xff),'<@'+_0x12e05f[_0x2f275e(0xef,0xf4)]['id']+'>');_0x2cf2f9[_0x2f275e(0xf5,0xf0)](lang[_0x2f275e(0xf6,0xd9)][_0x2f275e(0xc0,0xa9)][_0x2f275e(0xf8,0xde)]),_0x2cf2f9[_0x2f275e(0xfb,0xec)](_0x36a1d),await _0x25196c['update']({'embeds':[_0x2cf2f9],'components':[]});});}catch(_0x40519e){console[_0x50e37b(0x41e,0x433)](_0x40519e);}}};function _0x5114(){const _0xead3a6=['setColor','paper_','scissors_','exports','split','setStyle','8320xlNYky','7ZwdNAv','toUpperCase','avatarURL','includes','./lang.yml','Embed','load','Success','YourChoice','5035428srCycy','88FmfRoK','setName','scissors','setEmoji','replace','Buttons','discord.js','Tie','utf8','collect','Value','displayAvatarURL','Emoji','rps','Paper','Win','Fields','createMessageComponentCollector','reply','441920jMWVGL','Name','addFields','{user}','272063OfeuHI','Style','length','42lhEWoH','Lost','CantPlaySomeoneElsesGame','Text','Rock','Scissors','js-yaml','paper','setFooter','customId','1799058LStagS','setLabel','rock','Secondary','68wzMlAU','setCustomId','user','readFileSync','604576MnFmMh','Danger','143045sAnRYS','setTimestamp','setTitle','RockPaperScissors','Messages','Title','error','addComponents','setDescription','Description'];_0x5114=function(){return _0xead3a6;};return _0x5114();}function getButtonStyle(_0x234b23){function _0x458cbc(_0x5ca9d4,_0x48009d){return _0x22432b(_0x5ca9d4,_0x48009d- -0x41e);}const _0x36ff4e={'PRIMARY':ButtonStyle['Primary'],'SECONDARY':ButtonStyle[_0x458cbc(0x45,0x3c)],'SUCCESS':ButtonStyle[_0x458cbc(-0x4,0x12)],'DANGER':ButtonStyle[_0x458cbc(0x62,0x42)]};return _0x36ff4e[_0x234b23]||ButtonStyle[_0x458cbc(-0x9,0x12)];}