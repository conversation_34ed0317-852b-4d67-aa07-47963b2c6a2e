# ________________________________________________
#
#         DEFAULT SERVER TEMPLATE FILE
# ________________________________________________

# List of Channels to Create, Channels will be created in same order as they're listed here
channels:
  # Format:
  # PARENT CATEGORY | CHANNEL NAME | CHANNEL TYPE | PERMISSION GROUP | TOPIC
  # If you're creating Category Channel then set PARENT CATEGORY to null
  # If you don't want TOPIC set it to null
  # Channel Types: TEXT, VOICE, CATEGORY
  - 'null | informations | CATEGORY | firstGroup | null' # Category with name 'informations' & with permissions from group 'firstGroup'
  - 'informations | rules | TEXT | null | null' # Text Channel with name 'rules', parent category 'informations' & with permissions from group 'firstGroup'
  - 'null | Lounge | VOICE | null | null' # Voice Channel with name 'Lounge', without parent, permissions & topic (Voice Channels Cannot have Topic)

# Channels Permission Groups
channelPermissions:
  # Name For Channel Permission, must be unique
  # Placed to PERMISSION GROUP field in channel section
  firstGroup:
    # Name of Role for which to create Permissions
    - role: "Test"
      # Allowed Permissions
      allow:
        - "ManageMessages"
      # Denied Permissions
      deny: 
        - "ManageMessages"

# List of Roles to Create
roles:
    # Name of Role
  - name: "Test"
    # Color of Role in #RRGGBB Format
    color: "#4CAAFF"
    # Whether Role is Hoisted
    hoisted: true
    # Should bot automatically add Default Permissions onto below ones
    default: true
    # List of Permissions to give, if 'default: true' bot will also add default permissions
    permissions: ["ManageMessages"]

# List of Emojis to Create
emojis:
    # Name of Emoji
  - name: "wumpus"
    # URL of Emoji
    url: "https://emoji.gg/assets/emoji/4625-wumpus.png"

# Some Extra Stuff
extra:
  # Verification Level for Guild, can be NONE, LOW, MEDIUM, HIGH, VERY_HIGH.
  verificationLevel: "NONE"
  # Banner URL / null
  banner: null
  # Icon URL / null
  icon: null