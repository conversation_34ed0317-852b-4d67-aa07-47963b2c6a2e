const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.WebhookCreate,
    async execute(webhook, client) {
        try {
            console.log(`🔗 Webhook created in channel: ${webhook.channel?.name} in ${webhook.guild?.name}`);
            
            // Skip if no guild
            if (!webhook.guild) return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(webhook.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_WEBHOOK_CREATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_WEBHOOK_CREATE',
                user: 'System',
                userId: null,
                action: 'Webhook được tạo trong kênh',
                details: `Webhook **${webhook.name || 'Unnamed'}** đã được tạo trong kênh **${webhook.channel?.name || 'Unknown'}**`,
                target: webhook.name || 'Unnamed',
                channel: webhook.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who created the webhook from audit logs
            try {
                const auditLogs = await webhook.guild.fetchAuditLogs({
                    type: 50, // WEBHOOK_CREATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === webhook.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Webhook được tạo bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for webhook create');
            }
            
            // Add to database
            await client.db.addAuditLog(webhook.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🔗 Webhook được tạo trong kênh',
                `Vừa có webhook mới được tạo trong kênh`
            );
            
            embed.setColor(0x2ecc71); // Green for create
            
            // Get webhook type names
            const webhookTypes = {
                1: 'Incoming Webhook',
                2: 'Channel Follower Webhook',
                3: 'Application Webhook'
            };
            
            embed.addFields([
                {
                    name: '> Tên webhook',
                    value: `- ${webhook.name || 'Unnamed Webhook'}`,
                    inline: true
                },
                {
                    name: '> ID webhook',
                    value: `- ${webhook.id}`,
                    inline: true
                },
                {
                    name: '> Loại webhook',
                    value: `- ${webhookTypes[webhook.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${webhook.channel || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${webhook.channel?.id || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người tạo',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian tạo',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add webhook owner info if available
            if (webhook.owner) {
                embed.addFields({
                    name: '> Chủ sở hữu',
                    value: `- ${webhook.owner.tag || webhook.owner.name}`,
                    inline: true
                });
            }
            
            // Add application info if it's an application webhook
            if (webhook.application) {
                embed.addFields({
                    name: '> Ứng dụng',
                    value: `- ${webhook.application.name}`,
                    inline: true
                });
            }
            
            // Add webhook avatar info
            if (webhook.avatarURL()) {
                embed.addFields({
                    name: '> Avatar',
                    value: `- [Xem avatar](${webhook.avatarURL({ dynamic: true, size: 256 })})`,
                    inline: true
                });
                embed.setThumbnail(webhook.avatarURL({ dynamic: true, size: 256 }));
            } else {
                embed.addFields({
                    name: '> Avatar',
                    value: `- Không có avatar`,
                    inline: true
                });
            }
            
            // Add webhook URL info (partially hidden for security)
            if (webhook.url) {
                const hiddenUrl = `https://discord.com/api/webhooks/${webhook.id}/***`;
                embed.addFields({
                    name: '> URL webhook',
                    value: `- ${hiddenUrl}`,
                    inline: false
                });
            }
            
            // Add usage information
            embed.addFields({
                name: '> 💡 Cách sử dụng webhook',
                value: [
                    '• Sử dụng URL để gửi tin nhắn từ ứng dụng bên ngoài',
                    '• Có thể tùy chỉnh tên và avatar cho mỗi tin nhắn',
                    '• Hỗ trợ embeds và file attachments',
                    '• Không cần bot token để sử dụng'
                ].join('\n'),
                inline: false
            });
            
            // Add security warning
            embed.addFields({
                name: '> 🔒 Bảo mật',
                value: [
                    '• Giữ URL webhook bí mật',
                    '• Không chia sẻ URL trong tin nhắn công khai',
                    '• Có thể xóa webhook nếu bị lộ',
                    '• Kiểm tra logs thường xuyên'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do tạo',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${webhook.guild.name} • Channel Webhook Create`,
                iconURL: webhook.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel webhook create logged: ${webhook.name}`);
            
        } catch (error) {
            console.error('Error in channelWebhookCreate audit log:', error);
        }
    }
};
