const { REST, Routes } = require("discord.js");
const fs = require("fs");
const path = require("path");
const config = require("../config/config.js");

async function deployCommands() {
  const commands = [];

  console.log("🔄 Đang thu thập lệnh slash...");

  // Đ<PERSON><PERSON> tấ<PERSON> commands từ thư mục commands
  const commandsPath = path.join(__dirname, "..", "commands");

  if (!fs.existsSync(commandsPath)) {
    console.log("❌ <PERSON>h<PERSON> mục commands không tồn tại!");
    return;
  }

  const commandFolders = fs.readdirSync(commandsPath);

  for (const folder of commandFolders) {
    const folderPath = path.join(commandsPath, folder);

    if (fs.statSync(folderPath).isDirectory()) {
      const commandFiles = fs
        .readdirSync(folderPath)
        .filter((file) => file.endsWith(".js"));

      for (const file of commandFiles) {
        const filePath = path.join(folderPath, file);
        try {
          const command = require(filePath);

          if ("data" in command && "execute" in command) {
            commands.push(command.data.toJSON());
            console.log(`✅ Đã thu thập lệnh: ${command.data.name}`);
          } else {
            console.log(
              `⚠️ Lệnh tại ${filePath} thiếu thuộc tính "data" hoặc "execute".`
            );
          }
        } catch (error) {
          console.error(`❌ Lỗi khi thu thập lệnh ${filePath}:`, error);
        }
      }
    }
  }

  if (commands.length === 0) {
    console.log("⚠️ Không tìm thấy lệnh nào để deploy!");
    return;
  }

  // Tạo REST instance
  const rest = new REST({ version: "10" }).setToken(config.token);

  try {
    console.log(`🚀 Bắt đầu deploy ${commands.length} lệnh slash...`);

    // Xóa tất cả lệnh cũ trước (theo yêu cầu)
    console.log("🗑️ Đang xóa tất cả lệnh cũ...");

    if (config.guildId) {
      // Development: Xóa guild commands
      await rest.put(
        Routes.applicationGuildCommands(config.clientId, config.guildId),
        { body: [] }
      );
      console.log("✅ Đã xóa tất cả guild commands cũ");

      // Deploy guild commands mới
      const data = await rest.put(
        Routes.applicationGuildCommands(config.clientId, config.guildId),
        { body: commands }
      );

      console.log(`✅ Đã deploy thành công ${data.length} guild commands!`);
      console.log(`📍 Guild ID: ${config.guildId}`);
    } else {
      // Production: Xóa global commands
      await rest.put(Routes.applicationCommands(config.clientId), { body: [] });
      console.log("✅ Đã xóa tất cả global commands cũ");

      // Deploy global commands mới
      const data = await rest.put(Routes.applicationCommands(config.clientId), {
        body: commands,
      });

      console.log(`✅ Đã deploy thành công ${data.length} global commands!`);
      console.log("⏰ Lưu ý: Global commands có thể mất tới 1 giờ để cập nhật");
    }

    // Hiển thị danh sách commands đã deploy
    console.log("\n📋 Danh sách lệnh đã deploy:");
    commands.forEach((cmd, index) => {
      console.log(`${index + 1}. /${cmd.name} - ${cmd.description}`);
    });

    console.log("\n🎉 Deploy commands hoàn tất!");
  } catch (error) {
    console.error("❌ Lỗi khi deploy commands:", error);

    if (error.code === 20012) {
      console.error("💡 Lỗi: CLIENT_ID không khớp với bot token!");
      console.error("🔧 Cách sửa:");
      console.error(
        "   1. Vào Discord Developer Portal: https://discord.com/developers/applications"
      );
      console.error("   2. Chọn bot application của bạn");
      console.error('   3. Copy "Application ID" từ tab General Information');
      console.error("   4. Cập nhật CLIENT_ID trong file .env");
      console.error("   5. Đảm bảo DISCORD_TOKEN cũng từ cùng bot application");
    } else if (error.code === 50001) {
      console.error("💡 Lỗi: Bot thiếu quyền applications.commands");
      console.error(
        '🔧 Cách sửa: Mời lại bot với scope "applications.commands"'
      );
    } else if (error.code === 50035) {
      console.error("💡 Lỗi: Dữ liệu command không hợp lệ");
      console.error("Chi tiết:", error.rawError?.errors);
    } else if (error.status === 401) {
      console.error("💡 Lỗi: Token bot không hợp lệ");
      console.error("🔧 Cách sửa: Kiểm tra DISCORD_TOKEN trong file .env");
    } else if (error.status === 403) {
      console.error("💡 Lỗi: Bot không có quyền truy cập");
      console.error(
        "🔧 Cách sửa: Kiểm tra bot có trong server và có quyền cần thiết"
      );
    }

    console.error("\n📋 Checklist debug:");
    console.error("   ✓ DISCORD_TOKEN có đúng không?");
    console.error("   ✓ CLIENT_ID có khớp với bot không?");
    console.error("   ✓ Bot có trong server không?");
    console.error("   ✓ Bot có quyền applications.commands không?");
  }
}

// Chạy script nếu được gọi trực tiếp
if (require.main === module) {
  // Kiểm tra config trước khi deploy
  if (!config.token) {
    console.error("❌ Lỗi: DISCORD_TOKEN không được thiết lập trong file .env");
    process.exit(1);
  }

  if (!config.clientId) {
    console.error("❌ Lỗi: CLIENT_ID không được thiết lập trong file .env");
    process.exit(1);
  }

  console.log("🤖 Server Setup Bot - Deploy Commands Script");
  console.log("==========================================");
  console.log(`📱 Client ID: ${config.clientId}`);
  console.log(
    `🌍 Mode: ${config.guildId ? "Development (Guild)" : "Production (Global)"}`
  );
  if (config.guildId) {
    console.log(`🏠 Guild ID: ${config.guildId}`);
  }
  console.log("==========================================\n");

  deployCommands()
    .then(() => {
      console.log("\n✨ Script hoàn tất!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💥 Script thất bại:", error);
      process.exit(1);
    });
}

module.exports = { deployCommands };
