const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createWarningEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('kick')
        .setDescription('Kick thành viên khỏi server')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Thành viên cần kick')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Lý do kick')
                .setRequired(false)
        )
        .addBooleanOption(option =>
            option.setName('silent')
                .setDescription('Không gửi DM cho người bị kick')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.KickMembers)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                'Bạn cần quyền `Kick Members` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.KickMembers)) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                'Bot cần quyền `Kick Members` để thực hiện lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const user = interaction.options.getUser('user');
        const reason = interaction.options.getString('reason') || 'Không có lý do';
        const silent = interaction.options.getBoolean('silent') || false;
        const guildId = interaction.guild.id;
        
        try {
            const member = await interaction.guild.members.fetch(user.id);
            
            // Kiểm tra hierarchy
            if (member.roles.highest.position >= interaction.member.roles.highest.position) {
                const errorEmbed = createErrorEmbed(
                    'Không thể kick!',
                    'Bạn không thể kick thành viên có role cao hơn hoặc bằng bạn.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            if (member.roles.highest.position >= interaction.guild.members.me.roles.highest.position) {
                const errorEmbed = createErrorEmbed(
                    'Không thể kick!',
                    'Bot không thể kick thành viên có role cao hơn hoặc bằng bot.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            if (!member.kickable) {
                const errorEmbed = createErrorEmbed(
                    'Không thể kick!',
                    'Thành viên này không thể bị kick.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
            
            // Gửi DM trước khi kick
            if (!silent) {
                const dmEmbed = createWarningEmbed(
                    `Bạn đã bị kick khỏi ${interaction.guild.name}`,
                    `**Lý do:** ${reason}\n**Bởi:** ${interaction.user.tag}\n**Thời gian:** <t:${Math.floor(Date.now() / 1000)}:F>`
                );
                
                try {
                    await user.send({ embeds: [dmEmbed] });
                } catch (error) {
                    console.log('Không thể gửi DM cho user bị kick');
                }
            }
            
            // Thực hiện kick
            await member.kick(`${reason} | Bởi: ${interaction.user.tag}`);
            
            // Log vào database
            await client.db.addModerationLog(guildId, {
                type: 'kick',
                userId: user.id,
                moderatorId: interaction.user.id,
                reason: reason,
                duration: null
            });
            
            const successEmbed = createSuccessEmbed(
                '👢 Thành viên đã bị kick!',
                `**Thành viên:** ${user.tag} (${user.id})\n` +
                `**Lý do:** ${reason}\n` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            await interaction.reply({ embeds: [successEmbed] });
            
            // Gửi log vào channel
            await sendModerationLog(client, guildId, {
                type: 'kick',
                user: user,
                moderator: interaction.user,
                reason: reason
            });
            
        } catch (error) {
            console.error('Lỗi khi kick user:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi kick thành viên!',
                'Đã xảy ra lỗi khi kick thành viên. Vui lòng thử lại sau!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};
