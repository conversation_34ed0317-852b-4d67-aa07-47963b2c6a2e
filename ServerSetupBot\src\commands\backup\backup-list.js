const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { createInfoEmbed, createErrorEmbed } = require('../../utils/embedBuilder.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('backup-list')
        .setDescription('Xem danh sách backup của server'),
    category: 'backup',
    adminOnly: true,
    manageServer: false,
    
    async execute(interaction, client) {
        const guildId = interaction.guild.id;
        
        try {
            const backups = await client.db.getServerBackups(guildId);
            
            if (backups.length === 0) {
                const infoEmbed = createInfoEmbed(
                    'Chưa có backup nào',
                    'Server chưa có backup nào.\n\nSử dụng `/backup-create` để tạo backup đầu tiên!'
                );
                return await interaction.reply({ embeds: [infoEmbed] });
            }
            
            const embed = createInfoEmbed(
                `📦 Danh sách Backup (${backups.length})`,
                `Server **${interaction.guild.name}** có ${backups.length} backup:`
            );
            
            // Hiển thị tối đa 10 backup gần nhất
            const displayBackups = backups.slice(0, 10);
            
            for (const backup of displayBackups) {
                const backupData = JSON.parse(backup.backup_data);
                const createdDate = new Date(backup.created_at).toLocaleString('vi-VN');
                const fileSize = formatFileSize(backup.file_size || 0);
                
                let description = `**Loại:** ${getTypeDescription(backup.backup_type)}\n`;
                description += `**Kích thước:** ${fileSize}\n`;
                description += `**Tạo lúc:** ${createdDate}\n`;
                
                if (backupData.description) {
                    description += `**Mô tả:** ${backupData.description}\n`;
                }
                
                // Thống kê nhanh
                const stats = getQuickStats(backupData, backup.backup_type);
                if (stats) {
                    description += `**Nội dung:** ${stats}`;
                }
                
                embed.addFields({
                    name: `${getTypeEmoji(backup.backup_type)} ${backup.backup_name}`,
                    value: description,
                    inline: true
                });
            }
            
            if (backups.length > 10) {
                embed.addFields({
                    name: '📝 Lưu ý',
                    value: `Chỉ hiển thị 10 backup gần nhất. Tổng cộng có ${backups.length} backup.`,
                    inline: false
                });
            }
            
            embed.addFields({
                name: '🔧 Quản lý Backup',
                value: '• `/backup-create` - Tạo backup mới\n' +
                       '• `/backup-restore` - Khôi phục backup\n' +
                       '• `/backup-delete` - Xóa backup\n' +
                       '• `/backup-info` - Xem chi tiết backup',
                inline: false
            });
            
            // Tính tổng dung lượng
            const totalSize = backups.reduce((sum, backup) => sum + (backup.file_size || 0), 0);
            embed.setFooter({
                text: `Tổng dung lượng: ${formatFileSize(totalSize)} • Server Setup Bot`,
                iconURL: client.user.displayAvatarURL()
            });
            
            await interaction.reply({ embeds: [embed] });
            
        } catch (error) {
            console.error('Lỗi khi lấy danh sách backup:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi hệ thống!',
                'Không thể lấy danh sách backup. Vui lòng thử lại sau!'
            );
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};

function getTypeDescription(type) {
    const descriptions = {
        'full': '🏗️ Toàn bộ',
        'channels': '📝 Channels',
        'roles': '🎭 Roles',
        'settings': '⚙️ Settings'
    };
    return descriptions[type] || type;
}

function getTypeEmoji(type) {
    const emojis = {
        'full': '🏗️',
        'channels': '📝',
        'roles': '🎭',
        'settings': '⚙️'
    };
    return emojis[type] || '📦';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function getQuickStats(data, type) {
    const stats = [];
    
    if (data.channels) {
        const totalChannels = (data.channels.textChannels?.length || 0) + 
                             (data.channels.voiceChannels?.length || 0) + 
                             (data.channels.categories?.length || 0);
        if (totalChannels > 0) {
            stats.push(`${totalChannels} kênh`);
        }
    }
    
    if (data.roles && data.roles.length > 0) {
        stats.push(`${data.roles.length} role`);
    }
    
    if (data.settings) {
        const settingsCount = Object.keys(data.settings).length;
        if (settingsCount > 0) {
            stats.push(`${settingsCount} cấu hình`);
        }
    }
    
    return stats.join(', ') || null;
}
