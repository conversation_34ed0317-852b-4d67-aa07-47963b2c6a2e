const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.AutoModerationRuleUpdate,
    async execute(oldRule, newRule, client) {
        try {
            // Only handle actions changes
            const oldActionsString = JSON.stringify(oldRule.actions?.map(a => ({ type: a.type, metadata: a.metadata })) || []);
            const newActionsString = JSON.stringify(newRule.actions?.map(a => ({ type: a.type, metadata: a.metadata })) || []);
            
            if (oldActionsString === newActionsString) return;
            
            console.log(`⚡ AutoMod rule actions updated: ${newRule.name} in ${newRule.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newRule.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'AUTOMOD_RULE_ACTIONS_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'AUTOMOD_RULE_ACTIONS_UPDATE',
                user: 'System',
                userId: null,
                action: 'Hành động quy tắc AutoMod được cập nhật',
                details: `Hành động của quy tắc AutoMod **${newRule.name}** đã được cập nhật`,
                target: newRule.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the rule actions from audit logs
            try {
                const auditLogs = await newRule.guild.fetchAuditLogs({
                    type: 141, // AUTO_MODERATION_RULE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newRule.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Hành động quy tắc AutoMod được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for automod rule actions update');
            }
            
            // Add to database
            await client.db.addAuditLog(newRule.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '⚡ Hành động quy tắc AutoMod được cập nhật',
                `Vừa có hành động của quy tắc AutoMod được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get trigger type names
            const triggerTypes = {
                1: 'Keyword',
                2: 'Harmful Link',
                3: 'Spam',
                4: 'Keyword Preset',
                5: 'Mention Spam'
            };
            
            embed.addFields([
                {
                    name: '> Tên quy tắc',
                    value: `- ${newRule.name}`,
                    inline: true
                },
                {
                    name: '> ID quy tắc',
                    value: `- ${newRule.id}`,
                    inline: true
                },
                {
                    name: '> Loại trigger',
                    value: `- ${triggerTypes[newRule.triggerType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${newRule.enabled ? '✅ Đang hoạt động' : '❌ Đã tắt'}`,
                    inline: true
                }
            ]);
            
            // Format actions for display
            const formatActions = (actions) => {
                if (!actions || actions.length === 0) return 'Không có hành động';
                
                const actionTypes = {
                    1: 'Chặn tin nhắn',
                    2: 'Gửi cảnh báo',
                    3: 'Timeout người dùng',
                    4: 'Gửi tin nhắn đến kênh'
                };
                
                return actions.map(action => {
                    let actionText = actionTypes[action.type] || 'Unknown Action';
                    
                    // Add timeout duration if applicable
                    if (action.type === 3 && action.metadata?.durationSeconds) {
                        const duration = action.metadata.durationSeconds;
                        const minutes = Math.floor(duration / 60);
                        actionText += ` (${minutes} phút)`;
                    }
                    
                    // Add channel info if applicable
                    if (action.metadata?.channelId) {
                        const channel = newRule.guild.channels.cache.get(action.metadata.channelId);
                        actionText += ` → ${channel?.name || action.metadata.channelId}`;
                    }
                    
                    return actionText;
                }).join('\n• ');
            };
            
            // Compare old and new actions
            const oldActionsFormatted = formatActions(oldRule.actions);
            const newActionsFormatted = formatActions(newRule.actions);
            
            embed.addFields([
                {
                    name: '> Hành động cũ',
                    value: `• ${oldActionsFormatted}`,
                    inline: false
                },
                {
                    name: '> Hành động mới',
                    value: `• ${newActionsFormatted}`,
                    inline: false
                }
            ]);
            
            // Analyze changes
            const oldActions = oldRule.actions || [];
            const newActions = newRule.actions || [];
            
            const addedActions = newActions.filter(newAction => 
                !oldActions.find(oldAction => oldAction.type === newAction.type)
            );
            
            const removedActions = oldActions.filter(oldAction => 
                !newActions.find(newAction => newAction.type === oldAction.type)
            );
            
            if (addedActions.length > 0 || removedActions.length > 0) {
                let changesText = [];
                
                if (addedActions.length > 0) {
                    changesText.push(`**Thêm:** ${formatActions(addedActions)}`);
                }
                
                if (removedActions.length > 0) {
                    changesText.push(`**Xóa:** ${formatActions(removedActions)}`);
                }
                
                embed.addFields({
                    name: '> 🔄 Chi tiết thay đổi',
                    value: changesText.join('\n\n'),
                    inline: false
                });
            }
            
            // Add impact explanation
            embed.addFields({
                name: '> 📝 Tác động',
                value: [
                    '• Hành động mới sẽ được thực thi khi vi phạm',
                    '• Hành động cũ sẽ không còn được sử dụng',
                    '• Thay đổi ngay lập tức nếu quy tắc đang bật',
                    '• Có thể ảnh hưởng đến trải nghiệm người dùng'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newRule.guild.name} • AutoMod Rule Actions Update`,
                iconURL: newRule.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ AutoMod rule actions update logged: ${newRule.name}`);
            
        } catch (error) {
            console.error('Error in automodRuleActionsUpdate audit log:', error);
        }
    }
};
