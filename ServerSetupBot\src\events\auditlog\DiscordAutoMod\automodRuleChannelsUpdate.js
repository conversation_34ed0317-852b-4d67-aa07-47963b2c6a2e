const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.AutoModerationRuleUpdate,
    async execute(oldRule, newRule, client) {
        try {
            // Only handle exempt channels changes
            const oldChannelIds = Array.from(oldRule.exemptChannels?.keys() || []).sort();
            const newChannelIds = Array.from(newRule.exemptChannels?.keys() || []).sort();
            
            if (JSON.stringify(oldChannelIds) === JSON.stringify(newChannelIds)) return;
            
            console.log(`📺 AutoMod rule channels updated: ${newRule.name} in ${newRule.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newRule.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'AUTOMOD_RULE_CHANNELS_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'AUTOMOD_RULE_CHANNELS_UPDATE',
                user: 'System',
                userId: null,
                action: 'Kênh miễn trừ quy tắc AutoMod được cập nhật',
                details: `Kênh miễn trừ của quy tắc AutoMod **${newRule.name}** đã được cập nhật`,
                target: newRule.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the rule channels from audit logs
            try {
                const auditLogs = await newRule.guild.fetchAuditLogs({
                    type: 141, // AUTO_MODERATION_RULE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newRule.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Kênh miễn trừ quy tắc AutoMod được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for automod rule channels update');
            }
            
            // Add to database
            await client.db.addAuditLog(newRule.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📺 Kênh miễn trừ quy tắc AutoMod được cập nhật',
                `Vừa có kênh miễn trừ của quy tắc AutoMod được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get trigger type names
            const triggerTypes = {
                1: 'Keyword',
                2: 'Harmful Link',
                3: 'Spam',
                4: 'Keyword Preset',
                5: 'Mention Spam'
            };
            
            embed.addFields([
                {
                    name: '> Tên quy tắc',
                    value: `- ${newRule.name}`,
                    inline: true
                },
                {
                    name: '> ID quy tắc',
                    value: `- ${newRule.id}`,
                    inline: true
                },
                {
                    name: '> Loại trigger',
                    value: `- ${triggerTypes[newRule.triggerType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${newRule.enabled ? '✅ Đang hoạt động' : '❌ Đã tắt'}`,
                    inline: true
                }
            ]);
            
            // Analyze channel changes
            const oldChannels = Array.from(oldRule.exemptChannels?.values() || []);
            const newChannels = Array.from(newRule.exemptChannels?.values() || []);
            
            const addedChannels = newChannels.filter(newChannel => !oldChannels.find(oldChannel => oldChannel.id === newChannel.id));
            const removedChannels = oldChannels.filter(oldChannel => !newChannels.find(newChannel => newChannel.id === oldChannel.id));
            
            // Add channel counts
            embed.addFields([
                {
                    name: '> Số kênh cũ',
                    value: `- ${oldChannels.length} kênh`,
                    inline: true
                },
                {
                    name: '> Số kênh mới',
                    value: `- ${newChannels.length} kênh`,
                    inline: true
                },
                {
                    name: '> Thay đổi',
                    value: `- ${newChannels.length - oldChannels.length >= 0 ? '+' : ''}${newChannels.length - oldChannels.length} kênh`,
                    inline: true
                }
            ]);
            
            // Show added channels
            if (addedChannels.length > 0) {
                const addedChannelsList = addedChannels.map(channel => `• ${channel.name}`).join('\n');
                embed.addFields({
                    name: '> ✅ Kênh được thêm vào miễn trừ',
                    value: addedChannelsList.length > 1000 ? addedChannelsList.substring(0, 1000) + '...' : addedChannelsList,
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for additions
            }
            
            // Show removed channels
            if (removedChannels.length > 0) {
                const removedChannelsList = removedChannels.map(channel => `• ${channel.name}`).join('\n');
                embed.addFields({
                    name: '> ❌ Kênh được xóa khỏi miễn trừ',
                    value: removedChannelsList.length > 1000 ? removedChannelsList.substring(0, 1000) + '...' : removedChannelsList,
                    inline: false
                });
                if (addedChannels.length === 0) {
                    embed.setColor(0xe74c3c); // Red for removals only
                }
            }
            
            // Show current exempt channels (if not too many)
            if (newChannels.length <= 10 && newChannels.length > 0) {
                const currentChannelsList = newChannels.map(channel => `• ${channel.name}`).join('\n');
                embed.addFields({
                    name: '> 📋 Kênh miễn trừ hiện tại',
                    value: currentChannelsList,
                    inline: false
                });
            } else if (newChannels.length > 10) {
                embed.addFields({
                    name: '> 📋 Kênh miễn trừ hiện tại',
                    value: `- Tổng cộng ${newChannels.length} kênh (quá nhiều để hiển thị)`,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: '> 📋 Kênh miễn trừ hiện tại',
                    value: `- Không có kênh nào được miễn trừ`,
                    inline: false
                });
            }
            
            // Add channel type breakdown if there are exempt channels
            if (newChannels.length > 0) {
                const channelTypes = {
                    0: 'Text',
                    2: 'Voice',
                    4: 'Category',
                    5: 'News',
                    10: 'News Thread',
                    11: 'Public Thread',
                    12: 'Private Thread',
                    13: 'Stage',
                    15: 'Forum',
                    16: 'Media'
                };
                
                const typeCount = {};
                newChannels.forEach(channel => {
                    const typeName = channelTypes[channel.type] || 'Unknown';
                    typeCount[typeName] = (typeCount[typeName] || 0) + 1;
                });
                
                const typeBreakdown = Object.entries(typeCount)
                    .map(([type, count]) => `• ${type}: ${count}`)
                    .join('\n');
                
                embed.addFields({
                    name: '> 📊 Phân loại kênh miễn trừ',
                    value: typeBreakdown,
                    inline: false
                });
            }
            
            // Add impact explanation
            if (addedChannels.length > 0) {
                embed.addFields({
                    name: '> 📈 Tác động khi thêm kênh',
                    value: [
                        '• Tin nhắn trong kênh này sẽ không bị kiểm soát',
                        '• Có thể gửi nội dung vi phạm mà không bị xử lý',
                        '• Phù hợp cho kênh staff hoặc testing',
                        '• Cần cân nhắc kỹ trước khi thêm'
                    ].join('\n'),
                    inline: false
                });
            }
            
            if (removedChannels.length > 0) {
                embed.addFields({
                    name: '> 📉 Tác động khi xóa kênh',
                    value: [
                        '• Tin nhắn trong kênh này sẽ bị kiểm soát lại',
                        '• Nội dung vi phạm sẽ được xử lý',
                        '• Tăng cường bảo mật cho kênh',
                        '• Có thể ảnh hưởng đến hoạt động bình thường'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add best practices
            embed.addFields({
                name: '> 💡 Khuyến nghị',
                value: [
                    '• Chỉ miễn trừ kênh staff và testing',
                    '• Tránh miễn trừ kênh public quan trọng',
                    '• Kiểm tra định kỳ danh sách miễn trừ',
                    '• Cân nhắc tạo category riêng cho exempt channels'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newRule.guild.name} • AutoMod Rule Channels Update`,
                iconURL: newRule.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ AutoMod rule channels update logged: ${newRule.name}`);
            
        } catch (error) {
            console.error('Error in automodRuleChannelsUpdate audit log:', error);
        }
    }
};
