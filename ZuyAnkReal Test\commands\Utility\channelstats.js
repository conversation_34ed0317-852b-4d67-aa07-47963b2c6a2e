(function(_0x3f5f29,_0x488bd1){const _0x3d688b=_0x3f5f29();function _0x4d8c78(_0x1d03d3,_0x33480c){return _0x3286(_0x1d03d3- -0x1a6,_0x33480c);}while(!![]){try{const _0x1025d7=-parseInt(_0x4d8c78(-0x96,-0xb4))/0x1*(-parseInt(_0x4d8c78(-0xb8,-0xa3))/0x2)+parseInt(_0x4d8c78(-0xb7,-0xcc))/0x3*(parseInt(_0x4d8c78(-0xa3,-0xc3))/0x4)+-parseInt(_0x4d8c78(-0xbf,-0xa0))/0x5+parseInt(_0x4d8c78(-0xb4,-0xaf))/0x6+-parseInt(_0x4d8c78(-0xb0,-0xac))/0x7*(-parseInt(_0x4d8c78(-0x91,-0x95))/0x8)+parseInt(_0x4d8c78(-0x9e,-0xb0))/0x9+-parseInt(_0x4d8c78(-0xbe,-0xc2))/0xa;if(_0x1025d7===_0x488bd1)break;else _0x3d688b['push'](_0x3d688b['shift']());}catch(_0x5766e5){_0x3d688b['push'](_0x3d688b['shift']());}}}(_0x2b1c,0x53987));const {SlashCommandBuilder,ChannelType,PermissionsBitField}=require(_0x54265b(0x35f,0x37e)),ChannelStat=require(_0x54265b(0x34d,0x366));function _0x2b1c(){const _0x6de6a2=['**\x20with\x20type\x20**','setName','save','setRequired','36ZIErov','GuildVoice','NitroBoosterCount','member','OnlineMembersCount','4976892kkArAu','exports','add','addChannelOption','guild','getSubcommand','Remove\x20a\x20channel\x20stat','discord.js','3943MmEujo','getRole','setDescription','TotalBannedMembers','TotalRolesCount','675288MxxJpI','ServerCreationDate','{stats}','Removed\x20stat\x20for\x20channel\x20**','addSubcommand','The\x20name\x20of\x20the\x20channel\x20with\x20{stats}\x20placeholder','TotalMembersWithRole','getString','findOneAndDelete','remove','addStringOption','TotalChannelsCount','Add\x20a\x20channel\x20stat','role','**.','error','Manage\x20channel\x20statistics','Utility','Failed\x20to\x20add\x20the\x20channel\x20stat.\x20Please\x20try\x20again\x20later.','You\x20do\x20not\x20have\x20permission\x20to\x20use\x20this\x20command.','This\x20channel\x20already\x20has\x20a\x20stat\x20set.','1479020iAxPyX','10779030SpriAt','type','permissions','Failed\x20to\x20remove\x20the\x20channel\x20stat.\x20Please\x20try\x20again\x20later.','name','Administrator','228VbVeFa','116127DVykgQ','OnlineMembersWithRole','ServerRegion','1684992gCucPh','The\x20voice\x20channel\x20to\x20update','addChoices','MemberCount','7AGmMlL','../../models/channelStatSchema','addRoleOption','Flags','options','reply','Stat\x20for\x20channel\x20**','Type\x20of\x20the\x20stat','channel'];_0x2b1c=function(){return _0x6de6a2;};return _0x2b1c();}function _0x3286(_0x4d343f,_0x69a570){const _0x2b1ca3=_0x2b1c();return _0x3286=function(_0x328605,_0x261e74){_0x328605=_0x328605-0xe1;let _0xa62ef7=_0x2b1ca3[_0x328605];return _0xa62ef7;},_0x3286(_0x4d343f,_0x69a570);}function _0x54265b(_0x398375,_0x4ab6f9){return _0x3286(_0x4ab6f9-0x26f,_0x398375);}module[_0x54265b(0x376,0x378)]={'data':new SlashCommandBuilder()['setName']('channelstats')['setDescription'](_0x54265b(0x357,0x351))[_0x54265b(0x3aa,0x388)](_0x3327e4=>_0x3327e4[_0x54265b(0x34e,0x36f)](_0x54265b(0x392,0x379))[_0x54265b(0x36b,0x381)](_0x54265b(0x381,0x390))['addStringOption'](_0x4bafd3=>_0x4bafd3[_0x54265b(0x366,0x36f)]('channelname')[_0x54265b(0x363,0x381)](_0x54265b(0x3a9,0x389))['setRequired'](!![]))[_0x54265b(0x384,0x38e)](_0x3cfc07=>_0x3cfc07[_0x54265b(0x38c,0x36f)](_0x54265b(0x351,0x358))['setDescription'](_0x54265b(0x364,0x36c))[_0x54265b(0x38b,0x371)](!![])[_0x54265b(0x36d,0x363)]({'name':'MemberCount','value':_0x54265b(0x34d,0x364)},{'name':_0x54265b(0x37f,0x374),'value':_0x54265b(0x368,0x374)},{'name':_0x54265b(0x377,0x385),'value':_0x54265b(0x382,0x385)},{'name':_0x54265b(0x39b,0x383),'value':_0x54265b(0x368,0x383)},{'name':'TotalEmojisCount','value':'TotalEmojisCount'},{'name':_0x54265b(0x3ae,0x38f),'value':_0x54265b(0x387,0x38f)},{'name':_0x54265b(0x375,0x376),'value':'OnlineMembersCount'},{'name':_0x54265b(0x372,0x360),'value':_0x54265b(0x36c,0x360)},{'name':'TotalBannedMembers','value':_0x54265b(0x37c,0x382)},{'name':_0x54265b(0x392,0x38a),'value':_0x54265b(0x39c,0x38a)},{'name':_0x54265b(0x371,0x35f),'value':_0x54265b(0x375,0x35f)}))[_0x54265b(0x369,0x37a)](_0x150df3=>_0x150df3['setName'](_0x54265b(0x364,0x36d))['setDescription'](_0x54265b(0x36e,0x362))[_0x54265b(0x363,0x371)](!![])['addChannelTypes'](ChannelType[_0x54265b(0x36d,0x373)]))[_0x54265b(0x352,0x367)](_0x314e4c=>_0x314e4c[_0x54265b(0x366,0x36f)](_0x54265b(0x396,0x391))[_0x54265b(0x38e,0x381)]('The\x20role\x20to\x20count\x20(required\x20for\x20role-based\x20stats)')[_0x54265b(0x37b,0x371)](![])))[_0x54265b(0x3a7,0x388)](_0x630e7b=>_0x630e7b['setName']('remove')['setDescription'](_0x54265b(0x382,0x37d))[_0x54265b(0x381,0x37a)](_0x475d12=>_0x475d12[_0x54265b(0x380,0x36f)](_0x54265b(0x360,0x36d))[_0x54265b(0x36d,0x381)]('The\x20voice\x20channel\x20stat\x20to\x20remove')['setRequired'](!![])['addChannelTypes'](ChannelType[_0x54265b(0x386,0x373)]))),'category':_0x54265b(0x33b,0x352),async 'execute'(_0x44fa01){function _0x2cd0ec(_0x74f5b6,_0x20fbd7){return _0x54265b(_0x20fbd7,_0x74f5b6- -0x37d);}if(!_0x44fa01[_0x2cd0ec(-0x8,0x15)][_0x2cd0ec(-0x24,-0x2e)]['has'](PermissionsBitField[_0x2cd0ec(-0x15,-0xc)][_0x2cd0ec(-0x21,-0x6)]))return _0x44fa01[_0x2cd0ec(-0x13,-0x2c)]({'content':_0x2cd0ec(-0x29,-0x3b),'ephemeral':!![]});const _0x4a6034=_0x44fa01['options'][_0x2cd0ec(-0x1,-0x6)](),_0xf1fe44=_0x44fa01[_0x2cd0ec(-0x14,-0xe)][_0x2cd0ec(0xe,0x18)]('channelname'),_0x1d5793=_0x44fa01['options'][_0x2cd0ec(0xe,0x2f)]('type'),_0xf31658=_0x44fa01['options']['getChannel'](_0x2cd0ec(-0x10,-0x2e)),_0xe23866=_0x44fa01[_0x2cd0ec(-0x14,-0x2f)][_0x2cd0ec(0x3,0x4)](_0x2cd0ec(0x14,-0xa)),_0x85bc25=_0x44fa01[_0x2cd0ec(-0x2,-0x21)]['id'];if(_0x4a6034==='add'){if(!_0xf1fe44['includes'](_0x2cd0ec(0x9,0x20)))return _0x44fa01[_0x2cd0ec(-0x13,0xe)]({'content':'The\x20channel\x20name\x20must\x20include\x20the\x20{stats}\x20placeholder.','ephemeral':!![]});if((_0x1d5793===_0x2cd0ec(0xd,-0xe)||_0x1d5793===_0x2cd0ec(-0x1e,-0x38))&&!_0xe23866)return _0x44fa01[_0x2cd0ec(-0x13,-0x2d)]({'content':'You\x20must\x20specify\x20a\x20role\x20for\x20this\x20stat\x20type.','ephemeral':!![]});try{const _0x317887=await ChannelStat['findOne']({'guildId':_0x85bc25,'channelId':_0xf31658['id']});if(_0x317887)return _0x44fa01['reply']({'content':_0x2cd0ec(-0x28,-0x34),'ephemeral':!![]});const _0x1f6cfe=new ChannelStat({'guildId':_0x85bc25,'type':_0x1d5793,'channelId':_0xf31658['id'],'channelName':_0xf1fe44,'roleId':_0xe23866?_0xe23866['id']:null});await _0x1f6cfe[_0x2cd0ec(-0xd,0x6)](),await _0x44fa01[_0x2cd0ec(-0x13,-0x12)]({'content':'Added\x20stat\x20for\x20channel\x20**'+_0xf31658[_0x2cd0ec(-0x22,-0x20)]+_0x2cd0ec(-0xf,-0x30)+_0x1d5793+'**'+(_0xe23866?'\x20and\x20role\x20**'+_0xe23866[_0x2cd0ec(-0x22,-0x12)]+'**':'')+'.','ephemeral':!![]});}catch(_0x2afb32){console[_0x2cd0ec(-0x2d,-0x30)](_0x2afb32),await _0x44fa01[_0x2cd0ec(-0x13,-0x4)]({'content':_0x2cd0ec(-0x2a,-0x46),'ephemeral':!![]});}}else{if(_0x4a6034===_0x2cd0ec(0x10,-0x10))try{const _0x46f809=await ChannelStat[_0x2cd0ec(0xf,0x1d)]({'guildId':_0x85bc25,'channelId':_0xf31658['id']});_0x46f809?await _0x44fa01[_0x2cd0ec(-0x13,0x8)]({'content':_0x2cd0ec(0xa,-0xd)+_0xf31658[_0x2cd0ec(-0x22,-0x2a)]+_0x2cd0ec(0x15,0x4),'ephemeral':!![]}):await _0x44fa01['reply']({'content':_0x2cd0ec(-0x12,-0x28)+_0xf31658[_0x2cd0ec(-0x22,-0x14)]+'**\x20not\x20found.','ephemeral':!![]});}catch(_0x1b2dab){console[_0x2cd0ec(-0x2d,-0x18)](_0x1b2dab),await _0x44fa01[_0x2cd0ec(-0x13,-0x16)]({'content':_0x2cd0ec(-0x23,-0x13),'ephemeral':!![]});}}}};