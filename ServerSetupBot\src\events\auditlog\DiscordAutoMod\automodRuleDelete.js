const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.AutoModerationRuleDelete,
    async execute(rule, client) {
        try {
            console.log(`🗑️ AutoMod rule deleted: ${rule.name} in ${rule.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(rule.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'AUTOMOD_RULE_DELETE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'AUTOMOD_RULE_DELETE',
                user: 'System',
                userId: null,
                action: 'Quy tắc AutoMod được xóa',
                details: `Quy tắc AutoMod **${rule.name}** đã được xóa`,
                target: rule.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the rule from audit logs
            try {
                const auditLogs = await rule.guild.fetchAuditLogs({
                    type: 142, // AUTO_MODERATION_RULE_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === rule.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Quy tắc AutoMod được xóa bởi ${auditEntry.executor.tag}`;
                }
            } catch (error) {
                console.log('Could not fetch audit logs for automod rule delete');
            }
            
            // Add to database
            await client.db.addAuditLog(rule.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Quy tắc AutoMod được xóa',
                `Vừa có quy tắc AutoMod được xóa khỏi server`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            // Get trigger type names
            const triggerTypes = {
                1: 'Keyword',
                2: 'Harmful Link',
                3: 'Spam',
                4: 'Keyword Preset',
                5: 'Mention Spam'
            };
            
            embed.addFields([
                {
                    name: '> Tên quy tắc',
                    value: `- ${rule.name}`,
                    inline: true
                },
                {
                    name: '> ID quy tắc',
                    value: `- ${rule.id}`,
                    inline: true
                },
                {
                    name: '> Loại trigger',
                    value: `- ${triggerTypes[rule.triggerType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Trạng thái trước khi xóa',
                    value: `- ${rule.enabled ? '✅ Đã bật' : '❌ Đã tắt'}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add actions that were configured
            if (rule.actions && rule.actions.length > 0) {
                const actionTypes = {
                    1: 'Block Message',
                    2: 'Send Alert Message',
                    3: 'Timeout User'
                };
                
                const actionsList = rule.actions.map(action => 
                    actionTypes[action.type] || 'Unknown Action'
                ).join(', ');
                
                embed.addFields({
                    name: '> Hành động đã có',
                    value: `- ${actionsList}`,
                    inline: false
                });
            }
            
            // Add warning about rule deletion
            embed.addFields({
                name: '⚠️ Lưu ý',
                value: '- Quy tắc AutoMod đã bị xóa và không còn bảo vệ server',
                inline: false
            });
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${rule.guild.name} • AutoMod Rule Delete`,
                iconURL: rule.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ AutoMod rule delete logged: ${rule.name}`);
            
        } catch (error) {
            console.error('Error in automodRuleDelete audit log:', error);
        }
    }
};
