const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.AutoModerationRuleCreate,
    async execute(rule, client) {
        try {
            console.log(`🛡️ AutoMod rule created: ${rule.name} in ${rule.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(rule.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'AUTOMOD_RULE_CREATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'AUTOMOD_RULE_CREATE',
                user: 'System',
                userId: null,
                action: 'Quy tắc AutoMod được tạo',
                details: `Quy tắc AutoMod **${rule.name}** đã được tạo`,
                target: rule.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who created the rule from audit logs
            try {
                const auditLogs = await rule.guild.fetchAuditLogs({
                    type: 140, // AUTO_MODERATION_RULE_CREATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === rule.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Quy tắc AutoMod được tạo bởi ${auditEntry.executor.tag}`;
                }
            } catch (error) {
                console.log('Could not fetch audit logs for automod rule create');
            }
            
            // Add to database
            await client.db.addAuditLog(rule.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🛡️ Quy tắc AutoMod được tạo',
                `Vừa có quy tắc AutoMod mới được tạo trong server`
            );
            
            embed.setColor(0x2ecc71); // Green for create
            
            // Get trigger type names
            const triggerTypes = {
                1: 'Keyword',
                2: 'Harmful Link',
                3: 'Spam',
                4: 'Keyword Preset',
                5: 'Mention Spam'
            };
            
            // Get action type names
            const actionTypes = {
                1: 'Block Message',
                2: 'Send Alert Message',
                3: 'Timeout User'
            };
            
            embed.addFields([
                {
                    name: '> Tên quy tắc',
                    value: `- ${rule.name}`,
                    inline: true
                },
                {
                    name: '> ID quy tắc',
                    value: `- ${rule.id}`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${rule.enabled ? '✅ Bật' : '❌ Tắt'}`,
                    inline: true
                },
                {
                    name: '> Loại trigger',
                    value: `- ${triggerTypes[rule.triggerType] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người tạo',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add actions info
            if (rule.actions && rule.actions.length > 0) {
                const actionsList = rule.actions.map(action => 
                    actionTypes[action.type] || 'Unknown Action'
                ).join(', ');
                
                embed.addFields({
                    name: '> Hành động',
                    value: `- ${actionsList}`,
                    inline: false
                });
            }
            
            // Add exempt roles/channels if any
            if (rule.exemptRoles && rule.exemptRoles.length > 0) {
                const exemptRolesList = rule.exemptRoles.map(roleId => `<@&${roleId}>`).join(', ');
                embed.addFields({
                    name: '> Roles được miễn',
                    value: exemptRolesList.length > 1000 ? exemptRolesList.substring(0, 1000) + '...' : exemptRolesList,
                    inline: false
                });
            }
            
            if (rule.exemptChannels && rule.exemptChannels.length > 0) {
                const exemptChannelsList = rule.exemptChannels.map(channelId => `<#${channelId}>`).join(', ');
                embed.addFields({
                    name: '> Kênh được miễn',
                    value: exemptChannelsList.length > 1000 ? exemptChannelsList.substring(0, 1000) + '...' : exemptChannelsList,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${rule.guild.name} • AutoMod Rule Create`,
                iconURL: rule.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ AutoMod rule create logged: ${rule.name}`);
            
        } catch (error) {
            console.error('Error in automodRuleCreate audit log:', error);
        }
    }
};
