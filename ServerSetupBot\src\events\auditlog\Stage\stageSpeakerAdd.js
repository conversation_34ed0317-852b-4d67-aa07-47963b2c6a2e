const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'stageSpeakerAdd', // Custom event name
    async execute(member, stageInstance, client) {
        try {
            console.log(`🎤 Stage speaker added: ${member.user.tag} in ${stageInstance.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(stageInstance.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'STAGE_SPEAKER_ADD')) return;
            
            // Create event data
            const eventData = {
                eventType: 'STAGE_SPEAKER_ADD',
                user: 'System',
                userId: null,
                action: 'Speaker được thêm vào stage',
                details: `**${member.user.tag}** đã được thêm làm speaker trong stage`,
                target: member.user.tag,
                channel: stageInstance.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who added the speaker from audit logs
            try {
                const auditLogs = await stageInstance.guild.fetchAuditLogs({
                    type: 24, // MEMBER_UPDATE (for voice state changes)
                    limit: 5
                });
                
                const auditEntry = auditLogs.entries.find(entry => 
                    entry.target?.id === member.user.id && 
                    entry.createdTimestamp > Date.now() - 10000 // Within last 10 seconds
                );
                
                if (auditEntry) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Speaker được thêm vào stage bởi ${auditEntry.executor.tag}`;
                }
            } catch (error) {
                console.log('Could not fetch audit logs for stage speaker add');
            }
            
            // Add to database
            await client.db.addAuditLog(stageInstance.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🎤 Speaker được thêm vào stage',
                `Vừa có speaker mới được thêm vào stage trong server`
            );
            
            embed.setColor(0x2ecc71); // Green for speaker add
            
            embed.addFields([
                {
                    name: '> Speaker mới',
                    value: `- ${member.user.tag} (${member.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${member.user.id}`,
                    inline: true
                },
                {
                    name: '> Người thêm speaker',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add stage info
            embed.addFields([
                {
                    name: '> Chủ đề stage',
                    value: `- ${stageInstance.topic || 'Không có chủ đề'}`,
                    inline: false
                },
                {
                    name: '> ID stage',
                    value: `- ${stageInstance.id}`,
                    inline: true
                },
                {
                    name: '> Kênh stage',
                    value: `- ${stageInstance.channel || 'Unknown'}`,
                    inline: true
                }
            ]);
            
            // Add privacy level
            const privacyLevels = {
                1: '🌍 Public',
                2: '🏠 Guild Only'
            };
            
            embed.addFields({
                name: '> Mức độ riêng tư',
                value: `- ${privacyLevels[stageInstance.privacyLevel] || 'Unknown'}`,
                inline: true
            });
            
            // Add member info
            const accountAge = Date.now() - member.user.createdTimestamp;
            const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
            
            embed.addFields([
                {
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                },
                {
                    name: '> Tham gia server lúc',
                    value: `- <t:${Math.floor(member.joinedTimestamp / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add member roles
            const roleCount = member.roles.cache.size - 1; // -1 for @everyone
            if (roleCount > 0) {
                const topRoles = member.roles.cache
                    .filter(role => role.id !== member.guild.id)
                    .sort((a, b) => b.position - a.position)
                    .first(3)
                    .map(role => role.name)
                    .join(', ');
                
                embed.addFields([
                    {
                        name: '> Số roles',
                        value: `- ${roleCount} roles`,
                        inline: true
                    },
                    {
                        name: '> Top roles',
                        value: `- ${topRoles}`,
                        inline: true
                    }
                ]);
            }
            
            // Add speaker permissions
            embed.addFields({
                name: '> 🎤 Quyền speaker',
                value: [
                    '• Có thể nói chuyện trong stage',
                    '• Được nghe bởi tất cả audience',
                    '• Có thể tương tác với speakers khác',
                    '• Có thể tự mute/unmute',
                    '• Có thể rời khỏi stage bất cứ lúc nào'
                ].join('\n'),
                inline: false
            });
            
            // Add stage guidelines for speakers
            embed.addFields({
                name: '> 📋 Hướng dẫn cho speaker',
                value: [
                    '• Tuân thủ chủ đề stage',
                    '• Tôn trọng speakers khác',
                    '• Không spam hoặc gây rối',
                    '• Sử dụng mute khi không nói',
                    '• Tuân thủ quy tắc server'
                ].join('\n'),
                inline: false
            });
            
            // Add current stage stats
            if (stageInstance.channel) {
                const totalMembers = stageInstance.channel.members?.size || 0;
                embed.addFields({
                    name: '> 📊 Thống kê stage hiện tại',
                    value: `- Tổng số người tham gia: ${totalMembers} thành viên`,
                    inline: true
                });
            }
            
            // Add stage link
            if (stageInstance.channel) {
                embed.addFields({
                    name: '> Link tham gia stage',
                    value: `- [Nhấn để tham gia](https://discord.com/channels/${stageInstance.guild.id}/${stageInstance.channel.id})`,
                    inline: false
                });
            }
            
            // Set user avatar as thumbnail
            if (member.user.displayAvatarURL()) {
                embed.setThumbnail(member.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${stageInstance.guild.name} • Stage Speaker Add`,
                iconURL: stageInstance.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Stage speaker add logged: ${member.user.tag}`);
            
        } catch (error) {
            console.error('Error in stageSpeakerAdd audit log:', error);
        }
    }
};
