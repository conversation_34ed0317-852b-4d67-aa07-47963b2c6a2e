const { SlashCommandBuilder, PermissionFlagsBits, ChannelType } = require('discord.js');
const { createSuccessEmbed, createErrorEmbed, createWarningEmbed } = require('../../utils/embedBuilder.js');
const { sendModerationLog } = require('../../utils/moderationUtils.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('lockdown')
        .setDescription('Khóa kênh hoặc toàn server')
        .addStringOption(option =>
            option.setName('scope')
                .setDescription('Phạm vi lockdown')
                .setRequired(true)
                .addChoices(
                    { name: '📍 Channel hiện tại', value: 'current' },
                    { name: '📁 Category hiện tại', value: 'category' },
                    { name: '🏠 Toàn server', value: 'server' },
                    { name: '📝 Chỉ text channels', value: 'text' },
                    { name: '🔊 Chỉ voice channels', value: 'voice' }
                )
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Lý do lockdown')
                .setRequired(false)
        )
        .addBooleanOption(option =>
            option.setName('announce')
                .setDescription('Thông báo lockdown cho thành viên')
                .setRequired(false)
        )
        .addStringOption(option =>
            option.setName('duration')
                .setDescription('Thời gian lockdown (ví dụ: 1h, 30m)')
                .setRequired(false)
        ),
    category: 'moderation',
    adminOnly: false,
    manageServer: false,
    
    async execute(interaction, client) {
        // Kiểm tra quyền
        if (!interaction.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
            const errorEmbed = createErrorEmbed(
                'Bạn không có quyền!',
                'Bạn cần quyền `Manage Channels` để sử dụng lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageChannels)) {
            const errorEmbed = createErrorEmbed(
                'Bot thiếu quyền!',
                'Bot cần quyền `Manage Channels` để thực hiện lệnh này.'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
        
        const scope = interaction.options.getString('scope');
        const reason = interaction.options.getString('reason') || 'Lockdown khẩn cấp';
        const announce = interaction.options.getBoolean('announce') || false;
        const duration = interaction.options.getString('duration');
        const guildId = interaction.guild.id;
        
        await interaction.deferReply();
        
        try {
            let channelsToLock = [];
            let lockdownType = '';
            
            // Xác định channels cần lock
            switch (scope) {
                case 'current':
                    channelsToLock = [interaction.channel];
                    lockdownType = 'Kênh hiện tại';
                    break;
                    
                case 'category':
                    if (interaction.channel.parent) {
                        channelsToLock = interaction.channel.parent.children.cache
                            .filter(ch => ch.type === ChannelType.GuildText || ch.type === ChannelType.GuildVoice)
                            .map(ch => ch);
                        lockdownType = `Category: ${interaction.channel.parent.name}`;
                    } else {
                        channelsToLock = [interaction.channel];
                        lockdownType = 'Kênh hiện tại (không có category)';
                    }
                    break;
                    
                case 'server':
                    channelsToLock = interaction.guild.channels.cache
                        .filter(ch => ch.type === ChannelType.GuildText || ch.type === ChannelType.GuildVoice)
                        .map(ch => ch);
                    lockdownType = 'Toàn server';
                    break;
                    
                case 'text':
                    channelsToLock = interaction.guild.channels.cache
                        .filter(ch => ch.type === ChannelType.GuildText)
                        .map(ch => ch);
                    lockdownType = 'Tất cả text channels';
                    break;
                    
                case 'voice':
                    channelsToLock = interaction.guild.channels.cache
                        .filter(ch => ch.type === ChannelType.GuildVoice)
                        .map(ch => ch);
                    lockdownType = 'Tất cả voice channels';
                    break;
            }
            
            if (channelsToLock.length === 0) {
                const errorEmbed = createErrorEmbed(
                    'Không có kênh nào để lock!',
                    'Không tìm thấy kênh nào phù hợp với phạm vi đã chọn.'
                );
                return await interaction.editReply({ embeds: [errorEmbed] });
            }
            
            // Thông báo lockdown nếu được yêu cầu
            if (announce && scope !== 'current') {
                const announceEmbed = createWarningEmbed(
                    '🔒 LOCKDOWN ACTIVATED',
                    `**Phạm vi:** ${lockdownType}\n` +
                    `**Lý do:** ${reason}\n` +
                    `**Bởi:** ${interaction.user.tag}\n` +
                    `${duration ? `**Thời gian:** ${duration}\n` : ''}` +
                    `**Thời điểm:** <t:${Math.floor(Date.now() / 1000)}:F>\n\n` +
                    `⚠️ Các kênh đã bị khóa tạm thời. Vui lòng chờ thông báo mở khóa.`
                );
                
                try {
                    await interaction.channel.send({ embeds: [announceEmbed] });
                } catch (error) {
                    console.log('Không thể gửi thông báo lockdown');
                }
            }
            
            // Thực hiện lockdown
            let lockedCount = 0;
            let failedChannels = [];
            
            for (const channel of channelsToLock) {
                try {
                    const everyoneRole = interaction.guild.roles.everyone;
                    
                    if (channel.type === ChannelType.GuildText) {
                        // Lock text channel
                        await channel.permissionOverwrites.edit(everyoneRole, {
                            SendMessages: false,
                            AddReactions: false,
                            SendMessagesInThreads: false,
                            CreatePublicThreads: false,
                            CreatePrivateThreads: false
                        }, { reason: `Lockdown: ${reason} | Bởi: ${interaction.user.tag}` });
                    } else if (channel.type === ChannelType.GuildVoice) {
                        // Lock voice channel
                        await channel.permissionOverwrites.edit(everyoneRole, {
                            Connect: false,
                            Speak: false
                        }, { reason: `Lockdown: ${reason} | Bởi: ${interaction.user.tag}` });
                    }
                    
                    lockedCount++;
                } catch (error) {
                    console.error(`Lỗi khi lock channel ${channel.name}:`, error);
                    failedChannels.push(channel.name);
                }
            }
            
            // Lưu lockdown vào database để có thể unlock sau
            await client.db.addLockdown(guildId, {
                scope: scope,
                channels: channelsToLock.map(ch => ch.id),
                moderatorId: interaction.user.id,
                reason: reason,
                duration: duration,
                createdAt: new Date().toISOString()
            });
            
            // Log vào moderation logs
            await client.db.addModerationLog(guildId, {
                type: 'lockdown',
                userId: null,
                moderatorId: interaction.user.id,
                reason: reason,
                duration: duration ? duration : null
            });
            
            const successEmbed = createSuccessEmbed(
                '🔒 Lockdown đã được kích hoạt!',
                `**Phạm vi:** ${lockdownType}\n` +
                `**Kênh đã khóa:** ${lockedCount}/${channelsToLock.length}\n` +
                `${failedChannels.length > 0 ? `**Kênh lỗi:** ${failedChannels.join(', ')}\n` : ''}` +
                `**Lý do:** ${reason}\n` +
                `${duration ? `**Thời gian:** ${duration}\n` : ''}` +
                `**Bởi:** ${interaction.user.tag}`
            );
            
            if (failedChannels.length > 0) {
                successEmbed.addFields({
                    name: '⚠️ Cảnh báo',
                    value: `Không thể khóa ${failedChannels.length} kênh. Kiểm tra quyền bot trong các kênh này.`,
                    inline: false
                });
            }
            
            successEmbed.addFields({
                name: '🔓 Mở khóa',
                value: `Sử dụng \`/unlock scope:${scope}\` để mở khóa.`,
                inline: false
            });
            
            await interaction.editReply({ embeds: [successEmbed] });
            
            // Gửi log vào channel
            await sendModerationLog(client, guildId, {
                type: 'lockdown',
                user: { tag: 'System', id: 'system' },
                moderator: interaction.user,
                reason: reason,
                scope: lockdownType,
                count: lockedCount,
                duration: duration
            });
            
        } catch (error) {
            console.error('Lỗi khi lockdown:', error);
            const errorEmbed = createErrorEmbed(
                'Lỗi lockdown!',
                'Đã xảy ra lỗi khi thực hiện lockdown. Vui lòng thử lại sau!'
            );
            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },
};
