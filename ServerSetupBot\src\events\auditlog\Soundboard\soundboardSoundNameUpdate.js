const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: 'soundboardSoundUpdate', // Custom event name
    async execute(oldSound, newSound, client) {
        try {
            // Only handle name changes
            if (oldSound.name === newSound.name) return;
            
            console.log(`✏️ Soundboard sound name updated: ${oldSound.name} -> ${newSound.name} in ${newSound.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newSound.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'SOUNDBOARD_SOUND_NAME_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'SOUNDBOARD_SOUND_NAME_UPDATE',
                user: 'System',
                userId: null,
                action: 'Tên âm thanh soundboard được cập nhật',
                details: `Tên âm thanh soundboard đã được thay đổi từ **${oldSound.name}** thành **${newSound.name}**`,
                target: newSound.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the sound name from audit logs
            try {
                const auditLogs = await newSound.guild.fetchAuditLogs({
                    type: 131, // SOUNDBOARD_SOUND_UPDATE (hypothetical)
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newSound.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Tên âm thanh soundboard được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for soundboard name update');
            }
            
            // Add to database
            await client.db.addAuditLog(newSound.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '✏️ Tên âm thanh soundboard được cập nhật',
                `Vừa có tên âm thanh soundboard được thay đổi trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> ID âm thanh',
                    value: `- ${newSound.id}`,
                    inline: true
                },
                {
                    name: '> Tên cũ',
                    value: `- ${oldSound.name}`,
                    inline: true
                },
                {
                    name: '> Tên mới',
                    value: `- ${newSound.name}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add sound properties
            if (newSound.volume !== undefined) {
                embed.addFields({
                    name: '> Âm lượng',
                    value: `- ${Math.round(newSound.volume * 100)}%`,
                    inline: true
                });
            }
            
            if (newSound.emoji) {
                embed.addFields({
                    name: '> Emoji',
                    value: `- ${newSound.emoji}`,
                    inline: true
                });
            }
            
            // Add creator info if available
            if (newSound.user) {
                embed.addFields({
                    name: '> Người tạo',
                    value: `- ${newSound.user.tag}`,
                    inline: true
                });
            }
            
            // Add creation info if available
            if (newSound.createdTimestamp) {
                embed.addFields({
                    name: '> Được tạo lúc',
                    value: `- <t:${Math.floor(newSound.createdTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add usage info
            embed.addFields({
                name: '> Cách sử dụng',
                value: [
                    '• Vào voice channel',
                    '• Nhấn biểu tượng soundboard',
                    `• Tìm âm thanh "${newSound.name}"`,
                    '• Nhấn để phát'
                ].join('\n'),
                inline: false
            });
            
            // Add impact note
            embed.addFields({
                name: '> 📝 Lưu ý',
                value: `- Tên mới sẽ hiển thị trong soundboard của tất cả thành viên`,
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newSound.guild.name} • Soundboard Name Update`,
                iconURL: newSound.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Soundboard name update logged: ${oldSound.name} -> ${newSound.name}`);
            
        } catch (error) {
            console.error('Error in soundboardSoundNameUpdate audit log:', error);
        }
    }
};
