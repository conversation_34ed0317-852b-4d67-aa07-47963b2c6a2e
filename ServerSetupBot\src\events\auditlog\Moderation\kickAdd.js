const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildMemberRemove,
    async execute(member, client) {
        try {
            // Check if this was a kick by looking at audit logs
            let wasKicked = false;
            let kickInfo = null;
            
            try {
                const auditLogs = await member.guild.fetchAuditLogs({
                    type: 20, // MEMBER_KICK
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && 
                    auditEntry.target?.id === member.user.id && 
                    Date.now() - auditEntry.createdTimestamp < 5000) { // Within 5 seconds
                    wasKicked = true;
                    kickInfo = auditEntry;
                }
            } catch (error) {
                console.log('Could not fetch audit logs for kick check');
            }
            
            if (!wasKicked) return;
            
            console.log(`👢 User kicked: ${member.user.tag} in ${member.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(member.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'KICK_ADD')) return;
            
            // Create event data
            const eventData = {
                eventType: 'KICK_ADD',
                user: member.user.tag,
                userId: member.user.id,
                action: 'Thành viên bị kick',
                details: `**${member.user.tag}** đã bị kick khỏi server`,
                target: member.user.tag,
                timestamp: new Date().toISOString()
            };
            
            // Add moderator info from audit log
            if (kickInfo) {
                eventData.moderator = kickInfo.executor.tag;
                eventData.moderatorId = kickInfo.executor.id;
                eventData.action = `Thành viên bị kick bởi ${kickInfo.executor.tag}`;
                if (kickInfo.reason) {
                    eventData.reason = kickInfo.reason;
                }
            }
            
            // Add to database
            await client.db.addAuditLog(member.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '👢 Thành viên bị kick',
                `Vừa có thành viên bị kick khỏi server`
            );
            
            embed.setColor(0xe67e22); // Orange for kick
            
            embed.addFields([
                {
                    name: '> Thành viên bị kick',
                    value: `- ${member.user.tag} (${member.user})`,
                    inline: false
                },
                {
                    name: '> ID người dùng',
                    value: `- ${member.user.id}`,
                    inline: true
                },
                {
                    name: '> Thời gian kick',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add moderator info
            if (eventData.moderator) {
                embed.addFields({
                    name: '> Moderator',
                    value: `- ${eventData.moderator}`,
                    inline: true
                });
            }
            
            // Add kick reason if available
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do kick',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Add member info
            const accountAge = Date.now() - member.user.createdTimestamp;
            const accountAgeDays = Math.floor(accountAge / (1000 * 60 * 60 * 24));
            const memberAge = Date.now() - member.joinedTimestamp;
            const memberAgeDays = Math.floor(memberAge / (1000 * 60 * 60 * 24));
            
            embed.addFields([
                {
                    name: '> Tuổi tài khoản',
                    value: `- ${accountAgeDays} ngày`,
                    inline: true
                },
                {
                    name: '> Thời gian trong server',
                    value: `- ${memberAgeDays} ngày`,
                    inline: true
                },
                {
                    name: '> Tham gia server lúc',
                    value: `- <t:${Math.floor(member.joinedTimestamp / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add roles info
            const roles = member.roles.cache.filter(role => role.id !== member.guild.id);
            if (roles.size > 0) {
                const rolesList = roles.size > 5 
                    ? `${roles.first(5).map(role => role.name).join(', ')} và ${roles.size - 5} roles khác`
                    : roles.map(role => role.name).join(', ');
                
                embed.addFields({
                    name: '> Roles đã có',
                    value: `- ${rolesList}`,
                    inline: false
                });
            }
            
            // Add kick impact
            embed.addFields({
                name: '> 📝 Tác động của kick',
                value: [
                    '• Thành viên bị loại khỏi server',
                    '• Có thể tham gia lại bằng invite link',
                    '• Mất tất cả roles và permissions',
                    '• Tin nhắn vẫn tồn tại trong server'
                ].join('\n'),
                inline: false
            });
            
            // Add moderation guidelines
            embed.addFields({
                name: '> 💡 Lưu ý về kick',
                value: [
                    '• Kick nhẹ hơn ban, có thể quay lại',
                    '• Phù hợp cho vi phạm không nghiêm trọng',
                    '• Nên cảnh báo trước khi kick',
                    '• Ghi chép lý do rõ ràng'
                ].join('\n'),
                inline: false
            });
            
            // Set user avatar as thumbnail
            if (member.user.displayAvatarURL()) {
                embed.setThumbnail(member.user.displayAvatarURL({ dynamic: true, size: 256 }));
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${member.guild.name} • Member Kick`,
                iconURL: member.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Kick logged: ${member.user.tag}`);
            
        } catch (error) {
            console.error('Error in kickAdd audit log:', error);
        }
    }
};
