const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.WebhookDelete,
    async execute(webhook, client) {
        try {
            console.log(`🗑️ Webhook deleted from channel: ${webhook.channel?.name} in ${webhook.guild?.name}`);
            
            // Skip if no guild
            if (!webhook.guild) return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(webhook.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_WEBHOOK_DELETE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_WEBHOOK_DELETE',
                user: 'System',
                userId: null,
                action: 'Webhook được xóa khỏi kênh',
                details: `Webhook **${webhook.name || 'Unnamed'}** đã được xóa khỏi kênh **${webhook.channel?.name || 'Unknown'}**`,
                target: webhook.name || 'Unnamed',
                channel: webhook.channel?.name || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get who deleted the webhook from audit logs
            try {
                const auditLogs = await webhook.guild.fetchAuditLogs({
                    type: 52, // WEBHOOK_DELETE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === webhook.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Webhook được xóa bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for webhook delete');
            }
            
            // Add to database
            await client.db.addAuditLog(webhook.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🗑️ Webhook được xóa khỏi kênh',
                `Vừa có webhook được xóa khỏi kênh`
            );
            
            embed.setColor(0xe74c3c); // Red for delete
            
            // Get webhook type names
            const webhookTypes = {
                1: 'Incoming Webhook',
                2: 'Channel Follower Webhook',
                3: 'Application Webhook'
            };
            
            embed.addFields([
                {
                    name: '> Tên webhook',
                    value: `- ${webhook.name || 'Unnamed Webhook'}`,
                    inline: true
                },
                {
                    name: '> ID webhook',
                    value: `- ${webhook.id}`,
                    inline: true
                },
                {
                    name: '> Loại webhook',
                    value: `- ${webhookTypes[webhook.type] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: `- ${webhook.channel?.name || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${webhook.channel?.id || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người xóa',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian xóa',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add webhook owner info if available
            if (webhook.owner) {
                embed.addFields({
                    name: '> Chủ sở hữu',
                    value: `- ${webhook.owner.tag || webhook.owner.name}`,
                    inline: true
                });
            }
            
            // Add application info if it was an application webhook
            if (webhook.application) {
                embed.addFields({
                    name: '> Ứng dụng',
                    value: `- ${webhook.application.name}`,
                    inline: true
                });
            }
            
            // Add creation info if available
            if (webhook.createdTimestamp) {
                const lifetime = Date.now() - webhook.createdTimestamp;
                const lifetimeDays = Math.floor(lifetime / (1000 * 60 * 60 * 24));
                const lifetimeHours = Math.floor((lifetime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                
                embed.addFields([
                    {
                        name: '> Được tạo lúc',
                        value: `- <t:${Math.floor(webhook.createdTimestamp / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '> Thời gian tồn tại',
                        value: `- ${lifetimeDays} ngày, ${lifetimeHours} giờ`,
                        inline: true
                    }
                ]);
            }
            
            // Add webhook avatar info if it had one
            if (webhook.avatarURL()) {
                embed.addFields({
                    name: '> Avatar đã có',
                    value: `- [Xem avatar](${webhook.avatarURL({ dynamic: true, size: 256 })})`,
                    inline: true
                });
                embed.setThumbnail(webhook.avatarURL({ dynamic: true, size: 256 }));
            } else {
                embed.addFields({
                    name: '> Avatar đã có',
                    value: `- Không có avatar`,
                    inline: true
                });
            }
            
            // Add impact information
            embed.addFields({
                name: '> 📝 Tác động',
                value: [
                    '• URL webhook không còn hoạt động',
                    '• Ứng dụng sử dụng webhook sẽ gặp lỗi',
                    '• Cần tạo webhook mới nếu muốn tiếp tục',
                    '• Tin nhắn cũ từ webhook vẫn tồn tại'
                ].join('\n'),
                inline: false
            });
            
            // Add security note
            embed.addFields({
                name: '> 🔒 Bảo mật',
                value: [
                    '• URL webhook cũ đã vô hiệu hóa',
                    '• Không thể sử dụng để gửi tin nhắn nữa',
                    '• Nếu URL bị lộ, webhook đã được bảo vệ',
                    '• Cần tạo webhook mới với URL mới'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do xóa',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${webhook.guild.name} • Channel Webhook Delete`,
                iconURL: webhook.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel webhook delete logged: ${webhook.name}`);
            
        } catch (error) {
            console.error('Error in channelWebhookDelete audit log:', error);
        }
    }
};
