const db = require('../database/database');

class Item {
    constructor(data) {
        this.id = data.id;
        this.name = data.name;
        this.description = data.description;
        this.price = data.price;
        this.emoji = data.emoji;
        this.category = data.category;
        this.buyable = data.buyable;
        this.sellable = data.sellable;
        this.sellPrice = data.sell_price;
        this.createdAt = data.created_at;
    }

    // Lấy tất cả items
    static async getAll() {
        try {
            const items = await db.all('SELECT * FROM items ORDER BY category, price');
            return items.map(item => new Item(item));
        } catch (error) {
            console.error('Lỗi lấy items:', error);
            throw error;
        }
    }

    // Lấy items theo category
    static async getByCategory(category) {
        try {
            const items = await db.all('SELECT * FROM items WHERE category = ? ORDER BY price', [category]);
            return items.map(item => new Item(item));
        } catch (error) {
            console.error('Lỗi lấy items theo category:', error);
            throw error;
        }
    }

    // Lấy item theo ID
    static async findById(id) {
        try {
            const item = await db.get('SELECT * FROM items WHERE id = ?', [id]);
            return item ? new Item(item) : null;
        } catch (error) {
            console.error('Lỗi tìm item:', error);
            throw error;
        }
    }

    // Lấy item theo tên
    static async findByName(name) {
        try {
            const item = await db.get('SELECT * FROM items WHERE LOWER(name) = LOWER(?)', [name]);
            return item ? new Item(item) : null;
        } catch (error) {
            console.error('Lỗi tìm item theo tên:', error);
            throw error;
        }
    }

    // Lấy tất cả categories
    static async getCategories() {
        try {
            const categories = await db.all('SELECT DISTINCT category FROM items ORDER BY category');
            return categories.map(cat => cat.category);
        } catch (error) {
            console.error('Lỗi lấy categories:', error);
            throw error;
        }
    }

    // Thêm item mới
    static async create(itemData) {
        try {
            const result = await db.run(`
                INSERT INTO items (name, description, price, emoji, category, buyable, sellable, sell_price)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                itemData.name,
                itemData.description,
                itemData.price,
                itemData.emoji,
                itemData.category || 'general',
                itemData.buyable !== false ? 1 : 0,
                itemData.sellable !== false ? 1 : 0,
                itemData.sellPrice || Math.floor(itemData.price * 0.5)
            ]);

            return await Item.findById(result.id);
        } catch (error) {
            console.error('Lỗi tạo item:', error);
            throw error;
        }
    }

    // Cập nhật item
    async update(updateData) {
        try {
            await db.run(`
                UPDATE items SET 
                    name = ?, description = ?, price = ?, emoji = ?, 
                    category = ?, buyable = ?, sellable = ?, sell_price = ?
                WHERE id = ?
            `, [
                updateData.name || this.name,
                updateData.description || this.description,
                updateData.price || this.price,
                updateData.emoji || this.emoji,
                updateData.category || this.category,
                updateData.buyable !== undefined ? (updateData.buyable ? 1 : 0) : this.buyable,
                updateData.sellable !== undefined ? (updateData.sellable ? 1 : 0) : this.sellable,
                updateData.sellPrice || this.sellPrice,
                this.id
            ]);

            // Cập nhật object hiện tại
            Object.assign(this, updateData);
        } catch (error) {
            console.error('Lỗi cập nhật item:', error);
            throw error;
        }
    }

    // Xóa item
    async delete() {
        try {
            await db.run('DELETE FROM items WHERE id = ?', [this.id]);
        } catch (error) {
            console.error('Lỗi xóa item:', error);
            throw error;
        }
    }
}

module.exports = Item;
