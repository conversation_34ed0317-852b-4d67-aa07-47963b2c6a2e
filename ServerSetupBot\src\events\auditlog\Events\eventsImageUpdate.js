const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildScheduledEventUpdate,
    async execute(oldEvent, newEvent, client) {
        try {
            // Only handle image changes
            if (oldEvent.image === newEvent.image) return;
            
            console.log(`🖼️ Event image updated: ${newEvent.name} in ${newEvent.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newEvent.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'EVENTS_IMAGE_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'EVENTS_IMAGE_UPDATE',
                user: 'System',
                userId: null,
                action: '<PERSON><PERSON><PERSON> ảnh sự kiện được cập nhật',
                details: `<PERSON><PERSON><PERSON> ảnh của sự kiện **${newEvent.name}** đ<PERSON> được cập nhật`,
                target: newEvent.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the event image from audit logs
            try {
                const auditLogs = await newEvent.guild.fetchAuditLogs({
                    type: 102, // GUILD_SCHEDULED_EVENT_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newEvent.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Hình ảnh sự kiện được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for event image update');
            }
            
            // Add to database
            await client.db.addAuditLog(newEvent.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '🖼️ Hình ảnh sự kiện được cập nhật',
                `Vừa có hình ảnh sự kiện được thay đổi`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            // Get event status names
            const statusTypes = {
                1: 'Scheduled',
                2: 'Active',
                3: 'Completed',
                4: 'Canceled'
            };
            
            embed.addFields([
                {
                    name: '> Tên sự kiện',
                    value: `- ${newEvent.name}`,
                    inline: true
                },
                {
                    name: '> ID sự kiện',
                    value: `- ${newEvent.id}`,
                    inline: true
                },
                {
                    name: '> Trạng thái',
                    value: `- ${statusTypes[newEvent.status] || 'Unknown'}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            // Add image comparison
            const oldImageUrl = oldEvent.coverImageURL({ size: 512 });
            const newImageUrl = newEvent.coverImageURL({ size: 512 });
            
            if (oldImageUrl && newImageUrl) {
                embed.addFields([
                    {
                        name: '> Hình ảnh cũ',
                        value: `- [Xem hình ảnh cũ](${oldImageUrl})`,
                        inline: true
                    },
                    {
                        name: '> Hình ảnh mới',
                        value: `- [Xem hình ảnh mới](${newImageUrl})`,
                        inline: true
                    }
                ]);
                embed.setColor(0x3498db); // Blue for image change
            } else if (!oldImageUrl && newImageUrl) {
                embed.addFields({
                    name: '> Hình ảnh được thêm',
                    value: `- [Xem hình ảnh mới](${newImageUrl})`,
                    inline: false
                });
                embed.setColor(0x2ecc71); // Green for added image
            } else if (oldImageUrl && !newImageUrl) {
                embed.addFields({
                    name: '> Hình ảnh được xóa',
                    value: `- [Xem hình ảnh cũ](${oldImageUrl})`,
                    inline: false
                });
                embed.setColor(0xe74c3c); // Red for removed image
            }
            
            // Add event timing info
            if (newEvent.scheduledStartTimestamp) {
                embed.addFields({
                    name: '> Thời gian bắt đầu',
                    value: `- <t:${Math.floor(newEvent.scheduledStartTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            if (newEvent.scheduledEndTimestamp) {
                embed.addFields({
                    name: '> Thời gian kết thúc',
                    value: `- <t:${Math.floor(newEvent.scheduledEndTimestamp / 1000)}:F>`,
                    inline: true
                });
            }
            
            // Add creator info if available
            if (newEvent.creator) {
                embed.addFields({
                    name: '> Người tạo sự kiện',
                    value: `- ${newEvent.creator.tag}`,
                    inline: true
                });
            }
            
            // Add subscriber count if available
            if (newEvent.userCount !== null) {
                embed.addFields({
                    name: '> Số người quan tâm',
                    value: `- ${newEvent.userCount} người`,
                    inline: true
                });
            }
            
            // Add impact explanation based on image change
            if (!oldImageUrl && newImageUrl) {
                embed.addFields({
                    name: '> 📈 Tác động (Thêm hình ảnh)',
                    value: [
                        '• Sự kiện giờ đây có hình ảnh thu hút',
                        '• Tăng khả năng thu hút người tham gia',
                        '• Dễ nhận diện trong danh sách sự kiện',
                        '• Tạo ấn tượng chuyên nghiệp hơn'
                    ].join('\n'),
                    inline: false
                });
            } else if (oldImageUrl && !newImageUrl) {
                embed.addFields({
                    name: '> 📉 Tác động (Xóa hình ảnh)',
                    value: [
                        '• Sự kiện không còn hình ảnh',
                        '• Có thể giảm sức hút',
                        '• Khó nhận diện hơn',
                        '• Giao diện đơn giản hơn'
                    ].join('\n'),
                    inline: false
                });
            } else if (oldImageUrl && newImageUrl) {
                embed.addFields({
                    name: '> 🔄 Tác động (Thay đổi hình ảnh)',
                    value: [
                        '• Hình ảnh mới có thể thay đổi ấn tượng',
                        '• Cập nhật thông tin visual',
                        '• Có thể phản ánh nội dung mới',
                        '• Làm mới giao diện sự kiện'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add image guidelines
            embed.addFields({
                name: '> 💡 Lưu ý về hình ảnh sự kiện',
                value: [
                    '• Sử dụng hình ảnh chất lượng cao',
                    '• Kích thước khuyến nghị: 1920x1080px',
                    '• Nội dung phù hợp với chủ đề sự kiện',
                    '• Tránh hình ảnh có bản quyền'
                ].join('\n'),
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set new event image as thumbnail if available
            if (newImageUrl) {
                embed.setThumbnail(newImageUrl);
            } else if (oldImageUrl) {
                embed.setThumbnail(oldImageUrl);
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newEvent.guild.name} • Event Image Update`,
                iconURL: newEvent.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Event image update logged: ${newEvent.name}`);
            
        } catch (error) {
            console.error('Error in eventsImageUpdate audit log:', error);
        }
    }
};
