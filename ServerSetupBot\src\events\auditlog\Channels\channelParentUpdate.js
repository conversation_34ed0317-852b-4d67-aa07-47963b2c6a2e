const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.ChannelUpdate,
    async execute(oldChannel, newChannel, client) {
        try {
            // Only handle parent category changes
            if (oldChannel.parentId === newChannel.parentId) return;
            
            console.log(`📁 Channel parent updated: ${newChannel.name} in ${newChannel.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newChannel.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'CHANNEL_PARENT_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'CHANNEL_PARENT_UPDATE',
                user: 'System',
                userId: null,
                action: '<PERSON><PERSON> mục kênh được thay đổi',
                details: `<PERSON>ênh **${newChannel.name}** đ<PERSON> được chuyển danh mục`,
                target: newChannel.name,
                channel: newChannel.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the parent from audit logs
            try {
                const auditLogs = await newChannel.guild.fetchAuditLogs({
                    type: 11, // CHANNEL_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newChannel.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Danh mục kênh được thay đổi bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for parent update');
            }
            
            // Add to database
            await client.db.addAuditLog(newChannel.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📁 Danh mục kênh thay đổi',
                `Vừa có kênh được chuyển danh mục trong server`
            );
            
            embed.setColor(0xf39c12); // Orange for update
            
            embed.addFields([
                {
                    name: '> Kênh',
                    value: `- ${newChannel}`,
                    inline: true
                },
                {
                    name: '> ID kênh',
                    value: `- ${newChannel.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                }
            ]);
            
            // Add old and new parent info
            const oldParent = oldChannel.parent ? oldChannel.parent.name : 'Không có danh mục';
            const newParent = newChannel.parent ? newChannel.parent.name : 'Không có danh mục';
            
            embed.addFields([
                {
                    name: '> Danh mục cũ',
                    value: `- ${oldParent}`,
                    inline: true
                },
                {
                    name: '> Danh mục mới',
                    value: `- ${newParent}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                }
            ]);
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newChannel.guild.name} • Channel Parent Update`,
                iconURL: newChannel.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Channel parent update logged: ${newChannel.name}`);
            
        } catch (error) {
            console.error('Error in channelParentUpdate audit log:', error);
        }
    }
};
