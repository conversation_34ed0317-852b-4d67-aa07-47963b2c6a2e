// Final script to create ALL remaining audit log event files
const fs = require('fs');
const path = require('path');

console.log('🔧 Creating ALL remaining audit log event files...');

// Enhanced template for audit log files
const createFileTemplate = (fileInfo) => {
    const { event, type, desc, color, category, fields, impacts, alerts } = fileInfo;
    
    return `const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.${event},
    async execute(...args) {
        try {
            const client = args[args.length - 1]; // Client is always last argument
            
            console.log(\`📝 ${type}: Event triggered\`);
            
            // Get guild from arguments (varies by event type)
            let guild, target, user;
            if (args[0]?.guild) {
                guild = args[0].guild;
                target = args[0];
            } else if (args[1]?.guild) {
                guild = args[1].guild;
                target = args[1];
            } else if (args[0]?.channel?.guild) {
                guild = args[0].channel.guild;
                target = args[0];
            } else if (args[1]?.channel?.guild) {
                guild = args[1].channel.guild;
                target = args[1];
            } else return;
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, '${type}')) return;
            
            // Create event data
            const eventData = {
                eventType: '${type}',
                user: 'System',
                userId: null,
                action: '${desc}',
                details: \`${desc} đã xảy ra trong server\`,
                target: target?.name || target?.tag || 'Unknown',
                timestamp: new Date().toISOString()
            };
            
            // Try to get executor from audit logs
            try {
                const auditLogs = await guild.fetchAuditLogs({
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = \`${desc} bởi \${auditEntry.executor.tag}\`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for ${type}');
            }
            
            // Add to database
            await client.db.addAuditLog(guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📝 ${desc}',
                \`Vừa có \${desc.toLowerCase()} trong server\`
            );
            
            embed.setColor(${color});
            
            // Add basic fields
            embed.addFields([
                {
                    name: '> Loại sự kiện',
                    value: \`- ${type}\`,
                    inline: true
                },
                {
                    name: '> Category',
                    value: \`- ${category}\`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: \`- <t:\${Math.floor(Date.now() / 1000)}:F>\`,
                    inline: true
                },
                {
                    name: '> Người thực hiện',
                    value: \`- \${eventData.user}\`,
                    inline: true
                }
            ]);
            
            ${fields ? `// Add specific fields
            ${fields}` : ''}
            
            ${impacts ? `// Add impact information
            ${impacts}` : ''}
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: \`- \${eventData.reason}\`,
                    inline: false
                });
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: \`\${guild.name} • ${type}\`,
                iconURL: guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(\`✅ ${type} logged\`);
            
            ${alerts ? `// Smart alerts
            ${alerts}` : ''}
            
        } catch (error) {
            console.error(\`Error in ${type} audit log:\`, error);
        }
    }
};`;
};

// Define ALL remaining files with enhanced details
const filesToCreate = [
    // Emoji (2 files)
    {
        category: 'Emoji',
        file: 'emojiNameUpdate.js',
        event: 'GuildEmojiUpdate',
        type: 'EMOJI_NAME_UPDATE',
        desc: 'Tên emoji được cập nhật',
        color: '0xf39c12',
        fields: `embed.addFields([
                {
                    name: '> Tên cũ',
                    value: \`- \${args[0]?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Tên mới',
                    value: \`- \${args[1]?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Emoji',
                    value: \`- \${args[1] || 'Unknown'}\`,
                    inline: true
                }
            ]);`
    },
    {
        category: 'Emoji',
        file: 'emojiRolesUpdate.js',
        event: 'GuildEmojiUpdate',
        type: 'EMOJI_ROLES_UPDATE',
        desc: 'Roles emoji được cập nhật',
        color: '0xf39c12',
        fields: `embed.addFields([
                {
                    name: '> Emoji',
                    value: \`- \${args[1]?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Số roles hiện tại',
                    value: \`- \${args[1]?.roles?.cache?.size || 0} roles\`,
                    inline: true
                }
            ]);`
    },
    
    // Roles (3 files)
    {
        category: 'Roles',
        file: 'roleHoistUpdate.js',
        event: 'GuildRoleUpdate',
        type: 'ROLE_HOIST_UPDATE',
        desc: 'Hiển thị riêng role được cập nhật',
        color: '0xf39c12',
        fields: `embed.addFields([
                {
                    name: '> Role',
                    value: \`- \${args[1]?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Hiển thị riêng cũ',
                    value: \`- \${args[0]?.hoist ? 'Có' : 'Không'}\`,
                    inline: true
                },
                {
                    name: '> Hiển thị riêng mới',
                    value: \`- \${args[1]?.hoist ? 'Có' : 'Không'}\`,
                    inline: true
                }
            ]);`
    },
    {
        category: 'Roles',
        file: 'roleMentionableUpdate.js',
        event: 'GuildRoleUpdate',
        type: 'ROLE_MENTIONABLE_UPDATE',
        desc: 'Khả năng mention role được cập nhật',
        color: '0xf39c12',
        fields: `embed.addFields([
                {
                    name: '> Role',
                    value: \`- \${args[1]?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Mentionable cũ',
                    value: \`- \${args[0]?.mentionable ? 'Có' : 'Không'}\`,
                    inline: true
                },
                {
                    name: '> Mentionable mới',
                    value: \`- \${args[1]?.mentionable ? 'Có' : 'Không'}\`,
                    inline: true
                }
            ]);`
    },
    {
        category: 'Roles',
        file: 'roleIconUpdate.js',
        event: 'GuildRoleUpdate',
        type: 'ROLE_ICON_UPDATE',
        desc: 'Icon role được cập nhật',
        color: '0xf39c12',
        fields: `embed.addFields([
                {
                    name: '> Role',
                    value: \`- \${args[1]?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Icon cũ',
                    value: \`- \${args[0]?.icon ? 'Có icon' : 'Không có icon'}\`,
                    inline: true
                },
                {
                    name: '> Icon mới',
                    value: \`- \${args[1]?.icon ? 'Có icon' : 'Không có icon'}\`,
                    inline: true
                }
            ]);`
    },
    
    // Soundboard (3 files)
    {
        category: 'Soundboard',
        file: 'soundboardSoundNameUpdate.js',
        event: 'MessageUpdate',
        type: 'SOUNDBOARD_SOUND_NAME_UPDATE',
        desc: 'Tên âm thanh soundboard được cập nhật',
        color: '0xf39c12',
        fields: `embed.addFields([
                {
                    name: '> ID âm thanh',
                    value: \`- \${target?.id || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Tên cũ',
                    value: \`- \${args[0]?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Tên mới',
                    value: \`- \${args[1]?.name || 'Unknown'}\`,
                    inline: true
                }
            ]);`
    },
    {
        category: 'Soundboard',
        file: 'soundboardSoundVolumeUpdate.js',
        event: 'MessageUpdate',
        type: 'SOUNDBOARD_SOUND_VOLUME_UPDATE',
        desc: 'Âm lượng soundboard được cập nhật',
        color: '0xf39c12',
        fields: `embed.addFields([
                {
                    name: '> Âm thanh',
                    value: \`- \${target?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Âm lượng cũ',
                    value: \`- \${Math.round((args[0]?.volume || 1) * 100)}%\`,
                    inline: true
                },
                {
                    name: '> Âm lượng mới',
                    value: \`- \${Math.round((args[1]?.volume || 1) * 100)}%\`,
                    inline: true
                }
            ]);`
    },
    {
        category: 'Soundboard',
        file: 'soundboardSoundEmojiUpdate.js',
        event: 'MessageUpdate',
        type: 'SOUNDBOARD_SOUND_EMOJI_UPDATE',
        desc: 'Emoji soundboard được cập nhật',
        color: '0xf39c12',
        fields: `embed.addFields([
                {
                    name: '> Âm thanh',
                    value: \`- \${target?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Emoji cũ',
                    value: \`- \${args[0]?.emoji || 'Không có'}\`,
                    inline: true
                },
                {
                    name: '> Emoji mới',
                    value: \`- \${args[1]?.emoji || 'Không có'}\`,
                    inline: true
                }
            ]);`
    },
    
    // Webhooks (3 files)
    {
        category: 'Webhooks',
        file: 'webhooksAvatarUpdate.js',
        event: 'MessageUpdate',
        type: 'WEBHOOKS_AVATAR_UPDATE',
        desc: 'Avatar webhook được cập nhật',
        color: '0xf39c12',
        fields: `embed.addFields([
                {
                    name: '> Webhook',
                    value: \`- \${target?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Avatar cũ',
                    value: \`- \${args[0]?.avatar ? 'Có avatar' : 'Không có avatar'}\`,
                    inline: true
                },
                {
                    name: '> Avatar mới',
                    value: \`- \${args[1]?.avatar ? 'Có avatar' : 'Không có avatar'}\`,
                    inline: true
                }
            ]);`
    },
    {
        category: 'Webhooks',
        file: 'webhooksNameUpdate.js',
        event: 'MessageUpdate',
        type: 'WEBHOOKS_NAME_UPDATE',
        desc: 'Tên webhook được cập nhật',
        color: '0xf39c12',
        fields: `embed.addFields([
                {
                    name: '> ID webhook',
                    value: \`- \${target?.id || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Tên cũ',
                    value: \`- \${args[0]?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Tên mới',
                    value: \`- \${args[1]?.name || 'Unknown'}\`,
                    inline: true
                }
            ]);`
    },
    {
        category: 'Webhooks',
        file: 'webhooksChannelUpdate.js',
        event: 'MessageUpdate',
        type: 'WEBHOOKS_CHANNEL_UPDATE',
        desc: 'Kênh webhook được cập nhật',
        color: '0xf39c12',
        fields: `embed.addFields([
                {
                    name: '> Webhook',
                    value: \`- \${target?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Kênh cũ',
                    value: \`- \${args[0]?.channel?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Kênh mới',
                    value: \`- \${args[1]?.channel?.name || 'Unknown'}\`,
                    inline: true
                }
            ]);`
    },
    
    // Polls (5 files)
    {
        category: 'Polls',
        file: 'pollsCreate.js',
        event: 'MessageCreate',
        type: 'POLLS_CREATE',
        desc: 'Poll được tạo',
        color: '0x2ecc71',
        fields: `if (args[0]?.poll) {
                embed.addFields([
                    {
                        name: '> Câu hỏi poll',
                        value: \`- \${args[0].poll.question?.text || 'Unknown'}\`,
                        inline: false
                    },
                    {
                        name: '> Số lựa chọn',
                        value: \`- \${args[0].poll.answers?.length || 0} lựa chọn\`,
                        inline: true
                    },
                    {
                        name: '> Kênh',
                        value: \`- \${args[0].channel}\`,
                        inline: true
                    }
                ]);
            }`
    },
    {
        category: 'Polls',
        file: 'pollsDelete.js',
        event: 'MessageDelete',
        type: 'POLLS_DELETE',
        desc: 'Poll được xóa',
        color: '0xe74c3c',
        fields: `embed.addFields([
                {
                    name: '> ID tin nhắn',
                    value: \`- \${target?.id || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: \`- \${target?.channel || 'Unknown'}\`,
                    inline: true
                }
            ]);`
    },
    {
        category: 'Polls',
        file: 'pollsFinalize.js',
        event: 'MessageUpdate',
        type: 'POLLS_FINALIZE',
        desc: 'Poll được hoàn thành',
        color: '0x3498db',
        fields: `embed.addFields([
                {
                    name: '> Poll',
                    value: \`- \${args[1]?.poll?.question?.text || 'Unknown'}\`,
                    inline: false
                },
                {
                    name: '> Trạng thái',
                    value: \`- Đã hoàn thành\`,
                    inline: true
                },
                {
                    name: '> Kênh',
                    value: \`- \${args[1]?.channel || 'Unknown'}\`,
                    inline: true
                }
            ]);`
    },
    {
        category: 'Polls',
        file: 'pollsVotesAdd.js',
        event: 'MessageReactionAdd',
        type: 'POLLS_VOTES_ADD',
        desc: 'Vote được thêm vào poll',
        color: '0x2ecc71',
        fields: `embed.addFields([
                {
                    name: '> Người vote',
                    value: \`- \${args[1]?.tag || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Emoji vote',
                    value: \`- \${args[0]?.emoji || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Tin nhắn poll',
                    value: \`- \${args[0]?.message?.id || 'Unknown'}\`,
                    inline: true
                }
            ]);`
    },
    {
        category: 'Polls',
        file: 'pollsVotesRemove.js',
        event: 'MessageReactionRemove',
        type: 'POLLS_VOTES_REMOVE',
        desc: 'Vote được xóa khỏi poll',
        color: '0xe74c3c',
        fields: `embed.addFields([
                {
                    name: '> Người bỏ vote',
                    value: \`- \${args[1]?.tag || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Emoji vote',
                    value: \`- \${args[0]?.emoji || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Tin nhắn poll',
                    value: \`- \${args[0]?.message?.id || 'Unknown'}\`,
                    inline: true
                }
            ]);`
    },
    
    // Stage (3 files)
    {
        category: 'Stage',
        file: 'stageStart.js',
        event: 'StageInstanceCreate',
        type: 'STAGE_START',
        desc: 'Stage được bắt đầu',
        color: '0x2ecc71',
        fields: `embed.addFields([
                {
                    name: '> Chủ đề stage',
                    value: \`- \${target?.topic || 'Không có chủ đề'}\`,
                    inline: false
                },
                {
                    name: '> Kênh stage',
                    value: \`- \${target?.channel || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Mức độ riêng tư',
                    value: \`- \${target?.privacyLevel === 1 ? 'Public' : 'Guild Only'}\`,
                    inline: true
                }
            ]);`
    },
    {
        category: 'Stage',
        file: 'stageTopicUpdate.js',
        event: 'StageInstanceUpdate',
        type: 'STAGE_TOPIC_UPDATE',
        desc: 'Chủ đề stage được cập nhật',
        color: '0xf39c12',
        fields: `embed.addFields([
                {
                    name: '> Chủ đề cũ',
                    value: \`- \${args[0]?.topic || 'Không có'}\`,
                    inline: true
                },
                {
                    name: '> Chủ đề mới',
                    value: \`- \${args[1]?.topic || 'Không có'}\`,
                    inline: true
                },
                {
                    name: '> Kênh stage',
                    value: \`- \${args[1]?.channel || 'Unknown'}\`,
                    inline: true
                }
            ]);`
    },
    {
        category: 'Stage',
        file: 'stageEnd.js',
        event: 'StageInstanceDelete',
        type: 'STAGE_END',
        desc: 'Stage được kết thúc',
        color: '0xe74c3c',
        fields: `embed.addFields([
                {
                    name: '> Chủ đề stage',
                    value: \`- \${target?.topic || 'Không có chủ đề'}\`,
                    inline: false
                },
                {
                    name: '> Kênh stage',
                    value: \`- \${target?.channel?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Thời gian tồn tại',
                    value: \`- \${target?.createdTimestamp ? Math.floor((Date.now() - target.createdTimestamp) / (1000 * 60)) + ' phút' : 'Unknown'}\`,
                    inline: true
                }
            ]);`
    },
    
    // Stickers (3 files)
    {
        category: 'Stickers',
        file: 'stickersCreate.js',
        event: 'GuildStickerCreate',
        type: 'STICKERS_CREATE',
        desc: 'Sticker được tạo',
        color: '0x2ecc71',
        fields: `embed.addFields([
                {
                    name: '> Tên sticker',
                    value: \`- \${target?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> ID sticker',
                    value: \`- \${target?.id || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Mô tả',
                    value: \`- \${target?.description || 'Không có mô tả'}\`,
                    inline: false
                }
            ]);`
    },
    {
        category: 'Stickers',
        file: 'stickersDelete.js',
        event: 'GuildStickerDelete',
        type: 'STICKERS_DELETE',
        desc: 'Sticker được xóa',
        color: '0xe74c3c',
        fields: `embed.addFields([
                {
                    name: '> Tên sticker',
                    value: \`- \${target?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> ID sticker',
                    value: \`- \${target?.id || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Mô tả đã có',
                    value: \`- \${target?.description || 'Không có mô tả'}\`,
                    inline: false
                }
            ]);`
    },
    {
        category: 'Stickers',
        file: 'stickersNameUpdate.js',
        event: 'GuildStickerUpdate',
        type: 'STICKERS_NAME_UPDATE',
        desc: 'Tên sticker được cập nhật',
        color: '0xf39c12',
        fields: `embed.addFields([
                {
                    name: '> ID sticker',
                    value: \`- \${target?.id || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Tên cũ',
                    value: \`- \${args[0]?.name || 'Unknown'}\`,
                    inline: true
                },
                {
                    name: '> Tên mới',
                    value: \`- \${args[1]?.name || 'Unknown'}\`,
                    inline: true
                }
            ]);`
    }
];

// Create directories and files
let createdCount = 0;
let skippedCount = 0;

for (const fileInfo of filesToCreate) {
    const categoryDir = path.join(__dirname, 'src', 'events', 'auditlog', fileInfo.category);
    
    // Create category directory if it doesn't exist
    if (!fs.existsSync(categoryDir)) {
        fs.mkdirSync(categoryDir, { recursive: true });
        console.log(`📁 Created directory: ${fileInfo.category}`);
    }
    
    const filePath = path.join(categoryDir, fileInfo.file);
    
    // Only create if file doesn't exist
    if (!fs.existsSync(filePath)) {
        const content = createFileTemplate(fileInfo);
        fs.writeFileSync(filePath, content);
        console.log(`✅ Created: ${fileInfo.category}/${fileInfo.file}`);
        createdCount++;
    } else {
        console.log(`⏭️ Skipped: ${fileInfo.category}/${fileInfo.file} (already exists)`);
        skippedCount++;
    }
}

console.log('');
console.log(`🎉 BATCH COMPLETED! Created ${createdCount} new files, skipped ${skippedCount} existing files`);
console.log('');
console.log('📊 This batch created:');
console.log(`- Emoji: 2 files`);
console.log(`- Roles: 3 files`);
console.log(`- Soundboard: 3 files`);
console.log(`- Webhooks: 3 files`);
console.log(`- Polls: 5 files`);
console.log(`- Stage: 3 files`);
console.log(`- Stickers: 3 files`);
console.log('');
console.log('🚀 Next: Run this script and then check the total file count!');

// Clean up this script file after execution
setTimeout(() => {
    try {
        fs.unlinkSync(__filename);
        console.log('🗑️ Script file cleaned up');
    } catch (error) {
        console.log('⚠️ Could not clean up script file');
    }
}, 2000);
