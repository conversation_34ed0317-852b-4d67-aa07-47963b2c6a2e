const { Events } = require("discord.js");
const { createWelcomeEmbed } = require("../utils/embedBuilder.js");
const { canSendMessages } = require("../utils/permissions.js");

module.exports = {
  name: Events.GuildMemberAdd,
  async execute(member, client) {
    const guildId = member.guild.id;

    try {
      // L<PERSON>y cấu hình welcome
      const welcomeConfig = await client.db.getWelcomeConfig(guildId);

      // Xử lý welcome message
      if (welcomeConfig && welcomeConfig.welcome_enabled) {
        await handleWelcomeMessage(member, client, welcomeConfig);
      }

      // X<PERSON> lý auto roles
      await handleAutoRoles(member, client);
    } catch (error) {
      console.error(
        `❌ Lỗi khi xử lý thành viên mới ${member.user.tag} trong ${member.guild.name}:`,
        error
      );
    }
  },
};

async function handleWelcomeMessage(member, client, config) {
  try {
    // Gửi welcome message trong channel
    if (config.welcome_channel_id) {
      const channel = member.guild.channels.cache.get(
        config.welcome_channel_id
      );

      if (channel) {
        const canSend = canSendMessages(channel);
        if (canSend.canSend) {
          if (config.welcome_embed) {
            // Gửi embed
            const welcomeEmbed = createWelcomeEmbed(
              member,
              config.welcome_message
            );
            await channel.send({ embeds: [welcomeEmbed] });
          } else {
            // Gửi tin nhắn thường
            const defaultMessage = `Chào mừng **{user}** đến với **{server}**!\n\nHy vọng bạn sẽ có những trải nghiệm tuyệt vời tại đây! ${client.config.emojis.welcome}`;
            const message = config.welcome_message || defaultMessage;
            const processedMessage = message
              .replace(/{user}/g, `<@${member.id}>`)
              .replace(/{username}/g, member.user.username)
              .replace(/{server}/g, member.guild.name)
              .replace(/{membercount}/g, member.guild.memberCount.toString());

            await channel.send(processedMessage);
          }

          console.log(
            `✅ Đã gửi welcome message cho ${member.user.tag} trong ${member.guild.name}`
          );
        } else {
          console.log(`⚠️ Không thể gửi welcome message: ${canSend.reason}`);
        }
      } else {
        console.log(
          `⚠️ Welcome channel không tồn tại trong ${member.guild.name}`
        );
      }
    }

    // Gửi DM welcome nếu được bật
    if (config.dm_welcome) {
      try {
        const dmMessage =
          config.dm_welcome_message ||
          config.welcome_message ||
          `Chào mừng bạn đến với **${member.guild.name}**!\n\nHy vọng bạn sẽ có những trải nghiệm tuyệt vời tại đây! ${client.config.emojis.welcome}`;

        const processedDmMessage = dmMessage
          .replace(/{user}/g, member.user.username)
          .replace(/{username}/g, member.user.username)
          .replace(/{server}/g, member.guild.name)
          .replace(/{membercount}/g, member.guild.memberCount.toString());

        await member.send(processedDmMessage);
        console.log(`✅ Đã gửi DM welcome cho ${member.user.tag}`);
      } catch (dmError) {
        console.log(
          `⚠️ Không thể gửi DM welcome cho ${member.user.tag}: ${dmError.message}`
        );
      }
    }
  } catch (error) {
    console.error("❌ Lỗi khi gửi welcome message:", error);
  }
}

async function handleAutoRoles(member, client) {
  try {
    // Xác định loại thành viên (bot hoặc human)
    const targetType = member.user.bot ? "bot" : "human";

    const autoRoles = await client.db.getAutoRoles(member.guild.id, targetType);

    if (autoRoles.length === 0) return;

    console.log(`🎭 Xử lý auto-role cho ${targetType}: ${member.user.tag}`);

    for (const autoRole of autoRoles) {
      try {
        const role = member.guild.roles.cache.get(autoRole.role_id);

        if (!role) {
          console.log(
            `⚠️ Auto role ${autoRole.role_name} không tồn tại, xóa khỏi database`
          );
          await client.db.removeAutoRole(member.guild.id, autoRole.role_id);
          continue;
        }

        // Kiểm tra quyền bot
        const { canManageRole } = require("../utils/permissions.js");
        const canManage = canManageRole(member.guild, role);

        if (!canManage.canManage) {
          console.log(
            `⚠️ Không thể gán auto role ${role.name}: ${canManage.reason}`
          );
          continue;
        }

        // Gán role với delay nếu có
        if (autoRole.delay_seconds > 0) {
          setTimeout(async () => {
            try {
              // Kiểm tra member vẫn còn trong server
              const currentMember = member.guild.members.cache.get(member.id);
              if (currentMember && !currentMember.roles.cache.has(role.id)) {
                await currentMember.roles.add(role, "Auto role assignment");
                console.log(
                  `✅ Đã gán auto role ${role.name} cho ${member.user.tag} (sau ${autoRole.delay_seconds}s)`
                );
              }
            } catch (delayError) {
              console.error(
                `❌ Lỗi khi gán delayed auto role ${role.name}:`,
                delayError
              );
            }
          }, autoRole.delay_seconds * 1000);
        } else {
          // Gán role ngay lập tức
          await member.roles.add(role, "Auto role assignment");
          console.log(
            `✅ Đã gán auto role ${role.name} cho ${member.user.tag}`
          );
        }
      } catch (roleError) {
        console.error(
          `❌ Lỗi khi gán auto role ${autoRole.role_name}:`,
          roleError
        );
      }
    }
  } catch (error) {
    console.error("❌ Lỗi khi xử lý auto roles:", error);
  }
}
