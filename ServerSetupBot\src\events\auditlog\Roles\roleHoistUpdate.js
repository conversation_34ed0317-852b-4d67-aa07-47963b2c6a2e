const { Events } = require('discord.js');
const { createInfoEmbed } = require('../../../utils/embedBuilder.js');

module.exports = {
    name: Events.GuildRoleUpdate,
    async execute(oldRole, newRole, client) {
        try {
            // Only handle hoist changes
            if (oldRole.hoist === newRole.hoist) return;
            
            console.log(`📋 Role hoist updated: ${newRole.name} in ${newRole.guild.name}`);
            
            // Get audit log config
            const config = await client.db.getAuditLogConfig(newRole.guild.id);
            if (!config || !config.enabled) return;
            
            // Check if this event should be logged
            if (!client.db.shouldLogEvent(config, 'ROLE_HOIST_UPDATE')) return;
            
            // Create event data
            const eventData = {
                eventType: 'ROLE_HOIST_UPDATE',
                user: 'System',
                userId: null,
                action: 'Hiển thị riêng role được cập nhật',
                details: `<PERSON><PERSON><PERSON> thị riêng của role **${newRole.name}** đã được ${newRole.hoist ? 'bật' : 'tắt'}`,
                target: newRole.name,
                timestamp: new Date().toISOString()
            };
            
            // Try to get who updated the role hoist from audit logs
            try {
                const auditLogs = await newRole.guild.fetchAuditLogs({
                    type: 31, // ROLE_UPDATE
                    limit: 1
                });
                
                const auditEntry = auditLogs.entries.first();
                if (auditEntry && auditEntry.target?.id === newRole.id) {
                    eventData.user = auditEntry.executor.tag;
                    eventData.userId = auditEntry.executor.id;
                    eventData.action = `Hiển thị riêng role được cập nhật bởi ${auditEntry.executor.tag}`;
                    if (auditEntry.reason) {
                        eventData.reason = auditEntry.reason;
                    }
                }
            } catch (error) {
                console.log('Could not fetch audit logs for role hoist update');
            }
            
            // Add to database
            await client.db.addAuditLog(newRole.guild.id, eventData);
            
            // Send to audit log channel
            const auditChannel = await client.channels.fetch(config.channelId);
            if (!auditChannel) return;
            
            // Create embed
            const embed = createInfoEmbed(
                '📋 Hiển thị riêng role được cập nhật',
                `Vừa có hiển thị riêng role được thay đổi trong server`
            );
            
            embed.setColor(newRole.hoist ? 0x2ecc71 : 0xe74c3c); // Green for enabled, red for disabled
            
            embed.addFields([
                {
                    name: '> Role',
                    value: `- ${newRole} (${newRole.name})`,
                    inline: false
                },
                {
                    name: '> ID role',
                    value: `- ${newRole.id}`,
                    inline: true
                },
                {
                    name: '> Người thay đổi',
                    value: `- ${eventData.user}`,
                    inline: true
                },
                {
                    name: '> Thời gian',
                    value: `- <t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                },
                {
                    name: '> Hiển thị riêng cũ',
                    value: `- ${oldRole.hoist ? '✅ Có' : '❌ Không'}`,
                    inline: true
                },
                {
                    name: '> Hiển thị riêng mới',
                    value: `- ${newRole.hoist ? '✅ Có' : '❌ Không'}`,
                    inline: true
                },
                {
                    name: '> Số thành viên có role',
                    value: `- ${newRole.members.size} thành viên`,
                    inline: true
                }
            ]);
            
            // Add role information
            embed.addFields([
                {
                    name: '> Màu role',
                    value: `- ${newRole.hexColor}`,
                    inline: true
                },
                {
                    name: '> Vị trí role',
                    value: `- ${newRole.position}`,
                    inline: true
                },
                {
                    name: '> Có thể mention',
                    value: `- ${newRole.mentionable ? 'Có' : 'Không'}`,
                    inline: true
                }
            ]);
            
            // Add impact explanation
            if (newRole.hoist && !oldRole.hoist) {
                embed.addFields({
                    name: '> 📈 Tác động',
                    value: [
                        '• Role sẽ hiển thị riêng trong danh sách thành viên',
                        '• Thành viên có role này sẽ được nhóm lại',
                        '• Dễ dàng nhận biết ai có role này',
                        '• Tăng tính tổ chức trong server'
                    ].join('\n'),
                    inline: false
                });
            } else if (!newRole.hoist && oldRole.hoist) {
                embed.addFields({
                    name: '> 📉 Tác động',
                    value: [
                        '• Role sẽ không hiển thị riêng nữa',
                        '• Thành viên có role này sẽ trộn lẫn với @everyone',
                        '• Khó nhận biết ai có role này',
                        '• Danh sách thành viên sẽ gọn hơn'
                    ].join('\n'),
                    inline: false
                });
            }
            
            // Add note about role hierarchy
            embed.addFields({
                name: '> 📝 Lưu ý',
                value: `- Hiển thị riêng chỉ áp dụng khi role có màu khác @everyone`,
                inline: false
            });
            
            if (eventData.reason) {
                embed.addFields({
                    name: '> Lý do',
                    value: `- ${eventData.reason}`,
                    inline: false
                });
            }
            
            // Set role color as embed color if it has one
            if (newRole.color !== 0) {
                embed.setColor(newRole.color);
            }
            
            embed.setTimestamp();
            embed.setFooter({
                text: `${newRole.guild.name} • Role Hoist Update`,
                iconURL: newRole.guild.iconURL()
            });
            
            await auditChannel.send({ embeds: [embed] });
            console.log(`✅ Role hoist update logged: ${newRole.name}`);
            
        } catch (error) {
            console.error('Error in roleHoistUpdate audit log:', error);
        }
    }
};
